using System;
using System.ComponentModel;
using System.Windows;
using System.Windows.Media;

namespace LIVAnalyzer.UI.Services
{
    /// <summary>
    /// 主题资源助手 - 提供响应式主题颜色
    /// </summary>
    public class ThemeResourceHelper : INotifyPropertyChanged
    {
        private static ThemeResourceHelper? _instance;
        public static ThemeResourceHelper Instance => _instance ??= new ThemeResourceHelper();

        public event PropertyChangedEventHandler? PropertyChanged;

        private ThemeResourceHelper()
        {
            // 订阅主题变更事件
            NativeFluentThemeService.Instance.ThemeChanged += OnThemeChanged;
            
            // 初始化颜色
            UpdateColors();
        }

        private void OnThemeChanged(object? sender, NativeThemeChangedEventArgs e)
        {
            UpdateColors();
        }

        private void UpdateColors()
        {
            var isDark = NativeFluentThemeService.Instance.IsCurrentlyDarkMode();
            
            if (isDark)
            {
                // 深色主题颜色
                BackgroundColor = Color.FromRgb(32, 32, 32);        // #202020
                ForegroundColor = Color.FromRgb(255, 255, 255);     // #FFFFFF
                ControlBackgroundColor = Color.FromRgb(45, 45, 45); // #2D2D2D
                BorderColor = Color.FromRgb(70, 70, 70);            // #464646
            }
            else
            {
                // 浅色主题颜色
                BackgroundColor = Color.FromRgb(255, 255, 255);     // #FFFFFF
                ForegroundColor = Color.FromRgb(0, 0, 0);           // #000000
                ControlBackgroundColor = Color.FromRgb(243, 243, 243); // #F3F3F3
                BorderColor = Color.FromRgb(225, 225, 225);         // #E1E1E1
            }

            // 通知所有属性变更
            OnPropertyChanged(nameof(BackgroundColor));
            OnPropertyChanged(nameof(ForegroundColor));
            OnPropertyChanged(nameof(ControlBackgroundColor));
            OnPropertyChanged(nameof(BorderColor));
            OnPropertyChanged(nameof(BackgroundBrush));
            OnPropertyChanged(nameof(ForegroundBrush));
            OnPropertyChanged(nameof(ControlBackgroundBrush));
            OnPropertyChanged(nameof(BorderBrush));
        }

        // 颜色属性
        public Color BackgroundColor { get; private set; }
        public Color ForegroundColor { get; private set; }
        public Color ControlBackgroundColor { get; private set; }
        public Color BorderColor { get; private set; }

        // 画刷属性
        public SolidColorBrush BackgroundBrush => new SolidColorBrush(BackgroundColor);
        public SolidColorBrush ForegroundBrush => new SolidColorBrush(ForegroundColor);
        public SolidColorBrush ControlBackgroundBrush => new SolidColorBrush(ControlBackgroundColor);
        public SolidColorBrush BorderBrush => new SolidColorBrush(BorderColor);

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
