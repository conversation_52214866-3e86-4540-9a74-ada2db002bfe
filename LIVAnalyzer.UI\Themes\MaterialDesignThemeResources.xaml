<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">
                    
    <!-- 应用程序特定的 Material Design 样式覆盖 -->
    
    <!-- 自定义主色调 -->
    <SolidColorBrush x:Key="PrimaryHueLightBrush" Color="#2196F3"/>
    <SolidColorBrush x:Key="PrimaryHueMidBrush" Color="#1976D2"/>
    <SolidColorBrush x:Key="PrimaryHueDarkBrush" Color="#0D47A1"/>
    
    <!-- 自定义强调色 -->
    <SolidColorBrush x:Key="SecondaryHueLightBrush" Color="#00E5FF"/>
    <SolidColorBrush x:Key="SecondaryHueMidBrush" Color="#00BCD4"/>
    <SolidColorBrush x:Key="SecondaryHueDarkBrush" Color="#0097A7"/>
    
    <!-- 应用程序特定样式 -->
    <Style x:Key="ApplicationWindowStyle" TargetType="{x:Type Window}" BasedOn="{StaticResource MaterialDesignWindow}">
        <Setter Property="Background" Value="{DynamicResource MaterialDesignPaper}"/>
        <Setter Property="FontFamily" Value="Microsoft YaHei UI"/>
    </Style>
    
    <!-- 图表区域样式 -->
    <Style x:Key="ChartContainerStyle" TargetType="{x:Type Border}">
        <Setter Property="Background" Value="{DynamicResource MaterialDesignCardBackground}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource MaterialDesignDivider}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="CornerRadius" Value="4"/>
        <Setter Property="Margin" Value="8"/>
        <Setter Property="Padding" Value="16"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect BlurRadius="8" 
                                  ShadowDepth="2" 
                                  Direction="270" 
                                  Color="{DynamicResource MaterialDesignShadow}" 
                                  Opacity="0.26"/>
            </Setter.Value>
        </Setter>
    </Style>
    
    <!-- 数据输入区域样式 -->
    <Style x:Key="DataInputPanelStyle" TargetType="{x:Type Grid}">
        <Setter Property="Background" Value="{DynamicResource MaterialDesignCardBackground}"/>
        <Setter Property="Margin" Value="8"/>
    </Style>
    
    <!-- 自定义按钮样式 -->
    <Style x:Key="PrimaryActionButtonStyle" TargetType="{x:Type Button}" 
           BasedOn="{StaticResource MaterialDesignRaisedButton}">
        <Setter Property="Background" Value="{DynamicResource PrimaryHueMidBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource PrimaryHueMidBrush}"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="Height" Value="36"/>
        <Setter Property="Padding" Value="16,0"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontWeight" Value="Medium"/>
    </Style>
    
    <Style x:Key="SecondaryActionButtonStyle" TargetType="{x:Type Button}" 
           BasedOn="{StaticResource MaterialDesignOutlinedButton}">
        <Setter Property="BorderBrush" Value="{DynamicResource PrimaryHueMidBrush}"/>
        <Setter Property="Foreground" Value="{DynamicResource PrimaryHueMidBrush}"/>
        <Setter Property="Height" Value="36"/>
        <Setter Property="Padding" Value="16,0"/>
        <Setter Property="FontSize" Value="14"/>
    </Style>
    
</ResourceDictionary>