# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

LIV Analyzer WinUI is a modern Windows application for analyzing laser diode Light-Current-Voltage (LIV) characteristics. Built with WinUI 3 and .NET 9, it provides data visualization, analysis, and batch processing capabilities for laser testing data with a modern Windows 11 Fluent Design interface.

**Note**: This is the WinUI 3 version of the LIV Analyzer. The main WPF implementation is in the `LIVAnalyzer.UI` project. This WinUI project shares the same core business logic libraries but provides a modern WinUI 3 interface.

## Development Commands

### Building the Application
```bash
# Restore NuGet packages
dotnet restore

# Build the WinUI project specifically
dotnet build LIVAnalyzer.WinUI/LIVAnalyzer.WinUI.csproj

# Build in Release mode
dotnet build LIVAnalyzer.WinUI/LIVAnalyzer.WinUI.csproj --configuration Release

# Clean build artifacts
dotnet clean LIVAnalyzer.WinUI/LIVAnalyzer.WinUI.csproj

# Build the entire solution (includes shared libraries)
dotnet build --configuration Release
```

### Running the Application
```bash
# Run the WinUI project directly
dotnet run --project LIVAnalyzer.WinUI/LIVAnalyzer.WinUI.csproj

# Run in Release mode
dotnet run --project LIVAnalyzer.WinUI/LIVAnalyzer.WinUI.csproj --configuration Release
```

### Testing
```bash
# Run all tests (shared with WPF version)
dotnet test

# Run specific test project
dotnet test LIVAnalyzer.Tests/LIVAnalyzer.Tests.csproj

# Run with detailed output
dotnet test --verbosity normal
```

### Publishing
```bash
# Publish WinUI app as MSIX package
dotnet publish LIVAnalyzer.WinUI/LIVAnalyzer.WinUI.csproj -c Release -r win-x64 --self-contained true

# Publish as unpackaged (framework-dependent)
dotnet publish LIVAnalyzer.WinUI/LIVAnalyzer.WinUI.csproj -c Release -r win-x64 --self-contained false

# Publish for multiple architectures
dotnet publish LIVAnalyzer.WinUI/LIVAnalyzer.WinUI.csproj -c Release -r win-x86 --self-contained true
dotnet publish LIVAnalyzer.WinUI/LIVAnalyzer.WinUI.csproj -c Release -r win-arm64 --self-contained true
```

## Architecture Overview

### WinUI-Specific Architecture

The WinUI project leverages the same core business logic as the WPF version but provides a modern WinUI 3 interface:

1. **LIVAnalyzer.WinUI** - WinUI 3 presentation layer
   - MVVM pattern using CommunityToolkit.Mvvm with source generators
   - WinUI 3 controls and Fluent Design System
   - OxyPlot.WinUI for scientific plotting
   - Modern Windows 11 styling and behaviors

2. **Shared Libraries** (same as WPF version)
   - **LIVAnalyzer.Models**: Domain models and data structures
   - **LIVAnalyzer.Data**: Data access layer (Excel/CSV loading)
   - **LIVAnalyzer.Core**: Business logic and algorithms
   - **LIVAnalyzer.Services**: Configuration, logging, and services

### Key WinUI 3 Features

- **Modern UI Controls**: NavigationView, Grid layouts, modern input controls
- **Fluent Design**: Acrylic materials, reveal effects, modern animations
- **Adaptive Layouts**: Responsive design for different screen sizes
- **Windows 11 Integration**: System backdrop, rounded corners, updated styling
- **MSIX Packaging**: Modern deployment with automatic updates
- **Multi-Platform Support**: x86, x64, ARM64 architectures

### Target Framework and Platform

- **Target Framework**: `net9.0-windows10.0.19041.0`
- **Minimum Platform**: `10.0.17763.0` (Windows 10 version 1809)
- **Supported Architectures**: x86, x64, ARM64
- **Windows App SDK**: Latest version for WinUI 3 features

## Dependencies

### WinUI-Specific Packages
- **Microsoft.WindowsAppSDK** (1.6.241114003): Core WinUI 3 framework
- **Microsoft.Windows.SDK.BuildTools** (10.0.26100.1742): Windows SDK tools
- **CommunityToolkit.WinUI.Controls.DataGrid** (8.1.240916): Advanced data grid control
- **CommunityToolkit.WinUI.Converters** (8.1.240916): Value converters for WinUI
- **WinUIEx** (2.4.2): Extended WinUI controls and utilities
- **OxyPlot.WinUI** (2.2.0): Scientific plotting for WinUI

### Shared Dependencies (from core libraries)
- **CommunityToolkit.Mvvm** (8.3.2): MVVM framework with source generation
- **EPPlus**: Excel file manipulation
- **MathNet.Numerics**: Numerical computations
- **YamlDotNet**: Configuration management
- **Serilog**: Structured logging

## Key Implementation Differences from WPF

### UI Framework Differences
```csharp
// WinUI 3 uses different namespace and controls
using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Controls;

// Instead of WPF's System.Windows
using System.Windows;
using System.Windows.Controls;
```

### Plotting Library
```csharp
// WinUI uses OxyPlot.WinUI instead of OxyPlot.Wpf
using OxyPlot.WinUI;
// vs WPF: using OxyPlot.Wpf;
```

### Application Lifecycle
```csharp
// WinUI 3 application structure differs from WPF
public partial class App : Application
{
    protected override void OnLaunched(LaunchActivatedEventArgs args)
    {
        // WinUI 3 specific launch handling
    }
}
```

## MSIX Packaging

The WinUI project supports MSIX packaging for modern deployment:

### Package Configuration
- Package identity and versioning in `Package.appxmanifest`
- App icons and splash screens in `Assets/` folder
- Capabilities and declarations for file access

### Building MSIX Package
```bash
# Build MSIX package
msbuild LIVAnalyzer.WinUI.csproj /p:Configuration=Release /p:Platform=x64 /p:UapAppxPackageBuildMode=StoreUpload

# Or using dotnet
dotnet build LIVAnalyzer.WinUI/LIVAnalyzer.WinUI.csproj -c Release /p:AppxPackageSigningEnabled=false
```

## Data Processing Integration

The WinUI version uses the exact same data processing pipeline as the WPF version:

### File Loading
- Excel files via `ExcelDataLoader` (EPPlus-based)
- CSV files via `CsvDataLoader`
- Same file format requirements and validation

### Analysis Algorithms
- Shared `LIVDataProcessor` for core calculations
- Same threshold current, efficiency, FWHM algorithms
- Identical batch processing capabilities

### Configuration System
- Same YAML-based configuration in `%APPDATA%\LIVAnalyzer\config.yaml`
- Shared `ConfigurationManager` singleton pattern

## Platform Considerations

### Windows Version Support
- **Minimum**: Windows 10 version 1809 (build 17763)
- **Recommended**: Windows 11 for full Fluent Design features
- **Optimal**: Windows 11 22H2+ for latest WinUI 3 features

### Architecture Support
- x86: 32-bit Windows systems
- x64: 64-bit Windows systems (most common)
- ARM64: Windows on ARM devices (Surface Pro X, etc.)

### Deployment Options
1. **MSIX Package**: Modern deployment with automatic updates
2. **Self-Contained**: Includes .NET 9 runtime, larger but no dependencies
3. **Framework-Dependent**: Requires .NET 9 runtime installed

## Development Notes

### Project Structure
The WinUI project follows the same layered architecture but with WinUI-specific UI implementation:
- ViewModels can be shared or adapted from WPF version
- Data binding syntax is similar but with WinUI-specific considerations
- Navigation and lifecycle management follow WinUI 3 patterns

### Migration from WPF
When adapting WPF code to WinUI:
1. Update namespaces (`System.Windows` → `Microsoft.UI.Xaml`)
2. Adapt control-specific properties and events
3. Update plotting library references
4. Adjust application lifecycle handling

### Performance Considerations
- WinUI 3 generally provides better performance than WPF
- Native compilation support with .NET 9
- Hardware acceleration for graphics and animations
- Efficient memory management for large datasets

## File Format Compatibility

The WinUI version maintains 100% compatibility with the WPF version:

### Excel File Structure (same as WPF)
Required worksheets:
- `wavelength`: Columns [Wavelength (nm), Intensity]
- `power`: Columns [Current (A), Power (W)]
- `voltage`: Columns [Current (A), Voltage (V)]

Optional worksheets:
- `HFF`: Horizontal far field data
- `VFF`: Vertical far field data

### CSV File Format (same as WPF)
Standard format with headers: Wavelength, Intensity, Current, Power, Voltage

## Error Handling and Logging

Uses the same error handling and logging infrastructure:
- Structured logging with Serilog
- Error logs in `%APPDATA%\LIVAnalyzer\Logs\`
- User-friendly error dialogs adapted for WinUI 3

## Future Enhancements

Potential WinUI 3-specific improvements:
- Touch and pen input support
- Enhanced accessibility features
- Modern Windows 11 integrations (Jump Lists, etc.)
- Progressive Web App (PWA) capabilities
- Better multi-monitor support