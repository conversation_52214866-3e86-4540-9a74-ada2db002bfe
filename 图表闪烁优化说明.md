# 图表闪烁优化说明

## 问题描述
在曲线选择时，勾选或取消勾选会导致图表闪烁，用户体验不佳。

## 问题原因
原来的实现每次勾选/取消勾选时都会：
1. **完全清空图表** (`ClearPlots()`)
2. **重新创建所有图表设置** (`UpdateAllPlotSettings()`)
3. **重新添加所有数据系列**

这种完全重建的方式导致图表闪烁。

## 解决方案

### 1. 增量更新机制
实现了增量更新机制，只更新变化的部分：

- **新增方法**：
  - `UpdatePlotsIncremental()` - 增量更新主方法
  - `InitializePlotModelsIfNeeded()` - 按需初始化图表模型
  - `ClearPlotSeries()` - 只清空数据系列，保留图表结构
  - `UpdateLIVPlotIncremental()` - LIV图表增量更新
  - `UpdateSpectrumPlotIncremental()` - 光谱图表增量更新
  - `UpdateEfficiencyPlotIncremental()` - 效率图表增量更新
  - `UpdateDivergencePlotIncremental()` - 发散角图表增量更新

### 2. 智能设置更新
添加了 `_needsSettingsUpdate` 标志，只在必要时更新图表设置：

```csharp
private bool _needsSettingsUpdate = true; // 标记是否需要更新图表设置
```

### 3. 优化的PlotManager
扩展了 `OptimizedPlotManager` 以支持：
- **增量更新模式**：`BatchUpdateSeriesAsync(clearExisting: false)`
- **多种数据格式**：支持 `XData/YData` 和 `Points` 两种格式
- **轻量级刷新**：`InvalidatePlot(false)` 用于增量更新

### 4. 系列管理优化
对于每个图表，增量更新会：
1. **获取现有系列**：`existingSeries = plotModel.Series.ToDictionary(s => s.Title, s => s)`
2. **移除不再选中的系列**：只移除不在选中列表中的系列
3. **添加新选中的系列**：只添加新选中且不存在的系列
4. **保持现有系列**：已存在的系列保持不变

## 技术细节

### 增量更新流程
```csharp
private async Task UpdatePlotsIncremental(List<FileViewModel> selectedFiles, CancellationToken cancellationToken)
{
    // 1. 初始化图表模型（如果尚未初始化）
    InitializePlotModelsIfNeeded();

    // 2. 只在必要时更新图表设置
    if (_needsSettingsUpdate)
    {
        UpdateAllPlotSettings();
        _needsSettingsUpdate = false;
    }

    // 3. 如果没有选中文件，只清空数据系列
    if (!selectedFiles.Any())
    {
        ClearPlotSeries();
        return;
    }

    // 4. 并行增量更新所有图表
    var tasks = new List<Task>
    {
        UpdateLIVPlotIncremental(selectedFiles, cancellationToken),
        UpdateSpectrumPlotIncremental(selectedFiles, cancellationToken),
        UpdateEfficiencyPlotIncremental(selectedFiles, cancellationToken),
        UpdateDivergencePlotIncremental(selectedFiles, cancellationToken)
    };

    await Task.WhenAll(tasks);
}
```

### 系列增量更新示例
```csharp
// 获取现有系列
var existingSeries = LIVPlotModel.Series.ToDictionary(s => s.Title, s => s);
var selectedFileNames = selectedFiles.Select(f => f.FileName).ToHashSet();

// 移除不再选中的系列
var seriesToRemove = existingSeries.Keys.Where(title => !selectedFileNames.Contains(title)).ToList();
foreach (var title in seriesToRemove)
{
    LIVPlotModel.Series.Remove(existingSeries[title]);
}

// 添加新选中的系列
foreach (var file in selectedFiles)
{
    if (!existingSeries.ContainsKey(file.FileName)) 
    {
        // 创建并添加新系列
        var seriesData = new SeriesData { ... };
        // 添加到图表
    }
}
```

## 性能优化

### 1. 减少重绘次数
- 使用 `InvalidatePlot(false)` 进行轻量级刷新
- 避免不必要的图表重建

### 2. 批量操作
- 批量移除系列
- 批量添加系列
- 减少单个操作的开销

### 3. 智能更新
- 只在设置变化时更新图表设置
- 保持现有系列不变
- 按需初始化图表模型

## 预期效果

1. **消除闪烁**：图表不再完全重建，避免闪烁
2. **提升性能**：减少不必要的重绘和重建
3. **保持响应性**：快速响应用户的选择变化
4. **平滑体验**：曲线的添加和移除更加平滑

## 兼容性

该优化保持了与现有功能的完全兼容：
- **LIV图表**：包含功率曲线、电压曲线（虚线）、效率曲线（当启用时）
- **光谱图表**：正常显示光谱数据
- **效率图表**：正常显示效率计算结果
- **发散角图表**：包含数据曲线和所有计算虚线（FWHM、FW(1/e²)、FW95%）
- 主题切换正常
- 图表设置正常
- 数据处理正常

## 修复的问题

在初始实现中发现并修复了以下问题：
1. **LIV图表内容缺失**：恢复了电压曲线和效率曲线
2. **发散角虚线缺失**：恢复了FWHM、FW(1/e²)、FW95%标记线
3. **系列命名**：正确处理多种曲线类型的命名和管理

## 测试建议

1. **基本功能测试**：
   - 加载多个文件
   - 快速勾选/取消勾选多个文件
   - 观察图表是否还有闪烁

2. **性能测试**：
   - 加载大量文件（10+）
   - 快速切换选择状态
   - 观察响应速度

3. **兼容性测试**：
   - 测试所有图表类型
   - 测试主题切换
   - 测试图表设置修改
