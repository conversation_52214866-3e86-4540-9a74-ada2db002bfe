import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  build: {
    chunkSizeWarningLimit: 1500,
    rollupOptions: {
      output: {
        manualChunks: {
          react: ['react', 'react-dom', 'react-router-dom'],
          plot: ['@observablehq/plot', 'd3'],
          xlsx: ['xlsx'],
          csv: ['papaparse'],
        },
      },
    },
  },
})
