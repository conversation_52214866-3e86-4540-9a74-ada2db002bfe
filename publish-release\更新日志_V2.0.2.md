# LIV分析工具 V2.0.2 更新日志

发布日期：2025年7月21日

## 主要更新内容

### 1. 发散角计算优化
- **术语规范化**：将FW86.5%统一更名为FW(1/e²)，更准确地反映物理含义
- **算法改进**：明确FW(1/e²)为峰值强度13.5%处的宽度计算，而非86.5%功率包含
- **结果限制**：能量占比计算结果限制在100%以内，避免异常显示

### 2. 数据处理增强
- **负值处理**：改进负强度值处理方式，直接设为0而非使用偏移方法
- **边界检测**：优化测量边界值检测，提供更准确的警告提示
- **稳健性提升**：增强数据预处理算法的稳健性

### 3. 界面和文档优化
- **显示优化**：全面统一使用FW(1/e²)术语，提高专业性
- **导出改进**：优化Excel导出中的列名称和格式
- **文档完善**：更新技术文档，澄清各发散角参数的物理含义

### 4. 问题修复
- 修复发散角能量占比可能超过100%的计算错误
- 修复负强度值导致的计算异常
- 修复测试代码中的命名不一致问题

## 升级建议
- 本版本主要改进了发散角计算的准确性和专业性
- 建议所有用户升级到此版本以获得更准确的分析结果
- 升级后原有数据文件仍可正常使用

## 技术支持
如有问题请联系技术支持团队。