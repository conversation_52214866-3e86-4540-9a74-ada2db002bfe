using System;
using System.IO;
using System.Linq;
using Xunit;
using Xunit.Abstractions;
using LIVAnalyzer.Data.Loaders;
using LIVAnalyzer.Core.Processors;
using LIVAnalyzer.Models;

namespace LIVAnalyzer.Tests.Core
{
    /// <summary>
    /// 使用真实数据测试能量占比计算
    /// </summary>
    public class RealDataTest
    {
        private readonly ITestOutputHelper _output;

        public RealDataTest(ITestOutputHelper output)
        {
            _output = output;
        }

        [Fact]
        public void TestRealLaserDataEnergyRatio()
        {
            // 使用提供的真实测试数据
            string testDataPath = @"E:\01LaserPackage\software\LIV_Analyzer\光峰芯片测试数据\640nm_QCW2P5A_30duty_T45C_LIV+Spectrum0716\芯瑞光红光芯片测试数据\R24+COS01+201_converted.xlsx";
            
            // 创建输出文件
            string outputPath = Path.Combine(Directory.GetCurrentDirectory(), "real_data_analysis.txt");
            using (var writer = new StreamWriter(outputPath))
            {
                writer.WriteLine($"分析文件: {testDataPath}");
                writer.WriteLine($"分析时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                writer.WriteLine(new string('=', 80));
                
                if (!File.Exists(testDataPath))
                {
                    writer.WriteLine($"测试数据文件不存在: {testDataPath}");
                    _output.WriteLine($"分析结果已保存到: {outputPath}");
                    return;
                }

                try
                {
                    // 加载数据
                    var loader = new ExcelDataLoader();
                    var data = loader.LoadExcelDataAsync(testDataPath).Result;
                    
                    if (data == null)
                    {
                        writer.WriteLine("无法加载数据文件");
                        _output.WriteLine($"分析结果已保存到: {outputPath}");
                        return;
                    }
                    
                    writer.WriteLine($"成功加载数据文件: {Path.GetFileName(testDataPath)}");
                    writer.WriteLine($"波长数据点数: {data.WavelengthIntensityData.Count}");
                    writer.WriteLine($"功率数据点数: {data.CurrentPowerData.Count}");
                    writer.WriteLine($"电压数据点数: {data.CurrentVoltageData.Count}");
                    writer.WriteLine($"水平发散角数据点数: {data.HorizontalDivergenceData?.Count ?? 0}");
                    writer.WriteLine($"垂直发散角数据点数: {data.VerticalDivergenceData?.Count ?? 0}");
                    
                    _output.WriteLine($"成功加载数据文件: {Path.GetFileName(testDataPath)}");
                    _output.WriteLine($"波长数据点数: {data.WavelengthIntensityData.Count}");
                    _output.WriteLine($"功率数据点数: {data.CurrentPowerData.Count}");
                    _output.WriteLine($"电压数据点数: {data.CurrentVoltageData.Count}");
                    _output.WriteLine($"水平发散角数据点数: {data.HorizontalDivergenceData?.Count ?? 0}");
                    _output.WriteLine($"垂直发散角数据点数: {data.VerticalDivergenceData?.Count ?? 0}");
                    
                    // 如果有发散角数据，计算能量占比
                    if (data.HorizontalDivergenceData?.Any() == true)
                    {
                        writer.WriteLine("\n=== 水平发散角分析 ===");
                        _output.WriteLine("\n=== 水平发散角分析 ===");
                        AnalyzeDivergenceData(data.HorizontalDivergenceData, "水平", writer);
                    }
                    
                    if (data.VerticalDivergenceData?.Any() == true)
                    {
                        writer.WriteLine("\n=== 垂直发散角分析 ===");
                        _output.WriteLine("\n=== 垂直发散角分析 ===");
                        AnalyzeDivergenceData(data.VerticalDivergenceData, "垂直", writer);
                    }
                    
                    // 使用LIVDataProcessor处理
                    var processor = new LIVDataProcessor();
                    var parameters = processor.CalculateParameters(data);
                    
                    if (parameters != null)
                    {
                        writer.WriteLine("\n=== LIVDataProcessor处理结果 ===");
                        writer.WriteLine($"峰值波长: {parameters.PeakWavelength:F2} nm");
                        writer.WriteLine($"FWHM: {parameters.FWHM:F2} nm");
                        writer.WriteLine($"阈值电流: {parameters.ThresholdCurrent:F3} A");
                        
                        _output.WriteLine("\n=== LIVDataProcessor处理结果 ===");
                        _output.WriteLine($"峰值波长: {parameters.PeakWavelength:F2} nm");
                        _output.WriteLine($"FWHM: {parameters.FWHM:F2} nm");
                        _output.WriteLine($"阈值电流: {parameters.ThresholdCurrent:F3} A");
                        
                        if (data.DivergenceResults != null)
                        {
                            writer.WriteLine("\n发散角结果:");
                            writer.WriteLine($"水平FWHM: {data.DivergenceResults.HorizontalFWHM:F2}°");
                            writer.WriteLine($"水平FW(1/e²): {data.DivergenceResults.HorizontalFW1e2:F2}°");
                            writer.WriteLine($"水平FW(1/e²)能量占比: {data.DivergenceResults.HorizontalFW1e2PowerContainment:P3}");
                            writer.WriteLine($"垂直FWHM: {data.DivergenceResults.VerticalFWHM:F2}°");
                            writer.WriteLine($"垂直FW(1/e²): {data.DivergenceResults.VerticalFW1e2:F2}°");
                            writer.WriteLine($"垂直FW(1/e²)能量占比: {data.DivergenceResults.VerticalFW1e2PowerContainment:P3}");
                            
                            _output.WriteLine("\n发散角结果:");
                            _output.WriteLine($"水平FWHM: {data.DivergenceResults.HorizontalFWHM:F2}°");
                            _output.WriteLine($"水平FW(1/e²): {data.DivergenceResults.HorizontalFW1e2:F2}°");
                            _output.WriteLine($"水平FW(1/e²)能量占比: {data.DivergenceResults.HorizontalFW1e2PowerContainment:P3}");
                            _output.WriteLine($"垂直FWHM: {data.DivergenceResults.VerticalFWHM:F2}°");
                            _output.WriteLine($"垂直FW(1/e²): {data.DivergenceResults.VerticalFW1e2:F2}°");
                            _output.WriteLine($"垂直FW(1/e²)能量占比: {data.DivergenceResults.VerticalFW1e2PowerContainment:P3}");
                        }
                    }
                }
                catch (Exception ex)
                {
                    writer.WriteLine($"处理数据时出错: {ex.Message}");
                    writer.WriteLine($"堆栈跟踪: {ex.StackTrace}");
                    _output.WriteLine($"处理数据时出错: {ex.Message}");
                    _output.WriteLine($"堆栈跟踪: {ex.StackTrace}");
                }
            }
            
            _output.WriteLine($"分析结果已保存到: {outputPath}");
        }
        
        private void AnalyzeDivergenceData(System.Collections.Generic.List<DataPoint> divergenceData, string direction, StreamWriter writer)
        {
            var angles = divergenceData.Select(p => p.X).ToArray();
            var intensities = divergenceData.Select(p => p.Y).ToArray();
            
            writer.WriteLine($"{direction}方向数据范围: [{angles.Min():F2}°, {angles.Max():F2}°]");
            writer.WriteLine($"最大强度: {intensities.Max():F6}");
            writer.WriteLine($"最小强度: {intensities.Min():F6}");
            
            _output.WriteLine($"{direction}方向数据范围: [{angles.Min():F2}°, {angles.Max():F2}°]");
            _output.WriteLine($"最大强度: {intensities.Max():F6}");
            _output.WriteLine($"最小强度: {intensities.Min():F6}");
            
            // 检查边界强度
            var maxIntensity = intensities.Max();
            var firstIntensity = intensities.First();
            var lastIntensity = intensities.Last();
            var boundaryRatio = Math.Max(firstIntensity, lastIntensity) / maxIntensity;
            
            writer.WriteLine($"边界强度比: {boundaryRatio:P2} (边界强度/峰值强度)");
            _output.WriteLine($"边界强度比: {boundaryRatio:P2} (边界强度/峰值强度)");
            
            if (boundaryRatio > 0.01)
            {
                writer.WriteLine("警告: 边界强度较高，可能影响能量占比计算准确性");
                _output.WriteLine("警告: 边界强度较高，可能影响能量占比计算准确性");
            }
            
            // 使用DivergenceProcessor计算
            var processor = new DivergenceProcessor();
            var angleDataPoints = divergenceData.Select(p => new DataPoint(p.X, p.Y)).ToList();
            var intensityDataPoints = divergenceData.Select(p => new DataPoint(p.X, p.Y)).ToList();
            
            var result = processor.CalculateDivergence(angleDataPoints, intensityDataPoints);
            
            if (result.IsValid)
            {
                writer.WriteLine($"\nDivergenceProcessor计算结果:");
                writer.WriteLine($"FWHM: {result.FWHM:F2}°");
                writer.WriteLine($"FW(1/e²): {result.FW1e2:F2}°");
                writer.WriteLine($"FW(1/e²)能量占比: {result.FW1e2PowerContainment:P3}");
                writer.WriteLine($"FW95%: {result.FW95:F2}°");
                
                _output.WriteLine($"\nDivergenceProcessor计算结果:");
                _output.WriteLine($"FWHM: {result.FWHM:F2}°");
                _output.WriteLine($"FW(1/e²): {result.FW1e2:F2}°");
                _output.WriteLine($"FW(1/e²)能量占比: {result.FW1e2PowerContainment:P3}");
                _output.WriteLine($"FW95%: {result.FW95:F2}°");
                
                // 检查合理性
                if (result.FW1e2PowerContainment > 0.9)
                {
                    writer.WriteLine($"注意: 能量占比({result.FW1e2PowerContainment:P1})较高，可能原因:");
                    writer.WriteLine("  1. 光束分布接近平顶分布而非高斯分布");
                    writer.WriteLine("  2. 测量角度范围不够宽");
                    writer.WriteLine("  3. 数据中存在噪声或背景");
                    
                    _output.WriteLine($"注意: 能量占比({result.FW1e2PowerContainment:P1})较高，可能原因:");
                    _output.WriteLine("  1. 光束分布接近平顶分布而非高斯分布");
                    _output.WriteLine("  2. 测量角度范围不够宽");
                    _output.WriteLine("  3. 数据中存在噪声或背景");
                }
                else if (result.FW1e2PowerContainment < 0.8)
                {
                    writer.WriteLine($"注意: 能量占比({result.FW1e2PowerContainment:P1})较低，可能原因:");
                    writer.WriteLine("  1. 光束质量较差，有较多旁瓣");
                    writer.WriteLine("  2. 多模激光输出");
                    writer.WriteLine("  3. 测量或数据处理问题");
                    
                    _output.WriteLine($"注意: 能量占比({result.FW1e2PowerContainment:P1})较低，可能原因:");
                    _output.WriteLine("  1. 光束质量较差，有较多旁瓣");
                    _output.WriteLine("  2. 多模激光输出");
                    _output.WriteLine("  3. 测量或数据处理问题");
                }
                
                // 验证发散角顺序
                if (result.FWHM > 0 && result.FW1e2 > 0 && result.FW95 > 0)
                {
                    bool orderCorrect = result.FWHM < result.FW1e2 && result.FW1e2 < result.FW95;
                    writer.WriteLine($"\n发散角大小顺序检查: {(orderCorrect ? "正确" : "异常")}");
                    writer.WriteLine($"FWHM < FW(1/e²) < FW95%: {result.FWHM:F2}° < {result.FW1e2:F2}° < {result.FW95:F2}°");
                    
                    _output.WriteLine($"\n发散角大小顺序检查: {(orderCorrect ? "正确" : "异常")}");
                    _output.WriteLine($"FWHM < FW(1/e²) < FW95%: {result.FWHM:F2}° < {result.FW1e2:F2}° < {result.FW95:F2}°");
                }
            }
            else
            {
                writer.WriteLine($"计算失败: {result.ErrorMessage}");
                _output.WriteLine($"计算失败: {result.ErrorMessage}");
            }
        }
    }
}