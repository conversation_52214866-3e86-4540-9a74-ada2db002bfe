<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- ==============================================
         Semantic Color System - WPF兼容版本
         ============================================== -->

    <!-- 使用DynamicResource支持主题切换 -->
    
    <!-- Primary Colors -->
    <Color x:Key="PrimaryColor">#FF0078D4</Color>
    <Color x:Key="PrimaryLightColor">#FF106EBE</Color>
    <Color x:Key="PrimaryDarkColor">#FF005BA1</Color>
    <SolidColorBrush x:Key="PrimaryBrush" Color="{DynamicResource PrimaryColor}"/>
    <SolidColorBrush x:Key="PrimaryLightBrush" Color="{DynamicResource PrimaryLightColor}"/>
    <SolidColorBrush x:Key="PrimaryDarkBrush" Color="{DynamicResource PrimaryDarkColor}"/>

    <!-- Secondary Colors -->
    <Color x:Key="SecondaryColor">#FF8E8CD8</Color>
    <Color x:Key="SecondaryLightColor">#FFA7A5E4</Color>
    <Color x:Key="SecondaryDarkColor">#FF7573CC</Color>
    <SolidColorBrush x:Key="SecondaryBrush" Color="{DynamicResource SecondaryColor}"/>
    <SolidColorBrush x:Key="SecondaryLightBrush" Color="{DynamicResource SecondaryLightColor}"/>
    <SolidColorBrush x:Key="SecondaryDarkBrush" Color="{DynamicResource SecondaryDarkColor}"/>

    <!-- Accent Colors -->
    <Color x:Key="AccentColor">#FF00BCF2</Color>
    <SolidColorBrush x:Key="AccentBrush" Color="{DynamicResource AccentColor}"/>

    <!-- Surface Colors -->
    <Color x:Key="SurfaceColor">#FFFFFFFF</Color>
    <Color x:Key="SurfaceVariantColor">#FFF8F9FA</Color>
    <Color x:Key="SurfaceContainerColor">#FFF3F4F6</Color>
    <SolidColorBrush x:Key="SurfaceBrush" Color="{DynamicResource SurfaceColor}"/>
    <SolidColorBrush x:Key="SurfaceVariantBrush" Color="{DynamicResource SurfaceVariantColor}"/>
    <SolidColorBrush x:Key="SurfaceContainerBrush" Color="{DynamicResource SurfaceContainerColor}"/>

    <!-- Background Colors -->
    <Color x:Key="BackgroundColor">#FFFAFAFA</Color>
    <Color x:Key="BackgroundSecondaryColor">#FFF5F5F5</Color>
    <SolidColorBrush x:Key="BackgroundBrush" Color="{DynamicResource BackgroundColor}"/>
    <SolidColorBrush x:Key="BackgroundSecondaryBrush" Color="{DynamicResource BackgroundSecondaryColor}"/>

    <!-- Text Colors -->
    <Color x:Key="OnSurfaceColor">#FF1F2937</Color>
    <Color x:Key="OnSurfaceVariantColor">#FF6B7280</Color>
    <Color x:Key="OnSurfaceDisabledColor">#FF9CA3AF</Color>
    <SolidColorBrush x:Key="OnSurfaceBrush" Color="{DynamicResource OnSurfaceColor}"/>
    <SolidColorBrush x:Key="OnSurfaceVariantBrush" Color="{DynamicResource OnSurfaceVariantColor}"/>
    <SolidColorBrush x:Key="OnSurfaceDisabledBrush" Color="{DynamicResource OnSurfaceDisabledColor}"/>

    <!-- Border Colors -->
    <Color x:Key="OutlineColor">#FFE5E7EB</Color>
    <Color x:Key="OutlineVariantColor">#FFD1D5DB</Color>
    <SolidColorBrush x:Key="OutlineBrush" Color="{DynamicResource OutlineColor}"/>
    <SolidColorBrush x:Key="OutlineVariantBrush" Color="{DynamicResource OutlineVariantColor}"/>

    <!-- Status Colors -->
    <Color x:Key="SuccessColor">#FF10B981</Color>
    <Color x:Key="WarningColor">#FFF59E0B</Color>
    <Color x:Key="ErrorColor">#FFEF4444</Color>
    <Color x:Key="InfoColor">#FF3B82F6</Color>
    <SolidColorBrush x:Key="SuccessBrush" Color="{DynamicResource SuccessColor}"/>
    <SolidColorBrush x:Key="WarningBrush" Color="{DynamicResource WarningColor}"/>
    <SolidColorBrush x:Key="ErrorBrush" Color="{DynamicResource ErrorColor}"/>
    <SolidColorBrush x:Key="InfoBrush" Color="{DynamicResource InfoColor}"/>

    <!-- Chart Colors -->
    <Color x:Key="Chart1Color">#FF0078D4</Color>
    <Color x:Key="Chart2Color">#FFDC143C</Color>
    <Color x:Key="Chart3Color">#FF32CD32</Color>
    <Color x:Key="Chart4Color">#FFFF8C00</Color>
    <Color x:Key="Chart5Color">#FF8A2BE2</Color>
    <SolidColorBrush x:Key="Chart1Brush" Color="{DynamicResource Chart1Color}"/>
    <SolidColorBrush x:Key="Chart2Brush" Color="{DynamicResource Chart2Color}"/>
    <SolidColorBrush x:Key="Chart3Brush" Color="{DynamicResource Chart3Color}"/>
    <SolidColorBrush x:Key="Chart4Brush" Color="{DynamicResource Chart4Color}"/>
    <SolidColorBrush x:Key="Chart5Brush" Color="{DynamicResource Chart5Color}"/>

</ResourceDictionary>