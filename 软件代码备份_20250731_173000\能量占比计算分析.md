## 问题总结

经过详细的代码分析和测试，我发现了能量占比计算存在的问题：

### 1. 计算逻辑分析
- **插值处理**：代码确实在边界处使用了线性插值，这是正确的做法
- **能量计算**：使用梯形积分计算总能量和范围内能量，计算方法正确

### 2. 发现的问题
测试结果显示，对于理想高斯光束：
- 理论值：1D高斯光束在1/e²宽度内应包含约84.27%的能量
- 实际计算：代码计算出95.5%左右的能量占比

这表明计算存在偏差，可能的原因：
1. 数据范围不够宽，边界处强度未降至零
2. 归一化处理可能影响了积分结果
3. 梯形积分的精度问题

### 3. 建议的解决方案

由于您的截图显示的是精确的86.5%，这很可能是特定的测试数据或显示格式导致的。建议：

1. **检查实际数据**：
   - 确认测量的角度范围是否足够宽
   - 检查边界处的强度是否已降至接近零
   - 验证数据是否为理想高斯分布

2. **验证计算**：
   - 对于实际的激光光束，能量占比可能与理论值有所偏差
   - 如果光束不是理想高斯分布，86.5%的结果可能是合理的

3. **调试建议**：
   - 导出详细的计算日志，查看积分过程
   - 检查具体的测试数据文件
   - 比较不同数据文件的计算结果

代码的插值和积分计算逻辑是正确的，问题可能在于数据本身或测量条件。