using System;

namespace LIVAnalyzer.Core.Configuration
{
    /// <summary>
    /// 阈值电流计算的配置参数
    /// 用于替代硬编码的参数，提供更好的灵活性和适应性
    /// </summary>
    public class ThresholdCalculationConfig
    {
        /// <summary>
        /// 搜索范围比例 - 在前多少比例的电流范围内搜索阈值
        /// 默认值：0.5 (前50%的电流范围)
        /// 适用场景：
        /// - VCSEL激光器：0.3-0.4 (阈值通常较低)
        /// - 边发射激光器：0.5-0.6 (阈值中等)
        /// - 高功率激光器：0.6-0.8 (阈值可能较高)
        /// </summary>
        public double SearchRangeRatio { get; set; } = 0.5;
        
        /// <summary>
        /// 最大阈值比例 - 阈值电流不应超过最大电流的此比例
        /// 默认值：0.3 (30%的最大电流)
        /// 物理意义：超过此比例可能不是真实的阈值，而是测量范围问题
        /// </summary>
        public double MaxThresholdRatio { get; set; } = 0.3;
        
        /// <summary>
        /// 次级最大阈值比例 - 第二层约束检查
        /// 默认值：0.5 (50%的最大电流)
        /// 用于最终的合理性检查
        /// </summary>
        public double SecondaryMaxThresholdRatio { get; set; } = 0.5;
        
        /// <summary>
        /// 主要回退功率比例 - 当阈值计算失败时，使用功率达到此比例的点作为阈值
        /// 默认值：0.05 (5%的最大功率)
        /// 物理意义：功率开始显著增长的点
        /// </summary>
        public double PrimaryFallbackPowerRatio { get; set; } = 0.05;
        
        /// <summary>
        /// 次级回退功率比例 - 主要回退也失败时使用
        /// 默认值：0.01 (1%的最大功率)
        /// 用于极端情况下的最后回退
        /// </summary>
        public double SecondaryFallbackPowerRatio { get; set; } = 0.01;
        
        /// <summary>
        /// 第三级回退功率比例 - 用于最终的合理性检查回退
        /// 默认值：0.02 (2%的最大功率)
        /// </summary>
        public double TertiaryFallbackPowerRatio { get; set; } = 0.02;
        
        /// <summary>
        /// 最大平滑窗口大小 - 功率数据平滑的最大窗口
        /// 默认值：31
        /// 影响：窗口越大，平滑效果越强，但可能丢失细节
        /// </summary>
        public int MaxSmoothingWindow { get; set; } = 31;
        
        /// <summary>
        /// 数据窗口比例 - 平滑窗口大小相对于数据点数的比例
        /// 默认值：0.2 (数据点数的1/5)
        /// 自适应窗口大小计算：Math.Max(3, dataCount * DataWindowRatio)
        /// </summary>
        public double DataWindowRatio { get; set; } = 0.2;
        
        /// <summary>
        /// 最大导数平滑窗口 - 导数数据平滑的最大窗口
        /// 默认值：5
        /// 用于平滑计算出的导数，减少噪声影响
        /// </summary>
        public int MaxDerivativeSmoothingWindow { get; set; } = 5;
        
        /// <summary>
        /// 导数窗口比例 - 导数平滑窗口相对于导数数组长度的比例
        /// 默认值：0.33 (约1/3)
        /// </summary>
        public double DerivativeWindowRatio { get; set; } = 0.33;
        
        /// <summary>
        /// 导数比例 - 用于半最大值判据的比例
        /// 默认值：0.5 (导数最大值的一半)
        /// 理论基础：半最大值法是标准的阈值检测方法
        /// 可调整范围：0.3-0.7，根据数据特性优化
        /// </summary>
        public double DerivativeRatio { get; set; } = 0.5;
        
        /// <summary>
        /// 最小数据点数 - 进行阈值计算所需的最少数据点
        /// 默认值：10
        /// 少于此数量的数据点无法进行可靠的阈值计算
        /// </summary>
        public int MinDataPoints { get; set; } = 10;
        
        /// <summary>
        /// 数值精度阈值 - 用于避免除零和数值比较
        /// 默认值：1e-10
        /// 用于电流差值和导数计算的精度控制
        /// </summary>
        public double NumericalPrecision { get; set; } = 1e-10;
        
        /// <summary>
        /// 验证配置参数的有效性
        /// </summary>
        public void Validate()
        {
            if (SearchRangeRatio <= 0 || SearchRangeRatio > 1)
                throw new ArgumentOutOfRangeException(nameof(SearchRangeRatio), "搜索范围比例必须在(0,1]之间");
                
            if (MaxThresholdRatio <= 0 || MaxThresholdRatio > 1)
                throw new ArgumentOutOfRangeException(nameof(MaxThresholdRatio), "最大阈值比例必须在(0,1]之间");
                
            if (SecondaryMaxThresholdRatio <= 0 || SecondaryMaxThresholdRatio > 1)
                throw new ArgumentOutOfRangeException(nameof(SecondaryMaxThresholdRatio), "次级最大阈值比例必须在(0,1]之间");
                
            if (PrimaryFallbackPowerRatio <= 0 || PrimaryFallbackPowerRatio > 1)
                throw new ArgumentOutOfRangeException(nameof(PrimaryFallbackPowerRatio), "主要回退功率比例必须在(0,1]之间");
                
            if (SecondaryFallbackPowerRatio <= 0 || SecondaryFallbackPowerRatio > 1)
                throw new ArgumentOutOfRangeException(nameof(SecondaryFallbackPowerRatio), "次级回退功率比例必须在(0,1]之间");
                
            if (TertiaryFallbackPowerRatio <= 0 || TertiaryFallbackPowerRatio > 1)
                throw new ArgumentOutOfRangeException(nameof(TertiaryFallbackPowerRatio), "第三级回退功率比例必须在(0,1]之间");
                
            if (MaxSmoothingWindow < 3 || MaxSmoothingWindow % 2 == 0)
                throw new ArgumentOutOfRangeException(nameof(MaxSmoothingWindow), "最大平滑窗口必须是大于等于3的奇数");
                
            if (DataWindowRatio <= 0 || DataWindowRatio > 1)
                throw new ArgumentOutOfRangeException(nameof(DataWindowRatio), "数据窗口比例必须在(0,1]之间");
                
            if (MaxDerivativeSmoothingWindow < 3 || MaxDerivativeSmoothingWindow % 2 == 0)
                throw new ArgumentOutOfRangeException(nameof(MaxDerivativeSmoothingWindow), "最大导数平滑窗口必须是大于等于3的奇数");
                
            if (DerivativeWindowRatio <= 0 || DerivativeWindowRatio > 1)
                throw new ArgumentOutOfRangeException(nameof(DerivativeWindowRatio), "导数窗口比例必须在(0,1]之间");
                
            if (DerivativeRatio <= 0 || DerivativeRatio > 1)
                throw new ArgumentOutOfRangeException(nameof(DerivativeRatio), "导数比例必须在(0,1]之间");
                
            if (MinDataPoints < 3)
                throw new ArgumentOutOfRangeException(nameof(MinDataPoints), "最小数据点数必须大于等于3");
                
            if (NumericalPrecision <= 0)
                throw new ArgumentOutOfRangeException(nameof(NumericalPrecision), "数值精度阈值必须大于0");
        }
        
        /// <summary>
        /// 创建默认配置
        /// </summary>
        public static ThresholdCalculationConfig Default => new ThresholdCalculationConfig();
        
        /// <summary>
        /// 克隆配置对象
        /// </summary>
        public ThresholdCalculationConfig Clone()
        {
            return new ThresholdCalculationConfig
            {
                SearchRangeRatio = this.SearchRangeRatio,
                MaxThresholdRatio = this.MaxThresholdRatio,
                SecondaryMaxThresholdRatio = this.SecondaryMaxThresholdRatio,
                PrimaryFallbackPowerRatio = this.PrimaryFallbackPowerRatio,
                SecondaryFallbackPowerRatio = this.SecondaryFallbackPowerRatio,
                TertiaryFallbackPowerRatio = this.TertiaryFallbackPowerRatio,
                MaxSmoothingWindow = this.MaxSmoothingWindow,
                DataWindowRatio = this.DataWindowRatio,
                MaxDerivativeSmoothingWindow = this.MaxDerivativeSmoothingWindow,
                DerivativeWindowRatio = this.DerivativeWindowRatio,
                DerivativeRatio = this.DerivativeRatio,
                MinDataPoints = this.MinDataPoints,
                NumericalPrecision = this.NumericalPrecision
            };
        }
    }
}
