import type { ProcessingConfig } from '../types/data';

/**
 * 阈值电流计算的预设配置
 * 针对不同类型的激光器和测试条件提供优化的参数组合
 */

// 默认配置（边发射激光器）
export const DEFAULT_THRESHOLD_CONFIG: ProcessingConfig['threshold'] = {
  searchRangeRatio: 0.5,
  maxThresholdRatio: 0.3,
  secondaryMaxThresholdRatio: 0.5,
  primaryFallbackPowerRatio: 0.05,
  secondaryFallbackPowerRatio: 0.01,
  tertiaryFallbackPowerRatio: 0.02,
  maxSmoothingWindow: 31,
  dataWindowRatio: 0.2,
  maxDerivativeSmoothingWindow: 5,
  derivativeWindowRatio: 0.33,
  derivativeRatio: 0.5,
  minDataPoints: 10,
  numericalPrecision: 1e-10,
};

// VCSEL (垂直腔面发射激光器) 配置
export const VCSEL_THRESHOLD_CONFIG: ProcessingConfig['threshold'] = {
  searchRangeRatio: 0.4,
  maxThresholdRatio: 0.25,
  secondaryMaxThresholdRatio: 0.4,
  primaryFallbackPowerRatio: 0.03,
  secondaryFallbackPowerRatio: 0.008,
  tertiaryFallbackPowerRatio: 0.015,
  maxSmoothingWindow: 15,
  dataWindowRatio: 0.15,
  maxDerivativeSmoothingWindow: 3,
  derivativeWindowRatio: 0.25,
  derivativeRatio: 0.45,
  minDataPoints: 8,
  numericalPrecision: 1e-12,
};

// 边发射激光器配置
export const EDGE_EMITTING_THRESHOLD_CONFIG: ProcessingConfig['threshold'] = {
  searchRangeRatio: 0.6,
  maxThresholdRatio: 0.4,
  secondaryMaxThresholdRatio: 0.6,
  primaryFallbackPowerRatio: 0.05,
  secondaryFallbackPowerRatio: 0.01,
  tertiaryFallbackPowerRatio: 0.02,
  maxSmoothingWindow: 25,
  dataWindowRatio: 0.2,
  maxDerivativeSmoothingWindow: 5,
  derivativeWindowRatio: 0.33,
  derivativeRatio: 0.5,
  minDataPoints: 10,
  numericalPrecision: 1e-10,
};

// 高功率激光器配置
export const HIGH_POWER_THRESHOLD_CONFIG: ProcessingConfig['threshold'] = {
  searchRangeRatio: 0.8,
  maxThresholdRatio: 0.6,
  secondaryMaxThresholdRatio: 0.8,
  primaryFallbackPowerRatio: 0.08,
  secondaryFallbackPowerRatio: 0.02,
  tertiaryFallbackPowerRatio: 0.04,
  maxSmoothingWindow: 41,
  dataWindowRatio: 0.25,
  maxDerivativeSmoothingWindow: 7,
  derivativeWindowRatio: 0.4,
  derivativeRatio: 0.55,
  minDataPoints: 15,
  numericalPrecision: 1e-10,
};

// 量子级联激光器配置
export const QUANTUM_CASCADE_THRESHOLD_CONFIG: ProcessingConfig['threshold'] = {
  searchRangeRatio: 0.9,
  maxThresholdRatio: 0.7,
  secondaryMaxThresholdRatio: 0.9,
  primaryFallbackPowerRatio: 0.1,
  secondaryFallbackPowerRatio: 0.03,
  tertiaryFallbackPowerRatio: 0.05,
  maxSmoothingWindow: 51,
  dataWindowRatio: 0.3,
  maxDerivativeSmoothingWindow: 9,
  derivativeWindowRatio: 0.5,
  derivativeRatio: 0.6,
  minDataPoints: 20,
  numericalPrecision: 1e-10,
};

// 低噪声实验室环境配置
export const LOW_NOISE_THRESHOLD_CONFIG: ProcessingConfig['threshold'] = {
  searchRangeRatio: 0.5,
  maxThresholdRatio: 0.3,
  secondaryMaxThresholdRatio: 0.5,
  primaryFallbackPowerRatio: 0.02,
  secondaryFallbackPowerRatio: 0.005,
  tertiaryFallbackPowerRatio: 0.01,
  maxSmoothingWindow: 9,
  dataWindowRatio: 0.1,
  maxDerivativeSmoothingWindow: 3,
  derivativeWindowRatio: 0.2,
  derivativeRatio: 0.4,
  minDataPoints: 8,
  numericalPrecision: 1e-12,
};

// 高噪声工业环境配置
export const HIGH_NOISE_THRESHOLD_CONFIG: ProcessingConfig['threshold'] = {
  searchRangeRatio: 0.6,
  maxThresholdRatio: 0.5,
  secondaryMaxThresholdRatio: 0.7,
  primaryFallbackPowerRatio: 0.1,
  secondaryFallbackPowerRatio: 0.03,
  tertiaryFallbackPowerRatio: 0.05,
  maxSmoothingWindow: 61,
  dataWindowRatio: 0.4,
  maxDerivativeSmoothingWindow: 11,
  derivativeWindowRatio: 0.6,
  derivativeRatio: 0.6,
  minDataPoints: 25,
  numericalPrecision: 1e-8,
};

/**
 * 根据激光器类型获取推荐配置
 */
export function getRecommendedThresholdConfig(laserType?: string): ProcessingConfig['threshold'] {
  if (!laserType) return DEFAULT_THRESHOLD_CONFIG;
  
  const type = laserType.toLowerCase().replace(/[-_\s]/g, '');
  
  switch (type) {
    case 'vcsel':
      return VCSEL_THRESHOLD_CONFIG;
    case 'edgeemitting':
    case 'edge':
      return EDGE_EMITTING_THRESHOLD_CONFIG;
    case 'highpower':
      return HIGH_POWER_THRESHOLD_CONFIG;
    case 'quantumcascade':
    case 'qcl':
      return QUANTUM_CASCADE_THRESHOLD_CONFIG;
    case 'lownoise':
      return LOW_NOISE_THRESHOLD_CONFIG;
    case 'highnoise':
      return HIGH_NOISE_THRESHOLD_CONFIG;
    default:
      return DEFAULT_THRESHOLD_CONFIG;
  }
}

/**
 * 获取所有可用的预设配置
 */
export function getAvailablePresets(): Array<{ name: string; label: string; config: ProcessingConfig['threshold'] }> {
  return [
    { name: 'default', label: '默认 (边发射)', config: DEFAULT_THRESHOLD_CONFIG },
    { name: 'vcsel', label: 'VCSEL', config: VCSEL_THRESHOLD_CONFIG },
    { name: 'edge-emitting', label: '边发射激光器', config: EDGE_EMITTING_THRESHOLD_CONFIG },
    { name: 'high-power', label: '高功率激光器', config: HIGH_POWER_THRESHOLD_CONFIG },
    { name: 'quantum-cascade', label: '量子级联激光器', config: QUANTUM_CASCADE_THRESHOLD_CONFIG },
    { name: 'low-noise', label: '低噪声环境', config: LOW_NOISE_THRESHOLD_CONFIG },
    { name: 'high-noise', label: '高噪声环境', config: HIGH_NOISE_THRESHOLD_CONFIG },
  ];
}

/**
 * 合并用户配置和预设配置
 */
export function mergeThresholdConfig(
  userConfig?: ProcessingConfig['threshold'],
  preset?: string
): ProcessingConfig['threshold'] {
  const baseConfig = getRecommendedThresholdConfig(preset);
  
  if (!userConfig) return baseConfig;
  
  return {
    ...baseConfig,
    ...userConfig,
  };
}
