with open(r'E:\01LaserPackage\software\LIV_Analyzer\CSharp_Version\LIVAnalyzer.UI\ViewModels\MainWindowViewModel.cs', 'rb') as f:
    content = f.read()
    
# 查找第1922行(索引1921)
lines = content.split(b'\r\n')
line_1922 = lines[1921]

print(f"Line 1922 bytes: {line_1922}")
print(f"Line 1922 hex: {line_1922.hex()}")

# 检查是否有不可见字符
for i, byte in enumerate(line_1922):
    if byte < 32 or byte > 126:
        print(f"Special character at position {i}: {byte} (0x{byte:02x})")