using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;
using LIVAnalyzer.Models;

namespace LIVAnalyzer.UI.Converters
{
    /// <summary>
    /// 平滑算法类型到可见性转换器
    /// </summary>
    public class AlgorithmTypeToVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is SmoothingAlgorithmType algorithmType && parameter is string targetAlgorithm)
            {
                if (Enum.TryParse<SmoothingAlgorithmType>(targetAlgorithm, out var target))
                {
                    return algorithmType == target ? Visibility.Visible : Visibility.Collapsed;
                }
            }
            
            return Visibility.Collapsed;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}