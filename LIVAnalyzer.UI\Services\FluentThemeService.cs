using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Media;
using LIVAnalyzer.Services.Configuration;

namespace LIVAnalyzer.UI.Services
{
    /// <summary>
    /// Fluent Design 主题模式枚举
    /// </summary>
    public enum FluentThemeMode
    {
        System,  // 跟随系统设置
        Light,   // 浅色主题
        Dark     // 深色主题
    }

    /// <summary>
    /// 主题变更事件参数
    /// </summary>
    public class ThemeChangedEventArgs : EventArgs
    {
        public FluentThemeMode ThemeMode { get; }
        public string AccentColor { get; }

        public ThemeChangedEventArgs(FluentThemeMode themeMode, string accentColor)
        {
            ThemeMode = themeMode;
            AccentColor = accentColor;
        }
    }

    /// <summary>
    /// Fluent Design 主题服务
    /// 支持 .NET 9 原生 Fluent 主题和自定义主题扩展
    /// </summary>
    public class FluentThemeService
    {
        private static FluentThemeService? _instance;
        public static FluentThemeService Instance => _instance ??= new FluentThemeService();

        private FluentThemeMode _currentThemeMode = FluentThemeMode.System;
        private string _currentAccentColor = "Windows 默认";

        /// <summary>
        /// 主题变更事件
        /// </summary>
        public event EventHandler<ThemeChangedEventArgs>? ThemeChanged;

        /// <summary>
        /// 当前主题模式
        /// </summary>
        public FluentThemeMode CurrentThemeMode => _currentThemeMode;

        /// <summary>
        /// 当前强调色
        /// </summary>
        public string CurrentAccentColor => _currentAccentColor;

        /// <summary>
        /// 预定义的主题色
        /// </summary>
        public readonly Dictionary<string, Color> PredefinedColors = new()
        {
            { "Windows 默认", Color.FromArgb(255, 0, 120, 215) },
            { "活力橙", Color.FromArgb(255, 255, 140, 0) },
            { "自然绿", Color.FromArgb(255, 0, 178, 148) },
            { "优雅紫", Color.FromArgb(255, 135, 100, 184) },
            { "热情红", Color.FromArgb(255, 229, 20, 0) },
            { "科技青", Color.FromArgb(255, 0, 188, 212) },
            { "专业灰", Color.FromArgb(255, 96, 125, 139) },
            { "活力粉", Color.FromArgb(255, 233, 30, 99) }
        };

        /// <summary>
        /// 主题资源字典 URI
        /// </summary>
        private readonly Dictionary<FluentThemeMode, Uri> _themeResourceUris = new()
        {
            { FluentThemeMode.Light, new Uri("Themes/FluentLightTheme.xaml", UriKind.Relative) },
            { FluentThemeMode.Dark, new Uri("Themes/FluentDarkTheme.xaml", UriKind.Relative) }
        };

        private FluentThemeService()
        {
            // 初始化时检测系统主题
            _currentThemeMode = DetectSystemTheme();
            LoadThemeSettings();
        }

        /// <summary>
        /// 设置主题模式
        /// </summary>
        /// <param name="mode">主题模式</param>
        public void SetThemeMode(FluentThemeMode mode)
        {
            if (_currentThemeMode == mode) return;

            _currentThemeMode = mode;
            
            // 根据模式决定实际使用的主题
            var actualTheme = mode == FluentThemeMode.System ? DetectSystemTheme() : mode;
            
            ApplyTheme(actualTheme);
            SaveThemeSettings();
            
            // 触发主题变更事件
            ThemeChanged?.Invoke(this, new ThemeChangedEventArgs(_currentThemeMode, _currentAccentColor));
        }

        /// <summary>
        /// 设置强调色
        /// </summary>
        /// <param name="colorName">颜色名称</param>
        public void SetAccentColor(string colorName)
        {
            if (_currentAccentColor == colorName) return;

            if (PredefinedColors.TryGetValue(colorName, out var color))
            {
                _currentAccentColor = colorName;
                ApplyAccentColor(color);
                SaveThemeSettings();
                
                // 触发主题变更事件
                ThemeChanged?.Invoke(this, new ThemeChangedEventArgs(_currentThemeMode, _currentAccentColor));
            }
        }

        /// <summary>
        /// 设置自定义颜色
        /// </summary>
        /// <param name="color">自定义颜色</param>
        public void SetCustomAccentColor(Color color)
        {
            _currentAccentColor = "自定义";
            ApplyAccentColor(color);
            SaveThemeSettings();
            
            ThemeChanged?.Invoke(this, new ThemeChangedEventArgs(_currentThemeMode, _currentAccentColor));
        }

        /// <summary>
        /// 切换明暗主题
        /// </summary>
        public void ToggleTheme()
        {
            var newMode = _currentThemeMode switch
            {
                FluentThemeMode.Light => FluentThemeMode.Dark,
                FluentThemeMode.Dark => FluentThemeMode.Light,
                FluentThemeMode.System => DetectSystemTheme() == FluentThemeMode.Light 
                    ? FluentThemeMode.Dark 
                    : FluentThemeMode.Light,
                _ => FluentThemeMode.Light
            };
            
            SetThemeMode(newMode);
        }

        /// <summary>
        /// 检测系统主题
        /// </summary>
        /// <returns>系统当前主题模式</returns>
        public FluentThemeMode DetectSystemTheme()
        {
            try
            {
                // 读取 Windows 注册表获取系统主题设置
                using var key = Microsoft.Win32.Registry.CurrentUser.OpenSubKey(@"Software\Microsoft\Windows\CurrentVersion\Themes\Personalize");
                if (key?.GetValue("AppsUseLightTheme") is int useLightTheme)
                {
                    return useLightTheme == 1 ? FluentThemeMode.Light : FluentThemeMode.Dark;
                }
            }
            catch
            {
                // 如果无法读取注册表，默认使用浅色主题
            }
            
            return FluentThemeMode.Light;
        }

        /// <summary>
        /// 应用主题
        /// </summary>
        /// <param name="theme">要应用的主题</param>
        private void ApplyTheme(FluentThemeMode theme)
        {
            if (Application.Current?.Resources == null) return;

            try
            {
                // 查找并移除现有的主题资源字典
                ResourceDictionary? themeToRemove = null;
                foreach (ResourceDictionary dict in Application.Current.Resources.MergedDictionaries)
                {
                    if (dict.Source?.OriginalString.Contains("FluentLightTheme.xaml") == true ||
                        dict.Source?.OriginalString.Contains("FluentDarkTheme.xaml") == true)
                    {
                        themeToRemove = dict;
                        break;
                    }
                }

                if (themeToRemove != null)
                {
                    Application.Current.Resources.MergedDictionaries.Remove(themeToRemove);
                }

                // 添加新的主题资源字典
                if (_themeResourceUris.TryGetValue(theme, out var uri))
                {
                    var newThemeDict = new ResourceDictionary { Source = uri };
                    Application.Current.Resources.MergedDictionaries.Add(newThemeDict);
                }
            }
            catch (Exception ex)
            {
                // 记录错误但不中断程序运行
                System.Diagnostics.Debug.WriteLine($"应用主题时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 应用强调色
        /// </summary>
        /// <param name="color">强调色</param>
        private void ApplyAccentColor(Color color)
        {
            try
            {
                // 更新应用程序资源中的强调色
                if (Application.Current?.Resources != null)
                {
                    Application.Current.Resources["SystemAccentColor"] = color;
                    Application.Current.Resources["SystemAccentColorBrush"] = new SolidColorBrush(color);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"应用强调色时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取当前主题的图表配色方案
        /// </summary>
        /// <returns>图表颜色数组</returns>
        public Color[] GetCurrentChartColors()
        {
            var actualTheme = _currentThemeMode == FluentThemeMode.System ? DetectSystemTheme() : _currentThemeMode;
            
            try
            {
                var resourceKey = actualTheme == FluentThemeMode.Dark ? "FluentDarkChartColors" : "FluentLightChartColors";
                
                if (Application.Current?.Resources[resourceKey] is Color[] colors)
                {
                    return colors;
                }
            }
            catch
            {
                // 如果获取失败，返回默认配色
            }

            // 默认配色方案
            return actualTheme == FluentThemeMode.Dark 
                ? new[] { Color.FromRgb(96, 165, 250), Color.FromRgb(52, 211, 153), Color.FromRgb(251, 191, 36) }
                : new[] { Color.FromRgb(0, 120, 212), Color.FromRgb(0, 188, 242), Color.FromRgb(0, 204, 106) };
        }

        /// <summary>
        /// 保存主题设置
        /// </summary>
        private void SaveThemeSettings()
        {
            try
            {
                var config = ConfigurationManager.Instance.GetConfig();
                config.UI.Theme = _currentThemeMode.ToString();
                config.UI.ThemeColor = _currentAccentColor;
                ConfigurationManager.Instance.SaveConfiguration();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"保存主题设置时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 加载主题设置
        /// </summary>
        private void LoadThemeSettings()
        {
            try
            {
                var config = ConfigurationManager.Instance.GetConfig();
                
                if (Enum.TryParse<FluentThemeMode>(config.UI.Theme, out var savedMode))
                {
                    _currentThemeMode = savedMode;
                }

                if (!string.IsNullOrEmpty(config.UI.ThemeColor))
                {
                    _currentAccentColor = config.UI.ThemeColor;
                }

                // 应用加载的设置
                var actualTheme = _currentThemeMode == FluentThemeMode.System ? DetectSystemTheme() : _currentThemeMode;
                ApplyTheme(actualTheme);
                
                if (PredefinedColors.TryGetValue(_currentAccentColor, out var color))
                {
                    ApplyAccentColor(color);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载主题设置时发生错误: {ex.Message}");
            }
        }
    }
}