import { useAppStore } from '../state/store';
import { exportCSV } from '../services/export';

export default function ResultSummary() {
  const { results } = useAppStore();
  const r = results?.livParameters;
  const sp = results?.spectralParameters;
  const dv = results?.divergenceParameters;
  if (!r && !sp) return null;
  return (
    <div className="space-y-3 text-sm">
      <div className="grid grid-cols-2 gap-3">
      {r?.thresholdCurrent_mA !== undefined && (
        <div className="flex justify-between"><span>Ith</span><span>{r.thresholdCurrent_mA.toFixed(2)} mA</span></div>
      )}
      {r?.slopeEfficiency_W_per_A !== undefined && (
        <div className="flex justify-between"><span>ηd</span><span>{r.slopeEfficiency_W_per_A.toFixed(4)} W/A</span></div>
      )}
      {r?.seriesResistance_Ohm !== undefined && (
        <div className="flex justify-between"><span>Rs</span><span>{r.seriesResistance_Ohm.toFixed(4)} Ω</span></div>
      )}
      {r?.maxEfficiency !== undefined && (
        <div className="flex justify-between"><span>ηmax</span><span>{(r.maxEfficiency * 100).toFixed(2)} %</span></div>
      )}
      {sp?.peakWavelength_nm !== undefined && (
        <div className="flex justify-between"><span>λpeak</span><span>{sp.peakWavelength_nm.toFixed(2)} nm</span></div>
      )}
      {sp?.fwhm_nm !== undefined && (
        <div className="flex justify-between"><span>FWHM</span><span>{sp.fwhm_nm.toFixed(3)} nm</span></div>
      )}
      {sp?.centroid_nm !== undefined && (
        <div className="flex justify-between"><span>重心</span><span>{sp.centroid_nm.toFixed(2)} nm</span></div>
      )}
      {sp?.smsr_dB !== undefined && (
        <div className="flex justify-between"><span>SMSR</span><span>{sp.smsr_dB.toFixed(2)} dB</span></div>
      )}
      {dv?.horizontal_deg !== undefined && (
        <div className="flex justify-between"><span>θ∥(HFF)</span><span>{dv.horizontal_deg.toFixed(2)} °</span></div>
      )}
      {dv?.vertical_deg !== undefined && (
        <div className="flex justify-between"><span>θ⊥(VFF)</span><span>{dv.vertical_deg.toFixed(2)} °</span></div>
      )}
      {dv?.ellipticity !== undefined && (
        <div className="flex justify-between"><span>椭圆度</span><span>{dv.ellipticity.toFixed(3)}</span></div>
      )}
      </div>
      <div className="flex items-center gap-3">
        <button className="underline" onClick={() => {
          exportCSV('results', ['Metric','Value'], [
            ['Ith_mA', r?.thresholdCurrent_mA ?? ''],
            ['Slope_W_A', r?.slopeEfficiency_W_per_A ?? ''],
            ['Rs_Ohm', r?.seriesResistance_Ohm ?? ''],
            ['EtaMax', r?.maxEfficiency ?? ''],
            ['LambdaPeak_nm', sp?.peakWavelength_nm ?? ''],
            ['FWHM_nm', sp?.fwhm_nm ?? ''],
            ['Centroid_nm', sp?.centroid_nm ?? ''],
            ['SMSR_dB', sp?.smsr_dB ?? ''],
            ['Theta_H_deg', dv?.horizontal_deg ?? ''],
            ['Theta_V_deg', dv?.vertical_deg ?? ''],
            ['Ellipticity', dv?.ellipticity ?? ''],
          ]);
        }}>导出结果CSV</button>
        <button className="underline" onClick={async () => {
          const XLSX = await import('xlsx');
          const wb = XLSX.utils.book_new();
          const rows = [
            ['Metric','Value'],
            ['Ith_mA', r?.thresholdCurrent_mA ?? ''],
            ['Slope_W_A', r?.slopeEfficiency_W_per_A ?? ''],
            ['Rs_Ohm', r?.seriesResistance_Ohm ?? ''],
            ['EtaMax', r?.maxEfficiency ?? ''],
            ['LambdaPeak_nm', sp?.peakWavelength_nm ?? ''],
            ['FWHM_nm', sp?.fwhm_nm ?? ''],
            ['Centroid_nm', sp?.centroid_nm ?? ''],
            ['SMSR_dB', sp?.smsr_dB ?? ''],
            ['Theta_H_deg', dv?.horizontal_deg ?? ''],
            ['Theta_V_deg', dv?.vertical_deg ?? ''],
            ['Ellipticity', dv?.ellipticity ?? ''],
          ];
          const ws = XLSX.utils.aoa_to_sheet(rows);
          XLSX.utils.book_append_sheet(wb, ws, 'Summary');
          XLSX.writeFile(wb, 'results.xlsx');
        }}>导出Excel</button>
      </div>
    </div>
  );
}


