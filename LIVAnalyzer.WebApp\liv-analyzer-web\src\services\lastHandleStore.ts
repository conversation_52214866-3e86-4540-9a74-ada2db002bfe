import { get, set } from 'idb-keyval';

const KEY_LAST_FILE_HANDLE = 'lastFileHandle';

export async function saveLastFileHandle(handle: FileSystemFileHandle): Promise<void> {
  try {
    await set(KEY_LAST_FILE_HANDLE, handle as unknown as any);
  } catch {
    // ignore persistence failures (e.g., unsupported browser)
  }
}

export async function getLastStartInHandle(): Promise<FileSystemHandle | undefined> {
  try {
    const h = (await get(KEY_LAST_FILE_HANDLE)) as FileSystemHandle | undefined;
    return h;
  } catch {
    return undefined;
  }
}


