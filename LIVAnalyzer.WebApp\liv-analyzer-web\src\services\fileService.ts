import <PERSON> from 'papapar<PERSON>';
import type { ParseResult as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'papaparse';
import * as XLSX from 'xlsx';
import type { LIVData, SpectralSeries, PowerCurrentSeries, VoltageCurrentSeries } from '../types/data';

export type SupportedFile = File | Blob;

export interface ParseResult<T> {
  data: T | null;
  error?: string;
}

export async function parseCSV(file: SupportedFile): Promise<ParseResult<number[][]>> {
  return new Promise((resolve) => {
    Papa.parse<number[]>(file as File, {
      dynamicTyping: true,
      skipEmptyLines: true,
      complete: (results: PapaParseResult<number[]>) => {
        resolve({ data: results.data as unknown as number[][] });
      },
      error: (error: Error, _file: File) => resolve({ data: null, error: error.message }),
    });
  });
}

export async function parseExcel(file: SupportedFile): Promise<ParseResult<Record<string, number[][]>>> {
  try {
    const arrayBuffer = await (file as File).arrayBuffer();
    const workbook = XLSX.read(arrayBuffer, { type: 'array' });
    const sheetData: Record<string, number[][]> = {};
    for (const sheetName of workbook.SheetNames) {
      const sheet = workbook.Sheets[sheetName];
      const json = XLSX.utils.sheet_to_json<(number | string)[]> (sheet, { header: 1, raw: true });
      const numericRows = (json as (number | string)[][])
        .filter((row) => row && row.length >= 2)
        .map((row) => row.map((v) => (typeof v === 'number' ? v : Number(v))));
      sheetData[sheetName.toLowerCase()] = numericRows as number[][];
    }
    return { data: sheetData };
  } catch (error) {
    return { data: null, error: (error as Error).message };
  }
}

export function toLIVDataFromSheets(sheets: Record<string, number[][]>): LIVData {
  const getSeries = (rows: number[][]): { x: number[]; y: number[] } => ({
    x: rows.map((r) => r[0]).filter((v) => Number.isFinite(v)),
    y: rows.map((r) => r[1]).filter((v) => Number.isFinite(v)),
  });

  const data: LIVData = {};

  if (sheets['wavelength']) {
    const { x, y } = getSeries(sheets['wavelength']);
    data.wavelength = { wavelength: x, intensity: y } as SpectralSeries;
  }
  if (sheets['power']) {
    const { x, y } = getSeries(sheets['power']);
    data.power = { current: x, power: y } as PowerCurrentSeries;
  }
  if (sheets['voltage']) {
    const { x, y } = getSeries(sheets['voltage']);
    data.voltage = { current: x, voltage: y } as VoltageCurrentSeries;
  }
  if (sheets['hff']) {
    const { x, y } = getSeries(sheets['hff']);
    data.hff = { angle: x, intensity: y };
  }
  if (sheets['vff']) {
    const { x, y } = getSeries(sheets['vff']);
    data.vff = { angle: x, intensity: y };
  }

  return data;
}


