import { useAppStore } from '../state/store';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Select } from './ui/select';
import { Label } from './ui/label';
import { Checkbox } from './ui/checkbox';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { getAvailablePresets, mergeThresholdConfig } from '../services/thresholdPresets';

export default function ConfigPanel() {
  const { processingConfig, updateProcessingConfig, displayConfig, updateDisplayConfig } = useAppStore();

  const availablePresets = getAvailablePresets();

  const handlePresetChange = (presetName: string) => {
    const preset = availablePresets.find(p => p.name === presetName);
    if (preset) {
      const mergedConfig = mergeThresholdConfig(processingConfig.threshold, presetName);
      updateProcessingConfig({ threshold: mergedConfig });
    }
  };

  return (
    <div className="space-y-4">
      {/* 阈值检测方法 */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm">阈值检测方法</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="flex gap-2 flex-wrap">
            {([
              { value: 'linear', label: '线性拟合' },
              { value: 'segmented', label: '分段拟合' },
              { value: 'robust', label: '鲁棒拟合' }
            ] as const).map(({ value, label }) => (
              <Button
                key={value}
                variant={processingConfig.thresholdDetection === value ? 'default' : 'outline'}
                size="sm"
                onClick={() => updateProcessingConfig({ thresholdDetection: value })}
              >
                {label}
              </Button>
            ))}
          </div>
          <div className="space-y-2">
            <Label htmlFor="laser-preset">激光器预设</Label>
            <Select
              id="laser-preset"
              onChange={(e) => handlePresetChange(e.target.value)}
            >
              <option value="">选择预设配置...</option>
              {availablePresets.map(preset => (
                <option key={preset.name} value={preset.name}>
                  {preset.label}
                </option>
              ))}
            </Select>
            <p className="text-xs text-muted-foreground">根据激光器类型选择优化的参数配置</p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="fitting-points">拟合点数</Label>
            <Input
              id="fitting-points"
              type="number"
              className="w-32"
              value={processingConfig.fittingPoints ?? 150}
              onChange={(e) => updateProcessingConfig({ fittingPoints: Number(e.target.value) })}
              min={10}
              max={1000}
            />
            <p className="text-xs text-muted-foreground">用于阈值电流计算的数据点数量</p>
          </div>
        </CardContent>
      </Card>

      {/* 数据平滑设置 */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm">数据平滑设置</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="enable-smoothing"
              checked={processingConfig.smoothing?.enabled ?? false}
              onChange={(e) => updateProcessingConfig({
                smoothing: {
                  ...(processingConfig.smoothing ?? {}),
                  enabled: e.target.checked
                }
              })}
            />
            <Label htmlFor="enable-smoothing">启用数据平滑</Label>
          </div>

          {processingConfig.smoothing?.enabled && (
            <div className="space-y-3 pl-6 border-l-2 border-muted">
              <div className="grid grid-cols-2 gap-3">
                <div className="space-y-2">
                  <Label htmlFor="smoothing-method">平滑方法</Label>
                  <Select
                    id="smoothing-method"
                    value={processingConfig.smoothing?.method ?? 'moving-average'}
                    onChange={(e) => updateProcessingConfig({
                      smoothing: {
                        ...(processingConfig.smoothing ?? { enabled: true }),
                        method: e.target.value as any
                      }
                    })}
                  >
                    <option value="moving-average">移动平均</option>
                    <option value="sg">Savitzky-Golay</option>
                    <option value="gaussian">高斯滤波</option>
                    <option value="butterworth">Butterworth</option>
                    <option value="median">中值滤波</option>
                    <option value="adaptive">自适应平滑</option>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="window-size">窗口大小</Label>
                  <Input
                    id="window-size"
                    type="number"
                    value={processingConfig.smoothing?.windowSize ?? 5}
                    onChange={(e) => updateProcessingConfig({
                      smoothing: {
                        ...(processingConfig.smoothing ?? { enabled: true }),
                        windowSize: Number(e.target.value)
                      }
                    })}
                    min={3}
                    max={51}
                    step={2}
                  />
                </div>
              </div>

              {processingConfig.smoothing?.method === 'sg' && (
                <div className="space-y-2">
                  <Label htmlFor="polynomial-order">多项式阶数</Label>
                  <Input
                    id="polynomial-order"
                    type="number"
                    className="w-24"
                    value={processingConfig.smoothing?.polynomialOrder ?? 3}
                    onChange={(e) => updateProcessingConfig({
                      smoothing: {
                        ...(processingConfig.smoothing ?? { enabled: true }),
                        polynomialOrder: Number(e.target.value)
                      }
                    })}
                    min={1}
                    max={6}
                  />
                  <p className="text-xs text-muted-foreground">通常为2-4，必须小于窗口大小</p>
                </div>
              )}

              {processingConfig.smoothing?.method === 'gaussian' && (
                <div className="space-y-2">
                  <Label htmlFor="gaussian-sigma">标准差 (σ)</Label>
                  <Input
                    id="gaussian-sigma"
                    type="number"
                    className="w-32"
                    step="0.1"
                    min={0.1}
                    max={10}
                    value={processingConfig.smoothing?.sigma ?? 1.0}
                    onChange={(e) => updateProcessingConfig({
                      smoothing: {
                        ...(processingConfig.smoothing ?? { enabled: true }),
                        sigma: Number(e.target.value)
                      }
                    })}
                  />
                  <p className="text-xs text-muted-foreground">高斯核的标准差，控制平滑强度</p>
                </div>
              )}

              {processingConfig.smoothing?.method === 'butterworth' && (
                <>
                  <div className="space-y-2">
                    <Label htmlFor="cutoff-freq">截止频率</Label>
                    <Input
                      id="cutoff-freq"
                      type="number"
                      className="w-32"
                      step="0.01"
                      min={0.01}
                      max={0.49}
                      value={processingConfig.smoothing?.cutoff ?? 0.15}
                      onChange={(e) => updateProcessingConfig({
                        smoothing: {
                          ...(processingConfig.smoothing ?? { enabled: true }),
                          cutoff: Number(e.target.value)
                        }
                      })}
                    />
                    <p className="text-xs text-muted-foreground">归一化频率，范围0.01-0.49</p>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="filter-order">滤波器阶数</Label>
                    <Input
                      id="filter-order"
                      type="number"
                      className="w-24"
                      min={1}
                      max={4}
                      value={processingConfig.smoothing?.order ?? 2}
                      onChange={(e) => updateProcessingConfig({
                        smoothing: {
                          ...(processingConfig.smoothing ?? { enabled: true }),
                          order: Number(e.target.value)
                        }
                      })}
                    />
                    <p className="text-xs text-muted-foreground">阶数越高，滤波效果越陡峭</p>
                  </div>
                </>
              )}

              {processingConfig.smoothing?.method === 'adaptive' && (
                <div className="space-y-2">
                  <Label htmlFor="max-window-size">最大窗口大小</Label>
                  <Input
                    id="max-window-size"
                    type="number"
                    className="w-32"
                    min={5}
                    max={51}
                    step={2}
                    value={processingConfig.smoothing?.maxWindowSize ?? 15}
                    onChange={(e) => updateProcessingConfig({
                      smoothing: {
                        ...(processingConfig.smoothing ?? { enabled: true }),
                        maxWindowSize: Number(e.target.value)
                      }
                    })}
                  />
                  <p className="text-xs text-muted-foreground">自适应平滑的最大窗口大小</p>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* 单位设置 */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm">单位设置</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="grid grid-cols-2 gap-3">
            <div className="space-y-2">
              <Label htmlFor="power-unit">功率单位</Label>
              <Select
                id="power-unit"
                value={processingConfig.units?.power ?? 'W'}
                onChange={(e) => updateProcessingConfig({
                  units: {
                    ...(processingConfig.units ?? {}),
                    power: e.target.value as 'W' | 'mW'
                  }
                })}
              >
                <option value="W">瓦特 (W)</option>
                <option value="mW">毫瓦 (mW)</option>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="current-unit">电流单位</Label>
              <Select
                id="current-unit"
                value={processingConfig.units?.current ?? 'A'}
                onChange={(e) => updateProcessingConfig({
                  units: {
                    ...(processingConfig.units ?? {}),
                    current: e.target.value as 'A' | 'mA'
                  }
                })}
              >
                <option value="A">安培 (A)</option>
                <option value="mA">毫安 (mA)</option>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 显示选项 */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm">显示选项</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="grid grid-cols-2 gap-3">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="show-grid"
                checked={displayConfig.showGrid ?? true}
                onChange={(e) => updateDisplayConfig({ showGrid: e.target.checked })}
              />
              <Label htmlFor="show-grid">显示网格</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="show-legend"
                checked={displayConfig.showLegend ?? true}
                onChange={(e) => updateDisplayConfig({ showLegend: e.target.checked })}
              />
              <Label htmlFor="show-legend">显示图例</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="show-markers"
                checked={displayConfig.showMarkers ?? true}
                onChange={(e) => updateDisplayConfig({ showMarkers: e.target.checked })}
              />
              <Label htmlFor="show-markers">显示数据点</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="show-coords"
                checked={displayConfig.showCoords ?? true}
                onChange={(e) => updateDisplayConfig({ showCoords: e.target.checked })}
              />
              <Label htmlFor="show-coords">显示坐标值</Label>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}


