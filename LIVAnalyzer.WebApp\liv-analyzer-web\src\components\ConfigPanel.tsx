import { useAppStore } from '../state/store';
import { Button } from './ui/button';

export default function ConfigPanel() {
  const { processingConfig, updateProcessingConfig } = useAppStore();

  return (
    <div className="space-y-4 text-sm">
      <div>
        <div className="font-medium mb-2">阈值检测方法</div>
        <div className="flex gap-2 flex-wrap">
          {(['linear','segmented','robust'] as const).map(m => (
            <Button key={m} variant={processingConfig.thresholdDetection === m ? 'default' : 'outline'} size="sm"
              onClick={() => updateProcessingConfig({ thresholdDetection: m })}>
              {m}
            </Button>
          ))}
        </div>
      </div>

      <div>
        <div className="font-medium mb-2">平滑设置</div>
        <label className="flex items-center gap-2 mb-2">
          <input
            type="checkbox"
            checked={processingConfig.smoothing?.enabled ?? false}
            onChange={(e) => updateProcessingConfig({ smoothing: { ...(processingConfig.smoothing ?? {}), enabled: e.target.checked } })}
          />
          启用平滑
        </label>
        <div className="flex flex-wrap items-center gap-3">
          <label className="flex items-center gap-1">
            <span>方法</span>
            <select
              className="border rounded px-2 py-1 bg-background"
              value={processingConfig.smoothing?.method ?? 'moving-average'}
              onChange={(e) => updateProcessingConfig({ smoothing: { ...(processingConfig.smoothing ?? { enabled: true }), method: e.target.value as any } })}
            >
              <option value="moving-average">移动平均</option>
              <option value="sg">Savitzky-Golay</option>
              <option value="gaussian">高斯</option>
              <option value="butterworth">Butterworth</option>
            </select>
          </label>
          <label className="flex items-center gap-1">
            <span>窗口</span>
            <input
              type="number"
              className="w-20 border rounded px-2 py-1 bg-background"
              value={processingConfig.smoothing?.windowSize ?? 5}
              onChange={(e) => updateProcessingConfig({ smoothing: { ...(processingConfig.smoothing ?? { enabled: true }), windowSize: Number(e.target.value) } })}
            />
          </label>
          <label className="flex items-center gap-1">
            <span>阶数</span>
            <input
              type="number"
              className="w-20 border rounded px-2 py-1 bg-background"
              value={processingConfig.smoothing?.polynomialOrder ?? 3}
              onChange={(e) => updateProcessingConfig({ smoothing: { ...(processingConfig.smoothing ?? { enabled: true }), polynomialOrder: Number(e.target.value) } })}
              disabled={processingConfig.smoothing?.method !== 'sg'}
            />
          </label>
          <label className="flex items-center gap-1">
            <span>截止(0~0.5)</span>
            <input
              type="number"
              step="0.01"
              min={0.01}
              max={0.49}
              className="w-24 border rounded px-2 py-1 bg-background"
              value={processingConfig.smoothing?.cutoff ?? 0.15}
              onChange={(e) => updateProcessingConfig({ smoothing: { ...(processingConfig.smoothing ?? { enabled: true }), cutoff: Number(e.target.value) } })}
              disabled={processingConfig.smoothing?.method !== 'butterworth'}
            />
          </label>
        </div>
      </div>

      <div>
        <div className="font-medium mb-2">拟合点数</div>
        <input
          type="number"
          className="w-32 rounded border px-2 py-1 bg-background"
          value={processingConfig.fittingPoints ?? 150}
          onChange={(e) => updateProcessingConfig({ fittingPoints: Number(e.target.value) })}
        />
      </div>

      <div>
        <div className="font-medium mb-2">单位</div>
        <div className="flex gap-3">
          <label className="flex items-center gap-1">
            <span>功率</span>
            <select className="border rounded px-2 py-1 bg-background"
              value={processingConfig.units?.power ?? 'W'}
              onChange={(e) => updateProcessingConfig({ units: { ...(processingConfig.units ?? {}), power: e.target.value as 'W' | 'mW' } })}>
              <option value="W">W</option>
              <option value="mW">mW</option>
            </select>
          </label>
          <label className="flex items-center gap-1">
            <span>电流</span>
            <select className="border rounded px-2 py-1 bg-background"
              value={processingConfig.units?.current ?? 'A'}
              onChange={(e) => updateProcessingConfig({ units: { ...(processingConfig.units ?? {}), current: e.target.value as 'A' | 'mA' } })}>
              <option value="A">A</option>
              <option value="mA">mA</option>
            </select>
          </label>
        </div>
      </div>
    </div>
  );
}


