using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Imaging;
using System.IO;

namespace LIVAnalyzer.UI.Utilities
{
    /// <summary>
    /// LIV分析工具图标生成器
    /// </summary>
    public static class IconGenerator
    {
        /// <summary>
        /// 生成应用程序图标
        /// </summary>
        /// <param name="size">图标尺寸</param>
        /// <returns>生成的图标位图</returns>
        public static Bitmap GenerateIcon(int size)
        {
            var bitmap = new Bitmap(size, size, PixelFormat.Format32bppArgb);
            
            using (var g = Graphics.FromImage(bitmap))
            {
                // 设置高质量渲染
                g.SmoothingMode = SmoothingMode.AntiAlias;
                g.InterpolationMode = InterpolationMode.HighQualityBicubic;
                g.CompositingQuality = CompositingQuality.HighQuality;
                
                // 清除背景
                g.Clear(Color.Transparent);
                
                // 计算缩放比例
                float scale = size / 256f;
                
                // 绘制背景圆角矩形
                DrawBackground(g, size, scale);
                
                // 绘制坐标系
                DrawCoordinateSystem(g, size, scale);
                
                // 绘制LIV曲线
                DrawLIVCurve(g, size, scale);
                
                // 绘制激光器符号
                DrawLaserSymbol(g, size, scale);
                
                // 绘制标题（仅在较大尺寸时）
                if (size >= 48)
                {
                    DrawTitle(g, size, scale);
                }
            }
            
            return bitmap;
        }
        
        /// <summary>
        /// 绘制背景
        /// </summary>
        private static void DrawBackground(Graphics g, int size, float scale)
        {
            var rect = new RectangleF(16 * scale, 16 * scale, 224 * scale, 224 * scale);
            var radius = 32 * scale;
            
            using (var brush = new LinearGradientBrush(
                rect, 
                Color.FromArgb(255, 33, 150, 243), 
                Color.FromArgb(255, 25, 118, 210), 
                LinearGradientMode.ForwardDiagonal))
            {
                using (var path = CreateRoundedRectangle(rect, radius))
                {
                    g.FillPath(brush, path);
                }
            }
        }
        
        /// <summary>
        /// 绘制坐标系
        /// </summary>
        private static void DrawCoordinateSystem(Graphics g, int size, float scale)
        {
            using (var pen = new Pen(Color.FromArgb(180, 255, 255, 255), 1.5f * scale))
            {
                // X轴
                g.DrawLine(pen, 48 * scale, 192 * scale, 208 * scale, 192 * scale);
                
                // Y轴
                g.DrawLine(pen, 48 * scale, 64 * scale, 48 * scale, 192 * scale);
                
                // 网格线（仅在较大尺寸时显示）
                if (size >= 64)
                {
                    using (var gridPen = new Pen(Color.FromArgb(80, 255, 255, 255), 0.5f * scale))
                    {
                        // 垂直网格线
                        for (int x = 64; x <= 192; x += 32)
                        {
                            g.DrawLine(gridPen, x * scale, 64 * scale, x * scale, 192 * scale);
                        }
                        
                        // 水平网格线
                        for (int y = 80; y <= 176; y += 32)
                        {
                            g.DrawLine(gridPen, 48 * scale, y * scale, 208 * scale, y * scale);
                        }
                    }
                }
            }
            
            // 箭头
            using (var brush = new SolidBrush(Color.FromArgb(180, 255, 255, 255)))
            {
                var arrowSize = 4 * scale;
                
                // X轴箭头
                var xArrow = new PointF[]
                {
                    new PointF(208 * scale, 192 * scale),
                    new PointF((208 - 8) * scale, (192 - 4) * scale),
                    new PointF((208 - 8) * scale, (192 + 4) * scale)
                };
                g.FillPolygon(brush, xArrow);
                
                // Y轴箭头
                var yArrow = new PointF[]
                {
                    new PointF(48 * scale, 64 * scale),
                    new PointF((48 - 4) * scale, (64 + 8) * scale),
                    new PointF((48 + 4) * scale, (64 + 8) * scale)
                };
                g.FillPolygon(brush, yArrow);
            }
        }
        
        /// <summary>
        /// 绘制LIV曲线
        /// </summary>
        private static void DrawLIVCurve(Graphics g, int size, float scale)
        {
            var points = new PointF[]
            {
                new PointF(56 * scale, 180 * scale),
                new PointF(80 * scale, 165 * scale),
                new PointF(104 * scale, 140 * scale),
                new PointF(128 * scale, 110 * scale),
                new PointF(152 * scale, 85 * scale),
                new PointF(176 * scale, 72 * scale),
                new PointF(200 * scale, 75 * scale)
            };
            
            // 绘制曲线
            using (var pen = new Pen(Color.FromArgb(255, 76, 175, 80), 2.5f * scale))
            {
                if (points.Length > 1)
                {
                    g.DrawCurve(pen, points, 0.5f);
                }
            }
            
            // 绘制数据点
            using (var brush = new SolidBrush(Color.FromArgb(255, 76, 175, 80)))
            {
                var pointSize = 3 * scale;
                foreach (var point in points)
                {
                    g.FillEllipse(brush, point.X - pointSize, point.Y - pointSize, pointSize * 2, pointSize * 2);
                }
            }
        }
        
        /// <summary>
        /// 绘制激光器符号
        /// </summary>
        private static void DrawLaserSymbol(Graphics g, int size, float scale)
        {
            var x = 180 * scale;
            var y = 40 * scale;
            var width = 32 * scale;
            var height = 12 * scale;
            
            // 激光器外壳
            using (var brush = new SolidBrush(Color.FromArgb(220, 255, 255, 255)))
            {
                var rect = new RectangleF(x, y, width, height);
                using (var path = CreateRoundedRectangle(rect, 2 * scale))
                {
                    g.FillPath(brush, path);
                }
            }
            
            // 激光器内部
            using (var brush = new SolidBrush(Color.FromArgb(255, 25, 118, 210)))
            {
                var rect = new RectangleF(x + 2 * scale, y + 2 * scale, width - 4 * scale, height - 4 * scale);
                using (var path = CreateRoundedRectangle(rect, 1 * scale))
                {
                    g.FillPath(brush, path);
                }
            }
            
            // 激光束
            using (var pen = new Pen(Color.FromArgb(200, 255, 152, 0), 2 * scale))
            {
                var beamY = y + height / 2;
                g.DrawLine(pen, x + width, beamY, x + width + 16 * scale, beamY);
                
                // 发散光束
                using (var divergePen = new Pen(Color.FromArgb(120, 255, 152, 0), 1 * scale))
                {
                    g.DrawLine(divergePen, x + width, beamY, x + width + 12 * scale, beamY - 4 * scale);
                    g.DrawLine(divergePen, x + width, beamY, x + width + 12 * scale, beamY + 4 * scale);
                }
            }
        }
        
        /// <summary>
        /// 绘制标题
        /// </summary>
        private static void DrawTitle(Graphics g, int size, float scale)
        {
            var fontSize = Math.Max(8, 14 * scale);
            using (var font = new Font("Arial", fontSize, FontStyle.Bold))
            using (var brush = new SolidBrush(Color.FromArgb(200, 255, 255, 255)))
            {
                var text = "LIV";
                var textSize = g.MeasureString(text, font);
                var x = (size - textSize.Width) / 2;
                var y = 230 * scale;
                
                g.DrawString(text, font, brush, x, y);
            }
        }
        
        /// <summary>
        /// 创建圆角矩形路径
        /// </summary>
        private static GraphicsPath CreateRoundedRectangle(RectangleF rect, float radius)
        {
            var path = new GraphicsPath();
            
            if (radius <= 0)
            {
                path.AddRectangle(rect);
                return path;
            }
            
            var diameter = radius * 2;
            var arc = new RectangleF(rect.Location, new SizeF(diameter, diameter));
            
            // 左上角
            path.AddArc(arc, 180, 90);
            
            // 右上角
            arc.X = rect.Right - diameter;
            path.AddArc(arc, 270, 90);
            
            // 右下角
            arc.Y = rect.Bottom - diameter;
            path.AddArc(arc, 0, 90);
            
            // 左下角
            arc.X = rect.Left;
            path.AddArc(arc, 90, 90);
            
            path.CloseFigure();
            return path;
        }
        
        /// <summary>
        /// 生成多尺寸ICO文件
        /// </summary>
        /// <param name="outputPath">输出路径</param>
        public static void GenerateIconFile(string outputPath)
        {
            var sizes = new int[] { 16, 32, 48, 64, 128, 256 };
            var icons = new Bitmap[sizes.Length];
            
            try
            {
                // 生成各种尺寸的图标
                for (int i = 0; i < sizes.Length; i++)
                {
                    icons[i] = GenerateIcon(sizes[i]);
                }
                
                // 保存为ICO文件
                SaveAsIcon(icons, outputPath);
            }
            finally
            {
                // 清理资源
                foreach (var icon in icons)
                {
                    icon?.Dispose();
                }
            }
        }
        
        /// <summary>
        /// 保存为ICO文件
        /// </summary>
        private static void SaveAsIcon(Bitmap[] bitmaps, string outputPath)
        {
            using (var stream = new FileStream(outputPath, FileMode.Create))
            using (var writer = new BinaryWriter(stream))
            {
                // ICO文件头
                writer.Write((short)0);      // Reserved
                writer.Write((short)1);      // Type (1 = ICO)
                writer.Write((short)bitmaps.Length); // Number of images
                
                var offset = 6 + (16 * bitmaps.Length); // Header + directory entries
                
                // 目录条目
                foreach (var bitmap in bitmaps)
                {
                    writer.Write((byte)(bitmap.Width == 256 ? 0 : bitmap.Width));   // Width
                    writer.Write((byte)(bitmap.Height == 256 ? 0 : bitmap.Height)); // Height
                    writer.Write((byte)0);        // Color count
                    writer.Write((byte)0);        // Reserved
                    writer.Write((short)1);       // Planes
                    writer.Write((short)32);      // Bits per pixel
                    
                    using (var ms = new MemoryStream())
                    {
                        bitmap.Save(ms, ImageFormat.Png);
                        var imageData = ms.ToArray();
                        
                        writer.Write(imageData.Length); // Size
                        writer.Write(offset);           // Offset
                        
                        offset += imageData.Length;
                    }
                }
                
                // 图像数据
                foreach (var bitmap in bitmaps)
                {
                    using (var ms = new MemoryStream())
                    {
                        bitmap.Save(ms, ImageFormat.Png);
                        var imageData = ms.ToArray();
                        writer.Write(imageData);
                    }
                }
            }
        }
    }
}
