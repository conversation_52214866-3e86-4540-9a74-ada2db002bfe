using LIVAnalyzer.Models;
using LIVAnalyzer.Services.Logging;
using LIVAnalyzer.UI.ViewModels;
using OxyPlot;
using OxyPlot.Axes;
using OxyPlot.Series;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using DataPoint = LIVAnalyzer.Models.DataPoint;
using OxyDataPoint = OxyPlot.DataPoint;

namespace LIVAnalyzer.UI.Services
{
    /// <summary>
    /// 增量图表更新器，支持渐进式数据展示
    /// </summary>
    public class IncrementalPlotUpdater
    {
        private readonly SemaphoreSlim _updateSemaphore = new(1, 1);
        private readonly Dictionary<string, PlotDataCache> _plotDataCache = new();
        private readonly object _cacheLock = new object();

        /// <summary>
        /// 增量更新LIV图表
        /// </summary>
        public async Task UpdateLIVPlotIncrementalAsync(
            PlotModel plotModel,
            List<FileViewModel> files,
            bool isPreviewData = false,
            CancellationToken cancellationToken = default)
        {
            await _updateSemaphore.WaitAsync(cancellationToken);

            try
            {
                await Task.Run(() =>
                {
                    if (cancellationToken.IsCancellationRequested) return;

                    var selectedFiles = files.Where(f => f.IsSelected && f.Data != null).ToList();
                    
                    // 清理不再需要的系列
                    var existingSeries = plotModel.Series.ToList();
                    var currentFileNames = selectedFiles.Select(f => f.FileName).ToHashSet();

                    foreach (var series in existingSeries)
                    {
                        var seriesFileName = ExtractFileNameFromSeriesTitle(series.Title);
                        if (!string.IsNullOrEmpty(seriesFileName) && !currentFileNames.Contains(seriesFileName))
                        {
                            plotModel.Series.Remove(series);
                        }
                    }

                    // 更新或添加数据系列
                    foreach (var file in selectedFiles)
                    {
                        if (cancellationToken.IsCancellationRequested) break;

                        UpdateLIVSeriesIncremental(plotModel, file, isPreviewData);
                    }

                    // 更新标题显示预览状态
                    if (isPreviewData && !plotModel.Title.Contains("[预览]"))
                    {
                        plotModel.Title = plotModel.Title + " [预览]";
                    }
                    else if (!isPreviewData && plotModel.Title.Contains("[预览]"))
                    {
                        plotModel.Title = plotModel.Title.Replace(" [预览]", "");
                    }

                    // 自动调整坐标轴
                    plotModel.ResetAllAxes();
                    plotModel.InvalidatePlot(false);

                }, cancellationToken);
            }
            catch (OperationCanceledException)
            {
                // 正常取消，不需要处理
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "增量更新LIV图表时发生错误");
            }
            finally
            {
                _updateSemaphore.Release();
            }
        }

        /// <summary>
        /// 增量更新光谱图表
        /// </summary>
        public async Task UpdateSpectrumPlotIncrementalAsync(
            PlotModel plotModel,
            List<FileViewModel> files,
            bool isPreviewData = false,
            CancellationToken cancellationToken = default)
        {
            await _updateSemaphore.WaitAsync(cancellationToken);

            try
            {
                await Task.Run(() =>
                {
                    if (cancellationToken.IsCancellationRequested) return;

                    var selectedFiles = files.Where(f => f.IsSelected && f.Data != null).ToList();
                    
                    // 清理不再需要的系列
                    var existingSeries = plotModel.Series.ToList();
                    var currentFileNames = selectedFiles.Select(f => f.FileName).ToHashSet();

                    foreach (var series in existingSeries)
                    {
                        var seriesFileName = ExtractFileNameFromSeriesTitle(series.Title);
                        if (!string.IsNullOrEmpty(seriesFileName) && !currentFileNames.Contains(seriesFileName))
                        {
                            plotModel.Series.Remove(series);
                        }
                    }

                    // 更新或添加光谱数据系列
                    foreach (var file in selectedFiles)
                    {
                        if (cancellationToken.IsCancellationRequested) break;

                        UpdateSpectrumSeriesIncremental(plotModel, file, isPreviewData);
                    }

                    // 更新标题
                    if (isPreviewData && !plotModel.Title.Contains("[预览]"))
                    {
                        plotModel.Title = plotModel.Title + " [预览]";
                    }
                    else if (!isPreviewData && plotModel.Title.Contains("[预览]"))
                    {
                        plotModel.Title = plotModel.Title.Replace(" [预览]", "");
                    }

                    plotModel.ResetAllAxes();
                    plotModel.InvalidatePlot(false);

                }, cancellationToken);
            }
            catch (OperationCanceledException)
            {
                // 正常取消，不需要处理
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "增量更新光谱图表时发生错误");
            }
            finally
            {
                _updateSemaphore.Release();
            }
        }

        /// <summary>
        /// 增量更新效率图表
        /// </summary>
        public async Task UpdateEfficiencyPlotIncrementalAsync(
            PlotModel plotModel,
            List<FileViewModel> files,
            bool isPreviewData = false,
            CancellationToken cancellationToken = default)
        {
            await _updateSemaphore.WaitAsync(cancellationToken);

            try
            {
                await Task.Run(() =>
                {
                    if (cancellationToken.IsCancellationRequested) return;

                    var selectedFiles = files.Where(f => f.IsSelected && 
                        f.Data != null && 
                        f.Data.CurrentPowerData.Any() && 
                        f.Data.CurrentVoltageData.Any()).ToList();

                    // 清理不再需要的系列
                    var existingSeries = plotModel.Series.ToList();
                    var currentFileNames = selectedFiles.Select(f => f.FileName).ToHashSet();

                    foreach (var series in existingSeries)
                    {
                        var seriesFileName = ExtractFileNameFromSeriesTitle(series.Title);
                        if (!string.IsNullOrEmpty(seriesFileName) && !currentFileNames.Contains(seriesFileName))
                        {
                            plotModel.Series.Remove(series);
                        }
                    }

                    // 更新或添加效率数据系列
                    foreach (var file in selectedFiles)
                    {
                        if (cancellationToken.IsCancellationRequested) break;

                        UpdateEfficiencySeriesIncremental(plotModel, file, isPreviewData);
                    }

                    // 更新标题
                    if (isPreviewData && !plotModel.Title.Contains("[预览]"))
                    {
                        plotModel.Title = plotModel.Title + " [预览]";
                    }
                    else if (!isPreviewData && plotModel.Title.Contains("[预览]"))
                    {
                        plotModel.Title = plotModel.Title.Replace(" [预览]", "");
                    }

                    plotModel.ResetAllAxes();
                    plotModel.InvalidatePlot(false);

                }, cancellationToken);
            }
            catch (OperationCanceledException)
            {
                // 正常取消，不需要处理
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "增量更新效率图表时发生错误");
            }
            finally
            {
                _updateSemaphore.Release();
            }
        }

        /// <summary>
        /// 增量更新LIV数据系列
        /// </summary>
        private void UpdateLIVSeriesIncremental(PlotModel plotModel, FileViewModel file, bool isPreviewData)
        {
            var data = file.Data;
            if (data == null) return;

            var fileName = file.FileName;
            var cacheKey = $"LIV_{fileName}";

            lock (_cacheLock)
            {
                // 检查缓存
                if (_plotDataCache.TryGetValue(cacheKey, out var cached))
                {
                    // 如果数据没有变化，跳过更新
                    var currentDataHash = GetDataHash(data.CurrentPowerData, data.CurrentVoltageData);
                    if (cached.DataHash == currentDataHash && !isPreviewData)
                        return;
                }

                // 查找现有的功率和电压系列
                var powerSeries = plotModel.Series.FirstOrDefault(s => s.Title == $"{fileName} - 功率") as LineSeries;
                var voltageSeries = plotModel.Series.FirstOrDefault(s => s.Title == $"{fileName} - 电压") as LineSeries;

                // 创建或更新功率系列
                if (powerSeries == null)
                {
                    powerSeries = new LineSeries
                    {
                        Title = $"{fileName} - 功率",
                        Color = GetSeriesColor(plotModel.Series.Count),
                        StrokeThickness = isPreviewData ? 1.5 : 2.0,
                        LineStyle = isPreviewData ? LineStyle.Dash : LineStyle.Solid
                    };
                    plotModel.Series.Add(powerSeries);
                }

                // 创建或更新电压系列
                if (voltageSeries == null)
                {
                    voltageSeries = new LineSeries
                    {
                        Title = $"{fileName} - 电压",
                        Color = GetSeriesColor(plotModel.Series.Count),
                        StrokeThickness = isPreviewData ? 1.5 : 2.0,
                        LineStyle = isPreviewData ? LineStyle.Dash : LineStyle.Solid
                    };
                    plotModel.Series.Add(voltageSeries);
                }

                // 更新数据点
                powerSeries.Points.Clear();
                foreach (var point in data.CurrentPowerData.OrderBy(p => p.X))
                {
                    powerSeries.Points.Add(new OxyDataPoint(point.X, point.Y));
                }

                voltageSeries.Points.Clear();
                foreach (var point in data.CurrentVoltageData.OrderBy(p => p.X))
                {
                    voltageSeries.Points.Add(new OxyDataPoint(point.X, point.Y));
                }

                // 更新缓存
                _plotDataCache[cacheKey] = new PlotDataCache
                {
                    DataHash = GetDataHash(data.CurrentPowerData, data.CurrentVoltageData),
                    LastUpdated = DateTime.Now
                };
            }
        }

        /// <summary>
        /// 增量更新光谱数据系列
        /// </summary>
        private void UpdateSpectrumSeriesIncremental(PlotModel plotModel, FileViewModel file, bool isPreviewData)
        {
            var data = file.Data;
            if (data == null || !data.WavelengthIntensityData.Any()) return;

            var fileName = file.FileName;
            var cacheKey = $"Spectrum_{fileName}";

            lock (_cacheLock)
            {
                // 检查缓存
                if (_plotDataCache.TryGetValue(cacheKey, out var cached))
                {
                    var currentDataHash = GetDataHash(data.WavelengthIntensityData);
                    if (cached.DataHash == currentDataHash && !isPreviewData)
                        return;
                }

                // 查找现有系列
                var spectrumSeries = plotModel.Series.FirstOrDefault(s => s.Title == fileName) as LineSeries;

                if (spectrumSeries == null)
                {
                    spectrumSeries = new LineSeries
                    {
                        Title = fileName,
                        Color = GetSeriesColor(plotModel.Series.Count),
                        StrokeThickness = isPreviewData ? 1.5 : 2.0,
                        LineStyle = isPreviewData ? LineStyle.Dash : LineStyle.Solid
                    };
                    plotModel.Series.Add(spectrumSeries);
                }

                // 更新数据点
                spectrumSeries.Points.Clear();
                foreach (var point in data.WavelengthIntensityData.OrderBy(p => p.X))
                {
                    spectrumSeries.Points.Add(new OxyDataPoint(point.X, point.Y));
                }

                // 更新缓存
                _plotDataCache[cacheKey] = new PlotDataCache
                {
                    DataHash = GetDataHash(data.WavelengthIntensityData),
                    LastUpdated = DateTime.Now
                };
            }
        }

        /// <summary>
        /// 增量更新效率数据系列
        /// </summary>
        private void UpdateEfficiencySeriesIncremental(PlotModel plotModel, FileViewModel file, bool isPreviewData)
        {
            var data = file.Data;
            if (data == null || !data.CurrentPowerData.Any() || !data.CurrentVoltageData.Any()) return;

            var fileName = file.FileName;
            var cacheKey = $"Efficiency_{fileName}";

            lock (_cacheLock)
            {
                // 检查缓存
                if (_plotDataCache.TryGetValue(cacheKey, out var cached))
                {
                    var currentDataHash = GetDataHash(data.CurrentPowerData, data.CurrentVoltageData);
                    if (cached.DataHash == currentDataHash && !isPreviewData)
                        return;
                }

                // 计算效率数据
                var efficiencyData = CalculateEfficiencyData(data);
                if (!efficiencyData.Any()) return;

                // 查找现有系列
                var efficiencySeries = plotModel.Series.FirstOrDefault(s => s.Title == fileName) as LineSeries;

                if (efficiencySeries == null)
                {
                    efficiencySeries = new LineSeries
                    {
                        Title = fileName,
                        Color = GetSeriesColor(plotModel.Series.Count),
                        StrokeThickness = isPreviewData ? 1.5 : 2.0,
                        LineStyle = isPreviewData ? LineStyle.Dash : LineStyle.Solid
                    };
                    plotModel.Series.Add(efficiencySeries);
                }

                // 更新数据点
                efficiencySeries.Points.Clear();
                foreach (var point in efficiencyData.OrderBy(p => p.X))
                {
                    efficiencySeries.Points.Add(new OxyDataPoint(point.X, point.Y * 100)); // 转换为百分比
                }

                // 更新缓存
                _plotDataCache[cacheKey] = new PlotDataCache
                {
                    DataHash = GetDataHash(data.CurrentPowerData, data.CurrentVoltageData),
                    LastUpdated = DateTime.Now
                };
            }
        }

        /// <summary>
        /// 计算效率数据
        /// </summary>
        private List<DataPoint> CalculateEfficiencyData(LIVMeasurementData data)
        {
            var efficiencyPoints = new List<DataPoint>();

            // 按电流值配对功率和电压数据
            var powerDict = data.CurrentPowerData.ToDictionary(p => p.X, p => p.Y);
            var voltageDict = data.CurrentVoltageData.ToDictionary(p => p.X, p => p.Y);

            foreach (var current in powerDict.Keys.Where(k => voltageDict.ContainsKey(k)))
            {
                var power = powerDict[current];
                var voltage = voltageDict[current];

                if (current > 0 && voltage > 0)
                {
                    var efficiency = power / (current * voltage);
                    if (efficiency >= 0 && efficiency <= 1) // 效率应该在0-1之间
                    {
                        efficiencyPoints.Add(new DataPoint(current, efficiency));
                    }
                }
            }

            return efficiencyPoints;
        }

        /// <summary>
        /// 获取数据哈希值用于缓存比较
        /// </summary>
        private string GetDataHash(params List<DataPoint>[] dataLists)
        {
            var totalCount = dataLists.Sum(list => list?.Count ?? 0);
            var firstPoints = string.Join(",", dataLists
                .Where(list => list != null && list.Any())
                .Select(list => $"{list[0].X}:{list[0].Y}"));
            var lastPoints = string.Join(",", dataLists
                .Where(list => list != null && list.Any())
                .Select(list => $"{list[^1].X}:{list[^1].Y}"));
                
            return $"{totalCount}_{firstPoints}_{lastPoints}";
        }

        /// <summary>
        /// 从系列标题中提取文件名
        /// </summary>
        private string ExtractFileNameFromSeriesTitle(string title)
        {
            if (string.IsNullOrEmpty(title)) return string.Empty;

            // 移除后缀（如 " - 功率"）
            var suffixes = new[] { " - 功率", " - 电压", " - 强度" };
            foreach (var suffix in suffixes)
            {
                if (title.EndsWith(suffix))
                {
                    return title.Substring(0, title.Length - suffix.Length);
                }
            }

            return title;
        }

        /// <summary>
        /// 获取系列颜色
        /// </summary>
        private OxyColor GetSeriesColor(int index)
        {
            var colors = new[]
            {
                OxyColors.Blue, OxyColors.Red, OxyColors.Green, OxyColors.Orange,
                OxyColors.Purple, OxyColors.Brown, OxyColors.Pink, OxyColors.Gray,
                OxyColors.Olive, OxyColors.Navy, OxyColors.Maroon, OxyColors.Teal
            };

            return colors[index % colors.Length];
        }

        /// <summary>
        /// 清理缓存
        /// </summary>
        public void ClearCache()
        {
            lock (_cacheLock)
            {
                _plotDataCache.Clear();
            }
        }

        /// <summary>
        /// 清理过期缓存
        /// </summary>
        public void ClearExpiredCache(TimeSpan maxAge)
        {
            lock (_cacheLock)
            {
                var cutoffTime = DateTime.Now - maxAge;
                var expiredKeys = _plotDataCache
                    .Where(kvp => kvp.Value.LastUpdated < cutoffTime)
                    .Select(kvp => kvp.Key)
                    .ToList();

                foreach (var key in expiredKeys)
                {
                    _plotDataCache.Remove(key);
                }
            }
        }

        public void Dispose()
        {
            _updateSemaphore?.Dispose();
            ClearCache();
        }
    }

    /// <summary>
    /// 图表数据缓存
    /// </summary>
    internal class PlotDataCache
    {
        public string DataHash { get; set; } = string.Empty;
        public DateTime LastUpdated { get; set; }
    }
}