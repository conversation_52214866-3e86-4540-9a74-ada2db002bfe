using System;
using System.Collections.Generic;

namespace LIVAnalyzer.Models
{
    /// <summary>
    /// 渐进式加载进度模型
    /// </summary>
    public class LoadingProgress
    {
        /// <summary>
        /// 当前加载阶段描述
        /// </summary>
        public string Stage { get; set; } = string.Empty;

        /// <summary>
        /// 完成百分比 (0-100)
        /// </summary>
        public double Percentage { get; set; }

        /// <summary>
        /// 当前阶段的部分数据（用于增量更新）
        /// </summary>
        public LIVMeasurementData? PartialData { get; set; }

        /// <summary>
        /// 是否为部分数据（用于UI判断是否显示"预览"标识）
        /// </summary>
        public bool IsPartialData { get; set; } = true;

        /// <summary>
        /// 当前处理的文件名
        /// </summary>
        public string? FileName { get; set; }

        /// <summary>
        /// 详细状态信息
        /// </summary>
        public string? DetailMessage { get; set; }

        /// <summary>
        /// 当前加载的数据点数量
        /// </summary>
        public int LoadedDataPoints { get; set; }

        /// <summary>
        /// 总数据点数量
        /// </summary>
        public int TotalDataPoints { get; set; }
    }

    /// <summary>
    /// 采样配置
    /// </summary>
    public class SamplingConfig
    {
        /// <summary>
        /// 初始采样率（每N个点取1个）
        /// </summary>
        public int InitialSamplingRate { get; set; } = 10;

        /// <summary>
        /// 关键区域采样率（阈值附近）
        /// </summary>
        public int CriticalRegionSamplingRate { get; set; } = 3;

        /// <summary>
        /// 最小采样点数
        /// </summary>
        public int MinimumSamplePoints { get; set; } = 50;

        /// <summary>
        /// 最大初始采样点数
        /// </summary>
        public int MaximumInitialSamplePoints { get; set; } = 500;

        /// <summary>
        /// 启用智能采样
        /// </summary>
        public bool EnableSmartSampling { get; set; } = true;

        /// <summary>
        /// 关键区域起始比例（0-1）
        /// </summary>
        public double CriticalRegionStartRatio { get; set; } = 0.3;

        /// <summary>
        /// 关键区域结束比例（0-1）
        /// </summary>
        public double CriticalRegionEndRatio { get; set; } = 0.7;
    }

    /// <summary>
    /// 采样结果
    /// </summary>
    public class SamplingResult
    {
        /// <summary>
        /// 采样的行索引
        /// </summary>
        public List<int> SampledIndices { get; set; } = new List<int>();

        /// <summary>
        /// 剩余未采样的行索引
        /// </summary>
        public List<int> RemainingIndices { get; set; } = new List<int>();

        /// <summary>
        /// 总行数
        /// </summary>
        public int TotalRows { get; set; }

        /// <summary>
        /// 采样比例
        /// </summary>
        public double SamplingRatio => TotalRows > 0 ? (double)SampledIndices.Count / TotalRows : 0;
    }
}