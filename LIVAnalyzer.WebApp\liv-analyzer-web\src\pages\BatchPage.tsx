import { useState } from 'react';
import { ParserWorkerClient } from '../services/workerClient';
import { ComputeWorkerClient } from '../services/computeClient';
import type { LIVData } from '../types/data';
import { preprocessLIVData } from '../services/preprocess';
import { exportCSV } from '../services/export';
import { toLIVDataFromSheets, parseCSV, parseExcel } from '../services/fileService';

type Row = {
  name: string;
  status: 'pending' | 'processing' | 'done' | 'error';
  ith?: number; slope?: number; rs?: number; etamax?: number; lambda?: number; fwhm?: number;
  error?: string;
};

export default function BatchPage() {
  const [rows, setRows] = useState<Row[]>([]);
  const [concurrency, setConcurrency] = useState<number>(2);
  const [running, setRunning] = useState(false);

  function setRow(idx: number, patch: Partial<Row>) {
    setRows((r) => { const c = r.slice(); c[idx] = { ...c[idx], ...patch }; return c; });
  }

  async function onFiles(files: FileList | null) {
    if (!files || files.length === 0) return;
    const list: Row[] = [];
    for (let i = 0; i < files.length; i++) list.push({ name: files[i].name, status: 'pending' });
    setRows(list);
    await run(files);
  }

  async function run(files: FileList) {
    setRunning(true);
    const parser = new ParserWorkerClient();
    const compute = new ComputeWorkerClient();
    const queue: Promise<void>[] = [];
    let idx = 0;
    async function worker() {
      while (true) {
        const current = idx++;
        if (current >= files.length) break;
        const file = files[current];
        try {
          setRow(current, { status: 'processing' });
          const ext = file.name.split('.').pop()?.toLowerCase();
          let data: LIVData | null = null;
          if (ext === 'csv') {
            // use worker for CSV parse
            const csv = await parser.parseCSV(file);
            const rows = csv;
            const x = rows.map(r => r[0]).filter(Number.isFinite);
            const y = rows.map(r => r[1]).filter(Number.isFinite);
            data = { power: { current: x, power: y } } as LIVData;
          } else if (ext === 'xlsx' || ext === 'xls') {
            const sheets = await parser.parseExcel(file);
            data = toLIVDataFromSheets(sheets);
          } else {
            // fallback to main-thread parse
            if (ext === 'csv') {
              const res = await parseCSV(file);
              const rows = res.data ?? [];
              const x = rows.map(r => r[0]).filter(Number.isFinite);
              const y = rows.map(r => r[1]).filter(Number.isFinite);
              data = { power: { current: x, power: y } } as LIVData;
            } else {
              const res = await parseExcel(file);
              data = res.data ? toLIVDataFromSheets(res.data) : null;
            }
          }
          if (!data) throw new Error('解析失败');
          const cleaned = preprocessLIVData(data);
          const result = await compute.compute(cleaned);
          setRow(current, {
            status: 'done',
            ith: result.livParameters?.thresholdCurrent_mA,
            slope: result.livParameters?.slopeEfficiency_W_per_A,
            rs: result.livParameters?.seriesResistance_Ohm,
            etamax: result.livParameters?.maxEfficiency,
            lambda: result.spectralParameters?.peakWavelength_nm,
            fwhm: result.spectralParameters?.fwhm_nm,
          });
        } catch (e) {
          setRow(current, { status: 'error', error: (e as Error).message });
        }
      }
    }
    for (let i = 0; i < Math.max(1, Math.min(concurrency, files.length)); i++) queue.push(worker());
    await Promise.all(queue);
    setRunning(false);
  }

  function exportSummary() {
    exportCSV('batch_summary', ['File','Status','Ith_mA','Slope_W_A','Rs_Ohm','EtaMax','LambdaPeak_nm','FWHM_nm','Error'],
      rows.map(r => [r.name, r.status, r.ith ?? '', r.slope ?? '', r.rs ?? '', r.etamax ?? '', r.lambda ?? '', r.fwhm ?? '', r.error ?? ''])
    );
  }

  return (
    <div className="max-w-5xl mx-auto p-6 space-y-4 text-sm">
      <div className="text-lg font-semibold">批量处理（原型）</div>
      <div className="text-muted-foreground">选择多个 CSV/Excel 文件，后台并行解析并计算关键指标，支持导出汇总。</div>
      <div className="flex items-center gap-3">
        <label className="underline cursor-pointer">
          <input type="file" className="hidden" multiple accept=".csv,.xlsx,.xls" onChange={(e) => onFiles(e.target.files)} />
          选择文件
        </label>
        <label className="flex items-center gap-1">并发
          <input type="number" className="w-16 border rounded px-2 py-1" value={concurrency} onChange={e => setConcurrency(Number(e.target.value))} min={1} max={8} />
        </label>
        <button className="underline" onClick={exportSummary} disabled={rows.length === 0 || running}>导出汇总CSV</button>
        {running && <span>处理中...</span>}
      </div>
      <div className="overflow-auto border rounded">
        <table className="w-full text-xs">
          <thead className="bg-muted/30">
            <tr>
              <th className="px-2 py-2 text-left">文件</th>
              <th className="px-2 py-2 text-left">状态</th>
              <th className="px-2 py-2 text-left">Ith(mA)</th>
              <th className="px-2 py-2 text-left">Slope(W/A)</th>
              <th className="px-2 py-2 text-left">Rs(Ω)</th>
              <th className="px-2 py-2 text-left">ηmax</th>
              <th className="px-2 py-2 text-left">λpeak(nm)</th>
              <th className="px-2 py-2 text-left">FWHM(nm)</th>
              <th className="px-2 py-2 text-left">错误</th>
            </tr>
          </thead>
          <tbody>
            {rows.map((r, i) => (
              <tr key={i} className="even:bg-muted/10">
                <td className="px-2 py-1">{r.name}</td>
                <td className="px-2 py-1">{r.status}</td>
                <td className="px-2 py-1">{fmt(r.ith)}</td>
                <td className="px-2 py-1">{fmt(r.slope)}</td>
                <td className="px-2 py-1">{fmt(r.rs)}</td>
                <td className="px-2 py-1">{fmt(r.etamax)}</td>
                <td className="px-2 py-1">{fmt(r.lambda)}</td>
                <td className="px-2 py-1">{fmt(r.fwhm)}</td>
                <td className="px-2 py-1 text-red-600">{r.error ?? ''}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}

function fmt(v?: number) { return v === undefined ? '' : String(v); }



