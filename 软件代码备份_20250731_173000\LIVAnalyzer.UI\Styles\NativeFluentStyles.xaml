<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- .NET 9 原生 Fluent Design 样式集合 -->

    <!-- 简化的主题切换按钮样式 -->
    <Style x:Key="ThemeToggleButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="{DynamicResource {x:Static SystemColors.ControlBrushKey}}"/>
        <Setter Property="Foreground" Value="{DynamicResource {x:Static SystemColors.ControlTextBrushKey}}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource {x:Static SystemColors.ActiveBorderBrushKey}}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Cursor" Value="Hand"/>
    </Style>
    
    <!-- 参数组样式 - 原生Fluent Design -->
    <Style x:Key="NativeFluentGroupBox" TargetType="GroupBox">
        <Setter Property="Margin" Value="5"/>
        <Setter Property="Padding" Value="8"/>
        <Setter Property="BorderBrush" Value="{DynamicResource SystemBaseLowColor}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Background" Value="{DynamicResource SystemChromeMediumLowColor}"/>
        <Setter Property="Foreground" Value="{DynamicResource SystemBaseHighColor}"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="GroupBox">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="8"
                            Margin="{TemplateBinding Margin}">
                        <!-- Fluent Design 阴影效果 -->
                        <Border.Effect>
                            <DropShadowEffect Color="{DynamicResource SystemBaseLowColor}" 
                                            BlurRadius="8" 
                                            ShadowDepth="2" 
                                            Opacity="0.2"/>
                        </Border.Effect>
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>
                            <ContentPresenter Grid.Row="0"
                                            ContentSource="Header"
                                            Margin="8,4"/>
                            <ContentPresenter Grid.Row="1"
                                            Margin="{TemplateBinding Padding}"/>
                        </Grid>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 工具栏按钮样式 - 原生Fluent Design -->
    <Style x:Key="NativeFluentToolbarButton" TargetType="Button">
        <Setter Property="Margin" Value="5,2"/>
        <Setter Property="Padding" Value="12,6"/>
        <Setter Property="MinHeight" Value="32"/>
        <Setter Property="MinWidth" Value="80"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Background" Value="{DynamicResource SystemAccentColor}"/>
        <Setter Property="Foreground" Value="{DynamicResource SystemAltHighColor}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource SystemAccentColor}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="4"
                            Padding="{TemplateBinding Padding}">
                        <!-- Fluent Design 微妙阴影 -->
                        <Border.Effect>
                            <DropShadowEffect Color="{DynamicResource SystemBaseLowColor}" 
                                            BlurRadius="4" 
                                            ShadowDepth="1" 
                                            Opacity="0.3"/>
                        </Border.Effect>
                        <ContentPresenter HorizontalAlignment="Center" 
                                        VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="{DynamicResource SystemAccentColorLight1}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" Value="{DynamicResource SystemAccentColorDark1}"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Background" Value="{DynamicResource SystemBaseLowColor}"/>
                            <Setter Property="Foreground" Value="{DynamicResource SystemBaseMediumColor}"/>
                            <Setter Property="BorderBrush" Value="{DynamicResource SystemBaseLowColor}"/>
                            <Setter Property="Opacity" Value="0.6"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 状态文本样式 - 原生Fluent Design -->
    <Style x:Key="NativeFluentStatusText" TargetType="TextBlock">
        <Setter Property="Foreground" Value="{DynamicResource SystemBaseMediumColor}"/>
        <Setter Property="Margin" Value="5,2"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="FontWeight" Value="Normal"/>
    </Style>

    <!-- 普通按钮样式 - 原生Fluent Design -->
    <Style x:Key="NativeFluentButton" TargetType="Button">
        <Setter Property="Background" Value="{DynamicResource SystemChromeMediumLowColor}"/>
        <Setter Property="Foreground" Value="{DynamicResource SystemBaseHighColor}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource SystemBaseLowColor}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Padding" Value="12,6"/>
        <Setter Property="MinHeight" Value="32"/>
        <Setter Property="FontWeight" Value="Normal"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="4"
                            Padding="{TemplateBinding Padding}">
                        <ContentPresenter HorizontalAlignment="Center" 
                                        VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="{DynamicResource SystemChromeLowColor}"/>
                            <Setter Property="BorderBrush" Value="{DynamicResource SystemBaseMediumColor}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" Value="{DynamicResource SystemChromeMediumColor}"/>
                            <Setter Property="BorderBrush" Value="{DynamicResource SystemBaseMediumColor}"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Background" Value="{DynamicResource SystemAltMediumColor}"/>
                            <Setter Property="Foreground" Value="{DynamicResource SystemBaseMediumColor}"/>
                            <Setter Property="BorderBrush" Value="{DynamicResource SystemBaseLowColor}"/>
                            <Setter Property="Opacity" Value="0.6"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 文本框样式 - 原生Fluent Design -->
    <Style x:Key="NativeFluentTextBox" TargetType="TextBox">
        <Setter Property="Background" Value="{DynamicResource SystemChromeMediumLowColor}"/>
        <Setter Property="Foreground" Value="{DynamicResource SystemBaseHighColor}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource SystemBaseLowColor}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Padding" Value="8,4"/>
        <Setter Property="MinHeight" Value="32"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="TextBox">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="4"
                            Padding="{TemplateBinding Padding}">
                        <ScrollViewer x:Name="PART_ContentHost"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsFocused" Value="True">
                            <Setter Property="BorderBrush" Value="{DynamicResource SystemAccentColor}"/>
                            <Setter Property="BorderThickness" Value="2"/>
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="BorderBrush" Value="{DynamicResource SystemBaseMediumColor}"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Background" Value="{DynamicResource SystemAltMediumColor}"/>
                            <Setter Property="Foreground" Value="{DynamicResource SystemBaseMediumColor}"/>
                            <Setter Property="BorderBrush" Value="{DynamicResource SystemBaseLowColor}"/>
                            <Setter Property="Opacity" Value="0.6"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 标签样式 - 原生Fluent Design -->
    <Style x:Key="NativeFluentLabel" TargetType="TextBlock">
        <Setter Property="Foreground" Value="{DynamicResource SystemBaseHighColor}"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
        <Setter Property="Margin" Value="0,2"/>
    </Style>

    <!-- 参数标签样式 - 原生Fluent Design -->
    <Style x:Key="NativeFluentParameterLabel" TargetType="TextBlock">
        <Setter Property="Foreground" Value="{DynamicResource SystemBaseHighColor}"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
        <Setter Property="Margin" Value="0,2"/>
    </Style>

    <!-- 参数值样式 - 原生Fluent Design -->
    <Style x:Key="NativeFluentParameterValue" TargetType="TextBlock">
        <Setter Property="Foreground" Value="{DynamicResource SystemBaseHighColor}"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
        <Setter Property="Margin" Value="0,2"/>
    </Style>

</ResourceDictionary>
