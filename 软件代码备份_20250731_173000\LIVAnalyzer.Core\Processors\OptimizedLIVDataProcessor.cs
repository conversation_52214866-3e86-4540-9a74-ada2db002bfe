using LIVAnalyzer.Models;
using MathNet.Numerics;
using MathNet.Numerics.LinearAlgebra;
using MathNet.Numerics.Statistics;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using LIVAnalyzer.Core.Services;
using LIVAnalyzer.Services.Logging;

namespace LIVAnalyzer.Core.Processors
{
    /// <summary>
    /// 优化的LIV数据处理器 - 使用并行计算和缓存优化
    /// </summary>
    public class OptimizedLIVDataProcessor : LIVDataProcessor
    {
        // 缓存计算结果
        private readonly ConcurrentDictionary<string, object> _cache = new();
        
        /// <summary>
        /// 清除缓存
        /// </summary>
        public override void ClearCache()
        {
            _cache.Clear();
        }
        
        /// <summary>
        /// 并行计算LIV参数
        /// </summary>
        public new LIVParameters CalculateParameters(LIVMeasurementData data)
        {
            var parameters = new LIVParameters();
            
            // 使用并行任务计算各项参数
            var tasks = new List<Task>();
            
            // 光谱参数计算任务
            if (data.WavelengthIntensityData.Any())
            {
                tasks.Add(Task.Run(() => 
                {
                    var spectralParams = CalculateSpectralParametersOptimized(data.WavelengthIntensityData);
                    parameters.PeakWavelength = spectralParams.PeakWavelength;
                    parameters.FWHM = spectralParams.FWHM;
                }));
            }
            
            // LIV参数计算任务
            if (data.CurrentPowerData.Any())
            {
                tasks.Add(Task.Run(() => 
                {
                    var livParams = CalculateLIVParametersOptimized(data.CurrentPowerData);
                    parameters.MaxPower = livParams.MaxPower;
                    parameters.ThresholdCurrent = livParams.ThresholdCurrent;
                    parameters.SlopeEfficiency = livParams.SlopeEfficiency;
                }));
            }
            
            // 电阻参数计算任务
            if (data.CurrentVoltageData.Any())
            {
                tasks.Add(Task.Run(() => 
                {
                    var resistanceParams = CalculateResistanceParametersOptimized(data.CurrentVoltageData);
                    parameters.SeriesResistance = resistanceParams.SeriesResistance;
                    parameters.SeriesResistanceR2 = resistanceParams.SeriesResistanceR2;
                    parameters.DifferentialResistance = resistanceParams.DifferentialResistance;
                }));
            }
            
            // 等待所有计算完成
            Task.WaitAll(tasks.ToArray());
            
            // 计算最大效率
            if (data.CurrentPowerData.Any() && data.CurrentVoltageData.Any())
            {
                parameters.MaxEfficiency = CalculateMaxEfficiencyOptimized(data.CurrentPowerData, data.CurrentVoltageData);
            }
            
            return parameters;
        }
        
        /// <summary>
        /// 优化的光谱参数计算
        /// </summary>
        private SpectralParameters CalculateSpectralParametersOptimized(List<DataPoint> wavelengthData)
        {
            var result = new SpectralParameters();
            
            // 转换为数组以提高性能
            int count = wavelengthData.Count;
            var wavelengths = MemoryPoolManager.RentDoubleArray(count);
            var intensities = MemoryPoolManager.RentDoubleArray(count);
            
            try
            {
                // 并行填充数组
                Parallel.For(0, count, i =>
                {
                    wavelengths[i] = wavelengthData[i].X;
                    intensities[i] = wavelengthData[i].Y;
                });
                
                // 找到峰值
                double maxIntensity = 0;
                int peakIndex = 0;
                for (int i = 0; i < count; i++)
                {
                    if (intensities[i] > maxIntensity)
                    {
                        maxIntensity = intensities[i];
                        peakIndex = i;
                    }
                }
                result.PeakWavelength = wavelengths[peakIndex];
                
                // 计算FWHM - 使用插值方法提高精度
                double halfMax = maxIntensity / 2.0;
                
                // 寻找左侧半高点（从峰值向左搜索）
                double leftWavelength = wavelengths[0]; // 默认值
                for (int i = peakIndex - 1; i >= 0; i--)
                {
                    if (intensities[i] <= halfMax && i < count - 1)
                    {
                        // 线性插值找到精确交点
                        if (Math.Abs(intensities[i + 1] - intensities[i]) > 1e-10)
                        {
                            double t = (halfMax - intensities[i]) / (intensities[i + 1] - intensities[i]);
                            leftWavelength = wavelengths[i] + t * (wavelengths[i + 1] - wavelengths[i]);
                        }
                        else
                        {
                            leftWavelength = wavelengths[i];
                        }
                        break;
                    }
                }
                
                // 寻找右侧半高点（从峰值向右搜索）
                double rightWavelength = wavelengths[count - 1]; // 默认值
                for (int i = peakIndex + 1; i < count; i++)
                {
                    if (intensities[i] <= halfMax && i > 0)
                    {
                        // 线性插值找到精确交点
                        if (Math.Abs(intensities[i] - intensities[i - 1]) > 1e-10)
                        {
                            double t = (halfMax - intensities[i - 1]) / (intensities[i] - intensities[i - 1]);
                            rightWavelength = wavelengths[i - 1] + t * (wavelengths[i] - wavelengths[i - 1]);
                        }
                        else
                        {
                            rightWavelength = wavelengths[i];
                        }
                        break;
                    }
                }
                
                result.FWHM = rightWavelength - leftWavelength;
            }
            finally
            {
                // 释放数组
                MemoryPoolManager.ReturnDoubleArray(wavelengths);
                MemoryPoolManager.ReturnDoubleArray(intensities);
            }
            
            return result;
        }
        
        /// <summary>
        /// 优化的LIV参数计算
        /// </summary>
        private LIVParametersPartial CalculateLIVParametersOptimized(List<DataPoint> currentPowerData)
        {
            var result = new LIVParametersPartial();
            
            // 使用LINQ的并行查询
            result.MaxPower = currentPowerData.AsParallel().Max(p => p.Y);
            
            // 优化的阈值电流计算
            result.ThresholdCurrent = CalculateThresholdCurrentOptimized(currentPowerData);
            
            // 优化的斜率效率计算
            result.SlopeEfficiency = CalculateSlopeEfficiencyOptimized(currentPowerData, result.ThresholdCurrent);
            
            return result;
        }
        
        /// <summary>
        /// 优化的阈值电流计算 - 使用改进的一阶导数法
        /// </summary>
        private double CalculateThresholdCurrentOptimized(List<DataPoint> currentPowerData)
        {
            if (currentPowerData.Count < 10) return 0;
            
            // 暂时禁用缓存以避免阈值电流计算错误
            // TODO: 修复缓存哈希碰撞问题后重新启用
            // string cacheKey = $"threshold_{GetDataHash(currentPowerData)}";
            // if (_cache.TryGetValue(cacheKey, out var cached))
            //     return (double)cached;
            
            try
            {
                // 使用基础处理器的改进算法
                var basicProcessor = new LIVDataProcessor();
                
                // 创建临时的LIVMeasurementData对象
                var tempData = new LIVMeasurementData
                {
                    CurrentPowerData = currentPowerData
                };
                
                // 调用基础处理器的计算方法
                var result = basicProcessor.CalculateParameters(tempData);
                var thresholdCurrent = result.ThresholdCurrent;
                
                // 暂时禁用缓存存储
                // _cache.TryAdd(cacheKey, thresholdCurrent);
                
                return thresholdCurrent;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"优化阈值电流计算失败: {ex.Message}");
                return 0;
            }
        }
        
        /// <summary>
        /// 并行移动平均算法
        /// </summary>
        private double[] ParallelMovingAverage(double[] data, int windowSize)
        {
            if (windowSize > data.Length) windowSize = data.Length;
            if (windowSize % 2 == 0) windowSize--;
            if (windowSize < 1) windowSize = 1;
            
            var result = new double[data.Length - windowSize + 1];
            
            Parallel.For(0, result.Length, i =>
            {
                double sum = 0;
                for (int j = 0; j < windowSize; j++)
                {
                    sum += data[i + j];
                }
                result[i] = sum / windowSize;
            });
            
            return result;
        }
        
        /// <summary>
        /// 使用二分查找找到阈值电流
        /// </summary>
        private double FindThresholdByBinarySearch(double[] current, double[] derivatives, double targetValue)
        {
            // 寻找第一个跨越目标值的点
            for (int i = 0; i < derivatives.Length - 1; i++)
            {
                if ((derivatives[i] - targetValue) * (derivatives[i + 1] - targetValue) <= 0)
                {
                    // 线性插值获取精确位置
                    if (Math.Abs(derivatives[i + 1] - derivatives[i]) > 1e-10)
                    {
                        double t = (targetValue - derivatives[i]) / (derivatives[i + 1] - derivatives[i]);
                        return current[i] + t * (current[i + 1] - current[i]);
                    }
                }
            }
            
            // 如果没找到交点，返回最接近的点
            int closestIdx = 0;
            double minDiff = Math.Abs(derivatives[0] - targetValue);
            
            for (int i = 1; i < derivatives.Length; i++)
            {
                double diff = Math.Abs(derivatives[i] - targetValue);
                if (diff < minDiff)
                {
                    minDiff = diff;
                    closestIdx = i;
                }
            }
            
            return current[closestIdx];
        }
        
        /// <summary>
        /// 优化的电阻参数计算
        /// </summary>
        private ResistanceParameters CalculateResistanceParametersOptimized(List<DataPoint> currentVoltageData)
        {
            var result = new ResistanceParameters();
            
            if (currentVoltageData.Count < 2) return result;
            
            // 使用向量化操作
            var xVector = Vector<double>.Build.Dense(currentVoltageData.Select(p => p.X).ToArray());
            var yVector = Vector<double>.Build.Dense(currentVoltageData.Select(p => p.Y).ToArray());
            
            var fit = SimpleLinearRegression(xVector, yVector);
            result.SeriesResistance = fit.Slope;
            result.SeriesResistanceR2 = fit.RSquared;
            
            // 并行计算微分电阻
            if (currentVoltageData.Count > 2)
            {
                var diffResistances = new double[currentVoltageData.Count - 1];
                Parallel.For(1, currentVoltageData.Count, i =>
                {
                    var dV = currentVoltageData[i].Y - currentVoltageData[i - 1].Y;
                    var dI = currentVoltageData[i].X - currentVoltageData[i - 1].X;
                    if (Math.Abs(dI) > 1e-10)
                    {
                        diffResistances[i - 1] = dV / dI;
                    }
                });
                
                result.DifferentialResistance = diffResistances.Where(r => r > 0).DefaultIfEmpty(0).Average();
            }
            
            return result;
        }
        
        /// <summary>
        /// 优化的最大效率计算
        /// </summary>
        private double CalculateMaxEfficiencyOptimized(List<DataPoint> currentPowerData, List<DataPoint> currentVoltageData)
        {
            // 创建查找字典以提高性能
            var voltageDict = new Dictionary<double, double>(currentVoltageData.Count);
            foreach (var point in currentVoltageData)
            {
                voltageDict[point.X] = point.Y;
            }
            
            // 计算所有有效的效率值
            var efficiencyData = new ConcurrentBag<(double Current, double Efficiency)>();
            
            Parallel.ForEach(currentPowerData, point =>
            {
                if (point.X > 0 && point.Y > 0 && voltageDict.TryGetValue(point.X, out var voltage) && voltage > 0)
                {
                    var efficiency = (point.Y / (point.X * voltage)) * 100;
                    efficiencyData.Add((point.X, efficiency));
                }
            });
            
            if (!efficiencyData.Any()) return 0;
            
            // 转换为列表以便进行统计分析
            var efficiencyList = efficiencyData.ToList();
            
            // 使用统计方法检测和过滤异常值（复用基本处理器的方法）
            var processor = new LIVDataProcessor();
            var cleanedEfficiencies = FilterEfficiencyOutliersOptimized(efficiencyList);
            
            return cleanedEfficiencies.Any() ? cleanedEfficiencies.Max() : 0;
        }
        
        /// <summary>
        /// 优化版本的效率异常值过滤（复用基本处理器的逻辑）
        /// </summary>
        private List<double> FilterEfficiencyOutliersOptimized(List<(double Current, double Efficiency)> efficiencyData)
        {
            if (efficiencyData.Count < 5) return efficiencyData.Select(e => e.Efficiency).ToList();
            
            // 将数据按电流分为低、中、高三个区间分别分析
            var maxCurrent = efficiencyData.Max(e => e.Current);
            var lowCurrentData = efficiencyData.Where(e => e.Current <= maxCurrent * 0.2).ToList();
            var midCurrentData = efficiencyData.Where(e => e.Current > maxCurrent * 0.2 && e.Current <= maxCurrent * 0.8).ToList();
            var highCurrentData = efficiencyData.Where(e => e.Current > maxCurrent * 0.8).ToList();
            
            var cleanedEfficiencies = new List<double>();
            
            // 对每个区间分别应用异常值检测
            cleanedEfficiencies.AddRange(FilterRegionOutliersOptimized(lowCurrentData, "低电流"));
            cleanedEfficiencies.AddRange(FilterRegionOutliersOptimized(midCurrentData, "中电流"));
            cleanedEfficiencies.AddRange(FilterRegionOutliersOptimized(highCurrentData, "高电流"));
            
            return cleanedEfficiencies;
        }
        
        /// <summary>
        /// 优化版本的区间异常值检测
        /// </summary>
        private List<double> FilterRegionOutliersOptimized(List<(double Current, double Efficiency)> regionData, string regionName)
        {
            if (regionData.Count < 3) return regionData.Select(e => e.Efficiency).ToList();
            
            var efficiencies = regionData.Select(e => e.Efficiency).ToList();
            efficiencies.Sort();
            
            // 使用四分位数方法检测异常值 (IQR method)
            var q1Index = (int)(efficiencies.Count * 0.25);
            var q3Index = (int)(efficiencies.Count * 0.75);
            var q1 = efficiencies[q1Index];
            var q3 = efficiencies[q3Index];
            var iqr = q3 - q1;
            
            // 异常值定义：超过 Q3 + 1.5*IQR 或低于 Q1 - 1.5*IQR
            var lowerBound = q1 - 1.5 * iqr;
            var upperBound = q3 + 1.5 * iqr;
            
            // 对于低电流区域，更严格地过滤上界异常值
            if (regionName == "低电流" && iqr > 0)
            {
                // 如果存在明显的异常高值，使用更保守的上界
                var median = efficiencies[efficiencies.Count / 2];
                var conservativeUpperBound = median + 2 * iqr;
                upperBound = Math.Min(upperBound, conservativeUpperBound);
                
                // 额外的物理约束：低电流区域效率通常不会超过中高电流区域
                upperBound = Math.Min(upperBound, 50.0); // 50%作为低电流区域的合理上限
            }
            
            return efficiencies.Where(e => e >= lowerBound && e <= upperBound).ToList();
        }
        
        /// <summary>
        /// 优化的斜率效率计算
        /// </summary>
        private double CalculateSlopeEfficiencyOptimized(List<DataPoint> currentPowerData, double thresholdCurrent)
        {
            var linearRegion = currentPowerData
                .Where(p => p.X > thresholdCurrent)
                .OrderBy(p => p.X)
                .ToList();
                
            if (linearRegion.Count < 2) return 0;
            
            var xVector = Vector<double>.Build.Dense(linearRegion.Select(p => p.X).ToArray());
            var yVector = Vector<double>.Build.Dense(linearRegion.Select(p => p.Y).ToArray());
            
            var fit = SimpleLinearRegression(xVector, yVector);
            return fit.Slope;
        }
        
        /// <summary>
        /// 简单线性回归 - 使用向量化操作
        /// </summary>
        private LinearRegressionResult SimpleLinearRegression(Vector<double> x, Vector<double> y)
        {
            int n = x.Count;
            double sumX = x.Sum();
            double sumY = y.Sum();
            double sumXX = x.DotProduct(x);
            double sumXY = x.DotProduct(y);
            
            double slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
            double intercept = (sumY - slope * sumX) / n;
            
            // 计算R²
            double yMean = sumY / n;
            var yPred = x.Map(xi => slope * xi + intercept);
            var ssRes = (y - yPred).Map(r => r * r).Sum();
            var ssTot = y.Map(yi => (yi - yMean) * (yi - yMean)).Sum();
            double rSquared = 1 - (ssRes / ssTot);
            
            return new LinearRegressionResult { Slope = slope, Intercept = intercept, RSquared = rSquared };
        }
        
        /// <summary>
        /// 二分查找左边界
        /// </summary>
        private int BinarySearchLeft(double[] arr, double target, int left, int right)
        {
            while (left < right)
            {
                int mid = left + (right - left) / 2;
                if (arr[mid] < target)
                    left = mid + 1;
                else
                    right = mid;
            }
            return left;
        }
        
        /// <summary>
        /// 二分查找右边界
        /// </summary>
        private int BinarySearchRight(double[] arr, double target, int left, int right)
        {
            while (left < right)
            {
                int mid = left + (right - left + 1) / 2;
                if (arr[mid] > target)
                    right = mid - 1;
                else
                    left = mid;
            }
            return left;
        }
        
        /// <summary>
        /// 获取数据哈希值用于缓存
        /// </summary>
        private string GetDataHash(List<DataPoint> data)
        {
            // 使用更强的哈希算法避免碰撞
            using (var sha256 = System.Security.Cryptography.SHA256.Create())
            {
                var hashData = new System.Text.StringBuilder();
                
                // 包含数据点数量
                hashData.Append(data.Count);
                hashData.Append('|');
                
                // 包含前10个和后10个数据点
                int takeCount = Math.Min(10, data.Count);
                for (int i = 0; i < takeCount; i++)
                {
                    hashData.Append($"{data[i].X:F8},{data[i].Y:F8}|");
                }
                
                if (data.Count > 20)
                {
                    for (int i = data.Count - takeCount; i < data.Count; i++)
                    {
                        hashData.Append($"{data[i].X:F8},{data[i].Y:F8}|");
                    }
                }
                
                // 包含数据统计信息
                double sumX = data.Sum(p => p.X);
                double sumY = data.Sum(p => p.Y);
                double avgX = sumX / data.Count;
                double avgY = sumY / data.Count;
                hashData.Append($"{sumX:F8},{sumY:F8},{avgX:F8},{avgY:F8}");
                
                // 计算SHA256哈希
                byte[] bytes = System.Text.Encoding.UTF8.GetBytes(hashData.ToString());
                byte[] hash = sha256.ComputeHash(bytes);
                
                // 返回哈希的前16个字符
                return BitConverter.ToString(hash).Replace("-", "").Substring(0, 16);
            }
        }
        
        // 内部类定义
        private class SpectralParameters
        {
            public double PeakWavelength { get; set; }
            public double FWHM { get; set; }
        }
        
        private class LIVParametersPartial
        {
            public double MaxPower { get; set; }
            public double ThresholdCurrent { get; set; }
            public double SlopeEfficiency { get; set; }
        }
        
        private class ResistanceParameters
        {
            public double? SeriesResistance { get; set; }
            public double? SeriesResistanceR2 { get; set; }
            public double? DifferentialResistance { get; set; }
        }
        
        private class LinearRegressionResult
        {
            public double Slope { get; set; }
            public double Intercept { get; set; }
            public double RSquared { get; set; }
        }
    }
}