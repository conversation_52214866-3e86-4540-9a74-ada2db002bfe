import { useAppStore } from '../state/store';

export default function ChartControls() {
  const { displayConfig, updateDisplayConfig } = useAppStore();
  const cfg = displayConfig;
  return (
    <div className="flex flex-wrap items-center gap-4 text-sm">
      <label className="flex items-center gap-1">
        <input type="checkbox" checked={cfg.showGrid ?? true} onChange={(e) => updateDisplayConfig({ showGrid: e.target.checked })} /> 网格
      </label>
      <label className="flex items-center gap-1">
        <input type="checkbox" checked={cfg.showMarkers ?? true} onChange={(e) => updateDisplayConfig({ showMarkers: e.target.checked })} /> 标记
      </label>
      <label className="flex items-center gap-1">
        <input type="checkbox" checked={cfg.showPI ?? true} onChange={(e) => updateDisplayConfig({ showPI: e.target.checked })} /> P-I
      </label>
      <label className="flex items-center gap-1">
        <input type="checkbox" checked={cfg.showVI ?? true} onChange={(e) => updateDisplayConfig({ showVI: e.target.checked })} /> V-I
      </label>
      <label className="flex items-center gap-1">
        <input type="checkbox" checked={cfg.showEta ?? true} onChange={(e) => updateDisplayConfig({ showEta: e.target.checked })} /> η-I
      </label>
      <label className="flex items-center gap-1">
        <input type="checkbox" checked={cfg.showDiagnosticsLIV ?? true} onChange={(e) => updateDisplayConfig({ showDiagnosticsLIV: e.target.checked })} /> 诊断层
      </label>
    </div>
  );
}


