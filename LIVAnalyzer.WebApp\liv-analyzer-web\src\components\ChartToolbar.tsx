import { Button } from "./ui/button";
import { ZoomIn, ZoomOut, Maximize2, Save, Image as ImageIcon, FileCode } from "lucide-react";
import { exportPngFromContainer, exportSvgFromContainer } from "../services/export";

interface ChartToolbarProps {
  title?: string;
  container: HTMLElement | null;
  onReset?: () => void | Promise<void>;
}

export default function ChartToolbar({ title, container, onReset }: ChartToolbarProps) {
  return (
    <div className="flex items-center gap-2 justify-between">
      <div className="text-xs text-muted-foreground">{title}</div>
      <div className="flex items-center gap-1">
        <Button
          variant="ghost"
          size="icon"
          title="导出SVG"
          onClick={() => exportSvgFromContainer(container, title || 'chart')}
        >
          <FileCode className="w-4 h-4" />
        </Button>
        <Button
          variant="ghost"
          size="icon"
          title="导出PNG"
          onClick={() => exportPngFromContainer(container, title || 'chart')}
        >
          <ImageIcon className="w-4 h-4" />
        </Button>
        <Button
          variant="ghost"
          size="icon"
          title="导出PNG(2x)"
          onClick={() => exportPngFromContainer(container, (title || 'chart') + '_2x', 2)}
        >
          <Save className="w-4 h-4" />
        </Button>
        <Button
          variant="ghost"
          size="icon"
          title="重置视图"
          onClick={() => { try { onReset?.(); } catch {} }}
        >
          <Maximize2 className="w-4 h-4" />
        </Button>
        <Button variant="ghost" size="icon" title="放大"><ZoomIn className="w-4 h-4" /></Button>
        <Button variant="ghost" size="icon" title="缩小"><ZoomOut className="w-4 h-4" /></Button>
      </div>
    </div>
  );
}


