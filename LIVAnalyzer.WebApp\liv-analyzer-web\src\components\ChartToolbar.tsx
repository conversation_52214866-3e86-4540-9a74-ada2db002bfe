import { Button } from "./ui/button";
import { ZoomIn, ZoomOut, Maximize2, Save, Image as ImageIcon, FileCode, Move, Crosshair, Grid3X3, Eye, EyeOff } from "lucide-react";
import { exportPngFromContainer, exportSvgFromContainer } from "../services/export";
import { useState } from "react";

interface ChartToolbarProps {
  title?: string;
  container: HTMLElement | null;
  onReset?: () => void | Promise<void>;
  onZoomIn?: () => void;
  onZoomOut?: () => void;
  onTogglePan?: (enabled: boolean) => void;
  onToggleCrosshair?: (enabled: boolean) => void;
  onToggleGrid?: (enabled: boolean) => void;
  enablePan?: boolean;
  enableCrosshair?: boolean;
  enableGrid?: boolean;
}

export default function ChartToolbar({
  title,
  container,
  onReset,
  onZoomIn,
  onZoomOut,
  onTogglePan,
  onToggleCrosshair,
  onToggleGrid,
  enablePan = false,
  enableCrosshair = false,
  enableGrid = true
}: ChartToolbarProps) {
  const [panEnabled, setPanEnabled] = useState(enablePan);
  const [crosshairEnabled, setCrosshairEnabled] = useState(enableCrosshair);
  const [gridEnabled, setGridEnabled] = useState(enableGrid);

  const handleTogglePan = () => {
    const newState = !panEnabled;
    setPanEnabled(newState);
    onTogglePan?.(newState);
  };

  const handleToggleCrosshair = () => {
    const newState = !crosshairEnabled;
    setCrosshairEnabled(newState);
    onToggleCrosshair?.(newState);
  };

  const handleToggleGrid = () => {
    const newState = !gridEnabled;
    setGridEnabled(newState);
    onToggleGrid?.(newState);
  };

  return (
    <div className="flex items-center gap-2 justify-between">
      <div className="text-xs text-muted-foreground">{title}</div>
      <div className="flex items-center gap-1">
        {/* 交互工具 */}
        <div className="flex items-center gap-1 border-r pr-2 mr-2">
          <Button
            variant={panEnabled ? "default" : "ghost"}
            size="icon"
            title="平移模式"
            onClick={handleTogglePan}
          >
            <Move className="w-4 h-4" />
          </Button>
          <Button
            variant={crosshairEnabled ? "default" : "ghost"}
            size="icon"
            title="十字线"
            onClick={handleToggleCrosshair}
          >
            <Crosshair className="w-4 h-4" />
          </Button>
          <Button
            variant={gridEnabled ? "default" : "ghost"}
            size="icon"
            title="网格"
            onClick={handleToggleGrid}
          >
            <Grid3X3 className="w-4 h-4" />
          </Button>
        </div>

        {/* 缩放工具 */}
        <div className="flex items-center gap-1 border-r pr-2 mr-2">
          <Button
            variant="ghost"
            size="icon"
            title="放大"
            onClick={onZoomIn}
          >
            <ZoomIn className="w-4 h-4" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            title="缩小"
            onClick={onZoomOut}
          >
            <ZoomOut className="w-4 h-4" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            title="重置视图"
            onClick={() => { try { onReset?.(); } catch {} }}
          >
            <Maximize2 className="w-4 h-4" />
          </Button>
        </div>

        {/* 导出工具 */}
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="icon"
            title="导出SVG"
            onClick={() => exportSvgFromContainer(container, title || 'chart')}
          >
            <FileCode className="w-4 h-4" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            title="导出PNG"
            onClick={() => exportPngFromContainer(container, title || 'chart')}
          >
            <ImageIcon className="w-4 h-4" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            title="导出PNG(2x)"
            onClick={() => exportPngFromContainer(container, (title || 'chart') + '_2x', 2)}
          >
            <Save className="w-4 h-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}


