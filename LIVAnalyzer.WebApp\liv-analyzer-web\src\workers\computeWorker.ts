/// <reference lib="webworker" />
import type { LIVData, LIVParameters, SpectralParameters, ProcessingConfig, DivergenceParameters } from '../types/data';

type Request = { kind: 'compute'; data: LIVData; config?: ProcessingConfig };
type Response = { ok: true; livParameters: LIVParameters; spectralParameters?: SpectralParameters; divergenceParameters?: DivergenceParameters; livFitDiagnostics?: { slope?: number; intercept?: number; usedIndices?: number[]; outlierIndices?: number[] } } | { ok: false; error: string };

function linearFit(x: number[], y: number[]): { slope: number; intercept: number } | null {
  const n = Math.min(x.length, y.length);
  if (n < 2) return null;
  let sx = 0, sy = 0, sxx = 0, sxy = 0;
  for (let i = 0; i < n; i++) {
    const xi = x[i];
    const yi = y[i];
    if (!Number.isFinite(xi) || !Number.isFinite(yi)) continue;
    sx += xi; sy += yi; sxx += xi * xi; sxy += xi * yi;
  }
  const denom = (n * sxx - sx * sx);
  if (Math.abs(denom) < 1e-12) return null;
  const slope = (n * sxy - sx * sy) / denom;
  const intercept = (sy - slope * sx) / n;
  return { slope, intercept };
}

function estimateThresholdAndSlope(power?: { current: number[], power: number[] }, cfg?: ProcessingConfig): { ith?: number; slope?: number } {
  if (!power) return {};
  const method = cfg?.thresholdDetection ?? 'linear';
  const n = Math.min(power.current.length, power.power.length);
  if (n < 2) return {};

  // 生成候选索引
  let indices = Array.from({ length: n }, (_, i) => i);

  if (method === 'segmented') {
    // 取尾段进行拟合：优先使用 fittingPoints 个末尾点；若无则回退为功率上位分位
    const k = Math.max(2, Math.min(cfg?.fittingPoints ?? 150, n));
    indices = indices.slice(n - k);
  } else {
    // 默认：功率超过 5% 峰值的点
    const maxP = Math.max(...power.power.filter(Number.isFinite), 0);
    const ratio = 0.05;
    indices = indices.filter(i => power.power[i] > ratio * maxP);
    if (indices.length < 2) indices = Array.from({ length: n }, (_, i) => i);
  }

  let x = indices.map(i => power.current[i]);
  let y = indices.map(i => power.power[i]);

  let fit = linearFit(x, y);
  if (method === 'robust' && fit) {
    // 两阶段鲁棒：剔除 > 2.5σ 的残差再拟合
    const residuals = x.map((xi, i) => y[i] - (fit!.slope * xi + fit!.intercept));
    const mean = residuals.reduce((a, b) => a + b, 0) / residuals.length;
    const varr = residuals.reduce((a, b) => a + (b - mean) * (b - mean), 0) / Math.max(1, residuals.length - 1);
    const sigma = Math.sqrt(Math.max(varr, 1e-30));
    const keep = residuals.map(r => Math.abs(r - mean) <= 2.5 * sigma);
    const x2: number[] = []; const y2: number[] = [];
    for (let i = 0; i < x.length; i++) if (keep[i]) { x2.push(x[i]); y2.push(y[i]); }
    if (x2.length >= 2) fit = linearFit(x2, y2) ?? fit;
  }
  if (!fit) return {};
  const { slope, intercept } = fit;
  const ith = slope !== 0 ? -intercept / slope : undefined;
  return { ith, slope };
}

function estimateSeriesResistance(voltage?: { current: number[], voltage: number[] }): number | undefined {
  if (!voltage) return undefined;
  const fit = linearFit(voltage.current, voltage.voltage);
  return fit?.slope; // dV/dI ~ 串联电阻
}

function estimateMaxEfficiency(power?: { current: number[], power: number[] }, voltage?: { current: number[], voltage: number[] }): number | undefined {
  if (!power || !voltage) return undefined;
  const n = Math.min(power.current.length, power.power.length, voltage.current.length, voltage.voltage.length);
  let maxEta = -Infinity;
  for (let i = 0; i < n; i++) {
    const I = power.current[i];
    const P = power.power[i];
    const V = voltage.voltage[i];
    const denom = I * V;
    if (Number.isFinite(P) && Number.isFinite(denom) && Math.abs(denom) > 1e-12) {
      const eta = P / denom;
      if (eta > maxEta) maxEta = eta;
    }
  }
  return Number.isFinite(maxEta) ? maxEta : undefined;
}

self.addEventListener('message', (ev: MessageEvent<Request>) => {
  const req = ev.data;
  try {
    if (req.kind === 'compute') {
      const data = req.data;
      const cfg = req.config ?? {};
      const diag = estimateThresholdAndSlope(data.power, cfg);
      const { ith, slope } = diag;
      const rs = estimateSeriesResistance(data.voltage);
      const maxEfficiency = estimateMaxEfficiency(data.power, data.voltage);
      // 光谱参数
      let spectralParameters: SpectralParameters | undefined = undefined;
      if (data.wavelength && data.wavelength.wavelength.length > 1) {
        const sp = computeSpectralParameters(
          data.wavelength.wavelength,
          data.wavelength.intensity
        );
        spectralParameters = sp || undefined;
      }

      // 发散角参数（FWHM 作为发散角）
      let divergenceParameters: DivergenceParameters | undefined = undefined;
      if (data.hff && data.hff.angle.length > 1) {
        const hff = computeAngularFwhm(data.hff.angle, data.hff.intensity);
        const vff = data.vff && data.vff.angle.length > 1 ? computeAngularFwhm(data.vff.angle, data.vff.intensity) : undefined;
        const horizontal_deg = hff ?? undefined;
        const vertical_deg = vff ?? undefined;
        const ellipticity = horizontal_deg !== undefined && vertical_deg !== undefined && vertical_deg > 0 ? (horizontal_deg / vertical_deg) : undefined;
        divergenceParameters = { horizontal_deg, vertical_deg, ellipticity };
      }

      const resp: Response = { ok: true, livParameters: {
        thresholdCurrent_mA: ith !== undefined ? ith * 1000 : undefined,
        slopeEfficiency_W_per_A: slope,
        seriesResistance_Ohm: rs,
        maxEfficiency,
      }, spectralParameters, divergenceParameters, livFitDiagnostics: { slope: diag.slope, intercept: (diag as any).intercept, usedIndices: (diag as any).usedIndices, outlierIndices: (diag as any).outlierIndices } };
      (self as unknown as Worker).postMessage(resp);
      return;
    }
  } catch (e) {
    const resp: Response = { ok: false, error: (e as Error).message };
    (self as unknown as Worker).postMessage(resp);
  }
});

function computeSpectralParameters(wavelength: number[], intensity: number[]): SpectralParameters | null {
  const n = Math.min(wavelength.length, intensity.length);
  if (n < 3) return null;
  // 找峰值
  let peakIdx = 0;
  let peakVal = -Infinity;
  for (let i = 0; i < n; i++) {
    const y = intensity[i];
    if (Number.isFinite(y) && y > peakVal) {
      peakVal = y; peakIdx = i;
    }
  }
  const lambdaPeak = wavelength[peakIdx];
  if (!Number.isFinite(lambdaPeak) || !Number.isFinite(peakVal) || peakVal <= 0) {
    return { peakWavelength_nm: undefined, fwhm_nm: undefined, centroid_nm: undefined, smsr_dB: undefined };
  }
  // FWHM（线性插值）
  const half = peakVal / 2;
  const left = findCrossing(wavelength, intensity, peakIdx, -1, half);
  const right = findCrossing(wavelength, intensity, peakIdx, +1, half);
  const fwhm = left !== null && right !== null ? Math.abs(right - left) : undefined;
  // 重心
  let sumW = 0, sumI = 0;
  for (let i = 0; i < n; i++) {
    const w = wavelength[i];
    const y = intensity[i];
    if (Number.isFinite(w) && Number.isFinite(y) && y > 0) {
      sumW += w * y;
      sumI += y;
    }
  }
  const centroid = sumI > 0 ? (sumW / sumI) : undefined;
  // SMSR：寻找次强局部峰
  const side = findSecondPeak(intensity, wavelength, peakIdx);
  const smsr = (side !== null && side.val > 0) ? 10 * Math.log10(peakVal / side.val) : undefined;
  return {
    peakWavelength_nm: lambdaPeak,
    fwhm_nm: fwhm,
    centroid_nm: centroid,
    smsr_dB: smsr,
  };
}

function computeAngularFwhm(angle: number[], intensity: number[]): number | null {
  const n = Math.min(angle.length, intensity.length);
  if (n < 3) return null;
  let peakIdx = 0, peakVal = -Infinity;
  for (let i = 0; i < n; i++) { const y = intensity[i]; if (Number.isFinite(y) && y > peakVal) { peakVal = y; peakIdx = i; } }
  if (!Number.isFinite(peakVal) || peakVal <= 0) return null;
  const half = peakVal / 2;
  const left = findCrossing(angle, intensity, peakIdx, -1, half);
  const right = findCrossing(angle, intensity, peakIdx, +1, half);
  if (left === null || right === null) return null;
  return Math.abs(right - left);
}

function findCrossing(x: number[], y: number[], startIdx: number, step: 1 | -1, target: number): number | null {
  let i = startIdx;
  while (i + step >= 0 && i + step < y.length) {
    const y1 = y[i];
    const y2 = y[i + step];
    if (!Number.isFinite(y1) || !Number.isFinite(y2)) { i += step; continue; }
    if ((y1 - target) * (y2 - target) <= 0) {
      // 线性插值 x at target between (i, i+step)
      const t = (target - y1) / (y2 - y1);
      const x1 = x[i];
      const x2 = x[i + step];
      if (!Number.isFinite(x1) || !Number.isFinite(x2)) return null;
      return x1 + t * (x2 - x1);
    }
    i += step;
  }
  return null;
}

function findSecondPeak(y: number[], _x: number[], peakIdx: number): { idx: number; val: number } | null {
  // 简单局部极大值检测，排除主峰附近 +/- 2 点
  let bestIdx = -1;
  let bestVal = -Infinity;
  for (let i = 1; i < y.length - 1; i++) {
    if (Math.abs(i - peakIdx) <= 2) continue;
    const yi = y[i];
    if (!Number.isFinite(yi)) continue;
    if (yi > (y[i - 1] ?? -Infinity) && yi > (y[i + 1] ?? -Infinity)) {
      if (yi > bestVal) { bestVal = yi; bestIdx = i; }
    }
  }
  return bestIdx >= 0 ? { idx: bestIdx, val: bestVal } : null;
}


