/// <reference lib="webworker" />
import type { LIVData, LIVParameters, SpectralParameters, ProcessingConfig, DivergenceParameters } from '../types/data';

type Request = { kind: 'compute'; data: LIVData; config?: ProcessingConfig };
type Response = { ok: true; livParameters: LIVParameters; spectralParameters?: SpectralParameters; divergenceParameters?: DivergenceParameters; livFitDiagnostics?: { slope?: number; intercept?: number; usedIndices?: number[]; outlierIndices?: number[] } } | { ok: false; error: string };

function linearFit(x: number[], y: number[]): { slope: number; intercept: number } | null {
  const n = Math.min(x.length, y.length);
  if (n < 2) return null;
  let sx = 0, sy = 0, sxx = 0, sxy = 0;
  for (let i = 0; i < n; i++) {
    const xi = x[i];
    const yi = y[i];
    if (!Number.isFinite(xi) || !Number.isFinite(yi)) continue;
    sx += xi; sy += yi; sxx += xi * xi; sxy += xi * yi;
  }
  const denom = (n * sxx - sx * sx);
  if (Math.abs(denom) < 1e-12) return null;
  const slope = (n * sxy - sx * sy) / denom;
  const intercept = (sy - slope * sx) / n;
  return { slope, intercept };
}

function estimateThresholdAndSlope(power?: { current: number[], power: number[] }, cfg?: ProcessingConfig): { ith?: number; slope?: number; usedIndices?: number[]; outlierIndices?: number[] } {
  if (!power) return {};
  const method = cfg?.thresholdDetection ?? 'linear';
  const n = Math.min(power.current.length, power.power.length);
  if (n < 10) return {}; // 与桌面版对齐，最少需要10个点

  // 数据预处理：移除零值、负值，按电流排序
  const validData: { current: number; power: number; index: number }[] = [];
  for (let i = 0; i < n; i++) {
    const current = power.current[i];
    const pow = power.power[i];
    if (Number.isFinite(current) && Number.isFinite(pow) && current >= 0 && pow >= 0) {
      validData.push({ current, power: pow, index: i });
    }
  }

  if (validData.length < 10) return {};

  // 按电流排序
  validData.sort((a, b) => a.current - b.current);

  if (method === 'linear') {
    // 使用改进的一阶导数法（与桌面版对齐）
    return calculateThresholdUsingDerivative(validData, cfg);
  } else if (method === 'segmented') {
    // 分段拟合：使用线性区域
    return calculateThresholdUsingSegmented(validData, cfg);
  } else if (method === 'robust') {
    // 鲁棒拟合：先用导数法，再用鲁棒回归
    const derivativeResult = calculateThresholdUsingDerivative(validData, cfg);
    if (derivativeResult.ith !== undefined) {
      return applyRobustFitting(validData, derivativeResult, cfg);
    }
    return derivativeResult;
  }

  return {};
}

function calculateThresholdUsingDerivative(validData: { current: number; power: number; index: number }[], cfg?: ProcessingConfig): { ith?: number; slope?: number; usedIndices?: number[]; outlierIndices?: number[] } {
  const n = validData.length;

  // 获取配置参数（与桌面版对齐）
  const thresholdConfig = cfg?.threshold ?? {};
  const minDataPoints = thresholdConfig.minDataPoints ?? 10;
  const searchRangeRatio = thresholdConfig.searchRangeRatio ?? 0.5;
  const maxThresholdRatio = thresholdConfig.maxThresholdRatio ?? 0.3;
  const secondaryMaxThresholdRatio = thresholdConfig.secondaryMaxThresholdRatio ?? 0.5;
  const primaryFallbackPowerRatio = thresholdConfig.primaryFallbackPowerRatio ?? 0.05;
  const secondaryFallbackPowerRatio = thresholdConfig.secondaryFallbackPowerRatio ?? 0.01;
  const tertiaryFallbackPowerRatio = thresholdConfig.tertiaryFallbackPowerRatio ?? 0.02;
  const maxSmoothingWindow = thresholdConfig.maxSmoothingWindow ?? 31;
  const dataWindowRatio = thresholdConfig.dataWindowRatio ?? 0.2;
  const maxDerivativeSmoothingWindow = thresholdConfig.maxDerivativeSmoothingWindow ?? 5;
  const derivativeWindowRatio = thresholdConfig.derivativeWindowRatio ?? 0.33;
  const derivativeRatio = thresholdConfig.derivativeRatio ?? 0.5;
  const numericalPrecision = thresholdConfig.numericalPrecision ?? 1e-10;

  if (n < minDataPoints) return {};

  // 1. 限制搜索范围（与桌面版对齐）
  const searchEndIndex = Math.floor(n * searchRangeRatio);
  const searchData = validData.slice(0, Math.max(minDataPoints, searchEndIndex));
  const searchN = searchData.length;

  // 2. 自适应窗口大小计算（确保奇数）
  let windowSize = Math.max(3, Math.min(Math.floor(searchN * dataWindowRatio), maxSmoothingWindow));
  if (windowSize % 2 === 0) windowSize++;

  // 3. 移动平均平滑功率数据
  const smoothedPower = movingAverage(searchData.map(d => d.power), windowSize);

  // 4. 计算一阶导数 (dP/dI)
  const derivatives: number[] = [];
  const derivativeCurrent: number[] = [];

  for (let i = 1; i < searchN - 1; i++) {
    const dI = searchData[i + 1].current - searchData[i - 1].current;
    const dP = smoothedPower[i + 1] - smoothedPower[i - 1];
    if (Math.abs(dI) > numericalPrecision) {
      derivatives.push(dP / dI);
      derivativeCurrent.push(searchData[i].current);
    }
  }

  if (derivatives.length < 3) return {};

  // 5. 对导数进行二次平滑
  let derivativeSmoothingWindow = Math.max(3, Math.min(Math.floor(derivatives.length * derivativeWindowRatio), maxDerivativeSmoothingWindow));
  if (derivativeSmoothingWindow % 2 === 0) derivativeSmoothingWindow++;
  const smoothedDerivatives = movingAverage(derivatives, derivativeSmoothingWindow);

  // 6. 找到最大导数值
  let maxDerivative = -Infinity;
  let maxIndex = -1;
  for (let i = 0; i < smoothedDerivatives.length; i++) {
    if (smoothedDerivatives[i] > maxDerivative) {
      maxDerivative = smoothedDerivatives[i];
      maxIndex = i;
    }
  }

  if (maxIndex === -1 || maxDerivative <= 0) return {};

  // 7. 使用配置的导数比例
  const targetDerivative = maxDerivative * derivativeRatio;

  // 8. 使用插值找到导数等于目标值的精确位置
  let thresholdCurrent = 0;

  for (let i = 0; i < smoothedDerivatives.length - 1; i++) {
    if ((smoothedDerivatives[i] - targetDerivative) * (smoothedDerivatives[i + 1] - targetDerivative) <= 0) {
      // 线性插值
      if (Math.abs(smoothedDerivatives[i + 1] - smoothedDerivatives[i]) > numericalPrecision) {
        const t = (targetDerivative - smoothedDerivatives[i]) /
                  (smoothedDerivatives[i + 1] - smoothedDerivatives[i]);
        thresholdCurrent = derivativeCurrent[i] + t * (derivativeCurrent[i + 1] - derivativeCurrent[i]);
        break;
      }
    }
  }

  // 9. 计算斜率效率（阈值以上的线性区域）
  const linearRegion = validData.filter(d => d.current > thresholdCurrent);
  let slope = 0;
  const usedIndices: number[] = [];

  if (linearRegion.length >= 2) {
    const x = linearRegion.map(d => d.current);
    const y = linearRegion.map(d => d.power);
    const fit = linearFit(x, y);
    if (fit) {
      slope = fit.slope;
      usedIndices.push(...linearRegion.map(d => d.index));
    }
  }

  // 10. 严格的合理性验证（与桌面版对齐）
  const maxCurrent = validData[validData.length - 1].current;
  const maxPower = Math.max(...validData.map(d => d.power));

  if (thresholdCurrent < 0 || thresholdCurrent > maxCurrent * maxThresholdRatio) {
    // 使用主要回退方法
    const fallbackThreshold = maxPower * primaryFallbackPowerRatio;
    const fallbackPoint = validData.find(d => d.power > fallbackThreshold);
    if (fallbackPoint) {
      thresholdCurrent = fallbackPoint.current;
    } else {
      // 使用次级回退
      const secondaryThreshold = maxPower * secondaryFallbackPowerRatio;
      const secondaryPoint = validData.find(d => d.power > secondaryThreshold);
      if (secondaryPoint) {
        thresholdCurrent = secondaryPoint.current;
      } else {
        thresholdCurrent = 0;
      }
    }
  }

  // 11. 最终验证
  if (thresholdCurrent > maxCurrent * secondaryMaxThresholdRatio) {
    // 使用第三级回退
    const tertiaryThreshold = maxPower * tertiaryFallbackPowerRatio;
    const tertiaryPoint = validData.find(d => d.power > tertiaryThreshold);
    thresholdCurrent = tertiaryPoint?.current ?? 0;
  }

  return { ith: thresholdCurrent, slope, usedIndices };
}

function movingAverage(data: number[], windowSize: number): number[] {
  const result: number[] = [];
  const halfWindow = Math.floor(windowSize / 2);

  for (let i = 0; i < data.length; i++) {
    let sum = 0;
    let count = 0;

    for (let j = Math.max(0, i - halfWindow); j <= Math.min(data.length - 1, i + halfWindow); j++) {
      if (Number.isFinite(data[j])) {
        sum += data[j];
        count++;
      }
    }

    result.push(count > 0 ? sum / count : data[i]);
  }

  return result;
}

function calculateThresholdUsingSegmented(validData: { current: number; power: number; index: number }[], cfg?: ProcessingConfig): { ith?: number; slope?: number; usedIndices?: number[]; outlierIndices?: number[] } {
  const n = validData.length;
  const fittingPoints = Math.max(2, Math.min(cfg?.fittingPoints ?? 150, n));

  // 使用末尾的线性区域进行拟合
  const linearRegion = validData.slice(n - fittingPoints);

  if (linearRegion.length < 2) return {};

  const x = linearRegion.map(d => d.current);
  const y = linearRegion.map(d => d.power);
  const fit = linearFit(x, y);

  if (!fit) return {};

  const { slope, intercept } = fit;
  const ith = slope !== 0 ? -intercept / slope : undefined;
  const usedIndices = linearRegion.map(d => d.index);

  return { ith, slope, usedIndices };
}

function applyRobustFitting(validData: { current: number; power: number; index: number }[], initialResult: { ith?: number; slope?: number; usedIndices?: number[] }, cfg?: ProcessingConfig): { ith?: number; slope?: number; usedIndices?: number[]; outlierIndices?: number[] } {
  if (!initialResult.ith || !initialResult.usedIndices) return initialResult;

  // 获取线性区域数据
  const linearRegion = validData.filter(d => d.current > initialResult.ith!);

  if (linearRegion.length < 3) return initialResult;

  const x = linearRegion.map(d => d.current);
  const y = linearRegion.map(d => d.power);

  let fit = linearFit(x, y);
  if (!fit) return initialResult;

  // 鲁棒拟合：剔除异常值
  const residuals = x.map((xi, i) => y[i] - (fit!.slope * xi + fit!.intercept));
  const mean = residuals.reduce((a, b) => a + b, 0) / residuals.length;
  const variance = residuals.reduce((a, b) => a + (b - mean) * (b - mean), 0) / Math.max(1, residuals.length - 1);
  const sigma = Math.sqrt(Math.max(variance, 1e-30));

  const outlierIndices: number[] = [];
  const keepIndices: number[] = [];

  for (let i = 0; i < residuals.length; i++) {
    if (Math.abs(residuals[i] - mean) <= 2.5 * sigma) {
      keepIndices.push(i);
    } else {
      outlierIndices.push(linearRegion[i].index);
    }
  }

  if (keepIndices.length >= 2) {
    const x2 = keepIndices.map(i => x[i]);
    const y2 = keepIndices.map(i => y[i]);
    const robustFit = linearFit(x2, y2);

    if (robustFit) {
      const robustIth = robustFit.slope !== 0 ? -robustFit.intercept / robustFit.slope : initialResult.ith;
      const usedIndices = keepIndices.map(i => linearRegion[i].index);

      return {
        ith: robustIth,
        slope: robustFit.slope,
        usedIndices,
        outlierIndices
      };
    }
  }

  return initialResult;
}

function estimateSeriesResistance(voltage?: { current: number[], voltage: number[] }): number | undefined {
  if (!voltage) return undefined;
  const fit = linearFit(voltage.current, voltage.voltage);
  return fit?.slope; // dV/dI ~ 串联电阻
}

function estimateMaxEfficiency(power?: { current: number[], power: number[] }, voltage?: { current: number[], voltage: number[] }): number | undefined {
  if (!power || !voltage) return undefined;
  const n = Math.min(power.current.length, power.power.length, voltage.current.length, voltage.voltage.length);
  let maxEta = -Infinity;
  for (let i = 0; i < n; i++) {
    const I = power.current[i];
    const P = power.power[i];
    const V = voltage.voltage[i];
    const denom = I * V;
    if (Number.isFinite(P) && Number.isFinite(denom) && Math.abs(denom) > 1e-12) {
      const eta = P / denom;
      if (eta > maxEta) maxEta = eta;
    }
  }
  return Number.isFinite(maxEta) ? maxEta : undefined;
}

self.addEventListener('message', (ev: MessageEvent<Request>) => {
  const req = ev.data;
  try {
    if (req.kind === 'compute') {
      const data = req.data;
      const cfg = req.config ?? {};
      const diag = estimateThresholdAndSlope(data.power, cfg);
      const { ith, slope } = diag;
      const rs = estimateSeriesResistance(data.voltage);
      const maxEfficiency = estimateMaxEfficiency(data.power, data.voltage);
      // 光谱参数
      let spectralParameters: SpectralParameters | undefined = undefined;
      if (data.wavelength && data.wavelength.wavelength.length > 1) {
        const sp = computeSpectralParameters(
          data.wavelength.wavelength,
          data.wavelength.intensity
        );
        spectralParameters = sp || undefined;
      }

      // 发散角参数（FWHM 作为发散角）
      let divergenceParameters: DivergenceParameters | undefined = undefined;
      if (data.hff && data.hff.angle.length > 1) {
        const hff = computeAngularFwhm(data.hff.angle, data.hff.intensity);
        const vff = data.vff && data.vff.angle.length > 1 ? computeAngularFwhm(data.vff.angle, data.vff.intensity) : undefined;
        const horizontal_deg = hff ?? undefined;
        const vertical_deg = vff ?? undefined;
        const ellipticity = horizontal_deg !== undefined && vertical_deg !== undefined && vertical_deg > 0 ? (horizontal_deg / vertical_deg) : undefined;
        divergenceParameters = { horizontal_deg, vertical_deg, ellipticity };
      }

      const resp: Response = { ok: true, livParameters: {
        thresholdCurrent_mA: ith !== undefined ? ith * 1000 : undefined,
        slopeEfficiency_W_per_A: slope,
        seriesResistance_Ohm: rs,
        maxEfficiency,
      }, spectralParameters, divergenceParameters, livFitDiagnostics: {
        slope: diag.slope,
        intercept: diag.slope && diag.ith ? (diag.ith * -diag.slope) : undefined,
        usedIndices: diag.usedIndices,
        outlierIndices: diag.outlierIndices
      } };
      (self as unknown as Worker).postMessage(resp);
      return;
    }
  } catch (e) {
    const resp: Response = { ok: false, error: (e as Error).message };
    (self as unknown as Worker).postMessage(resp);
  }
});

function computeSpectralParameters(wavelength: number[], intensity: number[]): SpectralParameters | null {
  const n = Math.min(wavelength.length, intensity.length);
  if (n < 3) return null;
  // 找峰值
  let peakIdx = 0;
  let peakVal = -Infinity;
  for (let i = 0; i < n; i++) {
    const y = intensity[i];
    if (Number.isFinite(y) && y > peakVal) {
      peakVal = y; peakIdx = i;
    }
  }
  const lambdaPeak = wavelength[peakIdx];
  if (!Number.isFinite(lambdaPeak) || !Number.isFinite(peakVal) || peakVal <= 0) {
    return { peakWavelength_nm: undefined, fwhm_nm: undefined, centroid_nm: undefined, smsr_dB: undefined };
  }
  // FWHM（线性插值）
  const half = peakVal / 2;
  const left = findCrossing(wavelength, intensity, peakIdx, -1, half);
  const right = findCrossing(wavelength, intensity, peakIdx, +1, half);
  const fwhm = left !== null && right !== null ? Math.abs(right - left) : undefined;
  // 重心
  let sumW = 0, sumI = 0;
  for (let i = 0; i < n; i++) {
    const w = wavelength[i];
    const y = intensity[i];
    if (Number.isFinite(w) && Number.isFinite(y) && y > 0) {
      sumW += w * y;
      sumI += y;
    }
  }
  const centroid = sumI > 0 ? (sumW / sumI) : undefined;
  // SMSR：寻找次强局部峰
  const side = findSecondPeak(intensity, wavelength, peakIdx);
  const smsr = (side !== null && side.val > 0) ? 10 * Math.log10(peakVal / side.val) : undefined;
  return {
    peakWavelength_nm: lambdaPeak,
    fwhm_nm: fwhm,
    centroid_nm: centroid,
    smsr_dB: smsr,
  };
}

function computeAngularFwhm(angle: number[], intensity: number[]): number | null {
  const n = Math.min(angle.length, intensity.length);
  if (n < 3) return null;
  let peakIdx = 0, peakVal = -Infinity;
  for (let i = 0; i < n; i++) { const y = intensity[i]; if (Number.isFinite(y) && y > peakVal) { peakVal = y; peakIdx = i; } }
  if (!Number.isFinite(peakVal) || peakVal <= 0) return null;
  const half = peakVal / 2;
  const left = findCrossing(angle, intensity, peakIdx, -1, half);
  const right = findCrossing(angle, intensity, peakIdx, +1, half);
  if (left === null || right === null) return null;
  return Math.abs(right - left);
}

function findCrossing(x: number[], y: number[], startIdx: number, step: 1 | -1, target: number): number | null {
  let i = startIdx;
  while (i + step >= 0 && i + step < y.length) {
    const y1 = y[i];
    const y2 = y[i + step];
    if (!Number.isFinite(y1) || !Number.isFinite(y2)) { i += step; continue; }
    if ((y1 - target) * (y2 - target) <= 0) {
      // 线性插值 x at target between (i, i+step)
      const t = (target - y1) / (y2 - y1);
      const x1 = x[i];
      const x2 = x[i + step];
      if (!Number.isFinite(x1) || !Number.isFinite(x2)) return null;
      return x1 + t * (x2 - x1);
    }
    i += step;
  }
  return null;
}

function findSecondPeak(y: number[], _x: number[], peakIdx: number): { idx: number; val: number } | null {
  // 简单局部极大值检测，排除主峰附近 +/- 2 点
  let bestIdx = -1;
  let bestVal = -Infinity;
  for (let i = 1; i < y.length - 1; i++) {
    if (Math.abs(i - peakIdx) <= 2) continue;
    const yi = y[i];
    if (!Number.isFinite(yi)) continue;
    if (yi > (y[i - 1] ?? -Infinity) && yi > (y[i + 1] ?? -Infinity)) {
      if (yi > bestVal) { bestVal = yi; bestIdx = i; }
    }
  }
  return bestIdx >= 0 ? { idx: bestIdx, val: bestVal } : null;
}


