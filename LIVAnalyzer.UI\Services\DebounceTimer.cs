using System;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Threading;

namespace LIVAnalyzer.UI.Services
{
    /// <summary>
    /// 防抖定时器，用于避免频繁触发操作
    /// </summary>
    public class DebounceTimer : IDisposable
    {
        private readonly int _delayMilliseconds;
        private readonly Dispatcher _dispatcher;
        private CancellationTokenSource? _cancellationTokenSource;
        private readonly object _lock = new object();
        private bool _disposed = false;

        public DebounceTimer(int delayMilliseconds = 300, Dispatcher? dispatcher = null)
        {
            _delayMilliseconds = delayMilliseconds;
            _dispatcher = dispatcher ?? Dispatcher.CurrentDispatcher;
        }

        /// <summary>
        /// 触发防抖操作
        /// </summary>
        /// <param name="action">要执行的操作</param>
        public void Trigger(Action action)
        {
            if (_disposed) return;

            lock (_lock)
            {
                // 取消之前的操作
                _cancellationTokenSource?.Cancel();
                _cancellationTokenSource = new CancellationTokenSource();
                
                var token = _cancellationTokenSource.Token;

                // 启动新的延迟任务
                Task.Run(async () =>
                {
                    try
                    {
                        await Task.Delay(_delayMilliseconds, token);
                        
                        if (!token.IsCancellationRequested)
                        {
                            // 在UI线程上执行操作
                            await _dispatcher.BeginInvoke(action, DispatcherPriority.Background);
                        }
                    }
                    catch (OperationCanceledException)
                    {
                        // 预期的取消操作，不需要处理
                    }
                    catch (Exception ex)
                    {
                        // 记录错误但不抛出异常
                        Console.WriteLine($"DebounceTimer执行操作时发生错误: {ex.Message}");
                    }
                }, token);
            }
        }

        /// <summary>
        /// 触发防抖异步操作
        /// </summary>
        /// <param name="asyncAction">要执行的异步操作</param>
        public void TriggerAsync(Func<Task> asyncAction)
        {
            if (_disposed) return;

            lock (_lock)
            {
                // 取消之前的操作
                _cancellationTokenSource?.Cancel();
                _cancellationTokenSource = new CancellationTokenSource();
                
                var token = _cancellationTokenSource.Token;

                // 启动新的延迟任务
                Task.Run(async () =>
                {
                    try
                    {
                        await Task.Delay(_delayMilliseconds, token);
                        
                        if (!token.IsCancellationRequested)
                        {
                            // 在UI线程上执行异步操作
                            await _dispatcher.BeginInvoke(asyncAction, DispatcherPriority.Background);
                        }
                    }
                    catch (OperationCanceledException)
                    {
                        // 预期的取消操作，不需要处理
                    }
                    catch (Exception ex)
                    {
                        // 记录错误但不抛出异常
                        Console.WriteLine($"DebounceTimer执行异步操作时发生错误: {ex.Message}");
                    }
                }, token);
            }
        }

        /// <summary>
        /// 立即取消所有待执行的操作
        /// </summary>
        public void Cancel()
        {
            if (_disposed) return;

            lock (_lock)
            {
                _cancellationTokenSource?.Cancel();
            }
        }

        /// <summary>
        /// 检查是否有待执行的操作
        /// </summary>
        public bool HasPendingOperation
        {
            get
            {
                lock (_lock)
                {
                    return _cancellationTokenSource != null && !_cancellationTokenSource.Token.IsCancellationRequested;
                }
            }
        }

        public void Dispose()
        {
            if (_disposed) return;

            lock (_lock)
            {
                _disposed = true;
                _cancellationTokenSource?.Cancel();
                _cancellationTokenSource?.Dispose();
            }
        }
    }

    /// <summary>
    /// 支持参数的防抖定时器
    /// </summary>
    /// <typeparam name="T">参数类型</typeparam>
    public class DebounceTimer<T> : IDisposable
    {
        private readonly int _delayMilliseconds;
        private readonly Dispatcher _dispatcher;
        private CancellationTokenSource? _cancellationTokenSource;
        private readonly object _lock = new object();
        private bool _disposed = false;
        private T? _latestParameter;

        public DebounceTimer(int delayMilliseconds = 300, Dispatcher? dispatcher = null)
        {
            _delayMilliseconds = delayMilliseconds;
            _dispatcher = dispatcher ?? Dispatcher.CurrentDispatcher;
        }

        /// <summary>
        /// 触发带参数的防抖操作
        /// </summary>
        /// <param name="parameter">参数</param>
        /// <param name="action">要执行的操作</param>
        public void Trigger(T parameter, Action<T> action)
        {
            if (_disposed) return;

            lock (_lock)
            {
                _latestParameter = parameter;

                // 取消之前的操作
                _cancellationTokenSource?.Cancel();
                _cancellationTokenSource = new CancellationTokenSource();
                
                var token = _cancellationTokenSource.Token;

                // 启动新的延迟任务
                Task.Run(async () =>
                {
                    try
                    {
                        await Task.Delay(_delayMilliseconds, token);
                        
                        if (!token.IsCancellationRequested)
                        {
                            var paramToUse = _latestParameter;
                            // 在UI线程上执行操作
                            await _dispatcher.BeginInvoke(() => action(paramToUse!), DispatcherPriority.Background);
                        }
                    }
                    catch (OperationCanceledException)
                    {
                        // 预期的取消操作，不需要处理
                    }
                    catch (Exception ex)
                    {
                        // 记录错误但不抛出异常
                        Console.WriteLine($"DebounceTimer<T>执行操作时发生错误: {ex.Message}");
                    }
                }, token);
            }
        }

        public void Cancel()
        {
            if (_disposed) return;

            lock (_lock)
            {
                _cancellationTokenSource?.Cancel();
            }
        }

        public bool HasPendingOperation
        {
            get
            {
                lock (_lock)
                {
                    return _cancellationTokenSource != null && !_cancellationTokenSource.Token.IsCancellationRequested;
                }
            }
        }

        public void Dispose()
        {
            if (_disposed) return;

            lock (_lock)
            {
                _disposed = true;
                _cancellationTokenSource?.Cancel();
                _cancellationTokenSource?.Dispose();
                _latestParameter = default;
            }
        }
    }
}