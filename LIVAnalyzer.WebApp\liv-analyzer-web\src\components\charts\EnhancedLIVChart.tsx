import { useEffect, useRef, useState, useCallback } from 'react';
import { useAppStore } from '../../state/store';
import { movingAverage, savitzkyGolay, gaussianSmooth, butterworthLowpass, medianFilter, adaptiveSmooth } from '../../services/smoothing';
import ChartToolbar from "../ChartToolbar";

// 动态导入 Plot 以减少首包
let Plot: any;

interface ChartState {
  scale: number;
  offsetX: number;
  offsetY: number;
  panEnabled: boolean;
  crosshairEnabled: boolean;
  gridEnabled: boolean;
}

export default function EnhancedLIVChart() {
  const containerRef = useRef<HTMLDivElement | null>(null);
  const overlayRef = useRef<HTMLDivElement | null>(null);
  const { data, displayConfig } = useAppStore();
  
  const [chartState, setChartState] = useState<ChartState>({
    scale: 1,
    offsetX: 0,
    offsetY: 0,
    panEnabled: false,
    crosshairEnabled: true,
    gridEnabled: true
  });
  
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [crosshair, setCrosshair] = useState<{ x: number; y: number; visible: boolean }>({
    x: 0,
    y: 0,
    visible: false
  });

  const resetView = useCallback(() => {
    setChartState(prev => ({
      ...prev,
      scale: 1,
      offsetX: 0,
      offsetY: 0
    }));
  }, []);

  const zoomIn = useCallback(() => {
    setChartState(prev => ({
      ...prev,
      scale: Math.min(prev.scale * 1.2, 10)
    }));
  }, []);

  const zoomOut = useCallback(() => {
    setChartState(prev => ({
      ...prev,
      scale: Math.max(prev.scale / 1.2, 0.1)
    }));
  }, []);

  const togglePan = useCallback((enabled: boolean) => {
    setChartState(prev => ({ ...prev, panEnabled: enabled }));
  }, []);

  const toggleCrosshair = useCallback((enabled: boolean) => {
    setChartState(prev => ({ ...prev, crosshairEnabled: enabled }));
  }, []);

  const toggleGrid = useCallback((enabled: boolean) => {
    setChartState(prev => ({ ...prev, gridEnabled: enabled }));
  }, []);

  // 处理鼠标滚轮缩放
  const handleWheel = useCallback((e: WheelEvent) => {
    e.preventDefault();
    
    const rect = containerRef.current?.getBoundingClientRect();
    if (!rect) return;

    const mouseX = e.clientX - rect.left;
    const mouseY = e.clientY - rect.top;
    
    const zoomFactor = e.deltaY > 0 ? 0.9 : 1.1;
    
    setChartState(prev => {
      const newScale = Math.max(0.1, Math.min(10, prev.scale * zoomFactor));
      const scaleChange = newScale / prev.scale;
      
      return {
        ...prev,
        scale: newScale,
        offsetX: mouseX - (mouseX - prev.offsetX) * scaleChange,
        offsetY: mouseY - (mouseY - prev.offsetY) * scaleChange
      };
    });
  }, []);

  // 处理鼠标事件
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (!chartState.panEnabled) return;
    setIsDragging(true);
    setDragStart({ x: e.clientX - chartState.offsetX, y: e.clientY - chartState.offsetY });
  }, [chartState.panEnabled, chartState.offsetX, chartState.offsetY]);

  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    const rect = containerRef.current?.getBoundingClientRect();
    if (!rect) return;

    const mouseX = e.clientX - rect.left;
    const mouseY = e.clientY - rect.top;

    // 更新十字线
    if (chartState.crosshairEnabled) {
      setCrosshair({
        x: mouseX,
        y: mouseY,
        visible: true
      });
    }

    // 处理拖拽
    if (isDragging && chartState.panEnabled) {
      setChartState(prev => ({
        ...prev,
        offsetX: e.clientX - dragStart.x,
        offsetY: e.clientY - dragStart.y
      }));
    }
  }, [isDragging, dragStart, chartState.panEnabled, chartState.crosshairEnabled]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  const handleMouseLeave = useCallback(() => {
    setIsDragging(false);
    setCrosshair(prev => ({ ...prev, visible: false }));
  }, []);

  // 渲染图表
  const render = useCallback(() => {
    if (!containerRef.current || !Plot) return;

    const marks: any[] = [];
    
    // 网格
    if (chartState.gridEnabled && (displayConfig.showGrid ?? true)) {
      marks.push(Plot.gridX(), Plot.gridY());
    }

    // 处理功率数据
    if (data?.power && data.power.current.length && data.power.power.length) {
      const cfg = useAppStore.getState().processingConfig;
      let pY = data.power.power.slice();
      
      // 应用平滑
      if (cfg.smoothing?.enabled) {
        if (cfg.smoothing.method === 'moving-average') {
          pY = movingAverage(pY, cfg.smoothing.windowSize ?? 5);
        } else if (cfg.smoothing.method === 'sg') {
          pY = savitzkyGolay(pY, cfg.smoothing.windowSize ?? 5, cfg.smoothing.polynomialOrder ?? 3);
        } else if (cfg.smoothing.method === 'gaussian') {
          pY = gaussianSmooth(pY, cfg.smoothing.windowSize ?? 5, cfg.smoothing.sigma);
        } else if (cfg.smoothing.method === 'butterworth') {
          pY = butterworthLowpass(pY, cfg.smoothing.cutoff ?? 0.15, cfg.smoothing.order);
        } else if (cfg.smoothing.method === 'median') {
          pY = medianFilter(pY, cfg.smoothing.windowSize ?? 5);
        } else if (cfg.smoothing.method === 'adaptive') {
          pY = adaptiveSmooth(pY, cfg.smoothing.windowSize ?? 5, cfg.smoothing.maxWindowSize ?? 15);
        }
      }
      
      const powerSeries = data.power.current.map((x, i) => ({ x, y: pY[i] }));
      
      if (displayConfig.showPI ?? true) {
        marks.push(
          Plot.line(powerSeries, { x: "x", y: "y", stroke: "blue", strokeWidth: 2 })
        );
        if (displayConfig.showMarkers ?? false) {
          marks.push(
            Plot.dot(powerSeries, { x: "x", y: "y", fill: "blue", r: 2 })
          );
        }
      }
    }

    // 处理电压数据
    if (data?.voltage && data.voltage.current.length && data.voltage.voltage.length) {
      const cfg = useAppStore.getState().processingConfig;
      let vY = data.voltage.voltage.slice();
      
      // 应用平滑
      if (cfg.smoothing?.enabled) {
        if (cfg.smoothing.method === 'moving-average') {
          vY = movingAverage(vY, cfg.smoothing.windowSize ?? 5);
        } else if (cfg.smoothing.method === 'sg') {
          vY = savitzkyGolay(vY, cfg.smoothing.windowSize ?? 5, cfg.smoothing.polynomialOrder ?? 3);
        } else if (cfg.smoothing.method === 'gaussian') {
          vY = gaussianSmooth(vY, cfg.smoothing.windowSize ?? 5, cfg.smoothing.sigma);
        } else if (cfg.smoothing.method === 'butterworth') {
          vY = butterworthLowpass(vY, cfg.smoothing.cutoff ?? 0.15, cfg.smoothing.order);
        } else if (cfg.smoothing.method === 'median') {
          vY = medianFilter(vY, cfg.smoothing.windowSize ?? 5);
        } else if (cfg.smoothing.method === 'adaptive') {
          vY = adaptiveSmooth(vY, cfg.smoothing.windowSize ?? 5, cfg.smoothing.maxWindowSize ?? 15);
        }
      }
      
      const voltageSeries = data.voltage.current.map((x, i) => ({ x, y: vY[i] }));
      
      if (displayConfig.showVI ?? true) {
        marks.push(
          Plot.line(voltageSeries, { x: "x", y: "y", stroke: "red", strokeWidth: 2 })
        );
        if (displayConfig.showMarkers ?? false) {
          marks.push(
            Plot.dot(voltageSeries, { x: "x", y: "y", fill: "red", r: 2 })
          );
        }
      }
    }

    const plot = Plot.plot({
      marks,
      width: containerRef.current.clientWidth,
      height: containerRef.current.clientHeight - 40, // 为工具栏留空间
      marginLeft: 60,
      marginBottom: 40,
      x: { label: "电流 (A)" },
      y: { label: "功率 (W) / 电压 (V)" },
      style: {
        background: "white",
        fontSize: "12px"
      }
    });

    containerRef.current.innerHTML = '';
    containerRef.current.appendChild(plot);
  }, [data, displayConfig, chartState.gridEnabled]);

  // 设置事件监听器
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    container.addEventListener('wheel', handleWheel, { passive: false });
    
    return () => {
      container.removeEventListener('wheel', handleWheel);
    };
  }, [handleWheel]);

  // 渲染图表
  useEffect(() => {
    if (!Plot) {
      import('@observablehq/plot').then(mod => { 
        Plot = mod; 
        render(); 
      });
      return;
    }
    render();
  }, [render]);

  return (
    <div className="w-full h-full flex flex-col">
      <ChartToolbar
        title="LIV特性曲线"
        container={containerRef.current}
        onReset={resetView}
        onZoomIn={zoomIn}
        onZoomOut={zoomOut}
        onTogglePan={togglePan}
        onToggleCrosshair={toggleCrosshair}
        onToggleGrid={toggleGrid}
        enablePan={chartState.panEnabled}
        enableCrosshair={chartState.crosshairEnabled}
        enableGrid={chartState.gridEnabled}
      />
      
      <div className="flex-1 relative">
        {/* 图表容器 */}
        <div
          ref={containerRef}
          className={`w-full h-full ${chartState.panEnabled ? 'cursor-grab active:cursor-grabbing' : 'cursor-crosshair'}`}
          style={{
            transform: `translate(${chartState.offsetX}px, ${chartState.offsetY}px) scale(${chartState.scale})`,
            transformOrigin: '0 0'
          }}
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseLeave}
        />
        
        {/* 十字线覆盖层 */}
        {chartState.crosshairEnabled && (
          <div
            ref={overlayRef}
            className="absolute inset-0 pointer-events-none"
          >
            {crosshair.visible && (
              <>
                <div
                  className="absolute w-full h-px bg-gray-400 opacity-50"
                  style={{ top: crosshair.y }}
                />
                <div
                  className="absolute h-full w-px bg-gray-400 opacity-50"
                  style={{ left: crosshair.x }}
                />
                <div
                  className="absolute bg-black text-white text-xs px-2 py-1 rounded pointer-events-none"
                  style={{
                    left: crosshair.x + 10,
                    top: crosshair.y - 30,
                    zIndex: 1000
                  }}
                >
                  ({crosshair.x.toFixed(0)}, {crosshair.y.toFixed(0)})
                </div>
              </>
            )}
          </div>
        )}
        
        {/* 缩放指示器 */}
        <div className="absolute bottom-2 left-2 bg-black/70 text-white text-xs px-2 py-1 rounded">
          {(chartState.scale * 100).toFixed(0)}%
        </div>
      </div>
    </div>
  );
}
