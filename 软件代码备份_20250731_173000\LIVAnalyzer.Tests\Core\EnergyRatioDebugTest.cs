using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Xunit;
using LIVAnalyzer.Core.Processors;
using LIVAnalyzer.Models;
using Xunit.Abstractions;

namespace LIVAnalyzer.Tests.Core
{
    /// <summary>
    /// 测试能量占比计算是否正确
    /// </summary>
    public class EnergyRatioDebugTest
    {
        private readonly ITestOutputHelper _output;

        public EnergyRatioDebugTest(ITestOutputHelper output)
        {
            _output = output;
        }

        [Fact]
        public void DebugEnergyRatioCalculation()
        {
            // 创建一个简单的测试数据集
            var angles = new List<double>();
            var intensities = new List<double>();
            
            // 创建一个简单的梯形分布
            // 从 -20° 到 +20°，中心区域（-10° 到 +10°）强度为1，外围为0
            for (double angle = -20; angle <= 20; angle += 0.5)
            {
                angles.Add(angle);
                if (Math.Abs(angle) <= 10)
                {
                    intensities.Add(1.0); // 中心区域
                }
                else
                {
                    intensities.Add(0.0); // 外围区域
                }
            }
            
            // 创建测试数据
            var angleData = new List<DataPoint>();
            var intensityData = new List<DataPoint>();
            for (int i = 0; i < angles.Count; i++)
            {
                angleData.Add(new DataPoint(angles[i], intensities[i]));
                intensityData.Add(new DataPoint(angles[i], intensities[i]));
            }
            
            // 使用DivergenceProcessor计算
            var processor = new DivergenceProcessor();
            var result = processor.CalculateDivergence(angleData, intensityData);
            
            _output.WriteLine($"测试梯形分布:");
            _output.WriteLine($"FW(1/e²) = {result.FW1e2:F2}°");
            _output.WriteLine($"FW(1/e²) 能量占比 = {result.FW1e2PowerContainment:P2}");
            
            // 对于梯形分布，FW86.5%应该接近全宽（20°），能量占比应该接近100%
            Assert.True(result.IsValid);
            Assert.True(Math.Abs(result.FW1e2 - 20) < 1, $"FW(1/e²)应该接近20°，实际为{result.FW1e2:F2}°");
            
            // 写入日志文件以便查看详细计算过程
            var logPath = Path.Combine(Path.GetTempPath(), "divergence_debug.log");
            _output.WriteLine($"详细日志已写入: {logPath}");
        }
        
        [Fact]
        public void TestRealGaussianBeamEnergyRatio()
        {
            // 创建真实的高斯光束数据
            double theta0 = 5.0; // 1/e²半角（度）
            var angles = new List<double>();
            var intensities = new List<double>();
            
            // 生成高分辨率数据
            for (double angle = -30; angle <= 30; angle += 0.1)
            {
                angles.Add(angle);
                // 高斯分布: I = I0 * exp(-2*(θ/θ0)²)
                double intensity = Math.Exp(-2 * Math.Pow(angle / theta0, 2));
                intensities.Add(intensity);
            }
            
            // 手动计算理论值
            double totalPower = 0;
            double powerWithin1e2 = 0;
            
            for (int i = 0; i < angles.Count - 1; i++)
            {
                double a1 = angles[i];
                double a2 = angles[i + 1];
                double i1 = intensities[i];
                double i2 = intensities[i + 1];
                double delta = a2 - a1;
                double avgI = (i1 + i2) / 2;
                double power = avgI * delta;
                
                totalPower += power;
                
                // 检查是否在±theta0范围内
                if (Math.Abs(a1) <= theta0 && Math.Abs(a2) <= theta0)
                {
                    powerWithin1e2 += power;
                }
                else if ((a1 < -theta0 && a2 > -theta0) || (a1 < theta0 && a2 > theta0))
                {
                    // 边界段需要特殊处理
                    double boundary = (a1 < 0) ? -theta0 : theta0;
                    double t = (boundary - a1) / (a2 - a1);
                    double iBoundary = i1 + t * (i2 - i1);
                    
                    if (Math.Abs(a1) <= theta0)
                    {
                        // a1在范围内，a2在范围外
                        double partialDelta = boundary - a1;
                        double partialAvg = (i1 + iBoundary) / 2;
                        powerWithin1e2 += partialAvg * partialDelta;
                    }
                    else
                    {
                        // a1在范围外，a2在范围内
                        double partialDelta = a2 - boundary;
                        double partialAvg = (iBoundary + i2) / 2;
                        powerWithin1e2 += partialAvg * partialDelta;
                    }
                }
            }
            
            double theoreticalRatio = powerWithin1e2 / totalPower;
            _output.WriteLine($"高斯光束理论计算:");
            _output.WriteLine($"1/e²半角 = {theta0}°");
            _output.WriteLine($"理论能量占比 = {theoreticalRatio:P3}");
            
            // 使用DivergenceProcessor计算
            var angleData = new List<DataPoint>();
            var intensityData = new List<DataPoint>();
            for (int i = 0; i < angles.Count; i++)
            {
                angleData.Add(new DataPoint(angles[i], intensities[i]));
                intensityData.Add(new DataPoint(angles[i], intensities[i]));
            }
            
            var processor = new DivergenceProcessor();
            var result = processor.CalculateDivergence(angleData, intensityData);
            
            _output.WriteLine($"\nDivergenceProcessor计算结果:");
            _output.WriteLine($"FW(1/e²) = {result.FW1e2:F2}°");
            _output.WriteLine($"FW(1/e²) 能量占比 = {result.FW1e2PowerContainment:P3}");
            
            // 验证结果
            Assert.True(result.IsValid);
            Assert.True(Math.Abs(result.FW1e2 - 2 * theta0) < 0.2, 
                $"FW(1/e²)应该接近2*theta0={2*theta0}°，实际为{result.FW1e2:F2}°");
            
            // 能量占比应该接近理论值
            Assert.True(Math.Abs(result.FW1e2PowerContainment - theoreticalRatio) < 0.05,
                $"能量占比应该接近{theoreticalRatio:P2}，实际为{result.FW1e2PowerContainment:P2}");
        }
    }
}