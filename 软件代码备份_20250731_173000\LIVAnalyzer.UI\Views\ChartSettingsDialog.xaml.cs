using System;
using System.Windows;

namespace LIVAnalyzer.UI.Views
{
    public partial class ChartSettingsDialog : Window
    {
        public ChartSettingsDialog()
        {
            InitializeComponent();
        }

        protected override void OnClosed(EventArgs e)
        {
            base.OnClosed(e);
            
            // 释放ViewModel资源
            if (DataContext is IDisposable disposableViewModel)
            {
                disposableViewModel.Dispose();
            }
        }
    }
}