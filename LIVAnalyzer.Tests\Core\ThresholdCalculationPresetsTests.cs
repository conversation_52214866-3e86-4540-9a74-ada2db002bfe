using Xunit;
using LIVAnalyzer.Core.Configuration;

namespace LIVAnalyzer.Tests.Core
{
    /// <summary>
    /// 阈值计算预设配置的单元测试
    /// </summary>
    public class ThresholdCalculationPresetsTests
    {
        [Fact]
        public void VCSEL_ShouldHaveCorrectConfiguration()
        {
            // Act
            var config = ThresholdCalculationPresets.VCSEL;
            
            // Assert
            Assert.Equal(0.4, config.SearchRangeRatio);
            Assert.Equal(0.25, config.MaxThresholdRatio);
            Assert.Equal(0.03, config.PrimaryFallbackPowerRatio);
            Assert.Equal(15, config.MaxSmoothingWindow);
            Assert.Equal(0.45, config.DerivativeRatio);
            
            // 验证配置有效性
            config.Validate();
        }
        
        [Fact]
        public void EdgeEmitting_ShouldHaveCorrectConfiguration()
        {
            // Act
            var config = ThresholdCalculationPresets.EdgeEmitting;
            
            // Assert
            Assert.Equal(0.6, config.SearchRangeRatio);
            Assert.Equal(0.4, config.MaxThresholdRatio);
            Assert.Equal(0.05, config.PrimaryFallbackPowerRatio);
            Assert.Equal(25, config.MaxSmoothingWindow);
            Assert.Equal(0.5, config.DerivativeRatio);
            
            // 验证配置有效性
            config.Validate();
        }
        
        [Fact]
        public void HighPower_ShouldHaveCorrectConfiguration()
        {
            // Act
            var config = ThresholdCalculationPresets.HighPower;
            
            // Assert
            Assert.Equal(0.8, config.SearchRangeRatio);
            Assert.Equal(0.6, config.MaxThresholdRatio);
            Assert.Equal(0.08, config.PrimaryFallbackPowerRatio);
            Assert.Equal(41, config.MaxSmoothingWindow);
            Assert.Equal(0.55, config.DerivativeRatio);
            
            // 验证配置有效性
            config.Validate();
        }
        
        [Fact]
        public void QuantumCascade_ShouldHaveCorrectConfiguration()
        {
            // Act
            var config = ThresholdCalculationPresets.QuantumCascade;
            
            // Assert
            Assert.Equal(0.9, config.SearchRangeRatio);
            Assert.Equal(0.7, config.MaxThresholdRatio);
            Assert.Equal(0.1, config.PrimaryFallbackPowerRatio);
            Assert.Equal(51, config.MaxSmoothingWindow);
            Assert.Equal(0.6, config.DerivativeRatio);
            
            // 验证配置有效性
            config.Validate();
        }
        
        [Fact]
        public void LowNoise_ShouldHaveCorrectConfiguration()
        {
            // Act
            var config = ThresholdCalculationPresets.LowNoise;
            
            // Assert
            Assert.Equal(0.5, config.SearchRangeRatio);
            Assert.Equal(0.3, config.MaxThresholdRatio);
            Assert.Equal(0.02, config.PrimaryFallbackPowerRatio);
            Assert.Equal(9, config.MaxSmoothingWindow);
            Assert.Equal(0.4, config.DerivativeRatio);
            
            // 验证配置有效性
            config.Validate();
        }
        
        [Fact]
        public void HighNoise_ShouldHaveCorrectConfiguration()
        {
            // Act
            var config = ThresholdCalculationPresets.HighNoise;
            
            // Assert
            Assert.Equal(0.6, config.SearchRangeRatio);
            Assert.Equal(0.5, config.MaxThresholdRatio);
            Assert.Equal(0.1, config.PrimaryFallbackPowerRatio);
            Assert.Equal(61, config.MaxSmoothingWindow);
            Assert.Equal(0.6, config.DerivativeRatio);
            
            // 验证配置有效性
            config.Validate();
        }
        
        [Theory]
        [InlineData("vcsel", 0.4)]
        [InlineData("VCSEL", 0.4)]
        [InlineData("edge-emitting", 0.6)]
        [InlineData("edge_emitting", 0.6)]
        [InlineData("edgeemitting", 0.6)]
        [InlineData("high-power", 0.8)]
        [InlineData("high_power", 0.8)]
        [InlineData("highpower", 0.8)]
        [InlineData("quantum-cascade", 0.9)]
        [InlineData("quantum_cascade", 0.9)]
        [InlineData("quantumcascade", 0.9)]
        [InlineData("qcl", 0.9)]
        [InlineData("low-noise", 0.5)]
        [InlineData("low_noise", 0.5)]
        [InlineData("lownoise", 0.5)]
        [InlineData("high-noise", 0.6)]
        [InlineData("high_noise", 0.6)]
        [InlineData("highnoise", 0.6)]
        public void GetRecommendedConfig_WithKnownTypes_ShouldReturnCorrectConfig(string laserType, double expectedSearchRange)
        {
            // Act
            var config = ThresholdCalculationPresets.GetRecommendedConfig(laserType);
            
            // Assert
            Assert.Equal(expectedSearchRange, config.SearchRangeRatio);
            config.Validate();
        }
        
        [Theory]
        [InlineData("unknown")]
        [InlineData("")]
        [InlineData(null)]
        [InlineData("random-type")]
        public void GetRecommendedConfig_WithUnknownTypes_ShouldReturnEdgeEmittingConfig(string laserType)
        {
            // Act
            var config = ThresholdCalculationPresets.GetRecommendedConfig(laserType);
            var edgeEmittingConfig = ThresholdCalculationPresets.EdgeEmitting;
            
            // Assert
            Assert.Equal(edgeEmittingConfig.SearchRangeRatio, config.SearchRangeRatio);
            Assert.Equal(edgeEmittingConfig.MaxThresholdRatio, config.MaxThresholdRatio);
            Assert.Equal(edgeEmittingConfig.PrimaryFallbackPowerRatio, config.PrimaryFallbackPowerRatio);
            config.Validate();
        }
        
        [Fact]
        public void AllPresets_ShouldHaveValidConfigurations()
        {
            // Arrange
            var presets = new[]
            {
                ThresholdCalculationPresets.VCSEL,
                ThresholdCalculationPresets.EdgeEmitting,
                ThresholdCalculationPresets.HighPower,
                ThresholdCalculationPresets.QuantumCascade,
                ThresholdCalculationPresets.LowNoise,
                ThresholdCalculationPresets.HighNoise
            };
            
            // Act & Assert
            foreach (var preset in presets)
            {
                preset.Validate(); // Should not throw
            }
        }
        
        [Fact]
        public void VCSEL_ShouldBeOptimizedForLowThreshold()
        {
            // Act
            var vcsel = ThresholdCalculationPresets.VCSEL;
            var edgeEmitting = ThresholdCalculationPresets.EdgeEmitting;
            
            // Assert - VCSEL应该有更小的搜索范围和更严格的约束
            Assert.True(vcsel.SearchRangeRatio < edgeEmitting.SearchRangeRatio);
            Assert.True(vcsel.MaxThresholdRatio < edgeEmitting.MaxThresholdRatio);
            Assert.True(vcsel.PrimaryFallbackPowerRatio < edgeEmitting.PrimaryFallbackPowerRatio);
            Assert.True(vcsel.MaxSmoothingWindow < edgeEmitting.MaxSmoothingWindow);
        }
        
        [Fact]
        public void HighPower_ShouldBeOptimizedForHighThreshold()
        {
            // Act
            var highPower = ThresholdCalculationPresets.HighPower;
            var edgeEmitting = ThresholdCalculationPresets.EdgeEmitting;
            
            // Assert - 高功率激光器应该有更大的搜索范围和更宽松的约束
            Assert.True(highPower.SearchRangeRatio > edgeEmitting.SearchRangeRatio);
            Assert.True(highPower.MaxThresholdRatio > edgeEmitting.MaxThresholdRatio);
            Assert.True(highPower.PrimaryFallbackPowerRatio > edgeEmitting.PrimaryFallbackPowerRatio);
            Assert.True(highPower.MaxSmoothingWindow > edgeEmitting.MaxSmoothingWindow);
        }
    }
}
