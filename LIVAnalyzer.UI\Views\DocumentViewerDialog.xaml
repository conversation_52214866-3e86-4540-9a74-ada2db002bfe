<Window x:Class="LIVAnalyzer.UI.Views.DocumentViewerDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:converters="clr-namespace:LIVAnalyzer.UI.Converters"
        mc:Ignorable="d"
        Title="{Binding WindowTitle}"
        Height="700" Width="900"
        MinHeight="500" MinWidth="700"
        WindowStartupLocation="CenterOwner"
        Icon="pack://application:,,,/Resources/Icons/app-icon-32.png">

    <Window.Resources>
        <!-- 转换器 -->
        <converters:SimpleMarkdownConverter x:Key="MarkdownConverter"/>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- 标题栏 -->
        <Border Grid.Row="0" Background="{DynamicResource AppControlBackgroundBrush}"
                BorderBrush="{DynamicResource AppBorderBrush}"
                BorderThickness="0,0,0,1"
                Padding="20,10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0" Text="{Binding DocumentTitle}" 
                           FontSize="16" FontWeight="SemiBold"
                           Foreground="{DynamicResource AppForegroundBrush}"
                           VerticalAlignment="Center"/>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Command="{Binding CopyToClipboardCommand}" 
                            Style="{DynamicResource WhiteButton}" 
                            Margin="5,0">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="📋 " FontFamily="Segoe UI Emoji"/>
                            <TextBlock Text="复制"/>
                        </StackPanel>
                    </Button>
                    <Button Command="{Binding SaveToFileCommand}" 
                            Style="{DynamicResource WhiteButton}" 
                            Margin="5,0">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="💾 " FontFamily="Segoe UI Emoji"/>
                            <TextBlock Text="保存"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>
        
        <!-- 文档内容 - Markdown渲染 -->
        <FlowDocumentScrollViewer Grid.Row="1"
                                  Background="{DynamicResource AppBackgroundBrush}"
                                  Foreground="{DynamicResource AppForegroundBrush}"
                                  Padding="20"
                                  VerticalScrollBarVisibility="Auto"
                                  HorizontalScrollBarVisibility="Auto"
                                  Document="{Binding DocumentContent, Converter={StaticResource MarkdownConverter}}"
                                  IsToolBarVisible="False"
                                  Zoom="100"
                                  x:Name="MarkdownViewer"/>
        
        <!-- 底部按钮 -->
        <Border Grid.Row="2" Background="{DynamicResource AppControlBackgroundBrush}" 
                BorderBrush="{DynamicResource AppBorderBrush}"
                BorderThickness="0,1,0,0"
                Padding="20,10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0" Text="{Binding StatusText}" 
                           VerticalAlignment="Center" 
                           FontSize="12"
                           Foreground="{DynamicResource AppForegroundBrush}"
                           Opacity="0.7"/>
                
                <Button Grid.Column="1" Content="关闭" 
                        Style="{DynamicResource WhiteButton}"
                        Command="{Binding CloseCommand}"/>
            </Grid>
        </Border>
    </Grid>
</Window>