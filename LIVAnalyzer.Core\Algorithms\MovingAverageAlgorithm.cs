using System;
using System.Linq;
using LIVAnalyzer.Models;

namespace LIVAnalyzer.Core.Algorithms
{
    /// <summary>
    /// 移动平均平滑算法
    /// </summary>
    public class MovingAverageAlgorithm : ISmoothingAlgorithm
    {
        public string Name => "移动平均";
        
        public string Description => "使用移动窗口计算平均值进行数据平滑，适合一般噪声过滤";
        
        public SmoothingAlgorithmType AlgorithmType => SmoothingAlgorithmType.MovingAverage;
        
        public bool SupportsRealTimePreview => true;
        
        public double[] Smooth(double[] xData, double[] yData, SmoothingConfig config)
        {
            if (xData == null || yData == null)
                throw new ArgumentNullException("输入数据不能为空");
            
            if (xData.Length != yData.Length)
                throw new ArgumentException("X和Y数据长度不匹配");
            
            if (xData.Length == 0)
                return Array.Empty<double>();
            
            int windowSize = config.DefaultWindowSize;
            
            // 确保窗口大小为奇数
            if (windowSize % 2 == 0)
                windowSize++;
            
            // 验证窗口大小
            windowSize = Math.Max(config.MinWindowSize, Math.Min(config.MaxWindowSize, windowSize));
            
            return MovingAverage(yData, windowSize);
        }
        
        public string? ValidateParameters(SmoothingConfig config, int dataLength)
        {
            if (config.DefaultWindowSize < config.MinWindowSize)
                return $"窗口大小不能小于 {config.MinWindowSize}";
                
            if (config.DefaultWindowSize > config.MaxWindowSize)
                return $"窗口大小不能大于 {config.MaxWindowSize}";
                
            if (config.DefaultWindowSize >= dataLength)
                return "窗口大小不能大于等于数据长度";
            
            return null;
        }
        
        public SmoothingConfig GetRecommendedParameters(int dataLength, double noiseLevel = 0.1)
        {
            // 根据数据长度和噪声水平推荐窗口大小
            int recommendedWindowSize = Math.Max(3, Math.Min(
                (int)(dataLength * 0.05), // 不超过数据长度的5%
                (int)(5 + noiseLevel * 10) // 噪声越大，窗口越大
            ));
            
            // 确保为奇数
            if (recommendedWindowSize % 2 == 0)
                recommendedWindowSize++;
            
            return new SmoothingConfig
            {
                AlgorithmType = SmoothingAlgorithmType.MovingAverage,
                DefaultWindowSize = recommendedWindowSize,
                EnableByDefault = noiseLevel > 0.05 // 噪声较大时默认启用
            };
        }
        
        /// <summary>
        /// 移动平均算法实现
        /// </summary>
        /// <param name="data">输入数据</param>
        /// <param name="windowSize">窗口大小</param>
        /// <returns>平滑后的数据</returns>
        private static double[] MovingAverage(double[] data, int windowSize)
        {
            if (data == null || data.Length == 0)
                return Array.Empty<double>();
            
            var result = new double[data.Length];
            int halfWindow = windowSize / 2;
            
            for (int i = 0; i < data.Length; i++)
            {
                int start = Math.Max(0, i - halfWindow);
                int end = Math.Min(data.Length - 1, i + halfWindow);
                
                double sum = 0;
                int count = 0;
                
                for (int j = start; j <= end; j++)
                {
                    if (!double.IsNaN(data[j]) && !double.IsInfinity(data[j]))
                    {
                        sum += data[j];
                        count++;
                    }
                }
                
                result[i] = count > 0 ? sum / count : data[i];
            }
            
            return result;
        }
    }
}