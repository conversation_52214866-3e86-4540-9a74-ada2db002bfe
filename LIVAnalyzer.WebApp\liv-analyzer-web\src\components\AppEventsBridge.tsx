import { useEffect } from "react";
import { useAppStore } from "../state/store";
import { ComputeWorkerClient } from "../services/computeClient";
import { exportResultsSummary } from "../services/resultsExport";

export default function AppEventsBridge() {
  const { data, processingConfig, setResults, setError, setProcessing } = useAppStore();

  useEffect(() => {
    const computeClient = new ComputeWorkerClient();

    const onAnalyze = async () => {
      if (!data) { setError('暂无数据，请先加载文件'); return; }
      try {
        setProcessing(true);
        const result = await computeClient.compute(data, processingConfig);
        setResults(result);
      } catch (e) {
        setError((e as Error).message);
      } finally {
        setProcessing(false);
      }
    };

    const onExportResults = () => {
      try {
        exportResultsSummary();
      } catch (e) {
        setError((e as Error).message);
      }
    };

    window.addEventListener('analyze-now', onAnalyze as EventListener);
    window.addEventListener('export-results', onExportResults as EventListener);
    return () => {
      window.removeEventListener('analyze-now', onAnalyze as EventListener);
      window.removeEventListener('export-results', onExportResults as EventListener);
    };
  }, [data, processingConfig, setResults, setError, setProcessing]);

  return null;
}


