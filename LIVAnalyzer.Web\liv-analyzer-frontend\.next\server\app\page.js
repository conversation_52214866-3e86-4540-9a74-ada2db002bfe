/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=E%3A%5C01LaserPackage%5Csoftware%5CLIV_Analyzer%5CCSharp_Version%5CLIVAnalyzer.Web%5Cliv-analyzer-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5C01LaserPackage%5Csoftware%5CLIV_Analyzer%5CCSharp_Version%5CLIVAnalyzer.Web%5Cliv-analyzer-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=E%3A%5C01LaserPackage%5Csoftware%5CLIV_Analyzer%5CCSharp_Version%5CLIVAnalyzer.Web%5Cliv-analyzer-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5C01LaserPackage%5Csoftware%5CLIV_Analyzer%5CCSharp_Version%5CLIVAnalyzer.Web%5Cliv-analyzer-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=E%3A%5C01LaserPackage%5Csoftware%5CLIV_Analyzer%5CCSharp_Version%5CLIVAnalyzer.Web%5Cliv-analyzer-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5C01LaserPackage%5Csoftware%5CLIV_Analyzer%5CCSharp_Version%5CLIVAnalyzer.Web%5Cliv-analyzer-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C01LaserPackage%5C%5Csoftware%5C%5CLIV_Analyzer%5C%5CCSharp_Version%5C%5CLIVAnalyzer.Web%5C%5Cliv-analyzer-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C01LaserPackage%5C%5Csoftware%5C%5CLIV_Analyzer%5C%5CCSharp_Version%5C%5CLIVAnalyzer.Web%5C%5Cliv-analyzer-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C01LaserPackage%5C%5Csoftware%5C%5CLIV_Analyzer%5C%5CCSharp_Version%5C%5CLIVAnalyzer.Web%5C%5Cliv-analyzer-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C01LaserPackage%5C%5Csoftware%5C%5CLIV_Analyzer%5C%5CCSharp_Version%5C%5CLIVAnalyzer.Web%5C%5Cliv-analyzer-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C01LaserPackage%5C%5Csoftware%5C%5CLIV_Analyzer%5C%5CCSharp_Version%5C%5CLIVAnalyzer.Web%5C%5Cliv-analyzer-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C01LaserPackage%5C%5Csoftware%5C%5CLIV_Analyzer%5C%5CCSharp_Version%5C%5CLIVAnalyzer.Web%5C%5Cliv-analyzer-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C01LaserPackage%5C%5Csoftware%5C%5CLIV_Analyzer%5C%5CCSharp_Version%5C%5CLIVAnalyzer.Web%5C%5Cliv-analyzer-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C01LaserPackage%5C%5Csoftware%5C%5CLIV_Analyzer%5C%5CCSharp_Version%5C%5CLIVAnalyzer.Web%5C%5Cliv-analyzer-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C01LaserPackage%5C%5Csoftware%5C%5CLIV_Analyzer%5C%5CCSharp_Version%5C%5CLIVAnalyzer.Web%5C%5Cliv-analyzer-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C01LaserPackage%5C%5Csoftware%5C%5CLIV_Analyzer%5C%5CCSharp_Version%5C%5CLIVAnalyzer.Web%5C%5Cliv-analyzer-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C01LaserPackage%5C%5Csoftware%5C%5CLIV_Analyzer%5C%5CCSharp_Version%5C%5CLIVAnalyzer.Web%5C%5Cliv-analyzer-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C01LaserPackage%5C%5Csoftware%5C%5CLIV_Analyzer%5C%5CCSharp_Version%5C%5CLIVAnalyzer.Web%5C%5Cliv-analyzer-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C01LaserPackage%5C%5Csoftware%5C%5CLIV_Analyzer%5C%5CCSharp_Version%5C%5CLIVAnalyzer.Web%5C%5Cliv-analyzer-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C01LaserPackage%5C%5Csoftware%5C%5CLIV_Analyzer%5C%5CCSharp_Version%5C%5CLIVAnalyzer.Web%5C%5Cliv-analyzer-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C01LaserPackage%5C%5Csoftware%5C%5CLIV_Analyzer%5C%5CCSharp_Version%5C%5CLIVAnalyzer.Web%5C%5Cliv-analyzer-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C01LaserPackage%5C%5Csoftware%5C%5CLIV_Analyzer%5C%5CCSharp_Version%5C%5CLIVAnalyzer.Web%5C%5Cliv-analyzer-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C01LaserPackage%5C%5Csoftware%5C%5CLIV_Analyzer%5C%5CCSharp_Version%5C%5CLIVAnalyzer.Web%5C%5Cliv-analyzer-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C01LaserPackage%5C%5Csoftware%5C%5CLIV_Analyzer%5C%5CCSharp_Version%5C%5CLIVAnalyzer.Web%5C%5Cliv-analyzer-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C01LaserPackage%5C%5Csoftware%5C%5CLIV_Analyzer%5C%5CCSharp_Version%5C%5CLIVAnalyzer.Web%5C%5Cliv-analyzer-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C01LaserPackage%5C%5Csoftware%5C%5CLIV_Analyzer%5C%5CCSharp_Version%5C%5CLIVAnalyzer.Web%5C%5Cliv-analyzer-frontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C01LaserPackage%5C%5Csoftware%5C%5CLIV_Analyzer%5C%5CCSharp_Version%5C%5CLIVAnalyzer.Web%5C%5Cliv-analyzer-frontend%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C01LaserPackage%5C%5Csoftware%5C%5CLIV_Analyzer%5C%5CCSharp_Version%5C%5CLIVAnalyzer.Web%5C%5Cliv-analyzer-frontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ctoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C01LaserPackage%5C%5Csoftware%5C%5CLIV_Analyzer%5C%5CCSharp_Version%5C%5CLIVAnalyzer.Web%5C%5Cliv-analyzer-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C01LaserPackage%5C%5Csoftware%5C%5CLIV_Analyzer%5C%5CCSharp_Version%5C%5CLIVAnalyzer.Web%5C%5Cliv-analyzer-frontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C01LaserPackage%5C%5Csoftware%5C%5CLIV_Analyzer%5C%5CCSharp_Version%5C%5CLIVAnalyzer.Web%5C%5Cliv-analyzer-frontend%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C01LaserPackage%5C%5Csoftware%5C%5CLIV_Analyzer%5C%5CCSharp_Version%5C%5CLIVAnalyzer.Web%5C%5Cliv-analyzer-frontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ctoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers.tsx */ \"(ssr)/./src/components/providers.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/toaster.tsx */ \"(ssr)/./src/components/ui/toaster.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C01LaserPackage%5C%5Csoftware%5C%5CLIV_Analyzer%5C%5CCSharp_Version%5C%5CLIVAnalyzer.Web%5C%5Cliv-analyzer-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C01LaserPackage%5C%5Csoftware%5C%5CLIV_Analyzer%5C%5CCSharp_Version%5C%5CLIVAnalyzer.Web%5C%5Cliv-analyzer-frontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C01LaserPackage%5C%5Csoftware%5C%5CLIV_Analyzer%5C%5CCSharp_Version%5C%5CLIVAnalyzer.Web%5C%5Cliv-analyzer-frontend%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C01LaserPackage%5C%5Csoftware%5C%5CLIV_Analyzer%5C%5CCSharp_Version%5C%5CLIVAnalyzer.Web%5C%5Cliv-analyzer-frontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ctoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C01LaserPackage%5C%5Csoftware%5C%5CLIV_Analyzer%5C%5CCSharp_Version%5C%5CLIVAnalyzer.Web%5C%5Cliv-analyzer-frontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C01LaserPackage%5C%5Csoftware%5C%5CLIV_Analyzer%5C%5CCSharp_Version%5C%5CLIVAnalyzer.Web%5C%5Cliv-analyzer-frontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUMwMUxhc2VyUGFja2FnZSU1QyU1Q3NvZnR3YXJlJTVDJTVDTElWX0FuYWx5emVyJTVDJTVDQ1NoYXJwX1ZlcnNpb24lNUMlNUNMSVZBbmFseXplci5XZWIlNUMlNUNsaXYtYW5hbHl6ZXItZnJvbnRlbmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0pBQTJKIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbGl2LWFuYWx5emVyLWZyb250ZW5kLz9iOTY0Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRTpcXFxcMDFMYXNlclBhY2thZ2VcXFxcc29mdHdhcmVcXFxcTElWX0FuYWx5emVyXFxcXENTaGFycF9WZXJzaW9uXFxcXExJVkFuYWx5emVyLldlYlxcXFxsaXYtYW5hbHl6ZXItZnJvbnRlbmRcXFxcc3JjXFxcXGFwcFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C01LaserPackage%5C%5Csoftware%5C%5CLIV_Analyzer%5C%5CCSharp_Version%5C%5CLIVAnalyzer.Web%5C%5Cliv-analyzer-frontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_desktop_like_interface__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/desktop-like-interface */ \"(ssr)/./src/components/desktop-like-interface.tsx\");\n/* harmony import */ var _components_login__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/login */ \"(ssr)/./src/components/login.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/auth-context */ \"(ssr)/./src/contexts/auth-context.tsx\");\n/* harmony import */ var _barrel_optimize_names_LogOut_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=LogOut,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_LogOut_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=LogOut,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction MainApp() {\n    const { user, isAuthenticated, logout } = (0,_contexts_auth_context__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const [showLogin, setShowLogin] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(!isAuthenticated);\n    const handleLoginSuccess = ()=>{\n        setShowLogin(false);\n    };\n    const handleLogout = ()=>{\n        logout();\n        setShowLogin(true);\n    };\n    if (showLogin || !isAuthenticated) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_login__WEBPACK_IMPORTED_MODULE_3__.LoginComponent, {\n            onLoginSuccess: handleLoginSuccess\n        }, void 0, false, {\n            fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 25,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b px-4 py-2 flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-base font-normal\",\n                            children: \"LIV曲线分析工具 V2.0 (C#版本)\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 text-sm text-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogOut_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 42,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: user.username\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 43,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 41,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: handleLogout,\n                                className: \"text-xs\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogOut_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-3 w-3 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 53,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"退出\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 47,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-50 border-b px-4 py-1\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex space-x-6 text-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"hover:bg-gray-200 px-2 py-1 cursor-pointer\",\n                            children: \"文件\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"hover:bg-gray-200 px-2 py-1 cursor-pointer\",\n                            children: \"视图\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 63,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"hover:bg-gray-200 px-2 py-1 cursor-pointer\",\n                            children: \"帮助\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_desktop_like_interface__WEBPACK_IMPORTED_MODULE_2__.DesktopLikeInterface, {}, void 0, false, {\n                fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 69,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_auth_context__WEBPACK_IMPORTED_MODULE_5__.AuthProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MainApp, {}, void 0, false, {\n            fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 77,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 76,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/charts/divergence-chart.tsx":
/*!****************************************************!*\
  !*** ./src/components/charts/divergence-chart.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DivergenceChart: () => (/* binding */ DivergenceChart)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ DivergenceChart auto */ \n\nfunction DivergenceChart({ data, showGrid = true, showLegend = false }) {\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const canvas = canvasRef.current;\n        if (!canvas) return;\n        const ctx = canvas.getContext(\"2d\");\n        if (!ctx) return;\n        // 设置画布大小\n        const rect = canvas.getBoundingClientRect();\n        canvas.width = rect.width * window.devicePixelRatio;\n        canvas.height = rect.height * window.devicePixelRatio;\n        ctx.scale(window.devicePixelRatio, window.devicePixelRatio);\n        // 清空画布\n        ctx.clearRect(0, 0, rect.width, rect.height);\n        ctx.fillStyle = \"white\";\n        ctx.fillRect(0, 0, rect.width, rect.height);\n        // 图表边距\n        const margin = {\n            top: 40,\n            right: 60,\n            bottom: 60,\n            left: 80\n        };\n        const chartWidth = rect.width - margin.left - margin.right;\n        const chartHeight = rect.height - margin.top - margin.bottom;\n        // 计算布局 - 上下分割\n        const hffChartHeight = (chartHeight - 20) / 2;\n        const vffChartHeight = (chartHeight - 20) / 2;\n        const hffTopY = margin.top;\n        const vffTopY = margin.top + hffChartHeight + 40;\n        // 如果没有数据，显示提示信息\n        if (!data.hffData && !data.vffData) {\n            ctx.fillStyle = \"#666\";\n            ctx.font = \"14px Arial\";\n            ctx.textAlign = \"center\";\n            ctx.fillText(\"水平发散角 (HFF)\", margin.left + chartWidth / 2, hffTopY + hffChartHeight / 2 - 40);\n            ctx.fillText(\"无数据\", margin.left + chartWidth / 2, hffTopY + hffChartHeight / 2 - 20);\n            ctx.fillText(\"垂直发散角 (VFF)\", margin.left + chartWidth / 2, vffTopY + vffChartHeight / 2 - 40);\n            ctx.fillText(\"无数据\", margin.left + chartWidth / 2, vffTopY + vffChartHeight / 2 - 20);\n            return;\n        }\n        // 绘制HFF图表\n        if (data.hffData && data.hffData.length > 0) {\n            const hffData = data.hffData;\n            const minAngle = Math.min(...hffData.map((d)=>d.angle));\n            const maxAngle = Math.max(...hffData.map((d)=>d.angle));\n            const maxIntensity = Math.max(...hffData.map((d)=>d.intensity));\n            // 坐标转换函数\n            const xScale = (angle)=>margin.left + (angle - minAngle) / (maxAngle - minAngle) * chartWidth;\n            const yScale = (intensity)=>hffTopY + hffChartHeight - intensity / maxIntensity * hffChartHeight;\n            // 绘制网格\n            if (showGrid) {\n                ctx.strokeStyle = \"#e0e0e0\";\n                ctx.lineWidth = 1;\n                // 垂直网格线\n                for(let i = 0; i <= 10; i++){\n                    const x = margin.left + i / 10 * chartWidth;\n                    ctx.beginPath();\n                    ctx.moveTo(x, hffTopY);\n                    ctx.lineTo(x, hffTopY + hffChartHeight);\n                    ctx.stroke();\n                }\n                // 水平网格线\n                for(let i = 0; i <= 5; i++){\n                    const y = hffTopY + i / 5 * hffChartHeight;\n                    ctx.beginPath();\n                    ctx.moveTo(margin.left, y);\n                    ctx.lineTo(margin.left + chartWidth, y);\n                    ctx.stroke();\n                }\n            }\n            // 绘制坐标轴\n            ctx.strokeStyle = \"#000\";\n            ctx.lineWidth = 1;\n            // X轴\n            ctx.beginPath();\n            ctx.moveTo(margin.left, hffTopY + hffChartHeight);\n            ctx.lineTo(margin.left + chartWidth, hffTopY + hffChartHeight);\n            ctx.stroke();\n            // Y轴\n            ctx.beginPath();\n            ctx.moveTo(margin.left, hffTopY);\n            ctx.lineTo(margin.left, hffTopY + hffChartHeight);\n            ctx.stroke();\n            // 绘制HFF曲线 (红色)\n            ctx.strokeStyle = \"#ff0000\";\n            ctx.lineWidth = 2;\n            ctx.beginPath();\n            hffData.forEach((point, index)=>{\n                const x = xScale(point.angle);\n                const y = yScale(point.intensity);\n                if (index === 0) {\n                    ctx.moveTo(x, y);\n                } else {\n                    ctx.lineTo(x, y);\n                }\n            });\n            ctx.stroke();\n            // 绘制标题\n            ctx.fillStyle = \"#000\";\n            ctx.font = \"bold 12px Arial\";\n            ctx.textAlign = \"center\";\n            ctx.fillText(\"水平发散角 (HFF)\", margin.left + chartWidth / 2, hffTopY - 10);\n        }\n        // 绘制VFF图表\n        if (data.vffData && data.vffData.length > 0) {\n            const vffData = data.vffData;\n            const minAngle = Math.min(...vffData.map((d)=>d.angle));\n            const maxAngle = Math.max(...vffData.map((d)=>d.angle));\n            const maxIntensity = Math.max(...vffData.map((d)=>d.intensity));\n            // 坐标转换函数\n            const xScale = (angle)=>margin.left + (angle - minAngle) / (maxAngle - minAngle) * chartWidth;\n            const yScale = (intensity)=>vffTopY + vffChartHeight - intensity / maxIntensity * vffChartHeight;\n            // 绘制网格\n            if (showGrid) {\n                ctx.strokeStyle = \"#e0e0e0\";\n                ctx.lineWidth = 1;\n                // 垂直网格线\n                for(let i = 0; i <= 10; i++){\n                    const x = margin.left + i / 10 * chartWidth;\n                    ctx.beginPath();\n                    ctx.moveTo(x, vffTopY);\n                    ctx.lineTo(x, vffTopY + vffChartHeight);\n                    ctx.stroke();\n                }\n                // 水平网格线\n                for(let i = 0; i <= 5; i++){\n                    const y = vffTopY + i / 5 * vffChartHeight;\n                    ctx.beginPath();\n                    ctx.moveTo(margin.left, y);\n                    ctx.lineTo(margin.left + chartWidth, y);\n                    ctx.stroke();\n                }\n            }\n            // 绘制坐标轴\n            ctx.strokeStyle = \"#000\";\n            ctx.lineWidth = 1;\n            // X轴\n            ctx.beginPath();\n            ctx.moveTo(margin.left, vffTopY + vffChartHeight);\n            ctx.lineTo(margin.left + chartWidth, vffTopY + vffChartHeight);\n            ctx.stroke();\n            // Y轴\n            ctx.beginPath();\n            ctx.moveTo(margin.left, vffTopY);\n            ctx.lineTo(margin.left, vffTopY + vffChartHeight);\n            ctx.stroke();\n            // 绘制VFF曲线 (红色)\n            ctx.strokeStyle = \"#ff0000\";\n            ctx.lineWidth = 2;\n            ctx.beginPath();\n            vffData.forEach((point, index)=>{\n                const x = xScale(point.angle);\n                const y = yScale(point.intensity);\n                if (index === 0) {\n                    ctx.moveTo(x, y);\n                } else {\n                    ctx.lineTo(x, y);\n                }\n            });\n            ctx.stroke();\n            // 绘制标题\n            ctx.fillStyle = \"#000\";\n            ctx.font = \"bold 12px Arial\";\n            ctx.textAlign = \"center\";\n            ctx.fillText(\"垂直发散角 (VFF)\", margin.left + chartWidth / 2, vffTopY - 10);\n        }\n        // 绘制X轴标签\n        ctx.fillStyle = \"#000\";\n        ctx.font = \"12px Arial\";\n        ctx.textAlign = \"center\";\n        ctx.fillText(\"角度 (\\xb0)\", margin.left + chartWidth / 2, rect.height - 10);\n        // 绘制Y轴标签\n        ctx.save();\n        ctx.translate(20, margin.top + chartHeight / 2);\n        ctx.rotate(-Math.PI / 2);\n        ctx.fillText(\"强度\", 0, 0);\n        ctx.restore();\n    }, [\n        data,\n        showGrid,\n        showLegend\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n            ref: canvasRef,\n            className: \"w-full h-full\",\n            style: {\n                width: \"100%\",\n                height: \"100%\"\n            }\n        }, void 0, false, {\n            fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\charts\\\\divergence-chart.tsx\",\n            lineNumber: 228,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\charts\\\\divergence-chart.tsx\",\n        lineNumber: 227,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/charts/divergence-chart.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/charts/efficiency-chart.tsx":
/*!****************************************************!*\
  !*** ./src/components/charts/efficiency-chart.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EfficiencyChart: () => (/* binding */ EfficiencyChart)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ EfficiencyChart auto */ \n\nfunction EfficiencyChart({ data, showGrid = true, showLegend = false }) {\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const canvas = canvasRef.current;\n        if (!canvas || !data.powerData || data.powerData.length === 0) return;\n        const ctx = canvas.getContext(\"2d\");\n        if (!ctx) return;\n        // 设置画布大小\n        const rect = canvas.getBoundingClientRect();\n        canvas.width = rect.width * window.devicePixelRatio;\n        canvas.height = rect.height * window.devicePixelRatio;\n        ctx.scale(window.devicePixelRatio, window.devicePixelRatio);\n        // 清空画布\n        ctx.clearRect(0, 0, rect.width, rect.height);\n        ctx.fillStyle = \"white\";\n        ctx.fillRect(0, 0, rect.width, rect.height);\n        // 计算效率数据 (效率 = 功率 / (电流 * 假设的电压，这里简化处理)\n        const efficiencyData = data.powerData.map((point)=>({\n                current: point.current,\n                efficiency: point.current > 0 ? point.power / point.current * 100 : 0 // 简化的效率计算\n            })).filter((point)=>point.current > 0);\n        if (efficiencyData.length === 0) return;\n        // 图表边距\n        const margin = {\n            top: 40,\n            right: 60,\n            bottom: 60,\n            left: 80\n        };\n        const chartWidth = rect.width - margin.left - margin.right;\n        const chartHeight = rect.height - margin.top - margin.bottom;\n        // 数据范围\n        const maxCurrent = Math.max(...efficiencyData.map((d)=>d.current));\n        const maxEfficiency = Math.max(...efficiencyData.map((d)=>d.efficiency));\n        // 坐标转换函数\n        const xScale = (current)=>margin.left + current / maxCurrent * chartWidth;\n        const yScale = (efficiency)=>margin.top + chartHeight - efficiency / maxEfficiency * chartHeight;\n        // 绘制网格\n        if (showGrid) {\n            ctx.strokeStyle = \"#e0e0e0\";\n            ctx.lineWidth = 1;\n            // 垂直网格线\n            for(let i = 0; i <= 10; i++){\n                const x = margin.left + i / 10 * chartWidth;\n                ctx.beginPath();\n                ctx.moveTo(x, margin.top);\n                ctx.lineTo(x, margin.top + chartHeight);\n                ctx.stroke();\n            }\n            // 水平网格线\n            for(let i = 0; i <= 10; i++){\n                const y = margin.top + i / 10 * chartHeight;\n                ctx.beginPath();\n                ctx.moveTo(margin.left, y);\n                ctx.lineTo(margin.left + chartWidth, y);\n                ctx.stroke();\n            }\n        }\n        // 绘制坐标轴\n        ctx.strokeStyle = \"#000\";\n        ctx.lineWidth = 1;\n        // X轴\n        ctx.beginPath();\n        ctx.moveTo(margin.left, margin.top + chartHeight);\n        ctx.lineTo(margin.left + chartWidth, margin.top + chartHeight);\n        ctx.stroke();\n        // Y轴\n        ctx.beginPath();\n        ctx.moveTo(margin.left, margin.top);\n        ctx.lineTo(margin.left, margin.top + chartHeight);\n        ctx.stroke();\n        // 绘制效率曲线 (绿色)\n        ctx.strokeStyle = \"#00aa00\";\n        ctx.lineWidth = 2;\n        ctx.beginPath();\n        efficiencyData.forEach((point, index)=>{\n            const x = xScale(point.current);\n            const y = yScale(point.efficiency);\n            if (index === 0) {\n                ctx.moveTo(x, y);\n            } else {\n                ctx.lineTo(x, y);\n            }\n        });\n        ctx.stroke();\n        // 绘制坐标轴标签\n        ctx.fillStyle = \"#000\";\n        ctx.font = \"12px Arial\";\n        ctx.textAlign = \"center\";\n        // X轴标签\n        ctx.fillText(\"电流 (A)\", margin.left + chartWidth / 2, rect.height - 10);\n        // Y轴标签\n        ctx.save();\n        ctx.translate(20, margin.top + chartHeight / 2);\n        ctx.rotate(-Math.PI / 2);\n        ctx.fillText(\"效率 (%)\", 0, 0);\n        ctx.restore();\n        // 绘制刻度\n        ctx.textAlign = \"center\";\n        ctx.font = \"10px Arial\";\n        // X轴刻度\n        for(let i = 0; i <= 10; i++){\n            const value = (maxCurrent * i / 10).toFixed(1);\n            const x = margin.left + i / 10 * chartWidth;\n            ctx.fillText(value, x, margin.top + chartHeight + 15);\n        }\n        // Y轴刻度\n        ctx.textAlign = \"right\";\n        for(let i = 0; i <= 10; i++){\n            const value = (maxEfficiency * (10 - i) / 10).toFixed(1);\n            const y = margin.top + i / 10 * chartHeight + 4;\n            ctx.fillText(value, margin.left - 5, y);\n        }\n        // 绘制图例\n        if (showLegend) {\n            const legendX = margin.left + 20;\n            const legendY = margin.top + 20;\n            // 效率图例\n            ctx.strokeStyle = \"#00aa00\";\n            ctx.lineWidth = 2;\n            ctx.beginPath();\n            ctx.moveTo(legendX, legendY);\n            ctx.lineTo(legendX + 20, legendY);\n            ctx.stroke();\n            ctx.fillStyle = \"#000\";\n            ctx.font = \"12px Arial\";\n            ctx.textAlign = \"left\";\n            ctx.fillText(\"效率\", legendX + 25, legendY + 4);\n        }\n        // 绘制标题\n        ctx.fillStyle = \"#000\";\n        ctx.font = \"bold 14px Arial\";\n        ctx.textAlign = \"center\";\n        ctx.fillText(\"效率曲线\", margin.left + chartWidth / 2, 25);\n    }, [\n        data,\n        showGrid,\n        showLegend\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n            ref: canvasRef,\n            className: \"w-full h-full\",\n            style: {\n                width: \"100%\",\n                height: \"100%\"\n            }\n        }, void 0, false, {\n            fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\charts\\\\efficiency-chart.tsx\",\n            lineNumber: 176,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\charts\\\\efficiency-chart.tsx\",\n        lineNumber: 175,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/charts/efficiency-chart.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/charts/liv-chart.tsx":
/*!*********************************************!*\
  !*** ./src/components/charts/liv-chart.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LivChart: () => (/* binding */ LivChart)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ LivChart auto */ \n\nfunction LivChart({ data, showGrid = true, showLegend = false }) {\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const canvas = canvasRef.current;\n        if (!canvas || !data.powerData || !data.voltageData) return;\n        const ctx = canvas.getContext(\"2d\");\n        if (!ctx) return;\n        // 设置画布大小\n        const rect = canvas.getBoundingClientRect();\n        canvas.width = rect.width * window.devicePixelRatio;\n        canvas.height = rect.height * window.devicePixelRatio;\n        ctx.scale(window.devicePixelRatio, window.devicePixelRatio);\n        // 清空画布\n        ctx.clearRect(0, 0, rect.width, rect.height);\n        ctx.fillStyle = \"white\";\n        ctx.fillRect(0, 0, rect.width, rect.height);\n        // 图表边距\n        const margin = {\n            top: 40,\n            right: 60,\n            bottom: 60,\n            left: 80\n        };\n        const chartWidth = rect.width - margin.left - margin.right;\n        const chartHeight = rect.height - margin.top - margin.bottom;\n        // 数据范围\n        const maxCurrent = Math.max(...data.powerData.map((d)=>d.current), ...data.voltageData.map((d)=>d.current));\n        const maxPower = Math.max(...data.powerData.map((d)=>d.power));\n        const maxVoltage = Math.max(...data.voltageData.map((d)=>d.voltage));\n        // 坐标转换函数\n        const xScale = (current)=>margin.left + current / maxCurrent * chartWidth;\n        const yScalePower = (power)=>margin.top + chartHeight - power / maxPower * chartHeight;\n        const yScaleVoltage = (voltage)=>margin.top + chartHeight - voltage / maxVoltage * chartHeight;\n        // 绘制网格\n        if (showGrid) {\n            ctx.strokeStyle = \"#e0e0e0\";\n            ctx.lineWidth = 1;\n            // 垂直网格线\n            for(let i = 0; i <= 10; i++){\n                const x = margin.left + i / 10 * chartWidth;\n                ctx.beginPath();\n                ctx.moveTo(x, margin.top);\n                ctx.lineTo(x, margin.top + chartHeight);\n                ctx.stroke();\n            }\n            // 水平网格线\n            for(let i = 0; i <= 10; i++){\n                const y = margin.top + i / 10 * chartHeight;\n                ctx.beginPath();\n                ctx.moveTo(margin.left, y);\n                ctx.lineTo(margin.left + chartWidth, y);\n                ctx.stroke();\n            }\n        }\n        // 绘制坐标轴\n        ctx.strokeStyle = \"#000\";\n        ctx.lineWidth = 1;\n        // X轴\n        ctx.beginPath();\n        ctx.moveTo(margin.left, margin.top + chartHeight);\n        ctx.lineTo(margin.left + chartWidth, margin.top + chartHeight);\n        ctx.stroke();\n        // 左Y轴 (功率)\n        ctx.beginPath();\n        ctx.moveTo(margin.left, margin.top);\n        ctx.lineTo(margin.left, margin.top + chartHeight);\n        ctx.stroke();\n        // 右Y轴 (电压)\n        ctx.beginPath();\n        ctx.moveTo(margin.left + chartWidth, margin.top);\n        ctx.lineTo(margin.left + chartWidth, margin.top + chartHeight);\n        ctx.stroke();\n        // 绘制功率曲线 (红色)\n        if (data.powerData.length > 0) {\n            ctx.strokeStyle = \"#ff0000\";\n            ctx.lineWidth = 2;\n            ctx.beginPath();\n            data.powerData.forEach((point, index)=>{\n                const x = xScale(point.current);\n                const y = yScalePower(point.power);\n                if (index === 0) {\n                    ctx.moveTo(x, y);\n                } else {\n                    ctx.lineTo(x, y);\n                }\n            });\n            ctx.stroke();\n        }\n        // 绘制电压曲线 (蓝色)\n        if (data.voltageData.length > 0) {\n            ctx.strokeStyle = \"#0000ff\";\n            ctx.lineWidth = 2;\n            ctx.beginPath();\n            data.voltageData.forEach((point, index)=>{\n                const x = xScale(point.current);\n                const y = yScaleVoltage(point.voltage);\n                if (index === 0) {\n                    ctx.moveTo(x, y);\n                } else {\n                    ctx.lineTo(x, y);\n                }\n            });\n            ctx.stroke();\n        }\n        // 绘制坐标轴标签\n        ctx.fillStyle = \"#000\";\n        ctx.font = \"12px Arial\";\n        ctx.textAlign = \"center\";\n        // X轴标签\n        ctx.fillText(\"电流 (A)\", margin.left + chartWidth / 2, rect.height - 10);\n        // 左Y轴标签 (功率)\n        ctx.save();\n        ctx.translate(20, margin.top + chartHeight / 2);\n        ctx.rotate(-Math.PI / 2);\n        ctx.fillText(\"功率 (W)\", 0, 0);\n        ctx.restore();\n        // 右Y轴标签 (电压)\n        ctx.save();\n        ctx.translate(rect.width - 20, margin.top + chartHeight / 2);\n        ctx.rotate(Math.PI / 2);\n        ctx.fillText(\"电压 (V)\", 0, 0);\n        ctx.restore();\n        // 绘制刻度\n        ctx.textAlign = \"center\";\n        ctx.font = \"10px Arial\";\n        // X轴刻度\n        for(let i = 0; i <= 10; i++){\n            const value = (maxCurrent * i / 10).toFixed(1);\n            const x = margin.left + i / 10 * chartWidth;\n            ctx.fillText(value, x, margin.top + chartHeight + 15);\n        }\n        // 左Y轴刻度 (功率)\n        ctx.textAlign = \"right\";\n        for(let i = 0; i <= 10; i++){\n            const value = (maxPower * (10 - i) / 10).toFixed(1);\n            const y = margin.top + i / 10 * chartHeight + 4;\n            ctx.fillText(value, margin.left - 5, y);\n        }\n        // 右Y轴刻度 (电压)\n        ctx.textAlign = \"left\";\n        for(let i = 0; i <= 10; i++){\n            const value = (maxVoltage * (10 - i) / 10).toFixed(1);\n            const y = margin.top + i / 10 * chartHeight + 4;\n            ctx.fillText(value, margin.left + chartWidth + 5, y);\n        }\n        // 绘制图例\n        if (showLegend) {\n            const legendX = margin.left + 20;\n            const legendY = margin.top + 20;\n            // 功率图例\n            ctx.strokeStyle = \"#ff0000\";\n            ctx.lineWidth = 2;\n            ctx.beginPath();\n            ctx.moveTo(legendX, legendY);\n            ctx.lineTo(legendX + 20, legendY);\n            ctx.stroke();\n            ctx.fillStyle = \"#000\";\n            ctx.font = \"12px Arial\";\n            ctx.textAlign = \"left\";\n            ctx.fillText(\"功率\", legendX + 25, legendY + 4);\n            // 电压图例\n            ctx.strokeStyle = \"#0000ff\";\n            ctx.lineWidth = 2;\n            ctx.beginPath();\n            ctx.moveTo(legendX, legendY + 20);\n            ctx.lineTo(legendX + 20, legendY + 20);\n            ctx.stroke();\n            ctx.fillText(\"电压\", legendX + 25, legendY + 24);\n        }\n        // 绘制标题\n        ctx.fillStyle = \"#000\";\n        ctx.font = \"bold 14px Arial\";\n        ctx.textAlign = \"center\";\n        ctx.fillText(\"LIV曲线\", margin.left + chartWidth / 2, 25);\n    }, [\n        data,\n        showGrid,\n        showLegend\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n            ref: canvasRef,\n            className: \"w-full h-full\",\n            style: {\n                width: \"100%\",\n                height: \"100%\"\n            }\n        }, void 0, false, {\n            fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\charts\\\\liv-chart.tsx\",\n            lineNumber: 224,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\charts\\\\liv-chart.tsx\",\n        lineNumber: 223,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/charts/liv-chart.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/charts/spectrum-chart.tsx":
/*!**************************************************!*\
  !*** ./src/components/charts/spectrum-chart.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SpectrumChart: () => (/* binding */ SpectrumChart)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ SpectrumChart auto */ \n\nfunction SpectrumChart({ data, showGrid = true, showLegend = false }) {\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const canvas = canvasRef.current;\n        if (!canvas || !data.wavelengthData || data.wavelengthData.length === 0) return;\n        const ctx = canvas.getContext(\"2d\");\n        if (!ctx) return;\n        // 设置画布大小\n        const rect = canvas.getBoundingClientRect();\n        canvas.width = rect.width * window.devicePixelRatio;\n        canvas.height = rect.height * window.devicePixelRatio;\n        ctx.scale(window.devicePixelRatio, window.devicePixelRatio);\n        // 清空画布\n        ctx.clearRect(0, 0, rect.width, rect.height);\n        ctx.fillStyle = \"white\";\n        ctx.fillRect(0, 0, rect.width, rect.height);\n        // 图表边距\n        const margin = {\n            top: 40,\n            right: 60,\n            bottom: 60,\n            left: 80\n        };\n        const chartWidth = rect.width - margin.left - margin.right;\n        const chartHeight = rect.height - margin.top - margin.bottom;\n        // 数据范围\n        const minWavelength = Math.min(...data.wavelengthData.map((d)=>d.wavelength));\n        const maxWavelength = Math.max(...data.wavelengthData.map((d)=>d.wavelength));\n        const maxIntensity = Math.max(...data.wavelengthData.map((d)=>d.intensity));\n        // 坐标转换函数\n        const xScale = (wavelength)=>margin.left + (wavelength - minWavelength) / (maxWavelength - minWavelength) * chartWidth;\n        const yScale = (intensity)=>margin.top + chartHeight - intensity / maxIntensity * chartHeight;\n        // 绘制网格\n        if (showGrid) {\n            ctx.strokeStyle = \"#e0e0e0\";\n            ctx.lineWidth = 1;\n            // 垂直网格线\n            for(let i = 0; i <= 10; i++){\n                const x = margin.left + i / 10 * chartWidth;\n                ctx.beginPath();\n                ctx.moveTo(x, margin.top);\n                ctx.lineTo(x, margin.top + chartHeight);\n                ctx.stroke();\n            }\n            // 水平网格线\n            for(let i = 0; i <= 10; i++){\n                const y = margin.top + i / 10 * chartHeight;\n                ctx.beginPath();\n                ctx.moveTo(margin.left, y);\n                ctx.lineTo(margin.left + chartWidth, y);\n                ctx.stroke();\n            }\n        }\n        // 绘制坐标轴\n        ctx.strokeStyle = \"#000\";\n        ctx.lineWidth = 1;\n        // X轴\n        ctx.beginPath();\n        ctx.moveTo(margin.left, margin.top + chartHeight);\n        ctx.lineTo(margin.left + chartWidth, margin.top + chartHeight);\n        ctx.stroke();\n        // Y轴\n        ctx.beginPath();\n        ctx.moveTo(margin.left, margin.top);\n        ctx.lineTo(margin.left, margin.top + chartHeight);\n        ctx.stroke();\n        // 绘制光谱曲线 (红色)\n        ctx.strokeStyle = \"#ff0000\";\n        ctx.lineWidth = 2;\n        ctx.beginPath();\n        data.wavelengthData.forEach((point, index)=>{\n            const x = xScale(point.wavelength);\n            const y = yScale(point.intensity);\n            if (index === 0) {\n                ctx.moveTo(x, y);\n            } else {\n                ctx.lineTo(x, y);\n            }\n        });\n        ctx.stroke();\n        // 绘制坐标轴标签\n        ctx.fillStyle = \"#000\";\n        ctx.font = \"12px Arial\";\n        ctx.textAlign = \"center\";\n        // X轴标签\n        ctx.fillText(\"波长 (nm)\", margin.left + chartWidth / 2, rect.height - 10);\n        // Y轴标签\n        ctx.save();\n        ctx.translate(20, margin.top + chartHeight / 2);\n        ctx.rotate(-Math.PI / 2);\n        ctx.fillText(\"强度\", 0, 0);\n        ctx.restore();\n        // 绘制刻度\n        ctx.textAlign = \"center\";\n        ctx.font = \"10px Arial\";\n        // X轴刻度\n        for(let i = 0; i <= 10; i++){\n            const value = (minWavelength + (maxWavelength - minWavelength) * i / 10).toFixed(0);\n            const x = margin.left + i / 10 * chartWidth;\n            ctx.fillText(value, x, margin.top + chartHeight + 15);\n        }\n        // Y轴刻度\n        ctx.textAlign = \"right\";\n        for(let i = 0; i <= 10; i++){\n            const value = (maxIntensity * (10 - i) / 10).toFixed(0);\n            const y = margin.top + i / 10 * chartHeight + 4;\n            ctx.fillText(value, margin.left - 5, y);\n        }\n        // 绘制图例\n        if (showLegend) {\n            const legendX = margin.left + 20;\n            const legendY = margin.top + 20;\n            // 光谱图例\n            ctx.strokeStyle = \"#ff0000\";\n            ctx.lineWidth = 2;\n            ctx.beginPath();\n            ctx.moveTo(legendX, legendY);\n            ctx.lineTo(legendX + 20, legendY);\n            ctx.stroke();\n            ctx.fillStyle = \"#000\";\n            ctx.font = \"12px Arial\";\n            ctx.textAlign = \"left\";\n            ctx.fillText(\"光谱强度\", legendX + 25, legendY + 4);\n        }\n        // 绘制标题\n        ctx.fillStyle = \"#000\";\n        ctx.font = \"bold 14px Arial\";\n        ctx.textAlign = \"center\";\n        ctx.fillText(\"光谱图\", margin.left + chartWidth / 2, 25);\n    }, [\n        data,\n        showGrid,\n        showLegend\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n            ref: canvasRef,\n            className: \"w-full h-full\",\n            style: {\n                width: \"100%\",\n                height: \"100%\"\n            }\n        }, void 0, false, {\n            fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\charts\\\\spectrum-chart.tsx\",\n            lineNumber: 169,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\charts\\\\spectrum-chart.tsx\",\n        lineNumber: 168,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/charts/spectrum-chart.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/desktop-like-interface.tsx":
/*!***************************************************!*\
  !*** ./src/components/desktop-like-interface.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DesktopLikeInterface: () => (/* binding */ DesktopLikeInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(ssr)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(ssr)/./src/components/ui/checkbox.tsx\");\n/* harmony import */ var _components_ui_slider__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/slider */ \"(ssr)/./src/components/ui/slider.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/tabs */ \"(ssr)/./src/components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_collapsible__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/collapsible */ \"(ssr)/./src/components/ui/collapsible.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/use-toast */ \"(ssr)/./src/hooks/use-toast.tsx\");\n/* harmony import */ var _components_charts_liv_chart__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/charts/liv-chart */ \"(ssr)/./src/components/charts/liv-chart.tsx\");\n/* harmony import */ var _components_charts_spectrum_chart__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/charts/spectrum-chart */ \"(ssr)/./src/components/charts/spectrum-chart.tsx\");\n/* harmony import */ var _components_charts_efficiency_chart__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/charts/efficiency-chart */ \"(ssr)/./src/components/charts/efficiency-chart.tsx\");\n/* harmony import */ var _components_charts_divergence_chart__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/charts/divergence-chart */ \"(ssr)/./src/components/charts/divergence-chart.tsx\");\n/* __next_internal_client_entry_do_not_use__ DesktopLikeInterface auto */ \n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction DesktopLikeInterface() {\n    const [selectedFile, setSelectedFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [result, setResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"liv\");\n    // 界面控制状态\n    const [curvesOpen, setCurvesOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [paramsOpen, setParamsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [smoothOpen, setSmoothOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [chartOpen, setChartOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // 控制参数\n    const [i1Current, setI1Current] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"0.5\");\n    const [i2Current, setI2Current] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"1\");\n    const [useSmoothing, setUseSmoothing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [smoothingWindow, setSmoothingWindow] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        5\n    ]);\n    const [showLegend, setShowLegend] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showGrid, setShowGrid] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast)();\n    const handleFileSelect = (event)=>{\n        const file = event.target.files?.[0];\n        if (file) {\n            setSelectedFile(file);\n            setResult(null);\n        }\n    };\n    const handleUpload = async ()=>{\n        if (!selectedFile) {\n            toast({\n                variant: \"destructive\",\n                title: \"错误\",\n                description: \"请选择数据文件\"\n            });\n            return;\n        }\n        try {\n            setIsProcessing(true);\n            const formData = new FormData();\n            formData.append(\"file\", selectedFile);\n            const response = await fetch(\"http://localhost:5105/api/SimpleProcessing/upload\", {\n                method: \"POST\",\n                body: formData\n            });\n            const data = await response.json();\n            if (data.success) {\n                setResult(data);\n                toast({\n                    title: \"成功\",\n                    description: \"文件处理完成\"\n                });\n            } else {\n                toast({\n                    variant: \"destructive\",\n                    title: \"处理失败\",\n                    description: data.error || \"处理过程中发生错误\"\n                });\n            }\n        } catch (error) {\n            console.error(\"Upload error:\", error);\n            toast({\n                variant: \"destructive\",\n                title: \"上传失败\",\n                description: \"网络错误或服务器异常\"\n            });\n        } finally{\n            setIsProcessing(false);\n        }\n    };\n    const renderParametersTable = ()=>{\n        if (!result?.parameters) return null;\n        const { parameters } = result;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-xs bg-gray-50 p-2 border-l border-gray-300\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"font-semibold mb-2 text-gray-700\",\n                    children: \"等待数据...\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                    lineNumber: 131,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                \"峰值波长: \",\n                                parameters.peakWavelength?.toFixed(2) || \"N/A\",\n                                \" nm\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                \"半高全宽: \",\n                                parameters.fwhm?.toFixed(2) || \"N/A\",\n                                \" nm\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                \"阈值电流: \",\n                                parameters.thresholdCurrent?.toFixed(3) || \"N/A\",\n                                \" A\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                \"最大功率: \",\n                                parameters.maxPower?.toFixed(3) || \"N/A\",\n                                \" W\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                \"最大效率: \",\n                                parameters.maxEfficiency?.toFixed(2) || \"N/A\",\n                                \" %\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                \"斜率效率: \",\n                                parameters.slopeEfficiency?.toFixed(3) || \"N/A\",\n                                \" W/A\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                \"串联电阻: \",\n                                parameters.seriesResistance?.toFixed(3) || \"N/A\",\n                                \" Ω\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n            lineNumber: 130,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-[calc(100vh-80px)] bg-gray-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-80 bg-white border-r border-gray-300 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-3 border-b border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-sm font-semibold mb-3\",\n                                children: \"数据操作\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        className: \"w-full text-xs h-8\",\n                                        onClick: ()=>fileInputRef.current?.click(),\n                                        children: \"选择数据文件\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        ref: fileInputRef,\n                                        type: \"file\",\n                                        accept: \".xlsx,.xls,.csv\",\n                                        onChange: handleFileSelect,\n                                        className: \"hidden\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                className: \"flex-1 text-xs h-8\",\n                                                children: \"导出数据\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                className: \"flex-1 text-xs h-8\",\n                                                children: \"保存图表\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 11\n                            }, this),\n                            selectedFile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-3 p-2 bg-gray-50 rounded text-xs\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"truncate\",\n                                    children: [\n                                        \"文件: \",\n                                        selectedFile.name\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-3 border-b border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-sm font-semibold mb-3\",\n                                children: \"批量处理\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-2 mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                className: \"text-xs\",\n                                                children: \"I1 (A):\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                                lineNumber: 192,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                value: i1Current,\n                                                onChange: (e)=>setI1Current(e.target.value),\n                                                className: \"h-7 text-xs\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                className: \"text-xs\",\n                                                children: \"I2 (A):\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                value: i2Current,\n                                                onChange: (e)=>setI2Current(e.target.value),\n                                                className: \"h-7 text-xs\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: handleUpload,\n                                disabled: !selectedFile || isProcessing,\n                                className: \"w-full h-8 text-xs\",\n                                children: isProcessing ? \"批量计算...\" : \"批量计算\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_8__.Collapsible, {\n                        open: curvesOpen,\n                        onOpenChange: setCurvesOpen,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_8__.CollapsibleTrigger, {\n                                className: \"flex items-center justify-between w-full p-3 border-b border-gray-200 hover:bg-gray-50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-semibold\",\n                                        children: \"曲线选择\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 13\n                                    }, this),\n                                    curvesOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 27\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 65\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_8__.CollapsibleContent, {\n                                className: \"p-3 border-b border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2 text-xs\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    className: \"flex-1 h-7 text-xs\",\n                                                    children: \"全选\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                                    lineNumber: 226,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    className: \"flex-1 h-7 text-xs\",\n                                                    children: \"取消全选\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                                    lineNumber: 229,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    className: \"flex-1 h-7 text-xs\",\n                                                    children: \"清除\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                                    lineNumber: 232,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-gray-600 mt-2\",\n                                            children: \"请选择数据文件\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_8__.Collapsible, {\n                        open: paramsOpen,\n                        onOpenChange: setParamsOpen,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_8__.CollapsibleTrigger, {\n                                className: \"flex items-center justify-between w-full p-3 border-b border-gray-200 hover:bg-gray-50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-semibold\",\n                                        children: \"计算参数\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 13\n                                    }, this),\n                                    paramsOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 27\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 65\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_8__.CollapsibleContent, {\n                                className: \"p-3 border-b border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-600\",\n                                    children: \"等待数据...\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                    lineNumber: 250,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_8__.Collapsible, {\n                        open: smoothOpen,\n                        onOpenChange: setSmoothOpen,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_8__.CollapsibleTrigger, {\n                                className: \"flex items-center justify-between w-full p-3 border-b border-gray-200 hover:bg-gray-50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-semibold\",\n                                        children: \"数据平滑\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 13\n                                    }, this),\n                                    smoothOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 27\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 65\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_8__.CollapsibleContent, {\n                                className: \"p-3 border-b border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_5__.Checkbox, {\n                                                    id: \"smoothing\",\n                                                    checked: useSmoothing,\n                                                    onCheckedChange: (checked)=>setUseSmoothing(!!checked)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                    htmlFor: \"smoothing\",\n                                                    className: \"text-xs\",\n                                                    children: \"启用平滑\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                    className: \"text-xs\",\n                                                    children: \"窗口大小\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                                    lineNumber: 273,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_slider__WEBPACK_IMPORTED_MODULE_6__.Slider, {\n                                                    value: smoothingWindow,\n                                                    onValueChange: setSmoothingWindow,\n                                                    max: 20,\n                                                    min: 3,\n                                                    step: 1,\n                                                    className: \"w-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                                    lineNumber: 274,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-600 text-center\",\n                                                    children: smoothingWindow[0]\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                                    lineNumber: 282,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                            lineNumber: 272,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                    lineNumber: 263,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                        lineNumber: 257,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_8__.Collapsible, {\n                        open: chartOpen,\n                        onOpenChange: setChartOpen,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_8__.CollapsibleTrigger, {\n                                className: \"flex items-center justify-between w-full p-3 border-b border-gray-200 hover:bg-gray-50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-semibold\",\n                                        children: \"图表设置\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 13\n                                    }, this),\n                                    chartOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 26\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 64\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_8__.CollapsibleContent, {\n                                className: \"p-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_5__.Checkbox, {\n                                                    id: \"legend\",\n                                                    checked: showLegend,\n                                                    onCheckedChange: (checked)=>setShowLegend(!!checked)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                    htmlFor: \"legend\",\n                                                    className: \"text-xs\",\n                                                    children: \"图例\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                                    lineNumber: 304,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_5__.Checkbox, {\n                                                    id: \"grid\",\n                                                    checked: showGrid,\n                                                    onCheckedChange: (checked)=>setShowGrid(!!checked)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                                    lineNumber: 307,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                    htmlFor: \"grid\",\n                                                    className: \"text-xs\",\n                                                    children: \"网格\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                                    lineNumber: 312,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            className: \"w-full h-7 text-xs\",\n                                            children: \"重置缩放\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                lineNumber: 296,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                        lineNumber: 291,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                lineNumber: 148,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-full bg-white rounded border border-gray-300\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.Tabs, {\n                            value: activeTab,\n                            onValueChange: setActiveTab,\n                            className: \"h-full flex flex-col\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsList, {\n                                    className: \"w-fit m-0 rounded-none border-b border-gray-300 bg-transparent h-auto p-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                            value: \"liv\",\n                                            className: \"rounded-none border-r border-gray-300 px-6 py-2 text-sm data-[state=active]:bg-white data-[state=active]:border-b-white data-[state=active]:border-b-2 data-[state=active]:-mb-px\",\n                                            children: \"LIV曲线\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                            value: \"spectrum\",\n                                            className: \"rounded-none border-r border-gray-300 px-6 py-2 text-sm data-[state=active]:bg-white data-[state=active]:border-b-white data-[state=active]:border-b-2 data-[state=active]:-mb-px\",\n                                            children: \"光谱图\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                            value: \"efficiency\",\n                                            className: \"rounded-none border-r border-gray-300 px-6 py-2 text-sm data-[state=active]:bg-white data-[state=active]:border-b-white data-[state=active]:border-b-2 data-[state=active]:-mb-px\",\n                                            children: \"效率曲线\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                            lineNumber: 341,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                            value: \"divergence\",\n                                            className: \"rounded-none px-6 py-2 text-sm data-[state=active]:bg-white data-[state=active]:border-b-white data-[state=active]:border-b-2 data-[state=active]:-mb-px\",\n                                            children: \"发散角\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                            lineNumber: 347,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                    lineNumber: 328,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 flex\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsContent, {\n                                                    value: \"liv\",\n                                                    className: \"h-full m-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-full border border-gray-200 bg-gray-50 flex items-center justify-center\",\n                                                        children: result?.rawData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_charts_liv_chart__WEBPACK_IMPORTED_MODULE_10__.LivChart, {\n                                                            data: result.rawData,\n                                                            showGrid: showGrid,\n                                                            showLegend: showLegend\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                                            lineNumber: 361,\n                                                            columnNumber: 25\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-medium mb-2\",\n                                                                    children: \"LIV曲线\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                                                    lineNumber: 368,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-500 text-sm\",\n                                                                    children: \"请选择并处理数据文件以显示图表\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                                                    lineNumber: 369,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                                            lineNumber: 367,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                                        lineNumber: 359,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                                    lineNumber: 358,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsContent, {\n                                                    value: \"spectrum\",\n                                                    className: \"h-full m-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-full border border-gray-200 bg-gray-50 flex items-center justify-center\",\n                                                        children: result?.rawData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_charts_spectrum_chart__WEBPACK_IMPORTED_MODULE_11__.SpectrumChart, {\n                                                            data: result.rawData,\n                                                            showGrid: showGrid,\n                                                            showLegend: showLegend\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                                            lineNumber: 378,\n                                                            columnNumber: 25\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-medium mb-2\",\n                                                                    children: \"光谱图\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                                                    lineNumber: 385,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-500 text-sm\",\n                                                                    children: \"请选择并处理数据文件以显示图表\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                                                    lineNumber: 386,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                                            lineNumber: 384,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                                        lineNumber: 376,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                                    lineNumber: 375,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsContent, {\n                                                    value: \"efficiency\",\n                                                    className: \"h-full m-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-full border border-gray-200 bg-gray-50 flex items-center justify-center\",\n                                                        children: result?.rawData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_charts_efficiency_chart__WEBPACK_IMPORTED_MODULE_12__.EfficiencyChart, {\n                                                            data: result.rawData,\n                                                            showGrid: showGrid,\n                                                            showLegend: showLegend\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                                            lineNumber: 395,\n                                                            columnNumber: 25\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-medium mb-2\",\n                                                                    children: \"效率曲线\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                                                    lineNumber: 402,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-500 text-sm\",\n                                                                    children: \"请选择并处理数据文件以显示图表\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                                                    lineNumber: 403,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                                            lineNumber: 401,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                                        lineNumber: 393,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                                    lineNumber: 392,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsContent, {\n                                                    value: \"divergence\",\n                                                    className: \"h-full m-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-full border border-gray-200 bg-gray-50 flex items-center justify-center\",\n                                                        children: result?.rawData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_charts_divergence_chart__WEBPACK_IMPORTED_MODULE_13__.DivergenceChart, {\n                                                            data: result.rawData,\n                                                            showGrid: showGrid,\n                                                            showLegend: showLegend\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                                            lineNumber: 412,\n                                                            columnNumber: 25\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-medium mb-2\",\n                                                                    children: \"发散角\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                                                    lineNumber: 419,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-500 text-sm\",\n                                                                    children: \"请选择并处理数据文件以显示图表\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                                                    lineNumber: 420,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                                            lineNumber: 418,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                                    lineNumber: 409,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                            lineNumber: 357,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-64 border-l border-gray-200\",\n                                            children: renderParametersTable()\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                            lineNumber: 428,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                                    lineNumber: 355,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                            lineNumber: 327,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                        lineNumber: 325,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                    lineNumber: 324,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n                lineNumber: 323,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\desktop-like-interface.tsx\",\n        lineNumber: 146,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/desktop-like-interface.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/login.tsx":
/*!**********************************!*\
  !*** ./src/components/login.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LoginComponent: () => (/* binding */ LoginComponent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(ssr)/./src/components/ui/label.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/use-toast */ \"(ssr)/./src/hooks/use-toast.tsx\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Loader2_LogIn_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Loader2,LogIn!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-in.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Loader2_LogIn_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Loader2,LogIn!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Loader2_LogIn_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Loader2,LogIn!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Loader2_LogIn_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Loader2,LogIn!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* __next_internal_client_entry_do_not_use__ LoginComponent auto */ \n\n\n\n\n\n\n\nfunction LoginComponent({ onLoginSuccess, onCancel }) {\n    const [username, setUsername] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [rememberMe, setRememberMe] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    const handleLogin = async (e)=>{\n        e.preventDefault();\n        if (!username.trim() || !password.trim()) {\n            toast({\n                variant: \"destructive\",\n                title: \"输入错误\",\n                description: \"请输入用户名和密码\"\n            });\n            return;\n        }\n        setIsLoading(true);\n        try {\n            const response = await fetch(\"http://localhost:5105/api/Auth/login\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    username,\n                    password,\n                    rememberMe\n                })\n            });\n            const data = await response.json();\n            if (data.success && response.ok) {\n                const authData = {\n                    accessToken: data.accessToken,\n                    refreshToken: data.refreshToken,\n                    username: data.username,\n                    roles: data.roles,\n                    expiresIn: data.expiresIn\n                };\n                // 存储认证信息到localStorage\n                localStorage.setItem(\"auth_token\", data.accessToken);\n                localStorage.setItem(\"refresh_token\", data.refreshToken);\n                localStorage.setItem(\"user_info\", JSON.stringify({\n                    username: data.username,\n                    roles: data.roles\n                }));\n                toast({\n                    title: \"登录成功\",\n                    description: `欢迎回来，${data.username}！`\n                });\n                onLoginSuccess(authData);\n            } else {\n                throw new Error(data.error || \"登录失败\");\n            }\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            toast({\n                variant: \"destructive\",\n                title: \"登录失败\",\n                description: error instanceof Error ? error.message : \"网络错误，请重试\"\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleDemoLogin = (demoUser)=>{\n        const demoCredentials = {\n            admin: {\n                username: \"admin\",\n                password: \"admin123\"\n            },\n            user: {\n                username: \"user\",\n                password: \"user123\"\n            },\n            test: {\n                username: \"test\",\n                password: \"test123\"\n            }\n        };\n        const credentials = demoCredentials[demoUser];\n        setUsername(credentials.username);\n        setPassword(credentials.password);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex min-h-screen items-center justify-center bg-background p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n            className: \"w-full max-w-md\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                    className: \"space-y-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Loader2_LogIn_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-8 w-8 text-primary\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\login.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\login.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"text-2xl text-center\",\n                            children: \"LIV Analyzer 登录\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\login.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                            className: \"text-center\",\n                            children: \"请输入您的凭据以访问系统\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\login.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\login.tsx\",\n                    lineNumber: 125,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleLogin,\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                            htmlFor: \"username\",\n                                            children: \"用户名\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\login.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                            id: \"username\",\n                                            type: \"text\",\n                                            placeholder: \"请输入用户名\",\n                                            value: username,\n                                            onChange: (e)=>setUsername(e.target.value),\n                                            disabled: isLoading,\n                                            autoComplete: \"username\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\login.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\login.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                            htmlFor: \"password\",\n                                            children: \"密码\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\login.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                    id: \"password\",\n                                                    type: showPassword ? \"text\" : \"password\",\n                                                    placeholder: \"请输入密码\",\n                                                    value: password,\n                                                    onChange: (e)=>setPassword(e.target.value),\n                                                    disabled: isLoading,\n                                                    autoComplete: \"current-password\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\login.tsx\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    type: \"button\",\n                                                    variant: \"ghost\",\n                                                    size: \"sm\",\n                                                    className: \"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent\",\n                                                    onClick: ()=>setShowPassword(!showPassword),\n                                                    disabled: isLoading,\n                                                    children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Loader2_LogIn_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\login.tsx\",\n                                                        lineNumber: 170,\n                                                        columnNumber: 21\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Loader2_LogIn_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\login.tsx\",\n                                                        lineNumber: 172,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\login.tsx\",\n                                                    lineNumber: 161,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\login.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\login.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"remember-me\",\n                                            type: \"checkbox\",\n                                            checked: rememberMe,\n                                            onChange: (e)=>setRememberMe(e.target.checked),\n                                            disabled: isLoading,\n                                            className: \"h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\login.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                            htmlFor: \"remember-me\",\n                                            className: \"text-sm\",\n                                            children: \"记住我\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\login.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\login.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    type: \"submit\",\n                                    className: \"w-full\",\n                                    disabled: isLoading,\n                                    children: [\n                                        isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Loader2_LogIn_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4 animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\login.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 29\n                                        }, this),\n                                        \"登录\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\login.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\login.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 flex items-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"w-full border-t\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\login.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\login.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative flex justify-center text-xs uppercase\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-background px-2 text-muted-foreground\",\n                                                children: \"演示账户\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\login.tsx\",\n                                                lineNumber: 209,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\login.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\login.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-3 gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            onClick: ()=>handleDemoLogin(\"admin\"),\n                                            disabled: isLoading,\n                                            className: \"text-xs\",\n                                            children: \"管理员\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\login.tsx\",\n                                            lineNumber: 216,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            onClick: ()=>handleDemoLogin(\"user\"),\n                                            disabled: isLoading,\n                                            className: \"text-xs\",\n                                            children: \"用户\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\login.tsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            onClick: ()=>handleDemoLogin(\"test\"),\n                                            disabled: isLoading,\n                                            className: \"text-xs\",\n                                            children: \"测试\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\login.tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\login.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\login.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 11\n                        }, this),\n                        onCancel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"ghost\",\n                            className: \"w-full\",\n                            onClick: onCancel,\n                            disabled: isLoading,\n                            children: \"取消\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\login.tsx\",\n                            lineNumber: 247,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\login.tsx\",\n                    lineNumber: 134,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\login.tsx\",\n            lineNumber: 124,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\login.tsx\",\n        lineNumber: 123,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/login.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers.tsx":
/*!**************************************!*\
  !*** ./src/components/providers.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_toaster__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/toaster */ \"(ssr)/./src/components/ui/toaster.tsx\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\n\n\n\nfunction Providers({ children }) {\n    const [queryClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.QueryClient({\n            defaultOptions: {\n                queries: {\n                    staleTime: 60 * 1000,\n                    retry: 1\n                }\n            }\n        }));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_3__.ThemeProvider, {\n        attribute: \"class\",\n        defaultTheme: \"system\",\n        enableSystem: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__.QueryClientProvider, {\n            client: queryClient,\n            children: [\n                children,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toaster__WEBPACK_IMPORTED_MODULE_2__.Toaster, {}, void 0, false, {\n                    fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\providers.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\providers.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\providers.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9wcm92aWRlcnMudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFFd0U7QUFDeEM7QUFDaUI7QUFDTjtBQUVwQyxTQUFTSyxVQUFVLEVBQUVDLFFBQVEsRUFBaUM7SUFDbkUsTUFBTSxDQUFDQyxZQUFZLEdBQUdMLCtDQUFRQSxDQUFDLElBQU0sSUFBSUYsOERBQVdBLENBQUM7WUFDbkRRLGdCQUFnQjtnQkFDZEMsU0FBUztvQkFDUEMsV0FBVyxLQUFLO29CQUNoQkMsT0FBTztnQkFDVDtZQUNGO1FBQ0Y7SUFFQSxxQkFDRSw4REFBQ1Asc0RBQWFBO1FBQUNRLFdBQVU7UUFBUUMsY0FBYTtRQUFTQyxZQUFZO2tCQUNqRSw0RUFBQ2Isc0VBQW1CQTtZQUFDYyxRQUFRUjs7Z0JBQzFCRDs4QkFDRCw4REFBQ0gsMkRBQU9BOzs7Ozs7Ozs7Ozs7Ozs7O0FBSWhCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbGl2LWFuYWx5emVyLWZyb250ZW5kLy4vc3JjL2NvbXBvbmVudHMvcHJvdmlkZXJzLnRzeD9iZTg3Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiXG5cbmltcG9ydCB7IFF1ZXJ5Q2xpZW50LCBRdWVyeUNsaWVudFByb3ZpZGVyIH0gZnJvbSBcIkB0YW5zdGFjay9yZWFjdC1xdWVyeVwiXG5pbXBvcnQgeyB1c2VTdGF0ZSB9IGZyb20gXCJyZWFjdFwiXG5pbXBvcnQgeyBUb2FzdGVyIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS90b2FzdGVyXCJcbmltcG9ydCB7IFRoZW1lUHJvdmlkZXIgfSBmcm9tIFwibmV4dC10aGVtZXNcIlxuXG5leHBvcnQgZnVuY3Rpb24gUHJvdmlkZXJzKHsgY2hpbGRyZW4gfTogeyBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlIH0pIHtcbiAgY29uc3QgW3F1ZXJ5Q2xpZW50XSA9IHVzZVN0YXRlKCgpID0+IG5ldyBRdWVyeUNsaWVudCh7XG4gICAgZGVmYXVsdE9wdGlvbnM6IHtcbiAgICAgIHF1ZXJpZXM6IHtcbiAgICAgICAgc3RhbGVUaW1lOiA2MCAqIDEwMDAsXG4gICAgICAgIHJldHJ5OiAxLFxuICAgICAgfSxcbiAgICB9LFxuICB9KSlcblxuICByZXR1cm4gKFxuICAgIDxUaGVtZVByb3ZpZGVyIGF0dHJpYnV0ZT1cImNsYXNzXCIgZGVmYXVsdFRoZW1lPVwic3lzdGVtXCIgZW5hYmxlU3lzdGVtPlxuICAgICAgPFF1ZXJ5Q2xpZW50UHJvdmlkZXIgY2xpZW50PXtxdWVyeUNsaWVudH0+XG4gICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgPFRvYXN0ZXIgLz5cbiAgICAgIDwvUXVlcnlDbGllbnRQcm92aWRlcj5cbiAgICA8L1RoZW1lUHJvdmlkZXI+XG4gIClcbn0iXSwibmFtZXMiOlsiUXVlcnlDbGllbnQiLCJRdWVyeUNsaWVudFByb3ZpZGVyIiwidXNlU3RhdGUiLCJUb2FzdGVyIiwiVGhlbWVQcm92aWRlciIsIlByb3ZpZGVycyIsImNoaWxkcmVuIiwicXVlcnlDbGllbnQiLCJkZWZhdWx0T3B0aW9ucyIsInF1ZXJpZXMiLCJzdGFsZVRpbWUiLCJyZXRyeSIsImF0dHJpYnV0ZSIsImRlZmF1bHRUaGVtZSIsImVuYWJsZVN5c3RlbSIsImNsaWVudCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? react__WEBPACK_IMPORTED_MODULE_1__.Fragment : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 45,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 51,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 63,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 71,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/checkbox.tsx":
/*!****************************************!*\
  !*** ./src/components/ui/checkbox.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Checkbox: () => (/* binding */ Checkbox)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_checkbox__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-checkbox */ \"(ssr)/./node_modules/@radix-ui/react-checkbox/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Check_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Check!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Checkbox auto */ \n\n\n\n\nconst Checkbox = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_checkbox__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground\", className),\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_checkbox__WEBPACK_IMPORTED_MODULE_3__.Indicator, {\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center justify-center text-current\"),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\ui\\\\checkbox.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\ui\\\\checkbox.tsx\",\n            lineNumber: 21,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\ui\\\\checkbox.tsx\",\n        lineNumber: 13,\n        columnNumber: 3\n    }, undefined));\nCheckbox.displayName = _radix_ui_react_checkbox__WEBPACK_IMPORTED_MODULE_3__.Root.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/checkbox.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/collapsible.tsx":
/*!*******************************************!*\
  !*** ./src/components/ui/collapsible.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Collapsible: () => (/* binding */ Collapsible),\n/* harmony export */   CollapsibleContent: () => (/* binding */ CollapsibleContent),\n/* harmony export */   CollapsibleTrigger: () => (/* binding */ CollapsibleTrigger)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _radix_ui_react_collapsible__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-collapsible */ \"(ssr)/./node_modules/@radix-ui/react-collapsible/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ Collapsible,CollapsibleTrigger,CollapsibleContent auto */ \n\nconst Collapsible = _radix_ui_react_collapsible__WEBPACK_IMPORTED_MODULE_1__.Root;\nconst CollapsibleTrigger = _radix_ui_react_collapsible__WEBPACK_IMPORTED_MODULE_1__.CollapsibleTrigger;\nconst CollapsibleContent = _radix_ui_react_collapsible__WEBPACK_IMPORTED_MODULE_1__.CollapsibleContent;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9jb2xsYXBzaWJsZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O3VHQUU4QjtBQUNxQztBQUVuRSxNQUFNRSxjQUFjRCw2REFBeUI7QUFFN0MsTUFBTUcscUJBQXFCSCwyRUFBdUM7QUFFbEUsTUFBTUkscUJBQXFCSiwyRUFBdUM7QUFFSiIsInNvdXJjZXMiOlsid2VicGFjazovL2xpdi1hbmFseXplci1mcm9udGVuZC8uL3NyYy9jb21wb25lbnRzL3VpL2NvbGxhcHNpYmxlLnRzeD80N2Q0Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiXG5cbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiXG5pbXBvcnQgKiBhcyBDb2xsYXBzaWJsZVByaW1pdGl2ZSBmcm9tIFwiQHJhZGl4LXVpL3JlYWN0LWNvbGxhcHNpYmxlXCJcblxuY29uc3QgQ29sbGFwc2libGUgPSBDb2xsYXBzaWJsZVByaW1pdGl2ZS5Sb290XG5cbmNvbnN0IENvbGxhcHNpYmxlVHJpZ2dlciA9IENvbGxhcHNpYmxlUHJpbWl0aXZlLkNvbGxhcHNpYmxlVHJpZ2dlclxuXG5jb25zdCBDb2xsYXBzaWJsZUNvbnRlbnQgPSBDb2xsYXBzaWJsZVByaW1pdGl2ZS5Db2xsYXBzaWJsZUNvbnRlbnRcblxuZXhwb3J0IHsgQ29sbGFwc2libGUsIENvbGxhcHNpYmxlVHJpZ2dlciwgQ29sbGFwc2libGVDb250ZW50IH0iXSwibmFtZXMiOlsiUmVhY3QiLCJDb2xsYXBzaWJsZVByaW1pdGl2ZSIsIkNvbGxhcHNpYmxlIiwiUm9vdCIsIkNvbGxhcHNpYmxlVHJpZ2dlciIsIkNvbGxhcHNpYmxlQ29udGVudCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/collapsible.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/input.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/input.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, type, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 11,\n        columnNumber: 7\n    }, undefined);\n});\nInput.displayName = \"Input\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9pbnB1dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUE4QjtBQUVFO0FBS2hDLE1BQU1FLHNCQUFRRiw2Q0FBZ0IsQ0FDNUIsQ0FBQyxFQUFFSSxTQUFTLEVBQUVDLElBQUksRUFBRSxHQUFHQyxPQUFPLEVBQUVDO0lBQzlCLHFCQUNFLDhEQUFDQztRQUNDSCxNQUFNQTtRQUNORCxXQUFXSCw4Q0FBRUEsQ0FDWCxnV0FDQUc7UUFFRkcsS0FBS0E7UUFDSixHQUFHRCxLQUFLOzs7Ozs7QUFHZjtBQUVGSixNQUFNTyxXQUFXLEdBQUc7QUFFSiIsInNvdXJjZXMiOlsid2VicGFjazovL2xpdi1hbmFseXplci1mcm9udGVuZC8uL3NyYy9jb21wb25lbnRzL3VpL2lucHV0LnRzeD9jOTgzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiXG5cbmltcG9ydCB7IGNuIH0gZnJvbSBcIkAvbGliL3V0aWxzXCJcblxuZXhwb3J0IGludGVyZmFjZSBJbnB1dFByb3BzXG4gIGV4dGVuZHMgUmVhY3QuSW5wdXRIVE1MQXR0cmlidXRlczxIVE1MSW5wdXRFbGVtZW50PiB7fVxuXG5jb25zdCBJbnB1dCA9IFJlYWN0LmZvcndhcmRSZWY8SFRNTElucHV0RWxlbWVudCwgSW5wdXRQcm9wcz4oXG4gICh7IGNsYXNzTmFtZSwgdHlwZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxpbnB1dFxuICAgICAgICB0eXBlPXt0eXBlfVxuICAgICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICAgIFwiZmxleCBoLTEwIHctZnVsbCByb3VuZGVkLW1kIGJvcmRlciBib3JkZXItaW5wdXQgYmctYmFja2dyb3VuZCBweC0zIHB5LTIgdGV4dC1zbSByaW5nLW9mZnNldC1iYWNrZ3JvdW5kIGZpbGU6Ym9yZGVyLTAgZmlsZTpiZy10cmFuc3BhcmVudCBmaWxlOnRleHQtc20gZmlsZTpmb250LW1lZGl1bSBwbGFjZWhvbGRlcjp0ZXh0LW11dGVkLWZvcmVncm91bmQgZm9jdXMtdmlzaWJsZTpvdXRsaW5lLW5vbmUgZm9jdXMtdmlzaWJsZTpyaW5nLTIgZm9jdXMtdmlzaWJsZTpyaW5nLXJpbmcgZm9jdXMtdmlzaWJsZTpyaW5nLW9mZnNldC0yIGRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZCBkaXNhYmxlZDpvcGFjaXR5LTUwXCIsXG4gICAgICAgICAgY2xhc3NOYW1lXG4gICAgICAgICl9XG4gICAgICAgIHJlZj17cmVmfVxuICAgICAgICB7Li4ucHJvcHN9XG4gICAgICAvPlxuICAgIClcbiAgfVxuKVxuSW5wdXQuZGlzcGxheU5hbWUgPSBcIklucHV0XCJcblxuZXhwb3J0IHsgSW5wdXQgfSJdLCJuYW1lcyI6WyJSZWFjdCIsImNuIiwiSW5wdXQiLCJmb3J3YXJkUmVmIiwiY2xhc3NOYW1lIiwidHlwZSIsInByb3BzIiwicmVmIiwiaW5wdXQiLCJkaXNwbGF5TmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/input.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/label.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/label.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Label: () => (/* binding */ Label)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-label */ \"(ssr)/./node_modules/@radix-ui/react-label/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst labelVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\");\nconst Label = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(labelVariants(), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\ui\\\\label.tsx\",\n        lineNumber: 16,\n        columnNumber: 3\n    }, undefined));\nLabel.displayName = _radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__.Root.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9sYWJlbC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQThCO0FBQ3lCO0FBQ1U7QUFFakM7QUFFaEMsTUFBTUksZ0JBQWdCRiw2REFBR0EsQ0FDdkI7QUFHRixNQUFNRyxzQkFBUUwsNkNBQWdCLENBSTVCLENBQUMsRUFBRU8sU0FBUyxFQUFFLEdBQUdDLE9BQU8sRUFBRUMsb0JBQzFCLDhEQUFDUix1REFBbUI7UUFDbEJRLEtBQUtBO1FBQ0xGLFdBQVdKLDhDQUFFQSxDQUFDQyxpQkFBaUJHO1FBQzlCLEdBQUdDLEtBQUs7Ozs7OztBQUdiSCxNQUFNTSxXQUFXLEdBQUdWLHVEQUFtQixDQUFDVSxXQUFXO0FBRW5DIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbGl2LWFuYWx5emVyLWZyb250ZW5kLy4vc3JjL2NvbXBvbmVudHMvdWkvbGFiZWwudHN4PzEzZWIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCJcbmltcG9ydCAqIGFzIExhYmVsUHJpbWl0aXZlIGZyb20gXCJAcmFkaXgtdWkvcmVhY3QtbGFiZWxcIlxuaW1wb3J0IHsgY3ZhLCB0eXBlIFZhcmlhbnRQcm9wcyB9IGZyb20gXCJjbGFzcy12YXJpYW5jZS1hdXRob3JpdHlcIlxuXG5pbXBvcnQgeyBjbiB9IGZyb20gXCJAL2xpYi91dGlsc1wiXG5cbmNvbnN0IGxhYmVsVmFyaWFudHMgPSBjdmEoXG4gIFwidGV4dC1zbSBmb250LW1lZGl1bSBsZWFkaW5nLW5vbmUgcGVlci1kaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWQgcGVlci1kaXNhYmxlZDpvcGFjaXR5LTcwXCJcbilcblxuY29uc3QgTGFiZWwgPSBSZWFjdC5mb3J3YXJkUmVmPFxuICBSZWFjdC5FbGVtZW50UmVmPHR5cGVvZiBMYWJlbFByaW1pdGl2ZS5Sb290PixcbiAgUmVhY3QuQ29tcG9uZW50UHJvcHNXaXRob3V0UmVmPHR5cGVvZiBMYWJlbFByaW1pdGl2ZS5Sb290PiAmXG4gICAgVmFyaWFudFByb3BzPHR5cGVvZiBsYWJlbFZhcmlhbnRzPlxuPigoeyBjbGFzc05hbWUsIC4uLnByb3BzIH0sIHJlZikgPT4gKFxuICA8TGFiZWxQcmltaXRpdmUuUm9vdFxuICAgIHJlZj17cmVmfVxuICAgIGNsYXNzTmFtZT17Y24obGFiZWxWYXJpYW50cygpLCBjbGFzc05hbWUpfVxuICAgIHsuLi5wcm9wc31cbiAgLz5cbikpXG5MYWJlbC5kaXNwbGF5TmFtZSA9IExhYmVsUHJpbWl0aXZlLlJvb3QuZGlzcGxheU5hbWVcblxuZXhwb3J0IHsgTGFiZWwgfSJdLCJuYW1lcyI6WyJSZWFjdCIsIkxhYmVsUHJpbWl0aXZlIiwiY3ZhIiwiY24iLCJsYWJlbFZhcmlhbnRzIiwiTGFiZWwiLCJmb3J3YXJkUmVmIiwiY2xhc3NOYW1lIiwicHJvcHMiLCJyZWYiLCJSb290IiwiZGlzcGxheU5hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/label.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/slider.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/slider.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Slider: () => (/* binding */ Slider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-slider */ \"(ssr)/./node_modules/@radix-ui/react-slider/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Slider auto */ \n\n\n\nconst Slider = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_slider__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex w-full touch-none select-none items-center\", className),\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_slider__WEBPACK_IMPORTED_MODULE_3__.Track, {\n                className: \"relative h-2 w-full grow overflow-hidden rounded-full bg-secondary\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_slider__WEBPACK_IMPORTED_MODULE_3__.Range, {\n                    className: \"absolute h-full bg-primary\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\ui\\\\slider.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\ui\\\\slider.tsx\",\n                lineNumber: 20,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_slider__WEBPACK_IMPORTED_MODULE_3__.Thumb, {\n                className: \"block h-5 w-5 rounded-full border-2 border-primary bg-background ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\"\n            }, void 0, false, {\n                fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\ui\\\\slider.tsx\",\n                lineNumber: 23,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\ui\\\\slider.tsx\",\n        lineNumber: 12,\n        columnNumber: 3\n    }, undefined));\nSlider.displayName = _radix_ui_react_slider__WEBPACK_IMPORTED_MODULE_3__.Root.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/slider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/tabs.tsx":
/*!************************************!*\
  !*** ./src/components/ui/tabs.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Tabs: () => (/* binding */ Tabs),\n/* harmony export */   TabsContent: () => (/* binding */ TabsContent),\n/* harmony export */   TabsList: () => (/* binding */ TabsList),\n/* harmony export */   TabsTrigger: () => (/* binding */ TabsTrigger)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-tabs */ \"(ssr)/./node_modules/@radix-ui/react-tabs/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\nconst Tabs = _radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_3__.Root;\nconst TabsList = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_3__.List, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\ui\\\\tabs.tsx\",\n        lineNumber: 12,\n        columnNumber: 3\n    }, undefined));\nTabsList.displayName = _radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_3__.List.displayName;\nconst TabsTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_3__.Trigger, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\ui\\\\tabs.tsx\",\n        lineNumber: 27,\n        columnNumber: 3\n    }, undefined));\nTabsTrigger.displayName = _radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_3__.Trigger.displayName;\nconst TabsContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_3__.Content, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\ui\\\\tabs.tsx\",\n        lineNumber: 42,\n        columnNumber: 3\n    }, undefined));\nTabsContent.displayName = _radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_3__.Content.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/tabs.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/toast.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/toast.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toast: () => (/* binding */ Toast),\n/* harmony export */   ToastAction: () => (/* binding */ ToastAction),\n/* harmony export */   ToastClose: () => (/* binding */ ToastClose),\n/* harmony export */   ToastDescription: () => (/* binding */ ToastDescription),\n/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider),\n/* harmony export */   ToastTitle: () => (/* binding */ ToastTitle),\n/* harmony export */   ToastViewport: () => (/* binding */ ToastViewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-toast */ \"(ssr)/./node_modules/@radix-ui/react-toast/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ ToastProvider,ToastViewport,Toast,ToastTitle,ToastDescription,ToastClose,ToastAction auto */ \n\n\n\n\n\nconst ToastProvider = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Provider;\nconst ToastViewport = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Viewport, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 16,\n        columnNumber: 3\n    }, undefined));\nToastViewport.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Viewport.displayName;\nconst toastVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full\", {\n    variants: {\n        variant: {\n            default: \"border bg-background text-foreground\",\n            destructive: \"destructive group border-destructive bg-destructive text-destructive-foreground\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nconst Toast = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(toastVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, undefined);\n});\nToast.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Root.displayName;\nconst ToastAction = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Action, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 62,\n        columnNumber: 3\n    }, undefined));\nToastAction.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Action.displayName;\nconst ToastClose = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Close, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600\", className),\n        \"toast-close\": \"\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n            lineNumber: 86,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 77,\n        columnNumber: 3\n    }, undefined));\nToastClose.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Close.displayName;\nconst ToastTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Title, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm font-semibold\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 95,\n        columnNumber: 3\n    }, undefined));\nToastTitle.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Title.displayName;\nconst ToastDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Description, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm opacity-90\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 107,\n        columnNumber: 3\n    }, undefined));\nToastDescription.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Description.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/toast.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/toaster.tsx":
/*!***************************************!*\
  !*** ./src/components/ui/toaster.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toaster: () => (/* binding */ Toaster)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/toast */ \"(ssr)/./src/components/ui/toast.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/use-toast */ \"(ssr)/./src/hooks/use-toast.tsx\");\n/* __next_internal_client_entry_do_not_use__ Toaster auto */ \n\n\nfunction Toaster() {\n    const { toasts } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_2__.useToast)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastProvider, {\n        children: [\n            toasts.map(function({ id, title, description, action, ...props }) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.Toast, {\n                    ...props,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid gap-1\",\n                            children: [\n                                title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastTitle, {\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\ui\\\\toaster.tsx\",\n                                    lineNumber: 22,\n                                    columnNumber: 25\n                                }, this),\n                                description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastDescription, {\n                                    children: description\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\ui\\\\toaster.tsx\",\n                                    lineNumber: 24,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\ui\\\\toaster.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 13\n                        }, this),\n                        action,\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastClose, {}, void 0, false, {\n                            fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\ui\\\\toaster.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, id, true, {\n                    fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\ui\\\\toaster.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 11\n                }, this);\n            }),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastViewport, {}, void 0, false, {\n                fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\ui\\\\toaster.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\components\\\\ui\\\\toaster.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/toaster.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/auth-context.tsx":
/*!***************************************!*\
  !*** ./src/contexts/auth-context.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiClient: () => (/* binding */ ApiClient),\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth,ApiClient auto */ \n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 应用启动时检查localStorage中的认证信息\n        checkAuthStatus();\n    }, []);\n    const checkAuthStatus = ()=>{\n        try {\n            const token = localStorage.getItem(\"auth_token\");\n            const userInfo = localStorage.getItem(\"user_info\");\n            if (token && userInfo) {\n                const parsedUserInfo = JSON.parse(userInfo);\n                setUser(parsedUserInfo);\n                setIsAuthenticated(true);\n                // 验证token是否仍然有效\n                validateToken(token);\n            }\n        } catch (error) {\n            console.error(\"检查认证状态失败:\", error);\n            logout();\n        }\n    };\n    const validateToken = async (token)=>{\n        try {\n            const response = await fetch(\"http://localhost:5105/api/Auth/validate\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    token\n                })\n            });\n            const data = await response.json();\n            if (!data.success || !data.isValid) {\n                // Token无效,尝试刷新\n                const refreshSuccess = await refreshToken();\n                if (!refreshSuccess) {\n                    logout();\n                }\n            }\n        } catch (error) {\n            console.error(\"Token验证失败:\", error);\n        // 网络错误时不立即注销，但可以标记需要验证\n        }\n    };\n    const login = (authData)=>{\n        localStorage.setItem(\"auth_token\", authData.accessToken);\n        localStorage.setItem(\"refresh_token\", authData.refreshToken);\n        localStorage.setItem(\"user_info\", JSON.stringify({\n            username: authData.username,\n            roles: authData.roles\n        }));\n        setUser({\n            username: authData.username,\n            roles: authData.roles\n        });\n        setIsAuthenticated(true);\n    };\n    const logout = ()=>{\n        localStorage.removeItem(\"auth_token\");\n        localStorage.removeItem(\"refresh_token\");\n        localStorage.removeItem(\"user_info\");\n        setUser(null);\n        setIsAuthenticated(false);\n    };\n    const refreshToken = async ()=>{\n        try {\n            const refreshTokenValue = localStorage.getItem(\"refresh_token\");\n            if (!refreshTokenValue) {\n                return false;\n            }\n            const response = await fetch(\"http://localhost:5105/api/Auth/refresh\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    refreshToken: refreshTokenValue\n                })\n            });\n            const data = await response.json();\n            if (data.success && response.ok) {\n                localStorage.setItem(\"auth_token\", data.accessToken);\n                localStorage.setItem(\"refresh_token\", data.refreshToken);\n                return true;\n            } else {\n                logout();\n                return false;\n            }\n        } catch (error) {\n            console.error(\"刷新Token失败:\", error);\n            logout();\n            return false;\n        }\n    };\n    const getAuthToken = ()=>{\n        return localStorage.getItem(\"auth_token\");\n    };\n    const value = {\n        user,\n        isAuthenticated,\n        login,\n        logout,\n        refreshToken,\n        getAuthToken\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\contexts\\\\auth-context.tsx\",\n        lineNumber: 157,\n        columnNumber: 5\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n// HTTP客户端工具，自动处理JWT认证\nclass ApiClient {\n    static async getAuthHeaders() {\n        const token = localStorage.getItem(\"auth_token\");\n        const headers = {\n            \"Content-Type\": \"application/json\"\n        };\n        if (token) {\n            headers[\"Authorization\"] = `Bearer ${token}`;\n        }\n        return headers;\n    }\n    static async get(url) {\n        const headers = await this.getAuthHeaders();\n        return fetch(url, {\n            method: \"GET\",\n            headers\n        });\n    }\n    static async post(url, data) {\n        const headers = await this.getAuthHeaders();\n        return fetch(url, {\n            method: \"POST\",\n            headers,\n            body: JSON.stringify(data)\n        });\n    }\n    static async postFormData(url, formData) {\n        const token = localStorage.getItem(\"auth_token\");\n        const headers = {};\n        if (token) {\n            headers[\"Authorization\"] = `Bearer ${token}`;\n        }\n        return fetch(url, {\n            method: \"POST\",\n            headers,\n            body: formData\n        });\n    }\n    static async put(url, data) {\n        const headers = await this.getAuthHeaders();\n        return fetch(url, {\n            method: \"PUT\",\n            headers,\n            body: JSON.stringify(data)\n        });\n    }\n    static async delete(url) {\n        const headers = await this.getAuthHeaders();\n        return fetch(url, {\n            method: \"DELETE\",\n            headers\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/auth-context.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/use-toast.tsx":
/*!*********************************!*\
  !*** ./src/hooks/use-toast.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ toast,useToast auto */ \nconst toastListeners = [];\nfunction toast(props) {\n    const id = Math.random().toString(36).substring(2, 9);\n    const toast = {\n        ...props,\n        id\n    };\n    toastListeners.forEach((listener)=>listener(toast));\n}\nfunction useToast() {\n    const [toasts, setToasts] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const listener = (toast)=>{\n            setToasts((prev)=>[\n                    ...prev,\n                    toast\n                ]);\n            setTimeout(()=>{\n                setToasts((prev)=>prev.filter((t)=>t.id !== toast.id));\n            }, 5000);\n        };\n        toastListeners.push(listener);\n        return ()=>{\n            const index = toastListeners.indexOf(listener);\n            if (index > -1) {\n                toastListeners.splice(index, 1);\n            }\n        };\n    }, []);\n    return {\n        toasts,\n        toast,\n        dismiss: (toastId)=>{\n            setToasts((prev)=>toastId ? prev.filter((t)=>t.id !== toastId) : []);\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/use-toast.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0QztBQUNKO0FBRWpDLFNBQVNFLEdBQUcsR0FBR0MsTUFBb0I7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QiIsInNvdXJjZXMiOlsid2VicGFjazovL2xpdi1hbmFseXplci1mcm9udGVuZC8uL3NyYy9saWIvdXRpbHMudHM/N2MxYyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjbHN4LCB0eXBlIENsYXNzVmFsdWUgfSBmcm9tIFwiY2xzeFwiXG5pbXBvcnQgeyB0d01lcmdlIH0gZnJvbSBcInRhaWx3aW5kLW1lcmdlXCJcblxuZXhwb3J0IGZ1bmN0aW9uIGNuKC4uLmlucHV0czogQ2xhc3NWYWx1ZVtdKSB7XG4gIHJldHVybiB0d01lcmdlKGNsc3goaW5wdXRzKSlcbn0iXSwibmFtZXMiOlsiY2xzeCIsInR3TWVyZ2UiLCJjbiIsImlucHV0cyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"e96218a772fc\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbGl2LWFuYWx5emVyLWZyb250ZW5kLy4vc3JjL2FwcC9nbG9iYWxzLmNzcz8wMWUyIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiZTk2MjE4YTc3MmZjXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/providers */ \"(rsc)/./src/components/providers.tsx\");\n/* harmony import */ var _components_ui_toaster__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/toaster */ \"(rsc)/./src/components/ui/toaster.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: \"LIV Analyzer\",\n    description: \"激光二极管光电流电压特性分析工具\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers__WEBPACK_IMPORTED_MODULE_2__.Providers, {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toaster__WEBPACK_IMPORTED_MODULE_3__.Toaster, {}, void 0, false, {\n                    fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 21,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\01LaserPackage\\\\software\\\\LIV_Analyzer\\\\CSharp_Version\\\\LIVAnalyzer.Web\\\\liv-analyzer-frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQU1NQTtBQUpnQjtBQUM0QjtBQUNEO0FBSTFDLE1BQU1HLFdBQXFCO0lBQ2hDQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFDO0FBRWMsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdUO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7UUFBUUMsd0JBQXdCO2tCQUN6Qyw0RUFBQ0M7WUFBS0MsV0FBV1osK0pBQWU7OzhCQUM5Qiw4REFBQ0MsNERBQVNBOzhCQUFFTTs7Ozs7OzhCQUNaLDhEQUFDTCwyREFBT0E7Ozs7Ozs7Ozs7Ozs7Ozs7QUFJaEIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9saXYtYW5hbHl6ZXItZnJvbnRlbmQvLi9zcmMvYXBwL2xheW91dC50c3g/NTdhOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSBcIm5leHRcIlxuaW1wb3J0IHsgSW50ZXIgfSBmcm9tIFwibmV4dC9mb250L2dvb2dsZVwiXG5pbXBvcnQgXCIuL2dsb2JhbHMuY3NzXCJcbmltcG9ydCB7IFByb3ZpZGVycyB9IGZyb20gXCJAL2NvbXBvbmVudHMvcHJvdmlkZXJzXCJcbmltcG9ydCB7IFRvYXN0ZXIgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL3RvYXN0ZXJcIlxuXG5jb25zdCBpbnRlciA9IEludGVyKHsgc3Vic2V0czogW1wibGF0aW5cIl0gfSlcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6IFwiTElWIEFuYWx5emVyXCIsXG4gIGRlc2NyaXB0aW9uOiBcIua/gOWFieS6jOaegeeuoeWFieeUtea1geeUteWOi+eJueaAp+WIhuaekOW3peWFt1wiLFxufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcbiAgY2hpbGRyZW4sXG59OiB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGVcbn0pIHtcbiAgcmV0dXJuIChcbiAgICA8aHRtbCBsYW5nPVwiemgtQ05cIiBzdXBwcmVzc0h5ZHJhdGlvbldhcm5pbmc+XG4gICAgICA8Ym9keSBjbGFzc05hbWU9e2ludGVyLmNsYXNzTmFtZX0+XG4gICAgICAgIDxQcm92aWRlcnM+e2NoaWxkcmVufTwvUHJvdmlkZXJzPlxuICAgICAgICA8VG9hc3RlciAvPlxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKVxufSJdLCJuYW1lcyI6WyJpbnRlciIsIlByb3ZpZGVycyIsIlRvYXN0ZXIiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsInN1cHByZXNzSHlkcmF0aW9uV2FybmluZyIsImJvZHkiLCJjbGFzc05hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\01LaserPackage\software\LIV_Analyzer\CSharp_Version\LIVAnalyzer.Web\liv-analyzer-frontend\src\app\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\01LaserPackage\software\LIV_Analyzer\CSharp_Version\LIVAnalyzer.Web\liv-analyzer-frontend\src\app\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/components/providers.tsx":
/*!**************************************!*\
  !*** ./src/components/providers.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\01LaserPackage\software\LIV_Analyzer\CSharp_Version\LIVAnalyzer.Web\liv-analyzer-frontend\src\components\providers.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\01LaserPackage\software\LIV_Analyzer\CSharp_Version\LIVAnalyzer.Web\liv-analyzer-frontend\src\components\providers.tsx#Providers`);


/***/ }),

/***/ "(rsc)/./src/components/ui/toaster.tsx":
/*!***************************************!*\
  !*** ./src/components/ui/toaster.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Toaster: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\01LaserPackage\software\LIV_Analyzer\CSharp_Version\LIVAnalyzer.Web\liv-analyzer-frontend\src\components\ui\toaster.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\01LaserPackage\software\LIV_Analyzer\CSharp_Version\LIVAnalyzer.Web\liv-analyzer-frontend\src\components\ui\toaster.tsx#Toaster`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@radix-ui","vendor-chunks/@tanstack","vendor-chunks/lucide-react","vendor-chunks/@swc","vendor-chunks/tailwind-merge","vendor-chunks/next-themes","vendor-chunks/clsx","vendor-chunks/class-variance-authority"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=E%3A%5C01LaserPackage%5Csoftware%5CLIV_Analyzer%5CCSharp_Version%5CLIVAnalyzer.Web%5Cliv-analyzer-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5C01LaserPackage%5Csoftware%5CLIV_Analyzer%5CCSharp_Version%5CLIVAnalyzer.Web%5Cliv-analyzer-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();