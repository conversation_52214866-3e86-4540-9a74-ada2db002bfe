@echo off
cd /d "E:\01LaserPackage\software\LIV_Analyzer\CSharp_Version\LIVAnalyzer.UI\bin\Debug\net6.0-windows"

echo Starting LIV Analyzer...
echo.

REM Ensure .NET runtime is available
dotnet --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: .NET runtime not found. Please install .NET 6.0 or later.
    pause
    exit /b 1
)

REM Check if the executable exists
if not exist "LIVAnalyzer.dll" (
    echo ERROR: LIVAnalyzer.dll not found. Please build the project first.
    pause
    exit /b 1
)

REM Run using dotnet directly instead of the exe
dotnet LIVAnalyzer.dll

pause