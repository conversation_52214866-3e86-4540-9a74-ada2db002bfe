<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0-windows7.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <IsPackable>false</IsPackable>
    <UseWPF>true</UseWPF>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.11.1" />
    <PackageReference Include="xunit" Version="2.4.2" />
    <PackageReference Include="xunit.runner.visualstudio" Version="2.4.5">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Moq" Version="4.20.69" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\LIVAnalyzer.Core\LIVAnalyzer.Core.csproj" />
    <ProjectReference Include="..\LIVAnalyzer.Data\LIVAnalyzer.Data.csproj" />
    <ProjectReference Include="..\LIVAnalyzer.Models\LIVAnalyzer.Models.csproj" />
    <ProjectReference Include="..\LIVAnalyzer.Services\LIVAnalyzer.Services.csproj" />
    <ProjectReference Include="..\LIVAnalyzer.UI\LIVAnalyzer.UI.csproj" />
  </ItemGroup>

</Project>