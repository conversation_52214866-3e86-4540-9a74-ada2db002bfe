using CommunityToolkit.Mvvm.ComponentModel;
using LIVAnalyzer.Models;

namespace LIVAnalyzer.UI.ViewModels
{
    public partial class FileViewModel : ObservableObject
    {
        public FileViewModel(LIVMeasurementData data)
        {
            Data = data;
            FileName = data.FileName;
            IsSelected = true; // 默认选中
        }
        
        public LIVMeasurementData Data { get; }
        
        public string FileName { get; }
        
        [ObservableProperty]
        private bool isSelected;
    }
}