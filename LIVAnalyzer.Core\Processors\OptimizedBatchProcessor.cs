using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Threading.Tasks.Dataflow;
using LIVAnalyzer.Core.Exporters;
using LIVAnalyzer.Data.Loaders;
using LIVAnalyzer.Models;
using LIVAnalyzer.Services.Logging;

namespace LIVAnalyzer.Core.Processors
{
    /// <summary>
    /// 优化的批量处理器 - 使用并行处理和管道模式
    /// </summary>
    public class OptimizedBatchProcessor : BatchProcessor
    {
        private readonly OptimizedLIVDataProcessor _optimizedProcessor;
        private readonly SemaphoreSlim _semaphore;
        private readonly int _maxDegreeOfParallelism;
        
        public OptimizedBatchProcessor() : base()
        {
            _optimizedProcessor = new OptimizedLIVDataProcessor();
            _maxDegreeOfParallelism = Math.Min(Environment.ProcessorCount, 8);
            _semaphore = new SemaphoreSlim(_maxDegreeOfParallelism);
        }
        
        /// <summary>
        /// 优化的批量处理方法 - 使用TPL Dataflow管道
        /// </summary>
        public new async Task<BatchProcessResult> ProcessBatchAsync(
            string folderPath, 
            double i1Current, 
            double i2Current, 
            IProgress<BatchProgressInfo>? progress = null)
        {
            try
            {
                LoggingService.LogInformation($"开始优化批量处理文件夹: {folderPath}");
                
                var result = new BatchProcessResult();
                var progressInfo = new BatchProgressInfo();
                
                // 搜索所有支持的文件
                var supportedFiles = GetSupportedFilesParallel(folderPath);
                progressInfo.TotalFiles = supportedFiles.Count;
                progressInfo.Status = "扫描文件完成，开始并行处理...";
                progress?.Report(progressInfo);
                
                if (!supportedFiles.Any())
                {
                    result.ErrorMessage = "未找到支持的数据文件（Excel或CSV格式）";
                    return result;
                }
                
                // 创建处理管道
                var processedFiles = new ConcurrentBag<FileDataModel>();
                var failedFiles = new ConcurrentBag<(string path, string error)>();
                var processedCount = 0;
                
                // 设置数据流管道
                var loadBlock = new TransformBlock<string, (string path, LIVMeasurementData? data)>(
                    async filePath => await LoadFileAsync(filePath),
                    new ExecutionDataflowBlockOptions
                    {
                        MaxDegreeOfParallelism = _maxDegreeOfParallelism,
                        BoundedCapacity = _maxDegreeOfParallelism * 2
                    });
                
                var processBlock = new TransformBlock<(string path, LIVMeasurementData? data), FileDataModel?>(
                    async item => await ProcessDataAsync(item.path, item.data, i1Current, i2Current),
                    new ExecutionDataflowBlockOptions
                    {
                        MaxDegreeOfParallelism = _maxDegreeOfParallelism,
                        BoundedCapacity = _maxDegreeOfParallelism * 2
                    });
                
                var saveBlock = new ActionBlock<FileDataModel?>(
                    fileModel =>
                    {
                        if (fileModel != null)
                        {
                            processedFiles.Add(fileModel);
                            result.ProcessedFiles++;
                        }
                        
                        var count = Interlocked.Increment(ref processedCount);
                        progressInfo.CurrentFile = count;
                        progressInfo.CurrentFileName = fileModel?.FileName ?? "处理失败";
                        progressInfo.Status = $"已处理: {count}/{supportedFiles.Count}";
                        progress?.Report(progressInfo);
                    },
                    new ExecutionDataflowBlockOptions
                    {
                        MaxDegreeOfParallelism = 1 // 单线程更新进度
                    });
                
                // 链接管道
                var linkOptions = new DataflowLinkOptions { PropagateCompletion = true };
                loadBlock.LinkTo(processBlock, linkOptions);
                processBlock.LinkTo(saveBlock, linkOptions);
                
                // 开始处理
                foreach (var file in supportedFiles)
                {
                    await loadBlock.SendAsync(file);
                }
                loadBlock.Complete();
                
                // 等待处理完成
                await saveBlock.Completion;
                
                // 生成输出文件
                if (processedFiles.Any())
                {
                    progressInfo.Status = "正在生成Excel报告...";
                    progress?.Report(progressInfo);
                    
                    var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                    var outputFileName = $"批量处理结果_{timestamp}.xlsx";
                    var outputPath = Path.Combine(folderPath, outputFileName);
                    
                    var exporter = new ExcelDataExporter();
                    await exporter.ExportDataAsync(processedFiles.ToList(), outputPath);
                    
                    result.OutputFilePath = outputPath;
                    result.IsSuccess = true;
                    
                    LoggingService.LogInformation($"批量处理完成，结果已保存到: {outputPath}");
                }
                
                // 设置结果详情
                result.ProcessedFileNames = processedFiles.Select(f => f.FileName).ToList();
                result.FailedFileNames = failedFiles.Select(f => Path.GetFileName(f.path)).ToList();
                result.TotalFiles = supportedFiles.Count;
                result.FailedFiles = failedFiles.Count;
                
                progressInfo.Status = "批量处理完成";
                progress?.Report(progressInfo);
                
                return result;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "优化批量处理过程中发生错误");
                return new BatchProcessResult
                {
                    IsSuccess = false,
                    ErrorMessage = ex.Message
                };
            }
        }
        
        /// <summary>
        /// 并行搜索支持的文件
        /// </summary>
        private List<string> GetSupportedFilesParallel(string folderPath)
        {
            var supportedExtensions = new[] { ".xlsx", ".xls", ".csv" };
            var files = new ConcurrentBag<string>();
            
            try
            {
                // 并行搜索各种扩展名的文件
                Parallel.ForEach(supportedExtensions, extension =>
                {
                    var pattern = $"*{extension}";
                    var foundFiles = Directory.GetFiles(folderPath, pattern, SearchOption.AllDirectories)
                        .Where(f => !Path.GetFileName(f).StartsWith("~") && 
                                   !Path.GetFileName(f).StartsWith(".") &&
                                   !Path.GetFileName(f).Contains("批量处理结果"));
                    
                    foreach (var file in foundFiles)
                    {
                        files.Add(file);
                    }
                });
                
                LoggingService.LogInformation($"找到 {files.Count} 个支持的文件");
                return files.OrderBy(f => f).ToList();
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"扫描文件夹失败: {folderPath}");
                return new List<string>();
            }
        }
        
        /// <summary>
        /// 异步加载文件
        /// </summary>
        private async Task<(string path, LIVMeasurementData? data)> LoadFileAsync(string filePath)
        {
            await _semaphore.WaitAsync();
            try
            {
                var extension = Path.GetExtension(filePath).ToLower();
                LIVMeasurementData? data = null;
                
                if (extension == ".csv")
                {
                    var loader = new CsvDataLoader();
                    var validationResult = loader.ValidateFile(filePath);
                    if (validationResult.IsValid)
                    {
                        data = await loader.LoadCsvDataAsync(filePath);
                    }
                }
                else if (extension == ".xlsx" || extension == ".xls")
                {
                    var loader = new ExcelDataLoader();
                    var validationResult = loader.ValidateFile(filePath);
                    if (validationResult.IsValid)
                    {
                        data = await loader.LoadExcelDataAsync(filePath);
                    }
                }
                
                return (filePath, data);
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"加载文件失败: {filePath}");
                return (filePath, null);
            }
            finally
            {
                _semaphore.Release();
            }
        }
        
        /// <summary>
        /// 异步处理数据
        /// </summary>
        private async Task<FileDataModel?> ProcessDataAsync(
            string filePath, 
            LIVMeasurementData? data, 
            double i1Current, 
            double i2Current)
        {
            if (data == null || !HasValidData(data))
            {
                LoggingService.LogWarning($"文件无有效数据: {filePath}");
                return null;
            }
            
            return await Task.Run(() =>
            {
                // 清除缓存以确保每个文件独立计算
                _optimizedProcessor.ClearCache();
                
                // 使用优化的处理器计算参数
                var parameters = _optimizedProcessor.CalculateParameters(data);
                data.Parameters = parameters;
                data.IsProcessed = true;
                
                // 计算发散角参数（如果有发散角数据）
                if ((data.HorizontalDivergenceData != null && data.HorizontalDivergenceData.Any()) ||
                    (data.VerticalDivergenceData != null && data.VerticalDivergenceData.Any()))
                {
                    var divergenceResults = _optimizedProcessor.CalculateDivergenceParameters(data);
                    data.DivergenceResults = divergenceResults;
                }
                
                // 计算I1和I2对应的参数
                if (parameters != null && data.CurrentPowerData.Any() && data.CurrentVoltageData.Any())
                {
                    // 并行计算I1和I2的功率和电压
                    var i1PowerTask = Task.Run(() => CalculatePowerAtCurrentOptimized(data, i1Current));
                    var i1VoltageTask = Task.Run(() => CalculateVoltageAtCurrentOptimized(data, i1Current));
                    var i2PowerTask = Task.Run(() => CalculatePowerAtCurrentOptimized(data, i2Current));
                    var i2VoltageTask = Task.Run(() => CalculateVoltageAtCurrentOptimized(data, i2Current));

                    Task.WaitAll(i1PowerTask, i1VoltageTask, i2PowerTask, i2VoltageTask);

                    // 设置I1参数
                    parameters.I1Current = i1Current;
                    parameters.I1Power = i1PowerTask.Result;
                    parameters.I1Voltage = i1VoltageTask.Result;

                    // 计算I1效率
                    if (parameters.I1Power > 0 && parameters.I1Voltage > 0 && i1Current > 0)
                    {
                        parameters.I1Efficiency = (parameters.I1Power / (i1Current * parameters.I1Voltage)) * 100;
                    }

                    // 设置I2参数
                    parameters.I2Current = i2Current;
                    parameters.I2Power = i2PowerTask.Result;
                    parameters.I2Voltage = i2VoltageTask.Result;

                    // 计算I2效率
                    if (parameters.I2Power > 0 && parameters.I2Voltage > 0 && i2Current > 0)
                    {
                        parameters.I2Efficiency = (parameters.I2Power / (i2Current * parameters.I2Voltage)) * 100;
                    }
                }
                
                return new FileDataModel(data);
            });
        }
        
        /// <summary>
        /// 优化的功率计算方法
        /// </summary>
        private double CalculatePowerAtCurrentOptimized(LIVMeasurementData data, double targetCurrent)
        {
            if (!data.CurrentPowerData.Any())
                return 0;

            // 创建查找表以提高性能
            var powerLookup = data.CurrentPowerData
                .OrderBy(p => p.X)
                .ToList();

            // 使用二分查找快速定位
            int left = 0, right = powerLookup.Count - 1;

            while (left <= right)
            {
                int mid = left + (right - left) / 2;

                if (Math.Abs(powerLookup[mid].X - targetCurrent) < 0.001)
                {
                    return powerLookup[mid].Y;
                }

                if (powerLookup[mid].X < targetCurrent)
                    left = mid + 1;
                else
                    right = mid - 1;
            }

            // 线性插值
            if (right >= 0 && left < powerLookup.Count)
            {
                var leftPoint = powerLookup[right];
                var rightPoint = powerLookup[left];

                if (Math.Abs(rightPoint.X - leftPoint.X) > 1e-10)
                {
                    var ratio = (targetCurrent - leftPoint.X) / (rightPoint.X - leftPoint.X);
                    return leftPoint.Y + ratio * (rightPoint.Y - leftPoint.Y);
                }
            }

            return 0;
        }

        /// <summary>
        /// 优化的电压计算方法
        /// </summary>
        private double CalculateVoltageAtCurrentOptimized(LIVMeasurementData data, double targetCurrent)
        {
            if (!data.CurrentVoltageData.Any())
                return 0;

            // 创建查找表以提高性能
            var voltageLookup = data.CurrentVoltageData
                .OrderBy(p => p.X)
                .ToList();

            // 使用二分查找快速定位
            int left = 0, right = voltageLookup.Count - 1;

            while (left <= right)
            {
                int mid = left + (right - left) / 2;

                if (Math.Abs(voltageLookup[mid].X - targetCurrent) < 0.001)
                {
                    return voltageLookup[mid].Y;
                }

                if (voltageLookup[mid].X < targetCurrent)
                    left = mid + 1;
                else
                    right = mid - 1;
            }

            // 线性插值
            if (right >= 0 && left < voltageLookup.Count)
            {
                var leftPoint = voltageLookup[right];
                var rightPoint = voltageLookup[left];

                if (Math.Abs(rightPoint.X - leftPoint.X) > 1e-10)
                {
                    var ratio = (targetCurrent - leftPoint.X) / (rightPoint.X - leftPoint.X);
                    return leftPoint.Y + ratio * (rightPoint.Y - leftPoint.Y);
                }
            }

            return 0;
        }
        
        /// <summary>
        /// 检查数据是否有效
        /// </summary>
        private bool HasValidData(LIVMeasurementData data)
        {
            return data.CurrentPowerData.Any() || 
                   data.CurrentVoltageData.Any() || 
                   data.WavelengthIntensityData.Any();
        }
    }
}