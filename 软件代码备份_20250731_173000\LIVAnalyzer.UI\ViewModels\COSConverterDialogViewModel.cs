using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using LIVAnalyzer.Core.Processors;
using LIVAnalyzer.Services.Configuration;
using LIVAnalyzer.Services.Logging;
using Microsoft.Win32;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;

namespace LIVAnalyzer.UI.ViewModels
{
    public partial class COSConverterDialogViewModel : ObservableObject
    {
        private readonly COSToExcelConverter _converter;
        private readonly ConfigurationManager _config;
        private CancellationTokenSource? _cancellationTokenSource;

        public COSConverterDialogViewModel()
        {
            _converter = new COSToExcelConverter();
            _config = ConfigurationManager.Instance;
            
            InitializeCommands();
            InitializeProperties();
        }

        #region Properties

        [ObservableProperty]
        private string sourceFolder = "";

        [ObservableProperty]
        private string outputFolder = "";

        [ObservableProperty]
        private bool recursiveSearch = true;

        [ObservableProperty]
        private bool useSameFolder = true;

        [ObservableProperty]
        private bool isOutputFolderEnabled = false;

        [ObservableProperty]
        private string logText = "";

        [ObservableProperty]
        private bool isConverting = false;

        [ObservableProperty]
        private string conversionStatus = "";

        [ObservableProperty]
        private string convertButtonText = "开始转换";

        [ObservableProperty]
        private bool canStartConversion = false;

        [ObservableProperty]
        private bool showProgressDialog = false;

        [ObservableProperty]
        private double conversionProgress = 0;

        [ObservableProperty]
        private string conversionProgressText = "";

        #endregion

        #region Commands

        public IRelayCommand BrowseSourceFolderCommand { get; private set; }
        public IRelayCommand BrowseOutputFolderCommand { get; private set; }
        public IAsyncRelayCommand StartConversionCommand { get; private set; }
        public IRelayCommand CancelConversionCommand { get; private set; }
        public IRelayCommand CloseCommand { get; private set; }

        #endregion

        #region Initialization

        private void InitializeCommands()
        {
            BrowseSourceFolderCommand = new RelayCommand(BrowseSourceFolder);
            BrowseOutputFolderCommand = new RelayCommand(BrowseOutputFolder);
            StartConversionCommand = new AsyncRelayCommand(StartConversionAsync);
            CancelConversionCommand = new RelayCommand(CancelConversion);
            CloseCommand = new RelayCommand(CloseDialog);
        }

        private void InitializeProperties()
        {
            // 监听属性变化
            PropertyChanged += (s, e) =>
            {
                switch (e.PropertyName)
                {
                    case nameof(UseSameFolder):
                        OnUseSameFolderChanged();
                        break;
                    case nameof(SourceFolder):
                    case nameof(OutputFolder):
                        UpdateCanStartConversion();
                        break;
                }
            };

            // 初始状态
            OnUseSameFolderChanged();
            LogMessage("COS文件转换工具已就绪");
        }

        #endregion

        #region Command Implementations

        private void BrowseSourceFolder()
        {
            var dialog = new System.Windows.Forms.FolderBrowserDialog
            {
                Description = "选择包含COS文件的源文件夹",
                UseDescriptionForTitle = true,
                SelectedPath = GetDefaultDirectory()
            };

            if (dialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                SourceFolder = dialog.SelectedPath;
                _config.SetLastFolderPath(dialog.SelectedPath);
                
                if (UseSameFolder)
                {
                    OutputFolder = dialog.SelectedPath;
                }
                
                LogMessage($"已选择源文件夹: {dialog.SelectedPath}");
            }
        }

        private void BrowseOutputFolder()
        {
            var dialog = new System.Windows.Forms.FolderBrowserDialog
            {
                Description = "选择输出文件夹",
                UseDescriptionForTitle = true,
                SelectedPath = GetDefaultDirectory()
            };

            if (dialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                OutputFolder = dialog.SelectedPath;
                _config.SetLastFolderPath(dialog.SelectedPath);
                LogMessage($"已选择输出文件夹: {dialog.SelectedPath}");
            }
        }

        private async Task StartConversionAsync()
        {
            // 验证输入
            if (string.IsNullOrEmpty(SourceFolder))
            {
                MessageBox.Show("请选择源文件夹！", "警告", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            if (string.IsNullOrEmpty(OutputFolder))
            {
                MessageBox.Show("请选择输出文件夹！", "警告", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            if (!Directory.Exists(SourceFolder))
            {
                MessageBox.Show("源文件夹不存在！", "警告", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                // 创建输出文件夹
                Directory.CreateDirectory(OutputFolder);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"无法创建输出文件夹：\n{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                return;
            }

            // 开始转换
            IsConverting = true;
            ShowProgressDialog = true;
            ConvertButtonText = "转换中...";
            CanStartConversion = false;
            
            LogText = ""; // 清空日志
            LogMessage("开始转换...");
            LogMessage($"源文件夹: {SourceFolder}");
            LogMessage($"输出文件夹: {OutputFolder}");
            LogMessage($"递归搜索: {(RecursiveSearch ? "是" : "否")}");

            _cancellationTokenSource = new CancellationTokenSource();

            try
            {
                var progress = new Progress<(int Current, string Message)>(UpdateProgress);
                
                var result = await _converter.ConvertBatchFilesAsync(
                    SourceFolder, 
                    OutputFolder, 
                    RecursiveSearch, 
                    progress
                );

                // 转换完成
                LogMessage(new string('=', 50));
                LogMessage("转换完成!");
                LogMessage($"成功: {result.Success} 个文件");
                LogMessage($"失败: {result.Failed} 个文件");
                LogMessage($"总计: {result.Success + result.Failed} 个文件");

                if (result.Success > 0)
                {
                    var message = $"转换完成！\n成功转换 {result.Success} 个文件\n输出目录：{OutputFolder}";
                    if (result.Failed > 0)
                    {
                        message += $"\n\n失败 {result.Failed} 个文件，请查看日志了解详情";
                    }
                    MessageBox.Show(message, "转换完成", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    MessageBox.Show("所有文件转换失败！请查看日志了解详情。", "转换失败", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (OperationCanceledException)
            {
                LogMessage("转换已取消");
                MessageBox.Show("转换已取消", "信息", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "转换过程中发生错误");
                LogMessage($"转换过程中发生错误：{ex.Message}");
                MessageBox.Show($"转换过程中发生错误：\n{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                // 恢复状态
                IsConverting = false;
                ShowProgressDialog = false;
                ConvertButtonText = "开始转换";
                CanStartConversion = true;
                ConversionProgress = 0;
                ConversionProgressText = "";
                _cancellationTokenSource?.Dispose();
                _cancellationTokenSource = null;
            }
        }

        private void CancelConversion()
        {
            _cancellationTokenSource?.Cancel();
        }

        private void CloseDialog()
        {
            if (IsConverting)
            {
                var result = MessageBox.Show(
                    "转换正在进行中，确定要关闭吗？", 
                    "确认关闭", 
                    MessageBoxButton.YesNo, 
                    MessageBoxImage.Question
                );
                
                if (result == MessageBoxResult.Yes)
                {
                    CancelConversion();
                    Application.Current.Windows.OfType<Window>().FirstOrDefault(w => w.DataContext == this)?.Close();
                }
            }
            else
            {
                Application.Current.Windows.OfType<Window>().FirstOrDefault(w => w.DataContext == this)?.Close();
            }
        }

        #endregion

        #region Helper Methods

        private void OnUseSameFolderChanged()
        {
            IsOutputFolderEnabled = !UseSameFolder;
            
            if (UseSameFolder && !string.IsNullOrEmpty(SourceFolder))
            {
                OutputFolder = SourceFolder;
            }
            
            UpdateCanStartConversion();
        }

        private void UpdateCanStartConversion()
        {
            CanStartConversion = !IsConverting && 
                                !string.IsNullOrEmpty(SourceFolder) && 
                                !string.IsNullOrEmpty(OutputFolder);
        }

        private string GetDefaultDirectory()
        {
            var lastFolder = _config.GetLastFolderPath();
            return !string.IsNullOrEmpty(lastFolder) && Directory.Exists(lastFolder) ? lastFolder : "";
        }

        private void LogMessage(string message)
        {
            var timestamp = DateTime.Now.ToString("HH:mm:ss");
            LogText += $"[{timestamp}] {message}\n";
        }

        private void UpdateProgress((int Current, string Message) progress)
        {
            ConversionStatus = progress.Message;
            ConversionProgressText = progress.Message;
            LogMessage(progress.Message);
            
            // 这里可以根据需要计算进度百分比
            // 简单示例：假设总数可以从converter中获取
        }

        #endregion
    }
}