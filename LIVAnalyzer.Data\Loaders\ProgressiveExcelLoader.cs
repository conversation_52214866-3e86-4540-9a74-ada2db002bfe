using LIVAnalyzer.Data.Sampling;
using LIVAnalyzer.Data.Interfaces;
using LIVAnalyzer.Models;
using OfficeOpenXml;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;

namespace LIVAnalyzer.Data.Loaders
{
    /// <summary>
    /// 渐进式Excel数据加载器，支持快速预览和详细加载
    /// </summary>
    public class ProgressiveExcelLoader
    {
        private readonly SmartSampler _sampler;
        private readonly SamplingConfig _samplingConfig;

        public ProgressiveExcelLoader(SamplingConfig? samplingConfig = null)
        {
            _samplingConfig = samplingConfig ?? new SamplingConfig();
            _sampler = new SmartSampler(_samplingConfig);
            
            // 设置EPPlus许可证模式
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
        }

        /// <summary>
        /// 渐进式加载Excel数据
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="progress">进度报告器</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>完整的LIV测量数据</returns>
        public async Task<LIVMeasurementData?> LoadProgressivelyAsync(
            string filePath, 
            IProgress<LoadingProgress>? progress = null,
            CancellationToken cancellationToken = default)
        {
            var fileName = Path.GetFileName(filePath);
            
            try
            {
                // 验证文件
                var (isValid, errorMessage) = ValidateFile(filePath);
                if (!isValid)
                {
                    throw new InvalidOperationException(errorMessage);
                }

                progress?.Report(new LoadingProgress
                {
                    Stage = "正在打开文件...",
                    Percentage = 5,
                    FileName = fileName
                });

                var finalData = new LIVMeasurementData { FileName = fileName };

                using var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read, FileShare.Read, 
                    bufferSize: 65536, useAsync: true);
                using var package = new ExcelPackage(fileStream);

                // 第一阶段：快速采样加载
                progress?.Report(new LoadingProgress
                {
                    Stage = "生成数据预览...",
                    Percentage = 15,
                    FileName = fileName
                });

                var sampledData = await LoadSampledDataAsync(package, cancellationToken);
                if (sampledData != null)
                {
                    finalData = sampledData;
                    
                    // 报告预览数据
                    progress?.Report(new LoadingProgress
                    {
                        Stage = "显示数据预览",
                        Percentage = 35,
                        PartialData = CloneData(finalData),
                        IsPartialData = true,
                        FileName = fileName,
                        LoadedDataPoints = GetDataPointCount(finalData),
                        DetailMessage = "正在加载详细数据..."
                    });

                    // 短暂延迟，让用户看到预览效果
                    await Task.Delay(200, cancellationToken);
                }

                // 第二阶段：并行加载详细数据
                progress?.Report(new LoadingProgress
                {
                    Stage = "加载详细数据...",
                    Percentage = 45,
                    FileName = fileName
                });

                var detailedData = await LoadDetailedDataAsync(package, finalData, progress, cancellationToken);
                if (detailedData != null)
                {
                    finalData = detailedData;
                }

                // 最终完成
                progress?.Report(new LoadingProgress
                {
                    Stage = "加载完成",
                    Percentage = 100,
                    PartialData = CloneData(finalData),
                    IsPartialData = false,
                    FileName = fileName,
                    LoadedDataPoints = GetDataPointCount(finalData),
                    TotalDataPoints = GetDataPointCount(finalData),
                    DetailMessage = "数据加载完成"
                });

                return finalData;
            }
            catch (OperationCanceledException)
            {
                progress?.Report(new LoadingProgress
                {
                    Stage = "加载已取消",
                    Percentage = 0,
                    FileName = fileName
                });
                throw;
            }
            catch (Exception ex)
            {
                progress?.Report(new LoadingProgress
                {
                    Stage = "加载失败",
                    Percentage = 0,
                    FileName = fileName,
                    DetailMessage = ex.Message
                });
                throw new InvalidOperationException($"加载Excel文件失败 ({fileName}): {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 第一阶段：采样加载
        /// </summary>
        private async Task<LIVMeasurementData?> LoadSampledDataAsync(ExcelPackage package, CancellationToken cancellationToken)
        {
            return await Task.Run(() =>
            {
                var data = new LIVMeasurementData();

                // 并行采样加载三个主要工作表
                var tasks = new[]
                {
                    Task.Run(() => LoadWorksheetSampled(package, "power", data, WorksheetType.Power), cancellationToken),
                    Task.Run(() => LoadWorksheetSampled(package, "voltage", data, WorksheetType.Voltage), cancellationToken),
                    Task.Run(() => LoadWorksheetSampled(package, "wavelength", data, WorksheetType.Wavelength), cancellationToken)
                };

                Task.WaitAll(tasks, cancellationToken);
                return data;
            }, cancellationToken);
        }

        /// <summary>
        /// 第二阶段：详细数据加载
        /// </summary>
        private async Task<LIVMeasurementData?> LoadDetailedDataAsync(
            ExcelPackage package, 
            LIVMeasurementData initialData,
            IProgress<LoadingProgress>? progress,
            CancellationToken cancellationToken)
        {
            var detailedData = CloneData(initialData);

            // 分批加载剩余数据
            var worksheets = new[] { "power", "voltage", "wavelength" };
            var completedWorksheets = 0;

            foreach (var worksheetName in worksheets)
            {
                if (cancellationToken.IsCancellationRequested) break;

                try
                {
                    await LoadWorksheetDetailed(package, worksheetName, detailedData, cancellationToken);
                    
                    completedWorksheets++;
                    var progressPercentage = 50 + (completedWorksheets * 15); // 50% to 95%

                    progress?.Report(new LoadingProgress
                    {
                        Stage = $"完善 {worksheetName} 数据...",
                        Percentage = progressPercentage,
                        PartialData = CloneData(detailedData),
                        IsPartialData = completedWorksheets < worksheets.Length,
                        LoadedDataPoints = GetDataPointCount(detailedData)
                    });

                    // 让UI有时间更新
                    await Task.Delay(50, cancellationToken);
                }
                catch (Exception ex)
                {
                    // 记录错误但继续处理其他工作表
                    Console.WriteLine($"加载工作表 {worksheetName} 时出错: {ex.Message}");
                }
            }

            // 加载发散角数据（可选）
            try
            {
                await Task.Run(() => LoadDivergenceDataDetailed(package, detailedData), cancellationToken);
                progress?.Report(new LoadingProgress
                {
                    Stage = "完善发散角数据...",
                    Percentage = 98,
                    PartialData = CloneData(detailedData),
                    IsPartialData = false,
                    LoadedDataPoints = GetDataPointCount(detailedData)
                });
            }
            catch
            {
                // 发散角数据是可选的，失败不影响主要功能
            }

            return detailedData;
        }

        /// <summary>
        /// 采样加载工作表
        /// </summary>
        private void LoadWorksheetSampled(ExcelPackage package, string worksheetName, LIVMeasurementData data, WorksheetType type)
        {
            var worksheet = package.Workbook.Worksheets[worksheetName];
            if (worksheet?.Dimension == null) return;

            try
            {
                // 生成采样策略
                var samplingResult = _sampler.GenerateSamplingResult(worksheet, hasHeader: true);
                if (!samplingResult.SampledIndices.Any()) return;

                // 根据工作表类型加载对应数据
                switch (type)
                {
                    case WorksheetType.Power:
                        LoadPowerDataWithIndices(worksheet, data, samplingResult.SampledIndices);
                        break;
                    case WorksheetType.Voltage:
                        LoadVoltageDataWithIndices(worksheet, data, samplingResult.SampledIndices);
                        break;
                    case WorksheetType.Wavelength:
                        LoadWavelengthDataWithIndices(worksheet, data, samplingResult.SampledIndices);
                        break;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"采样加载工作表 {worksheetName} 失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 详细加载工作表剩余数据
        /// </summary>
        private async Task LoadWorksheetDetailed(ExcelPackage package, string worksheetName, LIVMeasurementData data, CancellationToken cancellationToken)
        {
            var worksheet = package.Workbook.Worksheets[worksheetName];
            if (worksheet?.Dimension == null) return;

            await Task.Run(() =>
            {
                try
                {
                    // 重新生成采样结果获取剩余索引
                    var samplingResult = _sampler.GenerateSamplingResult(worksheet, hasHeader: true);
                    if (!samplingResult.RemainingIndices.Any()) return;

                    // 分批加载剩余数据
                    var batches = _sampler.GenerateBatchLoadingStrategy(samplingResult.RemainingIndices, batchCount: 3);
                    
                    foreach (var batch in batches)
                    {
                        if (cancellationToken.IsCancellationRequested) break;

                        switch (worksheetName.ToLower())
                        {
                            case "power":
                                LoadPowerDataWithIndices(worksheet, data, batch);
                                break;
                            case "voltage":
                                LoadVoltageDataWithIndices(worksheet, data, batch);
                                break;
                            case "wavelength":
                                LoadWavelengthDataWithIndices(worksheet, data, batch);
                                break;
                        }

                        // 小延迟以避免UI阻塞
                        Thread.Sleep(10);
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"详细加载工作表 {worksheetName} 失败: {ex.Message}");
                }
            }, cancellationToken);
        }

        /// <summary>
        /// 根据指定索引加载功率数据
        /// </summary>
        private void LoadPowerDataWithIndices(ExcelWorksheet worksheet, LIVMeasurementData data, List<int> indices)
        {
            if (!indices.Any()) return;

            // 查找列索引
            int currentCol = -1, powerCol = -1;
            for (int col = 1; col <= worksheet.Dimension?.Columns; col++)
            {
                var header = worksheet.Cells[1, col].Text?.Trim();
                if (string.Equals(header, "Current", StringComparison.OrdinalIgnoreCase))
                    currentCol = col;
                else if (string.Equals(header, "Power", StringComparison.OrdinalIgnoreCase))
                    powerCol = col;
            }

            if (currentCol == -1 || powerCol == -1) return;

            var newDataPoints = new List<DataPoint>();

            foreach (var row in indices)
            {
                try
                {
                    var currentValue = worksheet.Cells[row, currentCol].Value;
                    var powerValue = worksheet.Cells[row, powerCol].Value;

                    if (currentValue != null && powerValue != null)
                    {
                        var currentStr = CleanNumericString(currentValue.ToString());
                        var powerStr = CleanNumericString(powerValue.ToString());

                        if (double.TryParse(currentStr, out var current) &&
                            double.TryParse(powerStr, out var power))
                        {
                            current = Math.Max(0, current);
                            power = Math.Max(0, power);

                            newDataPoints.Add(new DataPoint(current, power));
                        }
                    }
                }
                catch
                {
                    continue;
                }
            }

            // 合并新数据到现有数据
            lock (data.CurrentPowerData)
            {
                data.CurrentPowerData.AddRange(newDataPoints);
                // 按电流值排序以保持数据一致性
                data.CurrentPowerData = data.CurrentPowerData
                    .OrderBy(dp => dp.X)
                    .ToList();
            }
        }

        /// <summary>
        /// 根据指定索引加载电压数据
        /// </summary>
        private void LoadVoltageDataWithIndices(ExcelWorksheet worksheet, LIVMeasurementData data, List<int> indices)
        {
            if (!indices.Any()) return;

            // 查找列索引
            int currentCol = -1, voltageCol = -1;
            for (int col = 1; col <= worksheet.Dimension?.Columns; col++)
            {
                var header = worksheet.Cells[1, col].Text?.Trim();
                if (string.Equals(header, "Current", StringComparison.OrdinalIgnoreCase))
                    currentCol = col;
                else if (string.Equals(header, "Voltage", StringComparison.OrdinalIgnoreCase))
                    voltageCol = col;
            }

            if (currentCol == -1 || voltageCol == -1) return;

            var newDataPoints = new List<DataPoint>();

            foreach (var row in indices)
            {
                try
                {
                    var currentValue = worksheet.Cells[row, currentCol].Value;
                    var voltageValue = worksheet.Cells[row, voltageCol].Value;

                    if (currentValue != null && voltageValue != null)
                    {
                        var currentStr = CleanNumericString(currentValue.ToString());
                        var voltageStr = CleanNumericString(voltageValue.ToString());

                        if (double.TryParse(currentStr, out var current) &&
                            double.TryParse(voltageStr, out var voltage))
                        {
                            current = Math.Max(0, current);
                            voltage = Math.Max(0, voltage);

                            newDataPoints.Add(new DataPoint(current, voltage));
                        }
                    }
                }
                catch
                {
                    continue;
                }
            }

            // 合并新数据到现有数据
            lock (data.CurrentVoltageData)
            {
                data.CurrentVoltageData.AddRange(newDataPoints);
                // 按电流值排序以保持数据一致性
                data.CurrentVoltageData = data.CurrentVoltageData
                    .OrderBy(dp => dp.X)
                    .ToList();
            }
        }

        /// <summary>
        /// 根据指定索引加载波长数据
        /// </summary>
        private void LoadWavelengthDataWithIndices(ExcelWorksheet worksheet, LIVMeasurementData data, List<int> indices)
        {
            if (!indices.Any()) return;

            // 查找列索引
            int wavelengthCol = -1, intensityCol = -1;
            for (int col = 1; col <= worksheet.Dimension?.Columns; col++)
            {
                var header = worksheet.Cells[1, col].Text?.Trim();
                if (string.Equals(header, "Wavelength", StringComparison.OrdinalIgnoreCase))
                    wavelengthCol = col;
                else if (string.Equals(header, "Intensity", StringComparison.OrdinalIgnoreCase))
                    intensityCol = col;
            }

            if (wavelengthCol == -1 || intensityCol == -1) return;

            var newDataPoints = new List<DataPoint>();

            foreach (var row in indices)
            {
                try
                {
                    var wavelengthValue = worksheet.Cells[row, wavelengthCol].Value;
                    var intensityValue = worksheet.Cells[row, intensityCol].Value;

                    if (wavelengthValue != null && intensityValue != null)
                    {
                        if (double.TryParse(wavelengthValue.ToString(), out var wavelength) &&
                            double.TryParse(intensityValue.ToString(), out var intensity))
                        {
                            newDataPoints.Add(new DataPoint(wavelength, intensity));
                        }
                    }
                }
                catch
                {
                    continue;
                }
            }

            // 合并新数据到现有数据
            lock (data.WavelengthIntensityData)
            {
                data.WavelengthIntensityData.AddRange(newDataPoints);
                // 按波长值排序以保持数据一致性
                data.WavelengthIntensityData = data.WavelengthIntensityData
                    .OrderBy(dp => dp.X)
                    .ToList();
            }
        }

        /// <summary>
        /// 加载发散角数据（详细模式）
        /// </summary>
        private void LoadDivergenceDataDetailed(ExcelPackage package, LIVMeasurementData data)
        {
            // HFF数据
            var hffWorksheet = package.Workbook.Worksheets["HFF"];
            if (hffWorksheet?.Dimension != null)
            {
                var hffData = new List<DataPoint>();
                LoadDivergenceWorksheet(hffWorksheet, hffData);
                
                lock (data)
                {
                    data.HorizontalDivergenceData = hffData;
                }
            }

            // VFF数据
            var vffWorksheet = package.Workbook.Worksheets["VFF"];
            if (vffWorksheet?.Dimension != null)
            {
                var vffData = new List<DataPoint>();
                LoadDivergenceWorksheet(vffWorksheet, vffData);
                
                lock (data)
                {
                    data.VerticalDivergenceData = vffData;
                }
            }
        }

        /// <summary>
        /// 加载发散角工作表
        /// </summary>
        private void LoadDivergenceWorksheet(ExcelWorksheet worksheet, List<DataPoint> data)
        {
            var rowCount = worksheet.Dimension?.Rows ?? 0;
            if (rowCount < 2) return;

            // 查找列索引
            int angleCol = -1, photocurrentCol = -1;
            for (int col = 1; col <= worksheet.Dimension?.Columns; col++)
            {
                var header = worksheet.Cells[1, col].Text?.Trim();
                if (string.Equals(header, "Angle", StringComparison.OrdinalIgnoreCase))
                    angleCol = col;
                else if (string.Equals(header, "Photocurrent", StringComparison.OrdinalIgnoreCase))
                    photocurrentCol = col;
            }

            if (angleCol == -1 || photocurrentCol == -1) return;

            for (int row = 2; row <= rowCount; row++)
            {
                try
                {
                    var angle = worksheet.Cells[row, angleCol].GetValue<double>();
                    var photocurrent = worksheet.Cells[row, photocurrentCol].GetValue<double>();
                    data.Add(new DataPoint(angle, photocurrent));
                }
                catch
                {
                    continue;
                }
            }
        }

        /// <summary>
        /// 文件验证
        /// </summary>
        private (bool IsValid, string ErrorMessage) ValidateFile(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                    return (false, "文件不存在");

                var extension = Path.GetExtension(filePath).ToLower();
                if (extension != ".xlsx" && extension != ".xls")
                    return (false, "不支持的文件格式，仅支持Excel文件");

                var fileInfo = new FileInfo(filePath);
                if (fileInfo.Length == 0)
                    return (false, "文件为空");

                if (fileInfo.Length > 500 * 1024 * 1024) // 500MB限制
                    return (false, "文件过大，超过500MB限制");

                // 尝试验证Excel文件完整性
                try
                {
                    using var testStream = new FileStream(filePath, FileMode.Open, FileAccess.Read, FileShare.Read);
                    using var testPackage = new ExcelPackage(testStream);
                    
                    // 检查是否有工作表
                    if (testPackage.Workbook.Worksheets.Count == 0)
                        return (false, "Excel文件没有工作表");
                        
                    // 检查必需的工作表
                    var hasRequiredSheet = testPackage.Workbook.Worksheets.Any(ws => 
                        ws.Name.Equals("power", StringComparison.OrdinalIgnoreCase) ||
                        ws.Name.Equals("voltage", StringComparison.OrdinalIgnoreCase) ||
                        ws.Name.Equals("wavelength", StringComparison.OrdinalIgnoreCase));
                    
                    if (!hasRequiredSheet)
                        return (false, "Excel文件缺少必需的工作表(power/voltage/wavelength)");
                }
                catch (InvalidDataException)
                {
                    return (false, "Excel文件格式损坏或不正确");
                }
                catch (System.Xml.XmlException)
                {
                    return (false, "Excel文件包含无效的XML内容");
                }
                catch (IOException ex)
                {
                    return (false, $"无法读取Excel文件: {ex.Message}");
                }
                catch (UnauthorizedAccessException)
                {
                    return (false, "没有权限访问该文件");
                }
                catch (Exception ex)
                {
                    return (false, $"Excel文件验证失败: {ex.Message}");
                }

                return (true, string.Empty);
            }
            catch (Exception ex)
            {
                return (false, $"文件验证失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 清理数值字符串
        /// </summary>
        private static string CleanNumericString(string? input)
        {
            if (string.IsNullOrEmpty(input))
                return "0";

            var cleaned = Regex.Replace(input, @"[^\d\.\-\+eE]", "");

            var parts = cleaned.Split('.');
            if (parts.Length > 2)
            {
                cleaned = parts[0] + "." + string.Join("", parts.Skip(1));
            }

            if (string.IsNullOrEmpty(cleaned) || cleaned == "-" || cleaned == "+")
                return "0";

            return cleaned;
        }

        /// <summary>
        /// 克隆数据对象
        /// </summary>
        private LIVMeasurementData CloneData(LIVMeasurementData source)
        {
            return new LIVMeasurementData
            {
                FileName = source.FileName,
                CurrentPowerData = new List<DataPoint>(source.CurrentPowerData),
                CurrentVoltageData = new List<DataPoint>(source.CurrentVoltageData),
                WavelengthIntensityData = new List<DataPoint>(source.WavelengthIntensityData),
                HorizontalDivergenceData = source.HorizontalDivergenceData != null 
                    ? new List<DataPoint>(source.HorizontalDivergenceData) 
                    : null,
                VerticalDivergenceData = source.VerticalDivergenceData != null 
                    ? new List<DataPoint>(source.VerticalDivergenceData) 
                    : null
            };
        }

        /// <summary>
        /// 获取数据点总数
        /// </summary>
        private int GetDataPointCount(LIVMeasurementData data)
        {
            return data.CurrentPowerData.Count + 
                   data.CurrentVoltageData.Count + 
                   data.WavelengthIntensityData.Count +
                   (data.HorizontalDivergenceData?.Count ?? 0) +
                   (data.VerticalDivergenceData?.Count ?? 0);
        }

        /// <summary>
        /// 工作表类型枚举
        /// </summary>
        private enum WorksheetType
        {
            Power,
            Voltage,
            Wavelength
        }
    }
}