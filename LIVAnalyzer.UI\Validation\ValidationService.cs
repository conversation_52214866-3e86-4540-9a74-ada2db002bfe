using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Threading;

namespace LIVAnalyzer.UI.Validation
{
    /// <summary>
    /// 验证结果
    /// </summary>
    public class ValidationResult
    {
        public bool IsValid { get; set; }
        public List<string> ErrorMessages { get; set; } = new();
        public string PropertyName { get; set; } = string.Empty;
        public object? Value { get; set; }

        public string FirstErrorMessage => ErrorMessages.FirstOrDefault() ?? string.Empty;
        public bool HasErrors => ErrorMessages.Any();
    }

    /// <summary>
    /// 验证上下文
    /// </summary>
    public class ValidationContext
    {
        public object Instance { get; set; } = null!;
        public string PropertyName { get; set; } = string.Empty;
        public object? Value { get; set; }
        public PropertyInfo PropertyInfo { get; set; } = null!;
        public Dictionary<string, object> Items { get; set; } = new();
    }

    /// <summary>
    /// 实时输入验证服务
    /// </summary>
    public class ValidationService : IDisposable
    {
        private readonly Dictionary<string, ValidationResult> _validationCache = new();
        private readonly Dictionary<string, Timer> _validationTimers = new();
        private readonly object _lockObject = new();
        private readonly DispatcherTimer _batchValidationTimer;
        private readonly HashSet<string> _pendingValidations = new();
        
        private bool _disposed = false;

        /// <summary>
        /// 验证延迟（毫秒）- 避免过于频繁的验证
        /// </summary>
        public int ValidationDelay { get; set; } = 300;

        /// <summary>
        /// 验证完成事件
        /// </summary>
        public event EventHandler<ValidationCompletedEventArgs>? ValidationCompleted;

        public ValidationService()
        {
            _batchValidationTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(100)
            };
            _batchValidationTimer.Tick += OnBatchValidationTimer;
        }

        /// <summary>
        /// 验证单个属性值（带延迟）
        /// </summary>
        /// <param name="instance">包含属性的对象实例</param>
        /// <param name="propertyName">属性名</param>
        /// <param name="value">属性值</param>
        public void ValidatePropertyAsync(object instance, string propertyName, object? value)
        {
            if (_disposed) return;

            var key = $"{instance.GetType().FullName}.{propertyName}";

            lock (_lockObject)
            {
                // 取消现有的定时器
                if (_validationTimers.TryGetValue(key, out var existingTimer))
                {
                    existingTimer.Dispose();
                    _validationTimers.Remove(key);
                }

                // 创建新的延迟验证定时器
                var timer = new Timer(async _ =>
                {
                    await PerformValidationAsync(instance, propertyName, value);
                }, null, ValidationDelay, Timeout.Infinite);

                _validationTimers[key] = timer;
            }
        }

        /// <summary>
        /// 立即验证单个属性值
        /// </summary>
        /// <param name="instance">包含属性的对象实例</param>
        /// <param name="propertyName">属性名</param>
        /// <param name="value">属性值</param>
        public ValidationResult ValidateProperty(object instance, string propertyName, object? value)
        {
            if (_disposed) return new ValidationResult { IsValid = false, ErrorMessages = { "验证服务已释放" } };

            try
            {
                var propertyInfo = instance.GetType().GetProperty(propertyName);
                if (propertyInfo == null)
                {
                    return new ValidationResult
                    {
                        IsValid = false,
                        ErrorMessages = { $"属性 '{propertyName}' 不存在" },
                        PropertyName = propertyName,
                        Value = value
                    };
                }

                var validationAttributes = propertyInfo.GetCustomAttributes<ValidationAttribute>(true);
                var result = new ValidationResult
                {
                    IsValid = true,
                    PropertyName = propertyName,
                    Value = value
                };

                var validationContext = new System.ComponentModel.DataAnnotations.ValidationContext(instance)
                {
                    MemberName = propertyName
                };

                foreach (var attribute in validationAttributes)
                {
                    var validationResult = attribute.GetValidationResult(value, validationContext);
                    if (validationResult != System.ComponentModel.DataAnnotations.ValidationResult.Success)
                    {
                        result.IsValid = false;
                        if (!string.IsNullOrEmpty(validationResult?.ErrorMessage))
                        {
                            result.ErrorMessages.Add(validationResult.ErrorMessage);
                        }
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                return new ValidationResult
                {
                    IsValid = false,
                    ErrorMessages = { $"验证过程中发生错误: {ex.Message}" },
                    PropertyName = propertyName,
                    Value = value
                };
            }
        }

        /// <summary>
        /// 验证整个对象的所有属性
        /// </summary>
        /// <param name="instance">要验证的对象实例</param>
        public Dictionary<string, ValidationResult> ValidateObject(object instance)
        {
            if (_disposed) return new Dictionary<string, ValidationResult>();

            var results = new Dictionary<string, ValidationResult>();

            try
            {
                var properties = instance.GetType().GetProperties(BindingFlags.Public | BindingFlags.Instance)
                    .Where(p => p.CanRead && p.GetCustomAttributes<ValidationAttribute>(true).Any());

                foreach (var property in properties)
                {
                    var value = property.GetValue(instance);
                    var result = ValidateProperty(instance, property.Name, value);
                    results[property.Name] = result;
                }
            }
            catch (Exception ex)
            {
                results["_general"] = new ValidationResult
                {
                    IsValid = false,
                    ErrorMessages = { $"对象验证失败: {ex.Message}" },
                    PropertyName = "_general"
                };
            }

            return results;
        }

        /// <summary>
        /// 获取缓存的验证结果
        /// </summary>
        /// <param name="instance">对象实例</param>
        /// <param name="propertyName">属性名</param>
        public ValidationResult? GetCachedValidationResult(object instance, string propertyName)
        {
            var key = $"{instance.GetType().FullName}.{propertyName}";
            
            lock (_lockObject)
            {
                return _validationCache.TryGetValue(key, out var result) ? result : null;
            }
        }

        /// <summary>
        /// 清除验证缓存
        /// </summary>
        public void ClearValidationCache()
        {
            lock (_lockObject)
            {
                _validationCache.Clear();
            }
        }

        /// <summary>
        /// 批量验证多个属性
        /// </summary>
        /// <param name="validationRequests">验证请求列表</param>
        public void ValidatePropertiesBatch(IEnumerable<(object instance, string propertyName, object? value)> validationRequests)
        {
            if (_disposed) return;

            lock (_lockObject)
            {
                foreach (var (instance, propertyName, value) in validationRequests)
                {
                    var key = $"{instance.GetType().FullName}.{propertyName}";
                    _pendingValidations.Add(key);
                }

                if (!_batchValidationTimer.IsEnabled)
                {
                    _batchValidationTimer.Start();
                }
            }
        }

        private async Task PerformValidationAsync(object instance, string propertyName, object? value)
        {
            if (_disposed) return;

            try
            {
                // 在后台线程执行验证
                var result = await Task.Run(() => ValidateProperty(instance, propertyName, value));

                // 缓存结果
                var key = $"{instance.GetType().FullName}.{propertyName}";
                lock (_lockObject)
                {
                    _validationCache[key] = result;
                    
                    // 清理定时器
                    if (_validationTimers.TryGetValue(key, out var timer))
                    {
                        timer.Dispose();
                        _validationTimers.Remove(key);
                    }
                }

                // 在UI线程触发事件
                Application.Current?.Dispatcher.BeginInvoke(() =>
                {
                    ValidationCompleted?.Invoke(this, new ValidationCompletedEventArgs(instance, propertyName, result));
                }, DispatcherPriority.Background);
            }
            catch (Exception ex)
            {
                // 记录验证异常
                System.Diagnostics.Debug.WriteLine($"验证异常 [{propertyName}]: {ex.Message}");
            }
        }

        private void OnBatchValidationTimer(object? sender, EventArgs e)
        {
            _batchValidationTimer.Stop();

            HashSet<string> validationsToProcess;
            lock (_lockObject)
            {
                validationsToProcess = new HashSet<string>(_pendingValidations);
                _pendingValidations.Clear();
            }

            // 批量执行验证
            _ = Task.Run(async () =>
            {
                foreach (var key in validationsToProcess)
                {
                    // 解析key获取对象信息
                    // 这里需要维护对象实例的映射，简化版本暂不实现
                    await Task.Delay(1); // 占位
                }
            });
        }

        public void Dispose()
        {
            if (_disposed) return;

            _disposed = true;

            lock (_lockObject)
            {
                // 清理所有定时器
                foreach (var timer in _validationTimers.Values)
                {
                    timer?.Dispose();
                }
                _validationTimers.Clear();

                // 清理缓存
                _validationCache.Clear();
                _pendingValidations.Clear();
            }

            _batchValidationTimer?.Stop();
            
            GC.SuppressFinalize(this);
        }
    }

    /// <summary>
    /// 验证完成事件参数
    /// </summary>
    public class ValidationCompletedEventArgs : EventArgs
    {
        public object Instance { get; }
        public string PropertyName { get; }
        public ValidationResult Result { get; }

        public ValidationCompletedEventArgs(object instance, string propertyName, ValidationResult result)
        {
            Instance = instance;
            PropertyName = propertyName;
            Result = result;
        }
    }

    /// <summary>
    /// 验证服务单例
    /// </summary>
    public static class ValidationServiceInstance
    {
        private static readonly Lazy<ValidationService> _instance = new(() => new ValidationService());
        
        public static ValidationService Instance => _instance.Value;

        /// <summary>
        /// 重置验证服务实例（主要用于测试）
        /// </summary>
        public static void Reset()
        {
            if (_instance.IsValueCreated)
            {
                _instance.Value.Dispose();
            }
        }
    }
}