import FileUploader from './components/FileUploader';
import DataPreview from './components/DataPreview';
import { Card, CardContent, CardHeader, CardTitle } from './components/ui/card';
import TabsSkeleton from './components/TabsSkeleton';
import ErrorBanner from './components/ErrorBanner';
import DataTable from './components/DataTable';
import ResultSummary from './components/ResultSummary';
import ConfigPanel from './components/ConfigPanel';
import ExportPanel from './components/ExportPanel';
import MenuBar from './components/MenuBar';
import ToolBar from './components/ToolBar';
import SidebarResizer from './components/SidebarResizer';
import { useAppStore } from './state/store';
import './index.css';
import AppEventsBridge from './components/AppEventsBridge';

function App() {
  const { sidebarWidth } = useAppStore();
  return (
    <div className="min-h-screen bg-background text-foreground">
      <AppEventsBridge />
      <MenuBar />
      <ToolBar />
      <div className="mx-auto max-w-[1600px] px-4 py-4">
        <div className="flex gap-4">
          {/* 左侧：文件操作 + 参数面板，可拖拽宽度 */}
          <aside className="shrink-0 space-y-4" style={{ width: sidebarWidth }}>
            <Card>
              <CardHeader>
                <CardTitle>文件操作</CardTitle>
              </CardHeader>
              <CardContent>
                <FileUploader />
              </CardContent>
            </Card>
            <ConfigPanel />
            <ExportPanel />
          </aside>

          {/* 拖拽分隔条 */}
          <SidebarResizer />

          {/* 右侧：图表与结果 */}
          <main className="min-w-0 grow space-y-4">
            <ErrorBanner />
            <Card>
              <CardHeader>
                <CardTitle>数据预览</CardTitle>
              </CardHeader>
              <CardContent>
                <div data-chart-container="preview">
                  <DataPreview />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>结果汇总</CardTitle>
              </CardHeader>
              <CardContent>
                <div data-chart-container="summary">
                  <ResultSummary />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>数据表格（前50行）</CardTitle>
              </CardHeader>
              <CardContent>
                <div data-chart-container="table">
                  <DataTable />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent>
                <div data-chart-container="tabs">
                  <TabsSkeleton />
                </div>
              </CardContent>
            </Card>
          </main>
        </div>
      </div>
    </div>
  );
}

export default App
