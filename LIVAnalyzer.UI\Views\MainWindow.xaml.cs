using LIVAnalyzer.UI.ViewModels;
using LIVAnalyzer.UI.Services;
using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Linq;
using OxyPlot;
using OxyPlot.Wpf;
using System.Windows.Controls;

namespace LIVAnalyzer.UI.Views
{


    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window
    {
        public MainWindow()
        {
            InitializeComponent();
            DataContext = new MainWindowViewModel();

            // 确保窗口加载后应用主题
            this.Loaded += (sender, args) =>
            {
                NativeFluentThemeService.Instance.ApplyThemeToWindow(this);

                // 初始化主题按钮图标
                UpdateThemeButtonIcon();

                // 触发图表主题更新
                if (DataContext is MainWindowViewModel viewModel)
                {
                    viewModel.UpdateAllPlotsTheme();
                }


            };
        }

        private void AxisSettingsButton_Click(object sender, RoutedEventArgs e)
        {
            ShowAxisSettingsDialog();
        }

        private void ThemeToggleButton_Click(object sender, RoutedEventArgs e)
        {
            // 智能主题切换逻辑
            var currentIsDark = NativeFluentThemeService.Instance.IsCurrentlyDarkMode();
            var newTheme = currentIsDark ? "Light" : "Dark";
            App.SwitchTheme(newTheme);

            // 更新按钮图标
            UpdateThemeButtonIcon();

            // 更新图表主题
            if (DataContext is MainWindowViewModel viewModel)
            {
                viewModel.UpdateAllPlotsTheme();
            }
        }

        private void SetLightTheme_Click(object sender, RoutedEventArgs e)
        {
            App.SwitchTheme("Light");
            UpdateThemeButtonIcon();
            if (DataContext is MainWindowViewModel viewModel)
            {
                viewModel.UpdateAllPlotsTheme();
            }
        }

        private void SetDarkTheme_Click(object sender, RoutedEventArgs e)
        {
            App.SwitchTheme("Dark");
            UpdateThemeButtonIcon();
            if (DataContext is MainWindowViewModel viewModel)
            {
                viewModel.UpdateAllPlotsTheme();
            }
        }

        private void SetSystemTheme_Click(object sender, RoutedEventArgs e)
        {
            App.SwitchTheme("System");
            UpdateThemeButtonIcon();
            if (DataContext is MainWindowViewModel viewModel)
            {
                viewModel.UpdateAllPlotsTheme();
            }
        }

        private void UpdateThemeButtonIcon()
        {
            if (ThemeToggleButton != null)
            {
                var isDark = NativeFluentThemeService.Instance.IsCurrentlyDarkMode();
                ThemeToggleButton.Content = isDark ? "☀️" : "🌙";
                ThemeToggleButton.ToolTip = isDark ? "切换到浅色主题" : "切换到深色主题";

                // 强制刷新按钮样式
                ThemeToggleButton.InvalidateVisual();
            }
        }

        private void PlotView_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            if (sender is PlotView plotView && plotView.Model != null)
            {
                // 双击重置缩放
                plotView.Model.ResetAllAxes();
                plotView.Model.InvalidatePlot(true);
            }
        }





        /// <summary>
        /// 计算轴标题的边界框 - 考虑图例位置的影响
        /// </summary>
        private System.Windows.Rect CalculateAxisTitleBounds(OxyPlot.Axes.Axis axis, OxyRect plotArea, double actualWidth, double actualHeight)
        {
            // 标题文字的估算尺寸（基于字体大小）
            var titleFontSize = axis.TitleFontSize > 0 ? axis.TitleFontSize : 14;
            var titleText = axis.Title ?? "";

            // 估算文字尺寸（每个字符约占字体大小的0.6倍宽度）
            var estimatedTextWidth = titleText.Length * titleFontSize * 0.6;
            var estimatedTextHeight = titleFontSize * 1.2; // 包含行高

            // 获取图例信息以调整位置计算
            var legendInfo = GetLegendLayoutInfo(axis.PlotModel);

            // 根据轴的位置计算标题的中心位置和边界框
            switch (axis.Position)
            {
                case OxyPlot.Axes.AxisPosition.Bottom:
                    {
                        var centerX = plotArea.Left + plotArea.Width / 2;
                        var centerY = plotArea.Bottom + 35; // 底部轴标题通常在轴下方35像素处

                        // 如果图例在底部，需要向下调整
                        if (legendInfo.Position == OxyPlot.Legends.LegendPosition.BottomCenter)
                        {
                            centerY += legendInfo.EstimatedHeight + 10;
                        }

                        return new System.Windows.Rect(
                            centerX - estimatedTextWidth / 2 - 10,
                            centerY - estimatedTextHeight / 2 - 5,
                            estimatedTextWidth + 20,
                            estimatedTextHeight + 10
                        );
                    }

                case OxyPlot.Axes.AxisPosition.Left:
                    {
                        var centerX = plotArea.Left - 40; // 左侧轴标题通常在轴左方40像素处
                        var centerY = plotArea.Top + plotArea.Height / 2;

                        // 如果图例在左边，需要向左调整
                        if (legendInfo.Position == OxyPlot.Legends.LegendPosition.LeftMiddle)
                        {
                            centerX -= legendInfo.EstimatedWidth + 10;
                        }

                        // 左侧标题通常是旋转90度的，所以宽高互换
                        return new System.Windows.Rect(
                            centerX - estimatedTextHeight / 2 - 5,
                            centerY - estimatedTextWidth / 2 - 10,
                            estimatedTextHeight + 10,
                            estimatedTextWidth + 20
                        );
                    }

                case OxyPlot.Axes.AxisPosition.Right:
                    {
                        // 右侧轴需要考虑PositionTier来确定具体位置
                        var baseOffset = 50; // 基础偏移
                        var tierOffset = axis.PositionTier * 60; // 每层额外偏移60像素
                        var centerX = plotArea.Right + baseOffset + tierOffset;
                        var centerY = plotArea.Top + plotArea.Height / 2;

                        // 如果图例在右边，需要向右调整所有右侧轴的位置
                        if (legendInfo.Position == OxyPlot.Legends.LegendPosition.RightMiddle)
                        {
                            centerX += legendInfo.EstimatedWidth + 15; // 额外偏移以避开图例
                        }

                        // 右侧标题通常也是旋转90度的
                        return new System.Windows.Rect(
                            centerX - estimatedTextHeight / 2 - 5,
                            centerY - estimatedTextWidth / 2 - 10,
                            estimatedTextHeight + 10,
                            estimatedTextWidth + 20
                        );
                    }

                case OxyPlot.Axes.AxisPosition.Top:
                    {
                        var centerX = plotArea.Left + plotArea.Width / 2;
                        var centerY = plotArea.Top - 35; // 顶部轴标题通常在轴上方35像素处

                        // 如果图例在顶部，需要向上调整
                        if (legendInfo.Position == OxyPlot.Legends.LegendPosition.TopCenter)
                        {
                            centerY -= legendInfo.EstimatedHeight + 10;
                        }

                        return new System.Windows.Rect(
                            centerX - estimatedTextWidth / 2 - 10,
                            centerY - estimatedTextHeight / 2 - 5,
                            estimatedTextWidth + 20,
                            estimatedTextHeight + 10
                        );
                    }

                default:
                    return System.Windows.Rect.Empty;
            }
        }

        /// <summary>
        /// 获取图例布局信息
        /// </summary>
        private (OxyPlot.Legends.LegendPosition Position, double EstimatedWidth, double EstimatedHeight) GetLegendLayoutInfo(OxyPlot.PlotModel plotModel)
        {
            if (plotModel?.Legends == null || !plotModel.Legends.Any())
            {
                return (OxyPlot.Legends.LegendPosition.TopRight, 0, 0);
            }

            var legend = plotModel.Legends.First();
            if (!legend.IsLegendVisible)
            {
                return (legend.LegendPosition, 0, 0);
            }

            // 估算图例尺寸
            var legendFontSize = legend.LegendFontSize > 0 ? legend.LegendFontSize : 12;

            // 估算图例项目数量（基于数据系列）
            var seriesCount = plotModel.Series.Count(s => !string.IsNullOrEmpty(s.Title));

            // 估算每个图例项的尺寸
            var averageTitleLength = 8; // 平均标题长度估算
            if (plotModel.Series.Any(s => !string.IsNullOrEmpty(s.Title)))
            {
                averageTitleLength = (int)plotModel.Series
                    .Where(s => !string.IsNullOrEmpty(s.Title))
                    .Average(s => s.Title.Length);
            }

            var estimatedItemWidth = averageTitleLength * legendFontSize * 0.6 + 30; // 包含图标空间
            var estimatedItemHeight = legendFontSize * 1.5; // 包含行间距

            double estimatedWidth, estimatedHeight;

            switch (legend.LegendPosition)
            {
                case OxyPlot.Legends.LegendPosition.LeftMiddle:
                case OxyPlot.Legends.LegendPosition.RightMiddle:
                    // 垂直排列
                    estimatedWidth = estimatedItemWidth + 20; // 加上边距
                    estimatedHeight = seriesCount * estimatedItemHeight + 20;
                    break;

                case OxyPlot.Legends.LegendPosition.TopCenter:
                case OxyPlot.Legends.LegendPosition.BottomCenter:
                    // 水平排列
                    estimatedWidth = seriesCount * estimatedItemWidth + 20;
                    estimatedHeight = estimatedItemHeight + 20;
                    break;

                default:
                    // 其他位置（角落）- 通常垂直排列
                    estimatedWidth = estimatedItemWidth + 20;
                    estimatedHeight = seriesCount * estimatedItemHeight + 20;
                    break;
            }

            return (legend.LegendPosition, estimatedWidth, estimatedHeight);
        }





        /// <summary>
        /// 显示轴设置对话框
        /// </summary>
        private void ShowAxisSettingsDialog()
        {
            // 获取当前活动的图表
            var activeTabItem = GetActiveTabItem();
            if (activeTabItem == null) return;

            var plotViews = GetPlotViewsFromTabItem(activeTabItem);
            if (plotViews.Count == 0) return;

            // 如果只有一个图表，直接显示
            if (plotViews.Count == 1)
            {
                var plotView = plotViews[0];
                if (plotView?.Model == null) return;

                var dialog = new AxisSelectionDialog(plotView.Model, plotView.Tag as string ?? "图表");
                dialog.Owner = this;
                dialog.WindowStartupLocation = WindowStartupLocation.CenterOwner;

                if (dialog.ShowDialog() == true && dialog.SelectedAxis != null)
                {
                    if (DataContext is MainWindowViewModel viewModel)
                    {
                        viewModel.ShowAxisSettings(dialog.SelectedAxis, plotView.Tag as string ?? "图表");
                    }
                }
            }
            else
            {
                // 多个图表，先让用户选择图表
                ShowMultiChartAxisDialog(plotViews);
            }
        }

        /// <summary>
        /// 获取当前活动的TabItem
        /// </summary>
        private TabItem GetActiveTabItem()
        {
            // 查找主TabControl
            var tabControl = FindVisualChild<TabControl>(this);
            return tabControl?.SelectedItem as TabItem;
        }

        /// <summary>
        /// 从TabItem获取所有PlotView
        /// </summary>
        private List<PlotView> GetPlotViewsFromTabItem(TabItem tabItem)
        {
            var plotViews = new List<PlotView>();
            if (tabItem?.Content is Grid grid)
            {
                FindAllVisualChildren<PlotView>(grid, plotViews);
            }
            return plotViews;
        }

        /// <summary>
        /// 显示多图表轴选择对话框
        /// </summary>
        private void ShowMultiChartAxisDialog(List<PlotView> plotViews)
        {
            var dialog = new MultiChartAxisSelectionDialog(plotViews);
            dialog.Owner = this;
            dialog.WindowStartupLocation = WindowStartupLocation.CenterOwner;

            if (dialog.ShowDialog() == true && dialog.SelectedAxis != null && dialog.SelectedChartName != null)
            {
                if (DataContext is MainWindowViewModel viewModel)
                {
                    viewModel.ShowAxisSettings(dialog.SelectedAxis, dialog.SelectedChartName);
                }
            }
        }

        /// <summary>
        /// 查找可视化树中的子元素
        /// </summary>
        private T FindVisualChild<T>(DependencyObject parent) where T : DependencyObject
        {
            for (int i = 0; i < VisualTreeHelper.GetChildrenCount(parent); i++)
            {
                var child = VisualTreeHelper.GetChild(parent, i);
                if (child is T result)
                    return result;

                var childOfChild = FindVisualChild<T>(child);
                if (childOfChild != null)
                    return childOfChild;
            }
            return null;
        }

        /// <summary>
        /// 查找可视化树中的所有指定类型子元素
        /// </summary>
        private void FindAllVisualChildren<T>(DependencyObject parent, List<T> results) where T : DependencyObject
        {
            for (int i = 0; i < VisualTreeHelper.GetChildrenCount(parent); i++)
            {
                var child = VisualTreeHelper.GetChild(parent, i);
                if (child is T result)
                    results.Add(result);

                FindAllVisualChildren<T>(child, results);
            }
        }

        /// <summary>
        /// 坐标轴刻度信息
        /// </summary>
        public class AxisTickInfo
        {
            public OxyPlot.Axes.Axis Axis { get; set; }
            public double Value { get; set; }
            public bool IsMinimum { get; set; }
            public System.Windows.Rect Bounds { get; set; }
        }

        /// <summary>
        /// 检测鼠标位置是否在坐标轴刻度标签上
        /// </summary>
        private AxisTickInfo DetectAxisTickLabel(PlotView plotView, System.Windows.Point position)
        {
            if (plotView.Model == null) return null;

            var plotArea = plotView.Model.PlotArea;
            const double tickLabelMargin = 15; // 刻度标签检测边距

            foreach (var axis in plotView.Model.Axes)
            {
                if (!axis.IsAxisVisible) continue;

                // 检测最小值和最大值标签
                var minTickInfo = CheckTickLabel(axis, axis.ActualMinimum, plotArea, plotView.ActualWidth, plotView.ActualHeight, position, tickLabelMargin, true);
                if (minTickInfo != null) return minTickInfo;

                var maxTickInfo = CheckTickLabel(axis, axis.ActualMaximum, plotArea, plotView.ActualWidth, plotView.ActualHeight, position, tickLabelMargin, false);
                if (maxTickInfo != null) return maxTickInfo;
            }

            return null;
        }

        /// <summary>
        /// 检查特定刻度标签是否被点击
        /// </summary>
        private AxisTickInfo CheckTickLabel(OxyPlot.Axes.Axis axis, double value, OxyRect plotArea,
            double actualWidth, double actualHeight, System.Windows.Point clickPosition,
            double margin, bool isMinimum)
        {
            System.Windows.Rect labelBounds;

            switch (axis.Position)
            {
                case OxyPlot.Axes.AxisPosition.Bottom:
                    {
                        var x = plotArea.Left + (value - axis.ActualMinimum) / (axis.ActualMaximum - axis.ActualMinimum) * plotArea.Width;
                        var y = plotArea.Bottom + 5; // 标签在轴下方
                        labelBounds = new System.Windows.Rect(x - 30, y, 60, 20);
                        break;
                    }
                case OxyPlot.Axes.AxisPosition.Left:
                    {
                        var x = plotArea.Left - 50; // 标签在轴左侧
                        var y = plotArea.Bottom - (value - axis.ActualMinimum) / (axis.ActualMaximum - axis.ActualMinimum) * plotArea.Height - 10;
                        labelBounds = new System.Windows.Rect(x, y, 45, 20);
                        break;
                    }
                case OxyPlot.Axes.AxisPosition.Right:
                    {
                        var baseOffset = 10 + axis.PositionTier * 60; // 考虑多个右侧轴
                        var x = plotArea.Right + baseOffset;
                        var y = plotArea.Bottom - (value - axis.ActualMinimum) / (axis.ActualMaximum - axis.ActualMinimum) * plotArea.Height - 10;
                        labelBounds = new System.Windows.Rect(x, y, 45, 20);
                        break;
                    }
                case OxyPlot.Axes.AxisPosition.Top:
                    {
                        var x = plotArea.Left + (value - axis.ActualMinimum) / (axis.ActualMaximum - axis.ActualMinimum) * plotArea.Width;
                        var y = plotArea.Top - 25; // 标签在轴上方
                        labelBounds = new System.Windows.Rect(x - 30, y, 60, 20);
                        break;
                    }
                default:
                    return null;
            }

            // 扩大检测区域
            labelBounds.Inflate(margin, margin);

            if (labelBounds.Contains(clickPosition))
            {
                return new AxisTickInfo
                {
                    Axis = axis,
                    Value = value,
                    IsMinimum = isMinimum,
                    Bounds = labelBounds
                };
            }

            return null;
        }
    }
}