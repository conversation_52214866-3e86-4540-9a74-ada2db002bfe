namespace LIVAnalyzer.Models
{
    /// <summary>
    /// 应用程序配置模型
    /// </summary>
    public class ApplicationConfig
    {
        /// <summary>
        /// 数据处理配置
        /// </summary>
        public DataProcessingConfig DataProcessing { get; set; } = new();
        
        /// <summary>
        /// 显示配置
        /// </summary>
        public DisplayConfig Display { get; set; } = new();
        
        /// <summary>
        /// 性能配置
        /// </summary>
        public PerformanceConfig Performance { get; set; } = new();
        
        /// <summary>
        /// 用户界面配置
        /// </summary>
        public UIConfig UI { get; set; } = new();
    }
    
    /// <summary>
    /// 数据处理配置
    /// </summary>
    public class DataProcessingConfig
    {
        public SmoothingConfig Smoothing { get; set; } = new();
        public ThresholdConfig Threshold { get; set; } = new();
        public BatchProcessingConfig BatchProcessing { get; set; } = new();
    }
    
    /// <summary>
    /// 平滑算法类型
    /// </summary>
    public enum SmoothingAlgorithmType
    {
        /// <summary>
        /// 移动平均
        /// </summary>
        MovingAverage,
        
        /// <summary>
        /// Savitzky-Golay滤波器
        /// </summary>
        SavitzkyGolay,
        
        /// <summary>
        /// 高斯滤波器
        /// </summary>
        Gaussian,
        
        /// <summary>
        /// 低通滤波器
        /// </summary>
        LowPass
    }
    
    /// <summary>
    /// 平滑处理配置
    /// </summary>
    public class SmoothingConfig
    {
        public bool EnableByDefault { get; set; } = false;
        public SmoothingAlgorithmType AlgorithmType { get; set; } = SmoothingAlgorithmType.MovingAverage;
        
        // 移动平均参数
        public int DefaultWindowSize { get; set; } = 5;
        public int MinWindowSize { get; set; } = 3;
        public int MaxWindowSize { get; set; } = 51;
        
        // Savitzky-Golay参数
        public SavitzkyGolayConfig SavitzkyGolay { get; set; } = new();
        
        // 高斯滤波参数
        public GaussianConfig Gaussian { get; set; } = new();
        
        // 低通滤波参数
        public LowPassConfig LowPass { get; set; } = new();
    }
    
    /// <summary>
    /// Savitzky-Golay滤波器配置
    /// </summary>
    public class SavitzkyGolayConfig
    {
        /// <summary>
        /// 窗口大小（必须为奇数）
        /// </summary>
        public int WindowSize { get; set; } = 5;
        
        /// <summary>
        /// 多项式阶数
        /// </summary>
        public int PolynomialOrder { get; set; } = 2;
        
        /// <summary>
        /// 最小窗口大小
        /// </summary>
        public int MinWindowSize { get; set; } = 3;
        
        /// <summary>
        /// 最大窗口大小
        /// </summary>
        public int MaxWindowSize { get; set; } = 21;
        
        /// <summary>
        /// 最小多项式阶数
        /// </summary>
        public int MinPolynomialOrder { get; set; } = 1;
        
        /// <summary>
        /// 最大多项式阶数
        /// </summary>
        public int MaxPolynomialOrder { get; set; } = 5;
    }
    
    /// <summary>
    /// 高斯滤波器配置
    /// </summary>
    public class GaussianConfig
    {
        /// <summary>
        /// 标准差
        /// </summary>
        public double Sigma { get; set; } = 1.0;
        
        /// <summary>
        /// 最小标准差
        /// </summary>
        public double MinSigma { get; set; } = 0.1;
        
        /// <summary>
        /// 最大标准差
        /// </summary>
        public double MaxSigma { get; set; } = 5.0;
        
        /// <summary>
        /// 内核大小倍数（内核大小 = Sigma * KernelSizeMultiplier）
        /// </summary>
        public double KernelSizeMultiplier { get; set; } = 4.0;
    }
    
    /// <summary>
    /// 低通滤波器配置
    /// </summary>
    public class LowPassConfig
    {
        /// <summary>
        /// 截止频率（归一化频率，0-1）
        /// </summary>
        public double CutoffFrequency { get; set; } = 0.1;
        
        /// <summary>
        /// 最小截止频率
        /// </summary>
        public double MinCutoffFrequency { get; set; } = 0.01;
        
        /// <summary>
        /// 最大截止频率
        /// </summary>
        public double MaxCutoffFrequency { get; set; } = 0.5;
        
        /// <summary>
        /// 滤波器阶数
        /// </summary>
        public int FilterOrder { get; set; } = 4;
        
        /// <summary>
        /// 最小滤波器阶数
        /// </summary>
        public int MinFilterOrder { get; set; } = 2;
        
        /// <summary>
        /// 最大滤波器阶数
        /// </summary>
        public int MaxFilterOrder { get; set; } = 8;
    }
    
    /// <summary>
    /// 阈值配置
    /// </summary>
    public class ThresholdConfig
    {
        public double DefaultThreshold { get; set; } = 0.01;
        public double MinThreshold { get; set; } = 0.001;
        public double MaxThreshold { get; set; } = 1.0;
    }
    

    
    /// <summary>
    /// 批处理配置
    /// </summary>
    public class BatchProcessingConfig
    {
        public double DefaultI1 { get; set; } = 0.5;
        public double DefaultI2 { get; set; } = 1.0;
    }
    
    /// <summary>
    /// 显示配置
    /// </summary>
    public class DisplayConfig
    {
        public bool ShowGrid { get; set; } = true;
        public bool ShowLegend { get; set; } = false;
        public bool ShowCoordinates { get; set; } = true;
        public string DefaultLineStyle { get; set; } = "line";
        public ChartSettingsConfig ChartSettings { get; set; } = new();
    }
    
    /// <summary>
    /// 图表设置配置
    /// </summary>
    public class ChartSettingsConfig
    {
        public double LineThickness { get; set; } = 2.0;
        public int MarkerSize { get; set; } = 4;
        public string MarkerType { get; set; } = "圆形";
        public int MajorGridOpacity { get; set; } = 50;
        public int MinorGridOpacity { get; set; } = 25;
        public string MajorGridLineStyle { get; set; } = "实线";
        public string MinorGridLineStyle { get; set; } = "点线";
        public string MajorGridColor { get; set; } = "灰色";
        public string MinorGridColor { get; set; } = "灰色";
        public string ColorScheme { get; set; } = "默认";
        public string LegendPosition { get; set; } = "右上";
        public int LegendFontSize { get; set; } = 12;
        public int AxisTitleFontSize { get; set; } = 14;
        public int AxisLabelFontSize { get; set; } = 11;
        
        // 数据标签设置
        public bool ShowDataLabels { get; set; } = false;
        public string DataLabelPosition { get; set; } = "上方";
        public int DataLabelFontSize { get; set; } = 10;
        public string DataLabelFormat { get; set; } = "{0:F2}";
        
        // 坐标轴范围设置
        public bool EnableCustomXAxisRange { get; set; } = false;
        public double XAxisMinimum { get; set; } = 0;
        public double XAxisMaximum { get; set; } = 100;
        public bool EnableCustomYAxisRange { get; set; } = false;
        public double YAxisMinimum { get; set; } = 0;
        public double YAxisMaximum { get; set; } = 100;
        public bool EnableCustomSecondaryYAxisRange { get; set; } = false;
        public double SecondaryYAxisMinimum { get; set; } = 0;
        public double SecondaryYAxisMaximum { get; set; } = 100;
    }
    
    /// <summary>
    /// 性能配置
    /// </summary>
    public class PerformanceConfig
    {
        public int MaxFiles { get; set; } = 1000;
        public long MaxFileSizeMB { get; set; } = 500;
    }
    
    /// <summary>
    /// 用户界面配置
    /// </summary>
    public class UIConfig
    {
        public string LastFilePath { get; set; } = string.Empty;
        public string LastFolderPath { get; set; } = string.Empty;
        public WindowConfig Window { get; set; } = new();
        public string Theme { get; set; } = "System";
        public string ThemeColor { get; set; } = "Windows 默认";
    }
    
    /// <summary>
    /// 窗口配置
    /// </summary>
    public class WindowConfig
    {
        public int Width { get; set; } = 1280;
        public int Height { get; set; } = 720;
        public int MinWidth { get; set; } = 1024;
        public int MinHeight { get; set; } = 680;
    }
}