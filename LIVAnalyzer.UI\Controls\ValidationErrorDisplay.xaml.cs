using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media.Animation;

namespace LIVAnalyzer.UI.Controls
{
    /// <summary>
    /// 验证错误显示组件
    /// </summary>
    public partial class ValidationErrorDisplay : UserControl
    {
        #region Dependency Properties

        /// <summary>
        /// 消息类型依赖属性
        /// </summary>
        public static readonly DependencyProperty MessageTypeProperty =
            DependencyProperty.Register(nameof(MessageType), typeof(MessageType), typeof(ValidationErrorDisplay),
                new PropertyMetadata(MessageType.Error, OnMessageTypeChanged));

        /// <summary>
        /// 消息内容依赖属性
        /// </summary>
        public static readonly DependencyProperty MessageProperty =
            DependencyProperty.Register(nameof(Message), typeof(string), typeof(ValidationErrorDisplay),
                new PropertyMetadata(string.Empty, OnMessageChanged));

        /// <summary>
        /// 是否显示关闭按钮依赖属性
        /// </summary>
        public static readonly DependencyProperty ShowCloseButtonProperty =
            DependencyProperty.Register(nameof(ShowCloseButton), typeof(bool), typeof(ValidationErrorDisplay),
                new PropertyMetadata(true));

        /// <summary>
        /// 自动隐藏时间依赖属性（毫秒，0表示不自动隐藏）
        /// </summary>
        public static readonly DependencyProperty AutoHideDelayProperty =
            DependencyProperty.Register(nameof(AutoHideDelay), typeof(int), typeof(ValidationErrorDisplay),
                new PropertyMetadata(0, OnAutoHideDelayChanged));

        /// <summary>
        /// 是否显示依赖属性
        /// </summary>
        public static readonly DependencyProperty IsShowingProperty =
            DependencyProperty.Register(nameof(IsShowing), typeof(bool), typeof(ValidationErrorDisplay),
                new PropertyMetadata(false, OnIsShowingChanged));

        #endregion

        #region Properties

        /// <summary>
        /// 消息类型
        /// </summary>
        public MessageType MessageType
        {
            get => (MessageType)GetValue(MessageTypeProperty);
            set => SetValue(MessageTypeProperty, value);
        }

        /// <summary>
        /// 消息内容
        /// </summary>
        public string Message
        {
            get => (string)GetValue(MessageProperty);
            set => SetValue(MessageProperty, value);
        }

        /// <summary>
        /// 是否显示关闭按钮
        /// </summary>
        public bool ShowCloseButton
        {
            get => (bool)GetValue(ShowCloseButtonProperty);
            set => SetValue(ShowCloseButtonProperty, value);
        }

        /// <summary>
        /// 自动隐藏延迟（毫秒）
        /// </summary>
        public int AutoHideDelay
        {
            get => (int)GetValue(AutoHideDelayProperty);
            set => SetValue(AutoHideDelayProperty, value);
        }

        /// <summary>
        /// 是否正在显示
        /// </summary>
        public bool IsShowing
        {
            get => (bool)GetValue(IsShowingProperty);
            set => SetValue(IsShowingProperty, value);
        }

        #endregion

        #region Events

        /// <summary>
        /// 消息被关闭事件
        /// </summary>
        public event EventHandler? MessageClosed;

        /// <summary>
        /// 消息显示状态改变事件
        /// </summary>
        public event EventHandler<bool>? ShowingChanged;

        #endregion

        #region Fields

        private System.Windows.Threading.DispatcherTimer? _autoHideTimer;

        #endregion

        #region Constructor

        public ValidationErrorDisplay()
        {
            InitializeComponent();
            Loaded += OnLoaded;
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// 显示错误消息
        /// </summary>
        /// <param name="message">消息内容</param>
        /// <param name="messageType">消息类型</param>
        /// <param name="autoHideDelay">自动隐藏延迟（毫秒，0表示不自动隐藏）</param>
        public void ShowMessage(string message, MessageType messageType = MessageType.Error, int autoHideDelay = 0)
        {
            Message = message;
            MessageType = messageType;
            AutoHideDelay = autoHideDelay;
            IsShowing = !string.IsNullOrWhiteSpace(message);
        }

        /// <summary>
        /// 隐藏消息
        /// </summary>
        public void HideMessage()
        {
            IsShowing = false;
        }

        /// <summary>
        /// 切换消息显示状态
        /// </summary>
        public void ToggleMessage()
        {
            IsShowing = !IsShowing;
        }

        #endregion

        #region Event Handlers

        private void OnLoaded(object sender, RoutedEventArgs e)
        {
            // 初始化状态
            UpdateVisualState(false);
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            HideMessage();
            MessageClosed?.Invoke(this, EventArgs.Empty);
        }

        #endregion

        #region Property Changed Handlers

        private static void OnMessageTypeChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is ValidationErrorDisplay control)
            {
                control.UpdateVisualState();
            }
        }

        private static void OnMessageChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is ValidationErrorDisplay control)
            {
                var newMessage = e.NewValue as string ?? string.Empty;
                
                // 如果消息为空，自动隐藏
                if (string.IsNullOrWhiteSpace(newMessage))
                {
                    control.IsShowing = false;
                }
                else if (!control.IsShowing)
                {
                    // 如果有新消息且当前未显示，则显示
                    control.IsShowing = true;
                }
            }
        }

        private static void OnAutoHideDelayChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is ValidationErrorDisplay control)
            {
                control.SetupAutoHideTimer();
            }
        }

        private static void OnIsShowingChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is ValidationErrorDisplay control)
            {
                var isShowing = (bool)e.NewValue;
                control.UpdateVisualState(true);
                control.ShowingChanged?.Invoke(control, isShowing);
                
                if (isShowing)
                {
                    control.SetupAutoHideTimer();
                }
                else
                {
                    control.StopAutoHideTimer();
                }
            }
        }

        #endregion

        #region Private Methods

        private void UpdateVisualState(bool animate = true)
        {
            if (MainBorder == null) return;

            if (IsShowing && !string.IsNullOrWhiteSpace(Message))
            {
                ShowWithAnimation(animate);
            }
            else
            {
                HideWithAnimation(animate);
            }
        }

        private void ShowWithAnimation(bool animate)
        {
            if (!animate)
            {
                MainBorder.Opacity = 1;
                MainBorder.Height = 24;
                return;
            }

            var storyboard = FindResource("FadeInAnimation") as Storyboard;
            if (storyboard != null)
            {
                Storyboard.SetTarget(storyboard, MainBorder);
                storyboard.Begin();
            }
        }

        private void HideWithAnimation(bool animate)
        {
            if (!animate)
            {
                MainBorder.Opacity = 0;
                MainBorder.Height = 0;
                return;
            }

            var storyboard = FindResource("FadeOutAnimation") as Storyboard;
            if (storyboard != null)
            {
                Storyboard.SetTarget(storyboard, MainBorder);
                storyboard.Begin();
            }
        }

        private void SetupAutoHideTimer()
        {
            StopAutoHideTimer();

            if (AutoHideDelay > 0 && IsShowing)
            {
                _autoHideTimer = new System.Windows.Threading.DispatcherTimer
                {
                    Interval = TimeSpan.FromMilliseconds(AutoHideDelay)
                };
                _autoHideTimer.Tick += (s, e) =>
                {
                    HideMessage();
                    StopAutoHideTimer();
                };
                _autoHideTimer.Start();
            }
        }

        private void StopAutoHideTimer()
        {
            _autoHideTimer?.Stop();
            _autoHideTimer = null;
        }

        #endregion
    }

    /// <summary>
    /// 消息类型枚举
    /// </summary>
    public enum MessageType
    {
        /// <summary>
        /// 错误消息
        /// </summary>
        Error,

        /// <summary>
        /// 警告消息
        /// </summary>
        Warning,

        /// <summary>
        /// 信息消息
        /// </summary>
        Info
    }
}