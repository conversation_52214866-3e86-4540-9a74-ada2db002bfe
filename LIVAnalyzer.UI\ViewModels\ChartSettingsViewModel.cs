using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Collections.ObjectModel;
using System.Windows.Media;
using OxyPlot;
using System.Collections.Generic;
using System.Linq;
using System;

namespace LIVAnalyzer.UI.ViewModels
{
    public partial class ChartSettingsViewModel : ObservableObject, IDisposable
    {
        private readonly MainWindowViewModel _mainViewModel;
        private bool _isApplied = false;
        private bool _disposed = false;

        [ObservableProperty]
        private double lineThickness = 2.0;

        [ObservableProperty]
        private int markerSize = 4;

        [ObservableProperty]
        private string selectedMarkerType = "圆形";

        [ObservableProperty]
        private int majorGridOpacity = 50;

        [ObservableProperty]
        private int minorGridOpacity = 25;

        [ObservableProperty]
        private string majorGridLineStyle = "实线";

        [ObservableProperty]
        private string minorGridLineStyle = "点线";

        [ObservableProperty]
        private string majorGridColor = "灰色";

        [ObservableProperty]
        private string minorGridColor = "灰色";

        [ObservableProperty]
        private string selectedColorScheme = "默认";

        [ObservableProperty]
        private string selectedLegendPosition = "右上";

        [ObservableProperty]
        private int legendFontSize = 12;

        [ObservableProperty]
        private int axisTitleFontSize = 14;

        [ObservableProperty]
        private int axisLabelFontSize = 11;

        [ObservableProperty]
        private ObservableCollection<Brush> previewColors = new();

        [ObservableProperty]
        private bool enableRealTimePreview = false;

        [ObservableProperty]
        private bool isPreviewActive = false;
        
        // 数据标签属性
        [ObservableProperty]
        private bool showDataLabels = false;
        
        [ObservableProperty]
        private string dataLabelPosition = "上方";
        
        [ObservableProperty]
        private int dataLabelFontSize = 10;
        
        [ObservableProperty]
        private string dataLabelFormat = "{0:F2}";

        // 效率曲线叠加属性
        [ObservableProperty]
        private bool showEfficiencyOverlay = false;

        // 选项列表
        public ObservableCollection<string> MarkerTypes { get; } = new()
        {
            "无", "圆形", "方形", "菱形", "三角形", "十字"
        };

        public ObservableCollection<string> LineStyles { get; } = new()
        {
            "实线", "虚线", "点线", "点划线", "无"
        };

        public ObservableCollection<string> ColorSchemes { get; } = new()
        {
            "默认", "彩虹", "暖色", "冷色", "单色渐变", "高对比度"
        };

        public ObservableCollection<string> LegendPositions { get; } = new()
        {
            "左上", "右上", "左下", "右下", "顶部居中", "底部居中", "左边", "右边"
        };

        public ObservableCollection<int> FontSizes { get; } = new()
        {
            8, 9, 10, 11, 12, 14, 16, 18, 20
        };

        public ObservableCollection<string> GridColors { get; } = new()
        {
            "灰色", "黑色", "蓝色", "红色", "绿色", "橙色", "紫色", "棕色"
        };
        
        // 预设模板
        public ObservableCollection<string> PresetTemplates { get; } = new()
        {
            "默认", "演示模式", "打印模式", "分析模式"
        };
        
        // 数据标签位置选项
        public ObservableCollection<string> DataLabelPositions { get; } = new()
        {
            "上方", "下方", "左侧", "右侧", "中心"
        };
        
        // 数据标签格式选项
        public ObservableCollection<string> DataLabelFormats { get; } = new()
        {
            "{0:F0}", "{0:F1}", "{0:F2}", "{0:F3}", "{0:E2}"
        };

        // 存储原始值以便取消时恢复
        private ChartSettings _originalSettings;
        private ChartSettings _currentSettings;

        public ChartSettingsViewModel(MainWindowViewModel mainViewModel)
        {
            _mainViewModel = mainViewModel;
            LoadCurrentSettings();
            UpdateColorPreview();
            
            // 订阅属性变化事件以支持实时预览
            PropertyChanged += OnPropertyChangedForPreview;
        }

        private void OnPropertyChangedForPreview(object? sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            // 如果开启了实时预览且不是预览相关的属性变化
            if (EnableRealTimePreview && 
                e.PropertyName != nameof(EnableRealTimePreview) && 
                e.PropertyName != nameof(IsPreviewActive) &&
                e.PropertyName != nameof(PreviewColors))
            {
                ApplyPreview();
            }
        }

        private void ApplyPreview()
        {
            if (!EnableRealTimePreview) return;
            
            try
            {
                IsPreviewActive = true;
                var previewSettings = CreateChartSettingsFromUI();
                _mainViewModel.ApplyChartSettings(previewSettings);
            }
            catch (Exception ex)
            {
                LIVAnalyzer.Services.Logging.LoggingService.LogWarning($"Failed to apply preview: {ex.Message}");
            }
        }

        private void LoadCurrentSettings()
        {
            // 从主视图模型加载当前设置
            _originalSettings = GetCurrentSettings();
            ApplySettings(_originalSettings);
        }

        private ChartSettings GetCurrentSettings()
        {
            // 先尝试从配置文件加载
            try
            {
                var config = LIVAnalyzer.Services.Configuration.ConfigurationManager.Instance.GetConfig();
                var chartConfig = config.Display.ChartSettings;
                
                if (chartConfig != null)
                {
                    return CreateChartSettingsFromConfig(chartConfig);
                }
            }
            catch (Exception ex)
            {
                // 记录错误但继续尝试从主视图模型获取
                LIVAnalyzer.Services.Logging.LoggingService.LogWarning($"Failed to load chart settings from config: {ex.Message}");
            }
            
            // 从主视图模型获取当前设置（使用公共方法，避免反射）
            try
            {
                var currentSettings = _mainViewModel.GetCurrentChartSettings();
                if (currentSettings != null)
                {
                    return currentSettings;
                }
            }
            catch (Exception ex)
            {
                LIVAnalyzer.Services.Logging.LoggingService.LogWarning($"Failed to get chart settings from main view model: {ex.Message}");
            }
            
            // 如果获取失败，返回默认设置
            LIVAnalyzer.Services.Logging.LoggingService.LogInformation("Using default chart settings");
            return CreateDefaultChartSettings();
        }

        // 工厂方法：从配置创建ChartSettings
        private static ChartSettings CreateChartSettingsFromConfig(LIVAnalyzer.Models.ChartSettingsConfig config)
        {
            return new ChartSettings
            {
                LineThickness = config.LineThickness,
                MarkerSize = config.MarkerSize,
                MarkerType = config.MarkerType,
                MajorGridOpacity = config.MajorGridOpacity,
                MinorGridOpacity = config.MinorGridOpacity,
                MajorGridLineStyle = config.MajorGridLineStyle,
                MinorGridLineStyle = config.MinorGridLineStyle,
                MajorGridColor = config.MajorGridColor ?? "灰色",
                MinorGridColor = config.MinorGridColor ?? "灰色",
                ColorScheme = config.ColorScheme,
                LegendPosition = config.LegendPosition,
                LegendFontSize = config.LegendFontSize,
                AxisTitleFontSize = config.AxisTitleFontSize,
                AxisLabelFontSize = config.AxisLabelFontSize,
                // 数据标签设置 - 使用默认值，因为配置中可能还没有这些字段
                ShowDataLabels = false,
                DataLabelPosition = "上方",
                DataLabelFontSize = 10,
                DataLabelFormat = "{0:F2}",
                // 效率曲线叠加设置
                ShowEfficiencyOverlay = config.ShowEfficiencyOverlay
            };
        }

        // 工厂方法：创建默认ChartSettings
        private static ChartSettings CreateDefaultChartSettings()
        {
            return new ChartSettings
            {
                LineThickness = 2.0,
                MarkerSize = 4,
                MarkerType = "无", // 修改默认标记类型为"无"
                MajorGridOpacity = 50,
                MinorGridOpacity = 25,
                MajorGridLineStyle = "实线",
                MinorGridLineStyle = "点线",
                MajorGridColor = "灰色",
                MinorGridColor = "灰色",
                ColorScheme = "默认",
                LegendPosition = "右边", // 修改默认图例位置为"右边"
                LegendFontSize = 12,
                AxisTitleFontSize = 14,
                AxisLabelFontSize = 11,
                // 数据标签设置
                ShowDataLabels = false,
                DataLabelPosition = "上方",
                DataLabelFontSize = 10,
                DataLabelFormat = "{0:F2}",
                // 效率曲线叠加设置
                ShowEfficiencyOverlay = false
            };
        }

        // 克隆ChartSettings对象
        private static ChartSettings CloneChartSettings(ChartSettings source)
        {
            return new ChartSettings
            {
                LineThickness = source.LineThickness,
                MarkerSize = source.MarkerSize,
                MarkerType = source.MarkerType,
                MajorGridOpacity = source.MajorGridOpacity,
                MinorGridOpacity = source.MinorGridOpacity,
                MajorGridLineStyle = source.MajorGridLineStyle,
                MinorGridLineStyle = source.MinorGridLineStyle,
                MajorGridColor = source.MajorGridColor,
                MinorGridColor = source.MinorGridColor,
                ColorScheme = source.ColorScheme,
                LegendPosition = source.LegendPosition,
                LegendFontSize = source.LegendFontSize,
                AxisTitleFontSize = source.AxisTitleFontSize,
                AxisLabelFontSize = source.AxisLabelFontSize,
                // 数据标签设置
                ShowDataLabels = source.ShowDataLabels,
                DataLabelPosition = source.DataLabelPosition,
                DataLabelFontSize = source.DataLabelFontSize,
                DataLabelFormat = source.DataLabelFormat,
                // 效率曲线叠加设置
                ShowEfficiencyOverlay = source.ShowEfficiencyOverlay
            };
        }

        // 从当前UI状态创建ChartSettings
        private ChartSettings CreateChartSettingsFromUI()
        {
            return new ChartSettings
            {
                LineThickness = LineThickness,
                MarkerSize = MarkerSize,
                MarkerType = SelectedMarkerType,
                MajorGridOpacity = MajorGridOpacity,
                MinorGridOpacity = MinorGridOpacity,
                MajorGridLineStyle = MajorGridLineStyle,
                MinorGridLineStyle = MinorGridLineStyle,
                MajorGridColor = MajorGridColor,
                MinorGridColor = MinorGridColor,
                ColorScheme = SelectedColorScheme,
                LegendPosition = SelectedLegendPosition,
                LegendFontSize = LegendFontSize,
                AxisTitleFontSize = AxisTitleFontSize,
                AxisLabelFontSize = AxisLabelFontSize,
                // 数据标签设置
                ShowDataLabels = ShowDataLabels,
                DataLabelPosition = DataLabelPosition,
                DataLabelFontSize = DataLabelFontSize,
                DataLabelFormat = DataLabelFormat,
                // 效率曲线叠加设置
                ShowEfficiencyOverlay = ShowEfficiencyOverlay
            };
        }

        private void ApplySettings(ChartSettings settings)
        {
            LineThickness = settings.LineThickness;
            MarkerSize = settings.MarkerSize;
            SelectedMarkerType = settings.MarkerType;
            MajorGridOpacity = settings.MajorGridOpacity;
            MinorGridOpacity = settings.MinorGridOpacity;
            MajorGridLineStyle = settings.MajorGridLineStyle;
            MinorGridLineStyle = settings.MinorGridLineStyle;
            MajorGridColor = settings.MajorGridColor;
            MinorGridColor = settings.MinorGridColor;
            SelectedColorScheme = settings.ColorScheme;
            SelectedLegendPosition = settings.LegendPosition;
            LegendFontSize = settings.LegendFontSize;
            AxisTitleFontSize = settings.AxisTitleFontSize;
            AxisLabelFontSize = settings.AxisLabelFontSize;
            // 数据标签设置
            ShowDataLabels = settings.ShowDataLabels;
            DataLabelPosition = settings.DataLabelPosition;
            DataLabelFontSize = settings.DataLabelFontSize;
            DataLabelFormat = settings.DataLabelFormat;
            // 效率曲线叠加设置
            ShowEfficiencyOverlay = settings.ShowEfficiencyOverlay;
        }

        partial void OnEnableRealTimePreviewChanged(bool value)
        {
            if (!value && IsPreviewActive)
            {
                // 关闭实时预览时，恢复原始设置
                RevertPreview();
            }
        }

        partial void OnSelectedColorSchemeChanged(string value)
        {
            UpdateColorPreview();
        }

        private void UpdateColorPreview()
        {
            var colors = GetColorSchemeColors(SelectedColorScheme);
            var newBrushes = colors.Take(8).Select(c => new SolidColorBrush(Color.FromRgb(c.R, c.G, c.B))).ToList();
            
            // 仅在颜色数量不同或颜色值不同时更新
            if (PreviewColors.Count != newBrushes.Count || !AreBrushesEqual(PreviewColors, newBrushes))
            {
                PreviewColors.Clear();
                foreach (var brush in newBrushes)
                {
                    PreviewColors.Add(brush);
                }
            }
        }

        // 比较两个画刷集合是否相等
        private static bool AreBrushesEqual(ObservableCollection<Brush> collection1, List<SolidColorBrush> collection2)
        {
            if (collection1.Count != collection2.Count) return false;
            
            for (int i = 0; i < collection1.Count; i++)
            {
                if (collection1[i] is SolidColorBrush brush1 && brush1.Color != collection2[i].Color)
                {
                    return false;
                }
            }
            return true;
        }

        private List<OxyColor> GetColorSchemeColors(string scheme)
        {
            return scheme switch
            {
                "彩虹" => new List<OxyColor>
                {
                    OxyColors.Red, OxyColors.Orange, OxyColors.Yellow,
                    OxyColors.Green, OxyColors.Blue, OxyColors.Indigo,
                    OxyColors.Violet, OxyColors.Magenta
                },
                "暖色" => new List<OxyColor>
                {
                    OxyColors.DarkRed, OxyColors.Red, OxyColors.OrangeRed,
                    OxyColors.Orange, OxyColors.Gold, OxyColors.Yellow,
                    OxyColors.LightYellow, OxyColors.PeachPuff
                },
                "冷色" => new List<OxyColor>
                {
                    OxyColors.DarkBlue, OxyColors.Blue, OxyColors.CornflowerBlue,
                    OxyColors.LightBlue, OxyColors.Cyan, OxyColors.Teal,
                    OxyColors.SeaGreen, OxyColors.LightSeaGreen
                },
                "单色渐变" => new List<OxyColor>
                {
                    OxyColor.FromRgb(0, 0, 128), OxyColor.FromRgb(0, 0, 160),
                    OxyColor.FromRgb(0, 0, 192), OxyColor.FromRgb(0, 0, 224),
                    OxyColor.FromRgb(64, 64, 255), OxyColor.FromRgb(128, 128, 255),
                    OxyColor.FromRgb(192, 192, 255), OxyColor.FromRgb(224, 224, 255)
                },
                "高对比度" => new List<OxyColor>
                {
                    OxyColors.Black, OxyColors.Red, OxyColors.Blue,
                    OxyColors.Green, OxyColors.Orange, OxyColors.Purple,
                    OxyColors.Brown, OxyColors.Gray
                },
                _ => new List<OxyColor> // 默认
                {
                    OxyColors.Blue, OxyColors.Red, OxyColors.Green,
                    OxyColors.Orange, OxyColors.Purple, OxyColors.Brown,
                    OxyColors.Pink, OxyColors.Gray
                }
            };
        }

        [RelayCommand]
        private void RestoreDefaults()
        {
            var defaults = CreateDefaultChartSettings();
            ApplySettings(defaults);
        }

        [RelayCommand]
        private void ApplyPresetTemplate(string templateName)
        {
            ChartSettings presetSettings = templateName switch
            {
                "演示模式" => CreatePresentationModeSettings(),
                "打印模式" => CreatePrintModeSettings(),
                "分析模式" => CreateAnalysisModeSettings(),
                _ => CreateDefaultChartSettings()
            };
            
            ApplySettings(presetSettings);
            
            // 记录使用的预设
            LIVAnalyzer.Services.Logging.LoggingService.LogInformation($"Applied preset template: {templateName}");
        }

        // 演示模式：加粗线条，大字体，高对比度
        private static ChartSettings CreatePresentationModeSettings()
        {
            return new ChartSettings
            {
                LineThickness = 3.5,
                MarkerSize = 6,
                MarkerType = "圆形",
                MajorGridOpacity = 60,
                MinorGridOpacity = 30,
                MajorGridLineStyle = "实线",
                MinorGridLineStyle = "点线",
                MajorGridColor = "黑色",
                MinorGridColor = "灰色",
                ColorScheme = "高对比度",
                LegendPosition = "右上",
                LegendFontSize = 16,
                AxisTitleFontSize = 18,
                AxisLabelFontSize = 14
            };
        }

        // 打印模式：适中线条，清晰对比，黑白友好
        private static ChartSettings CreatePrintModeSettings()
        {
            return new ChartSettings
            {
                LineThickness = 2.0,
                MarkerSize = 4,
                MarkerType = "方形",
                MajorGridOpacity = 40,
                MinorGridOpacity = 20,
                MajorGridLineStyle = "实线",
                MinorGridLineStyle = "点线",
                MajorGridColor = "黑色",
                MinorGridColor = "灰色",
                ColorScheme = "默认",
                LegendPosition = "底部居中",
                LegendFontSize = 11,
                AxisTitleFontSize = 12,
                AxisLabelFontSize = 10
            };
        }

        // 分析模式：细线条，详细网格，小字体
        private static ChartSettings CreateAnalysisModeSettings()
        {
            return new ChartSettings
            {
                LineThickness = 1.5,
                MarkerSize = 3,
                MarkerType = "无",
                MajorGridOpacity = 70,
                MinorGridOpacity = 40,
                MajorGridLineStyle = "实线",
                MinorGridLineStyle = "点线",
                MajorGridColor = "灰色",
                MinorGridColor = "灰色",
                ColorScheme = "彩虹",
                LegendPosition = "右边",
                LegendFontSize = 10,
                AxisTitleFontSize = 12,
                AxisLabelFontSize = 9
            };
        }

        [RelayCommand]
        private void SaveAsDefault()
        {
            try
            {
                // 创建当前设置
                var currentSettings = CreateChartSettingsFromUI();

                // 保存到配置文件
                var config = LIVAnalyzer.Services.Configuration.ConfigurationManager.Instance.GetConfig();
                config.Display.ChartSettings = ConvertToChartSettingsConfig(currentSettings);
                
                LIVAnalyzer.Services.Configuration.ConfigurationManager.Instance.SaveConfiguration();
                
                // 记录成功日志
                LIVAnalyzer.Services.Logging.LoggingService.LogInformation("Chart settings saved as default successfully");
                
                // 显示成功消息
                System.Windows.MessageBox.Show("图表设置已保存为默认值", "保存成功", 
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                // 记录详细错误信息
                LIVAnalyzer.Services.Logging.LoggingService.LogError(ex, "Failed to save chart settings as default");
                
                // 显示用户友好的错误消息
                System.Windows.MessageBox.Show($"保存图表设置失败：{ex.Message}", "错误", 
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        // 转换为配置对象
        private static LIVAnalyzer.Models.ChartSettingsConfig ConvertToChartSettingsConfig(ChartSettings settings)
        {
            return new LIVAnalyzer.Models.ChartSettingsConfig
            {
                LineThickness = settings.LineThickness,
                MarkerSize = settings.MarkerSize,
                MarkerType = settings.MarkerType,
                MajorGridOpacity = settings.MajorGridOpacity,
                MinorGridOpacity = settings.MinorGridOpacity,
                MajorGridLineStyle = settings.MajorGridLineStyle,
                MinorGridLineStyle = settings.MinorGridLineStyle,
                MajorGridColor = settings.MajorGridColor,
                MinorGridColor = settings.MinorGridColor,
                ColorScheme = settings.ColorScheme,
                LegendPosition = settings.LegendPosition,
                LegendFontSize = settings.LegendFontSize,
                AxisTitleFontSize = settings.AxisTitleFontSize,
                AxisLabelFontSize = settings.AxisLabelFontSize,
                ShowEfficiencyOverlay = settings.ShowEfficiencyOverlay
            };
        }

        [RelayCommand]
        private void Apply()
        {
            _currentSettings = CreateChartSettingsFromUI();

            // 应用设置到主视图模型
            _mainViewModel.ApplyChartSettings(_currentSettings);
            _isApplied = true;
            
            // 关闭对话框
            OnRequestClose?.Invoke(true);
        }

        [RelayCommand]
        private void Preview()
        {
            if (!IsPreviewActive)
            {
                // 应用预览
                IsPreviewActive = true;
                var previewSettings = CreateChartSettingsFromUI();
                _mainViewModel.ApplyChartSettings(previewSettings);
            }
        }

        [RelayCommand]
        private void RevertPreview()
        {
            if (IsPreviewActive)
            {
                // 还原到原始设置
                IsPreviewActive = false;
                _mainViewModel.ApplyChartSettings(_originalSettings);
            }
        }

        [RelayCommand]
        private void Cancel()
        {
            // 如果开启了预览或已应用设置，恢复原始设置
            if (IsPreviewActive || _isApplied)
            {
                _mainViewModel.ApplyChartSettings(_originalSettings);
            }
            OnRequestClose?.Invoke(false);
        }

        public Action<bool>? OnRequestClose { get; set; }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    // 取消事件订阅
                    PropertyChanged -= OnPropertyChangedForPreview;
                }
                _disposed = true;
            }
        }
    }

    public class ChartSettings
    {
        public double LineThickness { get; set; }
        public int MarkerSize { get; set; }
        public string MarkerType { get; set; } = "无"; // 修改默认标记类型为"无"
        public int MajorGridOpacity { get; set; }
        public int MinorGridOpacity { get; set; }
        public string MajorGridLineStyle { get; set; } = "实线";
        public string MinorGridLineStyle { get; set; } = "点线";
        public string MajorGridColor { get; set; } = "灰色";
        public string MinorGridColor { get; set; } = "灰色";
        public string ColorScheme { get; set; } = "默认";
        public string LegendPosition { get; set; } = "右边"; // 修改默认图例位置为"右边"
        public int LegendFontSize { get; set; }
        public int AxisTitleFontSize { get; set; }
        public int AxisLabelFontSize { get; set; }
        
        // 数据标签设置
        public bool ShowDataLabels { get; set; } = false;
        public string DataLabelPosition { get; set; } = "上方";
        public int DataLabelFontSize { get; set; } = 10;
        public string DataLabelFormat { get; set; } = "{0:F2}";

        // 坐标轴范围设置
        public bool EnableCustomXAxisRange { get; set; } = false;
        public double XAxisMinimum { get; set; } = 0;
        public double XAxisMaximum { get; set; } = 100;
        public bool EnableCustomYAxisRange { get; set; } = false;
        public double YAxisMinimum { get; set; } = 0;
        public double YAxisMaximum { get; set; } = 100;
        public bool EnableCustomSecondaryYAxisRange { get; set; } = false;
        public double SecondaryYAxisMinimum { get; set; } = 0;
        public double SecondaryYAxisMaximum { get; set; } = 100;

        // 效率曲线叠加设置
        public bool ShowEfficiencyOverlay { get; set; } = false;
    }
}