using LIVAnalyzer.Models;
using LIVAnalyzer.Data.Interfaces;
using System.Collections.Concurrent;

namespace LIVAnalyzer.Core.Services
{
    /// <summary>
    /// 简化版渐进式数据加载器 - 避免复杂的类型问题
    /// </summary>
    public class SimpleProgressiveLoader
    {
        private readonly IDataLoader _csvLoader;
        private readonly IDataLoader _excelLoader;
        
        public event EventHandler<ProgressEventArgs>? ProgressUpdated;
        public event EventHandler<FileCompletedEventArgs>? FileCompleted;
        
        public SimpleProgressiveLoader(IDataLoader csvLoader, IDataLoader excelLoader)
        {
            _csvLoader = csvLoader;
            _excelLoader = excelLoader;
        }
        
        /// <summary>
        /// 渐进式加载文件
        /// </summary>
        public async Task<List<LIVMeasurementData>> LoadFilesAsync(string[] filePaths, CancellationToken cancellationToken = default)
        {
            var results = new List<LIVMeasurementData>();
            var totalFiles = filePaths.Length;
            
            ReportProgress("开始加载文件...", 0, totalFiles);
            
            // 阶段1：快速验证文件
            var validFiles = ValidateFiles(filePaths);
            ReportProgress($"验证完成，找到 {validFiles.Count} 个有效文件", 0, validFiles.Count);
            
            // 阶段2：并行加载基本数据
            var loadTasks = validFiles.Select(async (filePath, index) =>
            {
                try
                {
                    var data = await LoadSingleFileAsync(filePath, cancellationToken);
                    if (data != null)
                    {
                        // 立即通知文件加载完成
                        FileCompleted?.Invoke(this, new FileCompletedEventArgs
                        {
                            FilePath = filePath,
                            Data = data,
                            Index = index,
                            Stage = "DataLoaded"
                        });
                        
                        ReportProgress($"已加载: {Path.GetFileName(filePath)}", index + 1, validFiles.Count);
                        return data;
                    }
                }
                catch (Exception ex)
                {
                    ReportProgress($"加载失败: {Path.GetFileName(filePath)} - {ex.Message}", index + 1, validFiles.Count);
                }
                return null;
            }).ToArray();
            
            var loadedData = await Task.WhenAll(loadTasks);
            results.AddRange(loadedData.Where(d => d != null)!);
            
            // 阶段3：后台计算参数
            await ProcessParametersAsync(results, cancellationToken);
            
            ReportProgress("所有文件处理完成", results.Count, results.Count);
            return results;
        }
        
        private List<string> ValidateFiles(string[] filePaths)
        {
            var validFiles = new List<string>();
            
            foreach (var filePath in filePaths)
            {
                if (File.Exists(filePath))
                {
                    var extension = Path.GetExtension(filePath).ToLower();
                    if (extension == ".csv" || extension == ".xlsx" || extension == ".xls")
                    {
                        validFiles.Add(filePath);
                    }
                }
            }
            
            return validFiles;
        }
        
        private async Task<LIVMeasurementData?> LoadSingleFileAsync(string filePath, CancellationToken cancellationToken)
        {
            var extension = Path.GetExtension(filePath).ToLower();
            LIVMeasurementData? data = null;
            
            if (extension == ".csv")
            {
                data = await _csvLoader.LoadCsvDataAsync(filePath);
            }
            else if (extension == ".xlsx" || extension == ".xls")
            {
                data = await _excelLoader.LoadExcelDataAsync(filePath);
            }
            
            if (data != null)
            {
                // 设置基本信息
                data.IsProcessed = false;
                data.Parameters = new LIVParameters();
            }
            
            return data;
        }
        
        private async Task ProcessParametersAsync(List<LIVMeasurementData> dataList, CancellationToken cancellationToken)
        {
            var processor = new LIVAnalyzer.Core.Processors.LIVDataProcessor();
            var semaphore = new SemaphoreSlim(Environment.ProcessorCount, Environment.ProcessorCount);
            
            var tasks = dataList.Select(async (data, index) =>
            {
                await semaphore.WaitAsync(cancellationToken);
                try
                {
                    // 计算参数
                    var parameters = processor.CalculateParameters(data);
                    data.Parameters = parameters;
                    data.IsProcessed = true;
                    
                    // 通知参数计算完成
                    FileCompleted?.Invoke(this, new FileCompletedEventArgs
                    {
                        FilePath = data.FileName ?? "",
                        Data = data,
                        Index = index,
                        Stage = "ParametersCalculated"
                    });
                    
                    ReportProgress($"参数计算完成: {data.FileName}", index + 1, dataList.Count);
                }
                catch (Exception ex)
                {
                    ReportProgress($"参数计算失败: {data.FileName} - {ex.Message}", index + 1, dataList.Count);
                }
                finally
                {
                    semaphore.Release();
                }
            });
            
            await Task.WhenAll(tasks);
        }
        
        private void ReportProgress(string message, int current, int total)
        {
            ProgressUpdated?.Invoke(this, new ProgressEventArgs
            {
                Message = message,
                Current = current,
                Total = total,
                Percentage = total > 0 ? (double)current / total * 100 : 0
            });
        }
    }
    
    /// <summary>
    /// 进度事件参数
    /// </summary>
    public class ProgressEventArgs : EventArgs
    {
        public string Message { get; set; } = string.Empty;
        public int Current { get; set; }
        public int Total { get; set; }
        public double Percentage { get; set; }
    }
    
    /// <summary>
    /// 文件完成事件参数
    /// </summary>
    public class FileCompletedEventArgs : EventArgs
    {
        public string FilePath { get; set; } = string.Empty;
        public LIVMeasurementData? Data { get; set; }
        public int Index { get; set; }
        public string Stage { get; set; } = string.Empty;
    }
}
