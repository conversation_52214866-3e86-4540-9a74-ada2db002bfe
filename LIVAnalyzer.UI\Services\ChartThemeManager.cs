using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Media;
using OxyPlot;
using OxyPlot.Legends;
using OxyPlot.Series;
using LIVAnalyzer.Services.Logging;
using LIVAnalyzer.UI.Services;

namespace LIVAnalyzer.UI.Services
{
    /// <summary>
    /// 图表主题定义
    /// </summary>
    public class ChartTheme
    {
        public string Name { get; set; } = string.Empty;
        public bool IsDarkTheme { get; set; }
        
        // 基础颜色
        public OxyColor BackgroundColor { get; set; }
        public OxyColor PlotAreaBackgroundColor { get; set; }
        public OxyColor TextColor { get; set; }
        public OxyColor AxisColor { get; set; }
        public OxyColor BorderColor { get; set; }
        
        // 网格颜色
        public OxyColor MajorGridColor { get; set; }
        public OxyColor MinorGridColor { get; set; }
        
        // 图例颜色
        public OxyColor LegendBackgroundColor { get; set; }
        public OxyColor LegendBorderColor { get; set; }
        public OxyColor LegendTextColor { get; set; }
        
        // 特定轴颜色
        public OxyColor PowerAxisColor { get; set; }
        public OxyColor VoltageAxisColor { get; set; }
        
        // 系列颜色调色板
        public List<OxyColor> SeriesColors { get; set; } = new();
        
        // 状态颜色
        public OxyColor SuccessColor { get; set; }
        public OxyColor WarningColor { get; set; }
        public OxyColor ErrorColor { get; set; }
        public OxyColor InfoColor { get; set; }
    }

    /// <summary>
    /// 图表样式配置
    /// </summary>
    public class ChartStyleConfig
    {
        // 字体设置
        public double TitleFontSize { get; set; } = 16;
        public double AxisTitleFontSize { get; set; } = 14;
        public double AxisLabelFontSize { get; set; } = 11;
        public double LegendFontSize { get; set; } = 12;
        
        // 线条设置
        public double DefaultStrokeThickness { get; set; } = 2.0;
        public double AxisLineThickness { get; set; } = 1.0;
        public double BorderThickness { get; set; } = 1.0;
        
        // 边距设置
        public OxyThickness PlotMargins { get; set; } = new(60, 10, 60, 40);
        
        // 网格设置
        public LineStyle MajorGridLineStyle { get; set; } = LineStyle.Solid;
        public LineStyle MinorGridLineStyle { get; set; } = LineStyle.Dot;
        public byte MajorGridOpacity { get; set; } = 128;
        public byte MinorGridOpacity { get; set; } = 64;
    }

    /// <summary>
    /// 统一的图表主题管理器
    /// </summary>
    public class ChartThemeManager : IDisposable
    {
        private static readonly Lazy<ChartThemeManager> _instance = new(() => new ChartThemeManager());
        public static ChartThemeManager Instance => _instance.Value;

        private readonly Dictionary<string, ChartTheme> _themes = new();
        private readonly ChartStyleConfig _styleConfig = new();
        private ChartTheme _currentTheme;
        private bool _disposed = false;

        /// <summary>
        /// 当前主题改变事件
        /// </summary>
        public event EventHandler<ChartTheme>? ThemeChanged;

        /// <summary>
        /// 当前主题
        /// </summary>
        public ChartTheme CurrentTheme => _currentTheme;

        /// <summary>
        /// 样式配置
        /// </summary>
        public ChartStyleConfig StyleConfig => _styleConfig;

        private ChartThemeManager()
        {
            InitializeBuiltInThemes();
            _currentTheme = _themes["Light"]; // 默认使用浅色主题
            
            // 订阅系统主题变化
            if (NativeFluentThemeService.Instance != null)
            {
                // 这里可以添加主题变化监听
            }
        }

        /// <summary>
        /// 初始化内置主题
        /// </summary>
        private void InitializeBuiltInThemes()
        {
            // 浅色主题
            var lightTheme = new ChartTheme
            {
                Name = "Light",
                IsDarkTheme = false,
                BackgroundColor = OxyColor.FromRgb(255, 255, 255),
                PlotAreaBackgroundColor = OxyColor.FromRgb(255, 255, 255),
                TextColor = OxyColor.FromRgb(0, 0, 0),
                AxisColor = OxyColor.FromRgb(0, 0, 0),
                BorderColor = OxyColor.FromRgb(128, 128, 128),
                MajorGridColor = OxyColor.FromRgb(225, 225, 225),
                MinorGridColor = OxyColor.FromRgb(240, 240, 240),
                LegendBackgroundColor = OxyColor.FromAColor(240, OxyColor.FromRgb(255, 255, 255)),
                LegendBorderColor = OxyColor.FromRgb(128, 128, 128),
                LegendTextColor = OxyColor.FromRgb(0, 0, 0),
                PowerAxisColor = OxyColor.FromRgb(0, 100, 200),
                VoltageAxisColor = OxyColor.FromRgb(200, 0, 0),
                SeriesColors = new List<OxyColor>
                {
                    OxyColor.FromRgb(0, 100, 200),   // 蓝色
                    OxyColor.FromRgb(200, 0, 0),     // 红色
                    OxyColor.FromRgb(0, 150, 0),     // 绿色
                    OxyColor.FromRgb(255, 140, 0),   // 橙色
                    OxyColor.FromRgb(128, 0, 128),   // 紫色
                    OxyColor.FromRgb(255, 20, 147),  // 深粉色
                    OxyColor.FromRgb(184, 134, 11),  // 深黄色
                    OxyColor.FromRgb(0, 139, 139)    // 深青色
                },
                SuccessColor = OxyColor.FromRgb(16, 185, 129),
                WarningColor = OxyColor.FromRgb(245, 158, 11),
                ErrorColor = OxyColor.FromRgb(239, 68, 68),
                InfoColor = OxyColor.FromRgb(59, 130, 246)
            };

            // 深色主题
            var darkTheme = new ChartTheme
            {
                Name = "Dark",
                IsDarkTheme = true,
                BackgroundColor = OxyColor.FromRgb(24, 24, 24),
                PlotAreaBackgroundColor = OxyColor.FromRgb(32, 32, 32),
                TextColor = OxyColor.FromRgb(255, 255, 255),
                AxisColor = OxyColor.FromRgb(255, 255, 255),
                BorderColor = OxyColor.FromRgb(128, 128, 128),
                MajorGridColor = OxyColor.FromRgb(64, 64, 64),
                MinorGridColor = OxyColor.FromRgb(48, 48, 48),
                LegendBackgroundColor = OxyColor.FromAColor(220, OxyColor.FromRgb(32, 32, 32)),
                LegendBorderColor = OxyColor.FromRgb(255, 255, 255),
                LegendTextColor = OxyColor.FromRgb(255, 255, 255),
                PowerAxisColor = OxyColor.FromRgb(100, 149, 237),
                VoltageAxisColor = OxyColor.FromRgb(220, 20, 60),
                SeriesColors = new List<OxyColor>
                {
                    OxyColor.FromRgb(100, 149, 237), // 浅蓝色
                    OxyColor.FromRgb(220, 20, 60),   // 浅红色
                    OxyColor.FromRgb(50, 205, 50),   // 浅绿色
                    OxyColor.FromRgb(255, 165, 0),   // 橙色
                    OxyColor.FromRgb(138, 43, 226),  // 紫色
                    OxyColor.FromRgb(255, 192, 203), // 粉色
                    OxyColor.FromRgb(255, 255, 0),   // 黄色
                    OxyColor.FromRgb(0, 255, 255)    // 青色
                },
                SuccessColor = OxyColor.FromRgb(34, 197, 94),
                WarningColor = OxyColor.FromRgb(251, 191, 36),
                ErrorColor = OxyColor.FromRgb(248, 113, 113),
                InfoColor = OxyColor.FromRgb(96, 165, 250)
            };

            _themes["Light"] = lightTheme;
            _themes["Dark"] = darkTheme;
        }

        /// <summary>
        /// 切换主题
        /// </summary>
        /// <param name="themeName">主题名称</param>
        public void SwitchTheme(string themeName)
        {
            if (_themes.TryGetValue(themeName, out var theme))
            {
                var oldTheme = _currentTheme;
                _currentTheme = theme;
                
                LoggingService.LogInformation($"图表主题切换: {oldTheme?.Name} -> {theme.Name}");
                ThemeChanged?.Invoke(this, theme);
            }
            else
            {
                LoggingService.LogWarning($"未找到图表主题: {themeName}");
            }
        }

        /// <summary>
        /// 根据系统主题自动切换
        /// </summary>
        public void SwitchToSystemTheme()
        {
            try
            {
                var isDark = NativeFluentThemeService.Instance?.IsCurrentlyDarkMode() ?? false;
                var themeName = isDark ? "Dark" : "Light";
                SwitchTheme(themeName);
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "自动切换图表主题失败");
            }
        }

        /// <summary>
        /// 添加自定义主题
        /// </summary>
        /// <param name="theme">主题对象</param>
        public void AddTheme(ChartTheme theme)
        {
            if (string.IsNullOrEmpty(theme.Name))
            {
                throw new ArgumentException("主题名称不能为空", nameof(theme));
            }

            _themes[theme.Name] = theme;
            LoggingService.LogInformation($"添加图表主题: {theme.Name}");
        }

        /// <summary>
        /// 移除主题
        /// </summary>
        /// <param name="themeName">主题名称</param>
        public bool RemoveTheme(string themeName)
        {
            if (_themes.ContainsKey(themeName))
            {
                _themes.Remove(themeName);
                LoggingService.LogInformation($"移除图表主题: {themeName}");
                return true;
            }
            return false;
        }

        /// <summary>
        /// 获取所有可用主题名称
        /// </summary>
        public IEnumerable<string> GetAvailableThemes()
        {
            return _themes.Keys;
        }

        /// <summary>
        /// 获取指定主题
        /// </summary>
        /// <param name="themeName">主题名称</param>
        public ChartTheme? GetTheme(string themeName)
        {
            return _themes.TryGetValue(themeName, out var theme) ? theme : null;
        }

        /// <summary>
        /// 应用主题到图表模型
        /// </summary>
        /// <param name="plotModel">图表模型</param>
        public void ApplyThemeToPlot(PlotModel? plotModel)
        {
            if (plotModel == null || _currentTheme == null) return;

            try
            {
                // 应用基础样式
                plotModel.Background = _currentTheme.BackgroundColor;
                plotModel.PlotAreaBackground = _currentTheme.PlotAreaBackgroundColor;
                plotModel.TextColor = _currentTheme.TextColor;
                plotModel.PlotAreaBorderColor = _currentTheme.BorderColor;
                plotModel.PlotAreaBorderThickness = new OxyThickness(_styleConfig.BorderThickness);
                plotModel.PlotMargins = _styleConfig.PlotMargins;

                // 应用轴样式
                foreach (var axis in plotModel.Axes)
                {
                    ApplyThemeToAxis(axis);
                }

                // 应用图例样式
                ApplyThemeToLegends(plotModel);

                // 应用系列样式
                ApplyThemeToSeries(plotModel);

                plotModel.InvalidatePlot(true);
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "应用图表主题失败");
            }
        }

        /// <summary>
        /// 应用主题到轴
        /// </summary>
        private void ApplyThemeToAxis(OxyPlot.Axes.Axis axis)
        {
            axis.TextColor = _currentTheme.TextColor;
            axis.AxislineColor = _currentTheme.AxisColor;
            axis.AxislineStyle = LineStyle.Solid;
            axis.AxislineThickness = _styleConfig.AxisLineThickness;
            axis.FontSize = _styleConfig.AxisLabelFontSize;
            axis.TitleFontSize = _styleConfig.AxisTitleFontSize;

            // 网格样式
            axis.MajorGridlineColor = OxyColor.FromAColor(_styleConfig.MajorGridOpacity, _currentTheme.MajorGridColor);
            axis.MinorGridlineColor = OxyColor.FromAColor(_styleConfig.MinorGridOpacity, _currentTheme.MinorGridColor);
            axis.MajorGridlineStyle = _styleConfig.MajorGridLineStyle;
            axis.MinorGridlineStyle = _styleConfig.MinorGridLineStyle;

            // 特定轴颜色
            if (!string.IsNullOrEmpty(axis.Key))
            {
                switch (axis.Key)
                {
                    case "PowerAxis":
                        axis.TitleColor = _currentTheme.PowerAxisColor;
                        axis.TicklineColor = _currentTheme.PowerAxisColor;
                        break;
                    case "VoltageAxis":
                        axis.TitleColor = _currentTheme.VoltageAxisColor;
                        axis.TicklineColor = _currentTheme.VoltageAxisColor;
                        break;
                    default:
                        axis.TicklineColor = _currentTheme.AxisColor;
                        break;
                }
            }
            else
            {
                axis.TicklineColor = _currentTheme.AxisColor;
            }
        }

        /// <summary>
        /// 应用主题到图例
        /// </summary>
        private void ApplyThemeToLegends(PlotModel plotModel)
        {
            foreach (var legend in plotModel.Legends)
            {
                legend.LegendBackground = _currentTheme.LegendBackgroundColor;
                legend.LegendBorder = _currentTheme.LegendBorderColor;
                legend.LegendTextColor = _currentTheme.LegendTextColor;
                legend.LegendFontSize = _styleConfig.LegendFontSize;
                legend.LegendBorderThickness = _styleConfig.BorderThickness;
            }
        }

        /// <summary>
        /// 应用主题到系列
        /// </summary>
        private void ApplyThemeToSeries(PlotModel plotModel)
        {
            var seriesIndex = 0;
            foreach (var series in plotModel.Series)
            {
                if (series is LineSeries lineSeries)
                {
                    var color = GetSeriesColor(seriesIndex);
                    lineSeries.Color = color;
                    lineSeries.StrokeThickness = _styleConfig.DefaultStrokeThickness;
                    seriesIndex++;
                }
            }
        }

        /// <summary>
        /// 获取系列颜色
        /// </summary>
        /// <param name="index">系列索引</param>
        public OxyColor GetSeriesColor(int index)
        {
            if (_currentTheme.SeriesColors.Any())
            {
                return _currentTheme.SeriesColors[index % _currentTheme.SeriesColors.Count];
            }
            
            // 后备颜色
            return _currentTheme.IsDarkTheme ? 
                OxyColor.FromRgb(100, 149, 237) : 
                OxyColor.FromRgb(0, 100, 200);
        }

        /// <summary>
        /// 获取状态颜色
        /// </summary>
        /// <param name="status">状态类型</param>
        public OxyColor GetStatusColor(string status)
        {
            return status.ToLower() switch
            {
                "success" => _currentTheme.SuccessColor,
                "warning" => _currentTheme.WarningColor,
                "error" => _currentTheme.ErrorColor,
                "info" => _currentTheme.InfoColor,
                _ => _currentTheme.TextColor
            };
        }

        /// <summary>
        /// 更新样式配置
        /// </summary>
        /// <param name="config">新的样式配置</param>
        public void UpdateStyleConfig(ChartStyleConfig config)
        {
            _styleConfig.TitleFontSize = config.TitleFontSize;
            _styleConfig.AxisTitleFontSize = config.AxisTitleFontSize;
            _styleConfig.AxisLabelFontSize = config.AxisLabelFontSize;
            _styleConfig.LegendFontSize = config.LegendFontSize;
            _styleConfig.DefaultStrokeThickness = config.DefaultStrokeThickness;
            _styleConfig.AxisLineThickness = config.AxisLineThickness;
            _styleConfig.BorderThickness = config.BorderThickness;
            _styleConfig.PlotMargins = config.PlotMargins;
            _styleConfig.MajorGridLineStyle = config.MajorGridLineStyle;
            _styleConfig.MinorGridLineStyle = config.MinorGridLineStyle;
            _styleConfig.MajorGridOpacity = config.MajorGridOpacity;
            _styleConfig.MinorGridOpacity = config.MinorGridOpacity;

            LoggingService.LogInformation("图表样式配置已更新");
        }

        public void Dispose()
        {
            if (_disposed) return;

            _disposed = true;
            _themes.Clear();
            
            GC.SuppressFinalize(this);
        }
    }
}