using LIVAnalyzer.Models;
using OfficeOpenXml;
using System;
using System.Collections.Generic;
using System.Linq;

namespace LIVAnalyzer.Data.Sampling
{
    /// <summary>
    /// 智能采样器，实现渐进式数据加载的采样策略
    /// </summary>
    public class SmartSampler
    {
        private readonly SamplingConfig _config;

        public SmartSampler(SamplingConfig? config = null)
        {
            _config = config ?? new SamplingConfig();
        }

        /// <summary>
        /// 生成智能采样结果
        /// </summary>
        /// <param name="worksheet">Excel工作表</param>
        /// <param name="hasHeader">是否有标题行</param>
        /// <returns>采样结果</returns>
        public SamplingResult GenerateSamplingResult(ExcelWorksheet worksheet, bool hasHeader = true)
        {
            var totalRows = worksheet.Dimension?.Rows ?? 0;
            var startRow = hasHeader ? 2 : 1; // 跳过标题行
            var dataRows = totalRows - (hasHeader ? 1 : 0);

            if (dataRows <= 0)
            {
                return new SamplingResult { TotalRows = totalRows };
            }

            var allIndices = Enumerable.Range(startRow, dataRows).ToList();
            var sampledIndices = new List<int>();

            if (_config.EnableSmartSampling)
            {
                sampledIndices = GenerateSmartSamplingIndices(allIndices, worksheet);
            }
            else
            {
                sampledIndices = GenerateUniformSamplingIndices(allIndices);
            }

            // 确保采样点数在合理范围内
            sampledIndices = AdjustSampleSize(sampledIndices, allIndices);

            var remainingIndices = allIndices.Except(sampledIndices).ToList();

            return new SamplingResult
            {
                SampledIndices = sampledIndices.OrderBy(x => x).ToList(),
                RemainingIndices = remainingIndices.OrderBy(x => x).ToList(),
                TotalRows = totalRows
            };
        }

        /// <summary>
        /// 智能采样：在关键区域增加采样密度
        /// </summary>
        private List<int> GenerateSmartSamplingIndices(List<int> allIndices, ExcelWorksheet worksheet)
        {
            var sampledIndices = new HashSet<int>();
            var totalCount = allIndices.Count;

            // 1. 基础均匀采样
            for (int i = 0; i < allIndices.Count; i += _config.InitialSamplingRate)
            {
                sampledIndices.Add(allIndices[i]);
            }

            // 2. 关键区域密集采样（通常阈值在中间区域）
            var criticalStart = (int)(totalCount * _config.CriticalRegionStartRatio);
            var criticalEnd = (int)(totalCount * _config.CriticalRegionEndRatio);

            for (int i = criticalStart; i < criticalEnd && i < allIndices.Count; i += _config.CriticalRegionSamplingRate)
            {
                sampledIndices.Add(allIndices[i]);
            }

            // 3. 边界点采样（首尾重要）
            if (allIndices.Count > 0)
            {
                sampledIndices.Add(allIndices[0]); // 第一个点
                sampledIndices.Add(allIndices[^1]); // 最后一个点

                // 四分位点
                if (allIndices.Count > 4)
                {
                    sampledIndices.Add(allIndices[allIndices.Count / 4]);
                    sampledIndices.Add(allIndices[allIndices.Count / 2]);
                    sampledIndices.Add(allIndices[3 * allIndices.Count / 4]);
                }
            }

            // 4. 尝试识别数据变化剧烈区域（如果可能）
            try
            {
                var changePoints = DetectChangePoints(allIndices, worksheet);
                foreach (var point in changePoints)
                {
                    sampledIndices.Add(point);
                    // 在变化点周围增加采样
                    for (int offset = -2; offset <= 2; offset++)
                    {
                        var neighborIndex = Array.IndexOf(allIndices.ToArray(), point) + offset;
                        if (neighborIndex >= 0 && neighborIndex < allIndices.Count)
                        {
                            sampledIndices.Add(allIndices[neighborIndex]);
                        }
                    }
                }
            }
            catch
            {
                // 变化点检测失败时忽略，使用基础采样
            }

            return sampledIndices.ToList();
        }

        /// <summary>
        /// 均匀采样
        /// </summary>
        private List<int> GenerateUniformSamplingIndices(List<int> allIndices)
        {
            var sampledIndices = new List<int>();

            for (int i = 0; i < allIndices.Count; i += _config.InitialSamplingRate)
            {
                sampledIndices.Add(allIndices[i]);
            }

            return sampledIndices;
        }

        /// <summary>
        /// 调整采样大小到合理范围
        /// </summary>
        private List<int> AdjustSampleSize(List<int> sampledIndices, List<int> allIndices)
        {
            var result = sampledIndices.ToList();

            // 如果采样点太少，增加更多点
            while (result.Count < _config.MinimumSamplePoints && result.Count < allIndices.Count)
            {
                var missing = _config.MinimumSamplePoints - result.Count;
                var availableIndices = allIndices.Except(result).ToList();

                if (!availableIndices.Any()) break;

                // 均匀添加缺失的点
                var step = Math.Max(1, availableIndices.Count / missing);
                for (int i = 0; i < availableIndices.Count && result.Count < _config.MinimumSamplePoints; i += step)
                {
                    result.Add(availableIndices[i]);
                }
            }

            // 如果采样点太多，减少点
            if (result.Count > _config.MaximumInitialSamplePoints)
            {
                result = result.OrderBy(x => x).ToList();
                var step = (double)result.Count / _config.MaximumInitialSamplePoints;
                var reducedResult = new List<int>();

                for (int i = 0; i < _config.MaximumInitialSamplePoints; i++)
                {
                    var index = (int)(i * step);
                    if (index < result.Count)
                    {
                        reducedResult.Add(result[index]);
                    }
                }

                result = reducedResult;
            }

            return result.Distinct().OrderBy(x => x).ToList();
        }

        /// <summary>
        /// 检测数据变化剧烈的点（简单的梯度检测）
        /// </summary>
        private List<int> DetectChangePoints(List<int> allIndices, ExcelWorksheet worksheet)
        {
            var changePoints = new List<int>();

            if (allIndices.Count < 10) return changePoints; // 数据太少，跳过检测

            try
            {
                // 寻找电流或功率列
                int dataColumn = -1;
                for (int col = 1; col <= Math.Min(worksheet.Dimension?.Columns ?? 0, 5); col++)
                {
                    var header = worksheet.Cells[1, col].Text?.Trim().ToLower();
                    if (header != null && (header.Contains("power") || header.Contains("current")))
                    {
                        dataColumn = col;
                        break;
                    }
                }

                if (dataColumn == -1) return changePoints;

                // 采样检查变化率
                var sampleStep = Math.Max(1, allIndices.Count / 50); // 最多检查50个点
                var values = new List<(int Row, double Value)>();

                for (int i = 0; i < allIndices.Count; i += sampleStep)
                {
                    var row = allIndices[i];
                    var cellValue = worksheet.Cells[row, dataColumn].Value;

                    if (cellValue != null && double.TryParse(cellValue.ToString(), out var numValue))
                    {
                        values.Add((row, numValue));
                    }
                }

                // 计算梯度变化
                for (int i = 1; i < values.Count - 1; i++)
                {
                    var prev = values[i - 1];
                    var curr = values[i];
                    var next = values[i + 1];

                    var grad1 = Math.Abs(curr.Value - prev.Value);
                    var grad2 = Math.Abs(next.Value - curr.Value);

                    // 如果梯度变化超过阈值，认为是变化点
                    if (grad1 > 0 && grad2 > 0)
                    {
                        var gradChange = Math.Abs(grad2 - grad1) / Math.Max(grad1, grad2);
                        if (gradChange > 0.5) // 50%的梯度变化
                        {
                            changePoints.Add(curr.Row);
                        }
                    }
                }
            }
            catch
            {
                // 检测失败时返回空列表
                return new List<int>();
            }

            return changePoints.Take(10).ToList(); // 最多返回10个变化点
        }

        /// <summary>
        /// 生成剩余数据的分批加载策略
        /// </summary>
        public List<List<int>> GenerateBatchLoadingStrategy(List<int> remainingIndices, int batchCount = 3)
        {
            if (!remainingIndices.Any())
                return new List<List<int>>();

            var batches = new List<List<int>>();
            var batchSize = Math.Max(1, remainingIndices.Count / batchCount);

            for (int i = 0; i < remainingIndices.Count; i += batchSize)
            {
                var batch = remainingIndices.Skip(i).Take(batchSize).ToList();
                if (batch.Any())
                {
                    batches.Add(batch);
                }
            }

            return batches;
        }
    }
}