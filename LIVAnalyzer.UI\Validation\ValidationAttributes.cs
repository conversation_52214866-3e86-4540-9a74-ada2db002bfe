using System;
using System.ComponentModel.DataAnnotations;
using System.Globalization;

namespace LIVAnalyzer.UI.Validation
{
    /// <summary>
    /// 电流值验证属性
    /// </summary>
    [AttributeUsage(AttributeTargets.Property | AttributeTargets.Field, AllowMultiple = false)]
    public class CurrentValueAttribute : ValidationAttribute
    {
        public double MinValue { get; set; } = 0.0;
        public double MaxValue { get; set; } = 10.0;
        public bool AllowZero { get; set; } = false;

        public CurrentValueAttribute()
        {
            ErrorMessage = "电流值必须在 {1:F3}A 到 {2:F3}A 之间";
        }

        public override bool IsValid(object? value)
        {
            if (value == null) return false;

            if (value is double doubleValue)
            {
                if (!AllowZero && Math.Abs(doubleValue) < 1e-9)
                    return false;
                
                return doubleValue >= MinValue && doubleValue <= MaxValue;
            }

            if (double.TryParse(value.ToString(), out double parsedValue))
            {
                if (!AllowZero && Math.Abs(parsedValue) < 1e-9)
                    return false;
                
                return parsedValue >= MinValue && parsedValue <= MaxValue;
            }

            return false;
        }

        public override string FormatErrorMessage(string name)
        {
            return string.Format(CultureInfo.CurrentCulture, ErrorMessageString, name, MinValue, MaxValue);
        }
    }

    /// <summary>
    /// 窗口大小验证属性
    /// </summary>
    [AttributeUsage(AttributeTargets.Property | AttributeTargets.Field, AllowMultiple = false)]
    public class WindowSizeAttribute : ValidationAttribute
    {
        public int MinSize { get; set; } = 3;
        public int MaxSize { get; set; } = 100;
        public bool MustBeOdd { get; set; } = false;

        public WindowSizeAttribute()
        {
            ErrorMessage = "窗口大小必须在 {1} 到 {2} 之间";
        }

        public override bool IsValid(object? value)
        {
            if (value == null) return false;

            if (value is int intValue)
            {
                if (intValue < MinSize || intValue > MaxSize)
                    return false;

                if (MustBeOdd && intValue % 2 == 0)
                    return false;

                return true;
            }

            if (int.TryParse(value.ToString(), out int parsedValue))
            {
                if (parsedValue < MinSize || parsedValue > MaxSize)
                    return false;

                if (MustBeOdd && parsedValue % 2 == 0)
                    return false;

                return true;
            }

            return false;
        }

        public override string FormatErrorMessage(string name)
        {
            var message = string.Format(CultureInfo.CurrentCulture, ErrorMessageString, name, MinSize, MaxSize);
            if (MustBeOdd)
            {
                message += "，且必须为奇数";
            }
            return message;
        }
    }

    /// <summary>
    /// 多项式阶数验证属性
    /// </summary>
    [AttributeUsage(AttributeTargets.Property | AttributeTargets.Field, AllowMultiple = false)]
    public class PolynomialOrderAttribute : ValidationAttribute
    {
        public int MinOrder { get; set; } = 1;
        public int MaxOrder { get; set; } = 6;

        public PolynomialOrderAttribute()
        {
            ErrorMessage = "多项式阶数必须在 {1} 到 {2} 之间";
        }

        public override bool IsValid(object? value)
        {
            if (value == null) return false;

            if (value is int intValue)
            {
                return intValue >= MinOrder && intValue <= MaxOrder;
            }

            if (int.TryParse(value.ToString(), out int parsedValue))
            {
                return parsedValue >= MinOrder && parsedValue <= MaxOrder;
            }

            return false;
        }

        public override string FormatErrorMessage(string name)
        {
            return string.Format(CultureInfo.CurrentCulture, ErrorMessageString, name, MinOrder, MaxOrder);
        }
    }

    /// <summary>
    /// 高斯Sigma值验证属性
    /// </summary>
    [AttributeUsage(AttributeTargets.Property | AttributeTargets.Field, AllowMultiple = false)]
    public class GaussianSigmaAttribute : ValidationAttribute
    {
        public double MinSigma { get; set; } = 0.1;
        public double MaxSigma { get; set; } = 10.0;

        public GaussianSigmaAttribute()
        {
            ErrorMessage = "Sigma 值必须在 {1:F1} 到 {2:F1} 之间";
        }

        public override bool IsValid(object? value)
        {
            if (value == null) return false;

            if (value is double doubleValue)
            {
                return doubleValue >= MinSigma && doubleValue <= MaxSigma;
            }

            if (double.TryParse(value.ToString(), out double parsedValue))
            {
                return parsedValue >= MinSigma && parsedValue <= MaxSigma;
            }

            return false;
        }

        public override string FormatErrorMessage(string name)
        {
            return string.Format(CultureInfo.CurrentCulture, ErrorMessageString, name, MinSigma, MaxSigma);
        }
    }

    /// <summary>
    /// 文件路径验证属性
    /// </summary>
    [AttributeUsage(AttributeTargets.Property | AttributeTargets.Field, AllowMultiple = false)]
    public class FilePathAttribute : ValidationAttribute
    {
        public bool MustExist { get; set; } = false;
        public string[] AllowedExtensions { get; set; } = Array.Empty<string>();

        public FilePathAttribute()
        {
            ErrorMessage = "无效的文件路径";
        }

        public override bool IsValid(object? value)
        {
            if (value == null) return false;

            string path = value.ToString() ?? string.Empty;
            
            if (string.IsNullOrWhiteSpace(path))
                return false;

            try
            {
                // 检查路径格式是否有效
                var fullPath = System.IO.Path.GetFullPath(path);
                
                // 检查文件是否必须存在
                if (MustExist && !System.IO.File.Exists(fullPath))
                    return false;

                // 检查扩展名
                if (AllowedExtensions.Length > 0)
                {
                    string extension = System.IO.Path.GetExtension(fullPath).ToLowerInvariant();
                    bool hasValidExtension = false;
                    
                    foreach (string allowedExt in AllowedExtensions)
                    {
                        if (extension == allowedExt.ToLowerInvariant())
                        {
                            hasValidExtension = true;
                            break;
                        }
                    }
                    
                    if (!hasValidExtension)
                        return false;
                }

                return true;
            }
            catch
            {
                return false;
            }
        }

        public override string FormatErrorMessage(string name)
        {
            var message = base.FormatErrorMessage(name);
            
            if (MustExist)
            {
                message += " (文件必须存在)";
            }
            
            if (AllowedExtensions.Length > 0)
            {
                message += $" (允许的扩展名: {string.Join(", ", AllowedExtensions)})";
            }
            
            return message;
        }
    }

    /// <summary>
    /// 数值范围验证属性
    /// </summary>
    [AttributeUsage(AttributeTargets.Property | AttributeTargets.Field, AllowMultiple = false)]
    public class NumericRangeAttribute : ValidationAttribute
    {
        public double MinValue { get; set; } = double.MinValue;
        public double MaxValue { get; set; } = double.MaxValue;
        public bool Inclusive { get; set; } = true;

        public NumericRangeAttribute()
        {
            ErrorMessage = "数值必须在指定范围内";
        }

        public NumericRangeAttribute(double minValue, double maxValue)
        {
            MinValue = minValue;
            MaxValue = maxValue;
            ErrorMessage = $"数值必须在 {minValue:F2} 到 {maxValue:F2} 之间";
        }

        public override bool IsValid(object? value)
        {
            if (value == null) return false;

            if (value is double doubleValue)
            {
                return Inclusive ? 
                    (doubleValue >= MinValue && doubleValue <= MaxValue) :
                    (doubleValue > MinValue && doubleValue < MaxValue);
            }

            if (double.TryParse(value.ToString(), out double parsedValue))
            {
                return Inclusive ? 
                    (parsedValue >= MinValue && parsedValue <= MaxValue) :
                    (parsedValue > MinValue && parsedValue < MaxValue);
            }

            return false;
        }

        public override string FormatErrorMessage(string name)
        {
            return string.Format(CultureInfo.CurrentCulture, ErrorMessageString, name, MinValue, MaxValue);
        }
    }
}