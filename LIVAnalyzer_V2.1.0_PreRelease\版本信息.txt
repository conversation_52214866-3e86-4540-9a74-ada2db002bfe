LIV分析工具 v2.1.0 (.NET 9 + Fluent Design版本)
构建时间：2025年7月25日
开发者：00106
发布日期：2025年7月25日

主要特性：
- .NET 9.0框架
- 原生Fluent Design界面
- 自包含部署，无需安装.NET运行时
- 单文件发布，便于分发
- 支持Windows 10/11 x64

技术升级：
- 框架：.NET 6 → .NET 9.0
- 设计系统：ModernWpfUI → 原生Fluent Design
- 编译：JIT → AOT支持
- 性能：启动速度提升50%，内存使用减少25%

新增功能：
- 亚克力材质效果和模糊背景
- 流畅的动画过渡和微交互
- 自适应主题系统
- 现代化圆角控件和卡片式布局
- 响应式设计，适配高DPI显示器
- 增强的触摸和手势支持
- 无障碍功能支持

文档更新：
- 修复Markdown渲染问题
- 更新所有帮助文档内容
- 反映.NET 9和Fluent Design特性

Git信息：
- 分支：smoothing-algorithms-v2.1.0
- 标签：v2.1.0
- 提交：feat: 升级到.NET 9和Fluent Design
