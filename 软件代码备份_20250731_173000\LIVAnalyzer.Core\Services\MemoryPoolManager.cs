using System;
using System.Buffers;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;

namespace LIVAnalyzer.Core.Services
{
    /// <summary>
    /// 内存池管理器 - 减少GC压力，提高性能
    /// </summary>
    public static class MemoryPoolManager
    {
        // 数组池
        private static readonly ArrayPool<double> _doubleArrayPool = ArrayPool<double>.Shared;
        private static readonly ArrayPool<byte> _byteArrayPool = ArrayPool<byte>.Shared;
        
        // 对象池
        private static readonly ConcurrentBag<List<double>> _doubleListPool = new();
        private static readonly ConcurrentBag<List<LIVAnalyzer.Models.DataPoint>> _dataPointListPool = new();
        
        /// <summary>
        /// 租借double数组
        /// </summary>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static double[] RentDoubleArray(int minimumLength)
        {
            return _doubleArrayPool.Rent(minimumLength);
        }
        
        /// <summary>
        /// 归还double数组
        /// </summary>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static void ReturnDoubleArray(double[]? array, bool clearArray = false)
        {
            if (array != null)
            {
                _doubleArrayPool.Return(array, clearArray);
            }
        }
        
        /// <summary>
        /// 租借byte数组
        /// </summary>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static byte[] RentByteArray(int minimumLength)
        {
            return _byteArrayPool.Rent(minimumLength);
        }
        
        /// <summary>
        /// 归还byte数组
        /// </summary>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static void ReturnByteArray(byte[]? array, bool clearArray = false)
        {
            if (array != null)
            {
                _byteArrayPool.Return(array, clearArray);
            }
        }
        
        /// <summary>
        /// 租借Double列表
        /// </summary>
        public static List<double> RentDoubleList()
        {
            if (_doubleListPool.TryTake(out var list))
            {
                list.Clear();
                return list;
            }
            return new List<double>();
        }
        
        /// <summary>
        /// 归还Double列表
        /// </summary>
        public static void ReturnDoubleList(List<double>? list)
        {
            if (list != null && list.Capacity < 10000) // 避免保留过大的列表
            {
                list.Clear();
                _doubleListPool.Add(list);
            }
        }
        
        /// <summary>
        /// 租借DataPoint列表
        /// </summary>
        public static List<LIVAnalyzer.Models.DataPoint> RentDataPointList()
        {
            if (_dataPointListPool.TryTake(out var list))
            {
                list.Clear();
                return list;
            }
            return new List<LIVAnalyzer.Models.DataPoint>();
        }
        
        /// <summary>
        /// 归还DataPoint列表
        /// </summary>
        public static void ReturnDataPointList(List<LIVAnalyzer.Models.DataPoint>? list)
        {
            if (list != null && list.Capacity < 10000) // 避免保留过大的列表
            {
                list.Clear();
                _dataPointListPool.Add(list);
            }
        }
        
        /// <summary>
        /// 使用临时数组执行操作
        /// </summary>
        public static TResult UseTemporaryArray<TResult>(int size, Func<double[], TResult> operation)
        {
            var array = RentDoubleArray(size);
            try
            {
                return operation(array);
            }
            finally
            {
                ReturnDoubleArray(array, true);
            }
        }
        
        /// <summary>
        /// 数组复制优化
        /// </summary>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static void FastCopy(double[] source, double[] destination, int count)
        {
            if (count <= 0) return;
            
            // 对于小数组使用循环，对于大数组使用Buffer.BlockCopy
            if (count < 32)
            {
                for (int i = 0; i < count; i++)
                {
                    destination[i] = source[i];
                }
            }
            else
            {
                Buffer.BlockCopy(source, 0, destination, 0, count * sizeof(double));
            }
        }
        
        /// <summary>
        /// 优化的数组填充
        /// </summary>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static void FastFill(double[] array, double value, int start, int count)
        {
            if (count <= 0) return;
            
            // 使用向量化填充（如果可用）
            var end = start + count;
            
            // 对于小数组直接循环
            if (count < 16)
            {
                for (int i = start; i < end; i++)
                {
                    array[i] = value;
                }
                return;
            }
            
            // 对于大数组，先填充一部分，然后使用Buffer.BlockCopy
            int initialFillCount = Math.Min(16, count);
            for (int i = start; i < start + initialFillCount; i++)
            {
                array[i] = value;
            }
            
            // 使用倍增复制
            int filled = initialFillCount;
            while (filled < count)
            {
                int toCopy = Math.Min(filled, count - filled);
                Buffer.BlockCopy(array, start * sizeof(double), 
                               array, (start + filled) * sizeof(double), 
                               toCopy * sizeof(double));
                filled += toCopy;
            }
        }
        
        /// <summary>
        /// 清理内存池（在适当时机调用）
        /// </summary>
        public static void Cleanup()
        {
            // 清空对象池
            while (_doubleListPool.TryTake(out _)) { }
            while (_dataPointListPool.TryTake(out _)) { }
            
            // 强制垃圾回收（仅在必要时）
            GC.Collect(2, GCCollectionMode.Optimized);
        }
    }
    
    /// <summary>
    /// 可释放的数组包装器
    /// </summary>
    public struct RentedArray<T> : IDisposable
    {
        private T[]? _array;
        private readonly int _length;
        private readonly ArrayPool<T> _pool;
        
        public RentedArray(int length, ArrayPool<T>? pool = null)
        {
            _pool = pool ?? ArrayPool<T>.Shared;
            _array = _pool.Rent(length);
            _length = length;
        }
        
        public T[] Array => _array ?? throw new ObjectDisposedException(nameof(RentedArray<T>));
        public int Length => _length;
        
        public Span<T> AsSpan() => new Span<T>(Array, 0, _length);
        
        public void Dispose()
        {
            var array = _array;
            if (array != null)
            {
                _array = null;
                _pool.Return(array, clearArray: true);
            }
        }
    }
}