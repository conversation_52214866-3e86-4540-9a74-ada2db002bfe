using System.Windows;
using LIVAnalyzer.UI.ViewModels;

namespace LIVAnalyzer.UI.Views
{
    /// <summary>
    /// DocumentViewerDialog.xaml 的交互逻辑
    /// </summary>
    public partial class DocumentViewerDialog : Window
    {
        public DocumentViewerDialog()
        {
            InitializeComponent();
        }

        public DocumentViewerDialog(string title, string content, string windowTitle = "文档查看器") : this()
        {
            DataContext = new DocumentViewerDialogViewModel(title, content, windowTitle);
        }

        public DocumentViewerDialog(Window owner, string title, string content, string windowTitle = "文档查看器") : this(title, content, windowTitle)
        {
            Owner = owner;
        }
    }
}