===========================================
    LIV曲线分析工具 V2.0.1 (C# 版本)
===========================================

【软件说明】
LIV分析工具是一款专业的激光器测试数据分析软件，用于分析激光二极管的光-电流-电压（LIV）特性曲线。

【系统要求】
- 操作系统：Windows 10/11 (64位)
- 运行环境：已包含在程序中（自包含应用）
- 内存：建议4GB以上
- 硬盘空间：至少500MB

【文件说明】
- LIVAnalyzer.exe：主程序（约190MB）
- Accord.dll.config：配置文件
- 启动LIV分析工具.bat：便捷启动脚本
- 使用指南.md：详细使用说明

【快速开始】
1. 双击"启动LIV分析工具.bat"或直接运行"LIVAnalyzer.exe"
2. 点击"选择数据文件"加载Excel或CSV数据
3. 在文件列表中勾选要分析的文件
4. 查看分析结果和图表
5. 使用"导出数据"保存分析结果

【主要功能】
- LIV曲线分析（阈值电流、斜率效率、串联电阻）
- 光谱分析（峰值波长、FWHM）
- 效率曲线分析
- 发散角分析（HFF/VFF）
- 批量处理
- 高级图表设置（支持实时预览）
- 数据导出到Excel

【数据格式要求】
Excel文件需包含以下工作表：
- wavelength：波长数据（列A：波长nm，列B：强度）
- power：功率数据（列A：电流A，列B：功率W）
- voltage：电压数据（列A：电流A，列B：电压V）
- HFF（可选）：水平发散角数据
- VFF（可选）：垂直发散角数据

【注意事项】
- 首次运行可能需要Windows Defender扫描
- 如提示缺少.NET运行时，程序会自动提示下载
- 配置文件保存在：%AppData%\LIVAnalyzer\

【版本信息】
版本：2.0.1
发布日期：2025-01-21
开发者：00106

【技术支持】
如遇问题，请参考使用指南或联系技术支持。

===========================================