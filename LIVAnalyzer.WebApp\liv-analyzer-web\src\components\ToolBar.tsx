import { Button } from "./ui/button";
import { <PERSON><PERSON><PERSON>, <PERSON>ota<PERSON><PERSON>c<PERSON>, <PERSON><PERSON>, Save, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>olderOpen } from "lucide-react";
import { useEffect } from "react";
import { useAppStore } from "../state/store";

export default function ToolBar() {
  const { theme, setTheme, updateDisplayConfig } = useAppStore();

  useEffect(() => {
    const onToggleTheme = () => setTheme(theme === 'light' ? 'dark' : 'light');
    const onToggleGrid = () => updateDisplayConfig({ showGrid: !(useAppStore.getState().displayConfig.showGrid ?? true) });
    const onToggleLegend = () => updateDisplayConfig({ showLegend: !(useAppStore.getState().displayConfig.showLegend ?? true) });
    const onSaveConfig = () => {
      // 触发 store 的持久化已在 updateDisplayConfig/saveProcessingConfig 内处理
      // 这里只是提示
      try { console.info('配置已保存'); } catch {}
    };
    window.addEventListener('toggle-theme', onToggleTheme as EventListener);
    window.addEventListener('toggle-grid', onToggleGrid as EventListener);
    window.addEventListener('toggle-legend', onToggleLegend as EventListener);
    window.addEventListener('save-config', onSaveConfig as EventListener);
    return () => {
      window.removeEventListener('toggle-theme', onToggleTheme as EventListener);
      window.removeEventListener('toggle-grid', onToggleGrid as EventListener);
      window.removeEventListener('toggle-legend', onToggleLegend as EventListener);
      window.removeEventListener('save-config', onSaveConfig as EventListener);
    };
  }, [theme, setTheme, updateDisplayConfig]);

  return (
    <div className="w-full h-11 border-b bg-background/60 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="mx-auto max-w-full px-4 h-full flex items-center gap-2">
        <Button variant="ghost" size="icon" title="打开" onClick={() => document.getElementById("__open_files_btn")?.click()}>
          <FolderOpen className="w-5 h-5" />
        </Button>
        <Button variant="ghost" size="icon" title="分析" onClick={() => window.dispatchEvent(new CustomEvent("analyze-now"))}>
          <BarChart className="w-5 h-5" />
        </Button>
        <Button variant="ghost" size="icon" title="设置" onClick={() => window.dispatchEvent(new CustomEvent("open-settings"))}>
          <Settings className="w-5 h-5" />
        </Button>
        <Button variant="ghost" size="icon" title="刷新" onClick={() => window.location.reload()}>
          <RotateCcw className="w-5 h-5" />
        </Button>
        <Button variant="ghost" size="icon" title="复制" onClick={() => document.execCommand("copy") }>
          <Copy className="w-5 h-5" />
        </Button>
        <Button variant="ghost" size="icon" title="保存配置" onClick={() => window.dispatchEvent(new CustomEvent("save-config"))}>
          <Save className="w-5 h-5" />
        </Button>
        <Button variant="ghost" size="icon" title="主题" onClick={() => window.dispatchEvent(new CustomEvent("toggle-theme"))}>
          <Palette className="w-5 h-5" />
        </Button>
        <Button variant="ghost" size="icon" title="帮助" onClick={() => window.open("/docs", "_self") }>
          <HelpCircle className="w-5 h-5" />
        </Button>
        <div className="ml-auto text-xs text-muted-foreground">LIV Analyzer Web</div>
      </div>
    </div>
  );
}


