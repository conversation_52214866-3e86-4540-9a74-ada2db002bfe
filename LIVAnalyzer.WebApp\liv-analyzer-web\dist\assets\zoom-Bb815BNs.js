const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/plot-q4UycjGG.js","assets/react-DXNcgoS8.js"])))=>i.map(i=>d[i]);
import{_ as c}from"./index-B2ObuoTB.js";import"./react-DXNcgoS8.js";import"./csv-rDawIJ35.js";import"./xlsx-hHwYTrUW.js";async function f(i){const o=i.querySelector("svg");if(!o)return{dispose:()=>{},reset:()=>{}};const t=await c(()=>import("./plot-q4UycjGG.js").then(r=>r.i),__vite__mapDeps([0,1])),e=t.select(o),s=o.querySelector("g");if(!s)return{dispose:()=>{},reset:()=>{}};const n=t.zoom().scaleExtent([1,20]).on("zoom",r=>{s.setAttribute("transform",r.transform.toString())});return e.call(n),{dispose:()=>{e.on(".zoom",null)},reset:()=>{e.transition().duration(0).call(n.transform,t.zoomIdentity)}}}export{f as enableZoomPan};
