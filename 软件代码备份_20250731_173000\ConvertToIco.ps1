# 将PNG图标转换为ICO格式的PowerShell脚本

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    PNG到ICO转换工具" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

$iconDir = "LIVAnalyzer.UI\Resources\Icons"
$pngFile = "$iconDir\app-icon-256.png"
$icoFile = "$iconDir\app-icon.ico"

# 检查PNG文件是否存在
if (-not (Test-Path $pngFile)) {
    Write-Host "错误：找不到PNG文件：$pngFile" -ForegroundColor Red
    exit 1
}

Write-Host "正在转换图标..." -ForegroundColor Green
Write-Host "源文件：$pngFile" -ForegroundColor Gray
Write-Host "目标文件：$icoFile" -ForegroundColor Gray
Write-Host ""

try {
    # 使用.NET方法创建ICO文件
    Add-Type -AssemblyName System.Drawing
    
    # 加载PNG图像
    $png = [System.Drawing.Image]::FromFile((Resolve-Path $pngFile).Path)
    
    # 创建不同尺寸的图标
    $sizes = @(16, 32, 48, 64, 128, 256)
    $icons = @()
    
    foreach ($size in $sizes) {
        Write-Host "创建 ${size}x${size} 图标..." -ForegroundColor Yellow
        
        $bitmap = New-Object System.Drawing.Bitmap($size, $size)
        $graphics = [System.Drawing.Graphics]::FromImage($bitmap)
        $graphics.InterpolationMode = [System.Drawing.Drawing2D.InterpolationMode]::HighQualityBicubic
        $graphics.SmoothingMode = [System.Drawing.Drawing2D.SmoothingMode]::AntiAlias
        
        $graphics.DrawImage($png, 0, 0, $size, $size)
        $graphics.Dispose()
        
        $icons += $bitmap
    }
    
    # 保存为ICO文件
    Write-Host "保存ICO文件..." -ForegroundColor Yellow
    
    # 创建ICO文件流
    $stream = [System.IO.File]::Create($icoFile)
    $writer = New-Object System.IO.BinaryWriter($stream)
    
    # ICO文件头
    $writer.Write([uint16]0)      # Reserved
    $writer.Write([uint16]1)      # Type (1 = ICO)
    $writer.Write([uint16]$icons.Count) # Number of images
    
    $offset = 6 + (16 * $icons.Count)
    
    # 写入目录条目
    for ($i = 0; $i -lt $icons.Count; $i++) {
        $icon = $icons[$i]
        $size = $icon.Width
        
        $writer.Write([byte]($size -eq 256 ? 0 : $size))  # Width
        $writer.Write([byte]($size -eq 256 ? 0 : $size))  # Height
        $writer.Write([byte]0)        # Color count
        $writer.Write([byte]0)        # Reserved
        $writer.Write([uint16]1)      # Planes
        $writer.Write([uint16]32)     # Bits per pixel
        
        # 计算图像数据大小
        $ms = New-Object System.IO.MemoryStream
        $icon.Save($ms, [System.Drawing.Imaging.ImageFormat]::Png)
        $imageData = $ms.ToArray()
        $ms.Dispose()
        
        $writer.Write([uint32]$imageData.Length) # Size
        $writer.Write([uint32]$offset)           # Offset
        
        $offset += $imageData.Length
    }
    
    # 写入图像数据
    for ($i = 0; $i -lt $icons.Count; $i++) {
        $icon = $icons[$i]
        
        $ms = New-Object System.IO.MemoryStream
        $icon.Save($ms, [System.Drawing.Imaging.ImageFormat]::Png)
        $imageData = $ms.ToArray()
        $writer.Write($imageData)
        $ms.Dispose()
        
        $icon.Dispose()
    }
    
    $writer.Close()
    $stream.Close()
    $png.Dispose()
    
    Write-Host "✓ ICO文件创建成功！" -ForegroundColor Green
    
    # 显示文件信息
    $iconFileInfo = Get-Item $icoFile
    Write-Host ""
    Write-Host "文件信息：" -ForegroundColor Cyan
    Write-Host "  路径：$($iconFileInfo.FullName)" -ForegroundColor Gray
    Write-Host "  大小：$([math]::Round($iconFileInfo.Length / 1KB, 2)) KB" -ForegroundColor Gray
    Write-Host "  包含尺寸：$($sizes -join 'x, ')x 像素" -ForegroundColor Gray
    
} catch {
    Write-Host "错误：$($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "备用方案：" -ForegroundColor Yellow
    Write-Host "1. 访问 https://convertio.co/png-ico/" -ForegroundColor Yellow
    Write-Host "2. 上传 $pngFile" -ForegroundColor Yellow
    Write-Host "3. 下载转换后的ICO文件并重命名为 app-icon.ico" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "下一步：集成到WPF应用程序" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

pause
