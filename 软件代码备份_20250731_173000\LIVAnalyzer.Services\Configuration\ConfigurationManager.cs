using LIVAnalyzer.Models;
using YamlDotNet.Serialization;
using YamlDotNet.Serialization.NamingConventions;

namespace LIVAnalyzer.Services.Configuration
{
    /// <summary>
    /// 配置管理器
    /// </summary>
    public class ConfigurationManager
    {
        private static ConfigurationManager? _instance;
        private static readonly object _lock = new object();
        
        private ApplicationConfig _config;
        private readonly string _configFilePath;
        
        public static ConfigurationManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        _instance ??= new ConfigurationManager();
                    }
                }
                return _instance;
            }
        }
        
        private ConfigurationManager()
        {
            var appDataPath = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                "LIVAnalyzer");
            Directory.CreateDirectory(appDataPath);
            
            _configFilePath = Path.Combine(appDataPath, "config.yaml");
            _config = LoadConfiguration();
        }
        
        /// <summary>
        /// 获取应用程序配置
        /// </summary>
        public ApplicationConfig GetConfig() => _config;
        
        /// <summary>
        /// 保存配置
        /// </summary>
        public void SaveConfiguration()
        {
            try
            {
                var serializer = new SerializerBuilder()
                    .WithNamingConvention(PascalCaseNamingConvention.Instance)
                    .Build();
                    
                var yaml = serializer.Serialize(_config);
                File.WriteAllText(_configFilePath, yaml);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"保存配置失败: {ex.Message}", ex);
            }
        }
        
        /// <summary>
        /// 更新最后使用的文件路径
        /// </summary>
        public void SetLastFilePath(string filePath)
        {
            _config.UI.LastFilePath = filePath;
            SaveConfiguration();
        }
        
        /// <summary>
        /// 更新最后使用的文件夹路径
        /// </summary>
        public void SetLastFolderPath(string folderPath)
        {
            _config.UI.LastFolderPath = folderPath;
            SaveConfiguration();
        }
        
        /// <summary>
        /// 获取最后使用的文件路径
        /// </summary>
        public string GetLastFilePath() => _config.UI.LastFilePath;
        
        /// <summary>
        /// 获取最后使用的文件夹路径
        /// </summary>
        public string GetLastFolderPath() => _config.UI.LastFolderPath;
        
        /// <summary>
        /// 获取平滑窗口大小
        /// </summary>
        public int GetSmoothingWindowSize() => _config.DataProcessing.Smoothing.DefaultWindowSize;
        
        /// <summary>
        /// 获取默认I1电流值
        /// </summary>
        public double GetDefaultI1() => _config.DataProcessing.BatchProcessing.DefaultI1;
        
        /// <summary>
        /// 获取默认I2电流值
        /// </summary>
        public double GetDefaultI2() => _config.DataProcessing.BatchProcessing.DefaultI2;
        
        /// <summary>
        /// 获取最大文件大小限制（MB）
        /// </summary>
        public long GetMaxFileSizeMB() => _config.Performance.MaxFileSizeMB;
        
        /// <summary>
        /// 获取最大文件数量限制
        /// </summary>
        public int GetMaxFiles() => _config.Performance.MaxFiles;
        
        private ApplicationConfig LoadConfiguration()
        {
            try
            {
                if (File.Exists(_configFilePath))
                {
                    var yaml = File.ReadAllText(_configFilePath);
                    var deserializer = new DeserializerBuilder()
                        .WithNamingConvention(PascalCaseNamingConvention.Instance)
                        .Build();
                    
                    return deserializer.Deserialize<ApplicationConfig>(yaml);
                }
            }
            catch (Exception ex)
            {
                // 如果加载失败，记录错误并使用默认配置
                Console.WriteLine($"加载配置文件失败，使用默认配置: {ex.Message}");
            }
            
            // 返回默认配置
            var defaultConfig = CreateDefaultConfiguration();
            
            // 保存默认配置
            try
            {
                var serializer = new SerializerBuilder()
                    .WithNamingConvention(PascalCaseNamingConvention.Instance)
                    .Build();
                    
                var yaml = serializer.Serialize(defaultConfig);
                File.WriteAllText(_configFilePath, yaml);
            }
            catch
            {
                // 忽略保存错误
            }
            
            return defaultConfig;
        }
        
        private ApplicationConfig CreateDefaultConfiguration()
        {
            return new ApplicationConfig
            {
                DataProcessing = new DataProcessingConfig
                {
                    Smoothing = new SmoothingConfig
                    {
                        EnableByDefault = false,
                        DefaultWindowSize = 5,
                        MinWindowSize = 3,
                        MaxWindowSize = 51
                    },
                    Threshold = new ThresholdConfig
                    {
                        DefaultThreshold = 0.01,
                        MinThreshold = 0.001,
                        MaxThreshold = 1.0
                    },

                    BatchProcessing = new BatchProcessingConfig
                    {
                        DefaultI1 = 0.5,
                        DefaultI2 = 1.0
                    }
                },
                Display = new DisplayConfig
                {
                    ShowGrid = true,
                    ShowLegend = false,
                    ShowCoordinates = true,
                    DefaultLineStyle = "line"
                },
                Performance = new PerformanceConfig
                {
                    MaxFiles = 1000,
                    MaxFileSizeMB = 500
                },
                UI = new UIConfig
                {
                    LastFilePath = string.Empty,
                    LastFolderPath = string.Empty,
                    Window = new WindowConfig
                    {
                        Width = 1280,
                        Height = 720,
                        MinWidth = 1024,
                        MinHeight = 680
                    }
                }
            };
        }
    }
}