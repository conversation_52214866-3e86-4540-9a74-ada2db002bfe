@echo off
echo ====================================
echo      LIV Analyzer C# Version
echo      Installation and Run Guide
echo ====================================
echo.

REM Check if .NET SDK is installed
echo [1/4] Checking .NET SDK...
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo X .NET SDK not detected
    echo.
    echo Please install .NET 6 SDK following these steps:
    echo    1. Visit: https://dotnet.microsoft.com/download/dotnet/6.0
    echo    2. Download and install ".NET 6.0 SDK" (not Runtime)
    echo    3. Restart this script after installation
    echo.
    pause
    exit /b 1
) else (
    echo OK .NET SDK is installed
    dotnet --version
)

echo.
echo [2/4] Entering project directory...
cd /d "%~dp0"
if not exist "LIVAnalyzer.sln" (
    echo X Solution file not found
    pause
    exit /b 1
)
echo OK Project files found

echo.
echo [3/4] Restoring NuGet packages...
dotnet restore
if %errorlevel% neq 0 (
    echo X Package restore failed
    pause
    exit /b 1
)
echo OK Package restore successful

echo.
echo [4/4] Building project...
dotnet build --configuration Release
if %errorlevel% neq 0 (
    echo X Build failed
    pause
    exit /b 1
)
echo OK Build successful

echo.
echo Project is ready!
echo.
echo Available actions:
echo   R - Run application
echo   T - Run unit tests  
echo   P - Publish executable
echo   Q - Exit
echo.

:menu
set /p choice="Please choose an action (R/T/P/Q): "
if /i "%choice%"=="R" goto run
if /i "%choice%"=="T" goto test
if /i "%choice%"=="P" goto publish
if /i "%choice%"=="Q" goto quit
echo Invalid choice, please try again
goto menu

:run
echo.
echo Starting LIV Analyzer...
dotnet run --project LIVAnalyzer.UI --configuration Release
goto menu

:test
echo.
echo Running unit tests...
dotnet test --configuration Release --verbosity normal
echo.
pause
goto menu

:publish
echo.
echo Publishing executable...
if not exist "Release" mkdir Release
dotnet publish LIVAnalyzer.UI --configuration Release --runtime win-x64 --self-contained true --output Release\LIVAnalyzer -p:PublishSingleFile=true -p:IncludeNativeLibrariesForSelfExtract=true
if %errorlevel% equ 0 (
    echo OK Published successfully! Executable at: Release\LIVAnalyzer\LIVAnalyzer.exe
) else (
    echo X Publish failed
)
echo.
pause
goto menu

:quit
echo Goodbye!
pause
exit /b 0