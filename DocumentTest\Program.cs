﻿using LIVAnalyzer.Services.Documents;

Console.WriteLine("=== 测试文档更新 ===");
Console.WriteLine();

// 测试使用指南
Console.WriteLine("1. 使用指南标题检查:");
var userGuide = DocumentService.GetUserGuide();
if (userGuide.Contains(".NET 9 版本"))
{
    Console.WriteLine("✓ 使用指南已更新为.NET 9版本");
}
else
{
    Console.WriteLine("✗ 使用指南未正确更新");
}

if (userGuide.Contains("原生Fluent Design"))
{
    Console.WriteLine("✓ 使用指南包含Fluent Design信息");
}
else
{
    Console.WriteLine("✗ 使用指南缺少Fluent Design信息");
}

Console.WriteLine();

// 测试技术文档
Console.WriteLine("2. 技术文档检查:");
var techDoc = DocumentService.GetTechnicalDocumentation();
if (techDoc.Contains(".NET 9 Fluent Design版本"))
{
    Console.WriteLine("✓ 技术文档已更新为.NET 9 Fluent Design版本");
}
else
{
    Console.WriteLine("✗ 技术文档未正确更新");
}

if (techDoc.Contains("开发者：00106") && techDoc.Contains("2025年7月25日"))
{
    Console.WriteLine("✓ 技术文档包含正确的开发者和日期信息");
}
else
{
    Console.WriteLine("✗ 技术文档缺少开发者或日期信息");
}

Console.WriteLine();

// 测试发布说明
Console.WriteLine("3. 发布说明检查:");
var releaseNotes = DocumentService.GetReleaseNotes();
if (releaseNotes.Contains("版本 3.0.0 (2025-07-25)"))
{
    Console.WriteLine("✓ 发布说明包含新版本信息");
}
else
{
    Console.WriteLine("✗ 发布说明版本信息不正确");
}

if (releaseNotes.Contains("开发者: 00106"))
{
    Console.WriteLine("✓ 发布说明包含开发者信息");
}
else
{
    Console.WriteLine("✗ 发布说明缺少开发者信息");
}

Console.WriteLine();

// 测试关于信息
Console.WriteLine("4. 关于信息检查:");
var aboutInfo = DocumentService.GetAboutInfo();
if (aboutInfo.Contains("版本**: 3.0.0 (.NET 9 Fluent Design版本)"))
{
    Console.WriteLine("✓ 关于信息版本正确");
}
else
{
    Console.WriteLine("✗ 关于信息版本不正确");
}

if (aboutInfo.Contains("发布日期**: 2025年7月25日") && aboutInfo.Contains("开发者**: 00106"))
{
    Console.WriteLine("✓ 关于信息包含正确的日期和开发者");
}
else
{
    Console.WriteLine("✗ 关于信息日期或开发者不正确");
}

Console.WriteLine();
Console.WriteLine("=== 测试完成 ===");
