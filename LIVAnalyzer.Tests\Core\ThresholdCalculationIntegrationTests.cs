using System.Collections.Generic;
using System.Linq;
using Xunit;
using LIVAnalyzer.Core.Configuration;
using LIVAnalyzer.Core.Processors;
using LIVAnalyzer.Models;

namespace LIVAnalyzer.Tests.Core
{
    /// <summary>
    /// 阈值计算的集成测试
    /// 验证不同配置对阈值计算结果的影响
    /// </summary>
    public class ThresholdCalculationIntegrationTests
    {
        /// <summary>
        /// 创建模拟的LIV数据 - 典型的激光二极管特性
        /// </summary>
        private List<DataPoint> CreateTypicalLIVData()
        {
            var data = new List<DataPoint>();
            
            // 阈值前：功率增长缓慢（自发辐射）
            for (double current = 0; current <= 20; current += 1)
            {
                double power = current * 0.001 + (current * current * 0.00001); // 微弱的二次增长
                data.Add(new DataPoint(current, power));
            }
            
            // 阈值附近：功率开始快速增长（阈值约在25mA）
            for (double current = 21; current <= 30; current += 1)
            {
                double power = 0.02 + (current - 20) * 0.05; // 快速线性增长
                data.Add(new DataPoint(current, power));
            }
            
            // 阈值后：稳定的线性增长（受激辐射）
            for (double current = 31; current <= 100; current += 1)
            {
                double power = 0.52 + (current - 30) * 0.08; // 稳定线性增长
                data.Add(new DataPoint(current, power));
            }
            
            return data;
        }
        
        /// <summary>
        /// 创建高阈值激光器数据
        /// </summary>
        private List<DataPoint> CreateHighThresholdLIVData()
        {
            var data = new List<DataPoint>();
            
            // 阈值前：长时间的缓慢增长
            for (double current = 0; current <= 60; current += 2)
            {
                double power = current * 0.002;
                data.Add(new DataPoint(current, power));
            }
            
            // 阈值附近：功率开始快速增长（阈值约在70mA）
            for (double current = 62; current <= 80; current += 2)
            {
                double power = 0.12 + (current - 60) * 0.1;
                data.Add(new DataPoint(current, power));
            }
            
            // 阈值后：线性增长
            for (double current = 82; current <= 150; current += 2)
            {
                double power = 2.12 + (current - 80) * 0.15;
                data.Add(new DataPoint(current, power));
            }
            
            return data;
        }
        
        /// <summary>
        /// 创建带噪声的LIV数据
        /// </summary>
        private List<DataPoint> CreateNoisyLIVData()
        {
            var baseData = CreateTypicalLIVData();
            var noisyData = new List<DataPoint>();
            
            var random = new System.Random(42); // 固定种子确保测试可重复
            
            foreach (var point in baseData)
            {
                // 添加±10%的随机噪声
                double noise = (random.NextDouble() - 0.5) * 0.2 * point.Y;
                noisyData.Add(new DataPoint(point.X, point.Y + noise));
            }
            
            return noisyData;
        }
        
        [Fact]
        public void CalculateThresholdCurrent_WithDefaultConfig_ShouldFindReasonableThreshold()
        {
            // Arrange
            var processor = new TestableProcessor();
            var data = CreateTypicalLIVData();

            // Act
            var threshold = processor.TestCalculateThresholdCurrent(data, ThresholdCalculationConfig.Default);

            // Assert
            Assert.True(threshold > 20);
            Assert.True(threshold < 35);
        }
        
        [Fact]
        public void CalculateThresholdCurrent_WithVCSELConfig_ShouldFindLowerThreshold()
        {
            // Arrange
            var processor = new TestableProcessor();
            var data = CreateTypicalLIVData();
            var vcselConfig = ThresholdCalculationPresets.VCSEL;
            
            // Act
            var thresholdWithVCSEL = processor.TestCalculateThresholdCurrent(data, vcselConfig);
            var thresholdWithDefault = processor.TestCalculateThresholdCurrent(data, ThresholdCalculationConfig.Default);
            
            // Assert
            Assert.True(thresholdWithVCSEL > 0);
            Assert.True(thresholdWithDefault > 0);
            // VCSEL配置可能会找到稍微不同的阈值，因为它使用更敏感的参数
        }
        
        [Fact]
        public void CalculateThresholdCurrent_WithHighPowerConfig_ShouldHandleHighThresholdData()
        {
            // Arrange
            var processor = new TestableProcessor();
            var data = CreateHighThresholdLIVData();
            var highPowerConfig = ThresholdCalculationPresets.HighPower;
            var defaultConfig = ThresholdCalculationConfig.Default;
            
            // Act
            var thresholdWithHighPower = processor.TestCalculateThresholdCurrent(data, highPowerConfig);
            var thresholdWithDefault = processor.TestCalculateThresholdCurrent(data, defaultConfig);
            
            // Assert
            Assert.True(thresholdWithHighPower > 0);
            // 高功率配置应该能更好地处理高阈值数据
            Assert.True(thresholdWithHighPower > 50); // 应该找到合理的高阈值
        }
        
        [Fact]
        public void CalculateThresholdCurrent_WithHighNoiseConfig_ShouldHandleNoisyData()
        {
            // Arrange
            var processor = new TestableProcessor();
            var noisyData = CreateNoisyLIVData();
            var highNoiseConfig = ThresholdCalculationPresets.HighNoise;
            var lowNoiseConfig = ThresholdCalculationPresets.LowNoise;
            
            // Act
            var thresholdWithHighNoise = processor.TestCalculateThresholdCurrent(noisyData, highNoiseConfig);
            var thresholdWithLowNoise = processor.TestCalculateThresholdCurrent(noisyData, lowNoiseConfig);
            
            // Assert
            Assert.True(thresholdWithHighNoise > 0);
            Assert.True(thresholdWithLowNoise > 0);
            // 高噪声配置应该提供更稳定的结果
        }
        
        [Fact]
        public void AllProcessors_ShouldGiveSameResults_AfterUnification()
        {
            // Arrange
            var baseProcessor = new TestableProcessor();
            var optimizedProcessor = new TestableOptimizedProcessor();
            var ultraOptimizedProcessor = new TestableUltraOptimizedProcessor();
            var data = CreateTypicalLIVData();
            var config = ThresholdCalculationConfig.Default;

            // Act
            var baseThreshold = baseProcessor.TestCalculateThresholdCurrent(data, config);
            var optimizedThreshold = optimizedProcessor.TestCalculateThresholdCurrent(data, config);
            var ultraOptimizedThreshold = ultraOptimizedProcessor.TestCalculateThresholdCurrent(data, config);

            // Assert - 所有处理器现在应该给出相同的阈值电流结果
            Assert.Equal(baseThreshold, optimizedThreshold, 3);
            Assert.Equal(baseThreshold, ultraOptimizedThreshold, 3);
        }
        
        /// <summary>
        /// 可测试的处理器，暴露受保护的方法用于测试
        /// </summary>
        private class TestableProcessor : LIVDataProcessor
        {
            public double TestCalculateThresholdCurrent(List<DataPoint> data, ThresholdCalculationConfig config)
            {
                return CalculateThresholdCurrent(data, config);
            }
        }

        /// <summary>
        /// 可测试的优化处理器
        /// </summary>
        private class TestableOptimizedProcessor : OptimizedLIVDataProcessor
        {
            public double TestCalculateThresholdCurrent(List<DataPoint> data, ThresholdCalculationConfig config)
            {
                return CalculateThresholdCurrent(data, config);
            }
        }

        /// <summary>
        /// 可测试的超级优化处理器
        /// </summary>
        private class TestableUltraOptimizedProcessor : UltraOptimizedLIVDataProcessor
        {
            public double TestCalculateThresholdCurrent(List<DataPoint> data, ThresholdCalculationConfig config)
            {
                return CalculateThresholdCurrent(data, config);
            }
        }
    }
}
