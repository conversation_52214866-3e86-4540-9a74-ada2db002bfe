const KEY_PROCESSING = 'liv_processing_config_v1';
const KEY_DISPLAY = 'liv_display_config_v1';

export function saveProcessingConfig(cfg: any) {
  try { localStorage.setItem(KEY_PROCESSING, JSON.stringify(cfg)); } catch {}
}

export function loadProcessingConfig(): any | null {
  try { const s = localStorage.getItem(KEY_PROCESSING); return s ? JSON.parse(s) : null; } catch { return null; }
}

export function saveDisplayConfig(cfg: any) {
  try { localStorage.setItem(KEY_DISPLAY, JSON.stringify(cfg)); } catch {}
}

export function loadDisplayConfig(): any | null {
  try { const s = localStorage.getItem(KEY_DISPLAY); return s ? JSON.parse(s) : null; } catch { return null; }
}



