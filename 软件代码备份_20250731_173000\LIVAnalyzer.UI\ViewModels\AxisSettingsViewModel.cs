using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using OxyPlot.Axes;
using System;

namespace LIVAnalyzer.UI.ViewModels
{
    /// <summary>
    /// 单个坐标轴设置的视图模型
    /// </summary>
    public partial class AxisSettingsViewModel : ObservableObject
    {
        private readonly Axis _axis;
        private readonly string _chartName;
        private readonly double _originalMinimum;
        private readonly double _originalMaximum;
        private readonly bool _originalIsZoomEnabled;
        private readonly bool _originalIsPanEnabled;

        [ObservableProperty]
        private string axisTitle = "";

        [ObservableProperty]
        private string chartName = "";

        [ObservableProperty]
        private double axisMinimum = 0;

        [ObservableProperty]
        private double axisMaximum = 100;

        [ObservableProperty]
        private string axisType = "";

        public AxisSettingsViewModel(Axis axis, string chartName)
        {
            _axis = axis ?? throw new ArgumentNullException(nameof(axis));
            _chartName = chartName;

            // 保存原始值以便取消时恢复
            _originalMinimum = axis.Minimum;
            _originalMaximum = axis.Maximum;
            _originalIsZoomEnabled = axis.IsZoomEnabled;
            _originalIsPanEnabled = axis.IsPanEnabled;

            // 初始化属性
            AxisTitle = axis.Title ?? GetDefaultAxisTitle(axis);
            ChartName = chartName;
            AxisType = GetAxisTypeDescription(axis);

            // 使用当前的实际范围作为初始值
            AxisMinimum = double.IsNaN(axis.Minimum) ? axis.ActualMinimum : axis.Minimum;
            AxisMaximum = double.IsNaN(axis.Maximum) ? axis.ActualMaximum : axis.Maximum;
        }

        private static string GetDefaultAxisTitle(Axis axis)
        {
            return axis.Position switch
            {
                AxisPosition.Bottom => "X轴",
                AxisPosition.Top => "顶部X轴",
                AxisPosition.Left => "Y轴",
                AxisPosition.Right when string.IsNullOrEmpty(axis.Key) => "右侧Y轴",
                AxisPosition.Right => "副Y轴",
                _ => "坐标轴"
            };
        }

        private static string GetAxisTypeDescription(Axis axis)
        {
            var position = axis.Position switch
            {
                AxisPosition.Bottom => "底部",
                AxisPosition.Top => "顶部",
                AxisPosition.Left => "左侧",
                AxisPosition.Right => "右侧",
                _ => "未知"
            };

            var type = axis.Position switch
            {
                AxisPosition.Bottom or AxisPosition.Top => "X轴",
                AxisPosition.Left or AxisPosition.Right => "Y轴",
                _ => "轴"
            };

            return $"{position}{type}";
        }

        [RelayCommand]
        private void ResetToAutoRange()
        {
            // 将轴恢复为自动范围
            _axis.Minimum = double.NaN;
            _axis.Maximum = double.NaN;
            _axis.IsZoomEnabled = true;
            _axis.IsPanEnabled = true;
            _axis.PlotModel?.InvalidatePlot(true);
            
            // 更新显示值
            AxisMinimum = _axis.ActualMinimum;
            AxisMaximum = _axis.ActualMaximum;
        }

        [RelayCommand]
        private void UseCurrentRange()
        {
            // 使用当前实际显示的范围
            AxisMinimum = _axis.ActualMinimum;
            AxisMaximum = _axis.ActualMaximum;
        }

        [RelayCommand]
        private void Apply()
        {
            // 验证范围有效性
            if (AxisMaximum <= AxisMinimum)
            {
                System.Windows.MessageBox.Show("最大值必须大于最小值", "无效范围",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Warning);
                return;
            }

            // 应用自定义范围
            _axis.Minimum = AxisMinimum;
            _axis.Maximum = AxisMaximum;
            _axis.IsZoomEnabled = false;
            _axis.IsPanEnabled = false;

            // 刷新图表
            _axis.PlotModel?.InvalidatePlot(true);

            OnRequestClose?.Invoke(true);
        }

        [RelayCommand]
        private void Cancel()
        {
            // 恢复原始设置
            _axis.Minimum = _originalMinimum;
            _axis.Maximum = _originalMaximum;
            _axis.IsZoomEnabled = _originalIsZoomEnabled;
            _axis.IsPanEnabled = _originalIsPanEnabled;
            
            _axis.PlotModel?.InvalidatePlot(true);
            
            OnRequestClose?.Invoke(false);
        }

        public Action<bool>? OnRequestClose { get; set; }
    }
}