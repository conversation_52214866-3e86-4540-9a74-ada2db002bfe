@echo off
chcp 65001 >nul
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                  项目文件完整性检查                           ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

set error_count=0

REM 检查解决方案文件
echo 🔍 检查解决方案文件...
if exist "LIVAnalyzer.sln" (
    echo ✅ LIVAnalyzer.sln
) else (
    echo ❌ LIVAnalyzer.sln 缺失
    set /a error_count+=1
)

REM 检查各个项目
echo.
echo 🔍 检查项目文件...

REM Models项目
if exist "LIVAnalyzer.Models\LIVAnalyzer.Models.csproj" (
    echo ✅ LIVAnalyzer.Models
) else (
    echo ❌ LIVAnalyzer.Models 缺失
    set /a error_count+=1
)

REM Data项目
if exist "LIVAnalyzer.Data\LIVAnalyzer.Data.csproj" (
    echo ✅ LIVAnalyzer.Data
) else (
    echo ❌ LIVAnalyzer.Data 缺失
    set /a error_count+=1
)

REM Core项目
if exist "LIVAnalyzer.Core\LIVAnalyzer.Core.csproj" (
    echo ✅ LIVAnalyzer.Core
) else (
    echo ❌ LIVAnalyzer.Core 缺失
    set /a error_count+=1
)

REM Services项目
if exist "LIVAnalyzer.Services\LIVAnalyzer.Services.csproj" (
    echo ✅ LIVAnalyzer.Services
) else (
    echo ❌ LIVAnalyzer.Services 缺失
    set /a error_count+=1
)

REM UI项目
if exist "LIVAnalyzer.UI\LIVAnalyzer.UI.csproj" (
    echo ✅ LIVAnalyzer.UI
) else (
    echo ❌ LIVAnalyzer.UI 缺失
    set /a error_count+=1
)

REM Tests项目
if exist "LIVAnalyzer.Tests\LIVAnalyzer.Tests.csproj" (
    echo ✅ LIVAnalyzer.Tests
) else (
    echo ❌ LIVAnalyzer.Tests 缺失
    set /a error_count+=1
)

REM 检查核心源文件
echo.
echo 🔍 检查核心源文件...

set core_files=LIVAnalyzer.Models\LIVParameters.cs LIVAnalyzer.Core\Processors\LIVDataProcessor.cs LIVAnalyzer.UI\Views\MainWindow.xaml LIVAnalyzer.UI\ViewModels\MainWindowViewModel.cs

for %%f in (%core_files%) do (
    if exist "%%f" (
        echo ✅ %%f
    ) else (
        echo ❌ %%f 缺失
        set /a error_count+=1
    )
)

echo.
echo ╔══════════════════════════════════════════════════════════════╗
if %error_count% equ 0 (
    echo ║                    ✅ 项目文件完整                          ║
    echo ║                                                              ║
    echo ║ 🎉 所有文件检查通过，项目可以正常编译运行！                  ║
    echo ║                                                              ║
    echo ║ 📋 下一步操作:                                               ║
    echo ║    1. 确保安装.NET 6 SDK                                     ║
    echo ║    2. 运行"环境检查.bat"检查环境                              ║
    echo ║    3. 运行"运行向导.bat"编译并启动程序                        ║
) else (
    echo ║                    ❌ 发现问题                              ║
    echo ║                                                              ║
    echo ║ 发现 %error_count% 个文件缺失或错误！                                      ║
    echo ║ 请检查项目创建过程是否完整                                    ║
)
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM 显示项目统计信息
echo 📊 项目统计信息:
for /f %%i in ('dir /s *.cs ^| find "个文件"') do echo    C# 源文件: %%i
for /f %%i in ('dir /s *.xaml ^| find "个文件"') do echo    XAML 文件: %%i  
for /f %%i in ('dir /s *.csproj ^| find "个文件"') do echo    项目文件: %%i

echo.
pause