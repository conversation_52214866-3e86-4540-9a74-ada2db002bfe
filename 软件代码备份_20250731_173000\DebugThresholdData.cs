using System;
using System.IO;
using System.Linq;
using LIVAnalyzer.Data.Loaders;
using LIVAnalyzer.Core.Processors;
using LIVAnalyzer.Services.Logging;

namespace LIVAnalyzer.Debug
{
    class DebugThresholdData
    {
        static void Main(string[] args)
        {
            string filePath = @"E:\01LaserPackage\software\LIV_Analyzer\CSharp_Version\YL+COS01+25664_converted.xlsx";
            
            if (!File.Exists(filePath))
            {
                Console.WriteLine($"文件不存在: {filePath}");
                return;
            }
            
            try
            {
                // 加载数据
                var loader = new ExcelDataLoader();
                var data = loader.LoadData(filePath);
                
                Console.WriteLine("=== 数据基本信息 ===");
                Console.WriteLine($"文件名: {data.FileName}");
                Console.WriteLine($"电流-功率数据点数: {data.CurrentPowerData.Count}");
                Console.WriteLine($"电流-电压数据点数: {data.CurrentVoltageData.Count}");
                Console.WriteLine($"波长-强度数据点数: {data.WavelengthIntensityData.Count}");
                
                if (data.CurrentPowerData.Any())
                {
                    Console.WriteLine("\n=== 电流-功率数据 (前20个点) ===");
                    Console.WriteLine("电流(A)\t功率(W)");
                    for (int i = 0; i < Math.Min(20, data.CurrentPowerData.Count); i++)
                    {
                        var point = data.CurrentPowerData[i];
                        Console.WriteLine($"{point.X:F6}\t{point.Y:F6}");
                    }
                    
                    Console.WriteLine("\n=== 电流-功率数据 (最后10个点) ===");
                    Console.WriteLine("电流(A)\t功率(W)");
                    for (int i = Math.Max(0, data.CurrentPowerData.Count - 10); i < data.CurrentPowerData.Count; i++)
                    {
                        var point = data.CurrentPowerData[i];
                        Console.WriteLine($"{point.X:F6}\t{point.Y:F6}");
                    }
                    
                    // 计算阈值电流
                    Console.WriteLine("\n=== 阈值电流计算 ===");
                    
                    // 使用基础处理器
                    var basicProcessor = new LIVDataProcessor();
                    var basicResult = basicProcessor.CalculateParameters(data);
                    Console.WriteLine($"基础处理器阈值电流: {basicResult.ThresholdCurrent:F6} A");
                    
                    // 使用优化处理器
                    var optimizedProcessor = new OptimizedLIVDataProcessor();
                    optimizedProcessor.ClearCache(); // 确保清除缓存
                    var optimizedResult = optimizedProcessor.CalculateParameters(data);
                    Console.WriteLine($"优化处理器阈值电流: {optimizedResult.ThresholdCurrent:F6} A");
                    
                    // 分析数据特征
                    Console.WriteLine("\n=== 数据分析 ===");
                    var sortedData = data.CurrentPowerData.OrderBy(p => p.X).ToList();
                    
                    Console.WriteLine($"最小电流: {sortedData.First().X:F6} A");
                    Console.WriteLine($"最大电流: {sortedData.Last().X:F6} A");
                    Console.WriteLine($"最小功率: {sortedData.Min(p => p.Y):F6} W");
                    Console.WriteLine($"最大功率: {sortedData.Max(p => p.Y):F6} W");
                    
                    // 查找功率开始显著增长的点
                    var maxPower = sortedData.Max(p => p.Y);
                    var threshold1Percent = maxPower * 0.01;
                    var threshold5Percent = maxPower * 0.05;
                    
                    var firstSignificantPoint = sortedData.FirstOrDefault(p => p.Y > threshold1Percent);
                    var first5PercentPoint = sortedData.FirstOrDefault(p => p.Y > threshold5Percent);
                    
                    if (firstSignificantPoint != null)
                        Console.WriteLine($"功率达到最大值1%的电流: {firstSignificantPoint.X:F6} A (功率: {firstSignificantPoint.Y:F6} W)");
                    if (first5PercentPoint != null)
                        Console.WriteLine($"功率达到最大值5%的电流: {first5PercentPoint.X:F6} A (功率: {first5PercentPoint.Y:F6} W)");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"错误: {ex.Message}");
                Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
            }
            
            Console.WriteLine("\n按任意键退出...");
            Console.ReadKey();
        }
    }
}