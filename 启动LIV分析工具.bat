@echo off
chcp 65001 >nul
echo ====================================
echo   LIV 分析工具 V2.0 (C#版本)
echo   已修复所有已知问题 - 2025.7.19
echo ====================================
echo.

echo 检查 .NET 6 运行时...
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未安装 .NET 6 SDK/运行时!
    echo 请从 https://dotnet.microsoft.com/download 下载并安装 .NET 6
    pause
    exit /b 1
)

echo .NET 运行时检查通过
echo.

echo 停止可能运行的进程...
taskkill /F /IM LIVAnalyzer.exe >nul 2>&1

echo 清理之前的构建...
dotnet clean >nul 2>&1

echo.
echo 恢复NuGet包...
dotnet restore --force >nul 2>&1

echo.
echo 构建项目...
dotnet build --configuration Release

if %errorlevel% neq 0 (
    echo.
    echo 构建失败! 请检查上面的错误信息。
    pause
    exit /b 1
)

echo.
echo ============================================
echo   构建成功! 启动LIV分析工具...
echo.
echo   功能说明:
echo   - 支持Excel (.xlsx, .xls) 和 CSV 文件
echo   - 自动计算LIV参数 (阈值电流、斜率效率等)
echo   - 光谱分析 (峰值波长、FWHM)
echo   - 发散角分析支持
echo   - 实时数据可视化
echo   - 批量处理功能
echo.
echo   测试文件: test_data.csv (位于当前目录)
echo ============================================
echo.

dotnet run --project LIVAnalyzer.UI --configuration Release

if %errorlevel% neq 0 (
    echo.
    echo 应用程序启动失败，请检查错误信息。
    pause
)

echo.
echo 应用程序已退出。
pause