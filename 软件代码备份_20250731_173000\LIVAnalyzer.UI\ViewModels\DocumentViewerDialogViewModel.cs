using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using LIVAnalyzer.Services.Logging;
using Microsoft.Win32;
using System;
using System.IO;
using System.Linq;
using System.Windows;

namespace LIVAnalyzer.UI.ViewModels
{
    public partial class DocumentViewerDialogViewModel : ObservableObject
    {
        public DocumentViewerDialogViewModel(string documentTitle, string documentContent, string windowTitle = "文档查看器")
        {
            DocumentTitle = documentTitle;
            DocumentContent = documentContent;
            WindowTitle = windowTitle;
            StatusText = $"字符数: {documentContent.Length:N0}";
            
            InitializeCommands();
        }

        #region Properties

        [ObservableProperty]
        private string windowTitle = "文档查看器";

        [ObservableProperty]
        private string documentTitle = "";

        [ObservableProperty]
        private string documentContent = "";

        [ObservableProperty]
        private string statusText = "";

        [ObservableProperty]
        private bool showFallbackText = false;

        #endregion

        #region Commands

        public IRelayCommand CopyToClipboardCommand { get; private set; }
        public IRelayCommand SaveToFileCommand { get; private set; }
        public IRelayCommand CloseCommand { get; private set; }

        #endregion

        #region Initialization

        private void InitializeCommands()
        {
            CopyToClipboardCommand = new RelayCommand(CopyToClipboard);
            SaveToFileCommand = new RelayCommand(SaveToFile);
            CloseCommand = new RelayCommand(CloseDialog);
        }

        #endregion

        #region Command Implementations

        private void CopyToClipboard()
        {
            try
            {
                Clipboard.SetText(DocumentContent);
                StatusText = "内容已复制到剪贴板";
                LoggingService.LogUserAction("复制文档内容到剪贴板", DocumentTitle);
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "复制到剪贴板失败");
                MessageBox.Show($"复制失败：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SaveToFile()
        {
            try
            {
                var saveFileDialog = new SaveFileDialog
                {
                    Title = "保存文档",
                    Filter = "文本文件 (*.txt)|*.txt|Markdown文件 (*.md)|*.md|所有文件 (*.*)|*.*",
                    FileName = $"{DocumentTitle}_{DateTime.Now:yyyyMMdd}.txt",
                    InitialDirectory = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments)
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    File.WriteAllText(saveFileDialog.FileName, DocumentContent, System.Text.Encoding.UTF8);
                    StatusText = $"已保存到: {Path.GetFileName(saveFileDialog.FileName)}";
                    LoggingService.LogUserAction("保存文档到文件", saveFileDialog.FileName);
                    MessageBox.Show("文档保存成功！", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "保存文档失败");
                MessageBox.Show($"保存失败：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CloseDialog()
        {
            Application.Current.Windows.OfType<Window>().FirstOrDefault(w => w.DataContext == this)?.Close();
        }

        #endregion
    }
}