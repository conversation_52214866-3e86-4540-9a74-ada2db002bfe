using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Xunit;
using LIVAnalyzer.Core.Processors;
using LIVAnalyzer.Models;

namespace LIVAnalyzer.Tests.Core
{
    public class DivergenceCalculationTest
    {
        [Fact]
        public void TestDivergenceCalculations()
        {
            // Create a Gaussian beam profile
            var angles = new List<double>();
            var intensities = new List<double>();
            
            double theta0 = 10.0; // 1/e² half-angle in degrees
            double I0 = 1.0; // Peak intensity
            
            // Generate data from -5*theta0 to +5*theta0
            for (double theta = -5 * theta0; theta <= 5 * theta0; theta += 0.1)
            {
                angles.Add(theta);
                double intensity = I0 * Math.Exp(-2 * Math.Pow(theta / theta0, 2));
                intensities.Add(intensity);
            }
            
            // Create divergence data
            var angleData = new List<DataPoint>();
            var intensityData = new List<DataPoint>();
            for (int i = 0; i < angles.Count; i++)
            {
                angleData.Add(new DataPoint(angles[i], intensities[i]));
                intensityData.Add(new DataPoint(angles[i], intensities[i]));
            }
            
            // Process with DivergenceProcessor
            var processor = new DivergenceProcessor();
            var result = processor.CalculateDivergence(angleData, intensityData);
            
            // Write detailed results to file for analysis
            using (var writer = new StreamWriter("divergence_test_results.txt"))
            {
                writer.WriteLine("=== Gaussian Beam Divergence Analysis ===");
                writer.WriteLine($"Generated beam with 1/e² half-angle = {theta0}°");
                writer.WriteLine($"Expected 1/e² full width = {2 * theta0}°");
                writer.WriteLine();
                
                writer.WriteLine("=== Calculation Results ===");
                writer.WriteLine($"FWHM: {result.FWHM:F2}°");
                writer.WriteLine($"FW86.5% (1/e² width): {result.FW1e2:F2}°");
                writer.WriteLine($"FW95%: {result.FW95:F2}°");
                writer.WriteLine();
                
                writer.WriteLine("=== Energy Containment ===");
                writer.WriteLine($"FW86.5% threshold: {result.FW1e2Threshold:F4} (should be 0.1353)");
                writer.WriteLine($"FW86.5% energy containment: {result.FW1e2PowerContainment:P2}");
                writer.WriteLine($"FW95% threshold: {result.FW95Threshold:F4}");
                writer.WriteLine();
                
                writer.WriteLine("=== Validation ===");
                writer.WriteLine($"FW86.5% vs expected 2*theta0: {result.FW1e2:F2}° vs {2*theta0:F2}° (diff: {Math.Abs(result.FW1e2 - 2*theta0):F2}°)");
                writer.WriteLine($"Energy containment vs expected 86.5%: {result.FW1e2PowerContainment:P2} vs 86.5% (diff: {Math.Abs(result.FW1e2PowerContainment - 0.865)*100:F1}%)");
                
                // For Gaussian beam, theoretical values:
                double theoreticalFWHM = 2 * theta0 * Math.Sqrt(Math.Log(2) / 2); // ≈ 1.177 * theta0
                writer.WriteLine($"FWHM vs theoretical: {result.FWHM:F2}° vs {theoreticalFWHM:F2}° (diff: {Math.Abs(result.FWHM - theoreticalFWHM):F2}°)");
                
                writer.WriteLine();
                writer.WriteLine("=== Crossings ===");
                writer.WriteLine($"FWHM crossings: {string.Join(", ", result.FWHMCrossings.Select(c => $"{c:F2}°"))}");
                writer.WriteLine($"FW86.5% crossings: {string.Join(", ", result.FW1e2Crossings.Select(c => $"{c:F2}°"))}");
                writer.WriteLine($"FW95% crossings: {string.Join(", ", result.FW95Crossings.Select(c => $"{c:F2}°"))}");
            }
            
            // Assertions
            Assert.NotNull(result);
            Assert.True(result.IsValid, $"Result should be valid: {result.ErrorMessage}");
            
            // FWHM test
            double expectedFWHM = 2 * theta0 * Math.Sqrt(Math.Log(2) / 2);
            Assert.True(Math.Abs(result.FWHM - expectedFWHM) < 0.5, 
                $"FWHM should be close to {expectedFWHM:F2}°, but got {result.FWHM:F2}°");
            
            // FW86.5% test
            Assert.True(Math.Abs(result.FW1e2 - 2 * theta0) < 0.5,
                $"FW86.5% should be close to {2*theta0}°, but got {result.FW1e2}°");

            // Order test - only FWHM < FW86.5% should always be true
            Assert.True(result.FWHM < result.FW1e2,
                $"FWHM ({result.FWHM:F2}°) should be less than FW86.5% ({result.FW1e2:F2}°)");
            
            // Note: FW95% may be less than FW86.5% for some distributions (like Gaussian)
            // because they have different definitions:
            // - FW86.5% is the width at 13.5% intensity
            // - FW95% is the width that contains 95% of total energy
        }
    }
}