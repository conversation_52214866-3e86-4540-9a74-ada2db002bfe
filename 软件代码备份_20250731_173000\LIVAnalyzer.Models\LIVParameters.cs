namespace LIVAnalyzer.Models
{
    /// <summary>
    /// LIV参数计算结果模型
    /// </summary>
    public class LIVParameters
    {
        /// <summary>
        /// 峰值波长 (nm)
        /// </summary>
        public double PeakWavelength { get; set; }
        
        /// <summary>
        /// 半高宽 (nm)
        /// </summary>
        public double FWHM { get; set; }
        
        /// <summary>
        /// 阈值电流 (A)
        /// </summary>
        public double ThresholdCurrent { get; set; }
        
        /// <summary>
        /// 最大功率 (W)
        /// </summary>
        public double MaxPower { get; set; }
        
        /// <summary>
        /// 最大效率 (%)
        /// </summary>
        public double MaxEfficiency { get; set; }
        
        /// <summary>
        /// 斜率效率 (W/A)
        /// </summary>
        public double SlopeEfficiency { get; set; }
        
        /// <summary>
        /// 串联电阻 (Ω)
        /// </summary>
        public double? SeriesResistance { get; set; }
        
        /// <summary>
        /// 串联电阻拟合度 R²
        /// </summary>
        public double? SeriesResistanceR2 { get; set; }
        
        /// <summary>
        /// 微分电阻 (Ω)
        /// </summary>
        public double? DifferentialResistance { get; set; }
        
        /// <summary>
        /// I1电流值 (A)
        /// </summary>
        public double I1Current { get; set; }
        
        /// <summary>
        /// I1处的功率值 (W)
        /// </summary>
        public double I1Power { get; set; }
        
        /// <summary>
        /// I1处的电压值 (V)
        /// </summary>
        public double I1Voltage { get; set; }
        
        /// <summary>
        /// I1处的效率 (%)
        /// </summary>
        public double I1Efficiency { get; set; }
        
        /// <summary>
        /// I2电流值 (A)
        /// </summary>
        public double I2Current { get; set; }
        
        /// <summary>
        /// I2处的功率值 (W)
        /// </summary>
        public double I2Power { get; set; }
        
        /// <summary>
        /// I2处的电压值 (V)
        /// </summary>
        public double I2Voltage { get; set; }
        
        /// <summary>
        /// I2处的效率 (%)
        /// </summary>
        public double I2Efficiency { get; set; }
    }
}