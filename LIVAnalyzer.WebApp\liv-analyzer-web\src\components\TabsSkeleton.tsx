import * as Tabs from "@radix-ui/react-tabs";
import LIVChart from "./charts/LIVChart";
import SpectrumChart from "./charts/SpectrumChart";
import DivergenceChart from "./charts/DivergenceChart";
import EfficiencyChart from "./charts/EfficiencyChart";
import ChartControls from "../components/ChartControls";
import SpectrumControls from "../components/SpectrumControls";
import DivergenceControls from "../components/DivergenceControls";

export default function TabsSkeleton() {
  return (
    <Tabs.Root defaultValue="LIV" className="w-full">
      <Tabs.List className="flex gap-2 border-b">
        {['LIV','光谱','发散','效率'].map((t) => (
          <Tabs.Trigger key={t} value={t} className="px-3 py-2 text-sm data-[state=active]:border-b-2 data-[state=active]:border-primary">
            {t}
          </Tabs.Trigger>
        ))}
      </Tabs.List>
      <Tabs.Content value="LIV" className="p-4 space-y-3">
        <ChartControls />
        <div className="text-xs text-muted-foreground">图例：P-I=蓝色、V-I=红色、诊断线=红线、拟合点=绿色、异常点=橙色</div>
        <LIVChart />
      </Tabs.Content>
      <Tabs.Content value="光谱" className="p-4 space-y-3">
        <SpectrumControls />
        <SpectrumChart />
      </Tabs.Content>
      <Tabs.Content value="发散" className="p-4 space-y-3">
        <DivergenceControls />
        <DivergenceChart />
      </Tabs.Content>
      <Tabs.Content value="效率" className="p-4"><EfficiencyChart /></Tabs.Content>
    </Tabs.Root>
  );
}


