using System;
using System.Collections.Generic;
using System.Linq;
using LIVAnalyzer.Models;
using LIVAnalyzer.Services.Logging;

namespace LIVAnalyzer.Core.Processors
{
    /// <summary>
    /// 发散角计算处理器，实现FWHM、FW(1/e²)、FW95%计算
    /// FWHM: 半高全宽（50%强度）
    /// FW(1/e²): 1/e²强度处的宽度（13.5%强度）
    /// FW95%: 包含95%总功率的宽度
    /// </summary>
    public class DivergenceProcessor
    {
        // Python版本的常量定义
        private const double INTENSITY_THRESHOLD_1_E2 = 0.1353; // 1/e² intensity threshold (13.53%)
        private const double POWER_CONTAINMENT_95 = 0.95;    // 95% power definition
        private const double FWHM_THRESHOLD = 0.5;           // 50% for FWHM (FWHM_HALF_MAX)
        private const double DEFAULT_TOLERANCE = 1e-4;      // Python版本的默认容差
        private const double INTERPOLATION_TOLERANCE = 1e-10; // 线性插值精度容差
        private const int MIN_DATA_POINTS = 10;              // 最小数据点要求

        /// <summary>
        /// 计算发散角参数（FWHM、1/e²宽度、FW95%）
        /// </summary>
        /// <param name="angleData">角度数据</param>
        /// <param name="intensityData">强度数据</param>
        /// <param name="smoothingEnabled">是否启用平滑</param>
        /// <param name="smoothingWindowSize">平滑窗口大小</param>
        /// <returns>发散角计算结果</returns>
        public DivergenceResult CalculateDivergence(
            List<DataPoint> angleData,
            List<DataPoint> intensityData,
            bool smoothingEnabled = false,
            int smoothingWindowSize = 5)
        {
            try
            {
                LoggingService.LogInformation("开始计算发散角参数");

                if (!ValidateInputData(angleData, intensityData))
                {
                    return new DivergenceResult { IsValid = false, ErrorMessage = "输入数据无效" };
                }

                // 准备计算数据
                var processedData = PrepareCalculationData(angleData, intensityData, smoothingEnabled, smoothingWindowSize);
                if (processedData == null)
                {
                    return new DivergenceResult { IsValid = false, ErrorMessage = "数据预处理失败" };
                }

                var result = new DivergenceResult
                {
                    IsValid = true,
                    OriginalData = angleData.Zip(intensityData, (a, i) => new DataPoint(a.X, i.Y)).ToList(),
                    ProcessedData = processedData
                };

                // ✅ 关键修正: 确保数据处理的一致性
                var angles = processedData.Select(p => p.X).ToArray();
                var originalIntensity = processedData.Select(p => p.Y).ToArray();
                
                LoggingService.LogInformation($"数据统计: 角度范围=[{angles.Min():F2}°, {angles.Max():F2}°], 强度范围=[{originalIntensity.Min():F6}, {originalIntensity.Max():F6}]");

                // ✅ FWHM计算 - 使用独立的归一化和阈值方法
                result.FWHM = CalculateFWHM(angles, originalIntensity);
                LoggingService.LogInformation($"FWHM计算完成: {result.FWHM:F2}°");
                
                // 为可视化计算FWHM交点
                var normalizedForFWHM = NormalizeIntensity(originalIntensity);
                result.FWHMCrossings = FindThresholdCrossings(angles, normalizedForFWHM, FWHM_THRESHOLD);

                // ✅ FW(1/e²) 计算 - 使用13.5%强度阈值（1/e²）
                var fw1e2Result = CalculateIntensityThresholdWidth(angles, originalIntensity, INTENSITY_THRESHOLD_1_E2);
                result.FW1e2 = fw1e2Result.width;
                result.FW1e2Crossings = fw1e2Result.crossings;
                result.FW1e2Threshold = INTENSITY_THRESHOLD_1_E2; // 13.5%强度阈值
                result.FW1e2PowerContainment = fw1e2Result.powerContainment; // 实际能量占比
                LoggingService.LogInformation($"FW(1/e²)计算完成: {result.FW1e2:F2}° (1/e²宽度), 强度阈值: {INTENSITY_THRESHOLD_1_E2:P1}, 实际能量占比: {result.FW1e2PowerContainment:P1}");

                // ✅ FW95%计算 - 基于功率包含方法（这个是正确的）
                var fw95Result = CalculatePowerContainment(angles, originalIntensity, 0, POWER_CONTAINMENT_95);
                result.FW95 = fw95Result.divergenceAngle;
                result.FW95Crossings = fw95Result.crossings;
                result.FW95Threshold = fw95Result.threshold;
                LoggingService.LogInformation($"FW95%计算完成: {result.FW95:F2}°");

                // ✅ 验证结果的合理性
                if (result.FWHM > 0 && result.FW1e2 > 0 && result.FW95 > 0)
                {
                    // FWHM < FW(1/e²) 应该总是成立（因为50%强度 > 13.5%强度）
                    if (result.FW1e2 < result.FWHM)
                    {
                        LoggingService.LogWarning($"发散角结果异常: FW(1/e²)({result.FW1e2:F2}°) < FWHM({result.FWHM:F2}°)");
                        LoggingService.LogWarning($"  可能原因：数据噪声较大或非标准分布");
                    }
                    
                    // 注意：FW95% 和 FW(1/e²) 的大小关系不固定
                    // 因为它们的定义不同：
                    // - FW(1/e²) 是13.5%强度处的宽度
                    // - FW95% 是包含95%能量的宽度
                    // 对于高斯光束，可能出现 FW95% < FW(1/e²) 的情况
                    
                    // 记录详细信息
                    LoggingService.LogInformation($"发散角计算结果:");
                    LoggingService.LogInformation($"  FWHM = {result.FWHM:F2}° (50%强度处宽度)");
                    LoggingService.LogInformation($"  FW(1/e²) = {result.FW1e2:F2}° (13.5%强度处宽度)");
                    LoggingService.LogInformation($"  FW95% = {result.FW95:F2}° (包含95%能量的宽度)");
                    LoggingService.LogInformation($"  FW(1/e²)范围内的能量占比 = {result.FW1e2PowerContainment:P1}");
                    
                    // 验证能量占比的合理性
                    if (result.FW1e2PowerContainment > 1.0)
                    {
                        LoggingService.LogError($"错误：FW(1/e²)范围内的能量占比超过100%: {result.FW1e2PowerContainment:P1}");
                    }
                }

                LoggingService.LogInformation($"发散角计算完成: FWHM={result.FWHM:F2}°, FW(1/e²)={result.FW1e2:F2}°, FW95%={result.FW95:F2}°");
                return result;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "发散角计算失败");
                return new DivergenceResult { IsValid = false, ErrorMessage = ex.Message };
            }
        }

        private bool ValidateInputData(List<DataPoint> angleData, List<DataPoint> intensityData)
        {
            if (angleData == null || intensityData == null)
                return false;

            if (angleData.Count < MIN_DATA_POINTS || intensityData.Count < MIN_DATA_POINTS)
                return false;

            if (angleData.Count != intensityData.Count)
                return false;

            // 检查是否有有效的强度数据
            if (!intensityData.Any(p => p.Y > 0))
                return false;

            return true;
        }

        private List<DataPoint>? PrepareCalculationData(
            List<DataPoint> angleData,
            List<DataPoint> intensityData,
            bool smoothingEnabled,
            int smoothingWindowSize)
        {
            try
            {
                // 合并角度和强度数据
                var combinedData = angleData.Zip(intensityData, (a, i) => new DataPoint(a.X, i.Y))
                    .Where(p => !double.IsNaN(p.Y) && !double.IsInfinity(p.Y))
                    .OrderBy(p => p.X)
                    .ToList();

                // 检查并修正负值
                var negativeCount = combinedData.Count(p => p.Y < 0);
                if (negativeCount > 0)
                {
                    LoggingService.LogWarning($"检测到 {negativeCount} 个负强度值。将自动设为0。");
                    // 将所有负值设为0
                    combinedData = combinedData.Select(p => new DataPoint(p.X, Math.Max(0, p.Y))).ToList();
                    LoggingService.LogInformation($"已将 {negativeCount} 个负强度值修正为0。");
                }

                // 检查边界值
                if (combinedData.Count > 10)
                {
                    var maxIntensity = combinedData.Max(p => p.Y);
                    var firstIntensity = combinedData.First().Y;
                    var lastIntensity = combinedData.Last().Y;
                    
                    // 如果边界值超过峰值的5%，发出警告
                    if (firstIntensity > maxIntensity * 0.05 || lastIntensity > maxIntensity * 0.05)
                    {
                        LoggingService.LogWarning($"边界强度值较高: 起始={firstIntensity/maxIntensity:P1}, 结束={lastIntensity/maxIntensity:P1}");
                        LoggingService.LogWarning($"建议扩大测量角度范围以获得更准确的结果。");
                    }
                }

                if (smoothingEnabled && combinedData.Count > smoothingWindowSize)
                {
                    // 应用平滑处理
                    combinedData = ApplySmoothing(combinedData, smoothingWindowSize);
                }

                return combinedData;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "数据预处理失败");
                return null;
            }
        }

        private List<DataPoint> ApplySmoothing(List<DataPoint> data, int windowSize)
        {
            if (windowSize < 3 || windowSize > data.Count)
                return data;

            // 实现类似Python版本的平滑算法
            // 优先使用移动平均，这是最接近Python版本简单平滑的方法
            var smoothedData = new List<DataPoint>();
            int halfWindow = windowSize / 2;

            for (int i = 0; i < data.Count; i++)
            {
                int start = Math.Max(0, i - halfWindow);
                int end = Math.Min(data.Count - 1, i + halfWindow);
                
                double avgIntensity = 0;
                int count = 0;
                
                for (int j = start; j <= end; j++)
                {
                    avgIntensity += data[j].Y;
                    count++;
                }
                
                avgIntensity /= count;
                
                // 保持原始角度，只平滑强度
                smoothedData.Add(new DataPoint(data[i].X, avgIntensity));
            }

            // 计算噪声减少百分比（Python版本的特性）
            var originalNoise = CalculateNoise(data.Select(p => p.Y).ToArray());
            var smoothedNoise = CalculateNoise(smoothedData.Select(p => p.Y).ToArray());
            var noiseReduction = originalNoise > 0 ? (1 - smoothedNoise / originalNoise) * 100 : 0;
            
            LoggingService.LogInformation($"数据平滑完成: 窗口大小={windowSize}, 噪声减少={noiseReduction:F1}%");

            return smoothedData;
        }

        private double CalculateNoise(double[] data)
        {
            if (data.Length < 2) return 0;
            
            // 计算相邻点差值的标准差作为噪声指标
            var differences = new List<double>();
            for (int i = 0; i < data.Length - 1; i++)
            {
                differences.Add(Math.Abs(data[i + 1] - data[i]));
            }
            
            var mean = differences.Average();
            var variance = differences.Sum(d => Math.Pow(d - mean, 2)) / differences.Count;
            return Math.Sqrt(variance);
        }

        private double[] NormalizeIntensity(double[] intensity)
        {
            var maxIntensity = intensity.Max();
            if (maxIntensity <= 0)
                return intensity;

            return intensity.Select(i => i / maxIntensity).ToArray();
        }

        private double CalculateFWHM(double[] angles, double[] intensity)
        {
            // 使用通用的强度阈值方法计算FWHM
            var result = CalculateIntensityThresholdWidth(angles, intensity, FWHM_THRESHOLD);
            return result.width;
        }

        /// <summary>
        /// 计算指定强度阈值处的宽度（通用方法）
        /// </summary>
        /// <param name="angles">角度数组</param>
        /// <param name="intensity">强度数组</param>
        /// <param name="threshold">归一化强度阈值（0-1）</param>
        /// <returns>宽度、交点和能量占比</returns>
        private (double width, List<double> crossings, double powerContainment) CalculateIntensityThresholdWidth(double[] angles, double[] intensity, double threshold)
        {
            // 归一化强度数据
            var normalizedIntensity = NormalizeIntensity(intensity);
            var crossings = FindThresholdCrossings(angles, normalizedIntensity, threshold);
            
            if (crossings.Count >= 2)
            {
                // 取最外侧的两个交点
                double width = Math.Abs(crossings.Last() - crossings.First());
                
                // 计算两个交点之间的能量占比
                double powerContainment = 0;
                if (crossings.Count >= 2)
                {
                    // 取最外侧的两个交点作为积分范围
                    double startAngle = crossings.First();
                    double endAngle = crossings.Last();
                    powerContainment = CalculatePowerContainmentInRange(angles, normalizedIntensity, startAngle, endAngle);
                }
                
                LoggingService.LogInformation($"强度阈值 {threshold:P1} 处: 宽度={width:F2}°, 交点数={crossings.Count}, 能量占比={powerContainment:P1}");
                
                return (width, crossings, powerContainment);
            }
            
            LoggingService.LogWarning($"强度阈值 {threshold:P1} 处未找到足够的交点（找到{crossings.Count}个）");
            return (0, crossings, 0);
        }
        
        /// <summary>
        /// 计算指定角度范围内的能量占比
        /// </summary>
        private double CalculatePowerContainmentInRange(double[] angles, double[] normalizedIntensity, double startAngle, double endAngle)
        {
            // 确保startAngle < endAngle
            if (startAngle > endAngle)
            {
                var temp = startAngle;
                startAngle = endAngle;
                endAngle = temp;
            }
            
            // 调试信息
            LoggingService.LogInformation($"计算能量占比: 1/e²范围=[{startAngle:F2}°, {endAngle:F2}°], 宽度={endAngle - startAngle:F2}°");
            LoggingService.LogInformation($"角度数据范围: [{angles.First():F2}°, {angles.Last():F2}°], 数据点数={angles.Length}");
            
            // 计算总功率 - 使用原始数据而非归一化数据
            double totalPower = CalculateTotalPower(angles, normalizedIntensity);
            if (totalPower <= 0) return 0;
            
            LoggingService.LogInformation($"总功率（梯形积分）: {totalPower:F6}");
            
            // 计算范围内的功率
            double rangePower = 0;
            int segmentsProcessed = 0;
            int segmentsInRange = 0;
            
            for (int i = 0; i < angles.Length - 1; i++)
            {
                double angle1 = angles[i];
                double angle2 = angles[i + 1];
                
                // 跳过完全在范围外的段
                if (angle2 < startAngle || angle1 > endAngle)
                {
                    continue;
                }
                
                segmentsInRange++;
                
                // 计算有效的积分区间
                double effectiveStart = Math.Max(angle1, startAngle);
                double effectiveEnd = Math.Min(angle2, endAngle);
                
                if (effectiveEnd > effectiveStart)
                {
                    segmentsProcessed++;
                    
                    // 如果需要插值（边界不完全对齐）
                    double intensityAtStart, intensityAtEnd;
                    
                    if (effectiveStart == angle1)
                    {
                        intensityAtStart = normalizedIntensity[i];
                    }
                    else
                    {
                        // 插值计算effectiveStart处的强度
                        intensityAtStart = LinearInterpolate(angle1, angle2, 
                            normalizedIntensity[i], normalizedIntensity[i + 1], effectiveStart);
                    }
                    
                    if (effectiveEnd == angle2)
                    {
                        intensityAtEnd = normalizedIntensity[i + 1];
                    }
                    else
                    {
                        // 插值计算effectiveEnd处的强度
                        intensityAtEnd = LinearInterpolate(angle1, angle2, 
                            normalizedIntensity[i], normalizedIntensity[i + 1], effectiveEnd);
                    }
                    
                    // 梯形积分
                    double deltaAngle = effectiveEnd - effectiveStart;
                    double avgIntensity = (intensityAtStart + intensityAtEnd) / 2;
                    double segmentPower = avgIntensity * deltaAngle;
                    rangePower += segmentPower;
                    
                    // 记录前后几个段的详细信息
                    if (segmentsProcessed <= 3 || segmentsProcessed >= segmentsInRange - 2)
                    {
                        LoggingService.LogInformation($"  段{i}: 角度=[{effectiveStart:F3}°, {effectiveEnd:F3}°], " +
                            $"强度=[{intensityAtStart:F4}, {intensityAtEnd:F4}], 段功率={segmentPower:F6}");
                    }
                }
            }
            
            double ratio = rangePower / totalPower;
            
            LoggingService.LogInformation($"1/e²范围内功率: {rangePower:F6}, 处理段数: {segmentsProcessed}");
            LoggingService.LogInformation($"能量占比: {ratio:P2} (范围功率/总功率 = {rangePower:F6}/{totalPower:F6})");
            
            // 对于理想高斯光束的理论值：
            // - 1D高斯光束: erf(√2) ≈ 0.8427 (84.27%)
            // - 2D高斯光束: 1 - exp(-2) ≈ 0.8647 (86.47%)
            // 实际测量值可能有所不同
            if (ratio > 0.95)
            {
                LoggingService.LogWarning($"警告: 能量占比({ratio:P1})异常高，可能的原因：");
                LoggingService.LogWarning($"  1. 测量角度范围不够宽，边界处强度未降至零");
                LoggingService.LogWarning($"  2. 光束分布不是理想高斯分布");
                LoggingService.LogWarning($"  3. 数据中存在噪声或异常值");
            }
            
            // 添加合理性检查
            if (ratio > 1.0)
            {
                LoggingService.LogWarning($"错误: 能量占比计算值为{ratio:F4} ({ratio:P1})，超过100%");
                LoggingService.LogWarning($"详细信息: 范围功率={rangePower:F6}, 总功率={totalPower:F6}");
                LoggingService.LogWarning($"积分范围: [{startAngle:F3}°, {endAngle:F3}°], 测量范围: [{angles.First():F3}°, {angles.Last():F3}°]");
                
                // 检查边界处的强度
                var boundaryIntensity1 = normalizedIntensity.First();
                var boundaryIntensity2 = normalizedIntensity.Last();
                LoggingService.LogWarning($"边界归一化强度: 起始={boundaryIntensity1:F4}, 结束={boundaryIntensity2:F4}");
                
                // 限制为1.0以避免显示超过100%
                ratio = Math.Min(ratio, 1.0);
                LoggingService.LogInformation($"已将能量占比限制为100%");
            }
            
            return ratio;
        }
        
        /// <summary>
        /// 线性插值
        /// </summary>
        private double LinearInterpolate(double x1, double x2, double y1, double y2, double x)
        {
            if (Math.Abs(x2 - x1) < 1e-10) return y1;
            return y1 + (y2 - y1) * (x - x1) / (x2 - x1);
        }

        private List<double> FindThresholdCrossings(double[] angles, double[] intensity, double threshold)
        {
            var crossings = new List<double>();

            for (int i = 0; i < intensity.Length - 1; i++)
            {
                double y1 = intensity[i];
                double y2 = intensity[i + 1];
                
                // 检查是否跨越阈值线 - 使用Python版本的逻辑
                if ((y1 <= threshold && y2 >= threshold) || (y1 >= threshold && y2 <= threshold))
                {
                    // 线性插值找到精确的交点 - 使用Python版本的精度
                    if (Math.Abs(y2 - y1) > INTERPOLATION_TOLERANCE)  // Python版本的精度容差
                    {
                        double t = (threshold - y1) / (y2 - y1);
                        double crossingAngle = angles[i] + t * (angles[i + 1] - angles[i]);
                        crossings.Add(crossingAngle);
                    }
                }
            }

            return crossings;
        }

        private double CalculateTotalPower(double[] angles, double[] intensity)
        {
            // 使用梯形积分计算总功率
            double totalPower = 0;
            
            // 检查边界强度
            double firstIntensity = intensity.First();
            double lastIntensity = intensity.Last();
            double maxIntensity = intensity.Max();
            
            // 如果边界强度超过最大值的1%，发出警告
            if (firstIntensity > maxIntensity * 0.01 || lastIntensity > maxIntensity * 0.01)
            {
                LoggingService.LogWarning($"测量角度范围可能不够宽: 边界强度仍有{Math.Max(firstIntensity/maxIntensity, lastIntensity/maxIntensity):P1}的峰值强度");
            }
            
            for (int i = 0; i < angles.Length - 1; i++)
            {
                double deltaAngle = angles[i + 1] - angles[i];
                double avgIntensity = (intensity[i] + intensity[i + 1]) / 2;
                totalPower += avgIntensity * deltaAngle;
            }
            
            return totalPower;
        }
        
        /// <summary>
        /// 计算强度阈值以上的能量占比（修正版本）
        /// 能量占比 = (高于阈值部分的面积) / (总面积)
        /// 注意：只计算高于阈值的部分，不包括阈值线本身
        /// </summary>
        /// <param name="angles">角度数组</param>
        /// <param name="normalizedIntensity">归一化强度数组</param>
        /// <param name="threshold">强度阈值（0-1）</param>
        /// <returns>阈值以上的能量占比</returns>
        private double CalculatePowerAboveThreshold(double[] angles, double[] normalizedIntensity, double threshold)
        {
            LoggingService.LogInformation($"计算强度阈值 {threshold:P1} ({threshold:F4}) 以上的能量占比");
            
            double totalArea = 0;           // 总面积
            double areaAboveThreshold = 0;  // 阈值以上的面积
            
            // 先检查数据范围
            double minIntensity = normalizedIntensity.Min();
            double maxIntensity = normalizedIntensity.Max();
            LoggingService.LogInformation($"强度范围: [{minIntensity:F6}, {maxIntensity:F6}]");
            
            for (int i = 0; i < angles.Length - 1; i++)
            {
                double angle1 = angles[i];
                double angle2 = angles[i + 1];
                double intensity1 = normalizedIntensity[i];
                double intensity2 = normalizedIntensity[i + 1];
                double deltaAngle = angle2 - angle1;
                
                // 计算总面积（梯形积分）
                double avgIntensity = (intensity1 + intensity2) / 2;
                totalArea += avgIntensity * deltaAngle;
                
                // 计算阈值以上的面积
                if (intensity1 > threshold && intensity2 > threshold)
                {
                    // 两点都在阈值以上
                    // 计算超出阈值的部分形成的梯形面积
                    double h1 = intensity1 - threshold;  // 点1超出阈值的高度
                    double h2 = intensity2 - threshold;  // 点2超出阈值的高度
                    double avgHeightAbove = (h1 + h2) / 2;
                    areaAboveThreshold += avgHeightAbove * deltaAngle;
                }
                else if ((intensity1 > threshold) != (intensity2 > threshold))
                {
                    // 一个点在阈值以上，一个在阈值以下
                    // 需要找到与阈值的交点
                    double t = (threshold - intensity1) / (intensity2 - intensity1);
                    double crossingAngle = angle1 + t * deltaAngle;
                    
                    if (intensity1 > threshold)
                    {
                        // intensity1在阈值以上，intensity2在阈值以下
                        // 计算三角形面积
                        double h1 = intensity1 - threshold;
                        double width = crossingAngle - angle1;
                        areaAboveThreshold += 0.5 * h1 * width;
                    }
                    else
                    {
                        // intensity2在阈值以上，intensity1在阈值以下
                        // 计算三角形面积
                        double h2 = intensity2 - threshold;
                        double width = angle2 - crossingAngle;
                        areaAboveThreshold += 0.5 * h2 * width;
                    }
                }
                // 如果两点都在阈值或以下，不贡献面积
            }
            
            double ratio = totalArea > 0 ? areaAboveThreshold / totalArea : 0;
            
            LoggingService.LogInformation($"总面积: {totalArea:F6}");
            LoggingService.LogInformation($"阈值以上面积: {areaAboveThreshold:F6}");
            LoggingService.LogInformation($"能量占比: {ratio:P3} = {ratio:F6}");
            
            // 调试信息
            if (ratio > 0.99)
            {
                LoggingService.LogWarning($"能量占比接近100%，检查数据:");
                LoggingService.LogWarning($"- 最小归一化强度: {minIntensity:F6}");
                LoggingService.LogWarning($"- 阈值: {threshold:F6}");
                LoggingService.LogWarning($"- 如果最小强度 > 阈值，则所有数据都在阈值以上");
                
                // 统计有多少点在阈值以下
                int pointsBelowThreshold = normalizedIntensity.Count(i => i <= threshold);
                LoggingService.LogWarning($"- 阈值以下的点数: {pointsBelowThreshold}/{normalizedIntensity.Length}");
            }
            
            return ratio;
        }

        private (double divergenceAngle, List<double> crossings, double threshold) CalculatePowerContainment(
            double[] angles, double[] intensity, double totalPower, double targetPowerRatio)
        {
            try
            {
                LoggingService.LogInformation($"开始计算功率包含: 目标={targetPowerRatio:F3} (寻找两交点间能量占比为{targetPowerRatio:P0}的水平线)");
                
                // 使用原始强度数据，内部进行归一化
                var maxIntensity = intensity.Max();
                var normalizedIntensity = intensity.Select(i => i / maxIntensity).ToArray();
                
                // 计算归一化后的总功率
                var normalizedTotalPower = CalculateTotalPower(angles, normalizedIntensity);
                
                LoggingService.LogInformation($"数据统计: 最大强度={maxIntensity:F6}, 归一化总功率={normalizedTotalPower:F6}");
                
                // 设置搜索范围
                double lowThreshold, highThreshold;
                double tolerance = DEFAULT_TOLERANCE;
                int maxIterations;
                
                if (Math.Abs(targetPowerRatio - POWER_CONTAINMENT_95) < 1e-6)
                {
                    // 95%功率包含：需要找到合适的水平线高度
                    // 水平线越低，包含的能量越多
                    lowThreshold = 0.001;  // 很低的阈值（包含几乎所有能量）
                    highThreshold = 0.5;   // 较高的阈值（包含较少能量）
                    maxIterations = 100;
                    LoggingService.LogInformation($"95%功率包含搜索范围: 强度阈值=[{lowThreshold:F3}, {highThreshold:F3}]");
                }
                else
                {
                    // 其他功率包含比例
                    lowThreshold = 0.001;
                    highThreshold = 0.8;
                    maxIterations = 100;
                }

                double bestThreshold = 0;
                double bestDivergence = 0;
                List<double> bestCrossings = new List<double>();
                double bestPowerRatio = 0;
                double bestError = double.MaxValue;

                // 二分搜索找最佳阈值
                for (int iteration = 0; iteration < maxIterations; iteration++)
                {
                    double threshold = (lowThreshold + highThreshold) / 2;
                    
                    // 找到阈值线与曲线的交点
                    var crossings = FindThresholdCrossings(angles, normalizedIntensity, threshold);
                    
                    if (crossings.Count >= 2)
                    {
                        // 计算最外侧两个交点之间的能量占比
                        double startAngle = crossings.First();
                        double endAngle = crossings.Last();
                        double powerRatio = CalculatePowerContainmentInRange(angles, normalizedIntensity, startAngle, endAngle);
                        double error = Math.Abs(powerRatio - targetPowerRatio);
                        
                        // 调试信息
                        if (iteration < 5 || iteration % 10 == 0)
                        {
                            LoggingService.LogInformation($"迭代{iteration}: 阈值={threshold:F6}, 交点数={crossings.Count}, " +
                                $"范围=[{startAngle:F2}°, {endAngle:F2}°], 能量占比={powerRatio:F6}, 误差={error:F6}");
                        }
                        
                        if (error < bestError)
                        {
                            bestError = error;
                            bestThreshold = threshold;
                            bestPowerRatio = powerRatio;
                            bestCrossings = crossings;
                            bestDivergence = Math.Abs(endAngle - startAngle);
                        }
                        
                        if (error < tolerance)
                        {
                            LoggingService.LogInformation($"收敛: 阈值={threshold:F6}, 能量占比={powerRatio:F6}, 误差={error:F6}");
                            break;
                        }
                        
                        // 调整搜索范围
                        if (powerRatio > targetPowerRatio)
                        {
                            // 能量太多，需要提高阈值（减少包含范围）
                            lowThreshold = threshold;
                        }
                        else
                        {
                            // 能量太少，需要降低阈值（增加包含范围）
                            highThreshold = threshold;
                        }
                    }
                    else
                    {
                        // 没有足够的交点，调整阈值
                        if (crossings.Count == 0)
                        {
                            // 阈值太高，没有交点
                            highThreshold = threshold;
                        }
                        else
                        {
                            // 只有一个交点，降低阈值
                            highThreshold = threshold;
                        }
                    }
                    
                    // 防止无限循环
                    if (Math.Abs(highThreshold - lowThreshold) < 1e-8)
                        break;
                }

                LoggingService.LogInformation($"功率包含计算完成: 目标={targetPowerRatio:F3}, 实际={bestPowerRatio:F3}, " +
                    $"阈值={bestThreshold:F6}, 发散角={bestDivergence:F2}°");
                return (bestDivergence, bestCrossings, bestThreshold);
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"功率包含计算失败 (目标比例: {targetPowerRatio})");
                return (0, new List<double>(), 0);
            }
        }
    }

    /// <summary>
    /// 发散角计算结果
    /// </summary>
    public class DivergenceResult
    {
        public bool IsValid { get; set; }
        public string ErrorMessage { get; set; } = "";
        
        // 原始和处理后的数据
        public List<DataPoint> OriginalData { get; set; } = new();
        public List<DataPoint> ProcessedData { get; set; } = new();
        
        // FWHM结果
        public double FWHM { get; set; }
        public List<double> FWHMCrossings { get; set; } = new();
        
        // FW(1/e²)结果
        public double FW1e2 { get; set; }
        public List<double> FW1e2Crossings { get; set; } = new();
        public double FW1e2Threshold { get; set; }
        public double FW1e2PowerContainment { get; set; } // FW(1/e²)宽度内的能量占比
        
        // FW95%结果
        public double FW95 { get; set; }
        public List<double> FW95Crossings { get; set; } = new();
        public double FW95Threshold { get; set; }
    }
}