<Window x:Class="LIVAnalyzer.UI.Views.AxisSelectionDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="选择要设置的坐标轴" 
        Height="300" Width="400"
        ResizeMode="NoResize"
        WindowStyle="SingleBorderWindow"
        Background="{DynamicResource AppBackgroundBrush}">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0" 
                   Text="请选择要设置范围的坐标轴："
                   FontSize="14"
                   FontWeight="Bold"
                   Margin="0,0,0,15"
                   Foreground="{DynamicResource AppForegroundBrush}"/>

        <!-- 轴列表 -->
        <ListBox Grid.Row="1" 
                 x:Name="AxisListBox"
                 SelectionMode="Single"
                 Background="{DynamicResource AppControlBackgroundBrush}"
                 BorderBrush="{DynamicResource AppBorderBrush}"
                 Foreground="{DynamicResource AppForegroundBrush}"
                 ScrollViewer.HorizontalScrollBarVisibility="Disabled">
            <ListBox.ItemTemplate>
                <DataTemplate>
                    <Grid Margin="5">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <!-- 轴图标 -->
                        <TextBlock Grid.Column="0" 
                                   Text="{Binding Icon}"
                                   FontSize="16"
                                   Margin="0,0,10,0"
                                   VerticalAlignment="Center"/>
                        
                        <!-- 轴信息 -->
                        <StackPanel Grid.Column="1" VerticalAlignment="Center">
                            <TextBlock Text="{Binding DisplayName}" 
                                       FontWeight="Bold"
                                       FontSize="13"/>
                            <TextBlock Text="{Binding RangeInfo}" 
                                       FontSize="11"
                                       Foreground="{DynamicResource AppSecondaryForegroundBrush}"/>
                        </StackPanel>
                        
                        <!-- 位置标识 -->
                        <TextBlock Grid.Column="2"
                                   Text="{Binding PositionText}"
                                   FontSize="10"
                                   Foreground="{DynamicResource AppSecondaryForegroundBrush}"
                                   VerticalAlignment="Center"/>
                    </Grid>
                </DataTemplate>
            </ListBox.ItemTemplate>
        </ListBox>

        <!-- 按钮 -->
        <StackPanel Grid.Row="2" 
                    Orientation="Horizontal" 
                    HorizontalAlignment="Right" 
                    Margin="0,15,0,0">
            <Button Content="确定" 
                    Width="80" Height="30"
                    Margin="0,0,10,0"
                    Click="OkButton_Click"
                    IsDefault="True"
                    Background="{DynamicResource AppControlBackgroundBrush}"
                    BorderBrush="{DynamicResource AppBorderBrush}"
                    Foreground="{DynamicResource AppForegroundBrush}"/>
            <Button Content="取消" 
                    Width="80" Height="30"
                    Click="CancelButton_Click"
                    IsCancel="True"
                    Background="{DynamicResource AppControlBackgroundBrush}"
                    BorderBrush="{DynamicResource AppBorderBrush}"
                    Foreground="{DynamicResource AppForegroundBrush}"/>
        </StackPanel>
    </Grid>
</Window>
