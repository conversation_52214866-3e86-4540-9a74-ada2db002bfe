using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using OxyPlot;
using OxyPlot.Series;
using LIVAnalyzer.Models;

namespace LIVAnalyzer.UI.Services
{
    /// <summary>
    /// 优化的图表管理器 - 使用批量更新和数据抽样提升性能
    /// </summary>
    public class OptimizedPlotManager
    {
        private readonly ConcurrentDictionary<string, PlotModel> _plotCache = new();
        private readonly SemaphoreSlim _updateSemaphore = new(1, 1);
        private const int MAX_POINTS_PER_SERIES = 5000; // 最大数据点数限制
        
        /// <summary>
        /// 批量更新多个数据系列
        /// </summary>
        public async Task BatchUpdateSeriesAsync(PlotModel plotModel, List<SeriesData> seriesDataList, CancellationToken cancellationToken = default, bool clearExisting = true)
        {
            if (plotModel == null || seriesDataList == null || !seriesDataList.Any())
                return;

            await _updateSemaphore.WaitAsync(cancellationToken);
            try
            {
                if (clearExisting)
                {
                    // 清空现有系列（完全重建模式）
                    plotModel.Series.Clear();
                }

                // 并行处理数据系列
                var seriesTasks = seriesDataList.Select(async seriesData =>
                {
                    if (cancellationToken.IsCancellationRequested)
                        return null;

                    return await Task.Run(() => CreateOptimizedSeries(seriesData), cancellationToken);
                }).ToList();

                var series = await Task.WhenAll(seriesTasks);

                // 添加非空系列到图表
                foreach (var s in series.Where(s => s != null))
                {
                    plotModel.Series.Add(s!);
                }

                // 批量刷新图表（增量更新时使用轻量级刷新）
                await Task.Run(() => plotModel.InvalidatePlot(!clearExisting), cancellationToken);
            }
            finally
            {
                _updateSemaphore.Release();
            }
        }
        
        /// <summary>
        /// 创建优化的数据系列
        /// </summary>
        public LineSeries? CreateOptimizedSeries(SeriesData seriesData)
        {
            var series = new LineSeries
            {
                Title = seriesData.Title,
                Color = seriesData.Color,
                StrokeThickness = seriesData.StrokeThickness,
                LineStyle = seriesData.LineStyle,
                MarkerType = seriesData.MarkerType,
                MarkerSize = seriesData.MarkerSize,
                // 根据数据类型设置正确的工具提示格式
                TrackerFormatString = GetTrackerFormatString(seriesData.YAxisKey)
            };

            // 如果指定了Y轴键，设置它
            if (!string.IsNullOrEmpty(seriesData.YAxisKey))
            {
                series.YAxisKey = seriesData.YAxisKey;
            }

            // 支持两种数据格式
            List<LIVAnalyzer.Models.DataPoint> dataPoints;

            if (seriesData.XData != null && seriesData.YData != null &&
                seriesData.XData.Length == seriesData.YData.Length && seriesData.XData.Length > 0)
            {
                // 使用XData/YData格式
                dataPoints = seriesData.XData.Zip(seriesData.YData, (x, y) => new LIVAnalyzer.Models.DataPoint(x, y)).ToList();
            }
            else if (seriesData.Points != null && seriesData.Points.Any())
            {
                // 使用Points格式
                dataPoints = seriesData.Points;
            }
            else
            {
                return null; // 没有有效数据
            }

            // 优化数据点 - 如果点数过多，进行智能抽样
            var optimizedPoints = dataPoints.Count > MAX_POINTS_PER_SERIES ?
                DownsamplePoints(dataPoints, MAX_POINTS_PER_SERIES) :
                dataPoints;

            // 批量添加数据点
            series.Points.AddRange(optimizedPoints.Select(p => new OxyPlot.DataPoint(p.X, p.Y)));

            return series;
        }
        
        /// <summary>
        /// 智能降采样 - 保留关键特征点
        /// </summary>
        private List<LIVAnalyzer.Models.DataPoint> DownsamplePoints(List<LIVAnalyzer.Models.DataPoint> points, int targetCount)
        {
            if (points.Count <= targetCount)
                return points;
                
            var result = new List<LIVAnalyzer.Models.DataPoint>(targetCount);
            
            // 始终包含第一个和最后一个点
            result.Add(points.First());
            
            // 使用Douglas-Peucker算法的简化版本
            var step = (double)(points.Count - 1) / (targetCount - 1);
            for (int i = 1; i < targetCount - 1; i++)
            {
                var index = (int)(i * step);
                result.Add(points[index]);
            }
            
            result.Add(points.Last());
            
            // 确保保留峰值点
            var maxPoint = points.OrderByDescending(p => p.Y).First();
            var minPoint = points.OrderBy(p => p.Y).First();
            
            if (!result.Any(p => Math.Abs(p.Y - maxPoint.Y) < 1e-10))
            {
                // 找到最接近的点并替换
                var closestIndex = FindClosestIndex(result, maxPoint.X);
                if (closestIndex >= 0 && closestIndex < result.Count)
                {
                    result[closestIndex] = maxPoint;
                }
            }
            
            if (!result.Any(p => Math.Abs(p.Y - minPoint.Y) < 1e-10))
            {
                var closestIndex = FindClosestIndex(result, minPoint.X);
                if (closestIndex >= 0 && closestIndex < result.Count)
                {
                    result[closestIndex] = minPoint;
                }
            }
            
            return result.OrderBy(p => p.X).ToList();
        }
        
        /// <summary>
        /// 找到最接近目标X值的索引
        /// </summary>
        private int FindClosestIndex(List<LIVAnalyzer.Models.DataPoint> points, double targetX)
        {
            int closestIndex = 0;
            double minDistance = double.MaxValue;
            
            for (int i = 0; i < points.Count; i++)
            {
                var distance = Math.Abs(points[i].X - targetX);
                if (distance < minDistance)
                {
                    minDistance = distance;
                    closestIndex = i;
                }
            }
            
            return closestIndex;
        }
        
        /// <summary>
        /// 根据数据类型获取正确的工具提示格式字符串
        /// </summary>
        private string GetTrackerFormatString(string? yAxisKey)
        {
            return yAxisKey switch
            {
                "PowerAxis" => "{0}\n电流 (A): {2:F2}\n功率 (W): {4:F2}",
                "VoltageAxis" => "{0}\n电流 (A): {2:F2}\n电压 (V): {4:F2}",
                "IntensityAxis" => "{0}\n波长 (nm): {2:F1}\n强度 (a.u.): {4:F2}",
                "EfficiencyAxis" => "{0}\n电流 (A): {2:F2}\n效率 (%): {4:F2}",
                "DivergenceAxis" => "{0}\n角度 (°): {2:F1}\n强度 (a.u.): {4:F2}",
                _ => "{0}\nX: {2:F2}\nY: {4:F2}" // 默认格式
            };
        }
        
        /// <summary>
        /// 缓存图表模型
        /// </summary>
        public void CachePlotModel(string key, PlotModel model)
        {
            _plotCache.AddOrUpdate(key, model, (k, v) => model);
        }
        
        /// <summary>
        /// 获取缓存的图表模型
        /// </summary>
        public PlotModel? GetCachedPlotModel(string key)
        {
            return _plotCache.TryGetValue(key, out var model) ? model : null;
        }
        
        /// <summary>
        /// 清除缓存
        /// </summary>
        public void ClearCache()
        {
            _plotCache.Clear();
        }
    }
    
    /// <summary>
    /// 数据系列信息
    /// </summary>
    public class SeriesData
    {
        public string Title { get; set; } = "";
        public OxyColor Color { get; set; } = OxyColors.Blue;
        public double StrokeThickness { get; set; } = 2;
        public LineStyle LineStyle { get; set; } = LineStyle.Solid;
        public MarkerType MarkerType { get; set; } = MarkerType.None;
        public double MarkerSize { get; set; } = 3;
        public string? YAxisKey { get; set; }

        // 支持两种数据格式
        public List<LIVAnalyzer.Models.DataPoint> Points { get; set; } = new();
        public double[]? XData { get; set; }
        public double[]? YData { get; set; }
    }
}