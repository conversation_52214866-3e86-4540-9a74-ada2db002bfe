function Rn(e,t){for(var r=0;r<t.length;r++){const a=t[r];if(typeof a!="string"&&!Array.isArray(a)){for(const n in a)if(n!=="default"&&!(n in e)){const i=Object.getOwnPropertyDescriptor(a,n);i&&Object.defineProperty(e,n,i.get?i:{enumerable:!0,get:()=>a[n]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}function bn(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var _t={exports:{}},j={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var lr;function Cn(){if(lr)return j;lr=1;var e=Symbol.for("react.transitional.element"),t=Symbol.for("react.portal"),r=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),n=Symbol.for("react.profiler"),i=Symbol.for("react.consumer"),s=Symbol.for("react.context"),c=Symbol.for("react.forward_ref"),l=Symbol.for("react.suspense"),o=Symbol.for("react.memo"),h=Symbol.for("react.lazy"),g=Symbol.iterator;function v(d){return d===null||typeof d!="object"?null:(d=g&&d[g]||d["@@iterator"],typeof d=="function"?d:null)}var w={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},S=Object.assign,x={};function P(d,E,A){this.props=d,this.context=E,this.refs=x,this.updater=A||w}P.prototype.isReactComponent={},P.prototype.setState=function(d,E){if(typeof d!="object"&&typeof d!="function"&&d!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,d,E,"setState")},P.prototype.forceUpdate=function(d){this.updater.enqueueForceUpdate(this,d,"forceUpdate")};function R(){}R.prototype=P.prototype;function z(d,E,A){this.props=d,this.context=E,this.refs=x,this.updater=A||w}var _=z.prototype=new R;_.constructor=z,S(_,P.prototype),_.isPureReactComponent=!0;var V=Array.isArray,D={H:null,A:null,T:null,S:null,V:null},p=Object.prototype.hasOwnProperty;function q(d,E,A,N,H,J){return A=J.ref,{$$typeof:e,type:d,key:E,ref:A!==void 0?A:null,props:J}}function Z(d,E){return q(d.type,E,void 0,void 0,void 0,d.props)}function I(d){return typeof d=="object"&&d!==null&&d.$$typeof===e}function ie(d){var E={"=":"=0",":":"=2"};return"$"+d.replace(/[=:]/g,function(A){return E[A]})}var he=/\/+/g;function Ee(d,E){return typeof d=="object"&&d!==null&&d.key!=null?ie(""+d.key):E.toString(36)}function G(){}function X(d){switch(d.status){case"fulfilled":return d.value;case"rejected":throw d.reason;default:switch(typeof d.status=="string"?d.then(G,G):(d.status="pending",d.then(function(E){d.status==="pending"&&(d.status="fulfilled",d.value=E)},function(E){d.status==="pending"&&(d.status="rejected",d.reason=E)})),d.status){case"fulfilled":return d.value;case"rejected":throw d.reason}}throw d}function ae(d,E,A,N,H){var J=typeof d;(J==="undefined"||J==="boolean")&&(d=null);var k=!1;if(d===null)k=!0;else switch(J){case"bigint":case"string":case"number":k=!0;break;case"object":switch(d.$$typeof){case e:case t:k=!0;break;case h:return k=d._init,ae(k(d._payload),E,A,N,H)}}if(k)return H=H(d),k=N===""?"."+Ee(d,0):N,V(H)?(A="",k!=null&&(A=k.replace(he,"$&/")+"/"),ae(H,E,A,"",function(Ct){return Ct})):H!=null&&(I(H)&&(H=Z(H,A+(H.key==null||d&&d.key===H.key?"":(""+H.key).replace(he,"$&/")+"/")+k)),E.push(H)),1;k=0;var Re=N===""?".":N+":";if(V(d))for(var re=0;re<d.length;re++)N=d[re],J=Re+Ee(N,re),k+=ae(N,E,A,J,H);else if(re=v(d),typeof re=="function")for(d=re.call(d),re=0;!(N=d.next()).done;)N=N.value,J=Re+Ee(N,re++),k+=ae(N,E,A,J,H);else if(J==="object"){if(typeof d.then=="function")return ae(X(d),E,A,N,H);throw E=String(d),Error("Objects are not valid as a React child (found: "+(E==="[object Object]"?"object with keys {"+Object.keys(d).join(", ")+"}":E)+"). If you meant to render a collection of children, use an array instead.")}return k}function W(d,E,A){if(d==null)return d;var N=[],H=0;return ae(d,N,"","",function(J){return E.call(A,J,H++)}),N}function me(d){if(d._status===-1){var E=d._result;E=E(),E.then(function(A){(d._status===0||d._status===-1)&&(d._status=1,d._result=A)},function(A){(d._status===0||d._status===-1)&&(d._status=2,d._result=A)}),d._status===-1&&(d._status=0,d._result=E)}if(d._status===1)return d._result.default;throw d._result}var le=typeof reportError=="function"?reportError:function(d){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var E=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof d=="object"&&d!==null&&typeof d.message=="string"?String(d.message):String(d),error:d});if(!window.dispatchEvent(E))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",d);return}console.error(d)};function ye(){}return j.Children={map:W,forEach:function(d,E,A){W(d,function(){E.apply(this,arguments)},A)},count:function(d){var E=0;return W(d,function(){E++}),E},toArray:function(d){return W(d,function(E){return E})||[]},only:function(d){if(!I(d))throw Error("React.Children.only expected to receive a single React element child.");return d}},j.Component=P,j.Fragment=r,j.Profiler=n,j.PureComponent=z,j.StrictMode=a,j.Suspense=l,j.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=D,j.__COMPILER_RUNTIME={__proto__:null,c:function(d){return D.H.useMemoCache(d)}},j.cache=function(d){return function(){return d.apply(null,arguments)}},j.cloneElement=function(d,E,A){if(d==null)throw Error("The argument must be a React element, but you passed "+d+".");var N=S({},d.props),H=d.key,J=void 0;if(E!=null)for(k in E.ref!==void 0&&(J=void 0),E.key!==void 0&&(H=""+E.key),E)!p.call(E,k)||k==="key"||k==="__self"||k==="__source"||k==="ref"&&E.ref===void 0||(N[k]=E[k]);var k=arguments.length-2;if(k===1)N.children=A;else if(1<k){for(var Re=Array(k),re=0;re<k;re++)Re[re]=arguments[re+2];N.children=Re}return q(d.type,H,void 0,void 0,J,N)},j.createContext=function(d){return d={$$typeof:s,_currentValue:d,_currentValue2:d,_threadCount:0,Provider:null,Consumer:null},d.Provider=d,d.Consumer={$$typeof:i,_context:d},d},j.createElement=function(d,E,A){var N,H={},J=null;if(E!=null)for(N in E.key!==void 0&&(J=""+E.key),E)p.call(E,N)&&N!=="key"&&N!=="__self"&&N!=="__source"&&(H[N]=E[N]);var k=arguments.length-2;if(k===1)H.children=A;else if(1<k){for(var Re=Array(k),re=0;re<k;re++)Re[re]=arguments[re+2];H.children=Re}if(d&&d.defaultProps)for(N in k=d.defaultProps,k)H[N]===void 0&&(H[N]=k[N]);return q(d,J,void 0,void 0,null,H)},j.createRef=function(){return{current:null}},j.forwardRef=function(d){return{$$typeof:c,render:d}},j.isValidElement=I,j.lazy=function(d){return{$$typeof:h,_payload:{_status:-1,_result:d},_init:me}},j.memo=function(d,E){return{$$typeof:o,type:d,compare:E===void 0?null:E}},j.startTransition=function(d){var E=D.T,A={};D.T=A;try{var N=d(),H=D.S;H!==null&&H(A,N),typeof N=="object"&&N!==null&&typeof N.then=="function"&&N.then(ye,le)}catch(J){le(J)}finally{D.T=E}},j.unstable_useCacheRefresh=function(){return D.H.useCacheRefresh()},j.use=function(d){return D.H.use(d)},j.useActionState=function(d,E,A){return D.H.useActionState(d,E,A)},j.useCallback=function(d,E){return D.H.useCallback(d,E)},j.useContext=function(d){return D.H.useContext(d)},j.useDebugValue=function(){},j.useDeferredValue=function(d,E){return D.H.useDeferredValue(d,E)},j.useEffect=function(d,E,A){var N=D.H;if(typeof A=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return N.useEffect(d,E)},j.useId=function(){return D.H.useId()},j.useImperativeHandle=function(d,E,A){return D.H.useImperativeHandle(d,E,A)},j.useInsertionEffect=function(d,E){return D.H.useInsertionEffect(d,E)},j.useLayoutEffect=function(d,E){return D.H.useLayoutEffect(d,E)},j.useMemo=function(d,E){return D.H.useMemo(d,E)},j.useOptimistic=function(d,E){return D.H.useOptimistic(d,E)},j.useReducer=function(d,E,A){return D.H.useReducer(d,E,A)},j.useRef=function(d){return D.H.useRef(d)},j.useState=function(d){return D.H.useState(d)},j.useSyncExternalStore=function(d,E,A){return D.H.useSyncExternalStore(d,E,A)},j.useTransition=function(){return D.H.useTransition()},j.version="19.1.1",j}var sr;function Ar(){return sr||(sr=1,_t.exports=Cn()),_t.exports}var y=Ar();const Sn=bn(y),Ao=Rn({__proto__:null,default:Sn},[y]);var Dt={exports:{}},de={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ur;function Pn(){if(ur)return de;ur=1;var e=Ar();function t(l){var o="https://react.dev/errors/"+l;if(1<arguments.length){o+="?args[]="+encodeURIComponent(arguments[1]);for(var h=2;h<arguments.length;h++)o+="&args[]="+encodeURIComponent(arguments[h])}return"Minified React error #"+l+"; visit "+o+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function r(){}var a={d:{f:r,r:function(){throw Error(t(522))},D:r,C:r,L:r,m:r,X:r,S:r,M:r},p:0,findDOMNode:null},n=Symbol.for("react.portal");function i(l,o,h){var g=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:n,key:g==null?null:""+g,children:l,containerInfo:o,implementation:h}}var s=e.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function c(l,o){if(l==="font")return"";if(typeof o=="string")return o==="use-credentials"?o:""}return de.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=a,de.createPortal=function(l,o){var h=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!o||o.nodeType!==1&&o.nodeType!==9&&o.nodeType!==11)throw Error(t(299));return i(l,o,null,h)},de.flushSync=function(l){var o=s.T,h=a.p;try{if(s.T=null,a.p=2,l)return l()}finally{s.T=o,a.p=h,a.d.f()}},de.preconnect=function(l,o){typeof l=="string"&&(o?(o=o.crossOrigin,o=typeof o=="string"?o==="use-credentials"?o:"":void 0):o=null,a.d.C(l,o))},de.prefetchDNS=function(l){typeof l=="string"&&a.d.D(l)},de.preinit=function(l,o){if(typeof l=="string"&&o&&typeof o.as=="string"){var h=o.as,g=c(h,o.crossOrigin),v=typeof o.integrity=="string"?o.integrity:void 0,w=typeof o.fetchPriority=="string"?o.fetchPriority:void 0;h==="style"?a.d.S(l,typeof o.precedence=="string"?o.precedence:void 0,{crossOrigin:g,integrity:v,fetchPriority:w}):h==="script"&&a.d.X(l,{crossOrigin:g,integrity:v,fetchPriority:w,nonce:typeof o.nonce=="string"?o.nonce:void 0})}},de.preinitModule=function(l,o){if(typeof l=="string")if(typeof o=="object"&&o!==null){if(o.as==null||o.as==="script"){var h=c(o.as,o.crossOrigin);a.d.M(l,{crossOrigin:h,integrity:typeof o.integrity=="string"?o.integrity:void 0,nonce:typeof o.nonce=="string"?o.nonce:void 0})}}else o==null&&a.d.M(l)},de.preload=function(l,o){if(typeof l=="string"&&typeof o=="object"&&o!==null&&typeof o.as=="string"){var h=o.as,g=c(h,o.crossOrigin);a.d.L(l,h,{crossOrigin:g,integrity:typeof o.integrity=="string"?o.integrity:void 0,nonce:typeof o.nonce=="string"?o.nonce:void 0,type:typeof o.type=="string"?o.type:void 0,fetchPriority:typeof o.fetchPriority=="string"?o.fetchPriority:void 0,referrerPolicy:typeof o.referrerPolicy=="string"?o.referrerPolicy:void 0,imageSrcSet:typeof o.imageSrcSet=="string"?o.imageSrcSet:void 0,imageSizes:typeof o.imageSizes=="string"?o.imageSizes:void 0,media:typeof o.media=="string"?o.media:void 0})}},de.preloadModule=function(l,o){if(typeof l=="string")if(o){var h=c(o.as,o.crossOrigin);a.d.m(l,{as:typeof o.as=="string"&&o.as!=="script"?o.as:void 0,crossOrigin:h,integrity:typeof o.integrity=="string"?o.integrity:void 0})}else a.d.m(l)},de.requestFormReset=function(l){a.d.r(l)},de.unstable_batchedUpdates=function(l,o){return l(o)},de.useFormState=function(l,o,h){return s.H.useFormState(l,o,h)},de.useFormStatus=function(){return s.H.useHostTransitionStatus()},de.version="19.1.1",de}var cr;function xn(){if(cr)return Dt.exports;cr=1;function e(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}return e(),Dt.exports=Pn(),Dt.exports}/**
 * react-router v7.8.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */var Nr=e=>{throw TypeError(e)},Ln=(e,t,r)=>t.has(e)||Nr("Cannot "+r),Tt=(e,t,r)=>(Ln(e,t,"read from private field"),r?r.call(e):t.get(e)),_n=(e,t,r)=>t.has(e)?Nr("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,r),dr="popstate";function Dn(e={}){function t(a,n){let{pathname:i,search:s,hash:c}=a.location;return Ze("",{pathname:i,search:s,hash:c},n.state&&n.state.usr||null,n.state&&n.state.key||"default")}function r(a,n){return typeof n=="string"?n:Ae(n)}return Mn(t,r,null,e)}function B(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function ne(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function Tn(){return Math.random().toString(36).substring(2,10)}function fr(e,t){return{usr:e.state,key:e.key,idx:t}}function Ze(e,t,r=null,a){return{pathname:typeof e=="string"?e:e.pathname,search:"",hash:"",...typeof t=="string"?Ne(t):t,state:r,key:t&&t.key||a||Tn()}}function Ae({pathname:e="/",search:t="",hash:r=""}){return t&&t!=="?"&&(e+=t.charAt(0)==="?"?t:"?"+t),r&&r!=="#"&&(e+=r.charAt(0)==="#"?r:"#"+r),e}function Ne(e){let t={};if(e){let r=e.indexOf("#");r>=0&&(t.hash=e.substring(r),e=e.substring(0,r));let a=e.indexOf("?");a>=0&&(t.search=e.substring(a),e=e.substring(0,a)),e&&(t.pathname=e)}return t}function Mn(e,t,r,a={}){let{window:n=document.defaultView,v5Compat:i=!1}=a,s=n.history,c="POP",l=null,o=h();o==null&&(o=0,s.replaceState({...s.state,idx:o},""));function h(){return(s.state||{idx:null}).idx}function g(){c="POP";let P=h(),R=P==null?null:P-o;o=P,l&&l({action:c,location:x.location,delta:R})}function v(P,R){c="PUSH";let z=Ze(x.location,P,R);o=h()+1;let _=fr(z,o),V=x.createHref(z);try{s.pushState(_,"",V)}catch(D){if(D instanceof DOMException&&D.name==="DataCloneError")throw D;n.location.assign(V)}i&&l&&l({action:c,location:x.location,delta:1})}function w(P,R){c="REPLACE";let z=Ze(x.location,P,R);o=h();let _=fr(z,o),V=x.createHref(z);s.replaceState(_,"",V),i&&l&&l({action:c,location:x.location,delta:0})}function S(P){return Fr(P)}let x={get action(){return c},get location(){return e(n,s)},listen(P){if(l)throw new Error("A history only accepts one active listener");return n.addEventListener(dr,g),l=P,()=>{n.removeEventListener(dr,g),l=null}},createHref(P){return t(n,P)},createURL:S,encodeLocation(P){let R=S(P);return{pathname:R.pathname,search:R.search,hash:R.hash}},push:v,replace:w,go(P){return s.go(P)}};return x}function Fr(e,t=!1){let r="http://localhost";typeof window<"u"&&(r=window.location.origin!=="null"?window.location.origin:window.location.href),B(r,"No window.location.(origin|href) available to create URL");let a=typeof e=="string"?e:Ae(e);return a=a.replace(/ $/,"%20"),!t&&a.startsWith("//")&&(a=r+a),new URL(a,r)}var Qe,hr=class{constructor(e){if(_n(this,Qe,new Map),e)for(let[t,r]of e)this.set(t,r)}get(e){if(Tt(this,Qe).has(e))return Tt(this,Qe).get(e);if(e.defaultValue!==void 0)return e.defaultValue;throw new Error("No value found for context")}set(e,t){Tt(this,Qe).set(e,t)}};Qe=new WeakMap;var On=new Set(["lazy","caseSensitive","path","id","index","children"]);function An(e){return On.has(e)}var Nn=new Set(["lazy","caseSensitive","path","id","index","unstable_middleware","children"]);function Fn(e){return Nn.has(e)}function $n(e){return e.index===!0}function et(e,t,r=[],a={},n=!1){return e.map((i,s)=>{let c=[...r,String(s)],l=typeof i.id=="string"?i.id:c.join("-");if(B(i.index!==!0||!i.children,"Cannot specify children on an index route"),B(n||!a[l],`Found a route id collision on id "${l}".  Route id's must be globally unique within Data Router usages`),$n(i)){let o={...i,...t(i),id:l};return a[l]=o,o}else{let o={...i,...t(i),id:l,children:void 0};return a[l]=o,i.children&&(o.children=et(i.children,t,c,a,n)),o}})}function Me(e,t,r="/"){return mt(e,t,r,!1)}function mt(e,t,r,a){let n=typeof t=="string"?Ne(t):t,i=we(n.pathname||"/",r);if(i==null)return null;let s=$r(e);kn(s);let c=null;for(let l=0;c==null&&l<s.length;++l){let o=Gn(i);c=Kn(s[l],o,a)}return c}function Un(e,t){let{route:r,pathname:a,params:n}=e;return{id:r.id,pathname:a,params:n,data:t[r.id],loaderData:t[r.id],handle:r.handle}}function $r(e,t=[],r=[],a=""){let n=(i,s,c)=>{let l={relativePath:c===void 0?i.path||"":c,caseSensitive:i.caseSensitive===!0,childrenIndex:s,route:i};l.relativePath.startsWith("/")&&(B(l.relativePath.startsWith(a),`Absolute route path "${l.relativePath}" nested under path "${a}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),l.relativePath=l.relativePath.slice(a.length));let o=Ce([a,l.relativePath]),h=r.concat(l);i.children&&i.children.length>0&&(B(i.index!==!0,`Index routes must not have child routes. Please remove all child routes from route path "${o}".`),$r(i.children,t,h,o)),!(i.path==null&&!i.index)&&t.push({path:o,score:Yn(o,i.index),routesMeta:h})};return e.forEach((i,s)=>{if(i.path===""||!i.path?.includes("?"))n(i,s);else for(let c of Ur(i.path))n(i,s,c)}),t}function Ur(e){let t=e.split("/");if(t.length===0)return[];let[r,...a]=t,n=r.endsWith("?"),i=r.replace(/\?$/,"");if(a.length===0)return n?[i,""]:[i];let s=Ur(a.join("/")),c=[];return c.push(...s.map(l=>l===""?i:[i,l].join("/"))),n&&c.push(...s),c.map(l=>e.startsWith("/")&&l===""?"/":l)}function kn(e){e.sort((t,r)=>t.score!==r.score?r.score-t.score:Vn(t.routesMeta.map(a=>a.childrenIndex),r.routesMeta.map(a=>a.childrenIndex)))}var jn=/^:[\w-]+$/,Hn=3,zn=2,In=1,Bn=10,Wn=-2,mr=e=>e==="*";function Yn(e,t){let r=e.split("/"),a=r.length;return r.some(mr)&&(a+=Wn),t&&(a+=zn),r.filter(n=>!mr(n)).reduce((n,i)=>n+(jn.test(i)?Hn:i===""?In:Bn),a)}function Vn(e,t){return e.length===t.length&&e.slice(0,-1).every((a,n)=>a===t[n])?e[e.length-1]-t[t.length-1]:0}function Kn(e,t,r=!1){let{routesMeta:a}=e,n={},i="/",s=[];for(let c=0;c<a.length;++c){let l=a[c],o=c===a.length-1,h=i==="/"?t:t.slice(i.length)||"/",g=gt({path:l.relativePath,caseSensitive:l.caseSensitive,end:o},h),v=l.route;if(!g&&o&&r&&!a[a.length-1].route.index&&(g=gt({path:l.relativePath,caseSensitive:l.caseSensitive,end:!1},h)),!g)return null;Object.assign(n,g.params),s.push({params:n,pathname:Ce([i,g.pathname]),pathnameBase:Zn(Ce([i,g.pathnameBase])),route:v}),g.pathnameBase!=="/"&&(i=Ce([i,g.pathnameBase]))}return s}function gt(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[r,a]=qn(e.path,e.caseSensitive,e.end),n=t.match(r);if(!n)return null;let i=n[0],s=i.replace(/(.)\/+$/,"$1"),c=n.slice(1);return{params:a.reduce((o,{paramName:h,isOptional:g},v)=>{if(h==="*"){let S=c[v]||"";s=i.slice(0,i.length-S.length).replace(/(.)\/+$/,"$1")}const w=c[v];return g&&!w?o[h]=void 0:o[h]=(w||"").replace(/%2F/g,"/"),o},{}),pathname:i,pathnameBase:s,pattern:e}}function qn(e,t=!1,r=!0){ne(e==="*"||!e.endsWith("*")||e.endsWith("/*"),`Route path "${e}" will be treated as if it were "${e.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${e.replace(/\*$/,"/*")}".`);let a=[],n="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(s,c,l)=>(a.push({paramName:c,isOptional:l!=null}),l?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(a.push({paramName:"*"}),n+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):r?n+="\\/*$":e!==""&&e!=="/"&&(n+="(?:(?=\\/|$))"),[new RegExp(n,t?void 0:"i"),a]}function Gn(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return ne(!1,`The URL path "${e}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${t}).`),e}}function we(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let r=t.endsWith("/")?t.length-1:t.length,a=e.charAt(r);return a&&a!=="/"?null:e.slice(r)||"/"}function Jn({basename:e,pathname:t}){return t==="/"?e:Ce([e,t])}function Xn(e,t="/"){let{pathname:r,search:a="",hash:n=""}=typeof e=="string"?Ne(e):e;return{pathname:r?r.startsWith("/")?r:Qn(r,t):t,search:ea(a),hash:ta(n)}}function Qn(e,t){let r=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(n=>{n===".."?r.length>1&&r.pop():n!=="."&&r.push(n)}),r.length>1?r.join("/"):"/"}function Mt(e,t,r,a){return`Cannot include a '${e}' character in a manually specified \`to.${t}\` field [${JSON.stringify(a)}].  Please separate it out to the \`to.${r}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function kr(e){return e.filter((t,r)=>r===0||t.route.path&&t.route.path.length>0)}function jt(e){let t=kr(e);return t.map((r,a)=>a===t.length-1?r.pathname:r.pathnameBase)}function Ht(e,t,r,a=!1){let n;typeof e=="string"?n=Ne(e):(n={...e},B(!n.pathname||!n.pathname.includes("?"),Mt("?","pathname","search",n)),B(!n.pathname||!n.pathname.includes("#"),Mt("#","pathname","hash",n)),B(!n.search||!n.search.includes("#"),Mt("#","search","hash",n)));let i=e===""||n.pathname==="",s=i?"/":n.pathname,c;if(s==null)c=r;else{let g=t.length-1;if(!a&&s.startsWith("..")){let v=s.split("/");for(;v[0]==="..";)v.shift(),g-=1;n.pathname=v.join("/")}c=g>=0?t[g]:"/"}let l=Xn(n,c),o=s&&s!=="/"&&s.endsWith("/"),h=(i||s===".")&&r.endsWith("/");return!l.pathname.endsWith("/")&&(o||h)&&(l.pathname+="/"),l}var Ce=e=>e.join("/").replace(/\/\/+/g,"/"),Zn=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),ea=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,ta=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e,wt=class{constructor(e,t,r,a=!1){this.status=e,this.statusText=t||"",this.internal=a,r instanceof Error?(this.data=r.toString(),this.error=r):this.data=r}};function tt(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}var jr=["POST","PUT","PATCH","DELETE"],ra=new Set(jr),na=["GET",...jr],aa=new Set(na),oa=new Set([301,302,303,307,308]),ia=new Set([307,308]),Ot={state:"idle",location:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},la={state:"idle",data:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},Ge={state:"unblocked",proceed:void 0,reset:void 0,location:void 0},sa=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,zt=e=>sa.test(e),ua=e=>({hasErrorBoundary:!!e.hasErrorBoundary}),Hr="remix-router-transitions",zr=Symbol("ResetLoaderData");function ca(e){const t=e.window?e.window:typeof window<"u"?window:void 0,r=typeof t<"u"&&typeof t.document<"u"&&typeof t.document.createElement<"u";B(e.routes.length>0,"You must provide a non-empty routes array to createRouter");let a=e.hydrationRouteProperties||[],n=e.mapRouteProperties||ua,i={},s=et(e.routes,n,void 0,i),c,l=e.basename||"/",o=e.dataStrategy||pa,h={unstable_middleware:!1,...e.future},g=null,v=new Set,w=null,S=null,x=null,P=e.hydrationData!=null,R=Me(s,e.history.location,l),z=!1,_=null,V;if(R==null&&!e.patchRoutesOnNavigation){let u=ge(404,{pathname:e.history.location.pathname}),{matches:f,route:m}=xr(s);V=!0,R=f,_={[m.id]:u}}else if(R&&!e.hydrationData&&it(R,s,e.history.location.pathname).active&&(R=null),R)if(R.some(u=>u.route.lazy))V=!1;else if(!R.some(u=>u.route.loader))V=!0;else{let u=e.hydrationData?e.hydrationData.loaderData:null,f=e.hydrationData?e.hydrationData.errors:null;if(f){let m=R.findIndex(b=>f[b.route.id]!==void 0);V=R.slice(0,m+1).every(b=>!$t(b.route,u,f))}else V=R.every(m=>!$t(m.route,u,f))}else{V=!1,R=[];let u=it(null,s,e.history.location.pathname);u.active&&u.matches&&(z=!0,R=u.matches)}let D,p={historyAction:e.history.action,location:e.history.location,matches:R,initialized:V,navigation:Ot,restoreScrollPosition:e.hydrationData!=null?!1:null,preventScrollReset:!1,revalidation:"idle",loaderData:e.hydrationData&&e.hydrationData.loaderData||{},actionData:e.hydrationData&&e.hydrationData.actionData||null,errors:e.hydrationData&&e.hydrationData.errors||_,fetchers:new Map,blockers:new Map},q="POP",Z=!1,I,ie=!1,he=new Map,Ee=null,G=!1,X=!1,ae=new Set,W=new Map,me=0,le=-1,ye=new Map,d=new Set,E=new Map,A=new Map,N=new Set,H=new Map,J,k=null;function Re(){if(g=e.history.listen(({action:u,location:f,delta:m})=>{if(J){J(),J=void 0;return}ne(H.size===0||m!=null,"You are trying to use a blocker on a POP navigation to a location that was not created by @remix-run/router. This will fail silently in production. This can happen if you are navigating outside the router via `window.history.pushState`/`window.location.hash` instead of using router navigation APIs.  This can also happen if you are using createHashRouter and the user manually changes the URL.");let b=nr({currentLocation:p.location,nextLocation:f,historyAction:u});if(b&&m!=null){let C=new Promise(T=>{J=T});e.history.go(m*-1),ot(b,{state:"blocked",location:f,proceed(){ot(b,{state:"proceeding",proceed:void 0,reset:void 0,location:f}),C.then(()=>e.history.go(m))},reset(){let T=new Map(p.blockers);T.set(b,Ge),se({blockers:T})}});return}return Fe(u,f)}),r){xa(t,he);let u=()=>La(t,he);t.addEventListener("pagehide",u),Ee=()=>t.removeEventListener("pagehide",u)}return p.initialized||Fe("POP",p.location,{initialHydration:!0}),D}function re(){g&&g(),Ee&&Ee(),v.clear(),I&&I.abort(),p.fetchers.forEach((u,f)=>Pt(f)),p.blockers.forEach((u,f)=>rr(f))}function Ct(u){return v.add(u),()=>v.delete(u)}function se(u,f={}){u.matches&&(u.matches=u.matches.map(C=>{let T=i[C.route.id],M=C.route;return M.element!==T.element||M.errorElement!==T.errorElement||M.hydrateFallbackElement!==T.hydrateFallbackElement?{...C,route:T}:C})),p={...p,...u};let m=[],b=[];p.fetchers.forEach((C,T)=>{C.state==="idle"&&(N.has(T)?m.push(T):b.push(T))}),N.forEach(C=>{!p.fetchers.has(C)&&!W.has(C)&&m.push(C)}),[...v].forEach(C=>C(p,{deletedFetchers:m,viewTransitionOpts:f.viewTransitionOpts,flushSync:f.flushSync===!0})),m.forEach(C=>Pt(C)),b.forEach(C=>p.fetchers.delete(C))}function Ie(u,f,{flushSync:m}={}){let b=p.actionData!=null&&p.navigation.formMethod!=null&&pe(p.navigation.formMethod)&&p.navigation.state==="loading"&&u.state?._isRedirect!==!0,C;f.actionData?Object.keys(f.actionData).length>0?C=f.actionData:C=null:b?C=p.actionData:C=null;let T=f.loaderData?Sr(p.loaderData,f.loaderData,f.matches||[],f.errors):p.loaderData,M=p.blockers;M.size>0&&(M=new Map(M),M.forEach((O,$)=>M.set($,Ge)));let L=G?!1:or(u,f.matches||p.matches),F=Z===!0||p.navigation.formMethod!=null&&pe(p.navigation.formMethod)&&u.state?._isRedirect!==!0;c&&(s=c,c=void 0),G||q==="POP"||(q==="PUSH"?e.history.push(u,u.state):q==="REPLACE"&&e.history.replace(u,u.state));let U;if(q==="POP"){let O=he.get(p.location.pathname);O&&O.has(u.pathname)?U={currentLocation:p.location,nextLocation:u}:he.has(u.pathname)&&(U={currentLocation:u,nextLocation:p.location})}else if(ie){let O=he.get(p.location.pathname);O?O.add(u.pathname):(O=new Set([u.pathname]),he.set(p.location.pathname,O)),U={currentLocation:p.location,nextLocation:u}}se({...f,actionData:C,loaderData:T,historyAction:q,location:u,initialized:!0,navigation:Ot,revalidation:"idle",restoreScrollPosition:L,preventScrollReset:F,blockers:M},{viewTransitionOpts:U,flushSync:m===!0}),q="POP",Z=!1,ie=!1,G=!1,X=!1,k?.resolve(),k=null}async function Gt(u,f){if(typeof u=="number"){e.history.go(u);return}let m=Ft(p.location,p.matches,l,u,f?.fromRouteId,f?.relative),{path:b,submission:C,error:T}=pr(!1,m,f),M=p.location,L=Ze(p.location,b,f&&f.state);L={...L,...e.history.encodeLocation(L)};let F=f&&f.replace!=null?f.replace:void 0,U="PUSH";F===!0?U="REPLACE":F===!1||C!=null&&pe(C.formMethod)&&C.formAction===p.location.pathname+p.location.search&&(U="REPLACE");let O=f&&"preventScrollReset"in f?f.preventScrollReset===!0:void 0,$=(f&&f.flushSync)===!0,Y=nr({currentLocation:M,nextLocation:L,historyAction:U});if(Y){ot(Y,{state:"blocked",location:L,proceed(){ot(Y,{state:"proceeding",proceed:void 0,reset:void 0,location:L}),Gt(u,f)},reset(){let te=new Map(p.blockers);te.set(Y,Ge),se({blockers:te})}});return}await Fe(U,L,{submission:C,pendingError:T,preventScrollReset:O,replace:f&&f.replace,enableViewTransition:f&&f.viewTransition,flushSync:$})}function ln(){k||(k=_a()),St(),se({revalidation:"loading"});let u=k.promise;return p.navigation.state==="submitting"?u:p.navigation.state==="idle"?(Fe(p.historyAction,p.location,{startUninterruptedRevalidation:!0}),u):(Fe(q||p.historyAction,p.navigation.location,{overrideNavigation:p.navigation,enableViewTransition:ie===!0}),u)}async function Fe(u,f,m){I&&I.abort(),I=null,q=u,G=(m&&m.startUninterruptedRevalidation)===!0,vn(p.location,p.matches),Z=(m&&m.preventScrollReset)===!0,ie=(m&&m.enableViewTransition)===!0;let b=c||s,C=m&&m.overrideNavigation,T=m?.initialHydration&&p.matches&&p.matches.length>0&&!z?p.matches:Me(b,f,l),M=(m&&m.flushSync)===!0;if(T&&p.initialized&&!X&&Ra(p.location,f)&&!(m&&m.submission&&pe(m.submission.formMethod))){Ie(f,{matches:T},{flushSync:M});return}let L=it(T,b,f.pathname);if(L.active&&L.matches&&(T=L.matches),!T){let{error:fe,notFoundMatches:ee,route:Q}=xt(f.pathname);Ie(f,{matches:ee,loaderData:{},errors:{[Q.id]:fe}},{flushSync:M});return}I=new AbortController;let F=Be(e.history,f,I.signal,m&&m.submission),U=e.unstable_getContext?await e.unstable_getContext():new hr,O;if(m&&m.pendingError)O=[Oe(T).route.id,{type:"error",error:m.pendingError}];else if(m&&m.submission&&pe(m.submission.formMethod)){let fe=await sn(F,f,m.submission,T,U,L.active,m&&m.initialHydration===!0,{replace:m.replace,flushSync:M});if(fe.shortCircuited)return;if(fe.pendingActionResult){let[ee,Q]=fe.pendingActionResult;if(ve(Q)&&tt(Q.error)&&Q.error.status===404){I=null,Ie(f,{matches:fe.matches,loaderData:{},errors:{[ee]:Q.error}});return}}T=fe.matches||T,O=fe.pendingActionResult,C=At(f,m.submission),M=!1,L.active=!1,F=Be(e.history,F.url,F.signal)}let{shortCircuited:$,matches:Y,loaderData:te,errors:ue}=await un(F,f,T,U,L.active,C,m&&m.submission,m&&m.fetcherSubmission,m&&m.replace,m&&m.initialHydration===!0,M,O);$||(I=null,Ie(f,{matches:Y||T,...Pr(O),loaderData:te,errors:ue}))}async function sn(u,f,m,b,C,T,M,L={}){St();let F=Sa(f,m);if(se({navigation:F},{flushSync:L.flushSync===!0}),T){let $=await lt(b,f.pathname,u.signal);if($.type==="aborted")return{shortCircuited:!0};if($.type==="error"){let Y=Oe($.partialMatches).route.id;return{matches:$.partialMatches,pendingActionResult:[Y,{type:"error",error:$.error}]}}else if($.matches)b=$.matches;else{let{notFoundMatches:Y,error:te,route:ue}=xt(f.pathname);return{matches:Y,pendingActionResult:[ue.id,{type:"error",error:te}]}}}let U,O=pt(b,f);if(!O.route.action&&!O.route.lazy)U={type:"error",error:ge(405,{method:u.method,pathname:f.pathname,routeId:O.route.id})};else{let $=We(n,i,u,b,O,M?[]:a,C),Y=await Ye(u,$,C,null);if(U=Y[O.route.id],!U){for(let te of b)if(Y[te.route.id]){U=Y[te.route.id];break}}if(u.signal.aborted)return{shortCircuited:!0}}if(je(U)){let $;return L&&L.replace!=null?$=L.replace:$=Rr(U.response.headers.get("Location"),new URL(u.url),l)===p.location.pathname+p.location.search,await $e(u,U,!0,{submission:m,replace:$}),{shortCircuited:!0}}if(ve(U)){let $=Oe(b,O.route.id);return(L&&L.replace)!==!0&&(q="PUSH"),{matches:b,pendingActionResult:[$.route.id,U,O.route.id]}}return{matches:b,pendingActionResult:[O.route.id,U]}}async function un(u,f,m,b,C,T,M,L,F,U,O,$){let Y=T||At(f,M),te=M||L||_r(Y),ue=!G&&!U;if(C){if(ue){let ce=Jt($);se({navigation:Y,...ce!==void 0?{actionData:ce}:{}},{flushSync:O})}let K=await lt(m,f.pathname,u.signal);if(K.type==="aborted")return{shortCircuited:!0};if(K.type==="error"){let ce=Oe(K.partialMatches).route.id;return{matches:K.partialMatches,loaderData:{},errors:{[ce]:K.error}}}else if(K.matches)m=K.matches;else{let{error:ce,notFoundMatches:ct,route:qe}=xt(f.pathname);return{matches:ct,loaderData:{},errors:{[qe.id]:ce}}}}let fe=c||s,{dsMatches:ee,revalidatingFetchers:Q}=yr(u,b,n,i,e.history,p,m,te,f,U?[]:a,U===!0,X,ae,N,E,d,fe,l,e.patchRoutesOnNavigation!=null,$);if(le=++me,!e.dataStrategy&&!ee.some(K=>K.shouldLoad)&&!ee.some(K=>K.route.unstable_middleware)&&Q.length===0){let K=er();return Ie(f,{matches:m,loaderData:{},errors:$&&ve($[1])?{[$[0]]:$[1].error}:null,...Pr($),...K?{fetchers:new Map(p.fetchers)}:{}},{flushSync:O}),{shortCircuited:!0}}if(ue){let K={};if(!C){K.navigation=Y;let ce=Jt($);ce!==void 0&&(K.actionData=ce)}Q.length>0&&(K.fetchers=cn(Q)),se(K,{flushSync:O})}Q.forEach(K=>{_e(K.key),K.controller&&W.set(K.key,K.controller)});let Ue=()=>Q.forEach(K=>_e(K.key));I&&I.signal.addEventListener("abort",Ue);let{loaderResults:Ve,fetcherResults:De}=await Xt(ee,Q,u,b);if(u.signal.aborted)return{shortCircuited:!0};I&&I.signal.removeEventListener("abort",Ue),Q.forEach(K=>W.delete(K.key));let be=ft(Ve);if(be)return await $e(u,be.result,!0,{replace:F}),{shortCircuited:!0};if(be=ft(De),be)return d.add(be.key),await $e(u,be.result,!0,{replace:F}),{shortCircuited:!0};let{loaderData:Lt,errors:Ke}=Cr(p,m,Ve,$,Q,De);U&&p.errors&&(Ke={...p.errors,...Ke});let ke=er(),st=tr(le),ut=ke||st||Q.length>0;return{matches:m,loaderData:Lt,errors:Ke,...ut?{fetchers:new Map(p.fetchers)}:{}}}function Jt(u){if(u&&!ve(u[1]))return{[u[0]]:u[1].data};if(p.actionData)return Object.keys(p.actionData).length===0?null:p.actionData}function cn(u){return u.forEach(f=>{let m=p.fetchers.get(f.key),b=Je(void 0,m?m.data:void 0);p.fetchers.set(f.key,b)}),new Map(p.fetchers)}async function dn(u,f,m,b){_e(u);let C=(b&&b.flushSync)===!0,T=c||s,M=Ft(p.location,p.matches,l,m,f,b?.relative),L=Me(T,M,l),F=it(L,T,M);if(F.active&&F.matches&&(L=F.matches),!L){Pe(u,f,ge(404,{pathname:M}),{flushSync:C});return}let{path:U,submission:O,error:$}=pr(!0,M,b);if($){Pe(u,f,$,{flushSync:C});return}let Y=e.unstable_getContext?await e.unstable_getContext():new hr,te=(b&&b.preventScrollReset)===!0;if(O&&pe(O.formMethod)){await fn(u,f,U,L,Y,F.active,C,te,O);return}E.set(u,{routeId:f,path:U}),await hn(u,f,U,L,Y,F.active,C,te,O)}async function fn(u,f,m,b,C,T,M,L,F){St(),E.delete(u);let U=p.fetchers.get(u);Le(u,Pa(F,U),{flushSync:M});let O=new AbortController,$=Be(e.history,m,O.signal,F);if(T){let oe=await lt(b,new URL($.url).pathname,$.signal,u);if(oe.type==="aborted")return;if(oe.type==="error"){Pe(u,f,oe.error,{flushSync:M});return}else if(oe.matches)b=oe.matches;else{Pe(u,f,ge(404,{pathname:m}),{flushSync:M});return}}let Y=pt(b,m);if(!Y.route.action&&!Y.route.lazy){let oe=ge(405,{method:F.formMethod,pathname:m,routeId:f});Pe(u,f,oe,{flushSync:M});return}W.set(u,O);let te=me,ue=We(n,i,$,b,Y,a,C),ee=(await Ye($,ue,C,u))[Y.route.id];if($.signal.aborted){W.get(u)===O&&W.delete(u);return}if(N.has(u)){if(je(ee)||ve(ee)){Le(u,Te(void 0));return}}else{if(je(ee))if(W.delete(u),le>te){Le(u,Te(void 0));return}else return d.add(u),Le(u,Je(F)),$e($,ee,!1,{fetcherSubmission:F,preventScrollReset:L});if(ve(ee)){Pe(u,f,ee.error);return}}let Q=p.navigation.location||p.location,Ue=Be(e.history,Q,O.signal),Ve=c||s,De=p.navigation.state!=="idle"?Me(Ve,p.navigation.location,l):p.matches;B(De,"Didn't find any matches after fetcher action");let be=++me;ye.set(u,be);let Lt=Je(F,ee.data);p.fetchers.set(u,Lt);let{dsMatches:Ke,revalidatingFetchers:ke}=yr(Ue,C,n,i,e.history,p,De,F,Q,a,!1,X,ae,N,E,d,Ve,l,e.patchRoutesOnNavigation!=null,[Y.route.id,ee]);ke.filter(oe=>oe.key!==u).forEach(oe=>{let dt=oe.key,ir=p.fetchers.get(dt),En=Je(void 0,ir?ir.data:void 0);p.fetchers.set(dt,En),_e(dt),oe.controller&&W.set(dt,oe.controller)}),se({fetchers:new Map(p.fetchers)});let st=()=>ke.forEach(oe=>_e(oe.key));O.signal.addEventListener("abort",st);let{loaderResults:ut,fetcherResults:K}=await Xt(Ke,ke,Ue,C);if(O.signal.aborted)return;if(O.signal.removeEventListener("abort",st),ye.delete(u),W.delete(u),ke.forEach(oe=>W.delete(oe.key)),p.fetchers.has(u)){let oe=Te(ee.data);p.fetchers.set(u,oe)}let ce=ft(ut);if(ce)return $e(Ue,ce.result,!1,{preventScrollReset:L});if(ce=ft(K),ce)return d.add(ce.key),$e(Ue,ce.result,!1,{preventScrollReset:L});let{loaderData:ct,errors:qe}=Cr(p,De,ut,void 0,ke,K);tr(be),p.navigation.state==="loading"&&be>le?(B(q,"Expected pending action"),I&&I.abort(),Ie(p.navigation.location,{matches:De,loaderData:ct,errors:qe,fetchers:new Map(p.fetchers)})):(se({errors:qe,loaderData:Sr(p.loaderData,ct,De,qe),fetchers:new Map(p.fetchers)}),X=!1)}async function hn(u,f,m,b,C,T,M,L,F){let U=p.fetchers.get(u);Le(u,Je(F,U?U.data:void 0),{flushSync:M});let O=new AbortController,$=Be(e.history,m,O.signal);if(T){let Q=await lt(b,new URL($.url).pathname,$.signal,u);if(Q.type==="aborted")return;if(Q.type==="error"){Pe(u,f,Q.error,{flushSync:M});return}else if(Q.matches)b=Q.matches;else{Pe(u,f,ge(404,{pathname:m}),{flushSync:M});return}}let Y=pt(b,m);W.set(u,O);let te=me,ue=We(n,i,$,b,Y,a,C),ee=(await Ye($,ue,C,u))[Y.route.id];if(W.get(u)===O&&W.delete(u),!$.signal.aborted){if(N.has(u)){Le(u,Te(void 0));return}if(je(ee))if(le>te){Le(u,Te(void 0));return}else{d.add(u),await $e($,ee,!1,{preventScrollReset:L});return}if(ve(ee)){Pe(u,f,ee.error);return}Le(u,Te(ee.data))}}async function $e(u,f,m,{submission:b,fetcherSubmission:C,preventScrollReset:T,replace:M}={}){f.response.headers.has("X-Remix-Revalidate")&&(X=!0);let L=f.response.headers.get("Location");B(L,"Expected a Location header on the redirect Response"),L=Rr(L,new URL(u.url),l);let F=Ze(p.location,L,{_isRedirect:!0});if(r){let ue=!1;if(f.response.headers.has("X-Remix-Reload-Document"))ue=!0;else if(zt(L)){const fe=Fr(L,!0);ue=fe.origin!==t.location.origin||we(fe.pathname,l)==null}if(ue){M?t.location.replace(L):t.location.assign(L);return}}I=null;let U=M===!0||f.response.headers.has("X-Remix-Replace")?"REPLACE":"PUSH",{formMethod:O,formAction:$,formEncType:Y}=p.navigation;!b&&!C&&O&&$&&Y&&(b=_r(p.navigation));let te=b||C;if(ia.has(f.response.status)&&te&&pe(te.formMethod))await Fe(U,F,{submission:{...te,formAction:L},preventScrollReset:T||Z,enableViewTransition:m?ie:void 0});else{let ue=At(F,b);await Fe(U,F,{overrideNavigation:ue,fetcherSubmission:C,preventScrollReset:T||Z,enableViewTransition:m?ie:void 0})}}async function Ye(u,f,m,b){let C,T={};try{C=await ya(o,u,f,b,m,!1)}catch(M){return f.filter(L=>L.shouldLoad).forEach(L=>{T[L.route.id]={type:"error",error:M}}),T}if(u.signal.aborted)return T;for(let[M,L]of Object.entries(C))if(ba(L)){let F=L.result;T[M]={type:"redirect",response:wa(F,u,M,f,l)}}else T[M]=await ga(L);return T}async function Xt(u,f,m,b){let C=Ye(m,u,b,null),T=Promise.all(f.map(async F=>{if(F.matches&&F.match&&F.request&&F.controller){let O=(await Ye(F.request,F.matches,b,F.key))[F.match.route.id];return{[F.key]:O}}else return Promise.resolve({[F.key]:{type:"error",error:ge(404,{pathname:F.path})}})})),M=await C,L=(await T).reduce((F,U)=>Object.assign(F,U),{});return{loaderResults:M,fetcherResults:L}}function St(){X=!0,E.forEach((u,f)=>{W.has(f)&&ae.add(f),_e(f)})}function Le(u,f,m={}){p.fetchers.set(u,f),se({fetchers:new Map(p.fetchers)},{flushSync:(m&&m.flushSync)===!0})}function Pe(u,f,m,b={}){let C=Oe(p.matches,f);Pt(u),se({errors:{[C.route.id]:m},fetchers:new Map(p.fetchers)},{flushSync:(b&&b.flushSync)===!0})}function Qt(u){return A.set(u,(A.get(u)||0)+1),N.has(u)&&N.delete(u),p.fetchers.get(u)||la}function Pt(u){let f=p.fetchers.get(u);W.has(u)&&!(f&&f.state==="loading"&&ye.has(u))&&_e(u),E.delete(u),ye.delete(u),d.delete(u),N.delete(u),ae.delete(u),p.fetchers.delete(u)}function mn(u){let f=(A.get(u)||0)-1;f<=0?(A.delete(u),N.add(u)):A.set(u,f),se({fetchers:new Map(p.fetchers)})}function _e(u){let f=W.get(u);f&&(f.abort(),W.delete(u))}function Zt(u){for(let f of u){let m=Qt(f),b=Te(m.data);p.fetchers.set(f,b)}}function er(){let u=[],f=!1;for(let m of d){let b=p.fetchers.get(m);B(b,`Expected fetcher: ${m}`),b.state==="loading"&&(d.delete(m),u.push(m),f=!0)}return Zt(u),f}function tr(u){let f=[];for(let[m,b]of ye)if(b<u){let C=p.fetchers.get(m);B(C,`Expected fetcher: ${m}`),C.state==="loading"&&(_e(m),ye.delete(m),f.push(m))}return Zt(f),f.length>0}function pn(u,f){let m=p.blockers.get(u)||Ge;return H.get(u)!==f&&H.set(u,f),m}function rr(u){p.blockers.delete(u),H.delete(u)}function ot(u,f){let m=p.blockers.get(u)||Ge;B(m.state==="unblocked"&&f.state==="blocked"||m.state==="blocked"&&f.state==="blocked"||m.state==="blocked"&&f.state==="proceeding"||m.state==="blocked"&&f.state==="unblocked"||m.state==="proceeding"&&f.state==="unblocked",`Invalid blocker state transition: ${m.state} -> ${f.state}`);let b=new Map(p.blockers);b.set(u,f),se({blockers:b})}function nr({currentLocation:u,nextLocation:f,historyAction:m}){if(H.size===0)return;H.size>1&&ne(!1,"A router only supports one blocker at a time");let b=Array.from(H.entries()),[C,T]=b[b.length-1],M=p.blockers.get(C);if(!(M&&M.state==="proceeding")&&T({currentLocation:u,nextLocation:f,historyAction:m}))return C}function xt(u){let f=ge(404,{pathname:u}),m=c||s,{matches:b,route:C}=xr(m);return{notFoundMatches:b,route:C,error:f}}function yn(u,f,m){if(w=u,x=f,S=m||null,!P&&p.navigation===Ot){P=!0;let b=or(p.location,p.matches);b!=null&&se({restoreScrollPosition:b})}return()=>{w=null,x=null,S=null}}function ar(u,f){return S&&S(u,f.map(b=>Un(b,p.loaderData)))||u.key}function vn(u,f){if(w&&x){let m=ar(u,f);w[m]=x()}}function or(u,f){if(w){let m=ar(u,f),b=w[m];if(typeof b=="number")return b}return null}function it(u,f,m){if(e.patchRoutesOnNavigation)if(u){if(Object.keys(u[0].params).length>0)return{active:!0,matches:mt(f,m,l,!0)}}else return{active:!0,matches:mt(f,m,l,!0)||[]};return{active:!1,matches:null}}async function lt(u,f,m,b){if(!e.patchRoutesOnNavigation)return{type:"success",matches:u};let C=u;for(;;){let T=c==null,M=c||s,L=i;try{await e.patchRoutesOnNavigation({signal:m,path:f,matches:C,fetcherKey:b,patch:(O,$)=>{m.aborted||vr(O,$,M,L,n,!1)}})}catch(O){return{type:"error",error:O,partialMatches:C}}finally{T&&!m.aborted&&(s=[...s])}if(m.aborted)return{type:"aborted"};let F=Me(M,f,l);if(F)return{type:"success",matches:F};let U=mt(M,f,l,!0);if(!U||C.length===U.length&&C.every((O,$)=>O.route.id===U[$].route.id))return{type:"success",matches:null};C=U}}function gn(u){i={},c=et(u,n,void 0,i)}function wn(u,f,m=!1){let b=c==null;vr(u,f,c||s,i,n,m),b&&(s=[...s],se({}))}return D={get basename(){return l},get future(){return h},get state(){return p},get routes(){return s},get window(){return t},initialize:Re,subscribe:Ct,enableScrollRestoration:yn,navigate:Gt,fetch:dn,revalidate:ln,createHref:u=>e.history.createHref(u),encodeLocation:u=>e.history.encodeLocation(u),getFetcher:Qt,deleteFetcher:mn,dispose:re,getBlocker:pn,deleteBlocker:rr,patchRoutes:wn,_internalFetchControllers:W,_internalSetRoutes:gn,_internalSetStateDoNotUseOrYouWillBreakYourApp(u){se(u)}},D}function da(e){return e!=null&&("formData"in e&&e.formData!=null||"body"in e&&e.body!==void 0)}function Ft(e,t,r,a,n,i){let s,c;if(n){s=[];for(let o of t)if(s.push(o),o.route.id===n){c=o;break}}else s=t,c=t[t.length-1];let l=Ht(a||".",jt(s),we(e.pathname,r)||e.pathname,i==="path");if(a==null&&(l.search=e.search,l.hash=e.hash),(a==null||a===""||a===".")&&c){let o=It(l.search);if(c.route.index&&!o)l.search=l.search?l.search.replace(/^\?/,"?index&"):"?index";else if(!c.route.index&&o){let h=new URLSearchParams(l.search),g=h.getAll("index");h.delete("index"),g.filter(w=>w).forEach(w=>h.append("index",w));let v=h.toString();l.search=v?`?${v}`:""}}return r!=="/"&&(l.pathname=Jn({basename:r,pathname:l.pathname})),Ae(l)}function pr(e,t,r){if(!r||!da(r))return{path:t};if(r.formMethod&&!Ca(r.formMethod))return{path:t,error:ge(405,{method:r.formMethod})};let a=()=>({path:t,error:ge(400,{type:"invalid-body"})}),i=(r.formMethod||"get").toUpperCase(),s=qr(t);if(r.body!==void 0){if(r.formEncType==="text/plain"){if(!pe(i))return a();let g=typeof r.body=="string"?r.body:r.body instanceof FormData||r.body instanceof URLSearchParams?Array.from(r.body.entries()).reduce((v,[w,S])=>`${v}${w}=${S}
`,""):String(r.body);return{path:t,submission:{formMethod:i,formAction:s,formEncType:r.formEncType,formData:void 0,json:void 0,text:g}}}else if(r.formEncType==="application/json"){if(!pe(i))return a();try{let g=typeof r.body=="string"?JSON.parse(r.body):r.body;return{path:t,submission:{formMethod:i,formAction:s,formEncType:r.formEncType,formData:void 0,json:g,text:void 0}}}catch{return a()}}}B(typeof FormData=="function","FormData is not available in this environment");let c,l;if(r.formData)c=kt(r.formData),l=r.formData;else if(r.body instanceof FormData)c=kt(r.body),l=r.body;else if(r.body instanceof URLSearchParams)c=r.body,l=br(c);else if(r.body==null)c=new URLSearchParams,l=new FormData;else try{c=new URLSearchParams(r.body),l=br(c)}catch{return a()}let o={formMethod:i,formAction:s,formEncType:r&&r.formEncType||"application/x-www-form-urlencoded",formData:l,json:void 0,text:void 0};if(pe(o.formMethod))return{path:t,submission:o};let h=Ne(t);return e&&h.search&&It(h.search)&&c.append("index",""),h.search=`?${c}`,{path:Ae(h),submission:o}}function yr(e,t,r,a,n,i,s,c,l,o,h,g,v,w,S,x,P,R,z,_){let V=_?ve(_[1])?_[1].error:_[1].data:void 0,D=n.createURL(i.location),p=n.createURL(l),q;if(h&&i.errors){let G=Object.keys(i.errors)[0];q=s.findIndex(X=>X.route.id===G)}else if(_&&ve(_[1])){let G=_[0];q=s.findIndex(X=>X.route.id===G)-1}let Z=_?_[1].statusCode:void 0,I=Z&&Z>=400,ie={currentUrl:D,currentParams:i.matches[0]?.params||{},nextUrl:p,nextParams:s[0].params,...c,actionResult:V,actionStatus:Z},he=s.map((G,X)=>{let{route:ae}=G,W=null;if(q!=null&&X>q?W=!1:ae.lazy?W=!0:ae.loader==null?W=!1:h?W=$t(ae,i.loaderData,i.errors):fa(i.loaderData,i.matches[X],G)&&(W=!0),W!==null)return Ut(r,a,e,G,o,t,W);let me=I?!1:g||D.pathname+D.search===p.pathname+p.search||D.search!==p.search||ha(i.matches[X],G),le={...ie,defaultShouldRevalidate:me},ye=Et(G,le);return Ut(r,a,e,G,o,t,ye,le)}),Ee=[];return S.forEach((G,X)=>{if(h||!s.some(A=>A.route.id===G.routeId)||w.has(X))return;let ae=i.fetchers.get(X),W=ae&&ae.state!=="idle"&&ae.data===void 0,me=Me(P,G.path,R);if(!me){if(z&&W)return;Ee.push({key:X,routeId:G.routeId,path:G.path,matches:null,match:null,request:null,controller:null});return}if(x.has(X))return;let le=pt(me,G.path),ye=new AbortController,d=Be(n,G.path,ye.signal),E=null;if(v.has(X))v.delete(X),E=We(r,a,d,me,le,o,t);else if(W)g&&(E=We(r,a,d,me,le,o,t));else{let A={...ie,defaultShouldRevalidate:I?!1:g};Et(le,A)&&(E=We(r,a,d,me,le,o,t,A))}E&&Ee.push({key:X,routeId:G.routeId,path:G.path,matches:E,match:le,request:d,controller:ye})}),{dsMatches:he,revalidatingFetchers:Ee}}function $t(e,t,r){if(e.lazy)return!0;if(!e.loader)return!1;let a=t!=null&&e.id in t,n=r!=null&&r[e.id]!==void 0;return!a&&n?!1:typeof e.loader=="function"&&e.loader.hydrate===!0?!0:!a&&!n}function fa(e,t,r){let a=!t||r.route.id!==t.route.id,n=!e.hasOwnProperty(r.route.id);return a||n}function ha(e,t){let r=e.route.path;return e.pathname!==t.pathname||r!=null&&r.endsWith("*")&&e.params["*"]!==t.params["*"]}function Et(e,t){if(e.route.shouldRevalidate){let r=e.route.shouldRevalidate(t);if(typeof r=="boolean")return r}return t.defaultShouldRevalidate}function vr(e,t,r,a,n,i){let s;if(e){let o=a[e];B(o,`No route found to patch children into: routeId = ${e}`),o.children||(o.children=[]),s=o.children}else s=r;let c=[],l=[];if(t.forEach(o=>{let h=s.find(g=>Ir(o,g));h?l.push({existingRoute:h,newRoute:o}):c.push(o)}),c.length>0){let o=et(c,n,[e||"_","patch",String(s?.length||"0")],a);s.push(...o)}if(i&&l.length>0)for(let o=0;o<l.length;o++){let{existingRoute:h,newRoute:g}=l[o],v=h,[w]=et([g],n,[],{},!0);Object.assign(v,{element:w.element?w.element:v.element,errorElement:w.errorElement?w.errorElement:v.errorElement,hydrateFallbackElement:w.hydrateFallbackElement?w.hydrateFallbackElement:v.hydrateFallbackElement})}}function Ir(e,t){return"id"in e&&"id"in t&&e.id===t.id?!0:e.index===t.index&&e.path===t.path&&e.caseSensitive===t.caseSensitive?(!e.children||e.children.length===0)&&(!t.children||t.children.length===0)?!0:e.children.every((r,a)=>t.children?.some(n=>Ir(r,n))):!1}var gr=new WeakMap,Br=({key:e,route:t,manifest:r,mapRouteProperties:a})=>{let n=r[t.id];if(B(n,"No route found in manifest"),!n.lazy||typeof n.lazy!="object")return;let i=n.lazy[e];if(!i)return;let s=gr.get(n);s||(s={},gr.set(n,s));let c=s[e];if(c)return c;let l=(async()=>{let o=An(e),g=n[e]!==void 0&&e!=="hasErrorBoundary";if(o)ne(!o,"Route property "+e+" is not a supported lazy route property. This property will be ignored."),s[e]=Promise.resolve();else if(g)ne(!1,`Route "${n.id}" has a static property "${e}" defined. The lazy property will be ignored.`);else{let v=await i();v!=null&&(Object.assign(n,{[e]:v}),Object.assign(n,a(n)))}typeof n.lazy=="object"&&(n.lazy[e]=void 0,Object.values(n.lazy).every(v=>v===void 0)&&(n.lazy=void 0))})();return s[e]=l,l},wr=new WeakMap;function ma(e,t,r,a,n){let i=r[e.id];if(B(i,"No route found in manifest"),!e.lazy)return{lazyRoutePromise:void 0,lazyHandlerPromise:void 0};if(typeof e.lazy=="function"){let h=wr.get(i);if(h)return{lazyRoutePromise:h,lazyHandlerPromise:h};let g=(async()=>{B(typeof e.lazy=="function","No lazy route function found");let v=await e.lazy(),w={};for(let S in v){let x=v[S];if(x===void 0)continue;let P=Fn(S),z=i[S]!==void 0&&S!=="hasErrorBoundary";P?ne(!P,"Route property "+S+" is not a supported property to be returned from a lazy route function. This property will be ignored."):z?ne(!z,`Route "${i.id}" has a static property "${S}" defined but its lazy function is also returning a value for this property. The lazy route property "${S}" will be ignored.`):w[S]=x}Object.assign(i,w),Object.assign(i,{...a(i),lazy:void 0})})();return wr.set(i,g),g.catch(()=>{}),{lazyRoutePromise:g,lazyHandlerPromise:g}}let s=Object.keys(e.lazy),c=[],l;for(let h of s){if(n&&n.includes(h))continue;let g=Br({key:h,route:e,manifest:r,mapRouteProperties:a});g&&(c.push(g),h===t&&(l=g))}let o=c.length>0?Promise.all(c).then(()=>{}):void 0;return o?.catch(()=>{}),l?.catch(()=>{}),{lazyRoutePromise:o,lazyHandlerPromise:l}}async function Er(e){let t=e.matches.filter(n=>n.shouldLoad),r={};return(await Promise.all(t.map(n=>n.resolve()))).forEach((n,i)=>{r[t[i].route.id]=n}),r}async function pa(e){if(!e.matches.some(r=>r.route.unstable_middleware))return Er(e);let t=!1;return Yr(e,()=>(t=!0,Er(e)),(r,a)=>Wr(r,a,e.matches,t))}function Wr(e,t,r,a){return a?{[t]:{type:"error",result:e}}:{[Oe(r,r.find(i=>i.route.id===t||i.route.loader)?.route.id||t).route.id]:{type:"error",result:e}}}async function Yr(e,t,r){let{matches:a,request:n,params:i,context:s}=e,c=a.flatMap(o=>o.route.unstable_middleware?o.route.unstable_middleware.map(h=>[o.route.id,h]):[]),l={};return await Vr({request:n,params:i,context:s},c,t,r,l),l}async function Vr(e,t,r,a,n={},i=0){let{request:s}=e;if(s.signal.aborted)throw s.signal.reason?s.signal.reason:new Error(`Request aborted without an \`AbortSignal.reason\`: ${s.method} ${s.url}`);let c=t[i];if(!c){let v=await r();Object.assign(n,v);return}let[l,o]=c,h=!1,g=async()=>{if(h)throw new Error("You may only call `next()` once per middleware");h=!0;try{let v=await Vr(e,t,r,a,n,i+1);Object.assign(n,v)}catch(v){let w=await a(v,l);Object.assign(n,w)}};try{let v=await o({request:e.request,params:e.params,context:e.context},g);typeof v<"u"&&console.warn("client middlewares are not intended to return values, the value will be ignored",v),h||await g()}catch(v){let w=await a(v,l);Object.assign(n,w)}}function Kr(e,t,r,a,n){let i=Br({key:"unstable_middleware",route:a.route,manifest:t,mapRouteProperties:e}),s=ma(a.route,pe(r.method)?"action":"loader",t,e,n);return{middleware:i,route:s.lazyRoutePromise,handler:s.lazyHandlerPromise}}function Ut(e,t,r,a,n,i,s,c=null){let l=!1,o=Kr(e,t,r,a,n);return{...a,_lazyPromises:o,shouldLoad:s,unstable_shouldRevalidateArgs:c,unstable_shouldCallHandler(h){return l=!0,c?typeof h=="boolean"?Et(a,{...c,defaultShouldRevalidate:h}):Et(a,c):s},resolve(h){return l||s||h&&!pe(r.method)&&(a.route.lazy||a.route.loader)?va({request:r,match:a,lazyHandlerPromise:o?.handler,lazyRoutePromise:o?.route,handlerOverride:h,scopedContext:i}):Promise.resolve({type:"data",result:void 0})}}}function We(e,t,r,a,n,i,s,c=null){return a.map(l=>l.route.id!==n.route.id?{...l,shouldLoad:!1,unstable_shouldRevalidateArgs:c,unstable_shouldCallHandler:()=>!1,_lazyPromises:Kr(e,t,r,l,i),resolve:()=>Promise.resolve({type:"data",result:void 0})}:Ut(e,t,r,l,i,s,!0,c))}async function ya(e,t,r,a,n,i){r.some(o=>o._lazyPromises?.middleware)&&await Promise.all(r.map(o=>o._lazyPromises?.middleware));let s={request:t,params:r[0].params,context:n,matches:r},l=await e({...s,fetcherKey:a,unstable_runClientMiddleware:o=>{let h=s,g=!1;return Yr(h,()=>(g=!0,o({...h,fetcherKey:a,unstable_runClientMiddleware:()=>{throw new Error("Cannot call `unstable_runClientMiddleware()` from within an `unstable_runClientMiddleware` handler")}})),(v,w)=>Wr(v,w,r,g))}});try{await Promise.all(r.flatMap(o=>[o._lazyPromises?.handler,o._lazyPromises?.route]))}catch{}return l}async function va({request:e,match:t,lazyHandlerPromise:r,lazyRoutePromise:a,handlerOverride:n,scopedContext:i}){let s,c,l=pe(e.method),o=l?"action":"loader",h=g=>{let v,w=new Promise((P,R)=>v=R);c=()=>v(),e.signal.addEventListener("abort",c);let S=P=>typeof g!="function"?Promise.reject(new Error(`You cannot call the handler for a route which defines a boolean "${o}" [routeId: ${t.route.id}]`)):g({request:e,params:t.params,context:i},...P!==void 0?[P]:[]),x=(async()=>{try{return{type:"data",result:await(n?n(R=>S(R)):S())}}catch(P){return{type:"error",result:P}}})();return Promise.race([x,w])};try{let g=l?t.route.action:t.route.loader;if(r||a)if(g){let v,[w]=await Promise.all([h(g).catch(S=>{v=S}),r,a]);if(v!==void 0)throw v;s=w}else{await r;let v=l?t.route.action:t.route.loader;if(v)[s]=await Promise.all([h(v),a]);else if(o==="action"){let w=new URL(e.url),S=w.pathname+w.search;throw ge(405,{method:e.method,pathname:S,routeId:t.route.id})}else return{type:"data",result:void 0}}else if(g)s=await h(g);else{let v=new URL(e.url),w=v.pathname+v.search;throw ge(404,{pathname:w})}}catch(g){return{type:"error",result:g}}finally{c&&e.signal.removeEventListener("abort",c)}return s}async function ga(e){let{result:t,type:r}=e;if(Gr(t)){let a;try{let n=t.headers.get("Content-Type");n&&/\bapplication\/json\b/.test(n)?t.body==null?a=null:a=await t.json():a=await t.text()}catch(n){return{type:"error",error:n}}return r==="error"?{type:"error",error:new wt(t.status,t.statusText,a),statusCode:t.status,headers:t.headers}:{type:"data",data:a,statusCode:t.status,headers:t.headers}}return r==="error"?Lr(t)?t.data instanceof Error?{type:"error",error:t.data,statusCode:t.init?.status,headers:t.init?.headers?new Headers(t.init.headers):void 0}:{type:"error",error:new wt(t.init?.status||500,void 0,t.data),statusCode:tt(t)?t.status:void 0,headers:t.init?.headers?new Headers(t.init.headers):void 0}:{type:"error",error:t,statusCode:tt(t)?t.status:void 0}:Lr(t)?{type:"data",data:t.data,statusCode:t.init?.status,headers:t.init?.headers?new Headers(t.init.headers):void 0}:{type:"data",data:t}}function wa(e,t,r,a,n){let i=e.headers.get("Location");if(B(i,"Redirects returned/thrown from loaders/actions must have a Location header"),!zt(i)){let s=a.slice(0,a.findIndex(c=>c.route.id===r)+1);i=Ft(new URL(t.url),s,n,i),e.headers.set("Location",i)}return e}function Rr(e,t,r){if(zt(e)){let a=e,n=a.startsWith("//")?new URL(t.protocol+a):new URL(a),i=we(n.pathname,r)!=null;if(n.origin===t.origin&&i)return n.pathname+n.search+n.hash}return e}function Be(e,t,r,a){let n=e.createURL(qr(t)).toString(),i={signal:r};if(a&&pe(a.formMethod)){let{formMethod:s,formEncType:c}=a;i.method=s.toUpperCase(),c==="application/json"?(i.headers=new Headers({"Content-Type":c}),i.body=JSON.stringify(a.json)):c==="text/plain"?i.body=a.text:c==="application/x-www-form-urlencoded"&&a.formData?i.body=kt(a.formData):i.body=a.formData}return new Request(n,i)}function kt(e){let t=new URLSearchParams;for(let[r,a]of e.entries())t.append(r,typeof a=="string"?a:a.name);return t}function br(e){let t=new FormData;for(let[r,a]of e.entries())t.append(r,a);return t}function Ea(e,t,r,a=!1,n=!1){let i={},s=null,c,l=!1,o={},h=r&&ve(r[1])?r[1].error:void 0;return e.forEach(g=>{if(!(g.route.id in t))return;let v=g.route.id,w=t[v];if(B(!je(w),"Cannot handle redirect results in processLoaderData"),ve(w)){let S=w.error;if(h!==void 0&&(S=h,h=void 0),s=s||{},n)s[v]=S;else{let x=Oe(e,v);s[x.route.id]==null&&(s[x.route.id]=S)}a||(i[v]=zr),l||(l=!0,c=tt(w.error)?w.error.status:500),w.headers&&(o[v]=w.headers)}else i[v]=w.data,w.statusCode&&w.statusCode!==200&&!l&&(c=w.statusCode),w.headers&&(o[v]=w.headers)}),h!==void 0&&r&&(s={[r[0]]:h},r[2]&&(i[r[2]]=void 0)),{loaderData:i,errors:s,statusCode:c||200,loaderHeaders:o}}function Cr(e,t,r,a,n,i){let{loaderData:s,errors:c}=Ea(t,r,a);return n.filter(l=>!l.matches||l.matches.some(o=>o.shouldLoad)).forEach(l=>{let{key:o,match:h,controller:g}=l;if(g&&g.signal.aborted)return;let v=i[o];if(B(v,"Did not find corresponding fetcher result"),ve(v)){let w=Oe(e.matches,h?.route.id);c&&c[w.route.id]||(c={...c,[w.route.id]:v.error}),e.fetchers.delete(o)}else if(je(v))B(!1,"Unhandled fetcher revalidation redirect");else{let w=Te(v.data);e.fetchers.set(o,w)}}),{loaderData:s,errors:c}}function Sr(e,t,r,a){let n=Object.entries(t).filter(([,i])=>i!==zr).reduce((i,[s,c])=>(i[s]=c,i),{});for(let i of r){let s=i.route.id;if(!t.hasOwnProperty(s)&&e.hasOwnProperty(s)&&i.route.loader&&(n[s]=e[s]),a&&a.hasOwnProperty(s))break}return n}function Pr(e){return e?ve(e[1])?{actionData:{}}:{actionData:{[e[0]]:e[1].data}}:{}}function Oe(e,t){return(t?e.slice(0,e.findIndex(a=>a.route.id===t)+1):[...e]).reverse().find(a=>a.route.hasErrorBoundary===!0)||e[0]}function xr(e){let t=e.length===1?e[0]:e.find(r=>r.index||!r.path||r.path==="/")||{id:"__shim-error-route__"};return{matches:[{params:{},pathname:"",pathnameBase:"",route:t}],route:t}}function ge(e,{pathname:t,routeId:r,method:a,type:n,message:i}={}){let s="Unknown Server Error",c="Unknown @remix-run/router error";return e===400?(s="Bad Request",a&&t&&r?c=`You made a ${a} request to "${t}" but did not provide a \`loader\` for route "${r}", so there is no way to handle the request.`:n==="invalid-body"&&(c="Unable to encode submission body")):e===403?(s="Forbidden",c=`Route "${r}" does not match URL "${t}"`):e===404?(s="Not Found",c=`No route matches URL "${t}"`):e===405&&(s="Method Not Allowed",a&&t&&r?c=`You made a ${a.toUpperCase()} request to "${t}" but did not provide an \`action\` for route "${r}", so there is no way to handle the request.`:a&&(c=`Invalid request method "${a.toUpperCase()}"`)),new wt(e||500,s,new Error(c),!0)}function ft(e){let t=Object.entries(e);for(let r=t.length-1;r>=0;r--){let[a,n]=t[r];if(je(n))return{key:a,result:n}}}function qr(e){let t=typeof e=="string"?Ne(e):e;return Ae({...t,hash:""})}function Ra(e,t){return e.pathname!==t.pathname||e.search!==t.search?!1:e.hash===""?t.hash!=="":e.hash===t.hash?!0:t.hash!==""}function ba(e){return Gr(e.result)&&oa.has(e.result.status)}function ve(e){return e.type==="error"}function je(e){return(e&&e.type)==="redirect"}function Lr(e){return typeof e=="object"&&e!=null&&"type"in e&&"data"in e&&"init"in e&&e.type==="DataWithResponseInit"}function Gr(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.headers=="object"&&typeof e.body<"u"}function Ca(e){return aa.has(e.toUpperCase())}function pe(e){return ra.has(e.toUpperCase())}function It(e){return new URLSearchParams(e).getAll("index").some(t=>t==="")}function pt(e,t){let r=typeof t=="string"?Ne(t).search:t.search;if(e[e.length-1].route.index&&It(r||""))return e[e.length-1];let a=kr(e);return a[a.length-1]}function _r(e){let{formMethod:t,formAction:r,formEncType:a,text:n,formData:i,json:s}=e;if(!(!t||!r||!a)){if(n!=null)return{formMethod:t,formAction:r,formEncType:a,formData:void 0,json:void 0,text:n};if(i!=null)return{formMethod:t,formAction:r,formEncType:a,formData:i,json:void 0,text:void 0};if(s!==void 0)return{formMethod:t,formAction:r,formEncType:a,formData:void 0,json:s,text:void 0}}}function At(e,t){return t?{state:"loading",location:e,formMethod:t.formMethod,formAction:t.formAction,formEncType:t.formEncType,formData:t.formData,json:t.json,text:t.text}:{state:"loading",location:e,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0}}function Sa(e,t){return{state:"submitting",location:e,formMethod:t.formMethod,formAction:t.formAction,formEncType:t.formEncType,formData:t.formData,json:t.json,text:t.text}}function Je(e,t){return e?{state:"loading",formMethod:e.formMethod,formAction:e.formAction,formEncType:e.formEncType,formData:e.formData,json:e.json,text:e.text,data:t}:{state:"loading",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:t}}function Pa(e,t){return{state:"submitting",formMethod:e.formMethod,formAction:e.formAction,formEncType:e.formEncType,formData:e.formData,json:e.json,text:e.text,data:t?t.data:void 0}}function Te(e){return{state:"idle",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:e}}function xa(e,t){try{let r=e.sessionStorage.getItem(Hr);if(r){let a=JSON.parse(r);for(let[n,i]of Object.entries(a||{}))i&&Array.isArray(i)&&t.set(n,new Set(i||[]))}}catch{}}function La(e,t){if(t.size>0){let r={};for(let[a,n]of t)r[a]=[...n];try{e.sessionStorage.setItem(Hr,JSON.stringify(r))}catch(a){ne(!1,`Failed to save applied view transitions in sessionStorage (${a}).`)}}}function _a(){let e,t,r=new Promise((a,n)=>{e=async i=>{a(i);try{await r}catch{}},t=async i=>{n(i);try{await r}catch{}}});return{promise:r,resolve:e,reject:t}}var He=y.createContext(null);He.displayName="DataRouter";var rt=y.createContext(null);rt.displayName="DataRouterState";y.createContext(!1);var Bt=y.createContext({isTransitioning:!1});Bt.displayName="ViewTransition";var Jr=y.createContext(new Map);Jr.displayName="Fetchers";var Da=y.createContext(null);Da.displayName="Await";var Se=y.createContext(null);Se.displayName="Navigation";var Rt=y.createContext(null);Rt.displayName="Location";var xe=y.createContext({outlet:null,matches:[],isDataRoute:!1});xe.displayName="Route";var Wt=y.createContext(null);Wt.displayName="RouteError";function Ta(e,{relative:t}={}){B(nt(),"useHref() may be used only in the context of a <Router> component.");let{basename:r,navigator:a}=y.useContext(Se),{hash:n,pathname:i,search:s}=at(e,{relative:t}),c=i;return r!=="/"&&(c=i==="/"?r:Ce([r,i])),a.createHref({pathname:c,search:s,hash:n})}function nt(){return y.useContext(Rt)!=null}function ze(){return B(nt(),"useLocation() may be used only in the context of a <Router> component."),y.useContext(Rt).location}var Xr="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function Qr(e){y.useContext(Se).static||y.useLayoutEffect(e)}function Ma(){let{isDataRoute:e}=y.useContext(xe);return e?Wa():Oa()}function Oa(){B(nt(),"useNavigate() may be used only in the context of a <Router> component.");let e=y.useContext(He),{basename:t,navigator:r}=y.useContext(Se),{matches:a}=y.useContext(xe),{pathname:n}=ze(),i=JSON.stringify(jt(a)),s=y.useRef(!1);return Qr(()=>{s.current=!0}),y.useCallback((l,o={})=>{if(ne(s.current,Xr),!s.current)return;if(typeof l=="number"){r.go(l);return}let h=Ht(l,JSON.parse(i),n,o.relative==="path");e==null&&t!=="/"&&(h.pathname=h.pathname==="/"?t:Ce([t,h.pathname])),(o.replace?r.replace:r.push)(h,o.state,o)},[t,r,i,n,e])}y.createContext(null);function at(e,{relative:t}={}){let{matches:r}=y.useContext(xe),{pathname:a}=ze(),n=JSON.stringify(jt(r));return y.useMemo(()=>Ht(e,JSON.parse(n),a,t==="path"),[e,n,a,t])}function Aa(e,t,r,a){B(nt(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:n}=y.useContext(Se),{matches:i}=y.useContext(xe),s=i[i.length-1],c=s?s.params:{},l=s?s.pathname:"/",o=s?s.pathnameBase:"/",h=s&&s.route;{let R=h&&h.path||"";Zr(l,!h||R.endsWith("*")||R.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${l}" (under <Route path="${R}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${R}"> to <Route path="${R==="/"?"*":`${R}/*`}">.`)}let g=ze(),v;v=g;let w=v.pathname||"/",S=w;if(o!=="/"){let R=o.replace(/^\//,"").split("/");S="/"+w.replace(/^\//,"").split("/").slice(R.length).join("/")}let x=Me(e,{pathname:S});return ne(h||x!=null,`No routes matched location "${v.pathname}${v.search}${v.hash}" `),ne(x==null||x[x.length-1].route.element!==void 0||x[x.length-1].route.Component!==void 0||x[x.length-1].route.lazy!==void 0,`Matched leaf route at location "${v.pathname}${v.search}${v.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`),ka(x&&x.map(R=>Object.assign({},R,{params:Object.assign({},c,R.params),pathname:Ce([o,n.encodeLocation?n.encodeLocation(R.pathname).pathname:R.pathname]),pathnameBase:R.pathnameBase==="/"?o:Ce([o,n.encodeLocation?n.encodeLocation(R.pathnameBase).pathname:R.pathnameBase])})),i,r,a)}function Na(){let e=Ba(),t=tt(e)?`${e.status} ${e.statusText}`:e instanceof Error?e.message:JSON.stringify(e),r=e instanceof Error?e.stack:null,a="rgba(200,200,200, 0.5)",n={padding:"0.5rem",backgroundColor:a},i={padding:"2px 4px",backgroundColor:a},s=null;return console.error("Error handled by React Router default ErrorBoundary:",e),s=y.createElement(y.Fragment,null,y.createElement("p",null,"💿 Hey developer 👋"),y.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",y.createElement("code",{style:i},"ErrorBoundary")," or"," ",y.createElement("code",{style:i},"errorElement")," prop on your route.")),y.createElement(y.Fragment,null,y.createElement("h2",null,"Unexpected Application Error!"),y.createElement("h3",{style:{fontStyle:"italic"}},t),r?y.createElement("pre",{style:n},r):null,s)}var Fa=y.createElement(Na,null),$a=class extends y.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||t.revalidation!=="idle"&&e.revalidation==="idle"?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:e.error!==void 0?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return this.state.error!==void 0?y.createElement(xe.Provider,{value:this.props.routeContext},y.createElement(Wt.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function Ua({routeContext:e,match:t,children:r}){let a=y.useContext(He);return a&&a.static&&a.staticContext&&(t.route.errorElement||t.route.ErrorBoundary)&&(a.staticContext._deepestRenderedBoundaryId=t.route.id),y.createElement(xe.Provider,{value:e},r)}function ka(e,t=[],r=null,a=null){if(e==null){if(!r)return null;if(r.errors)e=r.matches;else if(t.length===0&&!r.initialized&&r.matches.length>0)e=r.matches;else return null}let n=e,i=r?.errors;if(i!=null){let l=n.findIndex(o=>o.route.id&&i?.[o.route.id]!==void 0);B(l>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(i).join(",")}`),n=n.slice(0,Math.min(n.length,l+1))}let s=!1,c=-1;if(r)for(let l=0;l<n.length;l++){let o=n[l];if((o.route.HydrateFallback||o.route.hydrateFallbackElement)&&(c=l),o.route.id){let{loaderData:h,errors:g}=r,v=o.route.loader&&!h.hasOwnProperty(o.route.id)&&(!g||g[o.route.id]===void 0);if(o.route.lazy||v){s=!0,c>=0?n=n.slice(0,c+1):n=[n[0]];break}}}return n.reduceRight((l,o,h)=>{let g,v=!1,w=null,S=null;r&&(g=i&&o.route.id?i[o.route.id]:void 0,w=o.route.errorElement||Fa,s&&(c<0&&h===0?(Zr("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),v=!0,S=null):c===h&&(v=!0,S=o.route.hydrateFallbackElement||null)));let x=t.concat(n.slice(0,h+1)),P=()=>{let R;return g?R=w:v?R=S:o.route.Component?R=y.createElement(o.route.Component,null):o.route.element?R=o.route.element:R=l,y.createElement(Ua,{match:o,routeContext:{outlet:l,matches:x,isDataRoute:r!=null},children:R})};return r&&(o.route.ErrorBoundary||o.route.errorElement||h===0)?y.createElement($a,{location:r.location,revalidation:r.revalidation,component:w,error:g,children:P(),routeContext:{outlet:null,matches:x,isDataRoute:!0}}):P()},null)}function Yt(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function ja(e){let t=y.useContext(He);return B(t,Yt(e)),t}function Ha(e){let t=y.useContext(rt);return B(t,Yt(e)),t}function za(e){let t=y.useContext(xe);return B(t,Yt(e)),t}function Vt(e){let t=za(e),r=t.matches[t.matches.length-1];return B(r.route.id,`${e} can only be used on routes that contain a unique "id"`),r.route.id}function Ia(){return Vt("useRouteId")}function Ba(){let e=y.useContext(Wt),t=Ha("useRouteError"),r=Vt("useRouteError");return e!==void 0?e:t.errors?.[r]}function Wa(){let{router:e}=ja("useNavigate"),t=Vt("useNavigate"),r=y.useRef(!1);return Qr(()=>{r.current=!0}),y.useCallback(async(n,i={})=>{ne(r.current,Xr),r.current&&(typeof n=="number"?e.navigate(n):await e.navigate(n,{fromRouteId:t,...i}))},[e,t])}var Dr={};function Zr(e,t,r){!t&&!Dr[e]&&(Dr[e]=!0,ne(!1,r))}var Tr={};function Mr(e,t){!e&&!Tr[t]&&(Tr[t]=!0,console.warn(t))}function Ya(e){let t={hasErrorBoundary:e.hasErrorBoundary||e.ErrorBoundary!=null||e.errorElement!=null};return e.Component&&(e.element&&ne(!1,"You should not include both `Component` and `element` on your route - `Component` will be used."),Object.assign(t,{element:y.createElement(e.Component),Component:void 0})),e.HydrateFallback&&(e.hydrateFallbackElement&&ne(!1,"You should not include both `HydrateFallback` and `hydrateFallbackElement` on your route - `HydrateFallback` will be used."),Object.assign(t,{hydrateFallbackElement:y.createElement(e.HydrateFallback),HydrateFallback:void 0})),e.ErrorBoundary&&(e.errorElement&&ne(!1,"You should not include both `ErrorBoundary` and `errorElement` on your route - `ErrorBoundary` will be used."),Object.assign(t,{errorElement:y.createElement(e.ErrorBoundary),ErrorBoundary:void 0})),t}var Va=["HydrateFallback","hydrateFallbackElement"],Ka=class{constructor(){this.status="pending",this.promise=new Promise((e,t)=>{this.resolve=r=>{this.status==="pending"&&(this.status="resolved",e(r))},this.reject=r=>{this.status==="pending"&&(this.status="rejected",t(r))}})}};function qa({router:e,flushSync:t}){let[r,a]=y.useState(e.state),[n,i]=y.useState(),[s,c]=y.useState({isTransitioning:!1}),[l,o]=y.useState(),[h,g]=y.useState(),[v,w]=y.useState(),S=y.useRef(new Map),x=y.useCallback((_,{deletedFetchers:V,flushSync:D,viewTransitionOpts:p})=>{_.fetchers.forEach((Z,I)=>{Z.data!==void 0&&S.current.set(I,Z.data)}),V.forEach(Z=>S.current.delete(Z)),Mr(D===!1||t!=null,'You provided the `flushSync` option to a router update, but you are not using the `<RouterProvider>` from `react-router/dom` so `ReactDOM.flushSync()` is unavailable.  Please update your app to `import { RouterProvider } from "react-router/dom"` and ensure you have `react-dom` installed as a dependency to use the `flushSync` option.');let q=e.window!=null&&e.window.document!=null&&typeof e.window.document.startViewTransition=="function";if(Mr(p==null||q,"You provided the `viewTransition` option to a router update, but you do not appear to be running in a DOM environment as `window.startViewTransition` is not available."),!p||!q){t&&D?t(()=>a(_)):y.startTransition(()=>a(_));return}if(t&&D){t(()=>{h&&(l&&l.resolve(),h.skipTransition()),c({isTransitioning:!0,flushSync:!0,currentLocation:p.currentLocation,nextLocation:p.nextLocation})});let Z=e.window.document.startViewTransition(()=>{t(()=>a(_))});Z.finished.finally(()=>{t(()=>{o(void 0),g(void 0),i(void 0),c({isTransitioning:!1})})}),t(()=>g(Z));return}h?(l&&l.resolve(),h.skipTransition(),w({state:_,currentLocation:p.currentLocation,nextLocation:p.nextLocation})):(i(_),c({isTransitioning:!0,flushSync:!1,currentLocation:p.currentLocation,nextLocation:p.nextLocation}))},[e.window,t,h,l]);y.useLayoutEffect(()=>e.subscribe(x),[e,x]),y.useEffect(()=>{s.isTransitioning&&!s.flushSync&&o(new Ka)},[s]),y.useEffect(()=>{if(l&&n&&e.window){let _=n,V=l.promise,D=e.window.document.startViewTransition(async()=>{y.startTransition(()=>a(_)),await V});D.finished.finally(()=>{o(void 0),g(void 0),i(void 0),c({isTransitioning:!1})}),g(D)}},[n,l,e.window]),y.useEffect(()=>{l&&n&&r.location.key===n.location.key&&l.resolve()},[l,h,r.location,n]),y.useEffect(()=>{!s.isTransitioning&&v&&(i(v.state),c({isTransitioning:!0,flushSync:!1,currentLocation:v.currentLocation,nextLocation:v.nextLocation}),w(void 0))},[s.isTransitioning,v]);let P=y.useMemo(()=>({createHref:e.createHref,encodeLocation:e.encodeLocation,go:_=>e.navigate(_),push:(_,V,D)=>e.navigate(_,{state:V,preventScrollReset:D?.preventScrollReset}),replace:(_,V,D)=>e.navigate(_,{replace:!0,state:V,preventScrollReset:D?.preventScrollReset})}),[e]),R=e.basename||"/",z=y.useMemo(()=>({router:e,navigator:P,static:!1,basename:R}),[e,P,R]);return y.createElement(y.Fragment,null,y.createElement(He.Provider,{value:z},y.createElement(rt.Provider,{value:r},y.createElement(Jr.Provider,{value:S.current},y.createElement(Bt.Provider,{value:s},y.createElement(Xa,{basename:R,location:r.location,navigationType:r.historyAction,navigator:P},y.createElement(Ga,{routes:e.routes,future:e.future,state:r})))))),null)}var Ga=y.memo(Ja);function Ja({routes:e,future:t,state:r}){return Aa(e,void 0,r,t)}function Xa({basename:e="/",children:t=null,location:r,navigationType:a="POP",navigator:n,static:i=!1}){B(!nt(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let s=e.replace(/^\/*/,"/"),c=y.useMemo(()=>({basename:s,navigator:n,static:i,future:{}}),[s,n,i]);typeof r=="string"&&(r=Ne(r));let{pathname:l="/",search:o="",hash:h="",state:g=null,key:v="default"}=r,w=y.useMemo(()=>{let S=we(l,s);return S==null?null:{location:{pathname:S,search:o,hash:h,state:g,key:v},navigationType:a}},[s,l,o,h,g,v,a]);return ne(w!=null,`<Router basename="${s}"> is not able to match the URL "${l}${o}${h}" because it does not start with the basename, so the <Router> won't render anything.`),w==null?null:y.createElement(Se.Provider,{value:c},y.createElement(Rt.Provider,{children:t,value:w}))}var yt="get",vt="application/x-www-form-urlencoded";function bt(e){return e!=null&&typeof e.tagName=="string"}function Qa(e){return bt(e)&&e.tagName.toLowerCase()==="button"}function Za(e){return bt(e)&&e.tagName.toLowerCase()==="form"}function eo(e){return bt(e)&&e.tagName.toLowerCase()==="input"}function to(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function ro(e,t){return e.button===0&&(!t||t==="_self")&&!to(e)}var ht=null;function no(){if(ht===null)try{new FormData(document.createElement("form"),0),ht=!1}catch{ht=!0}return ht}var ao=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function Nt(e){return e!=null&&!ao.has(e)?(ne(!1,`"${e}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${vt}"`),null):e}function oo(e,t){let r,a,n,i,s;if(Za(e)){let c=e.getAttribute("action");a=c?we(c,t):null,r=e.getAttribute("method")||yt,n=Nt(e.getAttribute("enctype"))||vt,i=new FormData(e)}else if(Qa(e)||eo(e)&&(e.type==="submit"||e.type==="image")){let c=e.form;if(c==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let l=e.getAttribute("formaction")||c.getAttribute("action");if(a=l?we(l,t):null,r=e.getAttribute("formmethod")||c.getAttribute("method")||yt,n=Nt(e.getAttribute("formenctype"))||Nt(c.getAttribute("enctype"))||vt,i=new FormData(c,e),!no()){let{name:o,type:h,value:g}=e;if(h==="image"){let v=o?`${o}.`:"";i.append(`${v}x`,"0"),i.append(`${v}y`,"0")}else o&&i.append(o,g)}}else{if(bt(e))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');r=yt,a=null,n=vt,s=e}return i&&n==="text/plain"&&(s=i,i=void 0),{action:a,method:r.toLowerCase(),encType:n,formData:i,body:s}}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");function Kt(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function io(e,t,r){let a=typeof e=="string"?new URL(e,typeof window>"u"?"server://singlefetch/":window.location.origin):e;return a.pathname==="/"?a.pathname=`_root.${r}`:t&&we(a.pathname,t)==="/"?a.pathname=`${t.replace(/\/$/,"")}/_root.${r}`:a.pathname=`${a.pathname.replace(/\/$/,"")}.${r}`,a}async function lo(e,t){if(e.id in t)return t[e.id];try{let r=await import(e.module);return t[e.id]=r,r}catch(r){return console.error(`Error loading route module \`${e.module}\`, reloading page...`),console.error(r),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function so(e){return e==null?!1:e.href==null?e.rel==="preload"&&typeof e.imageSrcSet=="string"&&typeof e.imageSizes=="string":typeof e.rel=="string"&&typeof e.href=="string"}async function uo(e,t,r){let a=await Promise.all(e.map(async n=>{let i=t.routes[n.route.id];if(i){let s=await lo(i,r);return s.links?s.links():[]}return[]}));return mo(a.flat(1).filter(so).filter(n=>n.rel==="stylesheet"||n.rel==="preload").map(n=>n.rel==="stylesheet"?{...n,rel:"prefetch",as:"style"}:{...n,rel:"prefetch"}))}function Or(e,t,r,a,n,i){let s=(l,o)=>r[o]?l.route.id!==r[o].route.id:!0,c=(l,o)=>r[o].pathname!==l.pathname||r[o].route.path?.endsWith("*")&&r[o].params["*"]!==l.params["*"];return i==="assets"?t.filter((l,o)=>s(l,o)||c(l,o)):i==="data"?t.filter((l,o)=>{let h=a.routes[l.route.id];if(!h||!h.hasLoader)return!1;if(s(l,o)||c(l,o))return!0;if(l.route.shouldRevalidate){let g=l.route.shouldRevalidate({currentUrl:new URL(n.pathname+n.search+n.hash,window.origin),currentParams:r[0]?.params||{},nextUrl:new URL(e,window.origin),nextParams:l.params,defaultShouldRevalidate:!0});if(typeof g=="boolean")return g}return!0}):[]}function co(e,t,{includeHydrateFallback:r}={}){return fo(e.map(a=>{let n=t.routes[a.route.id];if(!n)return[];let i=[n.module];return n.clientActionModule&&(i=i.concat(n.clientActionModule)),n.clientLoaderModule&&(i=i.concat(n.clientLoaderModule)),r&&n.hydrateFallbackModule&&(i=i.concat(n.hydrateFallbackModule)),n.imports&&(i=i.concat(n.imports)),i}).flat(1))}function fo(e){return[...new Set(e)]}function ho(e){let t={},r=Object.keys(e).sort();for(let a of r)t[a]=e[a];return t}function mo(e,t){let r=new Set;return new Set(t),e.reduce((a,n)=>{let i=JSON.stringify(ho(n));return r.has(i)||(r.add(i),a.push({key:i,link:n})),a},[])}function en(){let e=y.useContext(He);return Kt(e,"You must render this element inside a <DataRouterContext.Provider> element"),e}function po(){let e=y.useContext(rt);return Kt(e,"You must render this element inside a <DataRouterStateContext.Provider> element"),e}var qt=y.createContext(void 0);qt.displayName="FrameworkContext";function tn(){let e=y.useContext(qt);return Kt(e,"You must render this element inside a <HydratedRouter> element"),e}function yo(e,t){let r=y.useContext(qt),[a,n]=y.useState(!1),[i,s]=y.useState(!1),{onFocus:c,onBlur:l,onMouseEnter:o,onMouseLeave:h,onTouchStart:g}=t,v=y.useRef(null);y.useEffect(()=>{if(e==="render"&&s(!0),e==="viewport"){let x=R=>{R.forEach(z=>{s(z.isIntersecting)})},P=new IntersectionObserver(x,{threshold:.5});return v.current&&P.observe(v.current),()=>{P.disconnect()}}},[e]),y.useEffect(()=>{if(a){let x=setTimeout(()=>{s(!0)},100);return()=>{clearTimeout(x)}}},[a]);let w=()=>{n(!0)},S=()=>{n(!1),s(!1)};return r?e!=="intent"?[i,v,{}]:[i,v,{onFocus:Xe(c,w),onBlur:Xe(l,S),onMouseEnter:Xe(o,w),onMouseLeave:Xe(h,S),onTouchStart:Xe(g,w)}]:[!1,v,{}]}function Xe(e,t){return r=>{e&&e(r),r.defaultPrevented||t(r)}}function vo({page:e,...t}){let{router:r}=en(),a=y.useMemo(()=>Me(r.routes,e,r.basename),[r.routes,e,r.basename]);return a?y.createElement(wo,{page:e,matches:a,...t}):null}function go(e){let{manifest:t,routeModules:r}=tn(),[a,n]=y.useState([]);return y.useEffect(()=>{let i=!1;return uo(e,t,r).then(s=>{i||n(s)}),()=>{i=!0}},[e,t,r]),a}function wo({page:e,matches:t,...r}){let a=ze(),{manifest:n,routeModules:i}=tn(),{basename:s}=en(),{loaderData:c,matches:l}=po(),o=y.useMemo(()=>Or(e,t,l,n,a,"data"),[e,t,l,n,a]),h=y.useMemo(()=>Or(e,t,l,n,a,"assets"),[e,t,l,n,a]),g=y.useMemo(()=>{if(e===a.pathname+a.search+a.hash)return[];let S=new Set,x=!1;if(t.forEach(R=>{let z=n.routes[R.route.id];!z||!z.hasLoader||(!o.some(_=>_.route.id===R.route.id)&&R.route.id in c&&i[R.route.id]?.shouldRevalidate||z.hasClientLoader?x=!0:S.add(R.route.id))}),S.size===0)return[];let P=io(e,s,"data");return x&&S.size>0&&P.searchParams.set("_routes",t.filter(R=>S.has(R.route.id)).map(R=>R.route.id).join(",")),[P.pathname+P.search]},[s,c,a,n,o,t,e,i]),v=y.useMemo(()=>co(h,n),[h,n]),w=go(h);return y.createElement(y.Fragment,null,g.map(S=>y.createElement("link",{key:S,rel:"prefetch",as:"fetch",href:S,...r})),v.map(S=>y.createElement("link",{key:S,rel:"modulepreload",href:S,...r})),w.map(({key:S,link:x})=>y.createElement("link",{key:S,nonce:r.nonce,...x})))}function Eo(...e){return t=>{e.forEach(r=>{typeof r=="function"?r(t):r!=null&&(r.current=t)})}}var rn=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";try{rn&&(window.__reactRouterVersion="7.8.0")}catch{}function No(e,t){return ca({basename:t?.basename,unstable_getContext:t?.unstable_getContext,future:t?.future,history:Dn({window:t?.window}),hydrationData:Ro(),routes:e,mapRouteProperties:Ya,hydrationRouteProperties:Va,dataStrategy:t?.dataStrategy,patchRoutesOnNavigation:t?.patchRoutesOnNavigation,window:t?.window}).initialize()}function Ro(){let e=window?.__staticRouterHydrationData;return e&&e.errors&&(e={...e,errors:bo(e.errors)}),e}function bo(e){if(!e)return null;let t=Object.entries(e),r={};for(let[a,n]of t)if(n&&n.__type==="RouteErrorResponse")r[a]=new wt(n.status,n.statusText,n.data,n.internal===!0);else if(n&&n.__type==="Error"){if(n.__subType){let i=window[n.__subType];if(typeof i=="function")try{let s=new i(n.message);s.stack="",r[a]=s}catch{}}if(r[a]==null){let i=new Error(n.message);i.stack="",r[a]=i}}else r[a]=n;return r}var nn=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,an=y.forwardRef(function({onClick:t,discover:r="render",prefetch:a="none",relative:n,reloadDocument:i,replace:s,state:c,target:l,to:o,preventScrollReset:h,viewTransition:g,...v},w){let{basename:S}=y.useContext(Se),x=typeof o=="string"&&nn.test(o),P,R=!1;if(typeof o=="string"&&x&&(P=o,rn))try{let I=new URL(window.location.href),ie=o.startsWith("//")?new URL(I.protocol+o):new URL(o),he=we(ie.pathname,S);ie.origin===I.origin&&he!=null?o=he+ie.search+ie.hash:R=!0}catch{ne(!1,`<Link to="${o}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let z=Ta(o,{relative:n}),[_,V,D]=yo(a,v),p=xo(o,{replace:s,state:c,target:l,preventScrollReset:h,relative:n,viewTransition:g});function q(I){t&&t(I),I.defaultPrevented||p(I)}let Z=y.createElement("a",{...v,...D,href:P||z,onClick:R||i?t:q,ref:Eo(w,V),target:l,"data-discover":!x&&r==="render"?"true":void 0});return _&&!x?y.createElement(y.Fragment,null,Z,y.createElement(vo,{page:z})):Z});an.displayName="Link";var Co=y.forwardRef(function({"aria-current":t="page",caseSensitive:r=!1,className:a="",end:n=!1,style:i,to:s,viewTransition:c,children:l,...o},h){let g=at(s,{relative:o.relative}),v=ze(),w=y.useContext(rt),{navigator:S,basename:x}=y.useContext(Se),P=w!=null&&Mo(g)&&c===!0,R=S.encodeLocation?S.encodeLocation(g).pathname:g.pathname,z=v.pathname,_=w&&w.navigation&&w.navigation.location?w.navigation.location.pathname:null;r||(z=z.toLowerCase(),_=_?_.toLowerCase():null,R=R.toLowerCase()),_&&x&&(_=we(_,x)||_);const V=R!=="/"&&R.endsWith("/")?R.length-1:R.length;let D=z===R||!n&&z.startsWith(R)&&z.charAt(V)==="/",p=_!=null&&(_===R||!n&&_.startsWith(R)&&_.charAt(R.length)==="/"),q={isActive:D,isPending:p,isTransitioning:P},Z=D?t:void 0,I;typeof a=="function"?I=a(q):I=[a,D?"active":null,p?"pending":null,P?"transitioning":null].filter(Boolean).join(" ");let ie=typeof i=="function"?i(q):i;return y.createElement(an,{...o,"aria-current":Z,className:I,ref:h,style:ie,to:s,viewTransition:c},typeof l=="function"?l(q):l)});Co.displayName="NavLink";var So=y.forwardRef(({discover:e="render",fetcherKey:t,navigate:r,reloadDocument:a,replace:n,state:i,method:s=yt,action:c,onSubmit:l,relative:o,preventScrollReset:h,viewTransition:g,...v},w)=>{let S=Do(),x=To(c,{relative:o}),P=s.toLowerCase()==="get"?"get":"post",R=typeof c=="string"&&nn.test(c),z=_=>{if(l&&l(_),_.defaultPrevented)return;_.preventDefault();let V=_.nativeEvent.submitter,D=V?.getAttribute("formmethod")||s;S(V||_.currentTarget,{fetcherKey:t,method:D,navigate:r,replace:n,state:i,relative:o,preventScrollReset:h,viewTransition:g})};return y.createElement("form",{ref:w,method:P,action:x,onSubmit:a?l:z,...v,"data-discover":!R&&e==="render"?"true":void 0})});So.displayName="Form";function Po(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function on(e){let t=y.useContext(He);return B(t,Po(e)),t}function xo(e,{target:t,replace:r,state:a,preventScrollReset:n,relative:i,viewTransition:s}={}){let c=Ma(),l=ze(),o=at(e,{relative:i});return y.useCallback(h=>{if(ro(h,t)){h.preventDefault();let g=r!==void 0?r:Ae(l)===Ae(o);c(e,{replace:g,state:a,preventScrollReset:n,relative:i,viewTransition:s})}},[l,c,o,r,a,t,e,n,i,s])}var Lo=0,_o=()=>`__${String(++Lo)}__`;function Do(){let{router:e}=on("useSubmit"),{basename:t}=y.useContext(Se),r=Ia();return y.useCallback(async(a,n={})=>{let{action:i,method:s,encType:c,formData:l,body:o}=oo(a,t);if(n.navigate===!1){let h=n.fetcherKey||_o();await e.fetch(h,r,n.action||i,{preventScrollReset:n.preventScrollReset,formData:l,body:o,formMethod:n.method||s,formEncType:n.encType||c,flushSync:n.flushSync})}else await e.navigate(n.action||i,{preventScrollReset:n.preventScrollReset,formData:l,body:o,formMethod:n.method||s,formEncType:n.encType||c,replace:n.replace,state:n.state,fromRouteId:r,flushSync:n.flushSync,viewTransition:n.viewTransition})},[e,t,r])}function To(e,{relative:t}={}){let{basename:r}=y.useContext(Se),a=y.useContext(xe);B(a,"useFormAction must be used inside a RouteContext");let[n]=a.matches.slice(-1),i={...at(e||".",{relative:t})},s=ze();if(e==null){i.search=s.search;let c=new URLSearchParams(i.search),l=c.getAll("index");if(l.some(h=>h==="")){c.delete("index"),l.filter(g=>g).forEach(g=>c.append("index",g));let h=c.toString();i.search=h?`?${h}`:""}}return(!e||e===".")&&n.route.index&&(i.search=i.search?i.search.replace(/^\?/,"?index&"):"?index"),r!=="/"&&(i.pathname=i.pathname==="/"?r:Ce([r,i.pathname])),Ae(i)}function Mo(e,{relative:t}={}){let r=y.useContext(Bt);B(r!=null,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:a}=on("useViewTransitionState"),n=at(e,{relative:t});if(!r.isTransitioning)return!1;let i=we(r.currentLocation.pathname,a)||r.currentLocation.pathname,s=we(r.nextLocation.pathname,a)||r.nextLocation.pathname;return gt(n.pathname,s)!=null||gt(n.pathname,i)!=null}var Oo=xn();function Fo(e){return y.createElement(qa,{flushSync:Oo.flushSync,...e})}export{an as L,Sn as R,xn as a,y as b,Ao as c,No as d,Fo as e,bn as g,Ar as r};
