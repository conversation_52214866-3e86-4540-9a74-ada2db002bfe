<UserControl x:Class="LIVAnalyzer.UI.Controls.ValidatedTextBox"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:controls="clr-namespace:LIVAnalyzer.UI.Controls"
             mc:Ignorable="d" 
             d:DesignHeight="60" d:DesignWidth="200">
    
    <UserControl.Resources>
        <!-- 导入转换器 -->
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="/LIVAnalyzer.UI;component/Resources/Converters.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
        
        <!-- 默认TextBox样式 -->
        <Style x:Key="ValidatedTextBoxStyle" TargetType="TextBox">
            <Setter Property="Padding" Value="8,6"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="{DynamicResource OutlineBrush}"/>
            <Setter Property="Background" Value="{DynamicResource SurfaceBrush}"/>
            <Setter Property="Foreground" Value="{DynamicResource OnSurfaceBrush}"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Name="Border" 
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4">
                            <ScrollViewer x:Name="PART_ContentHost" 
                                        Margin="{TemplateBinding Padding}"
                                        VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <!-- 聚焦状态 -->
                            <Trigger Property="IsFocused" Value="True">
                                <Setter TargetName="Border" Property="BorderBrush" Value="{DynamicResource PrimaryBrush}"/>
                                <Setter TargetName="Border" Property="BorderThickness" Value="2"/>
                            </Trigger>
                            <!-- 鼠标悬停状态 -->
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="Border" Property="BorderBrush" Value="{DynamicResource PrimaryLightBrush}"/>
                            </Trigger>
                            <!-- 禁用状态 -->
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter TargetName="Border" Property="Background" Value="{DynamicResource SurfaceVariantBrush}"/>
                                <Setter TargetName="Border" Property="BorderBrush" Value="{DynamicResource OutlineVariantBrush}"/>
                                <Setter Property="Foreground" Value="{DynamicResource OnSurfaceDisabledBrush}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 错误状态TextBox样式 -->
        <Style x:Key="ErrorTextBoxStyle" TargetType="TextBox" BasedOn="{StaticResource ValidatedTextBoxStyle}">
            <Setter Property="BorderBrush" Value="{DynamicResource ErrorBrush}"/>
            <Setter Property="BorderThickness" Value="2"/>
            <Style.Triggers>
                <Trigger Property="IsFocused" Value="True">
                    <Setter Property="BorderBrush" Value="{DynamicResource ErrorBrush}"/>
                    <Setter Property="BorderThickness" Value="2"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- 标签样式 -->
        <Style x:Key="LabelStyle" TargetType="TextBlock">
            <Setter Property="Foreground" Value="{DynamicResource OnSurfaceVariantBrush}"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Margin" Value="0,0,0,4"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>

        <!-- 必填标记样式 -->
        <Style x:Key="RequiredMarkerStyle" TargetType="TextBlock">
            <Setter Property="Foreground" Value="{DynamicResource ErrorBrush}"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Margin" Value="4,0,0,0"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>

        <!-- 单位标签样式 -->
        <Style x:Key="UnitLabelStyle" TargetType="TextBlock">
            <Setter Property="Foreground" Value="{DynamicResource OnSurfaceVariantBrush}"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Margin" Value="6,0,0,0"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>
    </UserControl.Resources>

    <!-- 主容器 -->
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标签行 -->
        <Grid Grid.Row="0" 
              Visibility="{Binding ElementName=ValidatedTextBoxControl, Path=ShowLabel, Converter={StaticResource BooleanToVisibilityConverter}}">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <StackPanel Grid.Column="0" Orientation="Horizontal">
                <!-- 标签文本 -->
                <TextBlock x:Name="LabelText" 
                           Text="{Binding ElementName=ValidatedTextBoxControl, Path=Label}"
                           Style="{StaticResource LabelStyle}"/>
                
                <!-- 必填标记 -->
                <TextBlock Text="*" 
                           Style="{StaticResource RequiredMarkerStyle}"
                           Visibility="{Binding ElementName=ValidatedTextBoxControl, Path=IsRequired, Converter={StaticResource BooleanToVisibilityConverter}}"/>
            </StackPanel>

            <!-- 帮助按钮 -->
            <Button Grid.Column="1"
                    x:Name="HelpButton"
                    Width="16" Height="16"
                    Background="Transparent"
                    BorderThickness="0"
                    Cursor="Hand"
                    ToolTip="{Binding ElementName=ValidatedTextBoxControl, Path=HelpText}"
                    Visibility="{Binding ElementName=ValidatedTextBoxControl, Path=ShowHelpButton, Converter={StaticResource BooleanToVisibilityConverter}}">
                <Path Width="12" Height="12" 
                      Fill="{DynamicResource OnSurfaceVariantBrush}"
                      Stretch="Uniform"
                      Data="M11,18H13V16H11V18M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,20C7.59,20 4,16.41 4,12C4,7.59 7.59,4 12,4C16.41,4 20,7.59 20,12C20,16.41 16.41,20 12,20M12,6A4,4 0 0,0 8,10H10A2,2 0 0,1 12,8A2,2 0 0,1 14,10C14,12 11,11.75 11,15H13C13,12.75 16,12.5 16,10A4,4 0 0,0 12,6Z"/>
            </Button>
        </Grid>

        <!-- 输入行 -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- TextBox -->
            <Grid Grid.Column="0">
                <!-- 占位符文本 -->
                <TextBlock x:Name="PlaceholderTextBlock"
                          Text="{Binding ElementName=ValidatedTextBoxControl, Path=PlaceholderText}"
                          Foreground="{DynamicResource OnSurfaceDisabledBrush}"
                          FontSize="14"
                          Margin="10,0,0,0"
                          VerticalAlignment="Center"
                          IsHitTestVisible="False">
                    <TextBlock.Style>
                        <Style TargetType="TextBlock">
                            <Setter Property="Visibility" Value="Collapsed"/>
                            <Style.Triggers>
                                <MultiDataTrigger>
                                    <MultiDataTrigger.Conditions>
                                        <Condition Binding="{Binding ElementName=InputTextBox, Path=Text}" Value=""/>
                                        <Condition Binding="{Binding ElementName=InputTextBox, Path=IsFocused}" Value="False"/>
                                        <Condition Binding="{Binding ElementName=ValidatedTextBoxControl, Path=PlaceholderText, Converter={StaticResource NullToBooleanConverter}}" Value="True"/>
                                    </MultiDataTrigger.Conditions>
                                    <Setter Property="Visibility" Value="Visible"/>
                                </MultiDataTrigger>
                            </Style.Triggers>
                        </Style>
                    </TextBlock.Style>
                </TextBlock>

                <!-- 实际TextBox -->
                <TextBox x:Name="InputTextBox" 
                         Text="{Binding ElementName=ValidatedTextBoxControl, Path=Text, UpdateSourceTrigger=PropertyChanged}"
                         IsEnabled="{Binding ElementName=ValidatedTextBoxControl, Path=IsEnabled}"
                         MaxLength="{Binding ElementName=ValidatedTextBoxControl, Path=MaxLength}"
                         TextChanged="InputTextBox_TextChanged"
                         LostFocus="InputTextBox_LostFocus"
                         GotFocus="InputTextBox_GotFocus"
                         Background="Transparent">
                    <TextBox.Style>
                        <Style TargetType="TextBox" BasedOn="{StaticResource ValidatedTextBoxStyle}">
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding ElementName=ValidatedTextBoxControl, Path=HasValidationError}" Value="True">
                                    <Setter Property="BorderBrush" Value="{DynamicResource ErrorBrush}"/>
                                    <Setter Property="BorderThickness" Value="2"/>
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </TextBox.Style>
                </TextBox>
            </Grid>

            <!-- 单位标签 -->
            <TextBlock Grid.Column="1" 
                       Text="{Binding ElementName=ValidatedTextBoxControl, Path=Unit}"
                       Style="{StaticResource UnitLabelStyle}"
                       Visibility="{Binding ElementName=ValidatedTextBoxControl, Path=Unit, Converter={StaticResource StringToVisibilityConverter}}"/>
        </Grid>

        <!-- 验证错误显示 -->
        <controls:ValidationErrorDisplay x:Name="ErrorDisplay" 
                                        Grid.Row="2"
                                        Margin="0,4,0,0"
                                        MessageType="Error"
                                        ShowCloseButton="False"
                                        AutoHideDelay="0"/>
    </Grid>
</UserControl>