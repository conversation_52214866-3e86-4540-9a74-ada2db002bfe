import { useRef } from "react";
import { useAppStore } from "../state/store";

export default function SidebarResizer() {
  const isDragging = useRef(false);
  const startX = useRef(0);
  const startWidth = useRef(0);
  const { sidebarWidth, setSidebarWidth } = useAppStore();

  function onMouseDown(e: React.MouseEvent) {
    isDragging.current = true;
    startX.current = e.clientX;
    startWidth.current = sidebarWidth;
    window.addEventListener("mousemove", onMouseMove);
    window.addEventListener("mouseup", onMouseUp);
  }
  function onMouseMove(e: MouseEvent) {
    if (!isDragging.current) return;
    const delta = e.clientX - startX.current;
    const next = Math.min(Math.max(220, startWidth.current + delta), 560);
    setSidebarWidth(next);
  }
  function onMouseUp() {
    isDragging.current = false;
    window.removeEventListener("mousemove", onMouseMove);
    window.removeEventListener("mouseup", onMouseUp);
  }

  return (
    <div
      onMouseDown={onMouseDown}
      className="w-1 cursor-col-resize bg-border hover:bg-primary/40 active:bg-primary/60 transition-colors"
      style={{ userSelect: "none" }}
      role="separator"
      aria-orientation="vertical"
      aria-label="Resize sidebar"
    />
  );
}


