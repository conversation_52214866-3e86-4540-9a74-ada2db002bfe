# ModernWPF 全面兼容性实施方案

基于 [Kinnara/ModernWpf](https://github.com/Kinnara/ModernWpf) 库的特性，实现完整的 WPF 兼容性。

## 1. ModernWPF 核心特性集成

### 1.1 应用程序配置
```xml
<!-- App.xaml -->
<Application x:Class="LIVAnalyzer.UI.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:ui="http://schemas.modernwpf.com/2019">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- ModernWPF 核心资源 -->
                <ui:ThemeResources>
                    <!-- 支持自定义主题色 -->
                    <ui:ThemeResources.ThemeDictionaries>
                        <ResourceDictionary x:Key="Light">
                            <SolidColorBrush x:Key="SystemAccentColor" Color="#0078D4"/>
                        </ResourceDictionary>
                        <ResourceDictionary x:Key="Dark">
                            <SolidColorBrush x:Key="SystemAccentColor" Color="#40E0D0"/>
                        </ResourceDictionary>
                    </ui:ThemeResources.ThemeDictionaries>
                </ui:ThemeResources>
                
                <!-- 控件资源 -->
                <ui:XamlControlsResources />
                
                <!-- 自定义样式 -->
                <ResourceDictionary Source="Themes/CustomStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Application.Resources>
</Application>
```

### 1.2 主题切换支持
```csharp
using ModernWpf;
using System.Windows;
using System.Windows.Media;

namespace LIVAnalyzer.UI.Services
{
    public class ThemeService
    {
        private static ThemeService? _instance;
        public static ThemeService Instance => _instance ??= new ThemeService();

        public void InitializeTheme()
        {
            // 检测系统主题
            ThemeManager.Current.ApplicationTheme = null; // 跟随系统
            
            // 监听系统主题变化
            ThemeManager.Current.ActualApplicationThemeChanged += OnActualApplicationThemeChanged;
            
            // 设置窗口背景刷
            UpdateWindowBackground();
        }

        private void OnActualApplicationThemeChanged(ThemeManager sender, object args)
        {
            UpdateWindowBackground();
            OnThemeChanged?.Invoke(sender.ActualApplicationTheme ?? ApplicationTheme.Light);
        }

        public event Action<ApplicationTheme>? OnThemeChanged;

        public void SetTheme(ApplicationTheme? theme)
        {
            ThemeManager.Current.ApplicationTheme = theme;
        }

        public ApplicationTheme GetCurrentTheme()
        {
            return ThemeManager.Current.ActualApplicationTheme ?? ApplicationTheme.Light;
        }

        public void SetAccentColor(Color color)
        {
            ThemeManager.Current.AccentColor = color;
        }

        private void UpdateWindowBackground()
        {
            // 更新所有窗口的背景
            foreach (Window window in Application.Current.Windows)
            {
                if (window is IThemeAware themeAware)
                {
                    themeAware.UpdateTheme();
                }
            }
        }
    }

    public interface IThemeAware
    {
        void UpdateTheme();
    }
}
```

## 2. ModernWPF 控件兼容性扩展

### 2.1 扩展的 NavigationView
```csharp
using ModernWpf.Controls;
using System.Windows;
using System.Windows.Input;

namespace LIVAnalyzer.UI.Controls
{
    /// <summary>
    /// 增强的 NavigationView，支持更多 WPF 特性
    /// </summary>
    public class EnhancedNavigationView : NavigationView
    {
        #region 快捷键支持

        public static readonly DependencyProperty NavigationKeyGestureProperty =
            DependencyProperty.RegisterAttached(
                "NavigationKeyGesture",
                typeof(KeyGesture),
                typeof(EnhancedNavigationView),
                new PropertyMetadata(null));

        public static KeyGesture GetNavigationKeyGesture(NavigationViewItem item)
        {
            return (KeyGesture)item.GetValue(NavigationKeyGestureProperty);
        }

        public static void SetNavigationKeyGesture(NavigationViewItem item, KeyGesture value)
        {
            item.SetValue(NavigationKeyGestureProperty, value);
        }

        protected override void OnApplyTemplate()
        {
            base.OnApplyTemplate();
            RegisterKeyBindings();
        }

        private void RegisterKeyBindings()
        {
            // 为每个导航项注册快捷键
            foreach (var item in MenuItems.OfType<NavigationViewItem>())
            {
                var gesture = GetNavigationKeyGesture(item);
                if (gesture != null)
                {
                    var binding = new KeyBinding(
                        new RelayCommand(() => SelectedItem = item),
                        gesture);
                    InputBindings.Add(binding);
                }
            }
        }

        #endregion

        #region 搜索功能增强

        public static readonly DependencyProperty IsSearchEnabledProperty =
            DependencyProperty.Register(
                nameof(IsSearchEnabled),
                typeof(bool),
                typeof(EnhancedNavigationView),
                new PropertyMetadata(true, OnIsSearchEnabledChanged));

        public bool IsSearchEnabled
        {
            get => (bool)GetValue(IsSearchEnabledProperty);
            set => SetValue(IsSearchEnabledProperty, value);
        }

        private static void OnIsSearchEnabledChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is EnhancedNavigationView nav)
            {
                nav.AutoSuggestBox.Visibility = (bool)e.NewValue ? Visibility.Visible : Visibility.Collapsed;
            }
        }

        #endregion
    }
}
```

### 2.2 增强的 ContentDialog
```csharp
using ModernWpf.Controls;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;

namespace LIVAnalyzer.UI.Dialogs
{
    /// <summary>
    /// 支持 MVVM 的 ContentDialog
    /// </summary>
    public class MvvmContentDialog : ContentDialog
    {
        public static readonly DependencyProperty ViewModelProperty =
            DependencyProperty.Register(
                nameof(ViewModel),
                typeof(object),
                typeof(MvvmContentDialog),
                new PropertyMetadata(null, OnViewModelChanged));

        public object ViewModel
        {
            get => GetValue(ViewModelProperty);
            set => SetValue(ViewModelProperty, value);
        }

        private static void OnViewModelChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is MvvmContentDialog dialog)
            {
                dialog.DataContext = e.NewValue;
            }
        }

        // 支持 ESC 键关闭
        protected override void OnKeyDown(KeyEventArgs e)
        {
            base.OnKeyDown(e);
            
            if (e.Key == Key.Escape && CloseButtonText != null)
            {
                Hide();
            }
        }

        // 异步显示对话框
        public new async Task<ContentDialogResult> ShowAsync()
        {
            // 确保只有一个对话框打开
            CloseOpenDialogs();
            
            return await base.ShowAsync();
        }

        private void CloseOpenDialogs()
        {
            var openedDialogs = Window.GetWindow(this)?.GetVisualDescendants()
                .OfType<ContentDialog>()
                .Where(d => d != this && d.IsLoaded);
                
            if (openedDialogs != null)
            {
                foreach (var dialog in openedDialogs)
                {
                    dialog.Hide();
                }
            }
        }
    }
}
```

## 3. WPF 特定功能集成

### 3.1 窗口样式和行为
```csharp
using ModernWpf;
using ModernWpf.Controls.Primitives;
using System.Windows;
using System.Windows.Interop;

namespace LIVAnalyzer.UI.Windows
{
    /// <summary>
    /// ModernWPF 风格的窗口基类
    /// </summary>
    public class ModernWindow : Window, IThemeAware
    {
        static ModernWindow()
        {
            DefaultStyleKeyProperty.OverrideMetadata(
                typeof(ModernWindow),
                new FrameworkPropertyMetadata(typeof(ModernWindow)));
        }

        public ModernWindow()
        {
            // 应用 ModernWPF 窗口样式
            WindowHelper.SetUseModernWindowStyle(this, true);
            
            // 设置标题栏
            TitleBarHelper.SetIsBackButtonVisible(this, false);
            TitleBarHelper.SetExtendViewIntoTitleBar(this, true);
            
            // 监听主题变化
            ThemeService.Instance.OnThemeChanged += OnThemeChanged;
        }

        protected override void OnSourceInitialized(EventArgs e)
        {
            base.OnSourceInitialized(e);
            
            // 获取窗口句柄
            var hwnd = new WindowInteropHelper(this).Handle;
            
            // 应用 Mica/Acrylic 效果（Windows 11/10）
            ApplyBackdrop(hwnd);
        }

        private void ApplyBackdrop(IntPtr hwnd)
        {
            // Windows 11 Mica 效果
            if (Environment.OSVersion.Version.Build >= 22000)
            {
                WindowHelper.SetUseBackdropBrush(this, true);
            }
        }

        private void OnThemeChanged(ApplicationTheme theme)
        {
            UpdateTheme();
        }

        public void UpdateTheme()
        {
            // 更新窗口主题相关的属性
            var isDark = ThemeManager.Current.ActualApplicationTheme == ApplicationTheme.Dark;
            
            // 更新标题栏按钮颜色
            TitleBarHelper.SetButtonForeground(this, 
                isDark ? Brushes.White : Brushes.Black);
        }

        protected override void OnClosed(EventArgs e)
        {
            ThemeService.Instance.OnThemeChanged -= OnThemeChanged;
            base.OnClosed(e);
        }
    }
}
```

### 3.2 数据网格样式
```xml
<!-- Themes/DataGridStyles.xaml -->
<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:ui="http://schemas.modernwpf.com/2019">

    <!-- ModernWPF 风格的 DataGrid -->
    <Style x:Key="ModernDataGridStyle" TargetType="DataGrid">
        <Setter Property="Background" Value="{DynamicResource SystemControlBackgroundAltHighBrush}"/>
        <Setter Property="Foreground" Value="{DynamicResource SystemControlForegroundBaseHighBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource SystemControlForegroundBaseLowBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="RowDetailsVisibilityMode" Value="VisibleWhenSelected"/>
        <Setter Property="ScrollViewer.CanContentScroll" Value="True"/>
        <Setter Property="GridLinesVisibility" Value="None"/>
        <Setter Property="HeadersVisibility" Value="Column"/>
        <Setter Property="AutoGenerateColumns" Value="False"/>
        <Setter Property="CanUserAddRows" Value="False"/>
        
        <!-- 行样式 -->
        <Setter Property="RowStyle">
            <Setter.Value>
                <Style TargetType="DataGridRow">
                    <Setter Property="Background" Value="Transparent"/>
                    <Setter Property="Height" Value="40"/>
                    <Style.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="{DynamicResource SystemControlHighlightListLowBrush}"/>
                        </Trigger>
                        <Trigger Property="IsSelected" Value="True">
                            <Setter Property="Background" Value="{DynamicResource SystemControlHighlightAccentBrush}"/>
                            <Setter Property="Foreground" Value="{DynamicResource SystemControlForegroundBaseHighBrush}"/>
                        </Trigger>
                    </Style.Triggers>
                </Style>
            </Setter.Value>
        </Setter>
        
        <!-- 单元格样式 -->
        <Setter Property="CellStyle">
            <Setter.Value>
                <Style TargetType="DataGridCell">
                    <Setter Property="BorderThickness" Value="0"/>
                    <Setter Property="Padding" Value="12,0"/>
                    <Setter Property="Template">
                        <Setter.Value>
                            <ControlTemplate TargetType="DataGridCell">
                                <Border Background="{TemplateBinding Background}"
                                        BorderBrush="{TemplateBinding BorderBrush}"
                                        BorderThickness="{TemplateBinding BorderThickness}"
                                        Padding="{TemplateBinding Padding}">
                                    <ContentPresenter VerticalAlignment="Center"/>
                                </Border>
                            </ControlTemplate>
                        </Setter.Value>
                    </Setter>
                </Style>
            </Setter.Value>
        </Setter>
        
        <!-- 列头样式 -->
        <Setter Property="ColumnHeaderStyle">
            <Setter.Value>
                <Style TargetType="DataGridColumnHeader">
                    <Setter Property="Background" Value="{DynamicResource SystemControlBackgroundChromeMediumBrush}"/>
                    <Setter Property="Foreground" Value="{DynamicResource SystemControlForegroundBaseHighBrush}"/>
                    <Setter Property="Height" Value="48"/>
                    <Setter Property="Padding" Value="12,0"/>
                    <Setter Property="HorizontalContentAlignment" Value="Left"/>
                    <Setter Property="VerticalContentAlignment" Value="Center"/>
                    <Setter Property="FontWeight" Value="SemiBold"/>
                </Style>
            </Setter.Value>
        </Setter>
    </Style>
</ResourceDictionary>
```

## 4. 响应式设计支持

### 4.1 自适应触发器
```csharp
using System.Windows;
using ModernWpf;

namespace LIVAnalyzer.UI.Helpers
{
    /// <summary>
    /// 响应式设计触发器
    /// </summary>
    public class AdaptiveTrigger : DependencyObject
    {
        public static readonly DependencyProperty MinWindowWidthProperty =
            DependencyProperty.RegisterAttached(
                "MinWindowWidth",
                typeof(double),
                typeof(AdaptiveTrigger),
                new PropertyMetadata(0.0, OnMinWindowWidthChanged));

        public static double GetMinWindowWidth(DependencyObject obj)
        {
            return (double)obj.GetValue(MinWindowWidthProperty);
        }

        public static void SetMinWindowWidth(DependencyObject obj, double value)
        {
            obj.SetValue(MinWindowWidthProperty, value);
        }

        private static void OnMinWindowWidthChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is FrameworkElement element && element.IsLoaded)
            {
                var window = Window.GetWindow(element);
                if (window != null)
                {
                    window.SizeChanged += (s, args) => UpdateVisibility(element, window);
                    UpdateVisibility(element, window);
                }
            }
        }

        private static void UpdateVisibility(FrameworkElement element, Window window)
        {
            var minWidth = GetMinWindowWidth(element);
            element.Visibility = window.ActualWidth >= minWidth ? Visibility.Visible : Visibility.Collapsed;
        }
    }
}
```

### 4.2 响应式面板
```csharp
using System.Windows;
using System.Windows.Controls;

namespace LIVAnalyzer.UI.Controls
{
    /// <summary>
    /// 自适应的 WrapPanel
    /// </summary>
    public class ResponsiveWrapPanel : Panel
    {
        public static readonly DependencyProperty MinItemWidthProperty =
            DependencyProperty.Register(
                nameof(MinItemWidth),
                typeof(double),
                typeof(ResponsiveWrapPanel),
                new FrameworkPropertyMetadata(200.0, FrameworkPropertyMetadataOptions.AffectsMeasure));

        public double MinItemWidth
        {
            get => (double)GetValue(MinItemWidthProperty);
            set => SetValue(MinItemWidthProperty, value);
        }

        protected override Size MeasureOverride(Size availableSize)
        {
            var itemsPerRow = Math.Max(1, (int)(availableSize.Width / MinItemWidth));
            var itemWidth = availableSize.Width / itemsPerRow;
            
            double totalHeight = 0;
            double rowHeight = 0;
            int currentColumn = 0;

            foreach (UIElement child in Children)
            {
                child.Measure(new Size(itemWidth, double.PositiveInfinity));
                
                rowHeight = Math.Max(rowHeight, child.DesiredSize.Height);
                currentColumn++;
                
                if (currentColumn >= itemsPerRow)
                {
                    totalHeight += rowHeight;
                    rowHeight = 0;
                    currentColumn = 0;
                }
            }
            
            if (currentColumn > 0)
            {
                totalHeight += rowHeight;
            }

            return new Size(availableSize.Width, totalHeight);
        }

        protected override Size ArrangeOverride(Size finalSize)
        {
            var itemsPerRow = Math.Max(1, (int)(finalSize.Width / MinItemWidth));
            var itemWidth = finalSize.Width / itemsPerRow;
            
            double y = 0;
            double rowHeight = 0;
            int currentColumn = 0;

            foreach (UIElement child in Children)
            {
                var x = currentColumn * itemWidth;
                child.Arrange(new Rect(x, y, itemWidth, child.DesiredSize.Height));
                
                rowHeight = Math.Max(rowHeight, child.DesiredSize.Height);
                currentColumn++;
                
                if (currentColumn >= itemsPerRow)
                {
                    y += rowHeight;
                    rowHeight = 0;
                    currentColumn = 0;
                }
            }

            return finalSize;
        }
    }
}
```

## 5. 完整的示例应用

### 5.1 主窗口实现
```csharp
using ModernWpf.Controls;
using System.Windows;

namespace LIVAnalyzer.UI.Views
{
    public partial class MainWindow : ModernWindow
    {
        public MainWindow()
        {
            InitializeComponent();
            
            // 初始化主题
            ThemeService.Instance.InitializeTheme();
            
            // 设置导航
            SetupNavigation();
        }

        private void SetupNavigation()
        {
            // 配置 NavigationView
            NavView.SelectionChanged += OnNavigationSelectionChanged;
            
            // 设置初始页面
            NavView.SelectedItem = NavView.MenuItems[0];
        }

        private void OnNavigationSelectionChanged(NavigationView sender, NavigationViewSelectionChangedEventArgs args)
        {
            if (args.SelectedItem is NavigationViewItem item)
            {
                var tag = item.Tag?.ToString();
                NavigateToPage(tag);
            }
        }

        private void NavigateToPage(string? pageTag)
        {
            // 导航逻辑
            switch (pageTag)
            {
                case "analysis":
                    ContentFrame.Navigate(typeof(AnalysisPage));
                    break;
                case "batch":
                    ContentFrame.Navigate(typeof(BatchProcessPage));
                    break;
                case "settings":
                    ContentFrame.Navigate(typeof(SettingsPage));
                    break;
            }
        }
    }
}
```

### 5.2 主窗口 XAML
```xml
<local:ModernWindow x:Class="LIVAnalyzer.UI.Views.MainWindow"
                    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:local="clr-namespace:LIVAnalyzer.UI.Windows"
                    xmlns:ui="http://schemas.modernwpf.com/2019"
                    Title="LIV Analyzer"
                    Width="1200" Height="800">
    
    <Grid>
        <ui:NavigationView x:Name="NavView" 
                          PaneDisplayMode="Left"
                          IsBackButtonVisible="Collapsed"
                          IsSettingsVisible="True">
            
            <!-- 导航项 -->
            <ui:NavigationView.MenuItems>
                <ui:NavigationViewItem Icon="Home" Content="分析" Tag="analysis">
                    <local:EnhancedNavigationView.NavigationKeyGesture>
                        <KeyGesture>Ctrl+1</KeyGesture>
                    </local:EnhancedNavigationView.NavigationKeyGesture>
                </ui:NavigationViewItem>
                
                <ui:NavigationViewItem Content="批处理" Tag="batch">
                    <ui:NavigationViewItem.Icon>
                        <ui:SymbolIcon Symbol="Library"/>
                    </ui:NavigationViewItem.Icon>
                    <local:EnhancedNavigationView.NavigationKeyGesture>
                        <KeyGesture>Ctrl+2</KeyGesture>
                    </local:EnhancedNavigationView.NavigationKeyGesture>
                </ui:NavigationViewItem>
            </ui:NavigationView.MenuItems>
            
            <!-- 内容 -->
            <ui:NavigationView.Content>
                <Frame x:Name="ContentFrame" 
                       NavigationUIVisibility="Hidden"/>
            </ui:NavigationView.Content>
            
            <!-- 设置 -->
            <ui:NavigationView.PaneFooter>
                <ui:NavigationViewItem Icon="Setting" Content="设置" Tag="settings"/>
            </ui:NavigationView.PaneFooter>
        </ui:NavigationView>
    </Grid>
</local:ModernWindow>
```

## 实施建议

1. **逐步迁移**：先迁移核心功能，再添加高级特性
2. **保持一致性**：使用 ModernWPF 的设计语言
3. **性能优化**：利用虚拟化和异步加载
4. **测试覆盖**：确保所有 WPF 特性正常工作
5. **文档完善**：记录自定义控件和扩展的使用方法

这样就能充分利用 ModernWPF 的特性，同时保持完整的 WPF 兼容性。