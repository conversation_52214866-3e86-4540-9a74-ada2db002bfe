using System.Windows;
using System.Windows.Controls;
using LIVAnalyzer.UI.Services;

namespace LIVAnalyzer.UI.Controls
{
    /// <summary>
    /// 主题切换控件
    /// </summary>
    public class ThemeToggleButton : Control
    {
        static ThemeToggleButton()
        {
            DefaultStyleKeyProperty.OverrideMetadata(typeof(ThemeToggleButton), 
                new FrameworkPropertyMetadata(typeof(ThemeToggleButton)));
        }

        public ThemeToggleButton()
        {
            this.MouseLeftButtonUp += OnClick;
        }

        private void OnClick(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            // 智能主题切换逻辑
            var currentIsDark = NativeFluentThemeService.Instance.IsCurrentlyDarkMode();
            var newTheme = currentIsDark ? "Light" : "Dark";
            App.SwitchTheme(newTheme);
            
            // 更新图标
            UpdateIcon();
        }

        public override void OnApplyTemplate()
        {
            base.OnApplyTemplate();
            UpdateIcon();
        }

        private void UpdateIcon()
        {
            var icon = GetTemplateChild("PART_Icon") as TextBlock;
            if (icon != null)
            {
                // 根据当前实际主题设置图标
                var isDarkTheme = NativeFluentThemeService.Instance.IsCurrentlyDarkMode();
                icon.Text = isDarkTheme ? "☀️" : "🌙";
            }
        }
    }
}