# 创建LIV分析工具发布包 PowerShell脚本
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    创建LIV分析工具发布包 V2.0.2" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 设置工作目录
Set-Location "E:\01LaserPackage\software\LIV_Analyzer\CSharp_Version"

# 创建发布包目录
$releaseDir = "LIVAnalyzer_V2.0.2_Release"
if (Test-Path $releaseDir) {
    Remove-Item $releaseDir -Recurse -Force
}
New-Item -ItemType Directory -Path $releaseDir | Out-Null

Write-Host "[1/5] 复制程序文件..." -ForegroundColor Yellow
Copy-Item "publish-release\LIVAnalyzer.exe" "$releaseDir\" -Force
Copy-Item "publish-release\Accord.dll.config" "$releaseDir\" -Force
Copy-Item "publish-release\README.txt" "$releaseDir\" -Force
Copy-Item "publish-release\启动LIV分析工具.bat" "$releaseDir\" -Force

Write-Host "[2/5] 复制文档文件..." -ForegroundColor Yellow
Copy-Item "publish-release\使用指南.md" "$releaseDir\" -Force
Copy-Item "publish-release\更新日志_V2.0.2.md" "$releaseDir\" -Force

Write-Host "[3/5] 创建版本信息..." -ForegroundColor Yellow
$versionInfo = @"
LIV曲线分析工具 V2.0.2
构建时间：$(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
文件大小：约160MB
系统要求：Windows 10/11 x64

更新内容：
- FW86.5%更名为FW(1/e²)，更准确反映物理含义
- 改进负强度值处理和能量占比计算
- 优化界面显示和文档说明
- 修复多项已知问题
"@
$versionInfo | Out-File "$releaseDir\版本信息.txt" -Encoding UTF8

Write-Host "[4/5] 显示文件信息..." -ForegroundColor Yellow
Write-Host ""
Write-Host "发布目录：$releaseDir" -ForegroundColor Green
Write-Host "包含文件：" -ForegroundColor Green
Get-ChildItem $releaseDir | ForEach-Object { Write-Host "  - $($_.Name)" }

# 获取主程序大小
$exeFile = Get-Item "$releaseDir\LIVAnalyzer.exe"
$sizeMB = [math]::Round($exeFile.Length / 1MB, 2)
Write-Host ""
Write-Host "主程序大小：$sizeMB MB" -ForegroundColor Green

Write-Host ""
Write-Host "[5/5] 创建ZIP压缩包..." -ForegroundColor Yellow
$zipFile = "$releaseDir.zip"
if (Test-Path $zipFile) {
    Remove-Item $zipFile -Force
}
Compress-Archive -Path "$releaseDir\*" -DestinationPath $zipFile -CompressionLevel Optimal

if (Test-Path $zipFile) {
    $zipInfo = Get-Item $zipFile
    $zipSizeMB = [math]::Round($zipInfo.Length / 1MB, 2)
    Write-Host "压缩包创建成功：$zipFile" -ForegroundColor Green
    Write-Host "压缩包大小：$zipSizeMB MB" -ForegroundColor Green
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "         发布包创建完成！" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "可以分发以下文件给用户：" -ForegroundColor Yellow
Write-Host "1. 完整目录：$releaseDir\" -ForegroundColor White
Write-Host "2. 压缩包：$zipFile" -ForegroundColor White
Write-Host ""