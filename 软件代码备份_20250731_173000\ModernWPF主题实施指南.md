# ModernWPF 主题切换标准实现指南

## 概述

本指南说明如何在 LIV Analyzer 中正确实现符合 ModernWPF 标准的主题切换功能。

## 主题切换实现

### 1. 基本主题切换

```csharp
using ModernWpf;

// 切换到暗色主题
ThemeManager.Current.ApplicationTheme = ApplicationTheme.Dark;

// 切换到亮色主题
ThemeManager.Current.ApplicationTheme = ApplicationTheme.Light;

// 跟随系统主题
ThemeManager.Current.ApplicationTheme = null;
```

### 2. 在 ViewModel 中实现主题切换

```csharp
public class ThemeViewModel : ObservableObject
{
    private ApplicationTheme? _currentTheme;
    
    public ApplicationTheme? CurrentTheme
    {
        get => _currentTheme;
        set
        {
            if (SetProperty(ref _currentTheme, value))
            {
                ThemeManager.Current.ApplicationTheme = value;
                SaveThemePreference(value);
            }
        }
    }
    
    public ICommand ToggleThemeCommand { get; }
    
    public ThemeViewModel()
    {
        ToggleThemeCommand = new RelayCommand(ToggleTheme);
        LoadThemePreference();
    }
    
    private void ToggleTheme()
    {
        CurrentTheme = CurrentTheme == ApplicationTheme.Dark 
            ? ApplicationTheme.Light 
            : ApplicationTheme.Dark;
    }
    
    private void SaveThemePreference(ApplicationTheme? theme)
    {
        var config = ConfigurationManager.Instance.GetConfig();
        config.UI.Theme = theme?.ToString() ?? "System";
        ConfigurationManager.Instance.SaveConfig(config);
    }
    
    private void LoadThemePreference()
    {
        var config = ConfigurationManager.Instance.GetConfig();
        CurrentTheme = config.UI.Theme switch
        {
            "Dark" => ApplicationTheme.Dark,
            "Light" => ApplicationTheme.Light,
            _ => null // 跟随系统
        };
    }
}
```

### 3. 在 XAML 中添加主题切换控件

```xml
<!-- 使用 ModernWPF 的 ToggleSwitch -->
<ui:ToggleSwitch 
    Header="暗色主题"
    IsOn="{Binding CurrentTheme, Converter={StaticResource ThemeToBoolConverter}}"
    OnContent="开启"
    OffContent="关闭"/>

<!-- 或使用 RadioButtons -->
<ui:RadioButtons Header="选择主题">
    <RadioButton Content="跟随系统" IsChecked="{Binding IsSystemTheme}"/>
    <RadioButton Content="亮色" IsChecked="{Binding IsLightTheme}"/>
    <RadioButton Content="暗色" IsChecked="{Binding IsDarkTheme}"/>
</ui:RadioButtons>
```

### 4. 主题资源使用规范

#### ✅ 正确的做法：

```xml
<!-- 使用动态资源引用 -->
<Border Background="{DynamicResource SystemControlPageBackgroundAltHighBrush}"/>
<TextBlock Foreground="{DynamicResource SystemControlForegroundBaseHighBrush}"/>

<!-- 继承 ModernWPF 样式 -->
<Style TargetType="Button" BasedOn="{StaticResource DefaultButtonStyle}">
    <Setter Property="Margin" Value="4"/>
</Style>
```

#### ❌ 错误的做法：

```xml
<!-- 不要使用硬编码颜色 -->
<Border Background="#FFFFFF"/>
<Border Background="White"/>

<!-- 不要使用静态资源引用主题相关资源 -->
<TextBlock Foreground="{StaticResource SystemControlForegroundBaseHighBrush}"/>

<!-- 不要完全重写控件模板 -->
<Style TargetType="Button">
    <Setter Property="Template">
        <!-- 自定义模板 -->
    </Setter>
</Style>
```

## ModernWPF 系统资源参考

### 常用背景画刷
- `SystemControlPageBackgroundAltHighBrush` - 页面主背景
- `SystemControlPageBackgroundChromeLowBrush` - 卡片/面板背景
- `SystemControlPageBackgroundAltMediumBrush` - 次要背景
- `SystemControlTransparentBrush` - 透明背景

### 常用前景画刷
- `SystemControlForegroundBaseHighBrush` - 主要文本
- `SystemControlForegroundBaseMediumBrush` - 次要文本
- `SystemControlForegroundBaseLowBrush` - 禁用文本/边框

### 强调色画刷
- `SystemControlHighlightAccentBrush` - 主题色
- `SystemControlHighlightListAccentLowBrush` - 选中项背景
- `SystemControlHighlightListAccentHighBrush` - 鼠标悬停

## 最佳实践

1. **始终使用 DynamicResource**：对于所有主题相关的资源引用
2. **避免硬编码颜色**：使用系统定义的画刷资源
3. **继承基础样式**：自定义样式应基于 ModernWPF 提供的默认样式
4. **测试两种主题**：确保在明暗主题下都有良好的视觉效果
5. **保存用户偏好**：记住用户的主题选择

## 图表主题适配

对于 OxyPlot 图表，需要在主题切换时更新图表颜色：

```csharp
private void UpdatePlotTheme(PlotModel plotModel)
{
    var isDark = ThemeManager.Current.ActualApplicationTheme == ApplicationTheme.Dark;
    
    plotModel.Background = isDark ? OxyColors.Black : OxyColors.White;
    plotModel.PlotAreaBackground = isDark ? OxyColor.FromRgb(30, 30, 30) : OxyColors.White;
    plotModel.TextColor = isDark ? OxyColors.White : OxyColors.Black;
    
    // 更新坐标轴颜色
    foreach (var axis in plotModel.Axes)
    {
        axis.AxislineColor = isDark ? OxyColors.Gray : OxyColors.Black;
        axis.TicklineColor = isDark ? OxyColors.Gray : OxyColors.Black;
        axis.TextColor = isDark ? OxyColors.White : OxyColors.Black;
    }
    
    plotModel.InvalidatePlot(false);
}
```

## 故障排除

### 问题：主题切换后某些控件颜色不变
**解决方案**：检查是否使用了 StaticResource 或硬编码颜色

### 问题：自定义样式在暗色主题下不可见
**解决方案**：使用系统资源而不是固定颜色值

### 问题：第三方控件不支持主题
**解决方案**：监听主题变化事件并手动更新控件样式

```csharp
ThemeManager.Current.ActualApplicationThemeChanged += OnThemeChanged;

private void OnThemeChanged(ThemeManager sender, object args)
{
    // 更新第三方控件的颜色
    UpdateThirdPartyControlsTheme();
}
```

## 总结

遵循这些指南可以确保应用程序完全符合 ModernWPF 的主题标准，提供一致的用户体验，并支持无缝的主题切换功能。