import { getLastStartInHandle, saveLastFileHandle } from './lastHandleStore';

export function isFSAccessSupported(): boolean {
  return typeof window !== 'undefined' && 'showOpenFilePicker' in window;
}

export async function pickFileWithStartIn(): Promise<File | null> {
  if (!isFSAccessSupported()) return null;
  try {
    const startIn = await getLastStartInHandle();
    const handles: any[] = await (window as any).showOpenFilePicker({
      multiple: false,
      types: [
        { description: 'CSV', accept: { 'text/csv': ['.csv'] } },
        { description: 'Excel', accept: { 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'], 'application/vnd.ms-excel': ['.xls'] } },
      ],
      startIn: startIn ?? 'documents',
    });
    if (!handles || handles.length === 0) return null;
    const handle = handles[0] as FileSystemFileHandle;
    const file = await (handle as any).getFile();
    await saveLastFileHandle(handle as any);
    return file;
  } catch {
    return null;
  }
}


