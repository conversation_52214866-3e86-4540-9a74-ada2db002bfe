using LIVAnalyzer.Services.Logging;
using LIVAnalyzer.UI.Services;
using LIVAnalyzer.UI.Views;
using System;
using System.Windows;

namespace LIVAnalyzer.UI
{
    /// <summary>
    /// Interaction logic for App.xaml
    /// </summary>
    public partial class App : Application
    {
        static App()
        {
            // 添加应用程序域异常处理
            AppDomain.CurrentDomain.UnhandledException += (sender, args) =>
            {
                var ex = args.ExceptionObject as Exception;
                MessageBox.Show($"致命错误:\n{ex?.GetType().Name}\n{ex?.Message}\n\n堆栈跟踪:\n{ex?.StackTrace}", 
                    "LIV分析工具错误", MessageBoxButton.OK, MessageBoxImage.Error);
            };
        }

        protected override void OnStartup(StartupEventArgs e)
        {
            try
            {
                base.OnStartup(e);
                
                // 初始化日志系统
                LoggingService.Initialize();
                LoggingService.LogInformation("LIV分析工具启动");
                
                // 初始化主题系统
                InitializeTheme();
                
                // 创建并显示主窗口
                var mainWindow = new MainWindow();
                // 移除 ModernWpf 集成，使用我们的 Fluent 主题
                mainWindow.Show();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"启动失败:\n{ex.GetType().Name}\n{ex.Message}\n\n内部异常:\n{ex.InnerException?.Message}\n\n堆栈跟踪:\n{ex.StackTrace}", 
                    "LIV分析工具启动错误", MessageBoxButton.OK, MessageBoxImage.Error);
                Shutdown();
            }
            
            // 设置全局异常处理
            DispatcherUnhandledException += (sender, args) =>
            {
                LoggingService.LogError(args.Exception, "未处理的UI异常");
                args.Handled = true;
                
                MessageBox.Show($"发生错误: {args.Exception.Message}", 
                    "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            };
        }
        
        private void InitializeTheme()
        {
            // 初始化 .NET 9 原生 Fluent Design 主题服务
            try
            {
                var themeService = NativeFluentThemeService.Instance;
                var currentMode = themeService.GetCurrentThemeMode();
                LoggingService.LogInformation($".NET 9 原生 Fluent Design 主题已初始化，模式: {currentMode}");

                // 强制应用当前主题以确保资源正确更新
                themeService.SetTheme(currentMode);

                // 订阅主题变更事件
                themeService.ThemeChanged += (sender, args) =>
                {
                    LoggingService.LogInformation($"主题已切换到: {args.ThemeMode}");
                };
            }
            catch (Exception ex)
            {
                LoggingService.LogWarning($"初始化主题服务时发生错误: {ex.Message}");
                // 如果出错，至少应用默认的深色主题
                try
                {
                    var themeService = NativeFluentThemeService.Instance;
                    themeService.SetTheme(ThemeMode.Dark);
                }
                catch
                {
                    // 忽略二次错误
                }
            }
        }
        
        public static void SwitchTheme(string theme)
        {
            try
            {
                var themeService = NativeFluentThemeService.Instance;
                themeService.SetTheme(theme);
                LoggingService.LogInformation($"主题已切换到: {theme}");
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"切换主题失败: {theme}");
            }
        }
        
        protected override void OnExit(ExitEventArgs e)
        {
            LoggingService.LogInformation("LIV分析工具退出");
            LoggingService.Close();
            base.OnExit(e);
        }
    }
}