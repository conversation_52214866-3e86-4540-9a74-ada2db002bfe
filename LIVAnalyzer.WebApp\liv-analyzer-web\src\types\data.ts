export interface SpectralSeries {
  wavelength: number[];
  intensity: number[];
}

export interface PowerCurrentSeries {
  current: number[];
  power: number[];
}

export interface VoltageCurrentSeries {
  current: number[];
  voltage: number[];
}

export interface FarFieldSeries {
  angle: number[];
  intensity: number[];
}

export interface LIVData {
  wavelength?: SpectralSeries; // 可选：部分文件不含光谱
  power?: PowerCurrentSeries;
  voltage?: VoltageCurrentSeries;
  hff?: FarFieldSeries; // 水平远场（可选）
  vff?: FarFieldSeries; // 垂直远场（可选）
}

export interface LIVParameters {
  thresholdCurrent_mA?: number;
  slopeEfficiency_W_per_A?: number;
  seriesResistance_Ohm?: number;
  maxEfficiency?: number;
}

export interface SpectralParameters {
  peakWavelength_nm?: number;
  fwhm_nm?: number;
  centroid_nm?: number;
  smsr_dB?: number;
  peaks?: { wavelength_nm: number; intensity: number }[];
}

export interface DivergenceParameters {
  horizontal_deg?: number;
  vertical_deg?: number;
  ellipticity?: number;
}

export type PlotKind =
  | 'LIV'
  | 'Spectrum'
  | 'Divergence'
  | 'Efficiency';

export interface PlotData {
  kind: PlotKind;
  x: number[];
  y: number[];
  label?: string;
  unitX?: string;
  unitY?: string;
}

export interface ProcessingResult {
  livParameters?: LIVParameters;
  spectralParameters?: SpectralParameters;
  divergenceParameters?: DivergenceParameters;
  plots?: PlotData[];
  livFitDiagnostics?: {
    slope?: number;
    intercept?: number;
    usedIndices?: number[];
    outlierIndices?: number[];
  };
}

export interface ProcessingConfig {
  thresholdDetection?: 'linear' | 'segmented' | 'robust';
  fittingPoints?: number;
  smoothing?: {
    enabled: boolean;
    method?: 'sg' | 'moving-average' | 'gaussian' | 'butterworth' | 'median' | 'adaptive';
    windowSize?: number;
    polynomialOrder?: number;
    cutoff?: number; // 0..0.5 for IIR filters
    sigma?: number; // for gaussian filter
    order?: number; // for butterworth filter
    maxWindowSize?: number; // for adaptive filter
  };
  units?: {
    power?: 'W' | 'mW';
    current?: 'A' | 'mA';
  };
  // 阈值计算高级配置（与桌面版对齐）
  threshold?: {
    searchRangeRatio?: number;        // 搜索范围比例 (默认0.5)
    maxThresholdRatio?: number;       // 最大阈值比例 (默认0.3)
    secondaryMaxThresholdRatio?: number; // 次级最大阈值比例 (默认0.5)
    primaryFallbackPowerRatio?: number;  // 主要回退功率比例 (默认0.05)
    secondaryFallbackPowerRatio?: number; // 次级回退功率比例 (默认0.01)
    tertiaryFallbackPowerRatio?: number;  // 第三级回退功率比例 (默认0.02)
    maxSmoothingWindow?: number;      // 最大平滑窗口 (默认31)
    dataWindowRatio?: number;         // 数据窗口比例 (默认0.2)
    maxDerivativeSmoothingWindow?: number; // 最大导数平滑窗口 (默认5)
    derivativeWindowRatio?: number;   // 导数窗口比例 (默认0.33)
    derivativeRatio?: number;         // 导数比例 (默认0.5)
    minDataPoints?: number;           // 最小数据点数 (默认10)
    numericalPrecision?: number;      // 数值精度 (默认1e-10)
  };
}

export interface DisplayConfig {
  showGrid?: boolean;
  showLegend?: boolean;
  showMarkers?: boolean;
  showCoords?: boolean;
  theme?: 'light' | 'dark';
  showDiagnosticsLIV?: boolean;
  showPI?: boolean;
  showVI?: boolean;
  showEta?: boolean;
  showSpectrumLine?: boolean;
  showHFF?: boolean;
  showVFF?: boolean;
}


