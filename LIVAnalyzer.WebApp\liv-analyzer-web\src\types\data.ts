export interface SpectralSeries {
  wavelength: number[];
  intensity: number[];
}

export interface PowerCurrentSeries {
  current: number[];
  power: number[];
}

export interface VoltageCurrentSeries {
  current: number[];
  voltage: number[];
}

export interface FarFieldSeries {
  angle: number[];
  intensity: number[];
}

export interface LIVData {
  wavelength?: SpectralSeries; // 可选：部分文件不含光谱
  power?: PowerCurrentSeries;
  voltage?: VoltageCurrentSeries;
  hff?: FarFieldSeries; // 水平远场（可选）
  vff?: FarFieldSeries; // 垂直远场（可选）
}

export interface LIVParameters {
  thresholdCurrent_mA?: number;
  slopeEfficiency_W_per_A?: number;
  seriesResistance_Ohm?: number;
  maxEfficiency?: number;
}

export interface SpectralParameters {
  peakWavelength_nm?: number;
  fwhm_nm?: number;
  centroid_nm?: number;
  smsr_dB?: number;
  peaks?: { wavelength_nm: number; intensity: number }[];
}

export interface DivergenceParameters {
  horizontal_deg?: number;
  vertical_deg?: number;
  ellipticity?: number;
}

export type PlotKind =
  | 'LIV'
  | 'Spectrum'
  | 'Divergence'
  | 'Efficiency';

export interface PlotData {
  kind: PlotKind;
  x: number[];
  y: number[];
  label?: string;
  unitX?: string;
  unitY?: string;
}

export interface ProcessingResult {
  livParameters?: LIVParameters;
  spectralParameters?: SpectralParameters;
  divergenceParameters?: DivergenceParameters;
  plots?: PlotData[];
  livFitDiagnostics?: {
    slope?: number;
    intercept?: number;
    usedIndices?: number[];
    outlierIndices?: number[];
  };
}

export interface ProcessingConfig {
  thresholdDetection?: 'linear' | 'segmented' | 'robust';
  fittingPoints?: number;
  smoothing?: {
    enabled: boolean;
    method?: 'sg' | 'moving-average' | 'gaussian' | 'butterworth';
    windowSize?: number;
    polynomialOrder?: number;
    cutoff?: number; // 0..0.5 for IIR filters
  };
  units?: {
    power?: 'W' | 'mW';
    current?: 'A' | 'mA';
  };
}

export interface DisplayConfig {
  showGrid?: boolean;
  showLegend?: boolean;
  showMarkers?: boolean;
  showCoords?: boolean;
  theme?: 'light' | 'dark';
  showDiagnosticsLIV?: boolean;
  showPI?: boolean;
  showVI?: boolean;
  showEta?: boolean;
  showSpectrumLine?: boolean;
  showHFF?: boolean;
  showVFF?: boolean;
}


