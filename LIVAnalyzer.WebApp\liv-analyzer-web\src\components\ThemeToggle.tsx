import { <PERSON>, Moon } from "lucide-react";
import { But<PERSON> } from "./ui/button";
import { useEffect } from "react";
import { useAppStore } from "../state/store";

export default function ThemeToggle() {
  const { theme, setTheme } = useAppStore();

  useEffect(() => {
    const root = document.documentElement;
    if (theme === "dark") root.classList.add("dark");
    else root.classList.remove("dark");
  }, [theme]);

  return (
    <Button
      variant="ghost"
      size="icon"
      aria-label="Toggle theme"
      onClick={() => setTheme(theme === "light" ? "dark" : "light")}
      title={theme === "light" ? "切换到暗色" : "切换到亮色"}
    >
      {theme === "light" ? <Moon className="h-5 w-5" /> : <Sun className="h-5 w-5" />}
    </Button>
  );
}


