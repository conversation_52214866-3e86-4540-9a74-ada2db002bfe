# ModernWPF 主题色影响的 UI 元素说明

## 主题色（Accent Color）会改变哪些元素？

### 1. **按钮类控件**
- **AccentButtonStyle** 按钮的背景色
- 默认按钮的悬停和按下状态
- 切换按钮的选中状态
- 单选按钮和复选框的选中标记

### 2. **选择类控件**
- **TextBox** 获得焦点时的边框
- **ComboBox** 下拉时的高亮边框
- **ListBox/ListView** 选中项的背景
- **TreeView** 选中节点的背景
- **DataGrid** 选中行的背景

### 3. **进度和滑块控件**
- **ProgressBar** 的进度条颜色
- **Slider** 的轨道填充色和滑块
- **ToggleSwitch** 开启状态的背景

### 4. **导航类控件**
- **TabControl** 选中标签的下划线或背景
- **NavigationView** 选中项的指示器
- **Menu** 菜单项的悬停和选中状态

### 5. **其他交互元素**
- **Hyperlink** 的文字颜色
- **ToolTip** 的背景色（某些样式）
- 焦点框的颜色
- 选中文本的背景色

## 在 LIV Analyzer 中的具体体现

### 当前已应用主题色的元素：
1. **工具栏按钮** - 使用 `AccentButtonStyle` 的按钮
2. **菜单选中状态** - 菜单项的悬停高亮
3. **文本框焦点边框** - 输入框获得焦点时的边框
4. **进度条** - 批处理时的进度指示
5. **选中状态** - 各种列表的选中项

### 示例代码展示

```xml
<!-- 这些元素会受主题色影响 -->

<!-- 1. 主题色按钮 -->
<Button Style="{StaticResource AccentButtonStyle}" 
        Content="主要操作"/>

<!-- 2. 获得焦点时显示主题色边框 -->
<TextBox Style="{StaticResource DefaultTextBoxStyle}"/>

<!-- 3. 选中时显示主题色背景 -->
<ListBox Style="{StaticResource DefaultListBoxStyle}">
    <ListBoxItem>选中我会显示主题色</ListBoxItem>
</ListBox>

<!-- 4. 进度条使用主题色 -->
<ProgressBar Value="50" Maximum="100" 
             Style="{StaticResource DefaultProgressBarStyle}"/>

<!-- 5. 开关打开时显示主题色 -->
<ui:ToggleSwitch IsOn="True" Header="功能开关"/>
```

## 系统资源引用

ModernWPF 使用以下系统资源来应用主题色：

```xml
<!-- 主要的主题色资源 -->
<SolidColorBrush x:Key="SystemControlHighlightAccentBrush"/>
<SolidColorBrush x:Key="SystemControlHighlightListAccentLowBrush"/>
<SolidColorBrush x:Key="SystemControlHighlightListAccentHighBrush"/>
<SolidColorBrush x:Key="SystemControlHyperlinkTextBrush"/>
<SolidColorBrush x:Key="SystemControlHighlightAltAccentBrush"/>
```

## 视觉效果对比

### 不同主题色的效果：

| UI 元素 | Windows 默认（蓝） | 活力橙 | 自然绿 |
|---------|-------------------|---------|---------|
| 主按钮 | 蓝色背景 | 橙色背景 | 绿色背景 |
| 文本框焦点 | 蓝色边框 | 橙色边框 | 绿色边框 |
| 选中项 | 蓝色高亮 | 橙色高亮 | 绿色高亮 |
| 进度条 | 蓝色进度 | 橙色进度 | 绿色进度 |
| 链接文字 | 蓝色文字 | 橙色文字 | 绿色文字 |

## 自定义应用主题色

如果想让更多元素使用主题色，可以：

```xml
<!-- 在自定义样式中引用主题色 -->
<Style x:Key="CustomBorder" TargetType="Border">
    <Setter Property="BorderBrush" 
            Value="{DynamicResource SystemControlHighlightAccentBrush}"/>
    <Setter Property="BorderThickness" Value="2"/>
</Style>

<!-- 创建基于主题色的渐变 -->
<LinearGradientBrush x:Key="AccentGradient">
    <GradientStop Color="{DynamicResource SystemAccentColor}" Offset="0"/>
    <GradientStop Color="{DynamicResource SystemAccentColorDark1}" Offset="1"/>
</LinearGradientBrush>
```

## 注意事项

1. **不是所有元素都会改变** - 背景、文字等基础颜色不受主题色影响
2. **主题色主要用于交互反馈** - 焦点、选中、悬停等状态
3. **保持适度** - 过多使用主题色会降低视觉层次感
4. **考虑对比度** - 确保主题色在明暗主题下都有良好的可见性

这就是为什么改变主题色后，您会看到按钮、选中项、焦点框等交互元素的颜色发生变化，而背景、文字等基础元素保持不变。