# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

LIV Analyzer is a WPF desktop application for analyzing laser diode Light-Current-Voltage (LIV) characteristics. It provides data visualization, analysis, and batch processing capabilities for laser testing data, built with .NET 9.0 and modern C# practices with native Fluent Design theming.

## Development Commands

### Building the Application
```bash
# Restore NuGet packages
dotnet restore

# Build the solution
dotnet build

# Build in Release mode
dotnet build --configuration Release

# Clean build artifacts
dotnet clean

# Recommended: Use main build script (5-step automated process)
BuildAndPublish.bat

# Deep clean and rebuild all projects
FullCleanAndBuild.bat

# Alternative: Clean and rebuild without deep directory cleanup
CleanAndBuild.bat
```

### Running the Application
```bash
# Run the UI project directly
dotnet run --project LIVAnalyzer.UI/LIVAnalyzer.UI.csproj

# Run in Release mode
dotnet run --project LIVAnalyzer.UI/LIVAnalyzer.UI.csproj --configuration Release

# Quick debug launch (recommended for development)
RunDebugVersion.bat

# Production-ready launcher with error handling
RunLIVAnalyzer.bat

# Safe mode launcher with additional checks
RunLIVAnalyzer_Safe.bat

# Alternative launcher scripts
StartLIVAnalyzer.bat
LIVAnalyzerLauncher.bat
```

### Testing
```bash
# Run all tests
dotnet test

# Run with detailed output
dotnet test --verbosity normal

# Run specific test project
dotnet test LIVAnalyzer.Tests/LIVAnalyzer.Tests.csproj

# Run with code coverage (requires coverage tool)
dotnet test --collect:"XPlat Code Coverage"

# Run specific test class or method
dotnet test --filter "FullyQualifiedName~LIVDataProcessorTests"
dotnet test --filter "FullyQualifiedName~DivergenceCalculationTests"
dotnet test --filter "FullyQualifiedName~FWHMCalculationTests"

# Run tests by category (if categorized)
dotnet test --filter "Category=Integration"
dotnet test --filter "Category=Unit"

# Quick test execution using batch script
TestRun.bat
```

### Publishing
```bash
# Recommended: Use main build script (includes clean, restore, build, publish, and resource copying)
BuildAndPublish.bat

# Manual publish command (used by BuildAndPublish.bat)
dotnet publish LIVAnalyzer.UI/LIVAnalyzer.UI.csproj -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true -p:IncludeNativeLibrariesForSelfExtract=true -p:PublishTrimmed=false -p:PublishReadyToRun=true -o publish-release

# Create distributable release package with versioning
CreateReleasePackage.bat

# Advanced release creation with PowerShell automation
CreateRelease_V2.1.0.ps1

# Alternative publish script
PublishSelfContained.bat

# Utility scripts for development workflow
git_commit.bat              # Git workflow automation
ConvertToIco.ps1           # Icon generation for resources
CreateIcon.ps1             # Icon creation utilities
```

## Architecture Overview

### Solution Structure

The solution follows a clean, layered architecture with clear separation of concerns:

1. **LIVAnalyzer.Models** - Domain models and data structures (pure data, no dependencies)
   - `LIVData`: Core data model containing wavelength, power, voltage data
   - `ProcessingResult`: Analysis results including spectral and LIV parameters
   - `ApplicationConfig`: Configuration model with comprehensive nested settings

2. **LIVAnalyzer.Data** - Data access layer
   - `ExcelDataLoader`: EPPlus-based Excel file loading with streaming support
   - `CsvDataLoader`: CSV file support using CsvHelper
   - Expected Excel structure: `wavelength`, `power`, `voltage` sheets
   - Optional divergence data: `HFF`, `VFF` sheets for far-field analysis

3. **LIVAnalyzer.Core** - Business logic and algorithms
   - `LIVDataProcessor`: Core analysis engine calculating spectral and LIV parameters
   - `OptimizedLIVDataProcessor`: Performance-optimized variant for large datasets
   - Uses MathNet.Numerics for numerical computations and linear algebra
   - Algorithms: threshold current, slope efficiency, FWHM, peak wavelength, maximum efficiency

4. **LIVAnalyzer.Services** - Service layer with cross-cutting concerns
   - `ConfigurationManager`: Singleton YAML-based configuration management
   - `LoggingService`: Structured logging with Serilog
   - `DocumentService`: Document management and integrated help system
   - `MemoryPoolManager`: Memory management for large dataset processing

5. **LIVAnalyzer.UI** - WPF presentation layer
   - MVVM pattern using CommunityToolkit.Mvvm with source generators
   - `MainWindowViewModel`: Primary view model orchestrating data operations
   - OxyPlot for scientific plotting with advanced charting capabilities
   - .NET 9 native Fluent Design theming with automatic light/dark mode switching

6. **LIVAnalyzer.Tests** - Unit and integration tests
   - xUnit test framework with Moq for mocking
   - Focus on core data processing logic validation
   - Real-world test data validation in `3R-自动测试数据/` folder

### Key Design Patterns

- **MVVM Pattern**: ViewModels with INotifyPropertyChanged using CommunityToolkit.Mvvm source generators
- **Dependency Injection**: Service layer abstractions for testability and modularity
- **Singleton Pattern**: ConfigurationManager ensures single configuration instance across application
- **Repository Pattern**: Data loaders abstract file format specifics (Excel vs CSV)
- **Observer Pattern**: Plot manager updates visualizations based on data model changes
- **SemaphoreSlim Pattern**: Thread-safe plot updates using `_plotUpdateSemaphore` for UI responsiveness
- **CancellationToken Pattern**: Cancellable async operations in data processing pipelines
- **Command Pattern**: `[RelayCommand]` attributes for automatic command generation and binding

### Data Flow

1. User loads Excel/CSV file through UI
2. Data loader validates and extracts data into LIVData model
3. LIVDataProcessor performs calculations and analysis
4. Results displayed in interactive OxyPlot charts
5. Export functionality saves processed results to Excel

### Build Process Architecture

The `BuildAndPublish.bat` script implements a 5-step automated build pipeline:

1. **Clean Phase**: Removes all bin/obj directories from all projects
2. **Restore Phase**: `dotnet restore` for NuGet package dependencies
3. **Build Phase**: `dotnet build --configuration Release --no-restore`
4. **Publish Phase**: Self-contained single-file executable generation with:
   - `PublishSingleFile=true` for single executable
   - `IncludeNativeLibrariesForSelfExtract=true` for native dependencies
   - `PublishTrimmed=false` to preserve reflection-dependent code
   - `PublishReadyToRun=true` for faster startup
5. **Resource Copy Phase**: Copies documentation and resource files to output directory

Final output: ~160MB self-contained executable with embedded .NET 9 runtime.

## Configuration System

Configuration stored in `%APPDATA%\LIVAnalyzer\config.yaml`:
- Data processing parameters (smoothing, interpolation, thresholds)
- Display settings (grid, legend, coordinates, themes)
- Performance limits (max file size: 500MB, max batch files: 1000)
- UI preferences (window state, last used paths)

Access via: `ConfigurationManager.Instance.GetConfig()`

## Smoothing Algorithms Integration

The project includes multiple data smoothing algorithms implemented in v2.1.0:
- **Savitzky-Golay Filter**: For noise reduction while preserving peak characteristics
- **Moving Average**: Simple smoothing for basic noise reduction
- **Gaussian Filter**: Advanced smoothing with configurable sigma values
- **Butterworth Filter**: Low-pass filtering for frequency-domain smoothing

Access through `ChartSettingsViewModel.SmoothingMethod` property with real-time preview capabilities.

## Testing Approach

```bash
# Run unit tests for data processor
dotnet test --filter "FullyQualifiedName~LIVDataProcessorTests"

# Run integration tests
dotnet test --filter "Category=Integration"
```

Test files use xUnit attributes:
- `[Fact]` for simple tests
- `[Theory]` with `[InlineData]` for parameterized tests

## Dependencies

Key NuGet packages:
- **EPPlus** (7.0.0): Excel file manipulation (NonCommercial license)
- **OxyPlot.Wpf** (2.1.2): Scientific plotting with advanced charting capabilities
- **CommunityToolkit.Mvvm** (8.2.2): MVVM framework with source generation
- **MathNet.Numerics** (5.0.0): Numerical computations and linear algebra
- **YamlDotNet** (13.7.1): Configuration management
- **Accord.Statistics** (3.8.0): Statistical analysis and curve fitting
- **Microsoft.Xaml.Behaviors.Wpf** (1.1.77): WPF behaviors and interactions
- **Serilog**: Structured logging framework
- **CsvHelper** (30.0.1): CSV file processing
- **xUnit & Moq**: Testing framework and mocking library

## Important Implementation Details

### Theme System
The application uses .NET 9 native Fluent Design theming:
- `NativeFluentThemeService`: Primary theme service using `Application.Current.ThemeMode`
- Automatic system theme detection via Windows registry
- Dynamic resource updates for charts and controls
- Theme files: `NativeFluentStyles.xaml`, `NativeFluentChartThemes.xaml`
- Legacy `FluentThemeService` available as alternative implementation

### Thread Safety
- MainWindowViewModel uses `SemaphoreSlim _plotUpdateSemaphore` for thread-safe plot updates
- Async operations support cancellation via `CancellationTokenSource`
- File processing operations are properly awaited to prevent UI blocking

### Performance Optimization
- `OptimizedLIVDataProcessor` provides performance-critical data processing
- `OptimizedPlotManager` handles efficient chart rendering
- Memory pool management via `MemoryPoolManager` for large datasets
- Lazy loading and efficient Excel reading with EPPlus streaming

### Configuration System
Configuration managed through singleton `ConfigurationManager.Instance`:
```csharp
var config = ConfigurationManager.Instance.GetConfig();
var smoothingEnabled = config.DataProcessing.Smoothing.EnableByDefault;
```

## Performance Considerations

- Lazy loading for large datasets
- Configurable data smoothing using signal processing
- Batch processing with progress reporting and cancellation support
- Memory-efficient Excel reading with EPPlus streaming API
- Optimized data processor variants for performance-critical scenarios
- SemaphoreSlim for thread-safe operations without blocking UI
- Proper async/await patterns throughout the application

## File Format Details

### Excel File Structure
Required worksheets:
- `wavelength`: Columns [Wavelength (nm), Intensity]
- `power`: Columns [Current (A), Power (W)]
- `voltage`: Columns [Current (A), Voltage (V)]

Optional worksheets for divergence analysis:
- `HFF`: Horizontal far field data
- `VFF`: Vertical far field data

### CSV File Format
Standard format with headers:
- Wavelength, Intensity, Current, Power, Voltage

## Error Handling

The application includes comprehensive error handling:
- File format validation before processing
- Graceful handling of missing or malformed data
- User-friendly error dialogs with actionable messages
- Detailed error logging to `%APPDATA%\LIVAnalyzer\Logs\`

## Deployment Notes

The published executable is a self-contained single file (~160MB) that includes:
- .NET 9.0 runtime with enhanced performance and native Fluent Design support
- All dependencies
- Configuration templates

No additional runtime installation required on Windows 10/11 x64 systems.

## Development Notes

### Code Style and Patterns
- Project uses nullable reference types (`<Nullable>enable</Nullable>`)
- Assembly info generation is disabled to prevent conflicts (`<GenerateAssemblyInfo>false</GenerateAssemblyInfo>`)
- Common nullable warnings are suppressed in project files
- ViewModels inherit from `ObservableObject` (CommunityToolkit.Mvvm)
- Commands use `[RelayCommand]` attribute for code generation

### Key Architecture Files
- `LIVAnalyzer.Core/Processors/LIVDataProcessor.cs`: Core analysis algorithms
- `LIVAnalyzer.UI/ViewModels/MainWindowViewModel.cs`: Primary MVVM orchestration
- `LIVAnalyzer.Services/Configuration/ConfigurationManager.cs`: Singleton config management
- `LIVAnalyzer.Data/Loaders/ExcelDataLoader.cs`: EPPlus-based Excel file handling

### Debugging and Development
- Use `RunDebugVersion.bat` for quick debugging sessions
- Test data available in `3R-自动测试数据/` folder for development testing
- Real-world test files help validate algorithm accuracy
- Multilingual launcher support: `运行向导.bat` (Chinese), `运行向导_EN.bat` (English)
- Process management: `关闭所有进程.bat` for terminating related processes
- Environment validation: `环境检查.bat`, `项目检查.bat` for system verification

### Development Workflow Scripts
Multiple launcher variants provide flexibility for different development scenarios:
- `快速启动.bat`, `简单启动.bat`: Quick launch options
- `修复启动.bat`, `最终修复.bat`: Recovery and repair modes
- `启动LIV分析工具.bat`: Named launcher for production use

## Framework Information

### .NET 9 Implementation
The project has been successfully upgraded to .NET 9 and uses modern features:
- **Native Fluent Design**: Built-in `Application.Current.ThemeMode` for theming
- **Performance**: Enhanced GC and JIT compilation optimizations
- **Security**: Latest security updates and patches
- **WPF Enhancements**: Native Fluent theme support and improved rendering
- **Developer Experience**: Better tooling and diagnostic capabilities

**Migration Notes:**
- Successfully migrated from .NET 6.0 to .NET 9 (see `NET9_升级指南.md` for historical reference)
- All major packages verified compatible with .NET 9
- Native Fluent Design implementation replaces ModernWpfUI dependency
- Enhanced performance for scientific computing operations