export function movingAverage(values: number[], windowSize: number): number[] {
  const n = values.length;
  if (windowSize <= 1 || n === 0) return values.slice();
  const w = Math.min(windowSize, n);
  const out = new Array<number>(n);
  let sum = 0;
  for (let i = 0; i < n; i++) {
    sum += values[i];
    if (i >= w) sum -= values[i - w];
    out[i] = i >= w - 1 ? sum / w : values[i];
  }
  return out;
}

/**
 * 中值滤波器 - 对脉冲噪声有很好的抑制效果
 */
export function medianFilter(values: number[], windowSize: number): number[] {
  const n = values.length;
  if (n === 0) return values.slice();

  // 参数验证
  windowSize = Math.max(3, Math.min(windowSize, n));
  if (windowSize % 2 === 0) windowSize++;

  const halfWindow = Math.floor(windowSize / 2);
  const result = new Array<number>(n);

  for (let i = 0; i < n; i++) {
    const window: number[] = [];

    for (let j = -halfWindow; j <= halfWindow; j++) {
      let index = i + j;

      // 边界处理：镜像填充
      if (index < 0) {
        index = -index;
      } else if (index >= n) {
        index = 2 * n - index - 2;
      }

      if (index >= 0 && index < n && Number.isFinite(values[index])) {
        window.push(values[index]);
      }
    }

    if (window.length > 0) {
      window.sort((a, b) => a - b);
      const medianIndex = Math.floor(window.length / 2);
      result[i] = window[medianIndex];
    } else {
      result[i] = values[i];
    }
  }

  return result;
}

/**
 * 自适应平滑 - 根据局部方差自动调整平滑强度
 */
export function adaptiveSmooth(values: number[], baseWindowSize: number = 5, maxWindowSize: number = 15): number[] {
  const n = values.length;
  if (n === 0) return values.slice();

  const result = new Array<number>(n);

  for (let i = 0; i < n; i++) {
    // 计算局部方差
    const localVariance = calculateLocalVariance(values, i, baseWindowSize);

    // 根据方差调整窗口大小
    const adaptiveWindow = Math.min(maxWindowSize,
      Math.max(baseWindowSize, Math.floor(baseWindowSize * (1 + localVariance * 2))));

    // 应用移动平均
    const halfWindow = Math.floor(adaptiveWindow / 2);
    let sum = 0;
    let count = 0;

    for (let j = -halfWindow; j <= halfWindow; j++) {
      let index = i + j;

      if (index < 0) {
        index = -index;
      } else if (index >= n) {
        index = 2 * n - index - 2;
      }

      if (index >= 0 && index < n && Number.isFinite(values[index])) {
        sum += values[index];
        count++;
      }
    }

    result[i] = count > 0 ? sum / count : values[i];
  }

  return result;
}

/**
 * 计算局部方差
 */
function calculateLocalVariance(values: number[], center: number, windowSize: number): number {
  const halfWindow = Math.floor(windowSize / 2);
  const window: number[] = [];

  for (let j = -halfWindow; j <= halfWindow; j++) {
    let index = center + j;

    if (index < 0) {
      index = -index;
    } else if (index >= values.length) {
      index = 2 * values.length - index - 2;
    }

    if (index >= 0 && index < values.length && Number.isFinite(values[index])) {
      window.push(values[index]);
    }
  }

  if (window.length < 2) return 0;

  const mean = window.reduce((sum, val) => sum + val, 0) / window.length;
  const variance = window.reduce((sum, val) => sum + (val - mean) * (val - mean), 0) / (window.length - 1);

  return Math.sqrt(variance) / Math.abs(mean + 1e-10); // 归一化方差
}

export function savitzkyGolay(values: number[], windowSize: number, polynomial: number): number[] {
  const n = values.length;
  if (n === 0) return values.slice();

  // 参数验证和修正
  if (windowSize % 2 === 0) windowSize++;
  windowSize = Math.max(3, Math.min(windowSize, n));
  polynomial = Math.max(1, Math.min(polynomial, windowSize - 1));

  const halfWindow = Math.floor(windowSize / 2);
  const coefficients = calculateSavitzkyGolayCoefficients(windowSize, polynomial);

  if (!coefficients) {
    // 降级到移动平均
    return movingAverage(values, windowSize);
  }

  const result = new Array<number>(n);

  for (let i = 0; i < n; i++) {
    let sum = 0;
    let validCount = 0;

    for (let j = -halfWindow; j <= halfWindow; j++) {
      let index = i + j;

      // 边界处理：镜像填充（与桌面版对齐）
      if (index < 0) {
        index = -index;
      } else if (index >= n) {
        index = 2 * n - index - 2;
      }

      if (index >= 0 && index < n && Number.isFinite(values[index])) {
        sum += coefficients[j + halfWindow] * values[index];
        validCount++;
      }
    }

    result[i] = validCount > halfWindow ? sum : values[i];
  }

  return result;
}

/**
 * 计算Savitzky-Golay滤波器系数
 * 使用最小二乘法求解多项式拟合问题
 */
function calculateSavitzkyGolayCoefficients(windowSize: number, polynomialOrder: number): number[] | null {
  const halfWindow = Math.floor(windowSize / 2);

  try {
    // 构建Vandermonde矩阵
    const A: number[][] = [];
    for (let i = -halfWindow; i <= halfWindow; i++) {
      const row: number[] = [];
      for (let j = 0; j <= polynomialOrder; j++) {
        row.push(Math.pow(i, j));
      }
      A.push(row);
    }

    // 构建目标向量（只有中心点为1，其他为0）
    const b = new Array(windowSize).fill(0);
    b[halfWindow] = 1;

    // 求解最小二乘问题：min ||Ax - b||²
    // 使用正规方程：(A^T A) x = A^T b
    const AtA = matrixMultiply(transpose(A), A);
    const Atb = matrixVectorMultiply(transpose(A), b);

    const coeffs = solveLinearSystem(AtA, Atb);
    if (!coeffs) return null;

    // 计算滤波器系数
    const result = new Array<number>(windowSize);
    for (let i = 0; i < windowSize; i++) {
      let sum = 0;
      for (let j = 0; j <= polynomialOrder; j++) {
        sum += coeffs[j] * Math.pow(i - halfWindow, j);
      }
      result[i] = sum;
    }

    return result;
  } catch (error) {
    console.warn('Savitzky-Golay coefficient calculation failed:', error);
    return null;
  }
}

// 矩阵运算辅助函数
function transpose(matrix: number[][]): number[][] {
  const rows = matrix.length;
  const cols = matrix[0].length;
  const result: number[][] = [];

  for (let j = 0; j < cols; j++) {
    const row: number[] = [];
    for (let i = 0; i < rows; i++) {
      row.push(matrix[i][j]);
    }
    result.push(row);
  }

  return result;
}

function matrixMultiply(A: number[][], B: number[][]): number[][] {
  const rowsA = A.length;
  const colsA = A[0].length;
  const colsB = B[0].length;
  const result: number[][] = [];

  for (let i = 0; i < rowsA; i++) {
    const row: number[] = [];
    for (let j = 0; j < colsB; j++) {
      let sum = 0;
      for (let k = 0; k < colsA; k++) {
        sum += A[i][k] * B[k][j];
      }
      row.push(sum);
    }
    result.push(row);
  }

  return result;
}

function matrixVectorMultiply(A: number[][], b: number[]): number[] {
  const rows = A.length;
  const result: number[] = [];

  for (let i = 0; i < rows; i++) {
    let sum = 0;
    for (let j = 0; j < A[i].length; j++) {
      sum += A[i][j] * b[j];
    }
    result.push(sum);
  }

  return result;
}

function solveLinearSystem(A: number[][], b: number[]): number[] | null {
  const n = A.length;

  // 使用高斯消元法求解
  const augmented: number[][] = [];
  for (let i = 0; i < n; i++) {
    augmented.push([...A[i], b[i]]);
  }

  // 前向消元
  for (let i = 0; i < n; i++) {
    // 寻找主元
    let maxRow = i;
    for (let k = i + 1; k < n; k++) {
      if (Math.abs(augmented[k][i]) > Math.abs(augmented[maxRow][i])) {
        maxRow = k;
      }
    }

    // 交换行
    [augmented[i], augmented[maxRow]] = [augmented[maxRow], augmented[i]];

    // 检查奇异性
    if (Math.abs(augmented[i][i]) < 1e-12) {
      return null;
    }

    // 消元
    for (let k = i + 1; k < n; k++) {
      const factor = augmented[k][i] / augmented[i][i];
      for (let j = i; j <= n; j++) {
        augmented[k][j] -= factor * augmented[i][j];
      }
    }
  }

  // 回代
  const x = new Array<number>(n);
  for (let i = n - 1; i >= 0; i--) {
    x[i] = augmented[i][n];
    for (let j = i + 1; j < n; j++) {
      x[i] -= augmented[i][j] * x[j];
    }
    x[i] /= augmented[i][i];
  }

  return x;
}

export function gaussianSmooth(values: number[], windowSize: number, sigma?: number): number[] {
  const n = values.length;
  if (n === 0) return values.slice();

  // 参数验证和修正
  windowSize = Math.max(3, Math.min(windowSize, n));
  if (windowSize % 2 === 0) windowSize++;

  const halfWindow = Math.floor(windowSize / 2);

  // 自动计算sigma（与桌面版对齐）
  const effectiveSigma = sigma ?? Math.max(0.5, windowSize / 6.0);

  // 生成高斯核
  const kernel = generateGaussianKernel(windowSize, effectiveSigma);

  const result = new Array<number>(n);

  for (let i = 0; i < n; i++) {
    let sum = 0;
    let weightSum = 0;

    for (let j = -halfWindow; j <= halfWindow; j++) {
      let index = i + j;

      // 边界处理：镜像填充（与桌面版对齐）
      if (index < 0) {
        index = -index;
      } else if (index >= n) {
        index = 2 * n - index - 2;
      }

      if (index >= 0 && index < n && Number.isFinite(values[index])) {
        const weight = kernel[j + halfWindow];
        sum += values[index] * weight;
        weightSum += weight;
      }
    }

    result[i] = weightSum > 0 ? sum / weightSum : values[i];
  }

  return result;
}

/**
 * 生成归一化的高斯核
 */
function generateGaussianKernel(windowSize: number, sigma: number): number[] {
  const halfWindow = Math.floor(windowSize / 2);
  const kernel = new Array<number>(windowSize);
  let sum = 0;

  // 计算高斯权重
  for (let i = -halfWindow; i <= halfWindow; i++) {
    const weight = Math.exp(-(i * i) / (2 * sigma * sigma));
    kernel[i + halfWindow] = weight;
    sum += weight;
  }

  // 归一化
  for (let i = 0; i < windowSize; i++) {
    kernel[i] /= sum;
  }

  return kernel;
}

export function butterworthLowpass(values: number[], cutoff: number, order: number = 2): number[] {
  const n = values.length;
  if (n === 0) return values.slice();

  // 参数验证
  const fc = Math.min(0.49, Math.max(0.001, cutoff));
  const filterOrder = Math.max(1, Math.min(order, 4)); // 限制阶数

  if (filterOrder === 1) {
    // 一阶IIR低通滤波器
    return firstOrderLowpass(values, fc);
  } else {
    // 二阶及以上使用双线性变换设计
    return higherOrderButterworthLowpass(values, fc, filterOrder);
  }
}

/**
 * 一阶IIR低通滤波器
 */
function firstOrderLowpass(values: number[], cutoff: number): number[] {
  const n = values.length;
  const result = new Array<number>(n);

  // 计算滤波器系数（双线性变换）
  const wc = Math.tan(Math.PI * cutoff);
  const k1 = Math.sqrt(2) * wc;
  const k2 = wc * wc;
  const a0 = k2 + k1 + 1;
  const a1 = 2 * (k2 - 1) / a0;
  const a2 = (k2 - k1 + 1) / a0;
  const b0 = k2 / a0;
  const b1 = 2 * k2 / a0;
  const b2 = k2 / a0;

  // 初始化
  result[0] = values[0];
  if (n > 1) {
    result[1] = values[1];
  }

  // 应用滤波器
  for (let i = 2; i < n; i++) {
    result[i] = b0 * values[i] + b1 * values[i - 1] + b2 * values[i - 2]
                - a1 * result[i - 1] - a2 * result[i - 2];
  }

  return result;
}

/**
 * 高阶Butterworth低通滤波器
 */
function higherOrderButterworthLowpass(values: number[], cutoff: number, order: number): number[] {
  const n = values.length;
  if (n < order + 1) {
    // 数据点太少，降级到一阶
    return firstOrderLowpass(values, cutoff);
  }

  // 设计Butterworth滤波器系数
  const { a, b } = designButterworthCoefficients(cutoff, order);

  const result = new Array<number>(n);

  // 初始化（使用输入值）
  for (let i = 0; i < Math.min(order, n); i++) {
    result[i] = values[i];
  }

  // 应用滤波器
  for (let i = order; i < n; i++) {
    let sum = 0;

    // 前馈项
    for (let j = 0; j < b.length; j++) {
      if (i - j >= 0) {
        sum += b[j] * values[i - j];
      }
    }

    // 反馈项
    for (let j = 1; j < a.length; j++) {
      if (i - j >= 0) {
        sum -= a[j] * result[i - j];
      }
    }

    result[i] = sum / a[0];
  }

  return result;
}

/**
 * 设计Butterworth滤波器系数
 */
function designButterworthCoefficients(cutoff: number, order: number): { a: number[], b: number[] } {
  // 简化的Butterworth设计（二阶为例）
  if (order === 2) {
    const wc = Math.tan(Math.PI * cutoff);
    const k1 = Math.sqrt(2) * wc;
    const k2 = wc * wc;
    const norm = k2 + k1 + 1;

    return {
      a: [1, 2 * (k2 - 1) / norm, (k2 - k1 + 1) / norm],
      b: [k2 / norm, 2 * k2 / norm, k2 / norm]
    };
  } else {
    // 对于其他阶数，使用简化的一阶级联
    const wc = 2 * Math.tan(Math.PI * cutoff / 2);
    const norm = 2 + wc;

    return {
      a: [1, (wc - 2) / norm],
      b: [wc / norm, wc / norm]
    };
  }
}


