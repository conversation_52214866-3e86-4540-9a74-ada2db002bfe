export function movingAverage(values: number[], windowSize: number): number[] {
  const n = values.length;
  if (windowSize <= 1 || n === 0) return values.slice();
  const w = Math.min(windowSize, n);
  const out = new Array<number>(n);
  let sum = 0;
  for (let i = 0; i < n; i++) {
    sum += values[i];
    if (i >= w) sum -= values[i - w];
    out[i] = i >= w - 1 ? sum / w : values[i];
  }
  return out;
}

export function savitzkyGolay(values: number[], windowSize: number, polynomial: number): number[] {
  // 简化实现：当参数异常时退化为 moving average
  if (windowSize < 3 || polynomial < 2 || windowSize % 2 === 0) return movingAverage(values, Math.max(3, windowSize | 0));
  const half = Math.floor(windowSize / 2);
  const out = values.slice();
  for (let i = half; i < values.length - half; i++) {
    // 对称窗口的二次多项式拟合简化：中心加权求和（系数可进一步优化）
    let acc = 0;
    let denom = 0;
    for (let k = -half; k <= half; k++) {
      const idx = i + k;
      const weight = 1; // 占位：可替换为预计算的 SG 系数
      acc += values[idx] * weight;
      denom += weight;
    }
    out[i] = denom > 0 ? acc / denom : values[i];
  }
  return out;
}

export function gaussianSmooth(values: number[], windowSize: number, sigma?: number): number[] {
  const n = values.length;
  if (windowSize < 3 || n === 0) return values.slice();
  const w = windowSize | 0;
  const half = Math.max(1, Math.floor(w / 2));
  const s = sigma ?? Math.max(1, w / 3);
  const kernel: number[] = [];
  let norm = 0;
  for (let k = -half; k <= half; k++) {
    const val = Math.exp(-(k * k) / (2 * s * s));
    kernel.push(val);
    norm += val;
  }
  for (let i = 0; i < kernel.length; i++) kernel[i] /= norm;
  const out = values.slice();
  for (let i = 0; i < n; i++) {
    let acc = 0;
    for (let k = -half; k <= half; k++) {
      const idx = Math.min(n - 1, Math.max(0, i + k));
      acc += values[idx] * kernel[k + half];
    }
    out[i] = acc;
  }
  return out;
}

export function butterworthLowpass(values: number[], cutoff: number): number[] {
  // 简化一阶低通（IIR），cutoff: 0..0.5（相对 Nyquist）
  const n = values.length;
  if (n === 0) return values.slice();
  const fc = Math.min(0.49, Math.max(0.001, cutoff));
  // 预估 alpha（简单映射）
  const alpha = fc; // 简化映射，后续可用双线性变换精确化
  const out = new Array<number>(n);
  out[0] = values[0];
  for (let i = 1; i < n; i++) {
    out[i] = out[i - 1] + alpha * (values[i] - out[i - 1]);
  }
  return out;
}


