@echo off
echo Publishing LIV Analyzer as self-contained application...
cd /d "E:\01LaserPackage\software\LIV_Analyzer\CSharp_Version"

REM Clean previous publish
rd /s /q "LIVAnalyzer.UI\bin\Release\net6.0-windows\publish" 2>nul

REM Publish as self-contained application
dotnet publish LIVAnalyzer.UI\LIVAnalyzer.UI.csproj -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true -p:IncludeNativeLibrariesForSelfExtract=true

echo.
echo Publish completed!
echo Output location: LIVAnalyzer.UI\bin\Release\net6.0-windows\win-x64\publish\
pause