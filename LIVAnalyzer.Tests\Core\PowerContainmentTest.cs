using System;
using System.Collections.Generic;
using System.Linq;
using Xunit;
using LIVAnalyzer.Core.Processors;
using LIVAnalyzer.Models;

namespace LIVAnalyzer.Tests.Core
{
    public class PowerContainmentTest
    {
        [Fact]
        public void TestGaussianBeamPowerContainment()
        {
            // Create a Gaussian beam profile
            // For a Gaussian beam, the intensity I(θ) = I0 * exp(-2 * (θ/θ0)²)
            // Where θ0 is the 1/e² radius
            
            var angles = new List<double>();
            var intensities = new List<double>();
            
            double theta0 = 10.0; // 1/e² half-angle in degrees
            double I0 = 1.0; // Peak intensity
            
            // Generate data from -5*theta0 to +5*theta0
            for (double theta = -5 * theta0; theta <= 5 * theta0; theta += 0.1)
            {
                angles.Add(theta);
                double intensity = I0 * Math.Exp(-2 * Math.Pow(theta / theta0, 2));
                intensities.Add(intensity);
            }
            
            // Create divergence data
            var angleData = new List<DataPoint>();
            var intensityData = new List<DataPoint>();
            for (int i = 0; i < angles.Count; i++)
            {
                angleData.Add(new DataPoint(angles[i], intensities[i]));
                intensityData.Add(new DataPoint(angles[i], intensities[i]));
            }
            
            // Process with DivergenceProcessor
            var processor = new DivergenceProcessor();
            var result = processor.CalculateDivergence(angleData, intensityData);
            
            // For a Gaussian beam:
            // - At 1/e² intensity (13.53%), the full width should be 2*theta0 = 20°
            // - The power contained within 1/e² width should be approximately 86.5% for ideal Gaussian
            
            Assert.NotNull(result);
            Assert.True(result.FW1e2 > 0, "FW(1/e²) should be calculated");
            Assert.True(Math.Abs(result.FW1e2 - 2 * theta0) < 0.5, 
                $"FW(1/e²) should be close to 2*theta0 = {2*theta0}°, but got {result.FW1e2}°");
            
            // The key test: power containment should be around 86.5% for an ideal Gaussian beam
            Assert.True(result.FW1e2PowerContainment > 0, "Power containment should be calculated");
            Assert.True(Math.Abs(result.FW1e2PowerContainment - 0.865) < 0.02, 
                $"Power containment should be around 86.5% for Gaussian beam, but got {result.FW1e2PowerContainment:P1}");
        }
        
        [Fact]
        public void TestUniformBeamPowerContainment()
        {
            // Create a uniform (flat-top) beam profile
            var angleData = new List<DataPoint>();
            var intensityData = new List<DataPoint>();
            double beamWidth = 20.0; // degrees
            
            // Generate uniform beam from -30° to +30°
            for (double theta = -30; theta <= 30; theta += 0.5)
            {
                double intensity = (Math.Abs(theta) <= beamWidth / 2) ? 1.0 : 0.0;
                angleData.Add(new DataPoint(theta, intensity));
                intensityData.Add(new DataPoint(theta, intensity));
            }
            
            var processor = new DivergenceProcessor();
            var result = processor.CalculateDivergence(angleData, intensityData);
            
            // For a uniform beam, the FW(1/e²) calculation is looking for where intensity drops to 13.53%
            // Since it's a step function, this should capture the full beam width
            Assert.NotNull(result);
            Assert.True(result.FW1e2 > 0, "FW(1/e²) should be calculated");
            
            // Power containment should be 100% since all power is within the beam width
            Assert.True(result.FW1e2PowerContainment > 0.95, 
                $"Power containment for uniform beam should be close to 100%, but got {result.FW1e2PowerContainment:P1}");
        }
    }
}