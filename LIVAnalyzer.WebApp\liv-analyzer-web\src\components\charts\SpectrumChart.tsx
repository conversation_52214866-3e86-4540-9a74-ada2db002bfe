import { useEffect, useRef } from 'react';
let Plot: any;
import { useAppStore } from '../../state/store';
import { movingAverage, savitzkyGolay, gaussianSmooth, butterworthLowpass } from '../../services/smoothing';
import ChartToolbar from "../ChartToolbar";

export default function SpectrumChart() {
  const ref = useRef<HTMLDivElement | null>(null);
  const { data } = useAppStore();

  useEffect(() => {
    if (!ref.current) return;
    ref.current.innerHTML = '';
    if (!Plot) {
      import('@observablehq/plot').then(mod => { Plot = mod; render(); });
      return;
    }
    render();

  }, [data]);

  function render() {
    if (!ref.current || !Plot) return;
    const spectral = data?.wavelength;
    if (!spectral || spectral.wavelength.length === 0 || spectral.intensity.length === 0) {
      ref.current.textContent = '暂无光谱数据';
      return;
    }

    // 平滑（预览）
    let yVals = spectral.intensity.slice();
    const cfg = useAppStore.getState().processingConfig;
    if (cfg.smoothing?.enabled) {
      if (cfg.smoothing.method === 'moving-average') yVals = movingAverage(yVals, cfg.smoothing.windowSize ?? 5);
      else if (cfg.smoothing.method === 'sg') yVals = savitzkyGolay(yVals, cfg.smoothing.windowSize ?? 5, cfg.smoothing.polynomialOrder ?? 3);
      else if (cfg.smoothing.method === 'gaussian') yVals = gaussianSmooth(yVals, cfg.smoothing.windowSize ?? 5);
      else if (cfg.smoothing.method === 'butterworth') yVals = butterworthLowpass(yVals, cfg.smoothing.cutoff ?? 0.15);
    }
    const series = spectral.wavelength.map((x, i) => ({ x, y: yVals[i] }));

    const plot = Plot.plot({
      marginLeft: 50,
      marginBottom: 40,
      style: { background: 'transparent' },
      x: { label: 'Wavelength (nm)' },
      y: { label: 'Intensity' },
      marks: [
        ...(useAppStore.getState().displayConfig.showSpectrumLine ?? true ? [
          Plot.line(series, { x: 'x', y: 'y', stroke: 'rgb(99,102,241)', title: 'Spectrum' }),
          Plot.dot(series, { x: 'x', y: 'y', r: 1.5, fill: 'rgb(99,102,241)', title: (d: any) => `λ=${d.x}\nI=${d.y}` })
        ] : []),
        Plot.ruleY([0])
      ],
    });

    ref.current.appendChild(plot);
    import('../../services/zoom').then(z => { (window as any).__spectrum_zoom = z.enableZoomPan(ref.current!); });
    return () => {
      plot.remove();
    };
  }

  return (
    <div className="space-y-2">
      <ChartToolbar title="Spectrum_chart" container={ref.current} onReset={() => (window as any).__spectrum_zoom?.then((r: any) => r.reset())} />
      <div ref={ref} className="w-full overflow-auto" />
    </div>
  );
}


