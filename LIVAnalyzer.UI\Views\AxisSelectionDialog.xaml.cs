using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using OxyPlot;
using OxyPlot.Axes;

namespace LIVAnalyzer.UI.Views
{
    /// <summary>
    /// 轴信息显示类
    /// </summary>
    public class AxisDisplayInfo
    {
        public Axis Axis { get; set; }
        public string DisplayName { get; set; }
        public string RangeInfo { get; set; }
        public string PositionText { get; set; }
        public string Icon { get; set; }
    }

    /// <summary>
    /// 轴选择对话框
    /// </summary>
    public partial class AxisSelectionDialog : Window
    {
        public Axis SelectedAxis { get; private set; }

        public AxisSelectionDialog(PlotModel plotModel, string chartName)
        {
            InitializeComponent();
            
            Title = $"设置 {chartName} 的坐标轴";
            LoadAxes(plotModel);
        }

        private void LoadAxes(PlotModel plotModel)
        {
            var axisInfos = new List<AxisDisplayInfo>();

            foreach (var axis in plotModel.Axes.Where(a => a.IsAxisVisible))
            {
                var displayInfo = new AxisDisplayInfo
                {
                    Axis = axis,
                    DisplayName = GetAxisDisplayName(axis),
                    RangeInfo = $"范围: {axis.ActualMinimum:F2} ~ {axis.ActualMaximum:F2}",
                    PositionText = GetPositionText(axis),
                    Icon = GetAxisIcon(axis)
                };
                
                axisInfos.Add(displayInfo);
            }

            AxisListBox.ItemsSource = axisInfos;
            
            // 默认选择第一个轴
            if (axisInfos.Count > 0)
            {
                AxisListBox.SelectedIndex = 0;
            }
        }

        private string GetAxisDisplayName(Axis axis)
        {
            if (!string.IsNullOrEmpty(axis.Title))
            {
                return axis.Title;
            }

            return axis.Position switch
            {
                AxisPosition.Bottom => "底部X轴",
                AxisPosition.Top => "顶部X轴",
                AxisPosition.Left => "左侧Y轴",
                AxisPosition.Right when axis.PositionTier == 0 => "右侧Y轴",
                AxisPosition.Right when axis.PositionTier == 1 => "右侧Y轴 (第2层)",
                AxisPosition.Right when axis.PositionTier == 2 => "右侧Y轴 (第3层)",
                AxisPosition.Right => $"右侧Y轴 (第{axis.PositionTier + 1}层)",
                _ => "未知轴"
            };
        }

        private string GetPositionText(Axis axis)
        {
            var position = axis.Position switch
            {
                AxisPosition.Bottom => "底部",
                AxisPosition.Top => "顶部",
                AxisPosition.Left => "左侧",
                AxisPosition.Right => "右侧",
                _ => "未知"
            };

            if (axis.Position == AxisPosition.Right && axis.PositionTier > 0)
            {
                position += $" T{axis.PositionTier}";
            }

            return position;
        }

        private string GetAxisIcon(Axis axis)
        {
            return axis.Position switch
            {
                AxisPosition.Bottom => "📐", // 底部X轴
                AxisPosition.Top => "📏", // 顶部X轴
                AxisPosition.Left => "📊", // 左侧Y轴
                AxisPosition.Right => "📈", // 右侧Y轴
                _ => "📋"
            };
        }

        private void OkButton_Click(object sender, RoutedEventArgs e)
        {
            if (AxisListBox.SelectedItem is AxisDisplayInfo selectedInfo)
            {
                SelectedAxis = selectedInfo.Axis;
                DialogResult = true;
            }
            else
            {
                MessageBox.Show("请选择一个坐标轴。", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
        }
    }
}
