<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>

    <!-- 抑制常见警告 -->
    <NoWarn>$(NoWarn);CS8618;CS8600;CS8603;CS8619;CS1998</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="MathNet.Numerics" Version="5.0.0" />
    <PackageReference Include="Accord.Statistics" Version="3.8.0" />
    <PackageReference Include="EPPlus" Version="7.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\LIVAnalyzer.Models\LIVAnalyzer.Models.csproj" />
    <ProjectReference Include="..\LIVAnalyzer.Data\LIVAnalyzer.Data.csproj" />
    <ProjectReference Include="..\LIVAnalyzer.Services\LIVAnalyzer.Services.csproj" />
  </ItemGroup>

</Project>