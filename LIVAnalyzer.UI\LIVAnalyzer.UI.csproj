<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net9.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWPF>true</UseWPF>
    <UseWindowsForms>true</UseWindowsForms>
    <AssemblyName>LIVAnalyzer</AssemblyName>

    <!-- 最终解决方案：完全禁用Assembly特性自动生成 -->
    <!-- 这是处理顽固Assembly冲突问题的最彻底方法 -->
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>

    <!-- 抑制平台兼容性警告，因为这是Windows专用的WPF应用程序 -->
    <NoWarn>$(NoWarn);CA1416</NoWarn>

    <!-- 抑制其他常见的可空引用警告和WPF主题API警告 -->
    <NoWarn>$(NoWarn);CS8618;CS8600;CS8603;CS8619;CS1998;WPF0001</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="OxyPlot.Wpf" Version="2.1.2" />
    <PackageReference Include="CommunityToolkit.Mvvm" Version="8.2.2" />
    <PackageReference Include="Microsoft.Xaml.Behaviors.Wpf" Version="1.1.77" />
  </ItemGroup>

  <ItemGroup>
    <Resource Include="Resources\Icons\app-icon-16.png" />
    <Resource Include="Resources\Icons\app-icon-32.png" />
    <Resource Include="Resources\Icons\app-icon-64.png" />
    <Resource Include="Resources\Icons\app-icon-128.png" />
    <Resource Include="Resources\Icons\app-icon-256.png" />
    <Content Include="Resources\Icons\app-icon.ico" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\LIVAnalyzer.Core\LIVAnalyzer.Core.csproj" />
    <ProjectReference Include="..\LIVAnalyzer.Data\LIVAnalyzer.Data.csproj" />
    <ProjectReference Include="..\LIVAnalyzer.Models\LIVAnalyzer.Models.csproj" />
    <ProjectReference Include="..\LIVAnalyzer.Services\LIVAnalyzer.Services.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Resource Include="Resources\weblogo.svg" />
    <Resource Include="Resources\app_icon.svg" />
    <Resource Include="Resources\app_icon_simple.svg" />
    <Resource Include="Resources\app_icon_32.svg" />
  </ItemGroup>

</Project>