import { useEffect, useRef, useState, useCallback } from 'react';
import { useAppStore } from '../../state/store';

interface InteractiveChartProps {
  title?: string;
  children: React.ReactNode;
  onRender?: (container: HTMLElement) => void;
  enableZoom?: boolean;
  enablePan?: boolean;
  enableCrosshair?: boolean;
}

interface ViewState {
  xMin?: number;
  xMax?: number;
  yMin?: number;
  yMax?: number;
  scale: number;
  offsetX: number;
  offsetY: number;
}

export default function InteractiveChart({ 
  title, 
  children, 
  onRender, 
  enableZoom = true, 
  enablePan = true,
  enableCrosshair = true 
}: InteractiveChartProps) {
  const containerRef = useRef<HTMLDivElement | null>(null);
  const overlayRef = useRef<HTMLDivElement | null>(null);
  const [viewState, setViewState] = useState<ViewState>({
    scale: 1,
    offsetX: 0,
    offsetY: 0
  });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [crosshair, setCrosshair] = useState<{ x: number; y: number; visible: boolean }>({
    x: 0,
    y: 0,
    visible: false
  });

  const resetView = useCallback(() => {
    setViewState({
      scale: 1,
      offsetX: 0,
      offsetY: 0
    });
  }, []);

  const zoomIn = useCallback(() => {
    setViewState(prev => ({
      ...prev,
      scale: Math.min(prev.scale * 1.2, 10)
    }));
  }, []);

  const zoomOut = useCallback(() => {
    setViewState(prev => ({
      ...prev,
      scale: Math.max(prev.scale / 1.2, 0.1)
    }));
  }, []);

  // 处理鼠标滚轮缩放
  const handleWheel = useCallback((e: WheelEvent) => {
    if (!enableZoom) return;
    e.preventDefault();
    
    const rect = containerRef.current?.getBoundingClientRect();
    if (!rect) return;

    const mouseX = e.clientX - rect.left;
    const mouseY = e.clientY - rect.top;
    
    const zoomFactor = e.deltaY > 0 ? 0.9 : 1.1;
    
    setViewState(prev => {
      const newScale = Math.max(0.1, Math.min(10, prev.scale * zoomFactor));
      const scaleChange = newScale / prev.scale;
      
      return {
        ...prev,
        scale: newScale,
        offsetX: mouseX - (mouseX - prev.offsetX) * scaleChange,
        offsetY: mouseY - (mouseY - prev.offsetY) * scaleChange
      };
    });
  }, [enableZoom]);

  // 处理鼠标拖拽平移
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (!enablePan) return;
    setIsDragging(true);
    setDragStart({ x: e.clientX - viewState.offsetX, y: e.clientY - viewState.offsetY });
  }, [enablePan, viewState.offsetX, viewState.offsetY]);

  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    const rect = containerRef.current?.getBoundingClientRect();
    if (!rect) return;

    const mouseX = e.clientX - rect.left;
    const mouseY = e.clientY - rect.top;

    // 更新十字线
    if (enableCrosshair) {
      setCrosshair({
        x: mouseX,
        y: mouseY,
        visible: true
      });
    }

    // 处理拖拽
    if (isDragging && enablePan) {
      setViewState(prev => ({
        ...prev,
        offsetX: e.clientX - dragStart.x,
        offsetY: e.clientY - dragStart.y
      }));
    }
  }, [isDragging, dragStart, enablePan, enableCrosshair]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  const handleMouseLeave = useCallback(() => {
    setIsDragging(false);
    setCrosshair(prev => ({ ...prev, visible: false }));
  }, []);

  // 设置事件监听器
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    container.addEventListener('wheel', handleWheel, { passive: false });
    
    return () => {
      container.removeEventListener('wheel', handleWheel);
    };
  }, [handleWheel]);

  // 渲染图表
  useEffect(() => {
    if (containerRef.current && onRender) {
      onRender(containerRef.current);
    }
  }, [onRender, viewState]);

  return (
    <div className="relative w-full h-full">
      {/* 图表容器 */}
      <div
        ref={containerRef}
        className="w-full h-full cursor-grab active:cursor-grabbing"
        style={{
          transform: `translate(${viewState.offsetX}px, ${viewState.offsetY}px) scale(${viewState.scale})`,
          transformOrigin: '0 0'
        }}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseLeave}
      />
      
      {/* 交互覆盖层 */}
      <div
        ref={overlayRef}
        className="absolute inset-0 pointer-events-none"
      >
        {/* 十字线 */}
        {enableCrosshair && crosshair.visible && (
          <>
            <div
              className="absolute w-full h-px bg-gray-400 opacity-50"
              style={{ top: crosshair.y }}
            />
            <div
              className="absolute h-full w-px bg-gray-400 opacity-50"
              style={{ left: crosshair.x }}
            />
          </>
        )}
        
        {/* 坐标显示 */}
        {enableCrosshair && crosshair.visible && (
          <div
            className="absolute bg-black text-white text-xs px-2 py-1 rounded pointer-events-none"
            style={{
              left: crosshair.x + 10,
              top: crosshair.y - 30,
              zIndex: 1000
            }}
          >
            ({crosshair.x.toFixed(0)}, {crosshair.y.toFixed(0)})
          </div>
        )}
      </div>

      {/* 工具栏 */}
      <div className="absolute top-2 right-2 flex gap-1 bg-white/90 rounded p-1">
        <button
          onClick={zoomIn}
          className="p-1 hover:bg-gray-100 rounded text-xs"
          title="放大"
        >
          🔍+
        </button>
        <button
          onClick={zoomOut}
          className="p-1 hover:bg-gray-100 rounded text-xs"
          title="缩小"
        >
          🔍-
        </button>
        <button
          onClick={resetView}
          className="p-1 hover:bg-gray-100 rounded text-xs"
          title="重置视图"
        >
          ⌂
        </button>
      </div>

      {/* 缩放指示器 */}
      <div className="absolute bottom-2 left-2 bg-black/70 text-white text-xs px-2 py-1 rounded">
        {(viewState.scale * 100).toFixed(0)}%
      </div>

      {children}
    </div>
  );
}
