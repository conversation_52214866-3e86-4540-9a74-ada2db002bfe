<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:converters="clr-namespace:LIVAnalyzer.UI.Converters">

    <!-- 转换器实例 -->
    <converters:StringToVisibilityConverter x:Key="StringToVisibilityConverter"/>
    <converters:ColorToTransparentConverter x:Key="ColorToTransparentConverter"/>
    <converters:InverseBooleanConverter x:Key="InverseBooleanConverter"/>
    <converters:NullToBooleanConverter x:Key="NullToBooleanConverter"/>
    <converters:MultiValueToVisibilityConverter x:Key="MultiValueToVisibilityConverter"/>
    <converters:EnumToBooleanConverter x:Key="EnumToBooleanConverter"/>
    <converters:NumericRangeValidationConverter x:Key="NumericRangeValidationConverter"/>

    <!-- 布尔值到可见性转换器 -->
    <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>

</ResourceDictionary>