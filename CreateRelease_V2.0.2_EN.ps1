# Create LIV Analyzer Release Package PowerShell Script
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    Create LIV Analyzer Release V2.0.2" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Set working directory
Set-Location "E:\01LaserPackage\software\LIV_Analyzer\CSharp_Version"

# Create release directory
$releaseDir = "LIVAnalyzer_V2.0.2_Release"
if (Test-Path $releaseDir) {
    Remove-Item $releaseDir -Recurse -Force
}
New-Item -ItemType Directory -Path $releaseDir | Out-Null

Write-Host "[1/5] Copying program files..." -ForegroundColor Yellow
Copy-Item "publish-release\LIVAnalyzer.exe" "$releaseDir\" -Force
Copy-Item "publish-release\Accord.dll.config" "$releaseDir\" -Force
Copy-Item "publish-release\README.txt" "$releaseDir\" -Force
Copy-Item "publish-release\*.bat" "$releaseDir\" -Force

Write-Host "[2/5] Copying documentation..." -ForegroundColor Yellow
Copy-Item "publish-release\*.md" "$releaseDir\" -Force

Write-Host "[3/5] Creating version info..." -ForegroundColor Yellow
$versionInfo = @"
LIV Analyzer Tool V2.0.2
Build Time: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
File Size: ~160MB
System Requirements: Windows 10/11 x64

Updates:
- Renamed FW86.5% to FW(1/e2) for clarity
- Improved negative intensity handling
- Optimized UI display and documentation
- Fixed multiple known issues
"@
$versionInfo | Out-File "$releaseDir\VersionInfo.txt" -Encoding UTF8

Write-Host "[4/5] Showing file info..." -ForegroundColor Yellow
Write-Host ""
Write-Host "Release Directory: $releaseDir" -ForegroundColor Green
Write-Host "Files included:" -ForegroundColor Green
Get-ChildItem $releaseDir | ForEach-Object { Write-Host "  - $($_.Name)" }

# Get main program size
$exeFile = Get-Item "$releaseDir\LIVAnalyzer.exe"
$sizeMB = [math]::Round($exeFile.Length / 1MB, 2)
Write-Host ""
Write-Host "Program Size: $sizeMB MB" -ForegroundColor Green

Write-Host ""
Write-Host "[5/5] Creating ZIP archive..." -ForegroundColor Yellow
$zipFile = "$releaseDir.zip"
if (Test-Path $zipFile) {
    Remove-Item $zipFile -Force
}
Compress-Archive -Path "$releaseDir\*" -DestinationPath $zipFile -CompressionLevel Optimal

if (Test-Path $zipFile) {
    $zipInfo = Get-Item $zipFile
    $zipSizeMB = [math]::Round($zipInfo.Length / 1MB, 2)
    Write-Host "ZIP created successfully: $zipFile" -ForegroundColor Green
    Write-Host "ZIP size: $zipSizeMB MB" -ForegroundColor Green
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "         Release Package Created!" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "Files ready for distribution:" -ForegroundColor Yellow
Write-Host "1. Full directory: $releaseDir\" -ForegroundColor White
Write-Host "2. ZIP archive: $zipFile" -ForegroundColor White
Write-Host ""