import { useAppStore } from '../state/store';

export default function DivergenceControls() {
  const { displayConfig, updateDisplayConfig } = useAppStore();
  return (
    <div className="flex items-center gap-4 text-sm">
      <label className="flex items-center gap-1">
        <input type="checkbox" checked={displayConfig.showHFF ?? true}
          onChange={(e) => updateDisplayConfig({ showHFF: e.target.checked })} /> HFF
      </label>
      <label className="flex items-center gap-1">
        <input type="checkbox" checked={displayConfig.showVFF ?? true}
          onChange={(e) => updateDisplayConfig({ showVFF: e.target.checked })} /> VFF
      </label>
    </div>
  );
}


