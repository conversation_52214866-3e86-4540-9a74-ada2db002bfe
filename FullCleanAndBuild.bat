@echo off
echo Cleaning all build outputs...
cd /d "E:\01LaserPackage\software\LIV_Analyzer\CSharp_Version"

echo Deleting bin and obj folders...
rd /s /q "LIVAnalyzer.Core\bin" 2>nul
rd /s /q "LIVAnalyzer.Core\obj" 2>nul
rd /s /q "LIVAnalyzer.Services\bin" 2>nul
rd /s /q "LIVAnalyzer.Services\obj" 2>nul
rd /s /q "LIVAnalyzer.UI\bin" 2>nul
rd /s /q "LIVAnalyzer.UI\obj" 2>nul

echo Clean completed!
echo.
echo Building solution...
dotnet restore
dotnet build --no-restore

echo.
echo Build completed!
pause