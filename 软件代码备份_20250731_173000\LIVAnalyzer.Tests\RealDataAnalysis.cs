using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using LIVAnalyzer.Data.Loaders;
using LIVAnalyzer.Core.Processors;
using LIVAnalyzer.Models;

namespace LIVAnalyzer.Tests
{
    /// <summary>
    /// 独立的测试程序，用于分析真实数据
    /// </summary>
    class RealDataAnalysis
    {
        static async Task Main(string[] args)
        {
            string testDataPath = @"E:\01LaserPackage\software\LIV_Analyzer\光峰芯片测试数据\640nm_QCW2P5A_30duty_T45C_LIV+Spectrum0716\芯瑞光红光芯片测试数据\R24+COS01+201_converted.xlsx";
            
            Console.WriteLine($"分析文件: {testDataPath}");
            Console.WriteLine($"分析时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            Console.WriteLine(new string('=', 80));
            
            if (!File.Exists(testDataPath))
            {
                Console.WriteLine($"错误: 测试数据文件不存在");
                return;
            }

            try
            {
                // 加载数据
                var loader = new ExcelDataLoader();
                var data = await loader.LoadExcelDataAsync(testDataPath);
                
                if (data == null)
                {
                    Console.WriteLine("错误: 无法加载数据文件");
                    return;
                }
                
                Console.WriteLine($"\n成功加载数据文件: {Path.GetFileName(testDataPath)}");
                Console.WriteLine($"波长数据点数: {data.WavelengthIntensityData.Count}");
                Console.WriteLine($"功率数据点数: {data.CurrentPowerData.Count}");
                Console.WriteLine($"电压数据点数: {data.CurrentVoltageData.Count}");
                Console.WriteLine($"水平发散角数据点数: {data.HorizontalDivergenceData?.Count ?? 0}");
                Console.WriteLine($"垂直发散角数据点数: {data.VerticalDivergenceData?.Count ?? 0}");
                
                // 如果有发散角数据，计算能量占比
                if (data.HorizontalDivergenceData?.Any() == true)
                {
                    Console.WriteLine("\n=== 水平发散角分析 ===");
                    await AnalyzeDivergenceData(data.HorizontalDivergenceData, "水平");
                }
                
                if (data.VerticalDivergenceData?.Any() == true)
                {
                    Console.WriteLine("\n=== 垂直发散角分析 ===");
                    await AnalyzeDivergenceData(data.VerticalDivergenceData, "垂直");
                }
                
                // 使用LIVDataProcessor处理
                var processor = new LIVDataProcessor();
                var parameters = processor.CalculateParameters(data);
                
                if (parameters != null)
                {
                    Console.WriteLine("\n=== LIVDataProcessor处理结果 ===");
                    Console.WriteLine($"峰值波长: {parameters.PeakWavelength:F2} nm");
                    Console.WriteLine($"FWHM: {parameters.FWHM:F2} nm");
                    Console.WriteLine($"阈值电流: {parameters.ThresholdCurrent:F3} A");
                    Console.WriteLine($"斜率效率: {parameters.SlopeEfficiency:F3} W/A");
                    Console.WriteLine($"最大效率: {parameters.MaxEfficiency:F2} %");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"\n错误: {ex.Message}");
                Console.WriteLine($"堆栈跟踪:\n{ex.StackTrace}");
            }
            
            Console.WriteLine("\n按任意键退出...");
            Console.ReadKey();
        }
        
        static async Task AnalyzeDivergenceData(System.Collections.Generic.List<DataPoint> divergenceData, string direction)
        {
            var angles = divergenceData.Select(p => p.X).ToArray();
            var intensities = divergenceData.Select(p => p.Y).ToArray();
            
            Console.WriteLine($"{direction}方向数据范围: [{angles.Min():F2}°, {angles.Max():F2}°]");
            Console.WriteLine($"最大强度: {intensities.Max():F6}");
            Console.WriteLine($"最小强度: {intensities.Min():F6}");
            
            // 检查边界强度
            var maxIntensity = intensities.Max();
            var firstIntensity = intensities.First();
            var lastIntensity = intensities.Last();
            var boundaryRatio = Math.Max(firstIntensity, lastIntensity) / maxIntensity;
            
            Console.WriteLine($"边界强度比: {boundaryRatio:P2} (边界强度/峰值强度)");
            
            if (boundaryRatio > 0.01)
            {
                Console.WriteLine("警告: 边界强度较高，可能影响能量占比计算准确性");
            }
            
            // 使用DivergenceProcessor计算
            var processor = new DivergenceProcessor();
            var angleDataPoints = divergenceData.Select(p => new DataPoint(p.X, p.Y)).ToList();
            var intensityDataPoints = divergenceData.Select(p => new DataPoint(p.X, p.Y)).ToList();
            
            var result = processor.CalculateDivergence(angleDataPoints, intensityDataPoints);
            
            if (result.IsValid)
            {
                Console.WriteLine($"\nDivergenceProcessor计算结果:");
                Console.WriteLine($"FWHM: {result.FWHM:F2}°");
                Console.WriteLine($"FW(1/e²): {result.FW1e2:F2}°");
                Console.WriteLine($"FW(1/e²)能量占比: {result.FW1e2PowerContainment:P3}");
                Console.WriteLine($"FW95%: {result.FW95:F2}°");
                
                // 详细输出能量占比
                Console.WriteLine($"\n能量占比详细信息:");
                Console.WriteLine($"FW(1/e²)能量占比 = {result.FW1e2PowerContainment:F6} ({result.FW1e2PowerContainment * 100:F4}%)");
                
                // 检查合理性
                if (result.FW1e2PowerContainment > 0.9)
                {
                    Console.WriteLine($"\n注意: 能量占比({result.FW1e2PowerContainment:P1})较高，可能原因:");
                    Console.WriteLine("  1. 光束分布接近平顶分布而非高斯分布");
                    Console.WriteLine("  2. 测量角度范围不够宽");
                    Console.WriteLine("  3. 数据中存在噪声或背景");
                }
                else if (result.FW1e2PowerContainment < 0.8)
                {
                    Console.WriteLine($"\n注意: 能量占比({result.FW1e2PowerContainment:P1})较低，可能原因:");
                    Console.WriteLine("  1. 光束质量较差，有较多旁瓣");
                    Console.WriteLine("  2. 多模激光输出");
                    Console.WriteLine("  3. 测量或数据处理问题");
                }
                
                // 验证发散角顺序
                if (result.FWHM > 0 && result.FW1e2 > 0 && result.FW95 > 0)
                {
                    bool orderCorrect = result.FWHM < result.FW1e2 && result.FW1e2 < result.FW95;
                    Console.WriteLine($"\n发散角大小顺序检查: {(orderCorrect ? "正确" : "异常")}");
                    Console.WriteLine($"FWHM < FW(1/e²) < FW95%: {result.FWHM:F2}° < {result.FW1e2:F2}° < {result.FW95:F2}°");
                }
            }
            else
            {
                Console.WriteLine($"计算失败: {result.ErrorMessage}");
            }
            
            await Task.CompletedTask;
        }
    }
}