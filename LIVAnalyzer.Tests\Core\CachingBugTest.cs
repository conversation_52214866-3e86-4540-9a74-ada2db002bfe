using LIVAnalyzer.Core.Processors;
using LIVAnalyzer.Models;
using Xunit;
using Xunit.Abstractions;

namespace LIVAnalyzer.Tests.Core
{
    public class CachingBugTest
    {
        private readonly ITestOutputHelper _output;
        
        public CachingBugTest(ITestOutputHelper output)
        {
            _output = output;
        }
        
        [Fact]
        public void ThresholdCurrent_DifferentFiles_ShouldReturnDifferentValues()
        {
            // Arrange
            var processor = new OptimizedLIVDataProcessor();
            
            // Create distinctly different data sets
            var file1Data = new LIVMeasurementData
            {
                FileName = "file1.xlsx",
                CurrentPowerData = new List<DataPoint>
                {
                    new(0.0, 0.0),
                    new(0.5, 0.01),
                    new(0.6, 0.1),  // Threshold around 0.5-0.6
                    new(0.7, 0.2),
                    new(0.8, 0.3),
                    new(0.9, 0.4),
                    new(1.0, 0.5)
                }
            };
            
            var file2Data = new LIVMeasurementData
            {
                FileName = "file2.xlsx",
                CurrentPowerData = new List<DataPoint>
                {
                    new(0.0, 0.0),
                    new(0.1, 0.0001),
                    new(0.2, 0.0002),
                    new(0.3, 0.0003),
                    new(0.7, 0.01),
                    new(0.8, 0.1),  // Threshold around 0.7-0.8
                    new(0.9, 0.2),
                    new(1.0, 0.3),
                    new(1.1, 0.4),
                    new(1.2, 0.5)
                }
            };
            
            // Act
            processor.ClearCache();
            var result1 = processor.CalculateParameters(file1Data);
            
            processor.ClearCache();
            var result2 = processor.CalculateParameters(file2Data);
            
            // Output for debugging
            _output.WriteLine($"File1 threshold: {result1.ThresholdCurrent}");
            _output.WriteLine($"File2 threshold: {result2.ThresholdCurrent}");
            
            // Assert
            Assert.NotEqual(result1.ThresholdCurrent, result2.ThresholdCurrent);
            
            // Additional checks to ensure values are reasonable
            Assert.True(result1.ThresholdCurrent > 0, "File1 threshold should be positive");
            Assert.True(result2.ThresholdCurrent > 0, "File2 threshold should be positive");
            Assert.True(result1.ThresholdCurrent < 0.7, "File1 threshold should be less than 0.7");
            Assert.True(result2.ThresholdCurrent > 0.6, "File2 threshold should be greater than 0.6");
        }
        
        [Fact]
        public void ThresholdCurrent_BatchProcessing_ShouldClearCacheBetweenFiles()
        {
            // This simulates what happens in batch processing
            var processor = new OptimizedLIVDataProcessor();
            var results = new List<double>();
            
            // Create 5 different files with different threshold characteristics
            var testFiles = new List<LIVMeasurementData>();
            for (int i = 0; i < 5; i++)
            {
                var threshold = 0.4 + i * 0.1; // 0.4, 0.5, 0.6, 0.7, 0.8
                var data = new LIVMeasurementData
                {
                    FileName = $"file{i}.xlsx",
                    CurrentPowerData = GenerateDataWithThreshold(threshold)
                };
                testFiles.Add(data);
            }
            
            // Process each file
            foreach (var file in testFiles)
            {
                processor.ClearCache(); // This is what should happen in batch processing
                var parameters = processor.CalculateParameters(file);
                results.Add(parameters.ThresholdCurrent);
                _output.WriteLine($"{file.FileName}: threshold = {parameters.ThresholdCurrent}");
            }
            
            // All results should be different
            var distinctCount = results.Distinct().Count();
            Assert.Equal(5, distinctCount);
        }
        
        private List<DataPoint> GenerateDataWithThreshold(double threshold)
        {
            var data = new List<DataPoint>();
            
            // Generate realistic laser diode curve
            for (double current = 0; current <= 1.5; current += 0.05)
            {
                double power;
                if (current < threshold)
                {
                    // Below threshold: very low power
                    power = current * 0.01;
                }
                else
                {
                    // Above threshold: linear increase
                    power = (current - threshold) * 0.8 + threshold * 0.01;
                }
                
                data.Add(new DataPoint(current, power));
            }
            
            return data;
        }
    }
}