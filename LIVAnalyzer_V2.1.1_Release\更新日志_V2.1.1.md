# LIV分析工具 v2.1.1 更新日志

## 📅 发布信息
- **版本号**: v2.1.1
- **发布日期**: 2025年8月5日
- **开发者**: 00106
- **更新类型**: 功能增强

## 🔧 主要更新内容

### 1. Excel导出功能增强
**修复了Excel导出中缺少电压数据的问题**

#### 具体改进：
- ✅ 在汇总表中新增 **I1电压 (V)** 列
- ✅ 在汇总表中新增 **I2电压 (V)** 列
- ✅ 确保导出数据的完整性和准确性
- ✅ 保持与详细数据表的一致性

#### 修改文件：
- `LIVAnalyzer.Core/Exporters/ExcelDataExporter.cs`

### 2. 批量处理功能修复
**修复了批量处理中缺少关键参数的问题**

#### 具体改进：
- ✅ 修复批量处理中I1和I2电压计算缺失的问题
- ✅ 修复批量处理中I1和I2效率计算缺失的问题
- ✅ 修复批量处理中发散角能量占比计算缺失的问题
- ✅ 确保批量处理结果与单文件处理结果一致

#### 修改文件：
- `LIVAnalyzer.Core/Processors/OptimizedBatchProcessor.cs`
- `LIVAnalyzer.Core/Processors/LIVDataProcessor.cs`

#### 导出表头更新：
```
原表头：
"文件名", "峰值波长 (nm)", "FWHM (nm)", "阈值电流 (A)", 
"最大功率 (W)", "最大效率 (%)", "斜率效率 (W/A)", 
"串联电阻 (Ω)", "拟合优度 (R²)", "I1电流 (A)", "I1功率 (W)",
"I2电流 (A)", "I2功率 (W)", ...

新表头：
"文件名", "峰值波长 (nm)", "FWHM (nm)", "阈值电流 (A)", 
"最大功率 (W)", "最大效率 (%)", "斜率效率 (W/A)", 
"串联电阻 (Ω)", "拟合优度 (R²)", "I1电流 (A)", "I1功率 (W)", "I1电压 (V)",
"I2电流 (A)", "I2功率 (W)", "I2电压 (V)", ...
```

### 3. 版本信息更新
- ✅ 程序集版本从 2.0.2.0 升级到 2.1.1.0
- ✅ 更新 AssemblyInfo.cs 中的版本信息

## 📊 技术细节

### 代码变更统计
- **修改文件数**: 4
- **新增文件数**: 2
- **代码行变更**: 约80行

### 影响范围
- **Excel导出功能**: 汇总表增加2列电压数据
- **批量处理功能**: 修复多个关键参数计算缺失问题
- **数据完整性**: 大幅提升导出数据的完整性和准确性
- **用户体验**: 用户可获得更完整、更准确的分析结果

## 🔍 问题修复

### 修复的问题
1. **Excel汇总表缺少电压数据**
   - **问题描述**: 汇总表中只有I1和I2的电流、功率数据，缺少对应的电压数据
   - **解决方案**: 在汇总表中添加I1电压和I2电压列
   - **影响**: 用户现在可以在汇总表中直接查看完整的LIV参数

2. **批量处理中I1/I2电压和效率计算缺失**
   - **问题描述**: 批量处理时只计算了I1和I2的功率，电压和效率显示为0
   - **解决方案**: 添加电压计算方法和效率计算逻辑
   - **影响**: 批量处理结果现在包含完整的I1和I2参数

3. **批量处理中发散角能量占比计算缺失**
   - **问题描述**: 批量处理时水平和垂直1/e²能量占比显示为0
   - **解决方案**: 修复发散角计算方法，使用完整的DivergenceProcessor
   - **影响**: 批量处理现在能正确计算发散角能量占比

### 数据映射
```
Excel汇总表新增列：
第12列: I1电压 (V) ← parameters.I1Voltage
第15列: I2电压 (V) ← parameters.I2Voltage

批量处理新增计算：
I1电压 ← CalculateVoltageAtCurrentOptimized(data, i1Current)
I2电压 ← CalculateVoltageAtCurrentOptimized(data, i2Current)
I1效率 ← (I1功率 / (I1电流 * I1电压)) * 100
I2效率 ← (I2功率 / (I2电流 * I2电压)) * 100
发散角能量占比 ← DivergenceProcessor.CalculateDivergence()
```

## 🚀 兼容性说明

### 向后兼容性
- ✅ **完全兼容**: 现有数据文件格式无变化
- ✅ **配置兼容**: 用户配置文件保持兼容
- ✅ **功能兼容**: 所有现有功能正常工作

### 导出格式变化
- **汇总表**: 新增2列电压数据（列数从21列增加到23列）
- **详细数据表**: 无变化（已包含电压数据）
- **发散角数据**: 无变化

## 📋 升级指南

### 从v2.1.0升级
1. 备份现有配置文件（可选）
2. 替换可执行文件
3. 重新导出数据以获得完整的电压信息

### 从v2.0.x升级
1. 备份现有数据和配置
2. 安装新版本
3. 验证导出功能是否包含电压数据

## 🧪 测试建议

### 验证步骤
1. 加载测试数据文件
2. 执行数据分析
3. 导出Excel文件
4. 检查汇总表是否包含I1电压和I2电压列
5. 验证电压数据的准确性

### 预期结果
- 汇总表应包含23列数据
- I1电压和I2电压列应显示正确的数值
- 数据应与详细数据表中的电压值一致

## 📞 技术支持

如有问题或建议，请：
1. 检查导出的Excel文件是否包含新的电压列
2. 验证电压数据的准确性
3. 联系开发者：00106

## 🎯 下一步计划

### 可能的后续改进
- 考虑添加更多导出格式选项
- 优化大数据量的导出性能
- 增强数据验证功能

---

**LIV分析工具 v2.1.1** - 更完整的数据导出，更准确的分析结果！

*感谢您的使用和反馈！* 🎉
