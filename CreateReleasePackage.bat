@echo off
chcp 65001 >nul
echo ========================================
echo    创建LIV分析工具发布包
echo ========================================
echo.

cd /d "%~dp0"

REM 检查是否已经有发布文件
if not exist "publish-release\LIVAnalyzer.exe" (
    echo 错误：未找到已发布的程序文件！
    echo 请先运行 BuildAndPublish.bat 生成程序文件。
    pause
    exit /b 1
)

REM 创建发布包目录
set RELEASE_DIR=LIVAnalyzer_V2.0.2_Release
if exist "%RELEASE_DIR%" rd /s /q "%RELEASE_DIR%"
mkdir "%RELEASE_DIR%"

echo [1/4] 复制程序文件...
copy "publish-release\LIVAnalyzer.exe" "%RELEASE_DIR%\"
copy "publish-release\Accord.dll.config" "%RELEASE_DIR%\"
copy "publish-release\README.txt" "%RELEASE_DIR%\"
copy "publish-release\启动LIV分析工具.bat" "%RELEASE_DIR%\"

echo [2/4] 复制文档文件...
if exist "使用指南.md" copy "使用指南.md" "%RELEASE_DIR%\"
if exist "快速开始指南.md" copy "快速开始指南.md" "%RELEASE_DIR%\"

echo [3/4] 复制示例数据...
if exist "test_data.csv" copy "test_data.csv" "%RELEASE_DIR%\示例数据.csv"

echo [4/4] 创建版本信息...
echo LIV曲线分析工具 V2.0.2 > "%RELEASE_DIR%\版本信息.txt"
echo 构建时间：%date% %time% >> "%RELEASE_DIR%\版本信息.txt"
echo 文件大小：约160MB >> "%RELEASE_DIR%\版本信息.txt"
echo 系统要求：Windows 10/11 x64 >> "%RELEASE_DIR%\版本信息.txt"

REM 显示文件大小
echo.
echo ========================================
echo           发布包创建完成！
echo ========================================
echo.
echo 发布目录：%RELEASE_DIR%
echo.
echo 包含文件：
dir "%RELEASE_DIR%" /b
echo.
echo 主程序大小：
for %%f in ("%RELEASE_DIR%\LIVAnalyzer.exe") do echo   LIVAnalyzer.exe: %%~zf 字节 (约 %%~zf:~0,-6% MB)
echo.

REM 询问是否打开发布目录
set /p choice="是否打开发布目录？(Y/N): "
if /i "%choice%"=="Y" (
    explorer "%RELEASE_DIR%"
)

REM 询问是否创建压缩包
set /p choice2="是否创建ZIP压缩包？(Y/N): "
if /i "%choice2%"=="Y" (
    echo 正在创建压缩包...
    powershell -command "Compress-Archive -Path '%RELEASE_DIR%\*' -DestinationPath '%RELEASE_DIR%.zip' -Force"
    if exist "%RELEASE_DIR%.zip" (
        echo 压缩包创建成功：%RELEASE_DIR%.zip
        for %%f in ("%RELEASE_DIR%.zip") do echo 压缩包大小：%%~zf 字节 (约 %%~zf:~0,-6% MB)
    ) else (
        echo 压缩包创建失败！
    )
)

echo.
echo 发布包创建完成！可以分发给用户使用。
echo.
pause
