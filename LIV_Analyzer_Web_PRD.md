# LIV Analyzer Web版产品需求文档(PRD)

**版本**: 1.0  
**创建日期**: 2025-08-10  
**产品经理**: Claude  
**目标受众**: 激光器测试工程师、研发人员  

---

## 1. 产品概述

### 1.1 项目背景
LIV Analyzer是一款用于分析激光二极管光电特性的专业工具，目前为C# WPF桌面应用。为了提高产品的可访问性、跨平台兼容性和协作效率，需要将其完全迁移到Web平台。

### 1.2 产品定位
专业级Web端激光二极管特性分析工具，提供与桌面版完全一致的功能体验，支持大数据量处理和高精度科学计算。

### 1.3 目标用户
- **主要用户**: 激光器测试工程师
- **次要用户**: 光电子研发人员、实验室研究人员
- **管理用户**: 实验室主管、项目经理

### 1.4 核心价值
- **无需安装**: 浏览器直接访问，降低部署成本
- **跨平台兼容**: 支持Windows、macOS、Linux
- **协作便利**: 云端数据共享和协作分析
- **实时更新**: 无需手动更新软件版本

---

## 2. 功能需求规格

### 2.1 核心功能模块

#### 2.1.1 数据文件处理模块
**Must Have:**
- 支持Excel文件(.xlsx, .xls)上传解析
- 支持CSV文件上传解析
- 文件格式自动识别和验证
- 支持最大500MB文件大小
- 数据预览和格式检查

**Excel文件结构要求:**
- `wavelength`工作表: [Wavelength (nm), Intensity]
- `power`工作表: [Current (A), Power (W)]
- `voltage`工作表: [Current (A), Voltage (V)]
- `HFF`工作表（可选）: 水平远场数据
- `VFF`工作表（可选）: 垂直远场数据

**Should Have:**
- 拖拽上传功能
- 批量文件处理（最多1000个文件）
- 文件预处理进度显示
- 数据完整性检查

**验收标准:**
```
Given 用户上传符合格式要求的Excel文件
When 系统解析文件数据
Then 应正确提取wavelength、power、voltage数据
And 显示数据预览表格
And 验证数据完整性
```

#### 2.1.2 LIV特性计算模块
**Must Have:**
- 阈值电流(Ith)计算
- 斜率效率(ηd)计算
- 串联电阻(Rs)计算
- 最大效率计算
- 效率vs电流曲线分析

**算法要求:**
- 线性拟合算法实现阈值电流检测
- 分段线性拟合处理非线性区域
- 支持自定义阈值检测参数
- 数值稳定性保证

**Should Have:**
- 多种阈值检测算法选择
- 拟合质量评估指标
- 参数置信区间计算

**验收标准:**
```
Given 输入标准LIV测试数据
When 执行特性计算
Then 阈值电流误差 < 5%
And 斜率效率误差 < 3%
And 计算时间 < 2秒(对于10000点数据)
```

#### 2.1.3 光谱分析模块
**Must Have:**
- 峰值波长检测
- 半高全宽(FWHM)计算
- 光谱重心计算
- 边模抑制比(SMSR)分析

**算法实现:**
- 高斯拟合峰值检测
- 插值算法提升精度
- 多峰识别和分离
- 噪声滤波处理

**Should Have:**
- 光谱归一化选项
- 自定义波长范围分析
- 光谱质量评估

**验收标准:**
```
Given 激光器光谱数据
When 执行光谱分析
Then 峰值波长精度 ± 0.1nm
And FWHM计算精度 ± 0.05nm
And 支持1nm-2000nm波长范围
```

#### 2.1.4 发散角计算模块
**Must Have:**
- 水平发散角(θ∥)计算
- 垂直发散角(θ⊥)计算
- 远场分布拟合
- 发散角椭圆度分析

**Could Have:**
- 近场到远场变换
- M²光束质量因子计算

#### 2.1.5 数据可视化模块
**Must Have - 四类图表:**
1. **LIV特性图**: P-I、V-I、η-I曲线
2. **光谱图**: 波长vs强度
3. **发散角图**: 远场角度分布
4. **效率图**: 效率vs电流关系

**图表功能要求:**
- 交互式缩放和平移
- 数据点悬停显示
- 图例开关控制
- 坐标轴自定义
- 多数据集叠加显示
 - 图层显隐开关（P-I / V-I / η-I / 诊断层）
 - 颜色图例与数值悬浮提示

**Should Have:**
- 图表导出(PNG, SVG, PDF)
- 自定义配色方案
- 三维可视化选项

#### 2.1.6 数据平滑算法模块
**Must Have:**
- Savitzky-Golay滤波器
- 移动平均滤波
- 高斯滤波
- Butterworth滤波器

**参数配置:**
- 窗口大小调节
- 多项式阶数选择
- 截止频率设置
- 实时预览效果

#### 2.1.7 数据导出模块
**Must Have:**
- Excel格式导出(.xlsx)
- CSV格式导出
- 分析报告生成
- 参数汇总表

**Should Have:**
- PDF报告导出（预研）
- 自定义报告模板
- 批量导出功能

### 2.2 系统功能需求

#### 2.2.1 用户界面需求
**Must Have:**
- 响应式设计(桌面优先)
- 100%复刻WPF布局结构
- 主题切换(亮色/暗色)
- 多语言支持(中文/英文)
- 最近文件/目录记忆：支持 File System Access 的浏览器默认打开上次目录；不支持时回退为常规文件选择器（功能可降级）

**布局结构:**
```
┌─────────────────────────────────────────┐
│ 菜单栏 (File, Edit, View, Help)         │
├─────────────────────────────────────────┤
│ 工具栏 (快捷操作按钮)                    │
├──────────────┬──────────────────────────┤
│ 参数面板     │ 图表显示区域              │
│ (350px可调)  │ (标签页式图表组织)        │
│              │                          │
│ - 文件操作   │ [LIV] [光谱] [发散] [效率] │
│ - 计算参数   │                          │
│ - 平滑设置   │                          │
│ - 显示选项   │                          │
└──────────────┴──────────────────────────┘
```

#### 2.2.2 性能需求
**Must Have:**
- 支持500MB大文件处理
- 10000点数据计算时间 < 3秒
- 图表渲染帧率 > 30fps
- 内存使用 < 2GB

**Should Have:**
- Web Workers后台计算
- 渐进式数据加载
- 图表虚拟化滚动

#### 2.2.3 兼容性需求
**Must Have:**
- Chrome 90+
- Firefox 88+
- Edge 90+
- Safari 14+

**Should Have:**
- 移动端适配(平板)
- PWA离线功能

---

## 3. 技术架构设计

### 3.1 技术栈选型

#### 3.1.1 前端框架
- **React 18.3+**: 组件化开发，优秀生态
- **TypeScript 5.0+**: 类型安全，代码质量保证
- **Vite 5.0+**: 快速构建，HMR支持

#### 3.1.2 UI组件库
- **shadcn/ui**: 现代化组件库，高度可定制
- **Tailwind CSS**: 原子化CSS，快速样式开发
- **Lucide Icons**: 统一图标体系

#### 3.1.3 数据可视化
- **Observable Plot**: 高性能科学绘图库
- **D3.js**: 底层图形操作支持

#### 3.1.4 数值计算
- **ml-matrix**: 矩阵运算库
- **ml-regression**: 回归分析
- **simple-statistics**: 统计计算

#### 3.1.5 文件处理
- **SheetJS**: Excel文件解析
- **PapaParse**: CSV文件处理

### 3.2 系统架构

```mermaid
graph TB
    A[用户界面层] --> B[业务逻辑层]
    B --> C[数据处理层]
    C --> D[计算引擎层]
    
    A --> A1[React组件]
    A --> A2[shadcn/ui]
    A --> A3[状态管理]
    
    B --> B1[文件处理服务]
    B --> B2[分析服务]
    B --> B3[导出服务]
    
    C --> C1[数据解析器]
    C --> C2[数据验证器]
    C --> C3[数据转换器]
    
    D --> D1[LIV计算引擎]
    D --> D2[光谱分析引擎]
    D --> D3[数值算法库]
```

### 3.3 核心模块设计

#### 3.3.1 数据处理架构
```typescript
interface LIVData {
  wavelength: { wavelength: number[]; intensity: number[] }
  power: { current: number[]; power: number[] }
  voltage: { current: number[]; voltage: number[] }
  hff?: { angle: number[]; intensity: number[] }
  vff?: { angle: number[]; intensity: number[] }
}

interface ProcessingResult {
  livParameters: LIVParameters
  spectralParameters: SpectralParameters
  divergenceParameters?: DivergenceParameters
  plots: PlotData[]
}
```

#### 3.3.2 计算引擎架构
```typescript
class LIVProcessor {
  calculateThresholdCurrent(data: PowerCurrentData): number
  calculateSlopeEfficiency(data: PowerCurrentData): number
  calculateSeriesResistance(data: VoltageCurrentData): number
  calculateMaxEfficiency(data: PowerCurrentData, data2: VoltageCurrentData): number
}

class SpectralProcessor {
  findPeakWavelength(data: SpectralData): number
  calculateFWHM(data: SpectralData): number
  calculateCentroid(data: SpectralData): number
  calculateSMSR(data: SpectralData): number
}
```

### 3.4 状态管理架构

使用Zustand进行状态管理：

```typescript
interface AppState {
  // 数据状态
  data: LIVData | null
  results: ProcessingResult | null
  
  // UI状态
  currentTab: string
  theme: 'light' | 'dark'
  sidebarWidth: number
  
  // 处理状态
  isProcessing: boolean
  progress: number
  error: string | null
  
  // 配置状态
  processingConfig: ProcessingConfig
  displayConfig: DisplayConfig
}
```

---

## 4. 用户界面规范

### 4.1 设计原则
- **一致性**: 与桌面版保持视觉和交互一致
- **效率**: 优化科学计算工作流程
- **清晰**: 数据和结果呈现清晰易读
- **专业**: 符合科学软件设计标准

### 4.2 布局规范

#### 4.2.1 菜单栏设计
```
File    Edit    View    Tools    Help
├─ Open File(s)         ├─ Undo           ├─ Zoom In        ├─ Settings       ├─ Documentation
├─ Recent Files         ├─ Redo           ├─ Zoom Out       ├─ Preferences    ├─ Keyboard Shortcuts
├─ Export Results       ├─ Copy           ├─ Fit to Window  ├─ Reset Config   ├─ About
├─ Export Charts        ├─ Paste          ├─ Grid On/Off    
└─ Exit                 └─ Select All     └─ Legend On/Off  
```

#### 4.2.2 工具栏设计
```
[📁] [📊] [⚙️] [🔄] [📋] [💾] [🎨] [❓]
 打开  分析  设置  刷新  复制  保存  主题  帮助
```

#### 4.2.3 参数面板设计
```
┌─ 文件操作 ─────────────────┐
│ [选择文件] [批量处理]       │
│ 当前文件: example.xlsx      │
│ 状态: ✅ 数据已加载         │
├─ 分析参数 ─────────────────┤
│ 阈值检测方法: [下拉菜单]    │
│ 拟合点数: [150] ±10        │
│ 功率单位: [W] [mW]         │
├─ 平滑设置 ─────────────────┤
│ 启用平滑: [✓]              │
│ 方法: [Savitzky-Golay]     │
│ 窗口大小: [5]              │
│ 多项式阶数: [3]            │
├─ 显示选项 ─────────────────┤
│ 网格: [✓]                  │
│ 图例: [✓]                  │
│ 数据点: [✓]                │
│ 坐标值: [✓]                │
└─ 结果汇总 ─────────────────┘
│ Ith = 12.5 mA             │
│ ηd = 0.85 W/A             │
│ λpeak = 980.2 nm          │
│ FWHM = 2.1 nm             │
└───────────────────────────┘
```

### 4.3 图表显示规范

#### 4.3.1 图表标签页布局
```
[LIV特性] [光谱分析] [发散角度] [效率分析]
┌─────────────────────────────────────────┐
│ 图表工具栏: [🔍+] [🔍-] [📏] [💾] [⚙️]    │
├─────────────────────────────────────────┤
│                                         │
│        主图表显示区域                    │
│     (Observable Plot渲染)               │
│                                         │
│                                         │
├─────────────────────────────────────────┤
│ 图表控制: [✓]P-I [✓]V-I [✓]η-I          │
└─────────────────────────────────────────┘
```

#### 4.3.2 图表样式规范
- **配色方案**: 支持科学期刊标准配色
- **线条样式**: 实线、虚线、点线可选
- **标记样式**: 圆形、方形、三角形等
- **字体**: 支持数学符号和希腊字母
- **坐标轴**: 对数/线性刻度可选

### 4.4 响应式设计

#### 4.4.1 断点设计
- **桌面**: ≥1200px (主要目标)
- **平板**: 768px - 1199px
- **手机**: <768px (基本支持)

#### 4.4.2 自适应策略
- **桌面**: 完整三栏布局
- **平板**: 可折叠侧边栏
- **手机**: 垂直堆叠布局

---

## 5. 开发计划

### 5.1 项目里程碑

#### Phase 1: 基础架构 (4周)
**Week 1-2: 项目搭建**
- [x] React + TypeScript + Vite 项目初始化（新建独立目录 `LIVAnalyzer.WebApp/liv-analyzer-web`）
- [x] shadcn/ui 组件库与 Tailwind v4 集成（含主题切换、基础 UI）
- [x] 路由和状态管理配置（Zustand + React Router 已完成）
- [x] 基础布局和导航实现（菜单/主题切换/标签页）

**Week 3-4: 核心服务**
- [x] 文件上传和解析服务（CSV/Excel 解析、映射到 `LIVData`）
- [x] 数据验证和预处理（清洗 NaN/对齐 X-Y，前 50 行数据预览表）
- [x] 错误处理和用户反馈（错误横幅 `ErrorBanner`）
- [x] 基础图表组件（已实现 LIV/光谱/发散/效率 四类图表）
- [x] 最近目录/文件选择（基于 File System Access，含降级回退）
- [x] 配置持久化（处理参数/显示设置自动保存与恢复）
- [x] 图表导出（SVG/PNG，支持2x分辨率）
- [x] 平滑预览（光谱/LIV/发散，移动平均与简化SG 原型）

#### Phase 2: 核心算法 (6周)
**Week 5-6: LIV分析引擎**
- [x] 阈值电流计算算法（Worker 原型，线性拟合估算）
- [x] 斜率效率计算（Worker 原型）
- [x] 串联电阻分析（Worker 原型，dV/dI）
- [x] 线性拟合实现（最小二乘，原型）
备注：上述为原型实现，后续与桌面版进行精度对齐与回归测试。

### 5.4 当前进展与下一步

**当前进展**
- 新建独立 Web 子项目并完成构建链路（Vite + TS + React）。
- 集成 shadcn/ui（Button/Card/Progress/主题切换）与 Tailwind v4（`@tailwindcss/postcss`）。
- 完成文件解析服务（CSV/Excel → `LIVData`）、错误提示、数据点数概览与前 50 行数据表格。
- 完成四类图表（LIV/光谱/发散/效率）与标签页框架。
- 解析与计算 Worker 已接入（大文件解析 + LIV/光谱参数原型计算）。
- 支持“最近目录”文件选择（受浏览器支持限制，含降级回退）。

**下一步（Phase 1/2 持续推进）**
- 参数配置面板：阈值检测方法、拟合点数、单位设置等。
- 精度对齐：与桌面版结果对齐，完善鲁棒/分段拟合与误差评估。
- 首包体积优化：xlsx/plot/d3 动态导入与手动分包，降低加载时间。
- 数据导出与报告：结果汇总表与图表导出（前端/后端双通道预研）。
 - 图表导出：支持 SVG/PNG（前端，含2x），预研 PDF（后端）。
 - 回归对齐与批量处理：前端回归页面（/regress）加载黄金数据并输出报告；批量处理（/batch）支持并发解析与汇总导出。

**Week 7-8: 光谱分析引擎**
- [x] 峰值检测算法（Worker 原型）
- [x] FWHM计算（线性插值，Worker 原型）
- [x] 光谱重心（Worker 原型）
- [x] SMSR 计算（次强峰法，Worker 原型）
- [ ] 多峰识别（待增强）
- [ ] 光谱质量评估（待办）

**Week 9-10: 数据平滑算法**
- [ ] Savitzky-Golay滤波器
- [ ] 移动平均实现
- [ ] 高斯和Butterworth滤波
- [ ] 实时预览功能

#### Phase 3: 可视化实现 (4周)
**Week 11-12: 图表系统**
- [x] Observable Plot集成
- [x] 四类图表实现
 - [x] 交互功能开发（缩放/平移/重置视图 基础版）
 - [x] 悬浮提示与颜色图例
 - [x] 图层显隐开关（P-I / V-I / η-I / 诊断层）
- [x] 主题和样式系统

**Week 13-14: 高级功能**
- [x] 发散角计算和显示（基于角度FWHM原型，输出θ∥/θ⊥/椭圆度）
- [x] 批量处理功能（多文件并发解析与汇总导出 原型）
- [x] 数据导出系统（CSV/Excel 报告：结果与参数汇总）
- [ ] 配置管理

#### Phase 4: 优化和测试 (3周)
**Week 15: 性能优化**
- [x] Web Workers集成（解析/计算）
- [ ] 大文件处理优化
- [ ] 内存管理优化
- [ ] 渲染性能调优

**Week 16: 测试和修复**
- [ ] 单元测试覆盖
- [ ] 集成测试
- [ ] 用户验收测试
- [ ] Bug修复

**Week 17: 部署准备**
- [ ] 生产环境构建
- [ ] PWA功能完善
- [ ] 文档编写
- [ ] 发布准备

### 5.2 关键风险和缓解措施

| 风险 | 影响 | 概率 | 缓解措施 |
|------|------|------|----------|
| 大文件处理性能问题 | 高 | 中 | 实施流式处理和Web Workers |
| 算法精度不达标 | 高 | 低 | 早期原型验证，参考桌面版实现 |
| 浏览器兼容性问题 | 中 | 中 | 渐进增强设计，Polyfill支持 |
| 第三方库限制 | 中 | 低 | 备选方案准备，必要时自研 |

### 5.3 人力资源规划

**核心团队 (4人):**
- **前端架构师** (1人): 负责整体架构设计和核心模块开发
- **算法工程师** (1人): 负责数值计算和分析算法实现
- **UI/UX开发者** (1人): 负责界面实现和用户体验优化
- **测试工程师** (1人): 负责质量保证和性能优化

---

## 6. 验收标准

### 6.1 功能验收标准

#### 6.1.1 数据处理验收
**测试场景**: 使用标准测试数据集
```
Given: 上传500MB Excel文件包含10万个数据点
When: 执行数据解析和预处理
Then: 
- 解析成功率 = 100%
- 解析时间 < 30秒
- 内存使用 < 2GB
- 数据完整性检查通过
```

#### 6.1.2 计算精度验收
**LIV参数计算**
```
Given: 标准激光器测试数据
When: 执行LIV特性分析
Then:
- 阈值电流误差 < 5% (与桌面版对比)
- 斜率效率误差 < 3%
- 串联电阻误差 < 5%
- 计算时间 < 5秒
```

**光谱分析**
```
Given: 激光器光谱数据
When: 执行光谱分析
Then:
- 峰值波长精度 ± 0.1nm
- FWHM精度 ± 0.05nm
- 多峰识别准确率 > 95%
```

#### 6.1.3 可视化验收
```
Given: 计算结果数据
When: 生成图表显示
Then:
- 四类图表正确显示
- 交互响应时间 < 100ms
- 缩放和平移功能正常
- 数据导出功能正常
```

### 6.2 性能验收标准

| 指标 | 目标值 | 测试方法 |
|------|--------|----------|
| 首屏加载时间 | < 3秒 | Lighthouse测试 |
| 大文件处理时间 | < 60秒 (500MB) | 实际文件测试 |
| 内存使用峰值 | < 2GB | Chrome DevTools监控 |
| 图表渲染帧率 | > 30fps | Performance API测量 |
| 批量处理能力 | 1000文件不崩溃 | 压力测试 |

### 6.3 兼容性验收标准

**浏览器兼容性测试矩阵**
| 浏览器 | 版本 | 操作系统 | 核心功能 | 性能 |
|--------|------|----------|----------|------|
| Chrome | 90+ | Windows/macOS/Linux | ✅ | ✅ |
| Firefox | 88+ | Windows/macOS/Linux | ✅ | ✅ |
| Edge | 90+ | Windows | ✅ | ✅ |
| Safari | 14+ | macOS | ✅ | ⚠️ |

### 6.4 用户体验验收标准

**可用性测试指标**
- 新用户学习时间 < 30分钟
- 核心任务完成率 > 90%
- 用户满意度评分 > 4.0/5.0
- 错误操作恢复时间 < 10秒

### 6.5 质量保证标准

**代码质量**
- 单元测试覆盖率 > 80%
- TypeScript类型覆盖率 > 95%
- ESLint规则全部通过
- 零生产环境严重Bug

**安全标准**
- 文件上传安全验证
- XSS/CSRF防护
- 数据传输加密(HTTPS)
- 无敏感信息泄露

---

## 7. 风险评估与缓解策略

### 7.1 技术风险

#### 7.1.1 高风险项目
**风险**: Web端数值计算精度与桌面版存在差异
- **影响**: 用户无法接受分析结果，项目失败
- **概率**: 30%
- **缓解策略**: 
  - 使用双精度浮点运算
  - 实现与桌面版相同的算法逻辑
  - 建立完整的回归测试套件
  - 早期算法验证原型

#### 7.1.2 中风险项目
**风险**: 大文件处理性能不达标
- **影响**: 用户体验差，功能受限
- **概率**: 40%
- **缓解策略**:
  - Web Workers后台处理
  - 分块读取大文件
  - 流式处理架构
  - 性能监控和优化

### 7.2 项目风险

#### 7.2.1 进度风险
**风险**: 算法实现复杂度超出预期
- **影响**: 项目延期2-4周
- **概率**: 50%
- **缓解策略**:
  - 预留20%缓冲时间
  - 并行开发策略
  - 及时风险识别和上报

#### 7.2.2 资源风险
**风险**: 关键人员离职或不可用
- **影响**: 项目延期或质量下降
- **概率**: 20%
- **缓解策略**:
  - 知识共享和文档化
  - 交叉培训
  - 备用人员计划

---

## 8. 成功度量标准

### 8.1 技术成功指标
- ✅ 功能完整性: 100%复刻桌面版功能
- ✅ 计算精度: 与桌面版误差 < 5%
- ✅ 性能达标: 500MB文件处理 < 60秒
- ✅ 兼容性: 支持4个主流浏览器
- ✅ 稳定性: 连续运行24小时无崩溃

### 8.2 用户成功指标
- 📊 用户满意度: > 4.0/5.0
- 📊 任务完成率: > 90%
- 📊 学习成本: 新用户上手 < 30分钟
- 📊 错误率: < 5%的用户操作导致错误

### 8.3 业务成功指标
- 💼 迁移成功率: > 80%桌面版用户转移到Web版
- 💼 新用户增长: 相比桌面版增长 > 50%
- 💼 使用频率: 平均每用户每周使用 > 3次
- 💼 功能采用率: 核心功能使用率 > 70%

---

## 9. 附录

### 9.1 参考文档
- [LIV Analyzer桌面版用户手册](../docs/UserManual.md)
- [技术架构文档](../docs/Architecture.md)
- [算法规格说明](../docs/AlgorithmSpec.md)

### 9.2 术语表
| 术语 | 定义 |
|------|------|
| LIV | Light-Current-Voltage，激光器光电流电压特性 |
| Ith | 阈值电流，激光器开始激射的电流值 |
| ηd | 微分量子效率，功率对电流的斜率 |
| FWHM | 半高全宽，光谱峰值一半高度处的宽度 |
| SMSR | 边模抑制比，主模与最强边模的功率比 |
| PWA | 渐进式Web应用，支持离线使用的Web应用 |

### 9.3 变更历史
| 版本 | 日期 | 变更内容 | 作者 |
|------|------|----------|------|
| 1.0 | 2025-08-10 | 初始版本创建 | Claude |

---

**文档状态**: ✅ 已完成  
**下一步行动**: 推进 Phase 2 算法精度对齐与参数配置面板，实现交互增强与体积优化  
**联系人**: 产品经理 Claude