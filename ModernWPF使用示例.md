# ModernWPF 集成使用示例

展示如何在 LIV Analyzer 中使用 ModernWPF 的各种特性。

## 1. 应用程序启动配置

```csharp
// App.xaml.cs
using System.Windows;
using LIVAnalyzer.UI.Services;
using ModernWpf;

namespace LIVAnalyzer.UI
{
    public partial class App : Application
    {
        protected override void OnStartup(StartupEventArgs e)
        {
            base.OnStartup(e);
            
            // 初始化 ModernWPF
            ModernWpfIntegrationService.Instance.SetApplicationTheme(null); // 跟随系统
            
            // 设置主题色（可选）
            ModernWpfIntegrationService.Instance.SetAccentColor(Colors.DodgerBlue);
            
            // 显示主窗口
            var mainWindow = new MainWindow();
            ModernWpfIntegrationService.Instance.ApplyThemeToWindow(mainWindow);
            mainWindow.Show();
        }
    }
}
```

## 2. 主窗口实现

```csharp
// MainWindow.xaml.cs
using System.Windows;
using LIVAnalyzer.UI.Services;
using ModernWpf.Controls;

namespace LIVAnalyzer.UI.Views
{
    public partial class MainWindow : Window
    {
        private NavigationService _navigationService;
        
        public MainWindow()
        {
            InitializeComponent();
            
            // 设置导航服务
            _navigationService = ModernWpfIntegrationService.Instance
                .CreateNavigationService(ContentFrame);
            
            // 注册自适应处理
            ModernWpfIntegrationService.Instance.RegisterAdaptiveHandler(
                this, OnWindowSizeChanged);
            
            // 设置初始页面
            _navigationService.NavigateTo(typeof(AnalysisPage));
        }
        
        private void OnWindowSizeChanged(WindowSizeClass sizeClass)
        {
            // 根据窗口大小调整 UI
            switch (sizeClass)
            {
                case WindowSizeClass.Compact:
                    NavView.PaneDisplayMode = NavigationViewPaneDisplayMode.LeftMinimal;
                    break;
                case WindowSizeClass.Medium:
                    NavView.PaneDisplayMode = NavigationViewPaneDisplayMode.LeftCompact;
                    break;
                default:
                    NavView.PaneDisplayMode = NavigationViewPaneDisplayMode.Left;
                    break;
            }
        }
        
        private void NavView_SelectionChanged(NavigationView sender, 
            NavigationViewSelectionChangedEventArgs args)
        {
            if (args.SelectedItem is NavigationViewItem item)
            {
                switch (item.Tag?.ToString())
                {
                    case "analysis":
                        _navigationService.NavigateTo(typeof(AnalysisPage));
                        break;
                    case "batch":
                        _navigationService.NavigateTo(typeof(BatchProcessPage));
                        break;
                    case "settings":
                        _navigationService.NavigateTo(typeof(SettingsPage));
                        break;
                }
            }
        }
    }
}
```

## 3. 分析页面示例

```csharp
// Pages/AnalysisPage.xaml.cs
using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using LIVAnalyzer.UI.Services;
using ModernWpf.Controls;

namespace LIVAnalyzer.UI.Pages
{
    public partial class AnalysisPage : Page
    {
        private readonly AnalysisViewModel _viewModel;
        
        public AnalysisPage()
        {
            InitializeComponent();
            _viewModel = new AnalysisViewModel();
            DataContext = _viewModel;
        }
        
        private async void LoadFileButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 显示文件选择对话框
                var openFileDialog = new Microsoft.Win32.OpenFileDialog
                {
                    Filter = "Excel Files|*.xlsx;*.xls|CSV Files|*.csv|All Files|*.*",
                    Multiselect = true
                };
                
                if (openFileDialog.ShowDialog() == true)
                {
                    // 显示进度对话框
                    await ModernWpfIntegrationService.Instance.ShowProgressAsync(
                        "加载文件",
                        async (progress, cancellationToken) =>
                        {
                            await _viewModel.LoadFilesAsync(
                                openFileDialog.FileNames, 
                                progress, 
                                cancellationToken);
                        });
                    
                    // 显示成功消息
                    await ModernWpfIntegrationService.Instance.ShowMessageAsync(
                        "成功",
                        $"成功加载 {openFileDialog.FileNames.Length} 个文件",
                        "确定");
                }
            }
            catch (Exception ex)
            {
                // 显示错误消息
                await ModernWpfIntegrationService.Instance.ShowMessageAsync(
                    "错误",
                    $"加载文件失败：{ex.Message}",
                    "确定");
            }
        }
        
        private async void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            // 确认导出
            var result = await ModernWpfIntegrationService.Instance.ShowMessageAsync(
                "确认导出",
                "是否导出选中的数据？",
                "导出",
                "取消");
                
            if (result == ContentDialogResult.Primary)
            {
                await ExportDataAsync();
            }
        }
        
        private async Task ExportDataAsync()
        {
            // 导出逻辑
            await _viewModel.ExportDataAsync();
        }
    }
}
```

## 4. 设置页面示例

```csharp
// Pages/SettingsPage.xaml.cs
using System.Windows;
using System.Windows.Controls;
using LIVAnalyzer.UI.Services;
using ModernWpf;
using ModernWpf.Controls;

namespace LIVAnalyzer.UI.Pages
{
    public partial class SettingsPage : Page
    {
        public SettingsPage()
        {
            InitializeComponent();
            InitializeSettings();
        }
        
        private void InitializeSettings()
        {
            // 设置当前主题
            var currentTheme = ModernWpfIntegrationService.Instance.GetCurrentTheme();
            ThemeRadioButtons.SelectedIndex = currentTheme == ApplicationTheme.Light ? 0 :
                                            currentTheme == ApplicationTheme.Dark ? 1 : 2;
            
            // 设置当前主题色
            AccentColorPicker.Color = ThemeManager.Current.AccentColor ?? Colors.DodgerBlue;
        }
        
        private void ThemeRadioButtons_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (sender is RadioButtons radioButtons)
            {
                ApplicationTheme? theme = radioButtons.SelectedIndex switch
                {
                    0 => ApplicationTheme.Light,
                    1 => ApplicationTheme.Dark,
                    _ => null // 跟随系统
                };
                
                ModernWpfIntegrationService.Instance.SetApplicationTheme(theme);
            }
        }
        
        private void AccentColorPicker_ColorChanged(ColorPicker sender, ColorChangedEventArgs args)
        {
            ModernWpfIntegrationService.Instance.SetAccentColor(args.NewColor);
        }
        
        private async void ResetButton_Click(object sender, RoutedEventArgs e)
        {
            var result = await ModernWpfIntegrationService.Instance.ShowMessageAsync(
                "重置设置",
                "确定要重置所有设置吗？",
                "重置",
                "取消");
                
            if (result == ContentDialogResult.Primary)
            {
                // 重置逻辑
                ResetAllSettings();
            }
        }
        
        private void ResetAllSettings()
        {
            // 重置为默认值
            ModernWpfIntegrationService.Instance.SetApplicationTheme(null);
            ModernWpfIntegrationService.Instance.SetAccentColor(Colors.DodgerBlue);
            InitializeSettings();
        }
    }
}
```

## 5. 自定义控件示例

```csharp
// Controls/DataCard.cs
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using ModernWpf.Controls;

namespace LIVAnalyzer.UI.Controls
{
    /// <summary>
    /// ModernWPF 风格的数据卡片控件
    /// </summary>
    public class DataCard : ContentControl
    {
        static DataCard()
        {
            DefaultStyleKeyProperty.OverrideMetadata(
                typeof(DataCard),
                new FrameworkPropertyMetadata(typeof(DataCard)));
        }
        
        #region 依赖属性
        
        public static readonly DependencyProperty TitleProperty =
            DependencyProperty.Register(
                nameof(Title),
                typeof(string),
                typeof(DataCard),
                new PropertyMetadata(string.Empty));
                
        public string Title
        {
            get => (string)GetValue(TitleProperty);
            set => SetValue(TitleProperty, value);
        }
        
        public static readonly DependencyProperty IconProperty =
            DependencyProperty.Register(
                nameof(Icon),
                typeof(IconElement),
                typeof(DataCard));
                
        public IconElement Icon
        {
            get => (IconElement)GetValue(IconProperty);
            set => SetValue(IconProperty, value);
        }
        
        public static readonly DependencyProperty ValueProperty =
            DependencyProperty.Register(
                nameof(Value),
                typeof(object),
                typeof(DataCard));
                
        public object Value
        {
            get => GetValue(ValueProperty);
            set => SetValue(ValueProperty, value);
        }
        
        #endregion
        
        public override void OnApplyTemplate()
        {
            base.OnApplyTemplate();
            
            // 添加悬停效果
            MouseEnter += (s, e) => VisualStateManager.GoToState(this, "PointerOver", true);
            MouseLeave += (s, e) => VisualStateManager.GoToState(this, "Normal", true);
        }
    }
}
```

## 6. 样式定义

```xml
<!-- Themes/DataCard.xaml -->
<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:local="clr-namespace:LIVAnalyzer.UI.Controls"
                    xmlns:ui="http://schemas.modernwpf.com/2019">
    
    <Style TargetType="local:DataCard">
        <Setter Property="Background" Value="{DynamicResource CardBackgroundFillColorDefaultBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource CardStrokeColorDefaultBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="CornerRadius" Value="8"/>
        <Setter Property="Padding" Value="16"/>
        <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
        <Setter Property="VerticalContentAlignment" Value="Stretch"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="local:DataCard">
                    <Grid>
                        <Border x:Name="BackgroundBorder"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="{TemplateBinding CornerRadius}">
                            <Border.Effect>
                                <DropShadowEffect x:Name="Shadow"
                                                  BlurRadius="8"
                                                  ShadowDepth="2"
                                                  Direction="270"
                                                  Opacity="0.2"
                                                  Color="{DynamicResource SystemChromeBlackMediumColor}"/>
                            </Border.Effect>
                        </Border>
                        
                        <Grid Margin="{TemplateBinding Padding}">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>
                            
                            <!-- 标题栏 -->
                            <Grid Grid.Row="0" Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                
                                <ContentPresenter x:Name="IconPresenter"
                                                  Grid.Column="0"
                                                  Content="{TemplateBinding Icon}"
                                                  VerticalAlignment="Center"
                                                  Margin="0,0,12,0"/>
                                
                                <TextBlock Grid.Column="1"
                                           Text="{TemplateBinding Title}"
                                           Style="{DynamicResource SubtitleTextBlockStyle}"
                                           VerticalAlignment="Center"/>
                            </Grid>
                            
                            <!-- 内容 -->
                            <ContentPresenter Grid.Row="1"
                                              Content="{TemplateBinding Value}"
                                              HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                              VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                        </Grid>
                        
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="CommonStates">
                                <VisualState x:Name="Normal">
                                    <Storyboard>
                                        <DoubleAnimation Storyboard.TargetName="Shadow"
                                                         Storyboard.TargetProperty="Opacity"
                                                         To="0.2"
                                                         Duration="0:0:0.3"/>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="PointerOver">
                                    <Storyboard>
                                        <DoubleAnimation Storyboard.TargetName="Shadow"
                                                         Storyboard.TargetProperty="Opacity"
                                                         To="0.4"
                                                         Duration="0:0:0.3"/>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
</ResourceDictionary>
```

## 使用建议

1. **主题一致性**：始终使用 ModernWPF 的资源引用，而不是硬编码颜色
2. **响应式设计**：利用 NavigationView 的自适应功能
3. **动画效果**：适度使用，避免过度
4. **性能优化**：大数据集使用虚拟化
5. **无障碍支持**：确保所有控件都有适当的自动化属性

这样就能充分利用 ModernWPF 的所有特性，创建现代化的 WPF 应用程序。