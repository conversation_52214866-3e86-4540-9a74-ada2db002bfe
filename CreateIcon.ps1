# LIV分析工具图标生成脚本
# 此脚本将SVG图标转换为多尺寸ICO文件

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    LIV分析工具图标生成器" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 检查是否安装了ImageMagick
$imageMagickPath = Get-Command "magick" -ErrorAction SilentlyContinue
if (-not $imageMagickPath) {
    Write-Host "错误：未找到ImageMagick！" -ForegroundColor Red
    Write-Host "请先安装ImageMagick：" -ForegroundColor Yellow
    Write-Host "1. 访问 https://imagemagick.org/script/download.php#windows" -ForegroundColor Yellow
    Write-Host "2. 下载并安装Windows版本" -ForegroundColor Yellow
    Write-Host "3. 确保magick命令在PATH中可用" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "或者使用Chocolatey安装：choco install imagemagick" -ForegroundColor Yellow
    Write-Host ""
    
    # 提供备用方案
    Write-Host "备用方案：使用在线转换工具" -ForegroundColor Green
    Write-Host "1. 访问 https://convertio.co/svg-ico/" -ForegroundColor Green
    Write-Host "2. 上传 LIVAnalyzer.UI\Resources\Icons\app-icon.svg" -ForegroundColor Green
    Write-Host "3. 设置输出尺寸为 256x256" -ForegroundColor Green
    Write-Host "4. 下载转换后的ICO文件" -ForegroundColor Green
    
    pause
    exit 1
}

# 设置路径
$svgPath = "LIVAnalyzer.UI\Resources\Icons\app-icon.svg"
$iconDir = "LIVAnalyzer.UI\Resources\Icons"
$icoPath = "$iconDir\app-icon.ico"

# 检查SVG文件是否存在
if (-not (Test-Path $svgPath)) {
    Write-Host "错误：找不到SVG文件：$svgPath" -ForegroundColor Red
    exit 1
}

Write-Host "正在生成图标文件..." -ForegroundColor Green
Write-Host "源文件：$svgPath" -ForegroundColor Gray
Write-Host "目标文件：$icoPath" -ForegroundColor Gray
Write-Host ""

try {
    # 创建临时目录
    $tempDir = "temp_icons"
    if (Test-Path $tempDir) {
        Remove-Item $tempDir -Recurse -Force
    }
    New-Item -ItemType Directory -Path $tempDir | Out-Null
    
    # 生成不同尺寸的PNG文件
    $sizes = @(16, 32, 48, 64, 128, 256)
    $pngFiles = @()
    
    foreach ($size in $sizes) {
        $pngFile = "$tempDir\icon_$size.png"
        Write-Host "生成 ${size}x${size} 像素图标..." -ForegroundColor Yellow
        
        & magick $svgPath -resize "${size}x${size}" -background transparent $pngFile
        
        if ($LASTEXITCODE -eq 0) {
            $pngFiles += $pngFile
            Write-Host "  ✓ 完成" -ForegroundColor Green
        } else {
            Write-Host "  ✗ 失败" -ForegroundColor Red
        }
    }
    
    # 合并为ICO文件
    if ($pngFiles.Count -gt 0) {
        Write-Host ""
        Write-Host "正在合并为ICO文件..." -ForegroundColor Yellow
        
        $pngList = $pngFiles -join " "
        & magick $pngFiles $icoPath
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ ICO文件生成成功！" -ForegroundColor Green
            
            # 显示文件信息
            $iconFile = Get-Item $icoPath
            Write-Host ""
            Write-Host "文件信息：" -ForegroundColor Cyan
            Write-Host "  路径：$($iconFile.FullName)" -ForegroundColor Gray
            Write-Host "  大小：$([math]::Round($iconFile.Length / 1KB, 2)) KB" -ForegroundColor Gray
            Write-Host "  包含尺寸：$($sizes -join 'x, ')x 像素" -ForegroundColor Gray
        } else {
            Write-Host "✗ ICO文件生成失败！" -ForegroundColor Red
        }
    }
    
    # 清理临时文件
    Remove-Item $tempDir -Recurse -Force
    
} catch {
    Write-Host "错误：$($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "下一步：集成到WPF应用程序" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "1. 更新项目文件以包含图标" -ForegroundColor Yellow
Write-Host "2. 设置窗口图标属性" -ForegroundColor Yellow
Write-Host "3. 配置应用程序清单" -ForegroundColor Yellow
Write-Host ""

pause
