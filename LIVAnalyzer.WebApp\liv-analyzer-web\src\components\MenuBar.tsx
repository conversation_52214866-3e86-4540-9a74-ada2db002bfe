import { Link } from "react-router-dom";

export default function MenuBar() {
  return (
    <div className="w-full text-sm select-none border-b bg-background/60 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="mx-auto max-w-full px-4">
        <div className="flex h-10 items-center gap-6">
          <MenuGroup label="File" items={[
            { label: "Open File(s)", action: () => document.getElementById("__open_files_btn")?.click() },
            { label: "Recent Files", action: () => window.alert("Recent Files: 待实现") },
            { label: "Export Results", action: () => window.dispatchEvent(new CustomEvent("export-results")) },
            { label: "Export Charts", action: () => {
              // 统一导出四类图表为PNG
              const containers = document.querySelectorAll('[data-chart-container]');
              containers.forEach(async (c) => {
                const name = (c as HTMLElement).getAttribute('data-chart-container') || 'chart';
                const el = (c as HTMLElement).querySelector('svg')?.parentElement ?? (c as HTMLElement);
                const { exportPngFromContainer } = await import('../services/export');
                exportPngFromContainer(el as HTMLElement, name);
              });
            } },
            { label: "Exit", action: () => window.alert("请关闭浏览器标签页") },
          ]} />
          <MenuGroup label="Edit" items={[
            { label: "Undo", action: () => document.execCommand("undo") },
            { label: "Redo", action: () => document.execCommand("redo") },
            { label: "Copy", action: () => document.execCommand("copy") },
            { label: "Paste", action: () => document.execCommand("paste") },
            { label: "Select All", action: () => document.execCommand("selectAll") },
          ]} />
          <MenuGroup label="View" items={[
            { label: "Zoom In", action: () => document.body.style.setProperty("zoom", String((Number(document.body.style.zoom || 1) || 1) + 0.1)) },
            { label: "Zoom Out", action: () => document.body.style.setProperty("zoom", String((Number(document.body.style.zoom || 1) || 1) - 0.1)) },
            { label: "Fit to Window", action: () => document.body.style.removeProperty("zoom") },
            { label: "Grid On/Off", action: () => window.dispatchEvent(new CustomEvent("toggle-grid")) },
            { label: "Legend On/Off", action: () => window.dispatchEvent(new CustomEvent("toggle-legend")) },
          ]} />
          <MenuGroup label="Tools" items={[
            { label: "Settings", action: () => window.dispatchEvent(new CustomEvent("open-settings")) },
            { label: "Preferences", action: () => window.dispatchEvent(new CustomEvent("open-preferences")) },
            { label: "Reset Config", action: () => window.dispatchEvent(new CustomEvent("reset-config")) },
          ]} />
          <MenuGroup label="Help" items={[
            { label: "Documentation", action: () => window.open("/docs", "_self") },
            { label: "Keyboard Shortcuts", action: () => window.alert("Shortcuts: 待完善") },
            { label: "About", action: () => window.open("/about", "_self") },
          ]} />

          <div className="ml-auto flex items-center gap-4 text-muted-foreground">
            <Link to="/regress" className="hover:underline">回归对齐</Link>
            <Link to="/batch" className="hover:underline">批量处理</Link>
          </div>
        </div>
      </div>
      <div className="h-px border-t" />
    </div>
  );
}

function MenuGroup({ label, items }: { label: string; items: { label: string; action: () => void }[] }) {
  return (
    <div className="group relative">
      <button className="px-2 py-1 rounded hover:bg-accent hover:text-accent-foreground">{label}</button>
      <div className="invisible group-hover:visible opacity-0 group-hover:opacity-100 transition-opacity absolute left-0 mt-1 min-w-[180px] rounded-md border bg-popover text-popover-foreground shadow-md z-50">
        <ul className="py-1 text-sm">
          {items.map((it) => (
            <li key={it.label}>
              <button className="w-full text-left px-3 py-1.5 hover:bg-accent/60" onClick={it.action}>{it.label}</button>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
}


