@echo off
echo ====================================
echo      LIV Analyzer C# 版本
echo      安装和运行向导
echo ====================================
echo.

REM 检查.NET SDK是否已安装
echo [1/4] 检查.NET SDK...
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未检测到.NET SDK
    echo.
    echo 📥 请按照以下步骤安装.NET 6 SDK:
    echo    1. 访问: https://dotnet.microsoft.com/download/dotnet/6.0
    echo    2. 下载并安装 ".NET 6.0 SDK" (不是Runtime)
    echo    3. 安装完成后重新运行此脚本
    echo.
    pause
    exit /b 1
) else (
    echo ✅ .NET SDK 已安装
    dotnet --version
)

echo.
echo [2/4] 进入项目目录...
cd /d "%~dp0"
if not exist "LIVAnalyzer.sln" (
    echo ❌ 找不到解决方案文件
    pause
    exit /b 1
)
echo ✅ 找到项目文件

echo.
echo [3/4] 还原NuGet包...
dotnet restore
if %errorlevel% neq 0 (
    echo ❌ 包还原失败
    pause
    exit /b 1
)
echo ✅ 包还原成功

echo.
echo [4/4] 编译项目...
dotnet build --configuration Release
if %errorlevel% neq 0 (
    echo ❌ 编译失败
    pause
    exit /b 1
)
echo ✅ 编译成功

echo.
echo 🎉 项目准备完成!
echo.
echo 可用操作:
echo   R - 运行应用程序
echo   T - 运行单元测试  
echo   P - 发布可执行文件
echo   Q - 退出
echo.

:menu
set /p choice="请选择操作 (R/T/P/Q): "
if /i "%choice%"=="R" goto run
if /i "%choice%"=="T" goto test
if /i "%choice%"=="P" goto publish
if /i "%choice%"=="Q" goto quit
echo 无效选择，请重新输入
goto menu

:run
echo.
echo 🚀 启动LIV分析工具...
dotnet run --project LIVAnalyzer.UI --configuration Release
goto menu

:test
echo.
echo 🧪 运行单元测试...
dotnet test --configuration Release --verbosity normal
echo.
pause
goto menu

:publish
echo.
echo 📦 发布可执行文件...
if not exist "Release" mkdir Release
dotnet publish LIVAnalyzer.UI --configuration Release --runtime win-x64 --self-contained true --output Release\LIVAnalyzer -p:PublishSingleFile=true -p:IncludeNativeLibrariesForSelfExtract=true
if %errorlevel% equ 0 (
    echo ✅ 发布成功! 可执行文件位于: Release\LIVAnalyzer\LIVAnalyzer.exe
) else (
    echo ❌ 发布失败
)
echo.
pause
goto menu

:quit
echo 再见! 👋
pause
exit /b 0