<Window x:Class="LIVAnalyzer.UI.Views.AxisSettingsDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="坐标轴设置"
        Height="400" Width="450"
        MinHeight="350" MinWidth="400"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize">

    <Window.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
    </Window.Resources>
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- 标题信息 -->
        <Border Grid.Row="0" Background="{DynamicResource AppControlBackgroundBrush}"
                CornerRadius="4" Padding="16,12" Margin="0,0,0,16">
            <StackPanel>
                <TextBlock Text="{Binding ChartName}" FontWeight="Bold" FontSize="14" Margin="0,0,0,4"/>
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="坐标轴类型: " Opacity="0.8"/>
                    <TextBlock Text="{Binding AxisType}" FontWeight="Medium"/>
                </StackPanel>
                <StackPanel Orientation="Horizontal" Margin="0,2,0,0">
                    <TextBlock Text="轴标题: " Opacity="0.8"/>
                    <TextBlock Text="{Binding AxisTitle}" FontWeight="Medium"/>
                </StackPanel>
            </StackPanel>
        </Border>
        
        <!-- 设置内容 -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
            <StackPanel>
                <!-- 范围设置 -->
                <GroupBox Header="坐标轴范围设置" Margin="0,0,0,16">
                    <Grid Margin="8">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="80"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <!-- 最小值 -->
                        <TextBlock Text="最小值:" Grid.Row="0" Grid.Column="0" VerticalAlignment="Center" Margin="0,0,0,12"/>
                        <TextBox x:Name="MinimumTextBox" Grid.Row="0" Grid.Column="1" 
                                 Text="{Binding AxisMinimum, UpdateSourceTrigger=LostFocus}"
                                 VerticalAlignment="Center" Margin="0,0,0,12"
                                 KeyDown="TextBox_KeyDown"/>
                        
                        <!-- 最大值 -->
                        <TextBlock Text="最大值:" Grid.Row="1" Grid.Column="0" VerticalAlignment="Center" Margin="0,0,0,12"/>
                        <TextBox x:Name="MaximumTextBox" Grid.Row="1" Grid.Column="1" 
                                 Text="{Binding AxisMaximum, UpdateSourceTrigger=LostFocus}"
                                 VerticalAlignment="Center" Margin="0,0,0,12"
                                 KeyDown="TextBox_KeyDown"/>
                        
                        <!-- 快捷操作 -->
                        <StackPanel Grid.Row="2" Grid.Column="0" Grid.ColumnSpan="2" 
                                    Orientation="Horizontal" HorizontalAlignment="Center">
                            <Button Content="使用当前范围" Command="{Binding UseCurrentRangeCommand}"
                                    Style="{DynamicResource WhiteButton}" Margin="0,0,8,0" Width="120"/>
                            <Button Content="重置为自动" Command="{Binding ResetToAutoRangeCommand}"
                                    Style="{DynamicResource WhiteButton}" Width="120"/>
                        </StackPanel>
                    </Grid>
                </GroupBox>
                
                <!-- 说明文字 -->
                <Border Background="{DynamicResource AppControlBackgroundBrush}"
                        CornerRadius="4" Padding="12" Margin="0,0,0,8">
                    <StackPanel>
                        <TextBlock Text="使用说明:" FontWeight="Medium" Margin="0,0,0,4"/>
                        <TextBlock Text="• 直接输入数值设定坐标轴范围，输入完成后按回车或点击应用" 
                                   TextWrapping="Wrap" Opacity="0.8" FontSize="11" Margin="0,0,0,2"/>
                        <TextBlock Text="• 设定自定义范围后，该坐标轴将固定在指定范围内" 
                                   TextWrapping="Wrap" Opacity="0.8" FontSize="11" Margin="0,0,0,2"/>
                        <TextBlock Text="• 使用'重置为自动'可恢复自动调整以适应数据" 
                                   TextWrapping="Wrap" Opacity="0.8" FontSize="11" Margin="0,0,0,2"/>
                        <TextBlock Text="• 自定义范围时会禁用该轴的缩放和平移功能" 
                                   TextWrapping="Wrap" Opacity="0.8" FontSize="11"/>
                    </StackPanel>
                </Border>
            </StackPanel>
        </ScrollViewer>
        
        <!-- 按钮区域 -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,16,0,0">
            <Button Content="应用" Command="{Binding ApplyCommand}" IsDefault="True"
                    Style="{DynamicResource AccentButtonStyle}" Margin="0,0,8,0" Width="80"/>
            <Button Content="取消" Command="{Binding CancelCommand}" IsCancel="True"
                    Style="{DynamicResource WhiteButton}" Width="80"/>
        </StackPanel>
    </Grid>
</Window>