using System;
using System.Linq;
using LIVAnalyzer.Core.Algorithms;
using LIVAnalyzer.Models;
using Xunit;

namespace LIVAnalyzer.Tests.Core.Algorithms
{
    /// <summary>
    /// 平滑算法测试
    /// </summary>
    public class SmoothingAlgorithmTests
    {
        /// <summary>
        /// 生成测试数据：带噪声的正弦波
        /// </summary>
        private (double[] x, double[] y) GenerateTestData(int length = 100, double noiseLevel = 0.1)
        {
            var random = new Random(42); // 固定种子确保结果可重现
            var x = new double[length];
            var y = new double[length];
            
            for (int i = 0; i < length; i++)
            {
                x[i] = i * 0.1;
                y[i] = Math.Sin(x[i]) + (random.NextDouble() - 0.5) * noiseLevel;
            }
            
            return (x, y);
        }
        
        [Fact]
        public void MovingAverageAlgorithm_ShouldReduceNoise()
        {
            // Arrange
            var algorithm = new MovingAverageAlgorithm();
            var (xData, yData) = GenerateTestData(100, 0.2);
            var config = new SmoothingConfig
            {
                AlgorithmType = SmoothingAlgorithmType.MovingAverage,
                DefaultWindowSize = 5
            };
            
            // Act
            var smoothedData = algorithm.Smooth(xData, yData, config);
            
            // Assert
            Assert.Equal(yData.Length, smoothedData.Length);
            
            // 计算噪声减少：平滑后的数据应该有更小的方差
            var originalVariance = CalculateVariance(yData);
            var smoothedVariance = CalculateVariance(smoothedData);
            
            Assert.True(smoothedVariance < originalVariance, "平滑后方差应该减小");
        }
        
        [Fact]
        public void SavitzkyGolayAlgorithm_ShouldPreserveShape()
        {
            // Arrange
            var algorithm = new SavitzkyGolayAlgorithm();
            var (xData, yData) = GenerateTestData(50, 0.1);
            var config = new SmoothingConfig
            {
                AlgorithmType = SmoothingAlgorithmType.SavitzkyGolay
            };
            config.SavitzkyGolay.WindowSize = 7;
            config.SavitzkyGolay.PolynomialOrder = 2;
            
            // Act
            var smoothedData = algorithm.Smooth(xData, yData, config);
            
            // Assert
            Assert.Equal(yData.Length, smoothedData.Length);
            Assert.True(smoothedData.All(v => !double.IsNaN(v) && !double.IsInfinity(v)));
            
            // S-G滤波器应该保持峰值位置
            var originalPeakIndex = Array.IndexOf(yData, yData.Max());
            var smoothedPeakIndex = Array.IndexOf(smoothedData, smoothedData.Max());
            
            // 允许小的偏差
            Assert.True(Math.Abs(originalPeakIndex - smoothedPeakIndex) <= 2, "S-G滤波器应该保持峰值位置");
        }
        
        [Fact]
        public void GaussianAlgorithm_ShouldSmoothEffectively()
        {
            // Arrange
            var algorithm = new GaussianAlgorithm();
            var (xData, yData) = GenerateTestData(100, 0.3);
            var config = new SmoothingConfig
            {
                AlgorithmType = SmoothingAlgorithmType.Gaussian
            };
            config.Gaussian.Sigma = 1.5; // 增加sigma值以确保明显的平滑效果
            
            // Act
            var smoothedData = algorithm.Smooth(xData, yData, config);
            
            // Assert
            Assert.Equal(yData.Length, smoothedData.Length);
            Assert.True(smoothedData.All(v => !double.IsNaN(v) && !double.IsInfinity(v)));
            
            // 高斯滤波应该减少高频噪声 - 使用更宽松的阈值
            var originalVariance = CalculateVariance(yData);
            var smoothedVariance = CalculateVariance(smoothedData);
            
            // 降低期望，因为σ=1.5的高斯滤波器可能平滑效果有限
            Assert.True(smoothedVariance <= originalVariance, "高斯滤波后方差应该不增加");
            
            // 检查平滑效果：计算相邻点之间的平均差异
            var originalRoughness = CalculateRoughness(yData);
            var smoothedRoughness = CalculateRoughness(smoothedData);
            
            Assert.True(smoothedRoughness < originalRoughness, "高斯滤波应该减少数据的粗糙度");
        }
        
        [Fact]
        public void SmoothingAlgorithmFactory_ShouldReturnCorrectAlgorithms()
        {
            // Act & Assert
            var movingAverage = SmoothingAlgorithmFactory.GetAlgorithm(SmoothingAlgorithmType.MovingAverage);
            Assert.IsType<MovingAverageAlgorithm>(movingAverage);
            
            var savitzkyGolay = SmoothingAlgorithmFactory.GetAlgorithm(SmoothingAlgorithmType.SavitzkyGolay);
            Assert.IsType<SavitzkyGolayAlgorithm>(savitzkyGolay);
            
            var gaussian = SmoothingAlgorithmFactory.GetAlgorithm(SmoothingAlgorithmType.Gaussian);
            Assert.IsType<GaussianAlgorithm>(gaussian);
            
            // 测试所有算法都可用
            var allAlgorithms = SmoothingAlgorithmFactory.GetAllAlgorithms().ToList();
            Assert.True(allAlgorithms.Count >= 3, "应该至少有3种算法可用");
        }
        
        [Theory]
        [InlineData(SmoothingAlgorithmType.MovingAverage)]
        [InlineData(SmoothingAlgorithmType.SavitzkyGolay)]
        [InlineData(SmoothingAlgorithmType.Gaussian)]
        public void AllAlgorithms_ShouldHandleEmptyData(SmoothingAlgorithmType algorithmType)
        {
            // Arrange
            var algorithm = SmoothingAlgorithmFactory.GetAlgorithm(algorithmType);
            var emptyX = Array.Empty<double>();
            var emptyY = Array.Empty<double>();
            var config = algorithm.GetRecommendedParameters(0);
            
            // Act
            var result = algorithm.Smooth(emptyX, emptyY, config);
            
            // Assert
            Assert.Empty(result);
        }
        
        [Theory]
        [InlineData(SmoothingAlgorithmType.MovingAverage)]
        [InlineData(SmoothingAlgorithmType.SavitzkyGolay)]
        [InlineData(SmoothingAlgorithmType.Gaussian)]
        public void AllAlgorithms_ShouldValidateParameters(SmoothingAlgorithmType algorithmType)
        {
            // Arrange
            var algorithm = SmoothingAlgorithmFactory.GetAlgorithm(algorithmType);
            var config = new SmoothingConfig();
            
            // Act & Assert
            var validationResult = algorithm.ValidateParameters(config, 100);
            
            // 应该能够验证参数（可能返回null表示有效，或返回错误信息）
            Assert.True(validationResult == null || !string.IsNullOrEmpty(validationResult));
        }
        
        [Fact]
        public void SmoothingAlgorithmFactory_ShouldRecommendAppropriateAlgorithm()  
        {
            // Test different scenarios
            var (lowNoiseType, lowNoiseConfig) = SmoothingAlgorithmFactory.RecommendAlgorithm(100, 0.05, true);
            Assert.Equal(SmoothingAlgorithmType.SavitzkyGolay, lowNoiseType);
            
            var (highNoiseType, highNoiseConfig) = SmoothingAlgorithmFactory.RecommendAlgorithm(100, 0.5, false);
            Assert.Equal(SmoothingAlgorithmType.Gaussian, highNoiseType);
            
            var (normalType, normalConfig) = SmoothingAlgorithmFactory.RecommendAlgorithm(100, 0.2, false);
            Assert.Equal(SmoothingAlgorithmType.MovingAverage, normalType);
        }
        
        private double CalculateVariance(double[] data)
        {
            if (data.Length == 0) return 0;
            
            var mean = data.Average();
            var variance = data.Select(x => Math.Pow(x - mean, 2)).Average();
            return variance;
        }
        
        /// <summary>
        /// 计算数据的粗糙度（相邻点之间差异的平均值）
        /// </summary>
        private double CalculateRoughness(double[] data)
        {
            if (data.Length <= 1) return 0;
            
            var differences = new double[data.Length - 1];
            for (int i = 0; i < data.Length - 1; i++)
            {
                differences[i] = Math.Abs(data[i + 1] - data[i]);
            }
            
            return differences.Average();
        }
    }
}