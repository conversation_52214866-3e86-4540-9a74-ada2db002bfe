using System;
using LIVAnalyzer.Models;

namespace LIVAnalyzer.Core.Algorithms
{
    /// <summary>
    /// 高斯滤波器算法
    /// 使用高斯核进行卷积，提供优秀的平滑效果和噪声抑制
    /// </summary>
    public class GaussianAlgorithm : ISmoothingAlgorithm
    {
        public string Name => "高斯滤波器";
        
        public string Description => "使用高斯核进行卷积平滑，提供优秀的噪声抑制效果，适合高噪声数据";
        
        public SmoothingAlgorithmType AlgorithmType => SmoothingAlgorithmType.Gaussian;
        
        public bool SupportsRealTimePreview => true;
        
        public double[] Smooth(double[] xData, double[] yData, SmoothingConfig config)
        {
            if (xData == null || yData == null)
                throw new ArgumentNullException("输入数据不能为空");
            
            if (xData.Length != yData.Length)
                throw new ArgumentException("X和Y数据长度不匹配");
            
            if (xData.Length == 0)
                return Array.Empty<double>();
            
            var gaussianConfig = config.Gaussian;
            double sigma = Math.Max(gaussianConfig.MinSigma, 
                Math.Min(gaussianConfig.MaxSigma, gaussianConfig.Sigma));
            
            return GaussianFilter(yData, sigma, gaussianConfig.KernelSizeMultiplier);
        }
        
        public string? ValidateParameters(SmoothingConfig config, int dataLength)
        {
            var gaussianConfig = config.Gaussian;
            
            if (gaussianConfig.Sigma < gaussianConfig.MinSigma)
                return $"标准差不能小于 {gaussianConfig.MinSigma}";
                
            if (gaussianConfig.Sigma > gaussianConfig.MaxSigma)
                return $"标准差不能大于 {gaussianConfig.MaxSigma}";
            
            int kernelSize = (int)(gaussianConfig.Sigma * gaussianConfig.KernelSizeMultiplier);
            if (kernelSize >= dataLength)
                return "高斯核大小不能大于等于数据长度";
            
            return null;
        }
        
        public SmoothingConfig GetRecommendedParameters(int dataLength, double noiseLevel = 0.1)
        {
            // 根据数据长度和噪声水平推荐标准差
            double recommendedSigma = Math.Max(0.5, Math.Min(
                dataLength * 0.01, // 不超过数据长度的1%
                0.5 + noiseLevel * 2.0 // 噪声越大，平滑越强
            ));
            
            var config = new SmoothingConfig
            {
                AlgorithmType = SmoothingAlgorithmType.Gaussian,
                EnableByDefault = false
            };
            
            config.Gaussian.Sigma = recommendedSigma;
            
            return config;
        }
        
        /// <summary>
        /// 高斯滤波器实现
        /// </summary>
        /// <param name="data">输入数据</param>
        /// <param name="sigma">高斯分布标准差</param>
        /// <param name="kernelSizeMultiplier">核大小倍数</param>
        /// <returns>平滑后的数据</returns>
        private static double[] GaussianFilter(double[] data, double sigma, double kernelSizeMultiplier)
        {
            if (data == null || data.Length == 0)
                return Array.Empty<double>();
            
            // 计算核大小
            int kernelSize = (int)(sigma * kernelSizeMultiplier);
            if (kernelSize % 2 == 0) kernelSize++; // 确保为奇数
            kernelSize = Math.Max(3, Math.Min(kernelSize, data.Length / 2));
            
            // 生成高斯核
            var kernel = GenerateGaussianKernel(kernelSize, sigma);
            
            // 进行卷积
            return Convolve(data, kernel);
        }
        
        /// <summary>
        /// 生成高斯核
        /// </summary>
        /// <param name="size">核大小</param>
        /// <param name="sigma">标准差</param>
        /// <returns>归一化的高斯核</returns>
        private static double[] GenerateGaussianKernel(int size, double sigma)
        {
            var kernel = new double[size];
            int center = size / 2;
            double sum = 0;
            
            // 计算高斯权重
            for (int i = 0; i < size; i++)
            {
                double x = i - center;
                double weight = Math.Exp(-(x * x) / (2 * sigma * sigma));
                kernel[i] = weight;
                sum += weight;
            }
            
            // 归一化
            for (int i = 0; i < size; i++)
            {
                kernel[i] /= sum;
            }
            
            return kernel;
        }
        
        /// <summary>
        /// 一维卷积操作
        /// </summary>
        /// <param name="data">输入数据</param>
        /// <param name="kernel">卷积核</param>
        /// <returns>卷积结果</returns>
        private static double[] Convolve(double[] data, double[] kernel)
        {
            var result = new double[data.Length];
            int halfKernel = kernel.Length / 2;
            
            for (int i = 0; i < data.Length; i++)
            {
                double sum = 0;
                double weightSum = 0;
                
                for (int j = 0; j < kernel.Length; j++)
                {
                    int dataIndex = i + j - halfKernel;
                    
                    // 边界处理：镜像填充
                    if (dataIndex < 0)
                        dataIndex = -dataIndex;
                    else if (dataIndex >= data.Length)
                        dataIndex = 2 * data.Length - dataIndex - 2;
                    
                    if (dataIndex >= 0 && dataIndex < data.Length && 
                        !double.IsNaN(data[dataIndex]) && !double.IsInfinity(data[dataIndex]))
                    {
                        sum += data[dataIndex] * kernel[j];
                        weightSum += kernel[j];
                    }
                }
                
                result[i] = weightSum > 0 ? sum / weightSum : data[i];
            }
            
            return result;
        }
    }
}