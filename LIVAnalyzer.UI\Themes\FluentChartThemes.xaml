<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- Fluent Design 图表主题配置 -->
    
    <!-- 图表容器样式 -->
    <Style x:Key="FluentChartContainerStyle" TargetType="Border">
        <Setter Property="Background" Value="{DynamicResource FluentCardBackgroundBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource FluentBorderBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="CornerRadius" Value="{StaticResource FluentCardCornerRadius}"/>
        <Setter Property="Padding" Value="20"/>
        <Setter Property="Margin" Value="{StaticResource FluentCardMargin}"/>
        <Setter Property="Effect" Value="{StaticResource FluentCardShadow}"/>
    </Style>
    
    <!-- 图表标题样式 -->
    <Style x:Key="FluentChartTitleStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource FluentDisplayFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource FluentSubtitleFontSize}"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Foreground" Value="{DynamicResource FluentPrimaryTextBrush}"/>
        <Setter Property="Margin" Value="0,0,0,16"/>
        <Setter Property="HorizontalAlignment" Value="Center"/>
    </Style>
    
    <!-- 图表说明文字样式 -->
    <Style x:Key="FluentChartCaptionStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource FluentBodyFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource FluentCaptionFontSize}"/>
        <Setter Property="Foreground" Value="{DynamicResource FluentSecondaryTextBrush}"/>
        <Setter Property="Margin" Value="0,8,0,0"/>
        <Setter Property="HorizontalAlignment" Value="Center"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
    </Style>
    
    <!-- 图表工具栏样式 -->
    <Style x:Key="FluentChartToolbarStyle" TargetType="ToolBar">
        <Setter Property="Background" Value="{DynamicResource FluentSurfaceBackgroundBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource FluentBorderBrush}"/>
        <Setter Property="BorderThickness" Value="0,0,0,1"/>
        <Setter Property="Padding" Value="8"/>
        <Setter Property="Margin" Value="0,0,0,8"/>
    </Style>
    
    <!-- 图表按钮样式 -->
    <Style x:Key="FluentChartButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="{DynamicResource FluentButtonBackgroundBrush}"/>
        <Setter Property="Foreground" Value="{DynamicResource FluentPrimaryTextBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource FluentBorderBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Padding" Value="12,6"/>
        <Setter Property="Margin" Value="4,2"/>
        <Setter Property="FontFamily" Value="{StaticResource FluentBodyFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource FluentBodyFontSize}"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Name="Border"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="{StaticResource FluentButtonCornerRadius}"
                            Padding="{TemplateBinding Padding}">
                        <ContentPresenter HorizontalAlignment="Center"
                                        VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="Border" Property="Background" 
                                    Value="{DynamicResource FluentButtonHoverBackgroundBrush}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="Border" Property="Background" 
                                    Value="{DynamicResource FluentButtonPressedBackgroundBrush}"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="Border" Property="Opacity" Value="0.6"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    
    <!-- 图表设置面板样式 -->
    <Style x:Key="FluentChartSettingsPanelStyle" TargetType="StackPanel">
        <Setter Property="Background" Value="{DynamicResource FluentSurfaceBackgroundBrush}"/>
        <Setter Property="Margin" Value="0,8,0,0"/>
        <Setter Property="Orientation" Value="Horizontal"/>
        <Setter Property="HorizontalAlignment" Value="Center"/>
    </Style>
    
    <!-- 图表图例样式 -->
    <Style x:Key="FluentChartLegendStyle" TargetType="ItemsControl">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="HorizontalAlignment" Value="Center"/>
        <Setter Property="Margin" Value="0,16,0,0"/>
        <Setter Property="ItemsPanel">
            <Setter.Value>
                <ItemsPanelTemplate>
                    <WrapPanel Orientation="Horizontal" 
                              HorizontalAlignment="Center"/>
                </ItemsPanelTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    
    <!-- 图例项样式 -->
    <Style x:Key="FluentLegendItemStyle" TargetType="Border">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="Padding" Value="8,4"/>
        <Setter Property="Margin" Value="4,2"/>
        <Setter Property="CornerRadius" Value="4"/>
    </Style>
    
    <!-- 图例颜色标识样式 -->
    <Style x:Key="FluentLegendColorStyle" TargetType="Rectangle">
        <Setter Property="Width" Value="12"/>
        <Setter Property="Height" Value="12"/>
        <Setter Property="RadiusX" Value="2"/>
        <Setter Property="RadiusY" Value="2"/>
        <Setter Property="Margin" Value="0,0,6,0"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
    </Style>
    
    <!-- 图例文字样式 -->
    <Style x:Key="FluentLegendTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource FluentBodyFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource FluentCaptionFontSize}"/>
        <Setter Property="Foreground" Value="{DynamicResource FluentSecondaryTextBrush}"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
    </Style>
    
</ResourceDictionary>