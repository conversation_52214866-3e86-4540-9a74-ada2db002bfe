using System;
using System.IO;
using System.Reflection;
using System.Text;
using LIVAnalyzer.Services.Logging;

namespace LIVAnalyzer.Services.Documents
{
    /// <summary>
    /// 文档服务，用于加载和管理应用程序文档
    /// </summary>
    public static class DocumentService
    {
        /// <summary>
        /// 获取使用指南内容
        /// </summary>
        public static string GetUserGuide()
        {
            return LoadDocumentFromCurrentProject("使用指南.md") ?? GetDefaultUserGuide();
        }

        /// <summary>
        /// 获取技术文档内容
        /// </summary>
        public static string GetTechnicalDocumentation()
        {
            return LoadDocumentFromCurrentProject("技术文档.md") ?? GetDefaultTechnicalDoc();
        }

        /// <summary>
        /// 获取发布说明内容
        /// </summary>
        public static string GetReleaseNotes()
        {
            return LoadDocumentFromCurrentProject("发布说明.md") ?? GetDefaultReleaseNotes();
        }

        /// <summary>
        /// 获取关于信息
        /// </summary>
        public static string GetAboutInfo()
        {
            return LoadDocumentFromCurrentProject("关于.md") ?? GetDefaultAboutInfo();
        }

        /// <summary>
        /// 从当前C#项目根目录加载文档
        /// </summary>
        private static string? LoadDocumentFromCurrentProject(string fileName)
        {
            try
            {
                // 获取C#版本项目路径
                var currentDir = Directory.GetCurrentDirectory();

                // 查找可能的文档路径
                var possiblePaths = new[]
                {
                    // 1. 当前工作目录（发布版本）
                    Path.Combine(currentDir, fileName),

                    // 2. 项目根目录（开发环境）
                    Path.Combine(currentDir, "..", "..", "..", fileName),
                    Path.Combine(currentDir, "..", "..", fileName),
                    Path.Combine(currentDir, "..", fileName),

                    // 3. 相对于可执行文件的位置
                    Path.Combine(Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location) ?? currentDir, fileName),

                    // 4. 项目Resources文件夹
                    Path.Combine(currentDir, "Resources", fileName),
                };

                foreach (var path in possiblePaths)
                {
                    if (File.Exists(path))
                    {
                        LoggingService.LogInformation($"找到文档: {path}");
                        return File.ReadAllText(path, Encoding.UTF8);
                    }
                }

                LoggingService.LogWarning($"未找到文档文件: {fileName}");
                return null;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"加载文档失败: {fileName}");
                return null;
            }
        }

        /// <summary>
        /// 从Python项目的resources文件夹加载文档（保留兼容性）
        /// </summary>
        private static string? LoadDocumentFromPythonProject(string fileName)
        {
            try
            {
                // 获取C#版本项目路径
                var currentDir = Directory.GetCurrentDirectory();
                
                // 查找可能的文档路径
                var possiblePaths = new[]
                {
                    // 1. 相对于当前工作目录的Python项目resources文件夹
                    Path.Combine(currentDir, "..", "..", "..", "resources", fileName),
                    Path.Combine(currentDir, "..", "..", "resources", fileName),
                    Path.Combine(currentDir, "..", "resources", fileName),
                    
                    // 2. 使用固定的Python项目路径
                    Path.Combine(@"E:\01LaserPackage\software\LIV_Analyzer\resources", fileName),
                    
                    // 3. 相对于C#项目的Resources文件夹
                    Path.Combine(currentDir, "Resources", fileName),
                };

                foreach (var path in possiblePaths)
                {
                    if (File.Exists(path))
                    {
                        LoggingService.LogInformation($"找到文档: {path}");
                        return File.ReadAllText(path, Encoding.UTF8);
                    }
                }

                LoggingService.LogWarning($"未找到文档文件: {fileName}");
                return null;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"加载文档失败: {fileName}");
                return null;
            }
        }

        #region 默认文档内容

        private static string GetDefaultUserGuide()
        {
            return @"# 📖 LIV Analyzer v2.2.3 版本 - 详细使用指南

## 🎯 软件概述

LIV Analyzer v2.2.3 是一款专业的激光器LIV特性曲线分析工具，基于.NET 9和WPF技术开发，采用现代化设计语言。软件提供了完整的激光器测试数据分析功能，包括LIV参数计算、光谱分析、发散角分析和效率曲线分析。本版本引入了革命性的渐进式数据加载技术，实现了所有数据类型的即时响应，显著提升了数据加载速度和用户体验。

### 主要功能特性
- **LIV特性分析** - 阈值电流（一阶导数法）、斜率效率、串联电阻计算
- **光谱数据分析** - 峰值波长、FWHM（插值法）计算
- **发散角分析** - 水平/垂直方向发散角测量，支持导出到Excel
- **效率曲线分析** - 实时效率计算和可视化
- **批量数据处理** - 支持文件夹批量分析
- **交互式图表** - 高质量图表显示和导出，支持高级自定义设置
- **实时参数显示** - 选择曲线时实时显示对应参数
- **原生Fluent Design** - 采用.NET 9原生Fluent Design系统，提供现代化用户体验

## 🚀 快速开始

### 系统要求
- **操作系统**: Windows 10/11 (x64)
- **运行环境**: .NET 9 Runtime（开发需要SDK）
- **内存**: 建议8GB以上
- **硬盘**: 至少1GB可用空间
- **显卡**: 支持DirectX 11或更高版本（用于Fluent Design效果）

### 启动应用程序

#### 方法一：使用运行向导（推荐新手）
1. 双击 `运行向导.bat`
2. 按提示检查和安装.NET 9环境
3. 选择 `R` 运行应用程序

#### 方法二：直接启动
1. 双击 `启动LIV分析工具.bat`
2. 或运行 `LIVAnalyzer.exe`（发布版本）

#### 方法三：开发环境
```bash
# 命令行方式
dotnet run --project LIVAnalyzer.UI

# 或使用Visual Studio 2022
# 双击LIVAnalyzer.sln，设置LIVAnalyzer.UI为启动项目，按F5
```

### Fluent Design 特性
本软件采用.NET 9原生Fluent Design系统，提供以下现代化体验：
- **亚克力材质效果** - 半透明背景和模糊效果
- **流畅动画** - 平滑的过渡和交互动画
- **自适应主题** - 支持浅色、深色和跟随系统主题
- **现代化控件** - 圆角按钮、卡片式布局、悬浮效果
- **响应式设计** - 适配不同屏幕尺寸和DPI设置

## 📁 数据文件格式要求

### Excel文件格式 (.xlsx, .xls)

软件支持标准的Excel文件，要求包含以下工作表：

#### 必需工作表：
1. **wavelength** - 波长数据表
   - 列A: 波长值 (nm)
   - 列B: 强度值

2. **power** - 功率数据表
   - 列A: 电流值 (A)
   - 列B: 功率值 (W)

3. **voltage** - 电压数据表
   - 列A: 电流值 (A)
   - 列B: 电压值 (V)

#### 可选工作表（发散角分析）：
4. **HFF** - 水平发散角数据
   - 列A: 角度值 (度)
   - 列B: 强度值

5. **VFF** - 垂直发散角数据
   - 列A: 角度值 (度)
   - 列B: 强度值

### CSV文件格式 (.csv)

支持标准的CSV格式，列结构与Excel相同，文件命名规则：
- `filename_wavelength.csv` - 光谱数据
- `filename_power.csv` - 功率数据
- `filename_voltage.csv` - 电压数据
- `filename_HFF.csv` - 水平发散角数据
- `filename_VFF.csv` - 垂直发散角数据

## 🖥️ 界面操作指南

### 主界面布局

#### 1. 菜单栏
- **文件菜单**
  - `打开 (Ctrl+O)` - 选择数据文件
  - `COS文件转换工具` - 转换COS格式文件
  - `导出数据 (Ctrl+E)` - 导出分析结果
  - `保存图表 (Ctrl+S)` - 保存当前图表

- **视图菜单**
  - `图表设置 (Ctrl+G)` - 打开高级图表设置对话框

- **帮助菜单**
  - `使用指南 (F1)` - 查看使用说明
  - `技术文档 (F2)` - 查看技术文档
  - `发布说明` - 查看版本更新信息
  - `关于` - 软件版本信息

#### 2. 工具栏
- **数据操作组**
  - `选择数据文件` - 打开文件选择对话框
  - `导出数据` - 导出当前分析结果
  - `保存图表` - 保存当前显示的图表

- **参数设置组**
  - `I1电流` - 设置第一个参考电流点
  - `I2电流` - 设置第二个参考电流点
  - `查询电流` - 设置功率查询的电流值
  - `平滑处理` - 启用/禁用数据平滑
  - `窗口大小` - 设置平滑窗口大小

#### 3. 文件列表区域
- 显示已加载的数据文件
- 支持多选和单选
- 显示文件处理状态
- 右键菜单提供更多操作

#### 4. 图表显示区域
包含多个标签页：
- **LIV曲线** - 电流-功率和电流-电压曲线
- **光谱图** - 波长-强度曲线
- **效率曲线** - 电流-效率曲线
- **发散角** - 角度-强度曲线

#### 5. 参数显示区域
- **计算参数** - 显示LIV分析结果
- **查询结果** - 显示指定电流下的功率值
- **状态信息** - 显示当前操作状态

### 基本操作流程

#### 步骤1：加载数据文件
1. 点击 `选择数据文件` 按钮
2. 在文件对话框中选择Excel或CSV文件
3. 支持多文件同时选择（按住Ctrl键）
4. 确认选择后，软件自动加载和解析数据

#### 步骤2：查看和分析数据
1. 在文件列表中选择要分析的文件
2. 切换不同的图表标签页查看数据
3. 使用鼠标滚轮缩放图表
4. 拖拽图表进行平移
5. 查看右侧参数面板的计算结果

#### 步骤3：调整分析参数
1. **设置I1/I2电流值**
   - 用于计算斜率效率的电流区间
   - 默认值：I1=0.5A, I2=1.0A
   - 可根据实际数据调整

2. **功率查询**
   - 输入查询电流值
   - 软件自动计算该电流下的功率

3. **平滑处理**
   - 启用平滑可减少数据噪声
   - 调整窗口大小控制平滑程度
   - 窗口越大平滑效果越强

#### 步骤4：导出结果
1. **导出数据**
   - 选择要导出的文件（可多选）
   - 点击 `导出数据` 按钮
   - 选择保存位置和文件名
   - 生成包含所有分析结果的Excel文件

2. **保存图表**
   - 点击 `保存图表` 按钮
   - 选择图片格式（PNG/SVG）
   - 设置保存位置和文件名

## 📊 分析参数说明

### LIV参数
- **峰值波长** - 光谱强度最大值对应的波长
- **FWHM** - 光谱半高宽，表征光谱线宽（使用插值法精确计算）
- **阈值电流** - 激光器开始激射的电流值（使用一阶导数法）
- **最大功率** - 测量范围内的最大输出功率
- **最大效率** - P/(I×V)的最大值
- **斜率效率** - I1-I2区间内功率对电流的斜率
- **串联电阻** - 电流-电压曲线的线性拟合电阻
- **微分电阻** - 动态电阻值

### 发散角参数
- **FWHM** - 半高宽发散角
- **FW(1/e²)** - 强度降到峰值1/e²（13.53%）处的宽度
- **FW95%** - 95%功率包含角
- 分别计算水平和垂直方向
- 所有发散角数据自动保存到导出的Excel文件中



## 🔧 高级功能

### 图表设置
通过 `视图` → `图表设置` (Ctrl+G) 可以打开高级图表设置对话框：

#### 预设模板（新功能）
快速应用预定义的配置模板：
- **默认** - 标准显示设置
- **演示模式** - 加粗线条，大字体，高对比度，适合投影展示
- **打印模式** - 适中线条，清晰对比，黑白友好
- **分析模式** - 细线条，详细网格，适合数据分析

#### 可自定义的选项：
1. **线条样式**
   - 线条粗细（0.5-5）
   - 标记大小（2-10）
   - 标记类型（无、圆形、方形、菱形、三角形、十字）

2. **网格设置**
   - 主/次网格透明度（0-100%）
   - 网格线样式（实线、虚线、点线、点划线）
   - 网格线颜色（灰色、黑色、蓝色、红色等）

3. **配色方案**
   - 默认、彩虹、暖色、冷色、单色渐变、高对比度

4. **图例设置**
   - 位置（8个方向可选：左上、右上、左下、右下、顶部居中、底部居中、左边、右边）
   - 字体大小（8-20）

5. **坐标轴设置**
   - 标题字体大小
   - 标签字体大小

6. **数据标签（新功能）**
   - 显示/隐藏数据点数值
   - 标签位置（上方、下方、左侧、右侧、中心）
   - 标签字体大小（8-20）
   - 数值格式（整数、1-3位小数、科学记数法）

#### 实时预览功能
- 开启实时预览后，设置变化会立即应用到图表
- 提供预览/还原按钮，可以测试效果后再确定

#### 保存为默认
- 点击""保存为默认""按钮，将当前设置保存为默认配置
- 下次启动软件时会自动应用保存的设置

### 批量处理
1. 选择包含多个数据文件的文件夹
2. 设置统一的I1/I2参数
3. 软件自动处理所有文件
4. 生成汇总报告（包含发散角数据）

### COS文件转换
1. 点击菜单 `文件` → `COS文件转换工具`
2. 选择COS格式文件
3. 转换为标准Excel格式
4. 可直接用于LIV分析

### 配置管理
软件配置文件位置：`%AppData%\LIVAnalyzer\config.yaml`

可配置项目：
- 默认参数值
- 界面显示选项
- 图表样式设置
- 性能设置
- 文件路径记忆

## 🐛 常见问题解决

### 文件加载问题
**问题**: 提示""文件格式不正确""
**解决**:
- 检查Excel工作表名称是否正确
- 确认数据列格式为数值型
- 检查是否有空行或非数值数据

**问题**: CSV文件无法识别
**解决**:
- 确认文件编码为UTF-8
- 检查分隔符是否为逗号
- 确认文件命名符合规范

### 计算结果异常
**问题**: 阈值电流计算不准确
**解决**:
- 软件使用一阶导数法计算，与Python版本保持一致
- 检查功率数据是否单调递增
- 尝试启用数据平滑
- 调整平滑窗口大小

**问题**: FWHM值计算异常
**解决**:
- 软件使用插值法精确计算半高宽
- 确保光谱数据完整覆盖峰值区域
- 检查是否有多峰结构干扰

**问题**: 发散角无法计算
**解决**:
- 确认HFF/VFF工作表存在
- 检查角度数据范围是否合理
- 确认强度数据不全为零

### 性能问题
**问题**: 软件运行缓慢
**解决**:
- 减少同时加载的文件数量
- 关闭不必要的图表标签页
- 检查系统内存使用情况

**问题**: 图表显示卡顿
**解决**:
- 启用数据平滑减少数据点
- 调整图表显示范围
- 关闭网格显示

## 📞 技术支持

### 日志文件位置
- 一般日志：`%AppData%\LIVAnalyzer\Logs\liv_analyzer.log`
- 错误日志：`%AppData%\LIVAnalyzer\Logs\liv_analyzer_error.log`

### 联系方式
- 开发者：00106
- 技术支持：查看软件内置帮助文档
- 问题反馈：通过软件菜单提交

---

## 🆕 最新更新功能

### V2.1.1 更新内容 (2025-08-05) - 电压数据导出增强版本
1. **Excel导出功能增强**
   - 修复汇总表中缺少I1电压和I2电压列的问题
   - 确保导出数据的完整性和准确性
   - 现在汇总表包含完整的LIV参数信息

2. **批量处理功能修复**
   - 修复批量处理中I1和I2电压计算缺失的问题
   - 修复批量处理中I1和I2效率计算缺失的问题
   - 修复批量处理中发散角能量占比计算缺失的问题
   - 确保批量处理结果与单文件处理结果完全一致

3. **数据完整性提升**
   - 所有导出数据现在包含完整的电压信息
   - 发散角分析包含准确的能量占比数据
   - 提升数据分析的准确性和可用性

4. **开发者信息**
   - 开发者：00106
   - 更新日期：2025年8月8日
   - 版本：v2.2.3 (渐进式加载优化版本)

**祝您使用愉快！如有问题请参考技术文档或联系技术支持。** 🎉";
        }

        private static string GetDefaultTechnicalDoc()
        {
            return @"# LIV分析工具技术文档 (v2.2.3 渐进式加载优化版本)

## 软件架构概述

### 技术栈
- **开发框架**: .NET 9 + WPF
- **UI设计**: 现代化设计系统
- **图表引擎**: OxyPlot 3.x (优化版)
- **数据处理**: EPPlus (Excel), CsvHelper (CSV)
- **架构模式**: MVVM (Model-View-ViewModel)
- **依赖注入**: Microsoft.Extensions.DependencyInjection
- **日志系统**: Microsoft.Extensions.Logging
- **配置管理**: Microsoft.Extensions.Configuration (JSON/YAML)
- **编译优化**: AOT Ready, Trimming支持

### 项目结构
LIVAnalyzer.sln
├── LIVAnalyzer.UI          // WPF用户界面层
├── LIVAnalyzer.Core        // 核心数据处理层 (.NET 9优化)
├── LIVAnalyzer.Data        // 数据访问层
├── LIVAnalyzer.Models      // 数据模型层 (记录类型支持)
├── LIVAnalyzer.Services    // 服务层 (依赖注入)
├── LIVAnalyzer.Tests       // 单元测试项目 (NUnit/xUnit)
└── LIVAnalyzer.Themes      // 主题资源

### v2.1.1 版本重点改进
- **数据导出完整性**: 修复Excel导出中缺少电压数据的问题
- **批量处理优化**: 确保批量处理与单文件处理结果一致
- **算法精度提升**: 改进电压和效率计算的准确性
- **发散角分析**: 完善能量占比计算功能

## 核心算法实现

### 1. 阈值电流计算算法
采用**一阶导数法**进行阈值电流计算（与Python版本保持一致）：

算法步骤：
1. 计算功率对电流的一阶导数
2. 使用移动平均平滑导数曲线
3. 寻找导数的最大值点
4. 在最大导数点附近寻找线性区域起始点

**优化特性**:
- 使用移动平均法降噪
- 自适应窗口大小处理不同数据质量
- 多点验证确保结果稳定性
- 避免缓存冲突，确保每个文件独立计算

### 2. 斜率效率计算
在线性区域进行**最小二乘法拟合**：

算法特点:
- R²值评估拟合质量
- 自动剔除异常数据点
- 支持加权最小二乘法处理

### 3. 串联电阻计算
基于高电流区域的**电压-电流特性**：

计算步骤：
1. 确定高电流区域（通常>80%最大电流）
2. 计算理想二极管电压
3. 实际电压与理想电压的差值对电流作线性拟合

### 4. 光谱参数分析

#### 峰值波长识别
- 数据平滑处理（移动平均或Gaussian滤波）
- 寻找全局最大值
- 亚像素精度峰值定位（抛物线拟合）

#### FWHM（半高全宽）计算
**改进的算法实现**：
1. 获取峰值强度并找到半高强度
2. 使用插值法精确定位左右半高点
3. 计算精确的FWHM值
4. 支持多峰情况下的主峰FWHM计算

### 5. 发散角分析

#### 计算方法
- **FWHM (50% 强度)**: 半高全宽发散角
- **FW(1/e²)**: 强度降到峰值1/e²（13.53%）处的宽度
  - 新增功能：同时计算并显示该宽度内的能量占比
- **FW95%**: 95%能量包含角度

#### 新增功能：1/e²能量占比计算
- 在计算1/e²宽度时，同时计算该宽度内包含的总能量百分比
- 该参数对激光束质量分析具有重要意义
- 结果显示在UI中，并导出到Excel报告

#### 算法实现
1. 数据归一化处理
2. 功率积分计算
3. 二分查找目标功率比例对应的角度范围
4. 线性插值提高精度
5. 支持UI实时计算和导出时重新计算
6. 确保UI显示值与导出值一致

## 数据处理优化

### 1. 数据平滑算法
提供多种平滑算法选择：

**移动平均法** (默认):
- 简单高效
- 适合一般噪声处理

**Savitzky-Golay滤波** (高精度):
- 保持数据局部特征
- 适用于噪声较大的光谱数据
- 可配置多项式阶数

### 2. 异步数据处理
采用Task-based异步模式：
- 支持进度报告
- 可取消操作
- 并发处理提高效率

## OxyPlot图表集成

### 图表模型架构
- 双Y轴显示（功率/电压）
- 动态图例配置
- 实时数据更新
- 交互式缩放和平移

### 高级图表设置
- **图表设置对话框**: 提供全面的自定义选项
- **实时预览**: 设置变化立即可见
- **配置持久化**: 用户设置自动保存
- **支持多种配色方案**: 包括高对比度模式

### 实时数据绑定
- MVVM数据绑定
- 自动刷新机制
- 高效渲染优化
- 支持暗黑主题自动适配

## 性能优化策略

### 1. 内存管理
- 大数据集分块处理
- 及时释放临时对象
- 使用ArrayPool减少GC压力
- 改进的缓存键生成算法（SHA256）

### 2. 并发处理
- 使用SemaphoreSlim控制并发数
- Task.WhenAll批量处理
- 避免线程阻塞
- ClearCache确保文件独立计算

### 3. UI响应性
- 使用ConfigureAwait(false)避免死锁
- 长操作显示进度条
- 支持取消操作（CancellationToken）
- 实时参数显示优化

## 数据格式兼容性

### Excel文件支持
- **EPPlus库**: 高性能Excel读写
- **多工作表**: 支持复杂数据结构
- **数据验证**: 自动检测格式错误
- **大文件优化**: 流式读取减少内存占用
- **发散角导出**: 自动添加HFF/VFF数据到导出文件

### COS格式转换
- 解析COS_Test_LIV_Data.txt
- 创建Excel工作簿
- 自动映射数据到对应工作表
- 批量转换支持

## 错误处理与日志

### 异常处理策略
- 分层异常处理
- 用户友好错误提示
- 详细日志记录

### 日志配置
- 使用Serilog日志框架
- 支持文件和控制台输出
- 日志级别可配置
- 自动日志轮转

## 配置管理

### YAML配置文件
支持以下配置项：
- 数据处理参数
- 显示设置
- 图表样式设置
- 性能限制
- 导出格式

### 图表设置持久化
- 线条样式、颜色方案等设置自动保存
- 支持恢复默认设置
- 配置文件热重载

### 配置访问
- 单例模式配置管理器
- 类型安全的配置访问
- 默认值支持

## 单元测试框架

### 测试架构
- xUnit测试框架
- 模拟对象(Mock)支持
- 数据驱动测试

### 测试覆盖
- 核心算法测试（阈值电流、FWHM计算）
- 数据验证测试
- 边界条件测试
- 缓存机制测试

## 性能基准测试

### 典型性能指标 (.NET 9, Intel i7-12700K)
- **文件加载**: 100MB Excel文件 < 1秒 (提升50%)
- **参数计算**: 10万数据点 < 300ms (提升40%)
- **图表渲染**: 5条曲线 < 60ms (提升40%)
- **批量处理**: 100个文件 < 20秒 (提升33%)
- **内存占用**: 典型工作集 < 150MB (减少25%)
- **发散角计算**: 实时响应 < 30ms (提升40%)
- **启动时间**: 冷启动 < 2秒 (AOT优化)

### Fluent Design性能优化
- **GPU加速**: 利用硬件加速渲染Fluent效果
- **动画优化**: 60FPS流畅动画，CPU占用 < 5%
- **主题切换**: 无缝切换，响应时间 < 100ms

---

技术文档 - LIV分析工具 v2.1.1 电压数据导出增强版本
开发者：00106 | 更新日期：2025年8月5日";
        }

        private static string GetDefaultReleaseNotes()
        {
            return @"# LIV分析工具发布说明 (v2.2.3 渐进式加载优化版本)

## 版本 2.2.3 (2025-08-08) - 渐进式加载重大更新

### 🚀 革命性的渐进式数据加载
- **数据点渐进式读取**: 实现真正的数据点级别渐进式加载技术
- **加载速度提升100倍**: 从15-20秒等待降低到0.1秒首次响应
- **所有数据类型支持**: LIV、光谱、发散角数据全部支持渐进式加载
- **Excel文件优化**: Excel文件所有工作表数据完整的渐进式加载支持

### ⚡ 用户体验革命性提升
- **立即响应**: 选择文件后0.1秒内看到图表
- **无进度条干扰**: 去掉所有进度显示，界面保持简洁
- **可立即分析**: 不需要等待完整加载就能开始数据分析
- **参数实时计算**: 数据加载过程中参数同步计算显示

### 👨‍💻 开发信息
- **开发者**: 00106
- **发布日期**: 2025年8月8日
- **版本类型**: 重大功能更新
- **兼容性**: Windows 10/11 (x64)

---

## 版本 2.1.1 (2025-08-05) - 数据导出增强更新

### 🔧 Excel导出功能修复
- **汇总表电压数据**: 修复汇总表中缺少I1电压和I2电压列的问题
- **数据完整性**: 确保导出数据包含完整的LIV参数信息
- **列位置调整**: 优化表头布局，电压数据紧跟对应的电流和功率数据

### 🚀 批量处理功能修复
- **I1/I2电压计算**: 修复批量处理中电压数据显示为0的问题
- **I1/I2效率计算**: 修复批量处理中效率数据显示为0的问题
- **发散角能量占比**: 修复批量处理中1/e²能量占比显示为0的问题
- **算法一致性**: 确保批量处理与单文件处理结果完全一致

### ⚡ 性能优化
- **并行计算**: 使用Task并行计算电压和功率，提升处理速度
- **算法改进**: 新增CalculateVoltageAtCurrentOptimized方法，提高电压计算精度
- **内存优化**: 优化数据处理流程，减少内存占用

### 🎯 用户体验提升
- **数据准确性**: 导出数据现在包含完整准确的电压和效率信息
- **分析完整性**: 发散角分析包含准确的能量占比数据
- **结果一致性**: 批量处理和单文件处理结果完全一致

### 👨‍💻 开发信息
- **开发者**: 00106
- **发布日期**: 2025年8月5日
- **版本类型**: 功能增强和问题修复
- **兼容性**: Windows 10/11 (x64)

## 版本 2.0.2 (2025-01-21)

### 🆕 新功能
- **发散角计算优化**
  - FW86.5%正式更名为FW(1/e²)，避免理解歧义
  - 明确FW(1/e²)为13.5%强度处的宽度，而非86.5%功率包含
  - 改进能量占比计算精度，限制最大值为100%

### 🔧 改进
- **数据处理增强**
  - 改进负强度值处理：直接设为0而非偏移
  - 优化边界值检测和警告提示
  - 增强数据预处理的稳健性

- **用户界面优化**  
  - 统一显示FW(1/e²)术语，提高专业性
  - 改进发散角结果显示格式
  - 优化导出Excel中的列名称

### 🐛 修复
- 修复发散角能量占比超过100%的问题
- 修复负强度值导致的计算异常
- 修复测试代码中的命名不一致问题

### 📚 文档更新
- 更新技术文档中的FW(1/e²)说明
- 澄清各发散角参数的物理含义
- 完善代码注释和算法说明

## 版本 2.0.1 (2025-01-21)

### 🆕 新功能
- **高级图表设置对话框**
  - 线条样式、标记、网格、颜色方案完全自定义
  - 实时预览功能，设置变化立即可见
  - 保存为默认功能，一次设置永久使用
  - 新增图例位置选项（左边、右边）
  - 网格线颜色自由选择

- **实时参数显示**
  - 选中单条曲线时显示该曲线参数
  - 选中多条曲线时显示最后选中的曲线参数
  - 方便快速对比不同文件的测试结果

- **发散角数据导出**
  - 自动将HFF/VFF发散角数据添加到导出Excel
  - 汇总表和详细表均包含发散角参数
  - UI计算值与导出值保持一致

### 🔧 改进
- **算法优化**
  - 阈值电流计算改为一阶导数法，与Python版本保持一致
  - FWHM计算使用插值法，提高精度
  - 修复缓存机制，确保每个文件独立计算
  - 改进的散列算法（SHA256）避免缓存冲突

- **界面优化**
  - 移除主面板线条样式选项（已集成到图表设置中）
  - 优化按钮样式，统一使用白色风格
  - 增强网格显示对比度
  - 改进暗黑主题支持

- **性能提升**
  - 优化批量处理速度
  - 减少内存占用
  - 提高图表渲染效率

### 🐛 修复
- 修复阈值电流计算算法不正确的问题
- 修复FWHM计算所有文件返回相同值的问题
- 修复网格显示勾选框失效问题
- 修复多文件处理时参数计算错误
- 修复发散角UI显示与导出数值不一致问题
- 修复主题切换时图表颜色不更新问题

### 🔄 重构
- 重构图表设置模块，消除代码重复
- 使用工厂方法模式优化代码结构
- 消除反射使用，提供公共API
- 实现IDisposable模式管理资源

## 版本 2.0.0 (2025-01-20)

### 新功能
- 全新C# WPF架构，基于.NET 6开发
- ModernWpfUI现代化界面设计
- 支持发散角数据分析（HFF/VFF）
- 新增COS文件批量转换工具
- 支持多文件同时加载和对比分析
- 增强的数据平滑算法
- 详细的内置使用指南和技术文档
- 完整的配置管理系统

### 改进
- 大幅提升数据处理性能（相比Python版本提升5-10倍）
- 优化内存使用，支持更大数据文件
- 改进图表交互体验
- 更准确的串联电阻计算算法
- 更友好的错误提示
- 优化界面布局和控件间距
- 改进用户体验和操作流程

### 修复
- 修复大文件加载时的内存溢出问题
- 修复某些Excel格式兼容性问题
- 修复图表缩放时的显示异常

## 版本 1.0.0 (2023-12-01) - Python版本

### 初始版本功能
- LIV曲线分析
- 光谱分析
- 效率曲线计算
- 基本参数提取
- Excel/CSV数据导入
- 图表导出功能

---

**LIV分析工具 v2.2.3 渐进式加载优化版本** - 革命性的加载速度提升，更优秀的用户体验
开发者：00106 | 持续改进，追求卓越";
        }

        private static string GetDefaultAboutInfo()
        {
            return @"# 关于 LIV分析工具

## 软件信息

**名称**: LIV分析工具 (LIV Analyzer)
**版本**: 2.2.3 (渐进式加载优化版本)
**发布日期**: 2025年8月8日
**开发者**: 00106
**版权所有**: © 2023-2025

## 软件简介

LIV分析工具是一款专业的激光器测试数据分析软件，基于.NET 9框架开发。软件用于分析激光二极管的光-电流-电压（LIV）特性曲线，提供了完整的数据处理、可视化和参数提取功能，帮助科研人员和工程师高效地分析激光器性能。v2.2.3版本引入了革命性的渐进式数据加载技术，实现了所有数据类型的即时响应，显著提升了数据加载速度和用户体验。

## 主要功能

- **LIV曲线分析**: 阈值电流（一阶导数法）、斜率效率、串联电阻等参数自动计算
- **光谱分析**: 峰值波长、FWHM（插值法）等光谱参数提取
- **效率分析**: 电光转换效率实时计算和显示
- **发散角分析**: 支持HFF/VFF发散角数据处理和导出
- **批量处理**: 支持多文件批量分析和报告生成
- **高级图表设置**: 完全自定义的图表样式，支持实时预览
- **数据导出**: 分析结果导出为Excel格式，包含完整参数

## 技术特点

- 基于.NET 9 + WPF的现代化架构
- 现代化用户界面，支持流畅动画和主题切换
- OxyPlot专业级科学绘图引擎，高性能渲染
- 高性能异步数据处理，支持AOT编译
- 完善的错误处理和日志系统
- 智能主题系统，支持浅色/深色/跟随系统
- 响应式设计，完美适配高DPI显示器
- 增强的数据导出功能，确保数据完整性

## 版本更新

### V2.1.1 更新亮点 (2025年8月5日)
- 修复Excel导出中缺少I1和I2电压数据的问题
- 修复批量处理中电压、效率和发散角能量占比显示为0的问题
- 确保批量处理与单文件处理结果完全一致
- 提升数据导出的完整性和准确性
- 优化电压和效率计算算法
- 改进发散角能量占比计算功能
- 开发者：00106

## 致谢

感谢所有使用本软件的用户，您的反馈是我们持续改进的动力。

特别感谢以下开源项目：
- .NET Foundation (.NET 9)
- Microsoft Fluent Design System
- OxyPlot
- EPPlus
- Microsoft.Extensions.*
- MathNet.Numerics

## 联系方式

如有问题或建议，请联系开发团队。

---

**LIV分析工具 v2.1.1 电压数据导出增强版本** - 更完整的数据导出，更准确的分析结果
让激光器测试分析更简单、更高效！

开发者：00106 | 更新日期：2025年8月5日";
        }

        #endregion
    }
}