import{g as b4}from"./react-DXNcgoS8.js";function yt(t,n){return t==null||n==null?NaN:t<n?-1:t>n?1:t>=n?0:NaN}function dr(t,n){return t==null||n==null?NaN:n<t?-1:n>t?1:n>=t?0:NaN}function ta(t){let n,e,r;t.length!==2?(n=yt,e=(a,c)=>yt(t(a),c),r=(a,c)=>t(a)-c):(n=t===yt||t===dr?t:w4,e=t,r=t);function i(a,c,f=0,s=a.length){if(f<s){if(n(c,c)!==0)return s;do{const h=f+s>>>1;e(a[h],c)<0?f=h+1:s=h}while(f<s)}return f}function o(a,c,f=0,s=a.length){if(f<s){if(n(c,c)!==0)return s;do{const h=f+s>>>1;e(a[h],c)<=0?f=h+1:s=h}while(f<s)}return f}function u(a,c,f=0,s=a.length){const h=i(a,c,f,s-1);return h>f&&r(a[h-1],c)>-r(a[h],c)?h-1:h}return{left:i,center:u,right:o}}function w4(){return 0}function Rc(t){return t===null?NaN:+t}function*v4(t,n){if(n===void 0)for(let e of t)e!=null&&(e=+e)>=e&&(yield e);else{let e=-1;for(let r of t)(r=n(r,++e,t))!=null&&(r=+r)>=r&&(yield r)}}const mb=ta(yt),He=mb.right,x4=mb.left,_4=ta(Rc).center;function M4(t,n){if(!((n=+n)>=0))throw new RangeError("invalid r");let e=t.length;if(!((e=Math.floor(e))>=0))throw new RangeError("invalid length");if(!e||!n)return t;const r=Td(n),i=t.slice();return r(t,i,0,e,1),r(i,t,0,e,1),r(t,i,0,e,1),t}const Sd=bb(Td),yb=bb($4);function bb(t){return function(n,e,r=e){if(!((e=+e)>=0))throw new RangeError("invalid rx");if(!((r=+r)>=0))throw new RangeError("invalid ry");let{data:i,width:o,height:u}=n;if(!((o=Math.floor(o))>=0))throw new RangeError("invalid width");if(!((u=Math.floor(u!==void 0?u:i.length/o))>=0))throw new RangeError("invalid height");if(!o||!u||!e&&!r)return n;const a=e&&t(e),c=r&&t(r),f=i.slice();return a&&c?($i(a,f,i,o,u),$i(a,i,f,o,u),$i(a,f,i,o,u),Ai(c,i,f,o,u),Ai(c,f,i,o,u),Ai(c,i,f,o,u)):a?($i(a,i,f,o,u),$i(a,f,i,o,u),$i(a,i,f,o,u)):c&&(Ai(c,i,f,o,u),Ai(c,f,i,o,u),Ai(c,i,f,o,u)),n}}function $i(t,n,e,r,i){for(let o=0,u=r*i;o<u;)t(n,e,o,o+=r,1)}function Ai(t,n,e,r,i){for(let o=0,u=r*i;o<r;++o)t(n,e,o,o+u,r)}function $4(t){const n=Td(t);return(e,r,i,o,u)=>{i<<=2,o<<=2,u<<=2,n(e,r,i+0,o+0,u),n(e,r,i+1,o+1,u),n(e,r,i+2,o+2,u),n(e,r,i+3,o+3,u)}}function Td(t){const n=Math.floor(t);if(n===t)return A4(t);const e=t-n,r=2*t+1;return(i,o,u,a,c)=>{if(!((a-=c)>=u))return;let f=n*o[u];const s=c*n,h=s+c;for(let l=u,d=u+s;l<d;l+=c)f+=o[Math.min(a,l)];for(let l=u,d=a;l<=d;l+=c)f+=o[Math.min(a,l+s)],i[l]=(f+e*(o[Math.max(u,l-h)]+o[Math.min(a,l+h)]))/r,f-=o[Math.max(u,l-s)]}}function A4(t){const n=2*t+1;return(e,r,i,o,u)=>{if(!((o-=u)>=i))return;let a=t*r[i];const c=u*t;for(let f=i,s=i+c;f<s;f+=u)a+=r[Math.min(o,f)];for(let f=i,s=o;f<=s;f+=u)a+=r[Math.min(o,f+c)],e[f]=a/n,a-=r[Math.max(i,f-c)]}}function na(t,n){let e=0;if(n===void 0)for(let r of t)r!=null&&(r=+r)>=r&&++e;else{let r=-1;for(let i of t)(i=n(i,++r,t))!=null&&(i=+i)>=i&&++e}return e}function S4(t){return t.length|0}function T4(t){return!(t>0)}function E4(t){return typeof t!="object"||"length"in t?t:Array.from(t)}function k4(t){return n=>t(...n)}function wb(...t){const n=typeof t[t.length-1]=="function"&&k4(t.pop());t=t.map(E4);const e=t.map(S4),r=t.length-1,i=new Array(r+1).fill(0),o=[];if(r<0||e.some(T4))return o;for(;;){o.push(i.map((a,c)=>t[c][a]));let u=r;for(;++i[u]===e[u];){if(u===0)return n?o.map(n):o;i[u--]=0}}}function vb(t,n){var e=0,r=0;return Float64Array.from(t,n===void 0?i=>e+=+i||0:i=>e+=+n(i,r++,t)||0)}function qf(t,n){let e=0,r,i=0,o=0;if(n===void 0)for(let u of t)u!=null&&(u=+u)>=u&&(r=u-i,i+=r/++e,o+=r*(u-i));else{let u=-1;for(let a of t)(a=n(a,++u,t))!=null&&(a=+a)>=a&&(r=a-i,i+=r/++e,o+=r*(a-i))}if(e>1)return o/(e-1)}function $o(t,n){const e=qf(t,n);return e&&Math.sqrt(e)}function Tt(t,n){let e,r;if(n===void 0)for(const i of t)i!=null&&(e===void 0?i>=i&&(e=r=i):(e>i&&(e=i),r<i&&(r=i)));else{let i=-1;for(let o of t)(o=n(o,++i,t))!=null&&(e===void 0?o>=o&&(e=r=o):(e>o&&(e=o),r<o&&(r=o)))}return[e,r]}class Bt{constructor(){this._partials=new Float64Array(32),this._n=0}add(n){const e=this._partials;let r=0;for(let i=0;i<this._n&&i<32;i++){const o=e[i],u=n+o,a=Math.abs(n)<Math.abs(o)?n-(u-o):o-(u-n);a&&(e[r++]=a),n=u}return e[r]=n,this._n=r+1,this}valueOf(){const n=this._partials;let e=this._n,r,i,o,u=0;if(e>0){for(u=n[--e];e>0&&(r=u,i=n[--e],u=r+i,o=i-(u-r),!o););e>0&&(o<0&&n[e-1]<0||o>0&&n[e-1]>0)&&(i=o*2,r=u+i,i==r-u&&(u=r))}return u}}function N4(t,n){const e=new Bt;if(n===void 0)for(let r of t)(r=+r)&&e.add(r);else{let r=-1;for(let i of t)(i=+n(i,++r,t))&&e.add(i)}return+e}function C4(t,n){const e=new Bt;let r=-1;return Float64Array.from(t,n===void 0?i=>e.add(+i||0):i=>e.add(+n(i,++r,t)||0))}class gr extends Map{constructor(n,e=Mb){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:e}}),n!=null)for(const[r,i]of n)this.set(r,i)}get(n){return super.get(sh(this,n))}has(n){return super.has(sh(this,n))}set(n,e){return super.set(xb(this,n),e)}delete(n){return super.delete(_b(this,n))}}class Bn extends Set{constructor(n,e=Mb){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:e}}),n!=null)for(const r of n)this.add(r)}has(n){return super.has(sh(this,n))}add(n){return super.add(xb(this,n))}delete(n){return super.delete(_b(this,n))}}function sh({_intern:t,_key:n},e){const r=n(e);return t.has(r)?t.get(r):e}function xb({_intern:t,_key:n},e){const r=n(e);return t.has(r)?t.get(r):(t.set(r,e),e)}function _b({_intern:t,_key:n},e){const r=n(e);return t.has(r)&&(e=t.get(r),t.delete(r)),e}function Mb(t){return t!==null&&typeof t=="object"?t.valueOf():t}function Qi(t){return t}function je(t,...n){return Ao(t,Qi,Qi,n)}function $b(t,...n){return Ao(t,Array.from,Qi,n)}function Ab(t,n){for(let e=1,r=n.length;e<r;++e)t=t.flatMap(i=>i.pop().map(([o,u])=>[...i,o,u]));return t}function R4(t,...n){return Ab($b(t,...n),n)}function I4(t,n,...e){return Ab(Ed(t,n,...e),e)}function jr(t,n,...e){return Ao(t,Qi,n,e)}function Ed(t,n,...e){return Ao(t,Array.from,n,e)}function z4(t,...n){return Ao(t,Qi,Sb,n)}function L4(t,...n){return Ao(t,Array.from,Sb,n)}function Sb(t){if(t.length!==1)throw new Error("duplicate key");return t[0]}function Ao(t,n,e,r){return function i(o,u){if(u>=r.length)return e(o);const a=new gr,c=r[u++];let f=-1;for(const s of o){const h=c(s,++f,o),l=a.get(h);l?l.push(s):a.set(h,[s])}for(const[s,h]of a)a.set(s,i(h,u));return n(a)}(t,0)}function Tb(t,n){return Array.from(n,e=>t[e])}function $u(t,...n){if(typeof t[Symbol.iterator]!="function")throw new TypeError("values is not iterable");t=Array.from(t);let[e]=n;if(e&&e.length!==2||n.length>1){const r=Uint32Array.from(t,(i,o)=>o);return n.length>1?(n=n.map(i=>t.map(i)),r.sort((i,o)=>{for(const u of n){const a=Ji(u[i],u[o]);if(a)return a}})):(e=t.map(e),r.sort((i,o)=>Ji(e[i],e[o]))),Tb(t,r)}return t.sort(kd(e))}function kd(t=yt){if(t===yt)return Ji;if(typeof t!="function")throw new TypeError("compare is not a function");return(n,e)=>{const r=t(n,e);return r||r===0?r:(t(e,e)===0)-(t(n,n)===0)}}function Ji(t,n){return(t==null||!(t>=t))-(n==null||!(n>=n))||(t<n?-1:t>n?1:0)}function Yf(t,n,e){return(n.length!==2?$u(jr(t,n,e),([r,i],[o,u])=>yt(i,u)||yt(r,o)):$u(je(t,e),([r,i],[o,u])=>n(i,u)||yt(r,o))).map(([r])=>r)}var P4=Array.prototype,D4=P4.slice;function ol(t){return()=>t}const O4=Math.sqrt(50),F4=Math.sqrt(10),B4=Math.sqrt(2);function Ic(t,n,e){const r=(n-t)/Math.max(0,e),i=Math.floor(Math.log10(r)),o=r/Math.pow(10,i),u=o>=O4?10:o>=F4?5:o>=B4?2:1;let a,c,f;return i<0?(f=Math.pow(10,-i)/u,a=Math.round(t*f),c=Math.round(n*f),a/f<t&&++a,c/f>n&&--c,f=-f):(f=Math.pow(10,i)*u,a=Math.round(t/f),c=Math.round(n/f),a*f<t&&++a,c*f>n&&--c),c<a&&.5<=e&&e<2?Ic(t,n,e*2):[a,c,f]}function ge(t,n,e){if(n=+n,t=+t,e=+e,!(e>0))return[];if(t===n)return[t];const r=n<t,[i,o,u]=r?Ic(n,t,e):Ic(t,n,e);if(!(o>=i))return[];const a=o-i+1,c=new Array(a);if(r)if(u<0)for(let f=0;f<a;++f)c[f]=(o-f)/-u;else for(let f=0;f<a;++f)c[f]=(o-f)*u;else if(u<0)for(let f=0;f<a;++f)c[f]=(i+f)/-u;else for(let f=0;f<a;++f)c[f]=(i+f)*u;return c}function pr(t,n,e){return n=+n,t=+t,e=+e,Ic(t,n,e)[2]}function zc(t,n,e){n=+n,t=+t,e=+e;const r=n<t,i=r?pr(n,t,e):pr(t,n,e);return(r?-1:1)*(i<0?1/-i:i)}function Uf(t,n,e){let r;for(;;){const i=pr(t,n,e);if(i===r||i===0||!isFinite(i))return[t,n];i>0?(t=Math.floor(t/i)*i,n=Math.ceil(n/i)*i):i<0&&(t=Math.ceil(t*i)/i,n=Math.floor(n*i)/i),r=i}}function ea(t){return Math.max(1,Math.ceil(Math.log(na(t))/Math.LN2)+1)}function Wp(){var t=Qi,n=Tt,e=ea;function r(i){Array.isArray(i)||(i=Array.from(i));var o,u=i.length,a,c,f=new Array(u);for(o=0;o<u;++o)f[o]=t(i[o],o,i);var s=n(f),h=s[0],l=s[1],d=e(f,h,l);if(!Array.isArray(d)){const v=l,g=+d;if(n===Tt&&([h,l]=Uf(h,l,g)),d=ge(h,l,g),d[0]<=h&&(c=pr(h,l,g)),d[d.length-1]>=l)if(v>=l&&n===Tt){const x=pr(h,l,g);isFinite(x)&&(x>0?l=(Math.floor(l/x)+1)*x:x<0&&(l=(Math.ceil(l*-x)+1)/-x))}else d.pop()}for(var p=d.length,m=0,y=p;d[m]<=h;)++m;for(;d[y-1]>l;)--y;(m||y<p)&&(d=d.slice(m,y),p=y-m);var b=new Array(p+1),w;for(o=0;o<=p;++o)w=b[o]=[],w.x0=o>0?d[o-1]:h,w.x1=o<p?d[o]:l;if(isFinite(c)){if(c>0)for(o=0;o<u;++o)(a=f[o])!=null&&h<=a&&a<=l&&b[Math.min(p,Math.floor((a-h)/c))].push(i[o]);else if(c<0){for(o=0;o<u;++o)if((a=f[o])!=null&&h<=a&&a<=l){const v=Math.floor((h-a)*c);b[Math.min(p,v+(d[v]<=a))].push(i[o])}}}else for(o=0;o<u;++o)(a=f[o])!=null&&h<=a&&a<=l&&b[He(d,a,0,p)].push(i[o]);return b}return r.value=function(i){return arguments.length?(t=typeof i=="function"?i:ol(i),r):t},r.domain=function(i){return arguments.length?(n=typeof i=="function"?i:ol([i[0],i[1]]),r):n},r.thresholds=function(i){return arguments.length?(e=typeof i=="function"?i:ol(Array.isArray(i)?D4.call(i):i),r):e},r}function qt(t,n){let e;if(n===void 0)for(const r of t)r!=null&&(e<r||e===void 0&&r>=r)&&(e=r);else{let r=-1;for(let i of t)(i=n(i,++r,t))!=null&&(e<i||e===void 0&&i>=i)&&(e=i)}return e}function Xf(t,n){let e,r=-1,i=-1;if(n===void 0)for(const o of t)++i,o!=null&&(e<o||e===void 0&&o>=o)&&(e=o,r=i);else for(let o of t)(o=n(o,++i,t))!=null&&(e<o||e===void 0&&o>=o)&&(e=o,r=i);return r}function on(t,n){let e;if(n===void 0)for(const r of t)r!=null&&(e>r||e===void 0&&r>=r)&&(e=r);else{let r=-1;for(let i of t)(i=n(i,++r,t))!=null&&(e>i||e===void 0&&i>=i)&&(e=i)}return e}function Wf(t,n){let e,r=-1,i=-1;if(n===void 0)for(const o of t)++i,o!=null&&(e>o||e===void 0&&o>=o)&&(e=o,r=i);else for(let o of t)(o=n(o,++i,t))!=null&&(e>o||e===void 0&&o>=o)&&(e=o,r=i);return r}function Hf(t,n,e=0,r=1/0,i){if(n=Math.floor(n),e=Math.floor(Math.max(0,e)),r=Math.floor(Math.min(t.length-1,r)),!(e<=n&&n<=r))return t;for(i=i===void 0?Ji:kd(i);r>e;){if(r-e>600){const c=r-e+1,f=n-e+1,s=Math.log(c),h=.5*Math.exp(2*s/3),l=.5*Math.sqrt(s*h*(c-h)/c)*(f-c/2<0?-1:1),d=Math.max(e,Math.floor(n-f*h/c+l)),p=Math.min(r,Math.floor(n+(c-f)*h/c+l));Hf(t,n,d,p,i)}const o=t[n];let u=e,a=r;for(Wo(t,e,n),i(t[r],o)>0&&Wo(t,e,r);u<a;){for(Wo(t,u,a),++u,--a;i(t[u],o)<0;)++u;for(;i(t[a],o)>0;)--a}i(t[e],o)===0?Wo(t,e,a):(++a,Wo(t,a,r)),a<=n&&(e=a+1),n<=a&&(r=a-1)}return t}function Wo(t,n,e){const r=t[n];t[n]=t[e],t[e]=r}function ra(t,n=yt){let e,r=!1;if(n.length===1){let i;for(const o of t){const u=n(o);(r?yt(u,i)>0:yt(u,u)===0)&&(e=o,i=u,r=!0)}}else for(const i of t)(r?n(i,e)>0:n(i,i)===0)&&(e=i,r=!0);return e}function pe(t,n,e){if(t=Float64Array.from(v4(t,e)),!(!(r=t.length)||isNaN(n=+n))){if(n<=0||r<2)return on(t);if(n>=1)return qt(t);var r,i=(r-1)*n,o=Math.floor(i),u=qt(Hf(t,o).subarray(0,o+1)),a=on(t.subarray(o+1));return u+(a-u)*(i-o)}}function Eb(t,n,e=Rc){if(!(!(r=t.length)||isNaN(n=+n))){if(n<=0||r<2)return+e(t[0],0,t);if(n>=1)return+e(t[r-1],r-1,t);var r,i=(r-1)*n,o=Math.floor(i),u=+e(t[o],o,t),a=+e(t[o+1],o+1,t);return u+(a-u)*(i-o)}}function kb(t,n,e=Rc){if(!isNaN(n=+n)){if(r=Float64Array.from(t,(a,c)=>Rc(e(t[c],c,t))),n<=0)return Wf(r);if(n>=1)return Xf(r);var r,i=Uint32Array.from(t,(a,c)=>c),o=r.length-1,u=Math.floor(o*n);return Hf(i,u,0,o,(a,c)=>Ji(r[a],r[c])),u=ra(i.subarray(0,u+1),a=>r[a]),u>=0?u:-1}}function Nb(t,n,e){const r=na(t),i=pe(t,.75)-pe(t,.25);return r&&i?Math.ceil((e-n)/(2*i*Math.pow(r,-1/3))):1}function Nd(t,n,e){const r=na(t),i=$o(t);return r&&i?Math.ceil((e-n)*Math.cbrt(r)/(3.49*i)):1}function ia(t,n){let e=0,r=0;if(n===void 0)for(let i of t)i!=null&&(i=+i)>=i&&(++e,r+=i);else{let i=-1;for(let o of t)(o=n(o,++i,t))!=null&&(o=+o)>=o&&(++e,r+=o)}if(e)return r/e}function to(t,n){return pe(t,.5,n)}function q4(t,n){return kb(t,.5,n)}function*Y4(t){for(const n of t)yield*n}function Cd(t){return Array.from(Y4(t))}function Rd(t,n){const e=new gr;if(n===void 0)for(let o of t)o!=null&&o>=o&&e.set(o,(e.get(o)||0)+1);else{let o=-1;for(let u of t)(u=n(u,++o,t))!=null&&u>=u&&e.set(u,(e.get(u)||0)+1)}let r,i=0;for(const[o,u]of e)u>i&&(i=u,r=o);return r}function Cb(t,n=U4){const e=[];let r,i=!1;for(const o of t)i&&e.push(n(r,o)),r=o,i=!0;return e}function U4(t,n){return[t,n]}function kn(t,n,e){t=+t,n=+n,e=(i=arguments.length)<2?(n=t,t=0,1):i<3?1:+e;for(var r=-1,i=Math.max(0,Math.ceil((n-t)/e))|0,o=new Array(i);++r<i;)o[r]=t+r*e;return o}function Id(t,n=yt){if(typeof t[Symbol.iterator]!="function")throw new TypeError("values is not iterable");let e=Array.from(t);const r=new Float64Array(e.length);n.length!==2&&(e=e.map(n),n=yt);const i=(a,c)=>n(e[a],e[c]);let o,u;return t=Uint32Array.from(e,(a,c)=>c),t.sort(n===yt?(a,c)=>Ji(e[a],e[c]):kd(i)),t.forEach((a,c)=>{const f=i(a,o===void 0?a:o);f>=0?((o===void 0||f>0)&&(o=a,u=c),r[a]=u):r[a]=NaN}),r}function Rb(t,n=yt){let e,r=!1;if(n.length===1){let i;for(const o of t){const u=n(o);(r?yt(u,i)<0:yt(u,u)===0)&&(e=o,i=u,r=!0)}}else for(const i of t)(r?n(i,e)<0:n(i,i)===0)&&(e=i,r=!0);return e}function Ib(t,n=yt){if(n.length===1)return Wf(t,n);let e,r=-1,i=-1;for(const o of t)++i,(r<0?n(o,o)===0:n(o,e)<0)&&(e=o,r=i);return r}function X4(t,n=yt){if(n.length===1)return Xf(t,n);let e,r=-1,i=-1;for(const o of t)++i,(r<0?n(o,o)===0:n(o,e)>0)&&(e=o,r=i);return r}function W4(t,n){const e=Ib(t,n);return e<0?void 0:e}const H4=zb(Math.random);function zb(t){return function(e,r=0,i=e.length){let o=i-(r=+r);for(;o;){const u=t()*o--|0,a=e[o+r];e[o+r]=e[u+r],e[u+r]=a}return e}}function Kn(t,n){let e=0;if(n===void 0)for(let r of t)(r=+r)&&(e+=r);else{let r=-1;for(let i of t)(i=+n(i,++r,t))&&(e+=i)}return e}function Lb(t){if(!(o=t.length))return[];for(var n=-1,e=on(t,G4),r=new Array(e);++n<e;)for(var i=-1,o,u=r[n]=new Array(o);++i<o;)u[i]=t[i][n];return r}function G4(t){return t.length}function V4(){return Lb(arguments)}function j4(t,n){if(typeof n!="function")throw new TypeError("test is not a function");let e=-1;for(const r of t)if(!n(r,++e,t))return!1;return!0}function Z4(t,n){if(typeof n!="function")throw new TypeError("test is not a function");let e=-1;for(const r of t)if(n(r,++e,t))return!0;return!1}function K4(t,n){if(typeof n!="function")throw new TypeError("test is not a function");const e=[];let r=-1;for(const i of t)n(i,++r,t)&&e.push(i);return e}function Q4(t,n){if(typeof t[Symbol.iterator]!="function")throw new TypeError("values is not iterable");if(typeof n!="function")throw new TypeError("mapper is not a function");return Array.from(t,(e,r)=>n(e,r,t))}function J4(t,n,e){if(typeof n!="function")throw new TypeError("reducer is not a function");const r=t[Symbol.iterator]();let i,o,u=-1;if(arguments.length<3){if({done:i,value:e}=r.next(),i)return;++u}for(;{done:i,value:o}=r.next(),!i;)e=n(e,o,++u,t);return e}function Au(t){if(typeof t[Symbol.iterator]!="function")throw new TypeError("values is not iterable");return Array.from(t).reverse()}function t8(t,...n){t=new Bn(t);for(const e of n)for(const r of e)t.delete(r);return t}function n8(t,n){const e=n[Symbol.iterator](),r=new Bn;for(const i of t){if(r.has(i))return!1;let o,u;for(;({value:o,done:u}=e.next())&&!u;){if(Object.is(i,o))return!1;r.add(o)}}return!0}function e8(t,...n){t=new Bn(t),n=n.map(r8);t:for(const e of t)for(const r of n)if(!r.has(e)){t.delete(e);continue t}return t}function r8(t){return t instanceof Bn?t:new Bn(t)}function Pb(t,n){const e=t[Symbol.iterator](),r=new Set;for(const i of n){const o=Hp(i);if(r.has(o))continue;let u,a;for(;{value:u,done:a}=e.next();){if(a)return!1;const c=Hp(u);if(r.add(c),Object.is(o,c))break}}return!0}function Hp(t){return t!==null&&typeof t=="object"?t.valueOf():t}function i8(t,n){return Pb(n,t)}function o8(...t){const n=new Bn;for(const e of t)for(const r of e)n.add(r);return n}function u8(t){return t}var wc=1,vc=2,lh=3,ru=4,Gp=1e-6;function a8(t){return"translate("+t+",0)"}function c8(t){return"translate(0,"+t+")"}function f8(t){return n=>+t(n)}function s8(t,n){return n=Math.max(0,t.bandwidth()-n*2)/2,t.round()&&(n=Math.round(n)),e=>+t(e)+n}function l8(){return!this.__axis}function Gf(t,n){var e=[],r=null,i=null,o=6,u=6,a=3,c=typeof window<"u"&&window.devicePixelRatio>1?0:.5,f=t===wc||t===ru?-1:1,s=t===ru||t===vc?"x":"y",h=t===wc||t===lh?a8:c8;function l(d){var p=r??(n.ticks?n.ticks.apply(n,e):n.domain()),m=i??(n.tickFormat?n.tickFormat.apply(n,e):u8),y=Math.max(o,0)+a,b=n.range(),w=+b[0]+c,v=+b[b.length-1]+c,g=(n.bandwidth?s8:f8)(n.copy(),c),x=d.selection?d.selection():d,_=x.selectAll(".domain").data([null]),M=x.selectAll(".tick").data(p,n).order(),A=M.exit(),T=M.enter().append("g").attr("class","tick"),E=M.select("line"),S=M.select("text");_=_.merge(_.enter().insert("path",".tick").attr("class","domain").attr("stroke","currentColor")),M=M.merge(T),E=E.merge(T.append("line").attr("stroke","currentColor").attr(s+"2",f*o)),S=S.merge(T.append("text").attr("fill","currentColor").attr(s,f*y).attr("dy",t===wc?"0em":t===lh?"0.71em":"0.32em")),d!==x&&(_=_.transition(d),M=M.transition(d),E=E.transition(d),S=S.transition(d),A=A.transition(d).attr("opacity",Gp).attr("transform",function(R){return isFinite(R=g(R))?h(R+c):this.getAttribute("transform")}),T.attr("opacity",Gp).attr("transform",function(R){var C=this.parentNode.__axis;return h((C&&isFinite(C=C(R))?C:g(R))+c)})),A.remove(),_.attr("d",t===ru||t===vc?u?"M"+f*u+","+w+"H"+c+"V"+v+"H"+f*u:"M"+c+","+w+"V"+v:u?"M"+w+","+f*u+"V"+c+"H"+v+"V"+f*u:"M"+w+","+c+"H"+v),M.attr("opacity",1).attr("transform",function(R){return h(g(R)+c)}),E.attr(s+"2",f*o),S.attr(s,f*y).text(m),x.filter(l8).attr("fill","none").attr("font-size",10).attr("font-family","sans-serif").attr("text-anchor",t===vc?"start":t===ru?"end":"middle"),x.each(function(){this.__axis=g})}return l.scale=function(d){return arguments.length?(n=d,l):n},l.ticks=function(){return e=Array.from(arguments),l},l.tickArguments=function(d){return arguments.length?(e=d==null?[]:Array.from(d),l):e.slice()},l.tickValues=function(d){return arguments.length?(r=d==null?null:Array.from(d),l):r&&r.slice()},l.tickFormat=function(d){return arguments.length?(i=d,l):i},l.tickSize=function(d){return arguments.length?(o=u=+d,l):o},l.tickSizeInner=function(d){return arguments.length?(o=+d,l):o},l.tickSizeOuter=function(d){return arguments.length?(u=+d,l):u},l.tickPadding=function(d){return arguments.length?(a=+d,l):a},l.offset=function(d){return arguments.length?(c=+d,l):c},l}function h8(t){return Gf(wc,t)}function d8(t){return Gf(vc,t)}function Db(t){return Gf(lh,t)}function g8(t){return Gf(ru,t)}var p8={value:()=>{}};function li(){for(var t=0,n=arguments.length,e={},r;t<n;++t){if(!(r=arguments[t]+"")||r in e||/[\s.]/.test(r))throw new Error("illegal type: "+r);e[r]=[]}return new xc(e)}function xc(t){this._=t}function m8(t,n){return t.trim().split(/^|\s+/).map(function(e){var r="",i=e.indexOf(".");if(i>=0&&(r=e.slice(i+1),e=e.slice(0,i)),e&&!n.hasOwnProperty(e))throw new Error("unknown type: "+e);return{type:e,name:r}})}xc.prototype=li.prototype={constructor:xc,on:function(t,n){var e=this._,r=m8(t+"",e),i,o=-1,u=r.length;if(arguments.length<2){for(;++o<u;)if((i=(t=r[o]).type)&&(i=y8(e[i],t.name)))return i;return}if(n!=null&&typeof n!="function")throw new Error("invalid callback: "+n);for(;++o<u;)if(i=(t=r[o]).type)e[i]=Vp(e[i],t.name,n);else if(n==null)for(i in e)e[i]=Vp(e[i],t.name,null);return this},copy:function(){var t={},n=this._;for(var e in n)t[e]=n[e].slice();return new xc(t)},call:function(t,n){if((i=arguments.length-2)>0)for(var e=new Array(i),r=0,i,o;r<i;++r)e[r]=arguments[r+2];if(!this._.hasOwnProperty(t))throw new Error("unknown type: "+t);for(o=this._[t],r=0,i=o.length;r<i;++r)o[r].value.apply(n,e)},apply:function(t,n,e){if(!this._.hasOwnProperty(t))throw new Error("unknown type: "+t);for(var r=this._[t],i=0,o=r.length;i<o;++i)r[i].value.apply(n,e)}};function y8(t,n){for(var e=0,r=t.length,i;e<r;++e)if((i=t[e]).name===n)return i.value}function Vp(t,n,e){for(var r=0,i=t.length;r<i;++r)if(t[r].name===n){t[r]=p8,t=t.slice(0,r).concat(t.slice(r+1));break}return e!=null&&t.push({name:n,value:e}),t}var hh="http://www.w3.org/1999/xhtml";const me={svg:"http://www.w3.org/2000/svg",xhtml:hh,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"};function oa(t){var n=t+="",e=n.indexOf(":");return e>=0&&(n=t.slice(0,e))!=="xmlns"&&(t=t.slice(e+1)),me.hasOwnProperty(n)?{space:me[n],local:t}:t}function b8(t){return function(){var n=this.ownerDocument,e=this.namespaceURI;return e===hh&&n.documentElement.namespaceURI===hh?n.createElement(t):n.createElementNS(e,t)}}function w8(t){return function(){return this.ownerDocument.createElementNS(t.space,t.local)}}function So(t){var n=oa(t);return(n.local?w8:b8)(n)}function v8(){}function Vf(t){return t==null?v8:function(){return this.querySelector(t)}}function x8(t){typeof t!="function"&&(t=Vf(t));for(var n=this._groups,e=n.length,r=new Array(e),i=0;i<e;++i)for(var o=n[i],u=o.length,a=r[i]=new Array(u),c,f,s=0;s<u;++s)(c=o[s])&&(f=t.call(c,c.__data__,s,o))&&("__data__"in c&&(f.__data__=c.__data__),a[s]=f);return new dn(r,this._parents)}function Ob(t){return t==null?[]:Array.isArray(t)?t:Array.from(t)}function _8(){return[]}function zd(t){return t==null?_8:function(){return this.querySelectorAll(t)}}function M8(t){return function(){return Ob(t.apply(this,arguments))}}function $8(t){typeof t=="function"?t=M8(t):t=zd(t);for(var n=this._groups,e=n.length,r=[],i=[],o=0;o<e;++o)for(var u=n[o],a=u.length,c,f=0;f<a;++f)(c=u[f])&&(r.push(t.call(c,c.__data__,f,u)),i.push(c));return new dn(r,i)}function Ld(t){return function(){return this.matches(t)}}function Fb(t){return function(n){return n.matches(t)}}var A8=Array.prototype.find;function S8(t){return function(){return A8.call(this.children,t)}}function T8(){return this.firstElementChild}function E8(t){return this.select(t==null?T8:S8(typeof t=="function"?t:Fb(t)))}var k8=Array.prototype.filter;function N8(){return Array.from(this.children)}function C8(t){return function(){return k8.call(this.children,t)}}function R8(t){return this.selectAll(t==null?N8:C8(typeof t=="function"?t:Fb(t)))}function I8(t){typeof t!="function"&&(t=Ld(t));for(var n=this._groups,e=n.length,r=new Array(e),i=0;i<e;++i)for(var o=n[i],u=o.length,a=r[i]=[],c,f=0;f<u;++f)(c=o[f])&&t.call(c,c.__data__,f,o)&&a.push(c);return new dn(r,this._parents)}function Bb(t){return new Array(t.length)}function z8(){return new dn(this._enter||this._groups.map(Bb),this._parents)}function Lc(t,n){this.ownerDocument=t.ownerDocument,this.namespaceURI=t.namespaceURI,this._next=null,this._parent=t,this.__data__=n}Lc.prototype={constructor:Lc,appendChild:function(t){return this._parent.insertBefore(t,this._next)},insertBefore:function(t,n){return this._parent.insertBefore(t,n)},querySelector:function(t){return this._parent.querySelector(t)},querySelectorAll:function(t){return this._parent.querySelectorAll(t)}};function L8(t){return function(){return t}}function P8(t,n,e,r,i,o){for(var u=0,a,c=n.length,f=o.length;u<f;++u)(a=n[u])?(a.__data__=o[u],r[u]=a):e[u]=new Lc(t,o[u]);for(;u<c;++u)(a=n[u])&&(i[u]=a)}function D8(t,n,e,r,i,o,u){var a,c,f=new Map,s=n.length,h=o.length,l=new Array(s),d;for(a=0;a<s;++a)(c=n[a])&&(l[a]=d=u.call(c,c.__data__,a,n)+"",f.has(d)?i[a]=c:f.set(d,c));for(a=0;a<h;++a)d=u.call(t,o[a],a,o)+"",(c=f.get(d))?(r[a]=c,c.__data__=o[a],f.delete(d)):e[a]=new Lc(t,o[a]);for(a=0;a<s;++a)(c=n[a])&&f.get(l[a])===c&&(i[a]=c)}function O8(t){return t.__data__}function F8(t,n){if(!arguments.length)return Array.from(this,O8);var e=n?D8:P8,r=this._parents,i=this._groups;typeof t!="function"&&(t=L8(t));for(var o=i.length,u=new Array(o),a=new Array(o),c=new Array(o),f=0;f<o;++f){var s=r[f],h=i[f],l=h.length,d=B8(t.call(s,s&&s.__data__,f,r)),p=d.length,m=a[f]=new Array(p),y=u[f]=new Array(p),b=c[f]=new Array(l);e(s,h,m,y,b,d,n);for(var w=0,v=0,g,x;w<p;++w)if(g=m[w]){for(w>=v&&(v=w+1);!(x=y[v])&&++v<p;);g._next=x||null}}return u=new dn(u,r),u._enter=a,u._exit=c,u}function B8(t){return typeof t=="object"&&"length"in t?t:Array.from(t)}function q8(){return new dn(this._exit||this._groups.map(Bb),this._parents)}function Y8(t,n,e){var r=this.enter(),i=this,o=this.exit();return typeof t=="function"?(r=t(r),r&&(r=r.selection())):r=r.append(t+""),n!=null&&(i=n(i),i&&(i=i.selection())),e==null?o.remove():e(o),r&&i?r.merge(i).order():i}function U8(t){for(var n=t.selection?t.selection():t,e=this._groups,r=n._groups,i=e.length,o=r.length,u=Math.min(i,o),a=new Array(i),c=0;c<u;++c)for(var f=e[c],s=r[c],h=f.length,l=a[c]=new Array(h),d,p=0;p<h;++p)(d=f[p]||s[p])&&(l[p]=d);for(;c<i;++c)a[c]=e[c];return new dn(a,this._parents)}function X8(){for(var t=this._groups,n=-1,e=t.length;++n<e;)for(var r=t[n],i=r.length-1,o=r[i],u;--i>=0;)(u=r[i])&&(o&&u.compareDocumentPosition(o)^4&&o.parentNode.insertBefore(u,o),o=u);return this}function W8(t){t||(t=H8);function n(h,l){return h&&l?t(h.__data__,l.__data__):!h-!l}for(var e=this._groups,r=e.length,i=new Array(r),o=0;o<r;++o){for(var u=e[o],a=u.length,c=i[o]=new Array(a),f,s=0;s<a;++s)(f=u[s])&&(c[s]=f);c.sort(n)}return new dn(i,this._parents).order()}function H8(t,n){return t<n?-1:t>n?1:t>=n?0:NaN}function G8(){var t=arguments[0];return arguments[0]=this,t.apply(null,arguments),this}function V8(){return Array.from(this)}function j8(){for(var t=this._groups,n=0,e=t.length;n<e;++n)for(var r=t[n],i=0,o=r.length;i<o;++i){var u=r[i];if(u)return u}return null}function Z8(){let t=0;for(const n of this)++t;return t}function K8(){return!this.node()}function Q8(t){for(var n=this._groups,e=0,r=n.length;e<r;++e)for(var i=n[e],o=0,u=i.length,a;o<u;++o)(a=i[o])&&t.call(a,a.__data__,o,i);return this}function J8(t){return function(){this.removeAttribute(t)}}function tM(t){return function(){this.removeAttributeNS(t.space,t.local)}}function nM(t,n){return function(){this.setAttribute(t,n)}}function eM(t,n){return function(){this.setAttributeNS(t.space,t.local,n)}}function rM(t,n){return function(){var e=n.apply(this,arguments);e==null?this.removeAttribute(t):this.setAttribute(t,e)}}function iM(t,n){return function(){var e=n.apply(this,arguments);e==null?this.removeAttributeNS(t.space,t.local):this.setAttributeNS(t.space,t.local,e)}}function oM(t,n){var e=oa(t);if(arguments.length<2){var r=this.node();return e.local?r.getAttributeNS(e.space,e.local):r.getAttribute(e)}return this.each((n==null?e.local?tM:J8:typeof n=="function"?e.local?iM:rM:e.local?eM:nM)(e,n))}function Pd(t){return t.ownerDocument&&t.ownerDocument.defaultView||t.document&&t||t.defaultView}function uM(t){return function(){this.style.removeProperty(t)}}function aM(t,n,e){return function(){this.style.setProperty(t,n,e)}}function cM(t,n,e){return function(){var r=n.apply(this,arguments);r==null?this.style.removeProperty(t):this.style.setProperty(t,r,e)}}function fM(t,n,e){return arguments.length>1?this.each((n==null?uM:typeof n=="function"?cM:aM)(t,n,e??"")):ii(this.node(),t)}function ii(t,n){return t.style.getPropertyValue(n)||Pd(t).getComputedStyle(t,null).getPropertyValue(n)}function sM(t){return function(){delete this[t]}}function lM(t,n){return function(){this[t]=n}}function hM(t,n){return function(){var e=n.apply(this,arguments);e==null?delete this[t]:this[t]=e}}function dM(t,n){return arguments.length>1?this.each((n==null?sM:typeof n=="function"?hM:lM)(t,n)):this.node()[t]}function qb(t){return t.trim().split(/^|\s+/)}function Dd(t){return t.classList||new Yb(t)}function Yb(t){this._node=t,this._names=qb(t.getAttribute("class")||"")}Yb.prototype={add:function(t){var n=this._names.indexOf(t);n<0&&(this._names.push(t),this._node.setAttribute("class",this._names.join(" ")))},remove:function(t){var n=this._names.indexOf(t);n>=0&&(this._names.splice(n,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(t){return this._names.indexOf(t)>=0}};function Ub(t,n){for(var e=Dd(t),r=-1,i=n.length;++r<i;)e.add(n[r])}function Xb(t,n){for(var e=Dd(t),r=-1,i=n.length;++r<i;)e.remove(n[r])}function gM(t){return function(){Ub(this,t)}}function pM(t){return function(){Xb(this,t)}}function mM(t,n){return function(){(n.apply(this,arguments)?Ub:Xb)(this,t)}}function yM(t,n){var e=qb(t+"");if(arguments.length<2){for(var r=Dd(this.node()),i=-1,o=e.length;++i<o;)if(!r.contains(e[i]))return!1;return!0}return this.each((typeof n=="function"?mM:n?gM:pM)(e,n))}function bM(){this.textContent=""}function wM(t){return function(){this.textContent=t}}function vM(t){return function(){var n=t.apply(this,arguments);this.textContent=n??""}}function xM(t){return arguments.length?this.each(t==null?bM:(typeof t=="function"?vM:wM)(t)):this.node().textContent}function _M(){this.innerHTML=""}function MM(t){return function(){this.innerHTML=t}}function $M(t){return function(){var n=t.apply(this,arguments);this.innerHTML=n??""}}function AM(t){return arguments.length?this.each(t==null?_M:(typeof t=="function"?$M:MM)(t)):this.node().innerHTML}function SM(){this.nextSibling&&this.parentNode.appendChild(this)}function TM(){return this.each(SM)}function EM(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}function kM(){return this.each(EM)}function NM(t){var n=typeof t=="function"?t:So(t);return this.select(function(){return this.appendChild(n.apply(this,arguments))})}function CM(){return null}function RM(t,n){var e=typeof t=="function"?t:So(t),r=n==null?CM:typeof n=="function"?n:Vf(n);return this.select(function(){return this.insertBefore(e.apply(this,arguments),r.apply(this,arguments)||null)})}function IM(){var t=this.parentNode;t&&t.removeChild(this)}function zM(){return this.each(IM)}function LM(){var t=this.cloneNode(!1),n=this.parentNode;return n?n.insertBefore(t,this.nextSibling):t}function PM(){var t=this.cloneNode(!0),n=this.parentNode;return n?n.insertBefore(t,this.nextSibling):t}function DM(t){return this.select(t?PM:LM)}function OM(t){return arguments.length?this.property("__data__",t):this.node().__data__}function FM(t){return function(n){t.call(this,n,this.__data__)}}function BM(t){return t.trim().split(/^|\s+/).map(function(n){var e="",r=n.indexOf(".");return r>=0&&(e=n.slice(r+1),n=n.slice(0,r)),{type:n,name:e}})}function qM(t){return function(){var n=this.__on;if(n){for(var e=0,r=-1,i=n.length,o;e<i;++e)o=n[e],(!t.type||o.type===t.type)&&o.name===t.name?this.removeEventListener(o.type,o.listener,o.options):n[++r]=o;++r?n.length=r:delete this.__on}}}function YM(t,n,e){return function(){var r=this.__on,i,o=FM(n);if(r){for(var u=0,a=r.length;u<a;++u)if((i=r[u]).type===t.type&&i.name===t.name){this.removeEventListener(i.type,i.listener,i.options),this.addEventListener(i.type,i.listener=o,i.options=e),i.value=n;return}}this.addEventListener(t.type,o,e),i={type:t.type,name:t.name,value:n,listener:o,options:e},r?r.push(i):this.__on=[i]}}function UM(t,n,e){var r=BM(t+""),i,o=r.length,u;if(arguments.length<2){var a=this.node().__on;if(a){for(var c=0,f=a.length,s;c<f;++c)for(i=0,s=a[c];i<o;++i)if((u=r[i]).type===s.type&&u.name===s.name)return s.value}return}for(a=n?YM:qM,i=0;i<o;++i)this.each(a(r[i],n,e));return this}function Wb(t,n,e){var r=Pd(t),i=r.CustomEvent;typeof i=="function"?i=new i(n,e):(i=r.document.createEvent("Event"),e?(i.initEvent(n,e.bubbles,e.cancelable),i.detail=e.detail):i.initEvent(n,!1,!1)),t.dispatchEvent(i)}function XM(t,n){return function(){return Wb(this,t,n)}}function WM(t,n){return function(){return Wb(this,t,n.apply(this,arguments))}}function HM(t,n){return this.each((typeof n=="function"?WM:XM)(t,n))}function*GM(){for(var t=this._groups,n=0,e=t.length;n<e;++n)for(var r=t[n],i=0,o=r.length,u;i<o;++i)(u=r[i])&&(yield u)}var Od=[null];function dn(t,n){this._groups=t,this._parents=n}function hi(){return new dn([[document.documentElement]],Od)}function VM(){return this}dn.prototype=hi.prototype={constructor:dn,select:x8,selectAll:$8,selectChild:E8,selectChildren:R8,filter:I8,data:F8,enter:z8,exit:q8,join:Y8,merge:U8,selection:VM,order:X8,sort:W8,call:G8,nodes:V8,node:j8,size:Z8,empty:K8,each:Q8,attr:oM,style:fM,property:dM,classed:yM,text:xM,html:AM,raise:TM,lower:kM,append:NM,insert:RM,remove:zM,clone:DM,datum:OM,on:UM,dispatch:HM,[Symbol.iterator]:GM};function _t(t){return typeof t=="string"?new dn([[document.querySelector(t)]],[document.documentElement]):new dn([[t]],Od)}function jM(t){return _t(So(t).call(document.documentElement))}var ZM=0;function Hb(){return new dh}function dh(){this._="@"+(++ZM).toString(36)}dh.prototype=Hb.prototype={constructor:dh,get:function(t){for(var n=this._;!(n in t);)if(!(t=t.parentNode))return;return t[n]},set:function(t,n){return t[this._]=n},remove:function(t){return this._ in t&&delete t[this._]},toString:function(){return this._}};function Gb(t){let n;for(;n=t.sourceEvent;)t=n;return t}function Mn(t,n){if(t=Gb(t),n===void 0&&(n=t.currentTarget),n){var e=n.ownerSVGElement||n;if(e.createSVGPoint){var r=e.createSVGPoint();return r.x=t.clientX,r.y=t.clientY,r=r.matrixTransform(n.getScreenCTM().inverse()),[r.x,r.y]}if(n.getBoundingClientRect){var i=n.getBoundingClientRect();return[t.clientX-i.left-n.clientLeft,t.clientY-i.top-n.clientTop]}}return[t.pageX,t.pageY]}function KM(t,n){return t.target&&(t=Gb(t),n===void 0&&(n=t.currentTarget),t=t.touches||[t]),Array.from(t,e=>Mn(e,n))}function QM(t){return typeof t=="string"?new dn([document.querySelectorAll(t)],[document.documentElement]):new dn([Ob(t)],Od)}const JM={passive:!1},Su={capture:!0,passive:!1};function ul(t){t.stopImmediatePropagation()}function Wi(t){t.preventDefault(),t.stopImmediatePropagation()}function jf(t){var n=t.document.documentElement,e=_t(t).on("dragstart.drag",Wi,Su);"onselectstart"in n?e.on("selectstart.drag",Wi,Su):(n.__noselect=n.style.MozUserSelect,n.style.MozUserSelect="none")}function Zf(t,n){var e=t.document.documentElement,r=_t(t).on("dragstart.drag",null);n&&(r.on("click.drag",Wi,Su),setTimeout(function(){r.on("click.drag",null)},0)),"onselectstart"in e?r.on("selectstart.drag",null):(e.style.MozUserSelect=e.__noselect,delete e.__noselect)}const Fa=t=>()=>t;function gh(t,{sourceEvent:n,subject:e,target:r,identifier:i,active:o,x:u,y:a,dx:c,dy:f,dispatch:s}){Object.defineProperties(this,{type:{value:t,enumerable:!0,configurable:!0},sourceEvent:{value:n,enumerable:!0,configurable:!0},subject:{value:e,enumerable:!0,configurable:!0},target:{value:r,enumerable:!0,configurable:!0},identifier:{value:i,enumerable:!0,configurable:!0},active:{value:o,enumerable:!0,configurable:!0},x:{value:u,enumerable:!0,configurable:!0},y:{value:a,enumerable:!0,configurable:!0},dx:{value:c,enumerable:!0,configurable:!0},dy:{value:f,enumerable:!0,configurable:!0},_:{value:s}})}gh.prototype.on=function(){var t=this._.on.apply(this._,arguments);return t===this._?this:t};function t7(t){return!t.ctrlKey&&!t.button}function n7(){return this.parentNode}function e7(t,n){return n??{x:t.x,y:t.y}}function r7(){return navigator.maxTouchPoints||"ontouchstart"in this}function i7(){var t=t7,n=n7,e=e7,r=r7,i={},o=li("start","drag","end"),u=0,a,c,f,s,h=0;function l(g){g.on("mousedown.drag",d).filter(r).on("touchstart.drag",y).on("touchmove.drag",b,JM).on("touchend.drag touchcancel.drag",w).style("touch-action","none").style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function d(g,x){if(!(s||!t.call(this,g,x))){var _=v(this,n.call(this,g,x),g,x,"mouse");_&&(_t(g.view).on("mousemove.drag",p,Su).on("mouseup.drag",m,Su),jf(g.view),ul(g),f=!1,a=g.clientX,c=g.clientY,_("start",g))}}function p(g){if(Wi(g),!f){var x=g.clientX-a,_=g.clientY-c;f=x*x+_*_>h}i.mouse("drag",g)}function m(g){_t(g.view).on("mousemove.drag mouseup.drag",null),Zf(g.view,f),Wi(g),i.mouse("end",g)}function y(g,x){if(t.call(this,g,x)){var _=g.changedTouches,M=n.call(this,g,x),A=_.length,T,E;for(T=0;T<A;++T)(E=v(this,M,g,x,_[T].identifier,_[T]))&&(ul(g),E("start",g,_[T]))}}function b(g){var x=g.changedTouches,_=x.length,M,A;for(M=0;M<_;++M)(A=i[x[M].identifier])&&(Wi(g),A("drag",g,x[M]))}function w(g){var x=g.changedTouches,_=x.length,M,A;for(s&&clearTimeout(s),s=setTimeout(function(){s=null},500),M=0;M<_;++M)(A=i[x[M].identifier])&&(ul(g),A("end",g,x[M]))}function v(g,x,_,M,A,T){var E=o.copy(),S=Mn(T||_,x),R,C,$;if(($=e.call(g,new gh("beforestart",{sourceEvent:_,target:l,identifier:A,active:u,x:S[0],y:S[1],dx:0,dy:0,dispatch:E}),M))!=null)return R=$.x-S[0]||0,C=$.y-S[1]||0,function N(k,I,L){var z=S,P;switch(k){case"start":i[A]=N,P=u++;break;case"end":delete i[A],--u;case"drag":S=Mn(L||I,x),P=u;break}E.call(k,g,new gh(k,{sourceEvent:I,subject:$,target:l,identifier:A,active:P,x:S[0]+R,y:S[1]+C,dx:S[0]-z[0],dy:S[1]-z[1],dispatch:E}),M)}}return l.filter=function(g){return arguments.length?(t=typeof g=="function"?g:Fa(!!g),l):t},l.container=function(g){return arguments.length?(n=typeof g=="function"?g:Fa(g),l):n},l.subject=function(g){return arguments.length?(e=typeof g=="function"?g:Fa(g),l):e},l.touchable=function(g){return arguments.length?(r=typeof g=="function"?g:Fa(!!g),l):r},l.on=function(){var g=o.on.apply(o,arguments);return g===o?l:g},l.clickDistance=function(g){return arguments.length?(h=(g=+g)*g,l):Math.sqrt(h)},l}function To(t,n,e){t.prototype=n.prototype=e,e.constructor=t}function ua(t,n){var e=Object.create(t.prototype);for(var r in n)e[r]=n[r];return e}function kr(){}var oi=.7,no=1/oi,Hi="\\s*([+-]?\\d+)\\s*",Tu="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",de="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",o7=/^#([0-9a-f]{3,8})$/,u7=new RegExp(`^rgb\\(${Hi},${Hi},${Hi}\\)$`),a7=new RegExp(`^rgb\\(${de},${de},${de}\\)$`),c7=new RegExp(`^rgba\\(${Hi},${Hi},${Hi},${Tu}\\)$`),f7=new RegExp(`^rgba\\(${de},${de},${de},${Tu}\\)$`),s7=new RegExp(`^hsl\\(${Tu},${de},${de}\\)$`),l7=new RegExp(`^hsla\\(${Tu},${de},${de},${Tu}\\)$`),jp={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};To(kr,mr,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:Zp,formatHex:Zp,formatHex8:h7,formatHsl:d7,formatRgb:Kp,toString:Kp});function Zp(){return this.rgb().formatHex()}function h7(){return this.rgb().formatHex8()}function d7(){return Vb(this).formatHsl()}function Kp(){return this.rgb().formatRgb()}function mr(t){var n,e;return t=(t+"").trim().toLowerCase(),(n=o7.exec(t))?(e=n[1].length,n=parseInt(n[1],16),e===6?Qp(n):e===3?new Ut(n>>8&15|n>>4&240,n>>4&15|n&240,(n&15)<<4|n&15,1):e===8?Ba(n>>24&255,n>>16&255,n>>8&255,(n&255)/255):e===4?Ba(n>>12&15|n>>8&240,n>>8&15|n>>4&240,n>>4&15|n&240,((n&15)<<4|n&15)/255):null):(n=u7.exec(t))?new Ut(n[1],n[2],n[3],1):(n=a7.exec(t))?new Ut(n[1]*255/100,n[2]*255/100,n[3]*255/100,1):(n=c7.exec(t))?Ba(n[1],n[2],n[3],n[4]):(n=f7.exec(t))?Ba(n[1]*255/100,n[2]*255/100,n[3]*255/100,n[4]):(n=s7.exec(t))?nm(n[1],n[2]/100,n[3]/100,1):(n=l7.exec(t))?nm(n[1],n[2]/100,n[3]/100,n[4]):jp.hasOwnProperty(t)?Qp(jp[t]):t==="transparent"?new Ut(NaN,NaN,NaN,0):null}function Qp(t){return new Ut(t>>16&255,t>>8&255,t&255,1)}function Ba(t,n,e,r){return r<=0&&(t=n=e=NaN),new Ut(t,n,e,r)}function Fd(t){return t instanceof kr||(t=mr(t)),t?(t=t.rgb(),new Ut(t.r,t.g,t.b,t.opacity)):new Ut}function Qn(t,n,e,r){return arguments.length===1?Fd(t):new Ut(t,n,e,r??1)}function Ut(t,n,e,r){this.r=+t,this.g=+n,this.b=+e,this.opacity=+r}To(Ut,Qn,ua(kr,{brighter(t){return t=t==null?no:Math.pow(no,t),new Ut(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=t==null?oi:Math.pow(oi,t),new Ut(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new Ut(Zr(this.r),Zr(this.g),Zr(this.b),Pc(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:Jp,formatHex:Jp,formatHex8:g7,formatRgb:tm,toString:tm}));function Jp(){return`#${Wr(this.r)}${Wr(this.g)}${Wr(this.b)}`}function g7(){return`#${Wr(this.r)}${Wr(this.g)}${Wr(this.b)}${Wr((isNaN(this.opacity)?1:this.opacity)*255)}`}function tm(){const t=Pc(this.opacity);return`${t===1?"rgb(":"rgba("}${Zr(this.r)}, ${Zr(this.g)}, ${Zr(this.b)}${t===1?")":`, ${t})`}`}function Pc(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function Zr(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function Wr(t){return t=Zr(t),(t<16?"0":"")+t.toString(16)}function nm(t,n,e,r){return r<=0?t=n=e=NaN:e<=0||e>=1?t=n=NaN:n<=0&&(t=NaN),new Vn(t,n,e,r)}function Vb(t){if(t instanceof Vn)return new Vn(t.h,t.s,t.l,t.opacity);if(t instanceof kr||(t=mr(t)),!t)return new Vn;if(t instanceof Vn)return t;t=t.rgb();var n=t.r/255,e=t.g/255,r=t.b/255,i=Math.min(n,e,r),o=Math.max(n,e,r),u=NaN,a=o-i,c=(o+i)/2;return a?(n===o?u=(e-r)/a+(e<r)*6:e===o?u=(r-n)/a+2:u=(n-e)/a+4,a/=c<.5?o+i:2-o-i,u*=60):a=c>0&&c<1?0:u,new Vn(u,a,c,t.opacity)}function Dc(t,n,e,r){return arguments.length===1?Vb(t):new Vn(t,n,e,r??1)}function Vn(t,n,e,r){this.h=+t,this.s=+n,this.l=+e,this.opacity=+r}To(Vn,Dc,ua(kr,{brighter(t){return t=t==null?no:Math.pow(no,t),new Vn(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=t==null?oi:Math.pow(oi,t),new Vn(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+(this.h<0)*360,n=isNaN(t)||isNaN(this.s)?0:this.s,e=this.l,r=e+(e<.5?e:1-e)*n,i=2*e-r;return new Ut(al(t>=240?t-240:t+120,i,r),al(t,i,r),al(t<120?t+240:t-120,i,r),this.opacity)},clamp(){return new Vn(em(this.h),qa(this.s),qa(this.l),Pc(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const t=Pc(this.opacity);return`${t===1?"hsl(":"hsla("}${em(this.h)}, ${qa(this.s)*100}%, ${qa(this.l)*100}%${t===1?")":`, ${t})`}`}}));function em(t){return t=(t||0)%360,t<0?t+360:t}function qa(t){return Math.max(0,Math.min(1,t||0))}function al(t,n,e){return(t<60?n+(e-n)*t/60:t<180?e:t<240?n+(e-n)*(240-t)/60:n)*255}const jb=Math.PI/180,Zb=180/Math.PI,Oc=18,Kb=.96422,Qb=1,Jb=.82521,tw=4/29,Gi=6/29,nw=3*Gi*Gi,p7=Gi*Gi*Gi;function ew(t){if(t instanceof Zn)return new Zn(t.l,t.a,t.b,t.opacity);if(t instanceof fe)return iw(t);t instanceof Ut||(t=Fd(t));var n=ll(t.r),e=ll(t.g),r=ll(t.b),i=cl((.2225045*n+.7168786*e+.0606169*r)/Qb),o,u;return n===e&&e===r?o=u=i:(o=cl((.4360747*n+.3850649*e+.1430804*r)/Kb),u=cl((.0139322*n+.0971045*e+.7141733*r)/Jb)),new Zn(116*i-16,500*(o-i),200*(i-u),t.opacity)}function m7(t,n){return new Zn(t,0,0,n??1)}function Fc(t,n,e,r){return arguments.length===1?ew(t):new Zn(t,n,e,r??1)}function Zn(t,n,e,r){this.l=+t,this.a=+n,this.b=+e,this.opacity=+r}To(Zn,Fc,ua(kr,{brighter(t){return new Zn(this.l+Oc*(t??1),this.a,this.b,this.opacity)},darker(t){return new Zn(this.l-Oc*(t??1),this.a,this.b,this.opacity)},rgb(){var t=(this.l+16)/116,n=isNaN(this.a)?t:t+this.a/500,e=isNaN(this.b)?t:t-this.b/200;return n=Kb*fl(n),t=Qb*fl(t),e=Jb*fl(e),new Ut(sl(3.1338561*n-1.6168667*t-.4906146*e),sl(-.9787684*n+1.9161415*t+.033454*e),sl(.0719453*n-.2289914*t+1.4052427*e),this.opacity)}}));function cl(t){return t>p7?Math.pow(t,1/3):t/nw+tw}function fl(t){return t>Gi?t*t*t:nw*(t-tw)}function sl(t){return 255*(t<=.0031308?12.92*t:1.055*Math.pow(t,1/2.4)-.055)}function ll(t){return(t/=255)<=.04045?t/12.92:Math.pow((t+.055)/1.055,2.4)}function rw(t){if(t instanceof fe)return new fe(t.h,t.c,t.l,t.opacity);if(t instanceof Zn||(t=ew(t)),t.a===0&&t.b===0)return new fe(NaN,0<t.l&&t.l<100?0:NaN,t.l,t.opacity);var n=Math.atan2(t.b,t.a)*Zb;return new fe(n<0?n+360:n,Math.sqrt(t.a*t.a+t.b*t.b),t.l,t.opacity)}function y7(t,n,e,r){return arguments.length===1?rw(t):new fe(e,n,t,r??1)}function Bc(t,n,e,r){return arguments.length===1?rw(t):new fe(t,n,e,r??1)}function fe(t,n,e,r){this.h=+t,this.c=+n,this.l=+e,this.opacity=+r}function iw(t){if(isNaN(t.h))return new Zn(t.l,0,0,t.opacity);var n=t.h*jb;return new Zn(t.l,Math.cos(n)*t.c,Math.sin(n)*t.c,t.opacity)}To(fe,Bc,ua(kr,{brighter(t){return new fe(this.h,this.c,this.l+Oc*(t??1),this.opacity)},darker(t){return new fe(this.h,this.c,this.l-Oc*(t??1),this.opacity)},rgb(){return iw(this).rgb()}}));var ow=-.14861,Bd=1.78277,qd=-.29227,Kf=-.90649,Eu=1.97294,rm=Eu*Kf,im=Eu*Bd,om=Bd*qd-Kf*ow;function b7(t){if(t instanceof Kr)return new Kr(t.h,t.s,t.l,t.opacity);t instanceof Ut||(t=Fd(t));var n=t.r/255,e=t.g/255,r=t.b/255,i=(om*r+rm*n-im*e)/(om+rm-im),o=r-i,u=(Eu*(e-i)-qd*o)/Kf,a=Math.sqrt(u*u+o*o)/(Eu*i*(1-i)),c=a?Math.atan2(u,o)*Zb-120:NaN;return new Kr(c<0?c+360:c,a,i,t.opacity)}function Jn(t,n,e,r){return arguments.length===1?b7(t):new Kr(t,n,e,r??1)}function Kr(t,n,e,r){this.h=+t,this.s=+n,this.l=+e,this.opacity=+r}To(Kr,Jn,ua(kr,{brighter(t){return t=t==null?no:Math.pow(no,t),new Kr(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=t==null?oi:Math.pow(oi,t),new Kr(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=isNaN(this.h)?0:(this.h+120)*jb,n=+this.l,e=isNaN(this.s)?0:this.s*n*(1-n),r=Math.cos(t),i=Math.sin(t);return new Ut(255*(n+e*(ow*r+Bd*i)),255*(n+e*(qd*r+Kf*i)),255*(n+e*(Eu*r)),this.opacity)}}));function uw(t,n,e,r,i){var o=t*t,u=o*t;return((1-3*t+3*o-u)*n+(4-6*o+3*u)*e+(1+3*t+3*o-3*u)*r+u*i)/6}function aw(t){var n=t.length-1;return function(e){var r=e<=0?e=0:e>=1?(e=1,n-1):Math.floor(e*n),i=t[r],o=t[r+1],u=r>0?t[r-1]:2*i-o,a=r<n-1?t[r+2]:2*o-i;return uw((e-r/n)*n,u,i,o,a)}}function cw(t){var n=t.length;return function(e){var r=Math.floor(((e%=1)<0?++e:e)*n),i=t[(r+n-1)%n],o=t[r%n],u=t[(r+1)%n],a=t[(r+2)%n];return uw((e-r/n)*n,i,o,u,a)}}const Qf=t=>()=>t;function fw(t,n){return function(e){return t+e*n}}function w7(t,n,e){return t=Math.pow(t,e),n=Math.pow(n,e)-t,e=1/e,function(r){return Math.pow(t+r*n,e)}}function Jf(t,n){var e=n-t;return e?fw(t,e>180||e<-180?e-360*Math.round(e/360):e):Qf(isNaN(t)?n:t)}function v7(t){return(t=+t)==1?Xt:function(n,e){return e-n?w7(n,e,t):Qf(isNaN(n)?e:n)}}function Xt(t,n){var e=n-t;return e?fw(t,e):Qf(isNaN(t)?n:t)}const yr=function t(n){var e=v7(n);function r(i,o){var u=e((i=Qn(i)).r,(o=Qn(o)).r),a=e(i.g,o.g),c=e(i.b,o.b),f=Xt(i.opacity,o.opacity);return function(s){return i.r=u(s),i.g=a(s),i.b=c(s),i.opacity=f(s),i+""}}return r.gamma=t,r}(1);function sw(t){return function(n){var e=n.length,r=new Array(e),i=new Array(e),o=new Array(e),u,a;for(u=0;u<e;++u)a=Qn(n[u]),r[u]=a.r||0,i[u]=a.g||0,o[u]=a.b||0;return r=t(r),i=t(i),o=t(o),a.opacity=1,function(c){return a.r=r(c),a.g=i(c),a.b=o(c),a+""}}}var lw=sw(aw),x7=sw(cw);function Yd(t,n){n||(n=[]);var e=t?Math.min(n.length,t.length):0,r=n.slice(),i;return function(o){for(i=0;i<e;++i)r[i]=t[i]*(1-o)+n[i]*o;return r}}function hw(t){return ArrayBuffer.isView(t)&&!(t instanceof DataView)}function _7(t,n){return(hw(n)?Yd:dw)(t,n)}function dw(t,n){var e=n?n.length:0,r=t?Math.min(e,t.length):0,i=new Array(r),o=new Array(e),u;for(u=0;u<r;++u)i[u]=Nr(t[u],n[u]);for(;u<e;++u)o[u]=n[u];return function(a){for(u=0;u<r;++u)o[u]=i[u](a);return o}}function gw(t,n){var e=new Date;return t=+t,n=+n,function(r){return e.setTime(t*(1-r)+n*r),e}}function Gt(t,n){return t=+t,n=+n,function(e){return t*(1-e)+n*e}}function pw(t,n){var e={},r={},i;(t===null||typeof t!="object")&&(t={}),(n===null||typeof n!="object")&&(n={});for(i in n)i in t?e[i]=Nr(t[i],n[i]):r[i]=n[i];return function(o){for(i in e)r[i]=e[i](o);return r}}var ph=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,hl=new RegExp(ph.source,"g");function M7(t){return function(){return t}}function $7(t){return function(n){return t(n)+""}}function Ud(t,n){var e=ph.lastIndex=hl.lastIndex=0,r,i,o,u=-1,a=[],c=[];for(t=t+"",n=n+"";(r=ph.exec(t))&&(i=hl.exec(n));)(o=i.index)>e&&(o=n.slice(e,o),a[u]?a[u]+=o:a[++u]=o),(r=r[0])===(i=i[0])?a[u]?a[u]+=i:a[++u]=i:(a[++u]=null,c.push({i:u,x:Gt(r,i)})),e=hl.lastIndex;return e<n.length&&(o=n.slice(e),a[u]?a[u]+=o:a[++u]=o),a.length<2?c[0]?$7(c[0].x):M7(n):(n=c.length,function(f){for(var s=0,h;s<n;++s)a[(h=c[s]).i]=h.x(f);return a.join("")})}function Nr(t,n){var e=typeof n,r;return n==null||e==="boolean"?Qf(n):(e==="number"?Gt:e==="string"?(r=mr(n))?(n=r,yr):Ud:n instanceof mr?yr:n instanceof Date?gw:hw(n)?Yd:Array.isArray(n)?dw:typeof n.valueOf!="function"&&typeof n.toString!="function"||isNaN(n)?pw:Gt)(t,n)}function A7(t){var n=t.length;return function(e){return t[Math.max(0,Math.min(n-1,Math.floor(e*n)))]}}function S7(t,n){var e=Jf(+t,+n);return function(r){var i=e(r);return i-360*Math.floor(i/360)}}function aa(t,n){return t=+t,n=+n,function(e){return Math.round(t*(1-e)+n*e)}}var um=180/Math.PI,mh={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function mw(t,n,e,r,i,o){var u,a,c;return(u=Math.sqrt(t*t+n*n))&&(t/=u,n/=u),(c=t*e+n*r)&&(e-=t*c,r-=n*c),(a=Math.sqrt(e*e+r*r))&&(e/=a,r/=a,c/=a),t*r<n*e&&(t=-t,n=-n,c=-c,u=-u),{translateX:i,translateY:o,rotate:Math.atan2(n,t)*um,skewX:Math.atan(c)*um,scaleX:u,scaleY:a}}var Ya;function T7(t){const n=new(typeof DOMMatrix=="function"?DOMMatrix:WebKitCSSMatrix)(t+"");return n.isIdentity?mh:mw(n.a,n.b,n.c,n.d,n.e,n.f)}function E7(t){return t==null||(Ya||(Ya=document.createElementNS("http://www.w3.org/2000/svg","g")),Ya.setAttribute("transform",t),!(t=Ya.transform.baseVal.consolidate()))?mh:(t=t.matrix,mw(t.a,t.b,t.c,t.d,t.e,t.f))}function yw(t,n,e,r){function i(f){return f.length?f.pop()+" ":""}function o(f,s,h,l,d,p){if(f!==h||s!==l){var m=d.push("translate(",null,n,null,e);p.push({i:m-4,x:Gt(f,h)},{i:m-2,x:Gt(s,l)})}else(h||l)&&d.push("translate("+h+n+l+e)}function u(f,s,h,l){f!==s?(f-s>180?s+=360:s-f>180&&(f+=360),l.push({i:h.push(i(h)+"rotate(",null,r)-2,x:Gt(f,s)})):s&&h.push(i(h)+"rotate("+s+r)}function a(f,s,h,l){f!==s?l.push({i:h.push(i(h)+"skewX(",null,r)-2,x:Gt(f,s)}):s&&h.push(i(h)+"skewX("+s+r)}function c(f,s,h,l,d,p){if(f!==h||s!==l){var m=d.push(i(d)+"scale(",null,",",null,")");p.push({i:m-4,x:Gt(f,h)},{i:m-2,x:Gt(s,l)})}else(h!==1||l!==1)&&d.push(i(d)+"scale("+h+","+l+")")}return function(f,s){var h=[],l=[];return f=t(f),s=t(s),o(f.translateX,f.translateY,s.translateX,s.translateY,h,l),u(f.rotate,s.rotate,h,l),a(f.skewX,s.skewX,h,l),c(f.scaleX,f.scaleY,s.scaleX,s.scaleY,h,l),f=s=null,function(d){for(var p=-1,m=l.length,y;++p<m;)h[(y=l[p]).i]=y.x(d);return h.join("")}}}var bw=yw(T7,"px, ","px)","deg)"),ww=yw(E7,", ",")",")"),k7=1e-12;function am(t){return((t=Math.exp(t))+1/t)/2}function N7(t){return((t=Math.exp(t))-1/t)/2}function C7(t){return((t=Math.exp(2*t))-1)/(t+1)}const vw=function t(n,e,r){function i(o,u){var a=o[0],c=o[1],f=o[2],s=u[0],h=u[1],l=u[2],d=s-a,p=h-c,m=d*d+p*p,y,b;if(m<k7)b=Math.log(l/f)/n,y=function(M){return[a+M*d,c+M*p,f*Math.exp(n*M*b)]};else{var w=Math.sqrt(m),v=(l*l-f*f+r*m)/(2*f*e*w),g=(l*l-f*f-r*m)/(2*l*e*w),x=Math.log(Math.sqrt(v*v+1)-v),_=Math.log(Math.sqrt(g*g+1)-g);b=(_-x)/n,y=function(M){var A=M*b,T=am(x),E=f/(e*w)*(T*C7(n*A+x)-N7(x));return[a+E*d,c+E*p,f*T/am(n*A+x)]}}return y.duration=b*1e3*n/Math.SQRT2,y}return i.rho=function(o){var u=Math.max(.001,+o),a=u*u,c=a*a;return t(u,a,c)},i}(Math.SQRT2,2,4);function xw(t){return function(n,e){var r=t((n=Dc(n)).h,(e=Dc(e)).h),i=Xt(n.s,e.s),o=Xt(n.l,e.l),u=Xt(n.opacity,e.opacity);return function(a){return n.h=r(a),n.s=i(a),n.l=o(a),n.opacity=u(a),n+""}}}const _w=xw(Jf);var R7=xw(Xt);function Mw(t,n){var e=Xt((t=Fc(t)).l,(n=Fc(n)).l),r=Xt(t.a,n.a),i=Xt(t.b,n.b),o=Xt(t.opacity,n.opacity);return function(u){return t.l=e(u),t.a=r(u),t.b=i(u),t.opacity=o(u),t+""}}function $w(t){return function(n,e){var r=t((n=Bc(n)).h,(e=Bc(e)).h),i=Xt(n.c,e.c),o=Xt(n.l,e.l),u=Xt(n.opacity,e.opacity);return function(a){return n.h=r(a),n.c=i(a),n.l=o(a),n.opacity=u(a),n+""}}}const Aw=$w(Jf);var I7=$w(Xt);function Sw(t){return function n(e){e=+e;function r(i,o){var u=t((i=Jn(i)).h,(o=Jn(o)).h),a=Xt(i.s,o.s),c=Xt(i.l,o.l),f=Xt(i.opacity,o.opacity);return function(s){return i.h=u(s),i.s=a(s),i.l=c(Math.pow(s,e)),i.opacity=f(s),i+""}}return r.gamma=n,r}(1)}const z7=Sw(Jf);var ts=Sw(Xt);function ca(t,n){n===void 0&&(n=t,t=Nr);for(var e=0,r=n.length-1,i=n[0],o=new Array(r<0?0:r);e<r;)o[e]=t(i,i=n[++e]);return function(u){var a=Math.max(0,Math.min(r-1,Math.floor(u*=r)));return o[a](u-a)}}function qn(t,n){for(var e=new Array(n),r=0;r<n;++r)e[r]=t(r/(n-1));return e}var eo=0,iu=0,Ho=0,Tw=1e3,qc,ou,Yc=0,ui=0,ns=0,ku=typeof performance=="object"&&performance.now?performance:Date,Ew=typeof window=="object"&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(t){setTimeout(t,17)};function fa(){return ui||(Ew(L7),ui=ku.now()+ns)}function L7(){ui=0}function Nu(){this._call=this._time=this._next=null}Nu.prototype=es.prototype={constructor:Nu,restart:function(t,n,e){if(typeof t!="function")throw new TypeError("callback is not a function");e=(e==null?fa():+e)+(n==null?0:+n),!this._next&&ou!==this&&(ou?ou._next=this:qc=this,ou=this),this._call=t,this._time=e,yh()},stop:function(){this._call&&(this._call=null,this._time=1/0,yh())}};function es(t,n,e){var r=new Nu;return r.restart(t,n,e),r}function kw(){fa(),++eo;for(var t=qc,n;t;)(n=ui-t._time)>=0&&t._call.call(void 0,n),t=t._next;--eo}function cm(){ui=(Yc=ku.now())+ns,eo=iu=0;try{kw()}finally{eo=0,D7(),ui=0}}function P7(){var t=ku.now(),n=t-Yc;n>Tw&&(ns-=n,Yc=t)}function D7(){for(var t,n=qc,e,r=1/0;n;)n._call?(r>n._time&&(r=n._time),t=n,n=n._next):(e=n._next,n._next=null,n=t?t._next=e:qc=e);ou=t,yh(r)}function yh(t){if(!eo){iu&&(iu=clearTimeout(iu));var n=t-ui;n>24?(t<1/0&&(iu=setTimeout(cm,t-ku.now()-ns)),Ho&&(Ho=clearInterval(Ho))):(Ho||(Yc=ku.now(),Ho=setInterval(P7,Tw)),eo=1,Ew(cm))}}function bh(t,n,e){var r=new Nu;return n=n==null?0:+n,r.restart(i=>{r.stop(),t(i+n)},n,e),r}function O7(t,n,e){var r=new Nu,i=n;return n==null?(r.restart(t,n,e),r):(r._restart=r.restart,r.restart=function(o,u,a){u=+u,a=a==null?fa():+a,r._restart(function c(f){f+=i,r._restart(c,i+=u,a),o(f)},u,a)},r.restart(t,n,e),r)}var F7=li("start","end","cancel","interrupt"),B7=[],Nw=0,wh=1,vh=2,_c=3,fm=4,xh=5,Mc=6;function rs(t,n,e,r,i,o){var u=t.__transition;if(!u)t.__transition={};else if(e in u)return;q7(t,e,{name:n,index:r,group:i,on:F7,tween:B7,time:o.time,delay:o.delay,duration:o.duration,ease:o.ease,timer:null,state:Nw})}function Xd(t,n){var e=te(t,n);if(e.state>Nw)throw new Error("too late; already scheduled");return e}function _e(t,n){var e=te(t,n);if(e.state>_c)throw new Error("too late; already running");return e}function te(t,n){var e=t.__transition;if(!e||!(e=e[n]))throw new Error("transition not found");return e}function q7(t,n,e){var r=t.__transition,i;r[n]=e,e.timer=es(o,0,e.time);function o(f){e.state=wh,e.timer.restart(u,e.delay,e.time),e.delay<=f&&u(f-e.delay)}function u(f){var s,h,l,d;if(e.state!==wh)return c();for(s in r)if(d=r[s],d.name===e.name){if(d.state===_c)return bh(u);d.state===fm?(d.state=Mc,d.timer.stop(),d.on.call("interrupt",t,t.__data__,d.index,d.group),delete r[s]):+s<n&&(d.state=Mc,d.timer.stop(),d.on.call("cancel",t,t.__data__,d.index,d.group),delete r[s])}if(bh(function(){e.state===_c&&(e.state=fm,e.timer.restart(a,e.delay,e.time),a(f))}),e.state=vh,e.on.call("start",t,t.__data__,e.index,e.group),e.state===vh){for(e.state=_c,i=new Array(l=e.tween.length),s=0,h=-1;s<l;++s)(d=e.tween[s].value.call(t,t.__data__,e.index,e.group))&&(i[++h]=d);i.length=h+1}}function a(f){for(var s=f<e.duration?e.ease.call(null,f/e.duration):(e.timer.restart(c),e.state=xh,1),h=-1,l=i.length;++h<l;)i[h].call(t,s);e.state===xh&&(e.on.call("end",t,t.__data__,e.index,e.group),c())}function c(){e.state=Mc,e.timer.stop(),delete r[n];for(var f in r)return;delete t.__transition}}function Qr(t,n){var e=t.__transition,r,i,o=!0,u;if(e){n=n==null?null:n+"";for(u in e){if((r=e[u]).name!==n){o=!1;continue}i=r.state>vh&&r.state<xh,r.state=Mc,r.timer.stop(),r.on.call(i?"interrupt":"cancel",t,t.__data__,r.index,r.group),delete e[u]}o&&delete t.__transition}}function Y7(t){return this.each(function(){Qr(this,t)})}function U7(t,n){var e,r;return function(){var i=_e(this,t),o=i.tween;if(o!==e){r=e=o;for(var u=0,a=r.length;u<a;++u)if(r[u].name===n){r=r.slice(),r.splice(u,1);break}}i.tween=r}}function X7(t,n,e){var r,i;if(typeof e!="function")throw new Error;return function(){var o=_e(this,t),u=o.tween;if(u!==r){i=(r=u).slice();for(var a={name:n,value:e},c=0,f=i.length;c<f;++c)if(i[c].name===n){i[c]=a;break}c===f&&i.push(a)}o.tween=i}}function W7(t,n){var e=this._id;if(t+="",arguments.length<2){for(var r=te(this.node(),e).tween,i=0,o=r.length,u;i<o;++i)if((u=r[i]).name===t)return u.value;return null}return this.each((n==null?U7:X7)(e,t,n))}function Wd(t,n,e){var r=t._id;return t.each(function(){var i=_e(this,r);(i.value||(i.value={}))[n]=e.apply(this,arguments)}),function(i){return te(i,r).value[n]}}function Cw(t,n){var e;return(typeof n=="number"?Gt:n instanceof mr?yr:(e=mr(n))?(n=e,yr):Ud)(t,n)}function H7(t){return function(){this.removeAttribute(t)}}function G7(t){return function(){this.removeAttributeNS(t.space,t.local)}}function V7(t,n,e){var r,i=e+"",o;return function(){var u=this.getAttribute(t);return u===i?null:u===r?o:o=n(r=u,e)}}function j7(t,n,e){var r,i=e+"",o;return function(){var u=this.getAttributeNS(t.space,t.local);return u===i?null:u===r?o:o=n(r=u,e)}}function Z7(t,n,e){var r,i,o;return function(){var u,a=e(this),c;return a==null?void this.removeAttribute(t):(u=this.getAttribute(t),c=a+"",u===c?null:u===r&&c===i?o:(i=c,o=n(r=u,a)))}}function K7(t,n,e){var r,i,o;return function(){var u,a=e(this),c;return a==null?void this.removeAttributeNS(t.space,t.local):(u=this.getAttributeNS(t.space,t.local),c=a+"",u===c?null:u===r&&c===i?o:(i=c,o=n(r=u,a)))}}function Q7(t,n){var e=oa(t),r=e==="transform"?ww:Cw;return this.attrTween(t,typeof n=="function"?(e.local?K7:Z7)(e,r,Wd(this,"attr."+t,n)):n==null?(e.local?G7:H7)(e):(e.local?j7:V7)(e,r,n))}function J7(t,n){return function(e){this.setAttribute(t,n.call(this,e))}}function t9(t,n){return function(e){this.setAttributeNS(t.space,t.local,n.call(this,e))}}function n9(t,n){var e,r;function i(){var o=n.apply(this,arguments);return o!==r&&(e=(r=o)&&t9(t,o)),e}return i._value=n,i}function e9(t,n){var e,r;function i(){var o=n.apply(this,arguments);return o!==r&&(e=(r=o)&&J7(t,o)),e}return i._value=n,i}function r9(t,n){var e="attr."+t;if(arguments.length<2)return(e=this.tween(e))&&e._value;if(n==null)return this.tween(e,null);if(typeof n!="function")throw new Error;var r=oa(t);return this.tween(e,(r.local?n9:e9)(r,n))}function i9(t,n){return function(){Xd(this,t).delay=+n.apply(this,arguments)}}function o9(t,n){return n=+n,function(){Xd(this,t).delay=n}}function u9(t){var n=this._id;return arguments.length?this.each((typeof t=="function"?i9:o9)(n,t)):te(this.node(),n).delay}function a9(t,n){return function(){_e(this,t).duration=+n.apply(this,arguments)}}function c9(t,n){return n=+n,function(){_e(this,t).duration=n}}function f9(t){var n=this._id;return arguments.length?this.each((typeof t=="function"?a9:c9)(n,t)):te(this.node(),n).duration}function s9(t,n){if(typeof n!="function")throw new Error;return function(){_e(this,t).ease=n}}function l9(t){var n=this._id;return arguments.length?this.each(s9(n,t)):te(this.node(),n).ease}function h9(t,n){return function(){var e=n.apply(this,arguments);if(typeof e!="function")throw new Error;_e(this,t).ease=e}}function d9(t){if(typeof t!="function")throw new Error;return this.each(h9(this._id,t))}function g9(t){typeof t!="function"&&(t=Ld(t));for(var n=this._groups,e=n.length,r=new Array(e),i=0;i<e;++i)for(var o=n[i],u=o.length,a=r[i]=[],c,f=0;f<u;++f)(c=o[f])&&t.call(c,c.__data__,f,o)&&a.push(c);return new ye(r,this._parents,this._name,this._id)}function p9(t){if(t._id!==this._id)throw new Error;for(var n=this._groups,e=t._groups,r=n.length,i=e.length,o=Math.min(r,i),u=new Array(r),a=0;a<o;++a)for(var c=n[a],f=e[a],s=c.length,h=u[a]=new Array(s),l,d=0;d<s;++d)(l=c[d]||f[d])&&(h[d]=l);for(;a<r;++a)u[a]=n[a];return new ye(u,this._parents,this._name,this._id)}function m9(t){return(t+"").trim().split(/^|\s+/).every(function(n){var e=n.indexOf(".");return e>=0&&(n=n.slice(0,e)),!n||n==="start"})}function y9(t,n,e){var r,i,o=m9(n)?Xd:_e;return function(){var u=o(this,t),a=u.on;a!==r&&(i=(r=a).copy()).on(n,e),u.on=i}}function b9(t,n){var e=this._id;return arguments.length<2?te(this.node(),e).on.on(t):this.each(y9(e,t,n))}function w9(t){return function(){var n=this.parentNode;for(var e in this.__transition)if(+e!==t)return;n&&n.removeChild(this)}}function v9(){return this.on("end.remove",w9(this._id))}function x9(t){var n=this._name,e=this._id;typeof t!="function"&&(t=Vf(t));for(var r=this._groups,i=r.length,o=new Array(i),u=0;u<i;++u)for(var a=r[u],c=a.length,f=o[u]=new Array(c),s,h,l=0;l<c;++l)(s=a[l])&&(h=t.call(s,s.__data__,l,a))&&("__data__"in s&&(h.__data__=s.__data__),f[l]=h,rs(f[l],n,e,l,f,te(s,e)));return new ye(o,this._parents,n,e)}function _9(t){var n=this._name,e=this._id;typeof t!="function"&&(t=zd(t));for(var r=this._groups,i=r.length,o=[],u=[],a=0;a<i;++a)for(var c=r[a],f=c.length,s,h=0;h<f;++h)if(s=c[h]){for(var l=t.call(s,s.__data__,h,c),d,p=te(s,e),m=0,y=l.length;m<y;++m)(d=l[m])&&rs(d,n,e,m,l,p);o.push(l),u.push(s)}return new ye(o,u,n,e)}var M9=hi.prototype.constructor;function $9(){return new M9(this._groups,this._parents)}function A9(t,n){var e,r,i;return function(){var o=ii(this,t),u=(this.style.removeProperty(t),ii(this,t));return o===u?null:o===e&&u===r?i:i=n(e=o,r=u)}}function Rw(t){return function(){this.style.removeProperty(t)}}function S9(t,n,e){var r,i=e+"",o;return function(){var u=ii(this,t);return u===i?null:u===r?o:o=n(r=u,e)}}function T9(t,n,e){var r,i,o;return function(){var u=ii(this,t),a=e(this),c=a+"";return a==null&&(c=a=(this.style.removeProperty(t),ii(this,t))),u===c?null:u===r&&c===i?o:(i=c,o=n(r=u,a))}}function E9(t,n){var e,r,i,o="style."+n,u="end."+o,a;return function(){var c=_e(this,t),f=c.on,s=c.value[o]==null?a||(a=Rw(n)):void 0;(f!==e||i!==s)&&(r=(e=f).copy()).on(u,i=s),c.on=r}}function k9(t,n,e){var r=(t+="")=="transform"?bw:Cw;return n==null?this.styleTween(t,A9(t,r)).on("end.style."+t,Rw(t)):typeof n=="function"?this.styleTween(t,T9(t,r,Wd(this,"style."+t,n))).each(E9(this._id,t)):this.styleTween(t,S9(t,r,n),e).on("end.style."+t,null)}function N9(t,n,e){return function(r){this.style.setProperty(t,n.call(this,r),e)}}function C9(t,n,e){var r,i;function o(){var u=n.apply(this,arguments);return u!==i&&(r=(i=u)&&N9(t,u,e)),r}return o._value=n,o}function R9(t,n,e){var r="style."+(t+="");if(arguments.length<2)return(r=this.tween(r))&&r._value;if(n==null)return this.tween(r,null);if(typeof n!="function")throw new Error;return this.tween(r,C9(t,n,e??""))}function I9(t){return function(){this.textContent=t}}function z9(t){return function(){var n=t(this);this.textContent=n??""}}function L9(t){return this.tween("text",typeof t=="function"?z9(Wd(this,"text",t)):I9(t==null?"":t+""))}function P9(t){return function(n){this.textContent=t.call(this,n)}}function D9(t){var n,e;function r(){var i=t.apply(this,arguments);return i!==e&&(n=(e=i)&&P9(i)),n}return r._value=t,r}function O9(t){var n="text";if(arguments.length<1)return(n=this.tween(n))&&n._value;if(t==null)return this.tween(n,null);if(typeof t!="function")throw new Error;return this.tween(n,D9(t))}function F9(){for(var t=this._name,n=this._id,e=zw(),r=this._groups,i=r.length,o=0;o<i;++o)for(var u=r[o],a=u.length,c,f=0;f<a;++f)if(c=u[f]){var s=te(c,n);rs(c,t,e,f,u,{time:s.time+s.delay+s.duration,delay:0,duration:s.duration,ease:s.ease})}return new ye(r,this._parents,t,e)}function B9(){var t,n,e=this,r=e._id,i=e.size();return new Promise(function(o,u){var a={value:u},c={value:function(){--i===0&&o()}};e.each(function(){var f=_e(this,r),s=f.on;s!==t&&(n=(t=s).copy(),n._.cancel.push(a),n._.interrupt.push(a),n._.end.push(c)),f.on=n}),i===0&&o()})}var q9=0;function ye(t,n,e,r){this._groups=t,this._parents=n,this._name=e,this._id=r}function Iw(t){return hi().transition(t)}function zw(){return++q9}var Te=hi.prototype;ye.prototype=Iw.prototype={constructor:ye,select:x9,selectAll:_9,selectChild:Te.selectChild,selectChildren:Te.selectChildren,filter:g9,merge:p9,selection:$9,transition:F9,call:Te.call,nodes:Te.nodes,node:Te.node,size:Te.size,empty:Te.empty,each:Te.each,on:b9,attr:Q7,attrTween:r9,style:k9,styleTween:R9,text:L9,textTween:O9,remove:v9,tween:W7,delay:u9,duration:f9,ease:l9,easeVarying:d9,end:B9,[Symbol.iterator]:Te[Symbol.iterator]};const Y9=t=>+t;function U9(t){return t*t}function X9(t){return t*(2-t)}function sm(t){return((t*=2)<=1?t*t:--t*(2-t)+1)/2}function W9(t){return t*t*t}function H9(t){return--t*t*t+1}function _h(t){return((t*=2)<=1?t*t*t:(t-=2)*t*t+2)/2}var Hd=3,G9=function t(n){n=+n;function e(r){return Math.pow(r,n)}return e.exponent=t,e}(Hd),V9=function t(n){n=+n;function e(r){return 1-Math.pow(1-r,n)}return e.exponent=t,e}(Hd),lm=function t(n){n=+n;function e(r){return((r*=2)<=1?Math.pow(r,n):2-Math.pow(2-r,n))/2}return e.exponent=t,e}(Hd),Lw=Math.PI,Pw=Lw/2;function j9(t){return+t==1?1:1-Math.cos(t*Pw)}function Z9(t){return Math.sin(t*Pw)}function hm(t){return(1-Math.cos(Lw*t))/2}function br(t){return(Math.pow(2,-10*t)-.0009765625)*1.0009775171065494}function K9(t){return br(1-+t)}function Q9(t){return 1-br(t)}function dm(t){return((t*=2)<=1?br(1-t):2-br(t-1))/2}function J9(t){return 1-Math.sqrt(1-t*t)}function t$(t){return Math.sqrt(1- --t*t)}function gm(t){return((t*=2)<=1?1-Math.sqrt(1-t*t):Math.sqrt(1-(t-=2)*t)+1)/2}var Mh=4/11,n$=6/11,e$=8/11,r$=3/4,i$=9/11,o$=10/11,u$=15/16,a$=21/22,c$=63/64,Ua=1/Mh/Mh;function f$(t){return 1-Cu(1-t)}function Cu(t){return(t=+t)<Mh?Ua*t*t:t<e$?Ua*(t-=n$)*t+r$:t<o$?Ua*(t-=i$)*t+u$:Ua*(t-=a$)*t+c$}function s$(t){return((t*=2)<=1?1-Cu(1-t):Cu(t-1)+1)/2}var Gd=1.70158,l$=function t(n){n=+n;function e(r){return(r=+r)*r*(n*(r-1)+r)}return e.overshoot=t,e}(Gd),h$=function t(n){n=+n;function e(r){return--r*r*((r+1)*n+r)+1}return e.overshoot=t,e}(Gd),pm=function t(n){n=+n;function e(r){return((r*=2)<1?r*r*((n+1)*r-n):(r-=2)*r*((n+1)*r+n)+2)/2}return e.overshoot=t,e}(Gd),ro=2*Math.PI,Vd=1,jd=.3,d$=function t(n,e){var r=Math.asin(1/(n=Math.max(1,n)))*(e/=ro);function i(o){return n*br(- --o)*Math.sin((r-o)/e)}return i.amplitude=function(o){return t(o,e*ro)},i.period=function(o){return t(n,o)},i}(Vd,jd),mm=function t(n,e){var r=Math.asin(1/(n=Math.max(1,n)))*(e/=ro);function i(o){return 1-n*br(o=+o)*Math.sin((o+r)/e)}return i.amplitude=function(o){return t(o,e*ro)},i.period=function(o){return t(n,o)},i}(Vd,jd),g$=function t(n,e){var r=Math.asin(1/(n=Math.max(1,n)))*(e/=ro);function i(o){return((o=o*2-1)<0?n*br(-o)*Math.sin((r-o)/e):2-n*br(o)*Math.sin((r+o)/e))/2}return i.amplitude=function(o){return t(o,e*ro)},i.period=function(o){return t(n,o)},i}(Vd,jd),p$={time:null,delay:0,duration:250,ease:_h};function m$(t,n){for(var e;!(e=t.__transition)||!(e=e[n]);)if(!(t=t.parentNode))throw new Error(`transition ${n} not found`);return e}function y$(t){var n,e;t instanceof ye?(n=t._id,t=t._name):(n=zw(),(e=p$).time=fa(),t=t==null?null:t+"");for(var r=this._groups,i=r.length,o=0;o<i;++o)for(var u=r[o],a=u.length,c,f=0;f<a;++f)(c=u[f])&&rs(c,t,n,f,u,e||m$(c,n));return new ye(r,this._parents,t,n)}hi.prototype.interrupt=Y7;hi.prototype.transition=y$;var b$=[null];function w$(t,n){var e=t.__transition,r,i;if(e){n=n==null?null:n+"";for(i in e)if((r=e[i]).state>wh&&r.name===n)return new ye([[t]],b$,n,+i)}return null}const dl=t=>()=>t;function v$(t,{sourceEvent:n,target:e,selection:r,mode:i,dispatch:o}){Object.defineProperties(this,{type:{value:t,enumerable:!0,configurable:!0},sourceEvent:{value:n,enumerable:!0,configurable:!0},target:{value:e,enumerable:!0,configurable:!0},selection:{value:r,enumerable:!0,configurable:!0},mode:{value:i,enumerable:!0,configurable:!0},_:{value:o}})}function x$(t){t.stopImmediatePropagation()}function gl(t){t.preventDefault(),t.stopImmediatePropagation()}var ym={name:"drag"},pl={name:"space"},Si={name:"handle"},Ti={name:"center"};const{abs:bm,max:Jt,min:tn}=Math;function wm(t){return[+t[0],+t[1]]}function $h(t){return[wm(t[0]),wm(t[1])]}var $c={name:"x",handles:["w","e"].map(Ru),input:function(t,n){return t==null?null:[[+t[0],n[0][1]],[+t[1],n[1][1]]]},output:function(t){return t&&[t[0][0],t[1][0]]}},Ac={name:"y",handles:["n","s"].map(Ru),input:function(t,n){return t==null?null:[[n[0][0],+t[0]],[n[1][0],+t[1]]]},output:function(t){return t&&[t[0][1],t[1][1]]}},_$={name:"xy",handles:["n","w","e","s","nw","ne","sw","se"].map(Ru),input:function(t){return t==null?null:$h(t)},output:function(t){return t}},Ee={overlay:"crosshair",selection:"move",n:"ns-resize",e:"ew-resize",s:"ns-resize",w:"ew-resize",nw:"nwse-resize",ne:"nesw-resize",se:"nwse-resize",sw:"nesw-resize"},vm={e:"w",w:"e",nw:"ne",ne:"nw",se:"sw",sw:"se"},xm={n:"s",s:"n",nw:"sw",ne:"se",se:"ne",sw:"nw"},M$={overlay:1,selection:1,n:null,e:1,s:null,w:-1,nw:-1,ne:1,se:1,sw:-1},$$={overlay:1,selection:1,n:-1,e:null,s:1,w:null,nw:-1,ne:-1,se:1,sw:1};function Ru(t){return{type:t}}function A$(t){return!t.ctrlKey&&!t.button}function S$(){var t=this.ownerSVGElement||this;return t.hasAttribute("viewBox")?(t=t.viewBox.baseVal,[[t.x,t.y],[t.x+t.width,t.y+t.height]]):[[0,0],[t.width.baseVal.value,t.height.baseVal.value]]}function T$(){return navigator.maxTouchPoints||"ontouchstart"in this}function ml(t){for(;!t.__brush;)if(!(t=t.parentNode))return;return t.__brush}function E$(t){return t[0][0]===t[1][0]||t[0][1]===t[1][1]}function k$(t){var n=t.__brush;return n?n.dim.output(n.selection):null}function N$(){return Zd($c)}function C$(){return Zd(Ac)}function R$(){return Zd(_$)}function Zd(t){var n=S$,e=A$,r=T$,i=!0,o=li("start","brush","end"),u=6,a;function c(y){var b=y.property("__brush",m).selectAll(".overlay").data([Ru("overlay")]);b.enter().append("rect").attr("class","overlay").attr("pointer-events","all").attr("cursor",Ee.overlay).merge(b).each(function(){var v=ml(this).extent;_t(this).attr("x",v[0][0]).attr("y",v[0][1]).attr("width",v[1][0]-v[0][0]).attr("height",v[1][1]-v[0][1])}),y.selectAll(".selection").data([Ru("selection")]).enter().append("rect").attr("class","selection").attr("cursor",Ee.selection).attr("fill","#777").attr("fill-opacity",.3).attr("stroke","#fff").attr("shape-rendering","crispEdges");var w=y.selectAll(".handle").data(t.handles,function(v){return v.type});w.exit().remove(),w.enter().append("rect").attr("class",function(v){return"handle handle--"+v.type}).attr("cursor",function(v){return Ee[v.type]}),y.each(f).attr("fill","none").attr("pointer-events","all").on("mousedown.brush",l).filter(r).on("touchstart.brush",l).on("touchmove.brush",d).on("touchend.brush touchcancel.brush",p).style("touch-action","none").style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}c.move=function(y,b,w){y.tween?y.on("start.brush",function(v){s(this,arguments).beforestart().start(v)}).on("interrupt.brush end.brush",function(v){s(this,arguments).end(v)}).tween("brush",function(){var v=this,g=v.__brush,x=s(v,arguments),_=g.selection,M=t.input(typeof b=="function"?b.apply(this,arguments):b,g.extent),A=Nr(_,M);function T(E){g.selection=E===1&&M===null?null:A(E),f.call(v),x.brush()}return _!==null&&M!==null?T:T(1)}):y.each(function(){var v=this,g=arguments,x=v.__brush,_=t.input(typeof b=="function"?b.apply(v,g):b,x.extent),M=s(v,g).beforestart();Qr(v),x.selection=_===null?null:_,f.call(v),M.start(w).brush(w).end(w)})},c.clear=function(y,b){c.move(y,null,b)};function f(){var y=_t(this),b=ml(this).selection;b?(y.selectAll(".selection").style("display",null).attr("x",b[0][0]).attr("y",b[0][1]).attr("width",b[1][0]-b[0][0]).attr("height",b[1][1]-b[0][1]),y.selectAll(".handle").style("display",null).attr("x",function(w){return w.type[w.type.length-1]==="e"?b[1][0]-u/2:b[0][0]-u/2}).attr("y",function(w){return w.type[0]==="s"?b[1][1]-u/2:b[0][1]-u/2}).attr("width",function(w){return w.type==="n"||w.type==="s"?b[1][0]-b[0][0]+u:u}).attr("height",function(w){return w.type==="e"||w.type==="w"?b[1][1]-b[0][1]+u:u})):y.selectAll(".selection,.handle").style("display","none").attr("x",null).attr("y",null).attr("width",null).attr("height",null)}function s(y,b,w){var v=y.__brush.emitter;return v&&(!w||!v.clean)?v:new h(y,b,w)}function h(y,b,w){this.that=y,this.args=b,this.state=y.__brush,this.active=0,this.clean=w}h.prototype={beforestart:function(){return++this.active===1&&(this.state.emitter=this,this.starting=!0),this},start:function(y,b){return this.starting?(this.starting=!1,this.emit("start",y,b)):this.emit("brush",y),this},brush:function(y,b){return this.emit("brush",y,b),this},end:function(y,b){return--this.active===0&&(delete this.state.emitter,this.emit("end",y,b)),this},emit:function(y,b,w){var v=_t(this.that).datum();o.call(y,this.that,new v$(y,{sourceEvent:b,target:c,selection:t.output(this.state.selection),mode:w,dispatch:o}),v)}};function l(y){if(a&&!y.touches||!e.apply(this,arguments))return;var b=this,w=y.target.__data__.type,v=(i&&y.metaKey?w="overlay":w)==="selection"?ym:i&&y.altKey?Ti:Si,g=t===Ac?null:M$[w],x=t===$c?null:$$[w],_=ml(b),M=_.extent,A=_.selection,T=M[0][0],E,S,R=M[0][1],C,$,N=M[1][0],k,I,L=M[1][1],z,P,D=0,B=0,Y,U=g&&x&&i&&y.shiftKey,W,Z,q=Array.from(y.touches||[y],j=>{const gt=j.identifier;return j=Mn(j,b),j.point0=j.slice(),j.identifier=gt,j});Qr(b);var H=s(b,arguments,!0).beforestart();if(w==="overlay"){A&&(Y=!0);const j=[q[0],q[1]||q[0]];_.selection=A=[[E=t===Ac?T:tn(j[0][0],j[1][0]),C=t===$c?R:tn(j[0][1],j[1][1])],[k=t===Ac?N:Jt(j[0][0],j[1][0]),z=t===$c?L:Jt(j[0][1],j[1][1])]],q.length>1&&rt(y)}else E=A[0][0],C=A[0][1],k=A[1][0],z=A[1][1];S=E,$=C,I=k,P=z;var F=_t(b).attr("pointer-events","none"),X=F.selectAll(".overlay").attr("cursor",Ee[w]);if(y.touches)H.moved=O,H.ended=Q;else{var G=_t(y.view).on("mousemove.brush",O,!0).on("mouseup.brush",Q,!0);i&&G.on("keydown.brush",ft,!0).on("keyup.brush",lt,!0),jf(y.view)}f.call(b),H.start(y,v.name);function O(j){for(const gt of j.changedTouches||[j])for(const Sn of q)Sn.identifier===gt.identifier&&(Sn.cur=Mn(gt,b));if(U&&!W&&!Z&&q.length===1){const gt=q[0];bm(gt.cur[0]-gt[0])>bm(gt.cur[1]-gt[1])?Z=!0:W=!0}for(const gt of q)gt.cur&&(gt[0]=gt.cur[0],gt[1]=gt.cur[1]);Y=!0,gl(j),rt(j)}function rt(j){const gt=q[0],Sn=gt.point0;var re;switch(D=gt[0]-Sn[0],B=gt[1]-Sn[1],v){case pl:case ym:{g&&(D=Jt(T-E,tn(N-k,D)),S=E+D,I=k+D),x&&(B=Jt(R-C,tn(L-z,B)),$=C+B,P=z+B);break}case Si:{q[1]?(g&&(S=Jt(T,tn(N,q[0][0])),I=Jt(T,tn(N,q[1][0])),g=1),x&&($=Jt(R,tn(L,q[0][1])),P=Jt(R,tn(L,q[1][1])),x=1)):(g<0?(D=Jt(T-E,tn(N-E,D)),S=E+D,I=k):g>0&&(D=Jt(T-k,tn(N-k,D)),S=E,I=k+D),x<0?(B=Jt(R-C,tn(L-C,B)),$=C+B,P=z):x>0&&(B=Jt(R-z,tn(L-z,B)),$=C,P=z+B));break}case Ti:{g&&(S=Jt(T,tn(N,E-D*g)),I=Jt(T,tn(N,k+D*g))),x&&($=Jt(R,tn(L,C-B*x)),P=Jt(R,tn(L,z+B*x)));break}}I<S&&(g*=-1,re=E,E=k,k=re,re=S,S=I,I=re,w in vm&&X.attr("cursor",Ee[w=vm[w]])),P<$&&(x*=-1,re=C,C=z,z=re,re=$,$=P,P=re,w in xm&&X.attr("cursor",Ee[w=xm[w]])),_.selection&&(A=_.selection),W&&(S=A[0][0],I=A[1][0]),Z&&($=A[0][1],P=A[1][1]),(A[0][0]!==S||A[0][1]!==$||A[1][0]!==I||A[1][1]!==P)&&(_.selection=[[S,$],[I,P]],f.call(b),H.brush(j,v.name))}function Q(j){if(x$(j),j.touches){if(j.touches.length)return;a&&clearTimeout(a),a=setTimeout(function(){a=null},500)}else Zf(j.view,Y),G.on("keydown.brush keyup.brush mousemove.brush mouseup.brush",null);F.attr("pointer-events","all"),X.attr("cursor",Ee.overlay),_.selection&&(A=_.selection),E$(A)&&(_.selection=null,f.call(b)),H.end(j,v.name)}function ft(j){switch(j.keyCode){case 16:{U=g&&x;break}case 18:{v===Si&&(g&&(k=I-D*g,E=S+D*g),x&&(z=P-B*x,C=$+B*x),v=Ti,rt(j));break}case 32:{(v===Si||v===Ti)&&(g<0?k=I-D:g>0&&(E=S-D),x<0?z=P-B:x>0&&(C=$-B),v=pl,X.attr("cursor",Ee.selection),rt(j));break}default:return}gl(j)}function lt(j){switch(j.keyCode){case 16:{U&&(W=Z=U=!1,rt(j));break}case 18:{v===Ti&&(g<0?k=I:g>0&&(E=S),x<0?z=P:x>0&&(C=$),v=Si,rt(j));break}case 32:{v===pl&&(j.altKey?(g&&(k=I-D*g,E=S+D*g),x&&(z=P-B*x,C=$+B*x),v=Ti):(g<0?k=I:g>0&&(E=S),x<0?z=P:x>0&&(C=$),v=Si),X.attr("cursor",Ee[w]),rt(j));break}default:return}gl(j)}}function d(y){s(this,arguments).moved(y)}function p(y){s(this,arguments).ended(y)}function m(){var y=this.__brush||{selection:null};return y.extent=$h(n.apply(this,arguments)),y.dim=t,y}return c.extent=function(y){return arguments.length?(n=typeof y=="function"?y:dl($h(y)),c):n},c.filter=function(y){return arguments.length?(e=typeof y=="function"?y:dl(!!y),c):e},c.touchable=function(y){return arguments.length?(r=typeof y=="function"?y:dl(!!y),c):r},c.handleSize=function(y){return arguments.length?(u=+y,c):u},c.keyModifiers=function(y){return arguments.length?(i=!!y,c):i},c.on=function(){var y=o.on.apply(o,arguments);return y===o?c:y},c}var _m=Math.abs,Ei=Math.cos,ki=Math.sin,Dw=Math.PI,Xa=Dw/2,Mm=Dw*2,$m=Math.max,yl=1e-12;function bl(t,n){return Array.from({length:n-t},(e,r)=>t+r)}function I$(t){return function(n,e){return t(n.source.value+n.target.value,e.source.value+e.target.value)}}function z$(){return Kd(!1,!1)}function L$(){return Kd(!1,!0)}function P$(){return Kd(!0,!1)}function Kd(t,n){var e=0,r=null,i=null,o=null;function u(a){var c=a.length,f=new Array(c),s=bl(0,c),h=new Array(c*c),l=new Array(c),d=0,p;a=Float64Array.from({length:c*c},n?(m,y)=>a[y%c][y/c|0]:(m,y)=>a[y/c|0][y%c]);for(let m=0;m<c;++m){let y=0;for(let b=0;b<c;++b)y+=a[m*c+b]+t*a[b*c+m];d+=f[m]=y}d=$m(0,Mm-e*c)/d,p=d?e:Mm/c;{let m=0;r&&s.sort((y,b)=>r(f[y],f[b]));for(const y of s){const b=m;if(t){const w=bl(~c+1,c).filter(v=>v<0?a[~v*c+y]:a[y*c+v]);i&&w.sort((v,g)=>i(v<0?-a[~v*c+y]:a[y*c+v],g<0?-a[~g*c+y]:a[y*c+g]));for(const v of w)if(v<0){const g=h[~v*c+y]||(h[~v*c+y]={source:null,target:null});g.target={index:y,startAngle:m,endAngle:m+=a[~v*c+y]*d,value:a[~v*c+y]}}else{const g=h[y*c+v]||(h[y*c+v]={source:null,target:null});g.source={index:y,startAngle:m,endAngle:m+=a[y*c+v]*d,value:a[y*c+v]}}l[y]={index:y,startAngle:b,endAngle:m,value:f[y]}}else{const w=bl(0,c).filter(v=>a[y*c+v]||a[v*c+y]);i&&w.sort((v,g)=>i(a[y*c+v],a[y*c+g]));for(const v of w){let g;if(y<v?(g=h[y*c+v]||(h[y*c+v]={source:null,target:null}),g.source={index:y,startAngle:m,endAngle:m+=a[y*c+v]*d,value:a[y*c+v]}):(g=h[v*c+y]||(h[v*c+y]={source:null,target:null}),g.target={index:y,startAngle:m,endAngle:m+=a[y*c+v]*d,value:a[y*c+v]},y===v&&(g.source=g.target)),g.source&&g.target&&g.source.value<g.target.value){const x=g.source;g.source=g.target,g.target=x}}l[y]={index:y,startAngle:b,endAngle:m,value:f[y]}}m+=p}}return h=Object.values(h),h.groups=l,o?h.sort(o):h}return u.padAngle=function(a){return arguments.length?(e=$m(0,a),u):e},u.sortGroups=function(a){return arguments.length?(r=a,u):r},u.sortSubgroups=function(a){return arguments.length?(i=a,u):i},u.sortChords=function(a){return arguments.length?(a==null?o=null:(o=I$(a))._=a,u):o&&o._},u}const Ah=Math.PI,Sh=2*Ah,qr=1e-6,D$=Sh-qr;function Ow(t){this._+=t[0];for(let n=1,e=t.length;n<e;++n)this._+=arguments[n]+t[n]}function O$(t){let n=Math.floor(t);if(!(n>=0))throw new Error(`invalid digits: ${t}`);if(n>15)return Ow;const e=10**n;return function(r){this._+=r[0];for(let i=1,o=r.length;i<o;++i)this._+=Math.round(arguments[i]*e)/e+r[i]}}let sa=class{constructor(n){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=n==null?Ow:O$(n)}moveTo(n,e){this._append`M${this._x0=this._x1=+n},${this._y0=this._y1=+e}`}closePath(){this._x1!==null&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(n,e){this._append`L${this._x1=+n},${this._y1=+e}`}quadraticCurveTo(n,e,r,i){this._append`Q${+n},${+e},${this._x1=+r},${this._y1=+i}`}bezierCurveTo(n,e,r,i,o,u){this._append`C${+n},${+e},${+r},${+i},${this._x1=+o},${this._y1=+u}`}arcTo(n,e,r,i,o){if(n=+n,e=+e,r=+r,i=+i,o=+o,o<0)throw new Error(`negative radius: ${o}`);let u=this._x1,a=this._y1,c=r-n,f=i-e,s=u-n,h=a-e,l=s*s+h*h;if(this._x1===null)this._append`M${this._x1=n},${this._y1=e}`;else if(l>qr)if(!(Math.abs(h*c-f*s)>qr)||!o)this._append`L${this._x1=n},${this._y1=e}`;else{let d=r-u,p=i-a,m=c*c+f*f,y=d*d+p*p,b=Math.sqrt(m),w=Math.sqrt(l),v=o*Math.tan((Ah-Math.acos((m+l-y)/(2*b*w)))/2),g=v/w,x=v/b;Math.abs(g-1)>qr&&this._append`L${n+g*s},${e+g*h}`,this._append`A${o},${o},0,0,${+(h*d>s*p)},${this._x1=n+x*c},${this._y1=e+x*f}`}}arc(n,e,r,i,o,u){if(n=+n,e=+e,r=+r,u=!!u,r<0)throw new Error(`negative radius: ${r}`);let a=r*Math.cos(i),c=r*Math.sin(i),f=n+a,s=e+c,h=1^u,l=u?i-o:o-i;this._x1===null?this._append`M${f},${s}`:(Math.abs(this._x1-f)>qr||Math.abs(this._y1-s)>qr)&&this._append`L${f},${s}`,r&&(l<0&&(l=l%Sh+Sh),l>D$?this._append`A${r},${r},0,1,${h},${n-a},${e-c}A${r},${r},0,1,${h},${this._x1=f},${this._y1=s}`:l>qr&&this._append`A${r},${r},0,${+(l>=Ah)},${h},${this._x1=n+r*Math.cos(o)},${this._y1=e+r*Math.sin(o)}`)}rect(n,e,r,i){this._append`M${this._x0=this._x1=+n},${this._y0=this._y1=+e}h${r=+r}v${+i}h${-r}Z`}toString(){return this._}};function Qd(){return new sa}Qd.prototype=sa.prototype;function se(t=3){return new sa(+t)}var F$=Array.prototype.slice;function Fr(t){return function(){return t}}function B$(t){return t.source}function q$(t){return t.target}function Am(t){return t.radius}function Y$(t){return t.startAngle}function U$(t){return t.endAngle}function X$(){return 0}function W$(){return 10}function Fw(t){var n=B$,e=q$,r=Am,i=Am,o=Y$,u=U$,a=X$,c=null;function f(){var s,h=n.apply(this,arguments),l=e.apply(this,arguments),d=a.apply(this,arguments)/2,p=F$.call(arguments),m=+r.apply(this,(p[0]=h,p)),y=o.apply(this,p)-Xa,b=u.apply(this,p)-Xa,w=+i.apply(this,(p[0]=l,p)),v=o.apply(this,p)-Xa,g=u.apply(this,p)-Xa;if(c||(c=s=Qd()),d>yl&&(_m(b-y)>d*2+yl?b>y?(y+=d,b-=d):(y-=d,b+=d):y=b=(y+b)/2,_m(g-v)>d*2+yl?g>v?(v+=d,g-=d):(v-=d,g+=d):v=g=(v+g)/2),c.moveTo(m*Ei(y),m*ki(y)),c.arc(0,0,m,y,b),y!==v||b!==g)if(t){var x=+t.apply(this,arguments),_=w-x,M=(v+g)/2;c.quadraticCurveTo(0,0,_*Ei(v),_*ki(v)),c.lineTo(w*Ei(M),w*ki(M)),c.lineTo(_*Ei(g),_*ki(g))}else c.quadraticCurveTo(0,0,w*Ei(v),w*ki(v)),c.arc(0,0,w,v,g);if(c.quadraticCurveTo(0,0,m*Ei(y),m*ki(y)),c.closePath(),s)return c=null,s+""||null}return t&&(f.headRadius=function(s){return arguments.length?(t=typeof s=="function"?s:Fr(+s),f):t}),f.radius=function(s){return arguments.length?(r=i=typeof s=="function"?s:Fr(+s),f):r},f.sourceRadius=function(s){return arguments.length?(r=typeof s=="function"?s:Fr(+s),f):r},f.targetRadius=function(s){return arguments.length?(i=typeof s=="function"?s:Fr(+s),f):i},f.startAngle=function(s){return arguments.length?(o=typeof s=="function"?s:Fr(+s),f):o},f.endAngle=function(s){return arguments.length?(u=typeof s=="function"?s:Fr(+s),f):u},f.padAngle=function(s){return arguments.length?(a=typeof s=="function"?s:Fr(+s),f):a},f.source=function(s){return arguments.length?(n=s,f):n},f.target=function(s){return arguments.length?(e=s,f):e},f.context=function(s){return arguments.length?(c=s??null,f):c},f}function H$(){return Fw()}function G$(){return Fw(W$)}var V$=Array.prototype,Bw=V$.slice;function j$(t,n){return t-n}function Z$(t){for(var n=0,e=t.length,r=t[e-1][1]*t[0][0]-t[e-1][0]*t[0][1];++n<e;)r+=t[n-1][1]*t[n][0]-t[n-1][0]*t[n][1];return r}const ir=t=>()=>t;function K$(t,n){for(var e=-1,r=n.length,i;++e<r;)if(i=Q$(t,n[e]))return i;return 0}function Q$(t,n){for(var e=n[0],r=n[1],i=-1,o=0,u=t.length,a=u-1;o<u;a=o++){var c=t[o],f=c[0],s=c[1],h=t[a],l=h[0],d=h[1];if(J$(c,h,n))return 0;s>r!=d>r&&e<(l-f)*(r-s)/(d-s)+f&&(i=-i)}return i}function J$(t,n,e){var r;return tA(t,n,e)&&nA(t[r=+(t[0]===n[0])],e[r],n[r])}function tA(t,n,e){return(n[0]-t[0])*(e[1]-t[1])===(e[0]-t[0])*(n[1]-t[1])}function nA(t,n,e){return t<=n&&n<=e||e<=n&&n<=t}function eA(){}var ke=[[],[[[1,1.5],[.5,1]]],[[[1.5,1],[1,1.5]]],[[[1.5,1],[.5,1]]],[[[1,.5],[1.5,1]]],[[[1,1.5],[.5,1]],[[1,.5],[1.5,1]]],[[[1,.5],[1,1.5]]],[[[1,.5],[.5,1]]],[[[.5,1],[1,.5]]],[[[1,1.5],[1,.5]]],[[[.5,1],[1,.5]],[[1.5,1],[1,1.5]]],[[[1.5,1],[1,.5]]],[[[.5,1],[1.5,1]]],[[[1,1.5],[1.5,1]]],[[[.5,1],[1,1.5]]],[]];function Uc(){var t=1,n=1,e=ea,r=c;function i(f){var s=e(f);if(Array.isArray(s))s=s.slice().sort(j$);else{const h=Tt(f,rA);for(s=ge(...Uf(h[0],h[1],s),s);s[s.length-1]>=h[1];)s.pop();for(;s[1]<h[0];)s.shift()}return s.map(h=>o(f,h))}function o(f,s){const h=s==null?NaN:+s;if(isNaN(h))throw new Error(`invalid value: ${s}`);var l=[],d=[];return u(f,h,function(p){r(p,f,h),Z$(p)>0?l.push([p]):d.push(p)}),d.forEach(function(p){for(var m=0,y=l.length,b;m<y;++m)if(K$((b=l[m])[0],p)!==-1){b.push(p);return}}),{type:"MultiPolygon",value:s,coordinates:l}}function u(f,s,h){var l=new Array,d=new Array,p,m,y,b,w,v;for(p=m=-1,b=Br(f[0],s),ke[b<<1].forEach(g);++p<t-1;)y=b,b=Br(f[p+1],s),ke[y|b<<1].forEach(g);for(ke[b<<0].forEach(g);++m<n-1;){for(p=-1,b=Br(f[m*t+t],s),w=Br(f[m*t],s),ke[b<<1|w<<2].forEach(g);++p<t-1;)y=b,b=Br(f[m*t+t+p+1],s),v=w,w=Br(f[m*t+p+1],s),ke[y|b<<1|w<<2|v<<3].forEach(g);ke[b|w<<3].forEach(g)}for(p=-1,w=f[m*t]>=s,ke[w<<2].forEach(g);++p<t-1;)v=w,w=Br(f[m*t+p+1],s),ke[w<<2|v<<3].forEach(g);ke[w<<3].forEach(g);function g(x){var _=[x[0][0]+p,x[0][1]+m],M=[x[1][0]+p,x[1][1]+m],A=a(_),T=a(M),E,S;(E=d[A])?(S=l[T])?(delete d[E.end],delete l[S.start],E===S?(E.ring.push(M),h(E.ring)):l[E.start]=d[S.end]={start:E.start,end:S.end,ring:E.ring.concat(S.ring)}):(delete d[E.end],E.ring.push(M),d[E.end=T]=E):(E=l[T])?(S=d[A])?(delete l[E.start],delete d[S.end],E===S?(E.ring.push(M),h(E.ring)):l[S.start]=d[E.end]={start:S.start,end:E.end,ring:S.ring.concat(E.ring)}):(delete l[E.start],E.ring.unshift(_),l[E.start=A]=E):l[A]=d[T]={start:A,end:T,ring:[_,M]}}}function a(f){return f[0]*2+f[1]*(t+1)*4}function c(f,s,h){f.forEach(function(l){var d=l[0],p=l[1],m=d|0,y=p|0,b=wl(s[y*t+m]);d>0&&d<t&&m===d&&(l[0]=Sm(d,wl(s[y*t+m-1]),b,h)),p>0&&p<n&&y===p&&(l[1]=Sm(p,wl(s[(y-1)*t+m]),b,h))})}return i.contour=o,i.size=function(f){if(!arguments.length)return[t,n];var s=Math.floor(f[0]),h=Math.floor(f[1]);if(!(s>=0&&h>=0))throw new Error("invalid size");return t=s,n=h,i},i.thresholds=function(f){return arguments.length?(e=typeof f=="function"?f:Array.isArray(f)?ir(Bw.call(f)):ir(f),i):e},i.smooth=function(f){return arguments.length?(r=f?c:eA,i):r===c},i}function rA(t){return isFinite(t)?t:NaN}function Br(t,n){return t==null?!1:+t>=n}function wl(t){return t==null||isNaN(t=+t)?-1/0:t}function Sm(t,n,e,r){const i=r-n,o=e-n,u=isFinite(i)||isFinite(o)?i/o:Math.sign(i)/Math.sign(o);return isNaN(u)?t:t+u-.5}function iA(t){return t[0]}function oA(t){return t[1]}function uA(){return 1}function qw(){var t=iA,n=oA,e=uA,r=960,i=500,o=20,u=2,a=o*3,c=r+a*2>>u,f=i+a*2>>u,s=ir(20);function h(w){var v=new Float32Array(c*f),g=Math.pow(2,-u),x=-1;for(const C of w){var _=(t(C,++x,w)+a)*g,M=(n(C,x,w)+a)*g,A=+e(C,x,w);if(A&&_>=0&&_<c&&M>=0&&M<f){var T=Math.floor(_),E=Math.floor(M),S=_-T-.5,R=M-E-.5;v[T+E*c]+=(1-S)*(1-R)*A,v[T+1+E*c]+=S*(1-R)*A,v[T+1+(E+1)*c]+=S*R*A,v[T+(E+1)*c]+=(1-S)*R*A}}return Sd({data:v,width:c,height:f},o*g),v}function l(w){var v=h(w),g=s(v),x=Math.pow(2,2*u);return Array.isArray(g)||(g=ge(Number.MIN_VALUE,qt(v)/x,g)),Uc().size([c,f]).thresholds(g.map(_=>_*x))(v).map((_,M)=>(_.value=+g[M],d(_)))}l.contours=function(w){var v=h(w),g=Uc().size([c,f]),x=Math.pow(2,2*u),_=M=>{M=+M;var A=d(g.contour(v,M*x));return A.value=M,A};return Object.defineProperty(_,"max",{get:()=>qt(v)/x}),_};function d(w){return w.coordinates.forEach(p),w}function p(w){w.forEach(m)}function m(w){w.forEach(y)}function y(w){w[0]=w[0]*Math.pow(2,u)-a,w[1]=w[1]*Math.pow(2,u)-a}function b(){return a=o*3,c=r+a*2>>u,f=i+a*2>>u,l}return l.x=function(w){return arguments.length?(t=typeof w=="function"?w:ir(+w),l):t},l.y=function(w){return arguments.length?(n=typeof w=="function"?w:ir(+w),l):n},l.weight=function(w){return arguments.length?(e=typeof w=="function"?w:ir(+w),l):e},l.size=function(w){if(!arguments.length)return[r,i];var v=+w[0],g=+w[1];if(!(v>=0&&g>=0))throw new Error("invalid size");return r=v,i=g,b()},l.cellSize=function(w){if(!arguments.length)return 1<<u;if(!((w=+w)>=1))throw new Error("invalid cell size");return u=Math.floor(Math.log(w)/Math.LN2),b()},l.thresholds=function(w){return arguments.length?(s=typeof w=="function"?w:Array.isArray(w)?ir(Bw.call(w)):ir(w),l):s},l.bandwidth=function(w){if(!arguments.length)return Math.sqrt(o*(o+1));if(!((w=+w)>=0))throw new Error("invalid bandwidth");return o=(Math.sqrt(4*w*w+1)-1)/2,b()},l}const Ye=11102230246251565e-32,nn=134217729,aA=(3+8*Ye)*Ye;function vl(t,n,e,r,i){let o,u,a,c,f=n[0],s=r[0],h=0,l=0;s>f==s>-f?(o=f,f=n[++h]):(o=s,s=r[++l]);let d=0;if(h<t&&l<e)for(s>f==s>-f?(u=f+o,a=o-(u-f),f=n[++h]):(u=s+o,a=o-(u-s),s=r[++l]),o=u,a!==0&&(i[d++]=a);h<t&&l<e;)s>f==s>-f?(u=o+f,c=u-o,a=o-(u-c)+(f-c),f=n[++h]):(u=o+s,c=u-o,a=o-(u-c)+(s-c),s=r[++l]),o=u,a!==0&&(i[d++]=a);for(;h<t;)u=o+f,c=u-o,a=o-(u-c)+(f-c),f=n[++h],o=u,a!==0&&(i[d++]=a);for(;l<e;)u=o+s,c=u-o,a=o-(u-c)+(s-c),s=r[++l],o=u,a!==0&&(i[d++]=a);return(o!==0||d===0)&&(i[d++]=o),d}function cA(t,n){let e=n[0];for(let r=1;r<t;r++)e+=n[r];return e}function la(t){return new Float64Array(t)}const fA=(3+16*Ye)*Ye,sA=(2+12*Ye)*Ye,lA=(9+64*Ye)*Ye*Ye,Ni=la(4),Tm=la(8),Em=la(12),km=la(16),an=la(4);function hA(t,n,e,r,i,o,u){let a,c,f,s,h,l,d,p,m,y,b,w,v,g,x,_,M,A;const T=t-i,E=e-i,S=n-o,R=r-o;g=T*R,l=nn*T,d=l-(l-T),p=T-d,l=nn*R,m=l-(l-R),y=R-m,x=p*y-(g-d*m-p*m-d*y),_=S*E,l=nn*S,d=l-(l-S),p=S-d,l=nn*E,m=l-(l-E),y=E-m,M=p*y-(_-d*m-p*m-d*y),b=x-M,h=x-b,Ni[0]=x-(b+h)+(h-M),w=g+b,h=w-g,v=g-(w-h)+(b-h),b=v-_,h=v-b,Ni[1]=v-(b+h)+(h-_),A=w+b,h=A-w,Ni[2]=w-(A-h)+(b-h),Ni[3]=A;let C=cA(4,Ni),$=sA*u;if(C>=$||-C>=$||(h=t-T,a=t-(T+h)+(h-i),h=e-E,f=e-(E+h)+(h-i),h=n-S,c=n-(S+h)+(h-o),h=r-R,s=r-(R+h)+(h-o),a===0&&c===0&&f===0&&s===0)||($=lA*u+aA*Math.abs(C),C+=T*s+R*a-(S*f+E*c),C>=$||-C>=$))return C;g=a*R,l=nn*a,d=l-(l-a),p=a-d,l=nn*R,m=l-(l-R),y=R-m,x=p*y-(g-d*m-p*m-d*y),_=c*E,l=nn*c,d=l-(l-c),p=c-d,l=nn*E,m=l-(l-E),y=E-m,M=p*y-(_-d*m-p*m-d*y),b=x-M,h=x-b,an[0]=x-(b+h)+(h-M),w=g+b,h=w-g,v=g-(w-h)+(b-h),b=v-_,h=v-b,an[1]=v-(b+h)+(h-_),A=w+b,h=A-w,an[2]=w-(A-h)+(b-h),an[3]=A;const N=vl(4,Ni,4,an,Tm);g=T*s,l=nn*T,d=l-(l-T),p=T-d,l=nn*s,m=l-(l-s),y=s-m,x=p*y-(g-d*m-p*m-d*y),_=S*f,l=nn*S,d=l-(l-S),p=S-d,l=nn*f,m=l-(l-f),y=f-m,M=p*y-(_-d*m-p*m-d*y),b=x-M,h=x-b,an[0]=x-(b+h)+(h-M),w=g+b,h=w-g,v=g-(w-h)+(b-h),b=v-_,h=v-b,an[1]=v-(b+h)+(h-_),A=w+b,h=A-w,an[2]=w-(A-h)+(b-h),an[3]=A;const k=vl(N,Tm,4,an,Em);g=a*s,l=nn*a,d=l-(l-a),p=a-d,l=nn*s,m=l-(l-s),y=s-m,x=p*y-(g-d*m-p*m-d*y),_=c*f,l=nn*c,d=l-(l-c),p=c-d,l=nn*f,m=l-(l-f),y=f-m,M=p*y-(_-d*m-p*m-d*y),b=x-M,h=x-b,an[0]=x-(b+h)+(h-M),w=g+b,h=w-g,v=g-(w-h)+(b-h),b=v-_,h=v-b,an[1]=v-(b+h)+(h-_),A=w+b,h=A-w,an[2]=w-(A-h)+(b-h),an[3]=A;const I=vl(k,Em,4,an,km);return km[I-1]}function Wa(t,n,e,r,i,o){const u=(n-o)*(e-i),a=(t-i)*(r-o),c=u-a,f=Math.abs(u+a);return Math.abs(c)>=fA*f?c:-hA(t,n,e,r,i,o,f)}const Nm=Math.pow(2,-52),Ha=new Uint32Array(512);class Xc{static from(n,e=yA,r=bA){const i=n.length,o=new Float64Array(i*2);for(let u=0;u<i;u++){const a=n[u];o[2*u]=e(a),o[2*u+1]=r(a)}return new Xc(o)}constructor(n){const e=n.length>>1;if(e>0&&typeof n[0]!="number")throw new Error("Expected coords to contain numbers.");this.coords=n;const r=Math.max(2*e-5,0);this._triangles=new Uint32Array(r*3),this._halfedges=new Int32Array(r*3),this._hashSize=Math.ceil(Math.sqrt(e)),this._hullPrev=new Uint32Array(e),this._hullNext=new Uint32Array(e),this._hullTri=new Uint32Array(e),this._hullHash=new Int32Array(this._hashSize),this._ids=new Uint32Array(e),this._dists=new Float64Array(e),this.update()}update(){const{coords:n,_hullPrev:e,_hullNext:r,_hullTri:i,_hullHash:o}=this,u=n.length>>1;let a=1/0,c=1/0,f=-1/0,s=-1/0;for(let T=0;T<u;T++){const E=n[2*T],S=n[2*T+1];E<a&&(a=E),S<c&&(c=S),E>f&&(f=E),S>s&&(s=S),this._ids[T]=T}const h=(a+f)/2,l=(c+s)/2;let d,p,m;for(let T=0,E=1/0;T<u;T++){const S=xl(h,l,n[2*T],n[2*T+1]);S<E&&(d=T,E=S)}const y=n[2*d],b=n[2*d+1];for(let T=0,E=1/0;T<u;T++){if(T===d)continue;const S=xl(y,b,n[2*T],n[2*T+1]);S<E&&S>0&&(p=T,E=S)}let w=n[2*p],v=n[2*p+1],g=1/0;for(let T=0;T<u;T++){if(T===d||T===p)continue;const E=pA(y,b,w,v,n[2*T],n[2*T+1]);E<g&&(m=T,g=E)}let x=n[2*m],_=n[2*m+1];if(g===1/0){for(let S=0;S<u;S++)this._dists[S]=n[2*S]-n[0]||n[2*S+1]-n[1];Fi(this._ids,this._dists,0,u-1);const T=new Uint32Array(u);let E=0;for(let S=0,R=-1/0;S<u;S++){const C=this._ids[S],$=this._dists[C];$>R&&(T[E++]=C,R=$)}this.hull=T.subarray(0,E),this.triangles=new Uint32Array(0),this.halfedges=new Uint32Array(0);return}if(Wa(y,b,w,v,x,_)<0){const T=p,E=w,S=v;p=m,w=x,v=_,m=T,x=E,_=S}const M=mA(y,b,w,v,x,_);this._cx=M.x,this._cy=M.y;for(let T=0;T<u;T++)this._dists[T]=xl(n[2*T],n[2*T+1],M.x,M.y);Fi(this._ids,this._dists,0,u-1),this._hullStart=d;let A=3;r[d]=e[m]=p,r[p]=e[d]=m,r[m]=e[p]=d,i[d]=0,i[p]=1,i[m]=2,o.fill(-1),o[this._hashKey(y,b)]=d,o[this._hashKey(w,v)]=p,o[this._hashKey(x,_)]=m,this.trianglesLen=0,this._addTriangle(d,p,m,-1,-1,-1);for(let T=0,E,S;T<this._ids.length;T++){const R=this._ids[T],C=n[2*R],$=n[2*R+1];if(T>0&&Math.abs(C-E)<=Nm&&Math.abs($-S)<=Nm||(E=C,S=$,R===d||R===p||R===m))continue;let N=0;for(let P=0,D=this._hashKey(C,$);P<this._hashSize&&(N=o[(D+P)%this._hashSize],!(N!==-1&&N!==r[N]));P++);N=e[N];let k=N,I;for(;I=r[k],Wa(C,$,n[2*k],n[2*k+1],n[2*I],n[2*I+1])>=0;)if(k=I,k===N){k=-1;break}if(k===-1)continue;let L=this._addTriangle(k,R,r[k],-1,-1,i[k]);i[R]=this._legalize(L+2),i[k]=L,A++;let z=r[k];for(;I=r[z],Wa(C,$,n[2*z],n[2*z+1],n[2*I],n[2*I+1])<0;)L=this._addTriangle(z,R,I,i[R],-1,i[z]),i[R]=this._legalize(L+2),r[z]=z,A--,z=I;if(k===N)for(;I=e[k],Wa(C,$,n[2*I],n[2*I+1],n[2*k],n[2*k+1])<0;)L=this._addTriangle(I,R,k,-1,i[k],i[I]),this._legalize(L+2),i[I]=L,r[k]=k,A--,k=I;this._hullStart=e[R]=k,r[k]=e[z]=R,r[R]=z,o[this._hashKey(C,$)]=R,o[this._hashKey(n[2*k],n[2*k+1])]=k}this.hull=new Uint32Array(A);for(let T=0,E=this._hullStart;T<A;T++)this.hull[T]=E,E=r[E];this.triangles=this._triangles.subarray(0,this.trianglesLen),this.halfedges=this._halfedges.subarray(0,this.trianglesLen)}_hashKey(n,e){return Math.floor(dA(n-this._cx,e-this._cy)*this._hashSize)%this._hashSize}_legalize(n){const{_triangles:e,_halfedges:r,coords:i}=this;let o=0,u=0;for(;;){const a=r[n],c=n-n%3;if(u=c+(n+2)%3,a===-1){if(o===0)break;n=Ha[--o];continue}const f=a-a%3,s=c+(n+1)%3,h=f+(a+2)%3,l=e[u],d=e[n],p=e[s],m=e[h];if(gA(i[2*l],i[2*l+1],i[2*d],i[2*d+1],i[2*p],i[2*p+1],i[2*m],i[2*m+1])){e[n]=m,e[a]=l;const b=r[h];if(b===-1){let v=this._hullStart;do{if(this._hullTri[v]===h){this._hullTri[v]=n;break}v=this._hullPrev[v]}while(v!==this._hullStart)}this._link(n,b),this._link(a,r[u]),this._link(u,h);const w=f+(a+1)%3;o<Ha.length&&(Ha[o++]=w)}else{if(o===0)break;n=Ha[--o]}}return u}_link(n,e){this._halfedges[n]=e,e!==-1&&(this._halfedges[e]=n)}_addTriangle(n,e,r,i,o,u){const a=this.trianglesLen;return this._triangles[a]=n,this._triangles[a+1]=e,this._triangles[a+2]=r,this._link(a,i),this._link(a+1,o),this._link(a+2,u),this.trianglesLen+=3,a}}function dA(t,n){const e=t/(Math.abs(t)+Math.abs(n));return(n>0?3-e:1+e)/4}function xl(t,n,e,r){const i=t-e,o=n-r;return i*i+o*o}function gA(t,n,e,r,i,o,u,a){const c=t-u,f=n-a,s=e-u,h=r-a,l=i-u,d=o-a,p=c*c+f*f,m=s*s+h*h,y=l*l+d*d;return c*(h*y-m*d)-f*(s*y-m*l)+p*(s*d-h*l)<0}function pA(t,n,e,r,i,o){const u=e-t,a=r-n,c=i-t,f=o-n,s=u*u+a*a,h=c*c+f*f,l=.5/(u*f-a*c),d=(f*s-a*h)*l,p=(u*h-c*s)*l;return d*d+p*p}function mA(t,n,e,r,i,o){const u=e-t,a=r-n,c=i-t,f=o-n,s=u*u+a*a,h=c*c+f*f,l=.5/(u*f-a*c),d=t+(f*s-a*h)*l,p=n+(u*h-c*s)*l;return{x:d,y:p}}function Fi(t,n,e,r){if(r-e<=20)for(let i=e+1;i<=r;i++){const o=t[i],u=n[o];let a=i-1;for(;a>=e&&n[t[a]]>u;)t[a+1]=t[a--];t[a+1]=o}else{const i=e+r>>1;let o=e+1,u=r;Go(t,i,o),n[t[e]]>n[t[r]]&&Go(t,e,r),n[t[o]]>n[t[r]]&&Go(t,o,r),n[t[e]]>n[t[o]]&&Go(t,e,o);const a=t[o],c=n[a];for(;;){do o++;while(n[t[o]]<c);do u--;while(n[t[u]]>c);if(u<o)break;Go(t,o,u)}t[e+1]=t[u],t[u]=a,r-o+1>=u-e?(Fi(t,n,o,r),Fi(t,n,e,u-1)):(Fi(t,n,e,u-1),Fi(t,n,o,r))}}function Go(t,n,e){const r=t[n];t[n]=t[e],t[e]=r}function yA(t){return t[0]}function bA(t){return t[1]}const Cm=1e-6;class Hr{constructor(){this._x0=this._y0=this._x1=this._y1=null,this._=""}moveTo(n,e){this._+=`M${this._x0=this._x1=+n},${this._y0=this._y1=+e}`}closePath(){this._x1!==null&&(this._x1=this._x0,this._y1=this._y0,this._+="Z")}lineTo(n,e){this._+=`L${this._x1=+n},${this._y1=+e}`}arc(n,e,r){n=+n,e=+e,r=+r;const i=n+r,o=e;if(r<0)throw new Error("negative radius");this._x1===null?this._+=`M${i},${o}`:(Math.abs(this._x1-i)>Cm||Math.abs(this._y1-o)>Cm)&&(this._+="L"+i+","+o),r&&(this._+=`A${r},${r},0,1,1,${n-r},${e}A${r},${r},0,1,1,${this._x1=i},${this._y1=o}`)}rect(n,e,r,i){this._+=`M${this._x0=this._x1=+n},${this._y0=this._y1=+e}h${+r}v${+i}h${-r}Z`}value(){return this._||null}}class Th{constructor(){this._=[]}moveTo(n,e){this._.push([n,e])}closePath(){this._.push(this._[0].slice())}lineTo(n,e){this._.push([n,e])}value(){return this._.length?this._:null}}let Yw=class{constructor(n,[e,r,i,o]=[0,0,960,500]){if(!((i=+i)>=(e=+e))||!((o=+o)>=(r=+r)))throw new Error("invalid bounds");this.delaunay=n,this._circumcenters=new Float64Array(n.points.length*2),this.vectors=new Float64Array(n.points.length*2),this.xmax=i,this.xmin=e,this.ymax=o,this.ymin=r,this._init()}update(){return this.delaunay.update(),this._init(),this}_init(){const{delaunay:{points:n,hull:e,triangles:r},vectors:i}=this;let o,u;const a=this.circumcenters=this._circumcenters.subarray(0,r.length/3*2);for(let m=0,y=0,b=r.length,w,v;m<b;m+=3,y+=2){const g=r[m]*2,x=r[m+1]*2,_=r[m+2]*2,M=n[g],A=n[g+1],T=n[x],E=n[x+1],S=n[_],R=n[_+1],C=T-M,$=E-A,N=S-M,k=R-A,I=(C*k-$*N)*2;if(Math.abs(I)<1e-9){if(o===void 0){o=u=0;for(const z of e)o+=n[z*2],u+=n[z*2+1];o/=e.length,u/=e.length}const L=1e9*Math.sign((o-M)*k-(u-A)*N);w=(M+S)/2-L*k,v=(A+R)/2+L*N}else{const L=1/I,z=C*C+$*$,P=N*N+k*k;w=M+(k*z-$*P)*L,v=A+(C*P-N*z)*L}a[y]=w,a[y+1]=v}let c=e[e.length-1],f,s=c*4,h,l=n[2*c],d,p=n[2*c+1];i.fill(0);for(let m=0;m<e.length;++m)c=e[m],f=s,h=l,d=p,s=c*4,l=n[2*c],p=n[2*c+1],i[f+2]=i[s]=d-p,i[f+3]=i[s+1]=l-h}render(n){const e=n==null?n=new Hr:void 0,{delaunay:{halfedges:r,inedges:i,hull:o},circumcenters:u,vectors:a}=this;if(o.length<=1)return null;for(let s=0,h=r.length;s<h;++s){const l=r[s];if(l<s)continue;const d=Math.floor(s/3)*2,p=Math.floor(l/3)*2,m=u[d],y=u[d+1],b=u[p],w=u[p+1];this._renderSegment(m,y,b,w,n)}let c,f=o[o.length-1];for(let s=0;s<o.length;++s){c=f,f=o[s];const h=Math.floor(i[f]/3)*2,l=u[h],d=u[h+1],p=c*4,m=this._project(l,d,a[p+2],a[p+3]);m&&this._renderSegment(l,d,m[0],m[1],n)}return e&&e.value()}renderBounds(n){const e=n==null?n=new Hr:void 0;return n.rect(this.xmin,this.ymin,this.xmax-this.xmin,this.ymax-this.ymin),e&&e.value()}renderCell(n,e){const r=e==null?e=new Hr:void 0,i=this._clip(n);if(i===null||!i.length)return;e.moveTo(i[0],i[1]);let o=i.length;for(;i[0]===i[o-2]&&i[1]===i[o-1]&&o>1;)o-=2;for(let u=2;u<o;u+=2)(i[u]!==i[u-2]||i[u+1]!==i[u-1])&&e.lineTo(i[u],i[u+1]);return e.closePath(),r&&r.value()}*cellPolygons(){const{delaunay:{points:n}}=this;for(let e=0,r=n.length/2;e<r;++e){const i=this.cellPolygon(e);i&&(i.index=e,yield i)}}cellPolygon(n){const e=new Th;return this.renderCell(n,e),e.value()}_renderSegment(n,e,r,i,o){let u;const a=this._regioncode(n,e),c=this._regioncode(r,i);a===0&&c===0?(o.moveTo(n,e),o.lineTo(r,i)):(u=this._clipSegment(n,e,r,i,a,c))&&(o.moveTo(u[0],u[1]),o.lineTo(u[2],u[3]))}contains(n,e,r){return e=+e,e!==e||(r=+r,r!==r)?!1:this.delaunay._step(n,e,r)===n}*neighbors(n){const e=this._clip(n);if(e)for(const r of this.delaunay.neighbors(n)){const i=this._clip(r);if(i){t:for(let o=0,u=e.length;o<u;o+=2)for(let a=0,c=i.length;a<c;a+=2)if(e[o]===i[a]&&e[o+1]===i[a+1]&&e[(o+2)%u]===i[(a+c-2)%c]&&e[(o+3)%u]===i[(a+c-1)%c]){yield r;break t}}}}_cell(n){const{circumcenters:e,delaunay:{inedges:r,halfedges:i,triangles:o}}=this,u=r[n];if(u===-1)return null;const a=[];let c=u;do{const f=Math.floor(c/3);if(a.push(e[f*2],e[f*2+1]),c=c%3===2?c-2:c+1,o[c]!==n)break;c=i[c]}while(c!==u&&c!==-1);return a}_clip(n){if(n===0&&this.delaunay.hull.length===1)return[this.xmax,this.ymin,this.xmax,this.ymax,this.xmin,this.ymax,this.xmin,this.ymin];const e=this._cell(n);if(e===null)return null;const{vectors:r}=this,i=n*4;return this._simplify(r[i]||r[i+1]?this._clipInfinite(n,e,r[i],r[i+1],r[i+2],r[i+3]):this._clipFinite(n,e))}_clipFinite(n,e){const r=e.length;let i=null,o,u,a=e[r-2],c=e[r-1],f,s=this._regioncode(a,c),h,l=0;for(let d=0;d<r;d+=2)if(o=a,u=c,a=e[d],c=e[d+1],f=s,s=this._regioncode(a,c),f===0&&s===0)h=l,l=0,i?i.push(a,c):i=[a,c];else{let p,m,y,b,w;if(f===0){if((p=this._clipSegment(o,u,a,c,f,s))===null)continue;[m,y,b,w]=p}else{if((p=this._clipSegment(a,c,o,u,s,f))===null)continue;[b,w,m,y]=p,h=l,l=this._edgecode(m,y),h&&l&&this._edge(n,h,l,i,i.length),i?i.push(m,y):i=[m,y]}h=l,l=this._edgecode(b,w),h&&l&&this._edge(n,h,l,i,i.length),i?i.push(b,w):i=[b,w]}if(i)h=l,l=this._edgecode(i[0],i[1]),h&&l&&this._edge(n,h,l,i,i.length);else if(this.contains(n,(this.xmin+this.xmax)/2,(this.ymin+this.ymax)/2))return[this.xmax,this.ymin,this.xmax,this.ymax,this.xmin,this.ymax,this.xmin,this.ymin];return i}_clipSegment(n,e,r,i,o,u){const a=o<u;for(a&&([n,e,r,i,o,u]=[r,i,n,e,u,o]);;){if(o===0&&u===0)return a?[r,i,n,e]:[n,e,r,i];if(o&u)return null;let c,f,s=o||u;s&8?(c=n+(r-n)*(this.ymax-e)/(i-e),f=this.ymax):s&4?(c=n+(r-n)*(this.ymin-e)/(i-e),f=this.ymin):s&2?(f=e+(i-e)*(this.xmax-n)/(r-n),c=this.xmax):(f=e+(i-e)*(this.xmin-n)/(r-n),c=this.xmin),o?(n=c,e=f,o=this._regioncode(n,e)):(r=c,i=f,u=this._regioncode(r,i))}}_clipInfinite(n,e,r,i,o,u){let a=Array.from(e),c;if((c=this._project(a[0],a[1],r,i))&&a.unshift(c[0],c[1]),(c=this._project(a[a.length-2],a[a.length-1],o,u))&&a.push(c[0],c[1]),a=this._clipFinite(n,a))for(let f=0,s=a.length,h,l=this._edgecode(a[s-2],a[s-1]);f<s;f+=2)h=l,l=this._edgecode(a[f],a[f+1]),h&&l&&(f=this._edge(n,h,l,a,f),s=a.length);else this.contains(n,(this.xmin+this.xmax)/2,(this.ymin+this.ymax)/2)&&(a=[this.xmin,this.ymin,this.xmax,this.ymin,this.xmax,this.ymax,this.xmin,this.ymax]);return a}_edge(n,e,r,i,o){for(;e!==r;){let u,a;switch(e){case 5:e=4;continue;case 4:e=6,u=this.xmax,a=this.ymin;break;case 6:e=2;continue;case 2:e=10,u=this.xmax,a=this.ymax;break;case 10:e=8;continue;case 8:e=9,u=this.xmin,a=this.ymax;break;case 9:e=1;continue;case 1:e=5,u=this.xmin,a=this.ymin;break}(i[o]!==u||i[o+1]!==a)&&this.contains(n,u,a)&&(i.splice(o,0,u,a),o+=2)}return o}_project(n,e,r,i){let o=1/0,u,a,c;if(i<0){if(e<=this.ymin)return null;(u=(this.ymin-e)/i)<o&&(c=this.ymin,a=n+(o=u)*r)}else if(i>0){if(e>=this.ymax)return null;(u=(this.ymax-e)/i)<o&&(c=this.ymax,a=n+(o=u)*r)}if(r>0){if(n>=this.xmax)return null;(u=(this.xmax-n)/r)<o&&(a=this.xmax,c=e+(o=u)*i)}else if(r<0){if(n<=this.xmin)return null;(u=(this.xmin-n)/r)<o&&(a=this.xmin,c=e+(o=u)*i)}return[a,c]}_edgecode(n,e){return(n===this.xmin?1:n===this.xmax?2:0)|(e===this.ymin?4:e===this.ymax?8:0)}_regioncode(n,e){return(n<this.xmin?1:n>this.xmax?2:0)|(e<this.ymin?4:e>this.ymax?8:0)}_simplify(n){if(n&&n.length>4){for(let e=0;e<n.length;e+=2){const r=(e+2)%n.length,i=(e+4)%n.length;(n[e]===n[r]&&n[r]===n[i]||n[e+1]===n[r+1]&&n[r+1]===n[i+1])&&(n.splice(r,2),e-=2)}n.length||(n=null)}return n}};const wA=2*Math.PI,Ci=Math.pow;function vA(t){return t[0]}function xA(t){return t[1]}function _A(t){const{triangles:n,coords:e}=t;for(let r=0;r<n.length;r+=3){const i=2*n[r],o=2*n[r+1],u=2*n[r+2];if((e[u]-e[i])*(e[o+1]-e[i+1])-(e[o]-e[i])*(e[u+1]-e[i+1])>1e-10)return!1}return!0}function MA(t,n,e){return[t+Math.sin(t+n)*e,n+Math.cos(t-n)*e]}class Ze{static from(n,e=vA,r=xA,i){return new Ze("length"in n?$A(n,e,r,i):Float64Array.from(AA(n,e,r,i)))}constructor(n){this._delaunator=new Xc(n),this.inedges=new Int32Array(n.length/2),this._hullIndex=new Int32Array(n.length/2),this.points=this._delaunator.coords,this._init()}update(){return this._delaunator.update(),this._init(),this}_init(){const n=this._delaunator,e=this.points;if(n.hull&&n.hull.length>2&&_A(n)){this.collinear=Int32Array.from({length:e.length/2},(l,d)=>d).sort((l,d)=>e[2*l]-e[2*d]||e[2*l+1]-e[2*d+1]);const c=this.collinear[0],f=this.collinear[this.collinear.length-1],s=[e[2*c],e[2*c+1],e[2*f],e[2*f+1]],h=1e-8*Math.hypot(s[3]-s[1],s[2]-s[0]);for(let l=0,d=e.length/2;l<d;++l){const p=MA(e[2*l],e[2*l+1],h);e[2*l]=p[0],e[2*l+1]=p[1]}this._delaunator=new Xc(e)}else delete this.collinear;const r=this.halfedges=this._delaunator.halfedges,i=this.hull=this._delaunator.hull,o=this.triangles=this._delaunator.triangles,u=this.inedges.fill(-1),a=this._hullIndex.fill(-1);for(let c=0,f=r.length;c<f;++c){const s=o[c%3===2?c-2:c+1];(r[c]===-1||u[s]===-1)&&(u[s]=c)}for(let c=0,f=i.length;c<f;++c)a[i[c]]=c;i.length<=2&&i.length>0&&(this.triangles=new Int32Array(3).fill(-1),this.halfedges=new Int32Array(3).fill(-1),this.triangles[0]=i[0],u[i[0]]=1,i.length===2&&(u[i[1]]=0,this.triangles[1]=i[1],this.triangles[2]=i[1]))}voronoi(n){return new Yw(this,n)}*neighbors(n){const{inedges:e,hull:r,_hullIndex:i,halfedges:o,triangles:u,collinear:a}=this;if(a){const h=a.indexOf(n);h>0&&(yield a[h-1]),h<a.length-1&&(yield a[h+1]);return}const c=e[n];if(c===-1)return;let f=c,s=-1;do{if(yield s=u[f],f=f%3===2?f-2:f+1,u[f]!==n)return;if(f=o[f],f===-1){const h=r[(i[n]+1)%r.length];h!==s&&(yield h);return}}while(f!==c)}find(n,e,r=0){if(n=+n,n!==n||(e=+e,e!==e))return-1;const i=r;let o;for(;(o=this._step(r,n,e))>=0&&o!==r&&o!==i;)r=o;return o}_step(n,e,r){const{inedges:i,hull:o,_hullIndex:u,halfedges:a,triangles:c,points:f}=this;if(i[n]===-1||!f.length)return(n+1)%(f.length>>1);let s=n,h=Ci(e-f[n*2],2)+Ci(r-f[n*2+1],2);const l=i[n];let d=l;do{let p=c[d];const m=Ci(e-f[p*2],2)+Ci(r-f[p*2+1],2);if(m<h&&(h=m,s=p),d=d%3===2?d-2:d+1,c[d]!==n)break;if(d=a[d],d===-1){if(d=o[(u[n]+1)%o.length],d!==p&&Ci(e-f[d*2],2)+Ci(r-f[d*2+1],2)<h)return d;break}}while(d!==l);return s}render(n){const e=n==null?n=new Hr:void 0,{points:r,halfedges:i,triangles:o}=this;for(let u=0,a=i.length;u<a;++u){const c=i[u];if(c<u)continue;const f=o[u]*2,s=o[c]*2;n.moveTo(r[f],r[f+1]),n.lineTo(r[s],r[s+1])}return this.renderHull(n),e&&e.value()}renderPoints(n,e){e===void 0&&(!n||typeof n.moveTo!="function")&&(e=n,n=null),e=e==null?2:+e;const r=n==null?n=new Hr:void 0,{points:i}=this;for(let o=0,u=i.length;o<u;o+=2){const a=i[o],c=i[o+1];n.moveTo(a+e,c),n.arc(a,c,e,0,wA)}return r&&r.value()}renderHull(n){const e=n==null?n=new Hr:void 0,{hull:r,points:i}=this,o=r[0]*2,u=r.length;n.moveTo(i[o],i[o+1]);for(let a=1;a<u;++a){const c=2*r[a];n.lineTo(i[c],i[c+1])}return n.closePath(),e&&e.value()}hullPolygon(){const n=new Th;return this.renderHull(n),n.value()}renderTriangle(n,e){const r=e==null?e=new Hr:void 0,{points:i,triangles:o}=this,u=o[n*=3]*2,a=o[n+1]*2,c=o[n+2]*2;return e.moveTo(i[u],i[u+1]),e.lineTo(i[a],i[a+1]),e.lineTo(i[c],i[c+1]),e.closePath(),r&&r.value()}*trianglePolygons(){const{triangles:n}=this;for(let e=0,r=n.length/3;e<r;++e)yield this.trianglePolygon(e)}trianglePolygon(n){const e=new Th;return this.renderTriangle(n,e),e.value()}}function $A(t,n,e,r){const i=t.length,o=new Float64Array(i*2);for(let u=0;u<i;++u){const a=t[u];o[u*2]=n.call(r,a,u,t),o[u*2+1]=e.call(r,a,u,t)}return o}function*AA(t,n,e,r){let i=0;for(const o of t)yield n.call(r,o,i,t),yield e.call(r,o,i,t),++i}var Rm={},_l={},Ml=34,Vo=10,$l=13;function Uw(t){return new Function("d","return {"+t.map(function(n,e){return JSON.stringify(n)+": d["+e+'] || ""'}).join(",")+"}")}function SA(t,n){var e=Uw(t);return function(r,i){return n(e(r),i,t)}}function Im(t){var n=Object.create(null),e=[];return t.forEach(function(r){for(var i in r)i in n||e.push(n[i]=i)}),e}function vn(t,n){var e=t+"",r=e.length;return r<n?new Array(n-r+1).join(0)+e:e}function TA(t){return t<0?"-"+vn(-t,6):t>9999?"+"+vn(t,6):vn(t,4)}function EA(t){var n=t.getUTCHours(),e=t.getUTCMinutes(),r=t.getUTCSeconds(),i=t.getUTCMilliseconds();return isNaN(t)?"Invalid Date":TA(t.getUTCFullYear())+"-"+vn(t.getUTCMonth()+1,2)+"-"+vn(t.getUTCDate(),2)+(i?"T"+vn(n,2)+":"+vn(e,2)+":"+vn(r,2)+"."+vn(i,3)+"Z":r?"T"+vn(n,2)+":"+vn(e,2)+":"+vn(r,2)+"Z":e||n?"T"+vn(n,2)+":"+vn(e,2)+"Z":"")}function is(t){var n=new RegExp('["'+t+`
\r]`),e=t.charCodeAt(0);function r(h,l){var d,p,m=i(h,function(y,b){if(d)return d(y,b-1);p=y,d=l?SA(y,l):Uw(y)});return m.columns=p||[],m}function i(h,l){var d=[],p=h.length,m=0,y=0,b,w=p<=0,v=!1;h.charCodeAt(p-1)===Vo&&--p,h.charCodeAt(p-1)===$l&&--p;function g(){if(w)return _l;if(v)return v=!1,Rm;var _,M=m,A;if(h.charCodeAt(M)===Ml){for(;m++<p&&h.charCodeAt(m)!==Ml||h.charCodeAt(++m)===Ml;);return(_=m)>=p?w=!0:(A=h.charCodeAt(m++))===Vo?v=!0:A===$l&&(v=!0,h.charCodeAt(m)===Vo&&++m),h.slice(M+1,_-1).replace(/""/g,'"')}for(;m<p;){if((A=h.charCodeAt(_=m++))===Vo)v=!0;else if(A===$l)v=!0,h.charCodeAt(m)===Vo&&++m;else if(A!==e)continue;return h.slice(M,_)}return w=!0,h.slice(M,p)}for(;(b=g())!==_l;){for(var x=[];b!==Rm&&b!==_l;)x.push(b),b=g();l&&(x=l(x,y++))==null||d.push(x)}return d}function o(h,l){return h.map(function(d){return l.map(function(p){return s(d[p])}).join(t)})}function u(h,l){return l==null&&(l=Im(h)),[l.map(s).join(t)].concat(o(h,l)).join(`
`)}function a(h,l){return l==null&&(l=Im(h)),o(h,l).join(`
`)}function c(h){return h.map(f).join(`
`)}function f(h){return h.map(s).join(t)}function s(h){return h==null?"":h instanceof Date?EA(h):n.test(h+="")?'"'+h.replace(/"/g,'""')+'"':h}return{parse:r,parseRows:i,format:u,formatBody:a,formatRows:c,formatRow:f,formatValue:s}}var di=is(","),Xw=di.parse,kA=di.parseRows,NA=di.format,CA=di.formatBody,RA=di.formatRows,IA=di.formatRow,zA=di.formatValue,gi=is("	"),Ww=gi.parse,LA=gi.parseRows,PA=gi.format,DA=gi.formatBody,OA=gi.formatRows,FA=gi.formatRow,BA=gi.formatValue;function qA(t){for(var n in t){var e=t[n].trim(),r,i;if(!e)e=null;else if(e==="true")e=!0;else if(e==="false")e=!1;else if(e==="NaN")e=NaN;else if(!isNaN(r=+e))e=r;else if(i=e.match(/^([-+]\d{2})?\d{4}(-\d{2}(-\d{2})?)?(T\d{2}:\d{2}(:\d{2}(\.\d{3})?)?(Z|[-+]\d{2}:\d{2})?)?$/))YA&&i[4]&&!i[7]&&(e=e.replace(/-/g,"/").replace(/T/," ")),e=new Date(e);else continue;t[n]=e}return t}const YA=new Date("2019-01-01T00:00").getHours()||new Date("2019-07-01T00:00").getHours();function UA(t){if(!t.ok)throw new Error(t.status+" "+t.statusText);return t.blob()}function XA(t,n){return fetch(t,n).then(UA)}function WA(t){if(!t.ok)throw new Error(t.status+" "+t.statusText);return t.arrayBuffer()}function HA(t,n){return fetch(t,n).then(WA)}function GA(t){if(!t.ok)throw new Error(t.status+" "+t.statusText);return t.text()}function os(t,n){return fetch(t,n).then(GA)}function Hw(t){return function(n,e,r){return arguments.length===2&&typeof e=="function"&&(r=e,e=void 0),os(n,e).then(function(i){return t(i,r)})}}function VA(t,n,e,r){arguments.length===3&&typeof e=="function"&&(r=e,e=void 0);var i=is(t);return os(n,e).then(function(o){return i.parse(o,r)})}var jA=Hw(Xw),ZA=Hw(Ww);function KA(t,n){return new Promise(function(e,r){var i=new Image;for(var o in n)i[o]=n[o];i.onerror=r,i.onload=function(){e(i)},i.src=t})}function QA(t){if(!t.ok)throw new Error(t.status+" "+t.statusText);if(!(t.status===204||t.status===205))return t.json()}function JA(t,n){return fetch(t,n).then(QA)}function Jd(t){return(n,e)=>os(n,e).then(r=>new DOMParser().parseFromString(r,t))}const tS=Jd("application/xml");var nS=Jd("text/html"),eS=Jd("image/svg+xml");function rS(t,n){var e,r=1;t==null&&(t=0),n==null&&(n=0);function i(){var o,u=e.length,a,c=0,f=0;for(o=0;o<u;++o)a=e[o],c+=a.x,f+=a.y;for(c=(c/u-t)*r,f=(f/u-n)*r,o=0;o<u;++o)a=e[o],a.x-=c,a.y-=f}return i.initialize=function(o){e=o},i.x=function(o){return arguments.length?(t=+o,i):t},i.y=function(o){return arguments.length?(n=+o,i):n},i.strength=function(o){return arguments.length?(r=+o,i):r},i}function iS(t){const n=+this._x.call(null,t),e=+this._y.call(null,t);return Gw(this.cover(n,e),n,e,t)}function Gw(t,n,e,r){if(isNaN(n)||isNaN(e))return t;var i,o=t._root,u={data:r},a=t._x0,c=t._y0,f=t._x1,s=t._y1,h,l,d,p,m,y,b,w;if(!o)return t._root=u,t;for(;o.length;)if((m=n>=(h=(a+f)/2))?a=h:f=h,(y=e>=(l=(c+s)/2))?c=l:s=l,i=o,!(o=o[b=y<<1|m]))return i[b]=u,t;if(d=+t._x.call(null,o.data),p=+t._y.call(null,o.data),n===d&&e===p)return u.next=o,i?i[b]=u:t._root=u,t;do i=i?i[b]=new Array(4):t._root=new Array(4),(m=n>=(h=(a+f)/2))?a=h:f=h,(y=e>=(l=(c+s)/2))?c=l:s=l;while((b=y<<1|m)===(w=(p>=l)<<1|d>=h));return i[w]=o,i[b]=u,t}function oS(t){var n,e,r=t.length,i,o,u=new Array(r),a=new Array(r),c=1/0,f=1/0,s=-1/0,h=-1/0;for(e=0;e<r;++e)isNaN(i=+this._x.call(null,n=t[e]))||isNaN(o=+this._y.call(null,n))||(u[e]=i,a[e]=o,i<c&&(c=i),i>s&&(s=i),o<f&&(f=o),o>h&&(h=o));if(c>s||f>h)return this;for(this.cover(c,f).cover(s,h),e=0;e<r;++e)Gw(this,u[e],a[e],t[e]);return this}function uS(t,n){if(isNaN(t=+t)||isNaN(n=+n))return this;var e=this._x0,r=this._y0,i=this._x1,o=this._y1;if(isNaN(e))i=(e=Math.floor(t))+1,o=(r=Math.floor(n))+1;else{for(var u=i-e||1,a=this._root,c,f;e>t||t>=i||r>n||n>=o;)switch(f=(n<r)<<1|t<e,c=new Array(4),c[f]=a,a=c,u*=2,f){case 0:i=e+u,o=r+u;break;case 1:e=i-u,o=r+u;break;case 2:i=e+u,r=o-u;break;case 3:e=i-u,r=o-u;break}this._root&&this._root.length&&(this._root=a)}return this._x0=e,this._y0=r,this._x1=i,this._y1=o,this}function aS(){var t=[];return this.visit(function(n){if(!n.length)do t.push(n.data);while(n=n.next)}),t}function cS(t){return arguments.length?this.cover(+t[0][0],+t[0][1]).cover(+t[1][0],+t[1][1]):isNaN(this._x0)?void 0:[[this._x0,this._y0],[this._x1,this._y1]]}function ln(t,n,e,r,i){this.node=t,this.x0=n,this.y0=e,this.x1=r,this.y1=i}function fS(t,n,e){var r,i=this._x0,o=this._y0,u,a,c,f,s=this._x1,h=this._y1,l=[],d=this._root,p,m;for(d&&l.push(new ln(d,i,o,s,h)),e==null?e=1/0:(i=t-e,o=n-e,s=t+e,h=n+e,e*=e);p=l.pop();)if(!(!(d=p.node)||(u=p.x0)>s||(a=p.y0)>h||(c=p.x1)<i||(f=p.y1)<o))if(d.length){var y=(u+c)/2,b=(a+f)/2;l.push(new ln(d[3],y,b,c,f),new ln(d[2],u,b,y,f),new ln(d[1],y,a,c,b),new ln(d[0],u,a,y,b)),(m=(n>=b)<<1|t>=y)&&(p=l[l.length-1],l[l.length-1]=l[l.length-1-m],l[l.length-1-m]=p)}else{var w=t-+this._x.call(null,d.data),v=n-+this._y.call(null,d.data),g=w*w+v*v;if(g<e){var x=Math.sqrt(e=g);i=t-x,o=n-x,s=t+x,h=n+x,r=d.data}}return r}function sS(t){if(isNaN(s=+this._x.call(null,t))||isNaN(h=+this._y.call(null,t)))return this;var n,e=this._root,r,i,o,u=this._x0,a=this._y0,c=this._x1,f=this._y1,s,h,l,d,p,m,y,b;if(!e)return this;if(e.length)for(;;){if((p=s>=(l=(u+c)/2))?u=l:c=l,(m=h>=(d=(a+f)/2))?a=d:f=d,n=e,!(e=e[y=m<<1|p]))return this;if(!e.length)break;(n[y+1&3]||n[y+2&3]||n[y+3&3])&&(r=n,b=y)}for(;e.data!==t;)if(i=e,!(e=e.next))return this;return(o=e.next)&&delete e.next,i?(o?i.next=o:delete i.next,this):n?(o?n[y]=o:delete n[y],(e=n[0]||n[1]||n[2]||n[3])&&e===(n[3]||n[2]||n[1]||n[0])&&!e.length&&(r?r[b]=e:this._root=e),this):(this._root=o,this)}function lS(t){for(var n=0,e=t.length;n<e;++n)this.remove(t[n]);return this}function hS(){return this._root}function dS(){var t=0;return this.visit(function(n){if(!n.length)do++t;while(n=n.next)}),t}function gS(t){var n=[],e,r=this._root,i,o,u,a,c;for(r&&n.push(new ln(r,this._x0,this._y0,this._x1,this._y1));e=n.pop();)if(!t(r=e.node,o=e.x0,u=e.y0,a=e.x1,c=e.y1)&&r.length){var f=(o+a)/2,s=(u+c)/2;(i=r[3])&&n.push(new ln(i,f,s,a,c)),(i=r[2])&&n.push(new ln(i,o,s,f,c)),(i=r[1])&&n.push(new ln(i,f,u,a,s)),(i=r[0])&&n.push(new ln(i,o,u,f,s))}return this}function pS(t){var n=[],e=[],r;for(this._root&&n.push(new ln(this._root,this._x0,this._y0,this._x1,this._y1));r=n.pop();){var i=r.node;if(i.length){var o,u=r.x0,a=r.y0,c=r.x1,f=r.y1,s=(u+c)/2,h=(a+f)/2;(o=i[0])&&n.push(new ln(o,u,a,s,h)),(o=i[1])&&n.push(new ln(o,s,a,c,h)),(o=i[2])&&n.push(new ln(o,u,h,s,f)),(o=i[3])&&n.push(new ln(o,s,h,c,f))}e.push(r)}for(;r=e.pop();)t(r.node,r.x0,r.y0,r.x1,r.y1);return this}function mS(t){return t[0]}function yS(t){return arguments.length?(this._x=t,this):this._x}function bS(t){return t[1]}function wS(t){return arguments.length?(this._y=t,this):this._y}function us(t,n,e){var r=new t0(n??mS,e??bS,NaN,NaN,NaN,NaN);return t==null?r:r.addAll(t)}function t0(t,n,e,r,i,o){this._x=t,this._y=n,this._x0=e,this._y0=r,this._x1=i,this._y1=o,this._root=void 0}function zm(t){for(var n={data:t.data},e=n;t=t.next;)e=e.next={data:t.data};return n}var wn=us.prototype=t0.prototype;wn.copy=function(){var t=new t0(this._x,this._y,this._x0,this._y0,this._x1,this._y1),n=this._root,e,r;if(!n)return t;if(!n.length)return t._root=zm(n),t;for(e=[{source:n,target:t._root=new Array(4)}];n=e.pop();)for(var i=0;i<4;++i)(r=n.source[i])&&(r.length?e.push({source:r,target:n.target[i]=new Array(4)}):n.target[i]=zm(r));return t};wn.add=iS;wn.addAll=oS;wn.cover=uS;wn.data=aS;wn.extent=cS;wn.find=fS;wn.remove=sS;wn.removeAll=lS;wn.root=hS;wn.size=dS;wn.visit=gS;wn.visitAfter=pS;wn.x=yS;wn.y=wS;function Ft(t){return function(){return t}}function cr(t){return(t()-.5)*1e-6}function vS(t){return t.x+t.vx}function xS(t){return t.y+t.vy}function _S(t){var n,e,r,i=1,o=1;typeof t!="function"&&(t=Ft(t==null?1:+t));function u(){for(var f,s=n.length,h,l,d,p,m,y,b=0;b<o;++b)for(h=us(n,vS,xS).visitAfter(a),f=0;f<s;++f)l=n[f],m=e[l.index],y=m*m,d=l.x+l.vx,p=l.y+l.vy,h.visit(w);function w(v,g,x,_,M){var A=v.data,T=v.r,E=m+T;if(A){if(A.index>l.index){var S=d-A.x-A.vx,R=p-A.y-A.vy,C=S*S+R*R;C<E*E&&(S===0&&(S=cr(r),C+=S*S),R===0&&(R=cr(r),C+=R*R),C=(E-(C=Math.sqrt(C)))/C*i,l.vx+=(S*=C)*(E=(T*=T)/(y+T)),l.vy+=(R*=C)*E,A.vx-=S*(E=1-E),A.vy-=R*E)}return}return g>d+E||_<d-E||x>p+E||M<p-E}}function a(f){if(f.data)return f.r=e[f.data.index];for(var s=f.r=0;s<4;++s)f[s]&&f[s].r>f.r&&(f.r=f[s].r)}function c(){if(n){var f,s=n.length,h;for(e=new Array(s),f=0;f<s;++f)h=n[f],e[h.index]=+t(h,f,n)}}return u.initialize=function(f,s){n=f,r=s,c()},u.iterations=function(f){return arguments.length?(o=+f,u):o},u.strength=function(f){return arguments.length?(i=+f,u):i},u.radius=function(f){return arguments.length?(t=typeof f=="function"?f:Ft(+f),c(),u):t},u}function MS(t){return t.index}function Lm(t,n){var e=t.get(n);if(!e)throw new Error("node not found: "+n);return e}function $S(t){var n=MS,e=h,r,i=Ft(30),o,u,a,c,f,s=1;t==null&&(t=[]);function h(y){return 1/Math.min(a[y.source.index],a[y.target.index])}function l(y){for(var b=0,w=t.length;b<s;++b)for(var v=0,g,x,_,M,A,T,E;v<w;++v)g=t[v],x=g.source,_=g.target,M=_.x+_.vx-x.x-x.vx||cr(f),A=_.y+_.vy-x.y-x.vy||cr(f),T=Math.sqrt(M*M+A*A),T=(T-o[v])/T*y*r[v],M*=T,A*=T,_.vx-=M*(E=c[v]),_.vy-=A*E,x.vx+=M*(E=1-E),x.vy+=A*E}function d(){if(u){var y,b=u.length,w=t.length,v=new Map(u.map((x,_)=>[n(x,_,u),x])),g;for(y=0,a=new Array(b);y<w;++y)g=t[y],g.index=y,typeof g.source!="object"&&(g.source=Lm(v,g.source)),typeof g.target!="object"&&(g.target=Lm(v,g.target)),a[g.source.index]=(a[g.source.index]||0)+1,a[g.target.index]=(a[g.target.index]||0)+1;for(y=0,c=new Array(w);y<w;++y)g=t[y],c[y]=a[g.source.index]/(a[g.source.index]+a[g.target.index]);r=new Array(w),p(),o=new Array(w),m()}}function p(){if(u)for(var y=0,b=t.length;y<b;++y)r[y]=+e(t[y],y,t)}function m(){if(u)for(var y=0,b=t.length;y<b;++y)o[y]=+i(t[y],y,t)}return l.initialize=function(y,b){u=y,f=b,d()},l.links=function(y){return arguments.length?(t=y,d(),l):t},l.id=function(y){return arguments.length?(n=y,l):n},l.iterations=function(y){return arguments.length?(s=+y,l):s},l.strength=function(y){return arguments.length?(e=typeof y=="function"?y:Ft(+y),p(),l):e},l.distance=function(y){return arguments.length?(i=typeof y=="function"?y:Ft(+y),m(),l):i},l}const AS=1664525,SS=1013904223,Pm=4294967296;function TS(){let t=1;return()=>(t=(AS*t+SS)%Pm)/Pm}function ES(t){return t.x}function kS(t){return t.y}var NS=10,CS=Math.PI*(3-Math.sqrt(5));function RS(t){var n,e=1,r=.001,i=1-Math.pow(r,1/300),o=0,u=.6,a=new Map,c=es(h),f=li("tick","end"),s=TS();t==null&&(t=[]);function h(){l(),f.call("tick",n),e<r&&(c.stop(),f.call("end",n))}function l(m){var y,b=t.length,w;m===void 0&&(m=1);for(var v=0;v<m;++v)for(e+=(o-e)*i,a.forEach(function(g){g(e)}),y=0;y<b;++y)w=t[y],w.fx==null?w.x+=w.vx*=u:(w.x=w.fx,w.vx=0),w.fy==null?w.y+=w.vy*=u:(w.y=w.fy,w.vy=0);return n}function d(){for(var m=0,y=t.length,b;m<y;++m){if(b=t[m],b.index=m,b.fx!=null&&(b.x=b.fx),b.fy!=null&&(b.y=b.fy),isNaN(b.x)||isNaN(b.y)){var w=NS*Math.sqrt(.5+m),v=m*CS;b.x=w*Math.cos(v),b.y=w*Math.sin(v)}(isNaN(b.vx)||isNaN(b.vy))&&(b.vx=b.vy=0)}}function p(m){return m.initialize&&m.initialize(t,s),m}return d(),n={tick:l,restart:function(){return c.restart(h),n},stop:function(){return c.stop(),n},nodes:function(m){return arguments.length?(t=m,d(),a.forEach(p),n):t},alpha:function(m){return arguments.length?(e=+m,n):e},alphaMin:function(m){return arguments.length?(r=+m,n):r},alphaDecay:function(m){return arguments.length?(i=+m,n):+i},alphaTarget:function(m){return arguments.length?(o=+m,n):o},velocityDecay:function(m){return arguments.length?(u=1-m,n):1-u},randomSource:function(m){return arguments.length?(s=m,a.forEach(p),n):s},force:function(m,y){return arguments.length>1?(y==null?a.delete(m):a.set(m,p(y)),n):a.get(m)},find:function(m,y,b){var w=0,v=t.length,g,x,_,M,A;for(b==null?b=1/0:b*=b,w=0;w<v;++w)M=t[w],g=m-M.x,x=y-M.y,_=g*g+x*x,_<b&&(A=M,b=_);return A},on:function(m,y){return arguments.length>1?(f.on(m,y),n):f.on(m)}}}function IS(){var t,n,e,r,i=Ft(-30),o,u=1,a=1/0,c=.81;function f(d){var p,m=t.length,y=us(t,ES,kS).visitAfter(h);for(r=d,p=0;p<m;++p)n=t[p],y.visit(l)}function s(){if(t){var d,p=t.length,m;for(o=new Array(p),d=0;d<p;++d)m=t[d],o[m.index]=+i(m,d,t)}}function h(d){var p=0,m,y,b=0,w,v,g;if(d.length){for(w=v=g=0;g<4;++g)(m=d[g])&&(y=Math.abs(m.value))&&(p+=m.value,b+=y,w+=y*m.x,v+=y*m.y);d.x=w/b,d.y=v/b}else{m=d,m.x=m.data.x,m.y=m.data.y;do p+=o[m.data.index];while(m=m.next)}d.value=p}function l(d,p,m,y){if(!d.value)return!0;var b=d.x-n.x,w=d.y-n.y,v=y-p,g=b*b+w*w;if(v*v/c<g)return g<a&&(b===0&&(b=cr(e),g+=b*b),w===0&&(w=cr(e),g+=w*w),g<u&&(g=Math.sqrt(u*g)),n.vx+=b*d.value*r/g,n.vy+=w*d.value*r/g),!0;if(d.length||g>=a)return;(d.data!==n||d.next)&&(b===0&&(b=cr(e),g+=b*b),w===0&&(w=cr(e),g+=w*w),g<u&&(g=Math.sqrt(u*g)));do d.data!==n&&(v=o[d.data.index]*r/g,n.vx+=b*v,n.vy+=w*v);while(d=d.next)}return f.initialize=function(d,p){t=d,e=p,s()},f.strength=function(d){return arguments.length?(i=typeof d=="function"?d:Ft(+d),s(),f):i},f.distanceMin=function(d){return arguments.length?(u=d*d,f):Math.sqrt(u)},f.distanceMax=function(d){return arguments.length?(a=d*d,f):Math.sqrt(a)},f.theta=function(d){return arguments.length?(c=d*d,f):Math.sqrt(c)},f}function zS(t,n,e){var r,i=Ft(.1),o,u;typeof t!="function"&&(t=Ft(+t)),n==null&&(n=0),e==null&&(e=0);function a(f){for(var s=0,h=r.length;s<h;++s){var l=r[s],d=l.x-n||1e-6,p=l.y-e||1e-6,m=Math.sqrt(d*d+p*p),y=(u[s]-m)*o[s]*f/m;l.vx+=d*y,l.vy+=p*y}}function c(){if(r){var f,s=r.length;for(o=new Array(s),u=new Array(s),f=0;f<s;++f)u[f]=+t(r[f],f,r),o[f]=isNaN(u[f])?0:+i(r[f],f,r)}}return a.initialize=function(f){r=f,c()},a.strength=function(f){return arguments.length?(i=typeof f=="function"?f:Ft(+f),c(),a):i},a.radius=function(f){return arguments.length?(t=typeof f=="function"?f:Ft(+f),c(),a):t},a.x=function(f){return arguments.length?(n=+f,a):n},a.y=function(f){return arguments.length?(e=+f,a):e},a}function LS(t){var n=Ft(.1),e,r,i;typeof t!="function"&&(t=Ft(t==null?0:+t));function o(a){for(var c=0,f=e.length,s;c<f;++c)s=e[c],s.vx+=(i[c]-s.x)*r[c]*a}function u(){if(e){var a,c=e.length;for(r=new Array(c),i=new Array(c),a=0;a<c;++a)r[a]=isNaN(i[a]=+t(e[a],a,e))?0:+n(e[a],a,e)}}return o.initialize=function(a){e=a,u()},o.strength=function(a){return arguments.length?(n=typeof a=="function"?a:Ft(+a),u(),o):n},o.x=function(a){return arguments.length?(t=typeof a=="function"?a:Ft(+a),u(),o):t},o}function PS(t){var n=Ft(.1),e,r,i;typeof t!="function"&&(t=Ft(t==null?0:+t));function o(a){for(var c=0,f=e.length,s;c<f;++c)s=e[c],s.vy+=(i[c]-s.y)*r[c]*a}function u(){if(e){var a,c=e.length;for(r=new Array(c),i=new Array(c),a=0;a<c;++a)r[a]=isNaN(i[a]=+t(e[a],a,e))?0:+n(e[a],a,e)}}return o.initialize=function(a){e=a,u()},o.strength=function(a){return arguments.length?(n=typeof a=="function"?a:Ft(+a),u(),o):n},o.y=function(a){return arguments.length?(t=typeof a=="function"?a:Ft(+a),u(),o):t},o}function DS(t){return Math.abs(t=Math.round(t))>=1e21?t.toLocaleString("en").replace(/,/g,""):t.toString(10)}function Wc(t,n){if((e=(t=n?t.toExponential(n-1):t.toExponential()).indexOf("e"))<0)return null;var e,r=t.slice(0,e);return[r.length>1?r[0]+r.slice(2):r,+t.slice(e+1)]}function io(t){return t=Wc(Math.abs(t)),t?t[1]:NaN}function OS(t,n){return function(e,r){for(var i=e.length,o=[],u=0,a=t[0],c=0;i>0&&a>0&&(c+a+1>r&&(a=Math.max(1,r-c)),o.push(e.substring(i-=a,i+a)),!((c+=a+1)>r));)a=t[u=(u+1)%t.length];return o.reverse().join(n)}}function FS(t){return function(n){return n.replace(/[0-9]/g,function(e){return t[+e]})}}var BS=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function oo(t){if(!(n=BS.exec(t)))throw new Error("invalid format: "+t);var n;return new as({fill:n[1],align:n[2],sign:n[3],symbol:n[4],zero:n[5],width:n[6],comma:n[7],precision:n[8]&&n[8].slice(1),trim:n[9],type:n[10]})}oo.prototype=as.prototype;function as(t){this.fill=t.fill===void 0?" ":t.fill+"",this.align=t.align===void 0?">":t.align+"",this.sign=t.sign===void 0?"-":t.sign+"",this.symbol=t.symbol===void 0?"":t.symbol+"",this.zero=!!t.zero,this.width=t.width===void 0?void 0:+t.width,this.comma=!!t.comma,this.precision=t.precision===void 0?void 0:+t.precision,this.trim=!!t.trim,this.type=t.type===void 0?"":t.type+""}as.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(this.width===void 0?"":Math.max(1,this.width|0))+(this.comma?",":"")+(this.precision===void 0?"":"."+Math.max(0,this.precision|0))+(this.trim?"~":"")+this.type};function qS(t){t:for(var n=t.length,e=1,r=-1,i;e<n;++e)switch(t[e]){case".":r=i=e;break;case"0":r===0&&(r=e),i=e;break;default:if(!+t[e])break t;r>0&&(r=0);break}return r>0?t.slice(0,r)+t.slice(i+1):t}var Vw;function YS(t,n){var e=Wc(t,n);if(!e)return t+"";var r=e[0],i=e[1],o=i-(Vw=Math.max(-8,Math.min(8,Math.floor(i/3)))*3)+1,u=r.length;return o===u?r:o>u?r+new Array(o-u+1).join("0"):o>0?r.slice(0,o)+"."+r.slice(o):"0."+new Array(1-o).join("0")+Wc(t,Math.max(0,n+o-1))[0]}function Dm(t,n){var e=Wc(t,n);if(!e)return t+"";var r=e[0],i=e[1];return i<0?"0."+new Array(-i).join("0")+r:r.length>i+1?r.slice(0,i+1)+"."+r.slice(i+1):r+new Array(i-r.length+2).join("0")}const Om={"%":(t,n)=>(t*100).toFixed(n),b:t=>Math.round(t).toString(2),c:t=>t+"",d:DS,e:(t,n)=>t.toExponential(n),f:(t,n)=>t.toFixed(n),g:(t,n)=>t.toPrecision(n),o:t=>Math.round(t).toString(8),p:(t,n)=>Dm(t*100,n),r:Dm,s:YS,X:t=>Math.round(t).toString(16).toUpperCase(),x:t=>Math.round(t).toString(16)};function Fm(t){return t}var Bm=Array.prototype.map,qm=["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"];function jw(t){var n=t.grouping===void 0||t.thousands===void 0?Fm:OS(Bm.call(t.grouping,Number),t.thousands+""),e=t.currency===void 0?"":t.currency[0]+"",r=t.currency===void 0?"":t.currency[1]+"",i=t.decimal===void 0?".":t.decimal+"",o=t.numerals===void 0?Fm:FS(Bm.call(t.numerals,String)),u=t.percent===void 0?"%":t.percent+"",a=t.minus===void 0?"−":t.minus+"",c=t.nan===void 0?"NaN":t.nan+"";function f(h){h=oo(h);var l=h.fill,d=h.align,p=h.sign,m=h.symbol,y=h.zero,b=h.width,w=h.comma,v=h.precision,g=h.trim,x=h.type;x==="n"?(w=!0,x="g"):Om[x]||(v===void 0&&(v=12),g=!0,x="g"),(y||l==="0"&&d==="=")&&(y=!0,l="0",d="=");var _=m==="$"?e:m==="#"&&/[boxX]/.test(x)?"0"+x.toLowerCase():"",M=m==="$"?r:/[%p]/.test(x)?u:"",A=Om[x],T=/[defgprs%]/.test(x);v=v===void 0?6:/[gprs]/.test(x)?Math.max(1,Math.min(21,v)):Math.max(0,Math.min(20,v));function E(S){var R=_,C=M,$,N,k;if(x==="c")C=A(S)+C,S="";else{S=+S;var I=S<0||1/S<0;if(S=isNaN(S)?c:A(Math.abs(S),v),g&&(S=qS(S)),I&&+S==0&&p!=="+"&&(I=!1),R=(I?p==="("?p:a:p==="-"||p==="("?"":p)+R,C=(x==="s"?qm[8+Vw/3]:"")+C+(I&&p==="("?")":""),T){for($=-1,N=S.length;++$<N;)if(k=S.charCodeAt($),48>k||k>57){C=(k===46?i+S.slice($+1):S.slice($))+C,S=S.slice(0,$);break}}}w&&!y&&(S=n(S,1/0));var L=R.length+S.length+C.length,z=L<b?new Array(b-L+1).join(l):"";switch(w&&y&&(S=n(z+S,z.length?b-C.length:1/0),z=""),d){case"<":S=R+S+C+z;break;case"=":S=R+z+S+C;break;case"^":S=z.slice(0,L=z.length>>1)+R+S+C+z.slice(L);break;default:S=z+R+S+C;break}return o(S)}return E.toString=function(){return h+""},E}function s(h,l){var d=f((h=oo(h),h.type="f",h)),p=Math.max(-8,Math.min(8,Math.floor(io(l)/3)))*3,m=Math.pow(10,-p),y=qm[8+p/3];return function(b){return d(m*b)+y}}return{format:f,formatPrefix:s}}var Ga,pi,n0;Zw({thousands:",",grouping:[3],currency:["$",""]});function Zw(t){return Ga=jw(t),pi=Ga.format,n0=Ga.formatPrefix,Ga}function Kw(t){return Math.max(0,-io(Math.abs(t)))}function Qw(t,n){return Math.max(0,Math.max(-8,Math.min(8,Math.floor(io(n)/3)))*3-io(Math.abs(t)))}function Jw(t,n){return t=Math.abs(t),n=Math.abs(n)-t,Math.max(0,io(n)-io(t))+1}var J=1e-6,Iu=1e-12,at=Math.PI,zt=at/2,Hc=at/4,gn=at*2,mt=180/at,tt=at/180,st=Math.abs,Eo=Math.atan,pn=Math.atan2,K=Math.cos,Va=Math.ceil,tv=Math.exp,Eh=Math.hypot,Gc=Math.log,Al=Math.pow,V=Math.sin,On=Math.sign||function(t){return t>0?1:t<0?-1:0},Wt=Math.sqrt,e0=Math.tan;function nv(t){return t>1?0:t<-1?at:Math.acos(t)}function mn(t){return t>1?zt:t<-1?-zt:Math.asin(t)}function Ym(t){return(t=V(t/2))*t}function kt(){}function Vc(t,n){t&&Xm.hasOwnProperty(t.type)&&Xm[t.type](t,n)}var Um={Feature:function(t,n){Vc(t.geometry,n)},FeatureCollection:function(t,n){for(var e=t.features,r=-1,i=e.length;++r<i;)Vc(e[r].geometry,n)}},Xm={Sphere:function(t,n){n.sphere()},Point:function(t,n){t=t.coordinates,n.point(t[0],t[1],t[2])},MultiPoint:function(t,n){for(var e=t.coordinates,r=-1,i=e.length;++r<i;)t=e[r],n.point(t[0],t[1],t[2])},LineString:function(t,n){kh(t.coordinates,n,0)},MultiLineString:function(t,n){for(var e=t.coordinates,r=-1,i=e.length;++r<i;)kh(e[r],n,0)},Polygon:function(t,n){Wm(t.coordinates,n)},MultiPolygon:function(t,n){for(var e=t.coordinates,r=-1,i=e.length;++r<i;)Wm(e[r],n)},GeometryCollection:function(t,n){for(var e=t.geometries,r=-1,i=e.length;++r<i;)Vc(e[r],n)}};function kh(t,n,e){var r=-1,i=t.length-e,o;for(n.lineStart();++r<i;)o=t[r],n.point(o[0],o[1],o[2]);n.lineEnd()}function Wm(t,n){var e=-1,r=t.length;for(n.polygonStart();++e<r;)kh(t[e],n,1);n.polygonEnd()}function Pn(t,n){t&&Um.hasOwnProperty(t.type)?Um[t.type](t,n):Vc(t,n)}var jc=new Bt,Zc=new Bt,ev,rv,Nh,Ch,Rh,be={point:kt,lineStart:kt,lineEnd:kt,polygonStart:function(){jc=new Bt,be.lineStart=US,be.lineEnd=XS},polygonEnd:function(){var t=+jc;Zc.add(t<0?gn+t:t),this.lineStart=this.lineEnd=this.point=kt},sphere:function(){Zc.add(gn)}};function US(){be.point=WS}function XS(){iv(ev,rv)}function WS(t,n){be.point=iv,ev=t,rv=n,t*=tt,n*=tt,Nh=t,Ch=K(n=n/2+Hc),Rh=V(n)}function iv(t,n){t*=tt,n*=tt,n=n/2+Hc;var e=t-Nh,r=e>=0?1:-1,i=r*e,o=K(n),u=V(n),a=Rh*u,c=Ch*o+a*K(i),f=a*r*V(i);jc.add(pn(f,c)),Nh=t,Ch=o,Rh=u}function HS(t){return Zc=new Bt,Pn(t,be),Zc*2}function Kc(t){return[pn(t[1],t[0]),mn(t[2])]}function ai(t){var n=t[0],e=t[1],r=K(e);return[r*K(n),r*V(n),V(e)]}function ja(t,n){return t[0]*n[0]+t[1]*n[1]+t[2]*n[2]}function uo(t,n){return[t[1]*n[2]-t[2]*n[1],t[2]*n[0]-t[0]*n[2],t[0]*n[1]-t[1]*n[0]]}function Sl(t,n){t[0]+=n[0],t[1]+=n[1],t[2]+=n[2]}function Za(t,n){return[t[0]*n,t[1]*n,t[2]*n]}function Qc(t){var n=Wt(t[0]*t[0]+t[1]*t[1]+t[2]*t[2]);t[0]/=n,t[1]/=n,t[2]/=n}var Et,$n,It,En,Yr,ov,uv,Vi,mu,or,Ge,Pe={point:Ih,lineStart:Hm,lineEnd:Gm,polygonStart:function(){Pe.point=cv,Pe.lineStart=GS,Pe.lineEnd=VS,mu=new Bt,be.polygonStart()},polygonEnd:function(){be.polygonEnd(),Pe.point=Ih,Pe.lineStart=Hm,Pe.lineEnd=Gm,jc<0?(Et=-(It=180),$n=-(En=90)):mu>J?En=90:mu<-J&&($n=-90),Ge[0]=Et,Ge[1]=It},sphere:function(){Et=-(It=180),$n=-(En=90)}};function Ih(t,n){or.push(Ge=[Et=t,It=t]),n<$n&&($n=n),n>En&&(En=n)}function av(t,n){var e=ai([t*tt,n*tt]);if(Vi){var r=uo(Vi,e),i=[r[1],-r[0],0],o=uo(i,r);Qc(o),o=Kc(o);var u=t-Yr,a=u>0?1:-1,c=o[0]*mt*a,f,s=st(u)>180;s^(a*Yr<c&&c<a*t)?(f=o[1]*mt,f>En&&(En=f)):(c=(c+360)%360-180,s^(a*Yr<c&&c<a*t)?(f=-o[1]*mt,f<$n&&($n=f)):(n<$n&&($n=n),n>En&&(En=n))),s?t<Yr?Tn(Et,t)>Tn(Et,It)&&(It=t):Tn(t,It)>Tn(Et,It)&&(Et=t):It>=Et?(t<Et&&(Et=t),t>It&&(It=t)):t>Yr?Tn(Et,t)>Tn(Et,It)&&(It=t):Tn(t,It)>Tn(Et,It)&&(Et=t)}else or.push(Ge=[Et=t,It=t]);n<$n&&($n=n),n>En&&(En=n),Vi=e,Yr=t}function Hm(){Pe.point=av}function Gm(){Ge[0]=Et,Ge[1]=It,Pe.point=Ih,Vi=null}function cv(t,n){if(Vi){var e=t-Yr;mu.add(st(e)>180?e+(e>0?360:-360):e)}else ov=t,uv=n;be.point(t,n),av(t,n)}function GS(){be.lineStart()}function VS(){cv(ov,uv),be.lineEnd(),st(mu)>J&&(Et=-(It=180)),Ge[0]=Et,Ge[1]=It,Vi=null}function Tn(t,n){return(n-=t)<0?n+360:n}function jS(t,n){return t[0]-n[0]}function Vm(t,n){return t[0]<=t[1]?t[0]<=n&&n<=t[1]:n<t[0]||t[1]<n}function ZS(t){var n,e,r,i,o,u,a;if(En=It=-(Et=$n=1/0),or=[],Pn(t,Pe),e=or.length){for(or.sort(jS),n=1,r=or[0],o=[r];n<e;++n)i=or[n],Vm(r,i[0])||Vm(r,i[1])?(Tn(r[0],i[1])>Tn(r[0],r[1])&&(r[1]=i[1]),Tn(i[0],r[1])>Tn(r[0],r[1])&&(r[0]=i[0])):o.push(r=i);for(u=-1/0,e=o.length-1,n=0,r=o[e];n<=e;r=i,++n)i=o[n],(a=Tn(r[1],i[0]))>u&&(u=a,Et=i[0],It=r[1])}return or=Ge=null,Et===1/0||$n===1/0?[[NaN,NaN],[NaN,NaN]]:[[Et,$n],[It,En]]}var uu,Jc,tf,nf,ef,rf,of,uf,zh,Lh,Ph,fv,sv,cn,fn,sn,jn={sphere:kt,point:r0,lineStart:jm,lineEnd:Zm,polygonStart:function(){jn.lineStart=JS,jn.lineEnd=tT},polygonEnd:function(){jn.lineStart=jm,jn.lineEnd=Zm}};function r0(t,n){t*=tt,n*=tt;var e=K(n);ha(e*K(t),e*V(t),V(n))}function ha(t,n,e){++uu,tf+=(t-tf)/uu,nf+=(n-nf)/uu,ef+=(e-ef)/uu}function jm(){jn.point=KS}function KS(t,n){t*=tt,n*=tt;var e=K(n);cn=e*K(t),fn=e*V(t),sn=V(n),jn.point=QS,ha(cn,fn,sn)}function QS(t,n){t*=tt,n*=tt;var e=K(n),r=e*K(t),i=e*V(t),o=V(n),u=pn(Wt((u=fn*o-sn*i)*u+(u=sn*r-cn*o)*u+(u=cn*i-fn*r)*u),cn*r+fn*i+sn*o);Jc+=u,rf+=u*(cn+(cn=r)),of+=u*(fn+(fn=i)),uf+=u*(sn+(sn=o)),ha(cn,fn,sn)}function Zm(){jn.point=r0}function JS(){jn.point=nT}function tT(){lv(fv,sv),jn.point=r0}function nT(t,n){fv=t,sv=n,t*=tt,n*=tt,jn.point=lv;var e=K(n);cn=e*K(t),fn=e*V(t),sn=V(n),ha(cn,fn,sn)}function lv(t,n){t*=tt,n*=tt;var e=K(n),r=e*K(t),i=e*V(t),o=V(n),u=fn*o-sn*i,a=sn*r-cn*o,c=cn*i-fn*r,f=Eh(u,a,c),s=mn(f),h=f&&-s/f;zh.add(h*u),Lh.add(h*a),Ph.add(h*c),Jc+=s,rf+=s*(cn+(cn=r)),of+=s*(fn+(fn=i)),uf+=s*(sn+(sn=o)),ha(cn,fn,sn)}function hv(t){uu=Jc=tf=nf=ef=rf=of=uf=0,zh=new Bt,Lh=new Bt,Ph=new Bt,Pn(t,jn);var n=+zh,e=+Lh,r=+Ph,i=Eh(n,e,r);return i<Iu&&(n=rf,e=of,r=uf,Jc<J&&(n=tf,e=nf,r=ef),i=Eh(n,e,r),i<Iu)?[NaN,NaN]:[pn(e,n)*mt,mn(r/i)*mt]}function Ri(t){return function(){return t}}function Dh(t,n){function e(r,i){return r=t(r,i),n(r[0],r[1])}return t.invert&&n.invert&&(e.invert=function(r,i){return r=n.invert(r,i),r&&t.invert(r[0],r[1])}),e}function Oh(t,n){return st(t)>at&&(t-=Math.round(t/gn)*gn),[t,n]}Oh.invert=Oh;function i0(t,n,e){return(t%=gn)?n||e?Dh(Qm(t),Jm(n,e)):Qm(t):n||e?Jm(n,e):Oh}function Km(t){return function(n,e){return n+=t,st(n)>at&&(n-=Math.round(n/gn)*gn),[n,e]}}function Qm(t){var n=Km(t);return n.invert=Km(-t),n}function Jm(t,n){var e=K(t),r=V(t),i=K(n),o=V(n);function u(a,c){var f=K(c),s=K(a)*f,h=V(a)*f,l=V(c),d=l*e+s*r;return[pn(h*i-d*o,s*e-l*r),mn(d*i+h*o)]}return u.invert=function(a,c){var f=K(c),s=K(a)*f,h=V(a)*f,l=V(c),d=l*i-h*o;return[pn(h*i+l*o,s*e+d*r),mn(d*e-s*r)]},u}function dv(t){t=i0(t[0]*tt,t[1]*tt,t.length>2?t[2]*tt:0);function n(e){return e=t(e[0]*tt,e[1]*tt),e[0]*=mt,e[1]*=mt,e}return n.invert=function(e){return e=t.invert(e[0]*tt,e[1]*tt),e[0]*=mt,e[1]*=mt,e},n}function gv(t,n,e,r,i,o){if(e){var u=K(n),a=V(n),c=r*e;i==null?(i=n+r*gn,o=n-c/2):(i=ty(u,i),o=ty(u,o),(r>0?i<o:i>o)&&(i+=r*gn));for(var f,s=i;r>0?s>o:s<o;s-=c)f=Kc([u,-a*K(s),-a*V(s)]),t.point(f[0],f[1])}}function ty(t,n){n=ai(n),n[0]-=t,Qc(n);var e=nv(-n[1]);return((-n[2]<0?-e:e)+gn-J)%gn}function eT(){var t=Ri([0,0]),n=Ri(90),e=Ri(2),r,i,o={point:u};function u(c,f){r.push(c=i(c,f)),c[0]*=mt,c[1]*=mt}function a(){var c=t.apply(this,arguments),f=n.apply(this,arguments)*tt,s=e.apply(this,arguments)*tt;return r=[],i=i0(-c[0]*tt,-c[1]*tt,0).invert,gv(o,f,s,1),c={type:"Polygon",coordinates:[r]},r=i=null,c}return a.center=function(c){return arguments.length?(t=typeof c=="function"?c:Ri([+c[0],+c[1]]),a):t},a.radius=function(c){return arguments.length?(n=typeof c=="function"?c:Ri(+c),a):n},a.precision=function(c){return arguments.length?(e=typeof c=="function"?c:Ri(+c),a):e},a}function pv(){var t=[],n;return{point:function(e,r,i){n.push([e,r,i])},lineStart:function(){t.push(n=[])},lineEnd:kt,rejoin:function(){t.length>1&&t.push(t.pop().concat(t.shift()))},result:function(){var e=t;return t=[],n=null,e}}}function Sc(t,n){return st(t[0]-n[0])<J&&st(t[1]-n[1])<J}function Ka(t,n,e,r){this.x=t,this.z=n,this.o=e,this.e=r,this.v=!1,this.n=this.p=null}function mv(t,n,e,r,i){var o=[],u=[],a,c;if(t.forEach(function(p){if(!((m=p.length-1)<=0)){var m,y=p[0],b=p[m],w;if(Sc(y,b)){if(!y[2]&&!b[2]){for(i.lineStart(),a=0;a<m;++a)i.point((y=p[a])[0],y[1]);i.lineEnd();return}b[0]+=2*J}o.push(w=new Ka(y,p,null,!0)),u.push(w.o=new Ka(y,null,w,!1)),o.push(w=new Ka(b,p,null,!1)),u.push(w.o=new Ka(b,null,w,!0))}}),!!o.length){for(u.sort(n),ny(o),ny(u),a=0,c=u.length;a<c;++a)u[a].e=e=!e;for(var f=o[0],s,h;;){for(var l=f,d=!0;l.v;)if((l=l.n)===f)return;s=l.z,i.lineStart();do{if(l.v=l.o.v=!0,l.e){if(d)for(a=0,c=s.length;a<c;++a)i.point((h=s[a])[0],h[1]);else r(l.x,l.n.x,1,i);l=l.n}else{if(d)for(s=l.p.z,a=s.length-1;a>=0;--a)i.point((h=s[a])[0],h[1]);else r(l.x,l.p.x,-1,i);l=l.p}l=l.o,s=l.z,d=!d}while(!l.v);i.lineEnd()}}}function ny(t){if(n=t.length){for(var n,e=0,r=t[0],i;++e<n;)r.n=i=t[e],i.p=r,r=i;r.n=i=t[0],i.p=r}}function Tl(t){return st(t[0])<=at?t[0]:On(t[0])*((st(t[0])+at)%gn-at)}function yv(t,n){var e=Tl(n),r=n[1],i=V(r),o=[V(e),-K(e),0],u=0,a=0,c=new Bt;i===1?r=zt+J:i===-1&&(r=-zt-J);for(var f=0,s=t.length;f<s;++f)if(l=(h=t[f]).length)for(var h,l,d=h[l-1],p=Tl(d),m=d[1]/2+Hc,y=V(m),b=K(m),w=0;w<l;++w,p=g,y=_,b=M,d=v){var v=h[w],g=Tl(v),x=v[1]/2+Hc,_=V(x),M=K(x),A=g-p,T=A>=0?1:-1,E=T*A,S=E>at,R=y*_;if(c.add(pn(R*T*V(E),b*M+R*K(E))),u+=S?A+T*gn:A,S^p>=e^g>=e){var C=uo(ai(d),ai(v));Qc(C);var $=uo(o,C);Qc($);var N=(S^A>=0?-1:1)*mn($[2]);(r>N||r===N&&(C[0]||C[1]))&&(a+=S^A>=0?1:-1)}}return(u<-J||u<J&&c<-Iu)^a&1}function bv(t,n,e,r){return function(i){var o=n(i),u=pv(),a=n(u),c=!1,f,s,h,l={point:d,lineStart:m,lineEnd:y,polygonStart:function(){l.point=b,l.lineStart=w,l.lineEnd=v,s=[],f=[]},polygonEnd:function(){l.point=d,l.lineStart=m,l.lineEnd=y,s=Cd(s);var g=yv(f,r);s.length?(c||(i.polygonStart(),c=!0),mv(s,iT,g,e,i)):g&&(c||(i.polygonStart(),c=!0),i.lineStart(),e(null,null,1,i),i.lineEnd()),c&&(i.polygonEnd(),c=!1),s=f=null},sphere:function(){i.polygonStart(),i.lineStart(),e(null,null,1,i),i.lineEnd(),i.polygonEnd()}};function d(g,x){t(g,x)&&i.point(g,x)}function p(g,x){o.point(g,x)}function m(){l.point=p,o.lineStart()}function y(){l.point=d,o.lineEnd()}function b(g,x){h.push([g,x]),a.point(g,x)}function w(){a.lineStart(),h=[]}function v(){b(h[0][0],h[0][1]),a.lineEnd();var g=a.clean(),x=u.result(),_,M=x.length,A,T,E;if(h.pop(),f.push(h),h=null,!!M){if(g&1){if(T=x[0],(A=T.length-1)>0){for(c||(i.polygonStart(),c=!0),i.lineStart(),_=0;_<A;++_)i.point((E=T[_])[0],E[1]);i.lineEnd()}return}M>1&&g&2&&x.push(x.pop().concat(x.shift())),s.push(x.filter(rT))}}return l}}function rT(t){return t.length>1}function iT(t,n){return((t=t.x)[0]<0?t[1]-zt-J:zt-t[1])-((n=n.x)[0]<0?n[1]-zt-J:zt-n[1])}const Fh=bv(function(){return!0},oT,aT,[-at,-zt]);function oT(t){var n=NaN,e=NaN,r=NaN,i;return{lineStart:function(){t.lineStart(),i=1},point:function(o,u){var a=o>0?at:-at,c=st(o-n);st(c-at)<J?(t.point(n,e=(e+u)/2>0?zt:-zt),t.point(r,e),t.lineEnd(),t.lineStart(),t.point(a,e),t.point(o,e),i=0):r!==a&&c>=at&&(st(n-r)<J&&(n-=r*J),st(o-a)<J&&(o-=a*J),e=uT(n,e,o,u),t.point(r,e),t.lineEnd(),t.lineStart(),t.point(a,e),i=0),t.point(n=o,e=u),r=a},lineEnd:function(){t.lineEnd(),n=e=NaN},clean:function(){return 2-i}}}function uT(t,n,e,r){var i,o,u=V(t-e);return st(u)>J?Eo((V(n)*(o=K(r))*V(e)-V(r)*(i=K(n))*V(t))/(i*o*u)):(n+r)/2}function aT(t,n,e,r){var i;if(t==null)i=e*zt,r.point(-at,i),r.point(0,i),r.point(at,i),r.point(at,0),r.point(at,-i),r.point(0,-i),r.point(-at,-i),r.point(-at,0),r.point(-at,i);else if(st(t[0]-n[0])>J){var o=t[0]<n[0]?at:-at;i=e*o/2,r.point(-o,i),r.point(0,i),r.point(o,i)}else r.point(n[0],n[1])}function wv(t){var n=K(t),e=2*tt,r=n>0,i=st(n)>J;function o(s,h,l,d){gv(d,t,e,l,s,h)}function u(s,h){return K(s)*K(h)>n}function a(s){var h,l,d,p,m;return{lineStart:function(){p=d=!1,m=1},point:function(y,b){var w=[y,b],v,g=u(y,b),x=r?g?0:f(y,b):g?f(y+(y<0?at:-at),b):0;if(!h&&(p=d=g)&&s.lineStart(),g!==d&&(v=c(h,w),(!v||Sc(h,v)||Sc(w,v))&&(w[2]=1)),g!==d)m=0,g?(s.lineStart(),v=c(w,h),s.point(v[0],v[1])):(v=c(h,w),s.point(v[0],v[1],2),s.lineEnd()),h=v;else if(i&&h&&r^g){var _;!(x&l)&&(_=c(w,h,!0))&&(m=0,r?(s.lineStart(),s.point(_[0][0],_[0][1]),s.point(_[1][0],_[1][1]),s.lineEnd()):(s.point(_[1][0],_[1][1]),s.lineEnd(),s.lineStart(),s.point(_[0][0],_[0][1],3)))}g&&(!h||!Sc(h,w))&&s.point(w[0],w[1]),h=w,d=g,l=x},lineEnd:function(){d&&s.lineEnd(),h=null},clean:function(){return m|(p&&d)<<1}}}function c(s,h,l){var d=ai(s),p=ai(h),m=[1,0,0],y=uo(d,p),b=ja(y,y),w=y[0],v=b-w*w;if(!v)return!l&&s;var g=n*b/v,x=-n*w/v,_=uo(m,y),M=Za(m,g),A=Za(y,x);Sl(M,A);var T=_,E=ja(M,T),S=ja(T,T),R=E*E-S*(ja(M,M)-1);if(!(R<0)){var C=Wt(R),$=Za(T,(-E-C)/S);if(Sl($,M),$=Kc($),!l)return $;var N=s[0],k=h[0],I=s[1],L=h[1],z;k<N&&(z=N,N=k,k=z);var P=k-N,D=st(P-at)<J,B=D||P<J;if(!D&&L<I&&(z=I,I=L,L=z),B?D?I+L>0^$[1]<(st($[0]-N)<J?I:L):I<=$[1]&&$[1]<=L:P>at^(N<=$[0]&&$[0]<=k)){var Y=Za(T,(-E+C)/S);return Sl(Y,M),[$,Kc(Y)]}}}function f(s,h){var l=r?t:at-t,d=0;return s<-l?d|=1:s>l&&(d|=2),h<-l?d|=4:h>l&&(d|=8),d}return bv(u,a,o,r?[0,-t]:[-at,t-at])}function cT(t,n,e,r,i,o){var u=t[0],a=t[1],c=n[0],f=n[1],s=0,h=1,l=c-u,d=f-a,p;if(p=e-u,!(!l&&p>0)){if(p/=l,l<0){if(p<s)return;p<h&&(h=p)}else if(l>0){if(p>h)return;p>s&&(s=p)}if(p=i-u,!(!l&&p<0)){if(p/=l,l<0){if(p>h)return;p>s&&(s=p)}else if(l>0){if(p<s)return;p<h&&(h=p)}if(p=r-a,!(!d&&p>0)){if(p/=d,d<0){if(p<s)return;p<h&&(h=p)}else if(d>0){if(p>h)return;p>s&&(s=p)}if(p=o-a,!(!d&&p<0)){if(p/=d,d<0){if(p>h)return;p>s&&(s=p)}else if(d>0){if(p<s)return;p<h&&(h=p)}return s>0&&(t[0]=u+s*l,t[1]=a+s*d),h<1&&(n[0]=u+h*l,n[1]=a+h*d),!0}}}}}var au=1e9,Qa=-au;function da(t,n,e,r){function i(f,s){return t<=f&&f<=e&&n<=s&&s<=r}function o(f,s,h,l){var d=0,p=0;if(f==null||(d=u(f,h))!==(p=u(s,h))||c(f,s)<0^h>0)do l.point(d===0||d===3?t:e,d>1?r:n);while((d=(d+h+4)%4)!==p);else l.point(s[0],s[1])}function u(f,s){return st(f[0]-t)<J?s>0?0:3:st(f[0]-e)<J?s>0?2:1:st(f[1]-n)<J?s>0?1:0:s>0?3:2}function a(f,s){return c(f.x,s.x)}function c(f,s){var h=u(f,1),l=u(s,1);return h!==l?h-l:h===0?s[1]-f[1]:h===1?f[0]-s[0]:h===2?f[1]-s[1]:s[0]-f[0]}return function(f){var s=f,h=pv(),l,d,p,m,y,b,w,v,g,x,_,M={point:A,lineStart:R,lineEnd:C,polygonStart:E,polygonEnd:S};function A(N,k){i(N,k)&&s.point(N,k)}function T(){for(var N=0,k=0,I=d.length;k<I;++k)for(var L=d[k],z=1,P=L.length,D=L[0],B,Y,U=D[0],W=D[1];z<P;++z)B=U,Y=W,D=L[z],U=D[0],W=D[1],Y<=r?W>r&&(U-B)*(r-Y)>(W-Y)*(t-B)&&++N:W<=r&&(U-B)*(r-Y)<(W-Y)*(t-B)&&--N;return N}function E(){s=h,l=[],d=[],_=!0}function S(){var N=T(),k=_&&N,I=(l=Cd(l)).length;(k||I)&&(f.polygonStart(),k&&(f.lineStart(),o(null,null,1,f),f.lineEnd()),I&&mv(l,a,N,o,f),f.polygonEnd()),s=f,l=d=p=null}function R(){M.point=$,d&&d.push(p=[]),x=!0,g=!1,w=v=NaN}function C(){l&&($(m,y),b&&g&&h.rejoin(),l.push(h.result())),M.point=A,g&&s.lineEnd()}function $(N,k){var I=i(N,k);if(d&&p.push([N,k]),x)m=N,y=k,b=I,x=!1,I&&(s.lineStart(),s.point(N,k));else if(I&&g)s.point(N,k);else{var L=[w=Math.max(Qa,Math.min(au,w)),v=Math.max(Qa,Math.min(au,v))],z=[N=Math.max(Qa,Math.min(au,N)),k=Math.max(Qa,Math.min(au,k))];cT(L,z,t,n,e,r)?(g||(s.lineStart(),s.point(L[0],L[1])),s.point(z[0],z[1]),I||s.lineEnd(),_=!1):I&&(s.lineStart(),s.point(N,k),_=!1)}w=N,v=k,g=I}return M}}function fT(){var t=0,n=0,e=960,r=500,i,o,u;return u={stream:function(a){return i&&o===a?i:i=da(t,n,e,r)(o=a)},extent:function(a){return arguments.length?(t=+a[0][0],n=+a[0][1],e=+a[1][0],r=+a[1][1],i=o=null,u):[[t,n],[e,r]]}}}var Bh,qh,Tc,Ec,ao={sphere:kt,point:kt,lineStart:sT,lineEnd:kt,polygonStart:kt,polygonEnd:kt};function sT(){ao.point=hT,ao.lineEnd=lT}function lT(){ao.point=ao.lineEnd=kt}function hT(t,n){t*=tt,n*=tt,qh=t,Tc=V(n),Ec=K(n),ao.point=dT}function dT(t,n){t*=tt,n*=tt;var e=V(n),r=K(n),i=st(t-qh),o=K(i),u=V(i),a=r*u,c=Ec*e-Tc*r*o,f=Tc*e+Ec*r*o;Bh.add(pn(Wt(a*a+c*c),f)),qh=t,Tc=e,Ec=r}function vv(t){return Bh=new Bt,Pn(t,ao),+Bh}var Yh=[null,null],gT={type:"LineString",coordinates:Yh};function af(t,n){return Yh[0]=t,Yh[1]=n,vv(gT)}var ey={Feature:function(t,n){return cf(t.geometry,n)},FeatureCollection:function(t,n){for(var e=t.features,r=-1,i=e.length;++r<i;)if(cf(e[r].geometry,n))return!0;return!1}},ry={Sphere:function(){return!0},Point:function(t,n){return iy(t.coordinates,n)},MultiPoint:function(t,n){for(var e=t.coordinates,r=-1,i=e.length;++r<i;)if(iy(e[r],n))return!0;return!1},LineString:function(t,n){return oy(t.coordinates,n)},MultiLineString:function(t,n){for(var e=t.coordinates,r=-1,i=e.length;++r<i;)if(oy(e[r],n))return!0;return!1},Polygon:function(t,n){return uy(t.coordinates,n)},MultiPolygon:function(t,n){for(var e=t.coordinates,r=-1,i=e.length;++r<i;)if(uy(e[r],n))return!0;return!1},GeometryCollection:function(t,n){for(var e=t.geometries,r=-1,i=e.length;++r<i;)if(cf(e[r],n))return!0;return!1}};function cf(t,n){return t&&ry.hasOwnProperty(t.type)?ry[t.type](t,n):!1}function iy(t,n){return af(t,n)===0}function oy(t,n){for(var e,r,i,o=0,u=t.length;o<u;o++){if(r=af(t[o],n),r===0||o>0&&(i=af(t[o],t[o-1]),i>0&&e<=i&&r<=i&&(e+r-i)*(1-Math.pow((e-r)/i,2))<Iu*i))return!0;e=r}return!1}function uy(t,n){return!!yv(t.map(pT),xv(n))}function pT(t){return t=t.map(xv),t.pop(),t}function xv(t){return[t[0]*tt,t[1]*tt]}function mT(t,n){return(t&&ey.hasOwnProperty(t.type)?ey[t.type]:cf)(t,n)}function ay(t,n,e){var r=kn(t,n-J,e).concat(n);return function(i){return r.map(function(o){return[i,o]})}}function cy(t,n,e){var r=kn(t,n-J,e).concat(n);return function(i){return r.map(function(o){return[o,i]})}}function _v(){var t,n,e,r,i,o,u,a,c=10,f=c,s=90,h=360,l,d,p,m,y=2.5;function b(){return{type:"MultiLineString",coordinates:w()}}function w(){return kn(Va(r/s)*s,e,s).map(p).concat(kn(Va(a/h)*h,u,h).map(m)).concat(kn(Va(n/c)*c,t,c).filter(function(v){return st(v%s)>J}).map(l)).concat(kn(Va(o/f)*f,i,f).filter(function(v){return st(v%h)>J}).map(d))}return b.lines=function(){return w().map(function(v){return{type:"LineString",coordinates:v}})},b.outline=function(){return{type:"Polygon",coordinates:[p(r).concat(m(u).slice(1),p(e).reverse().slice(1),m(a).reverse().slice(1))]}},b.extent=function(v){return arguments.length?b.extentMajor(v).extentMinor(v):b.extentMinor()},b.extentMajor=function(v){return arguments.length?(r=+v[0][0],e=+v[1][0],a=+v[0][1],u=+v[1][1],r>e&&(v=r,r=e,e=v),a>u&&(v=a,a=u,u=v),b.precision(y)):[[r,a],[e,u]]},b.extentMinor=function(v){return arguments.length?(n=+v[0][0],t=+v[1][0],o=+v[0][1],i=+v[1][1],n>t&&(v=n,n=t,t=v),o>i&&(v=o,o=i,i=v),b.precision(y)):[[n,o],[t,i]]},b.step=function(v){return arguments.length?b.stepMajor(v).stepMinor(v):b.stepMinor()},b.stepMajor=function(v){return arguments.length?(s=+v[0],h=+v[1],b):[s,h]},b.stepMinor=function(v){return arguments.length?(c=+v[0],f=+v[1],b):[c,f]},b.precision=function(v){return arguments.length?(y=+v,l=ay(o,i,90),d=cy(n,t,y),p=ay(a,u,90),m=cy(r,e,y),b):y},b.extentMajor([[-180,-90+J],[180,90-J]]).extentMinor([[-180,-80-J],[180,80+J]])}function Mv(){return _v()()}function yT(t,n){var e=t[0]*tt,r=t[1]*tt,i=n[0]*tt,o=n[1]*tt,u=K(r),a=V(r),c=K(o),f=V(o),s=u*K(e),h=u*V(e),l=c*K(i),d=c*V(i),p=2*mn(Wt(Ym(o-r)+u*c*Ym(i-e))),m=V(p),y=p?function(b){var w=V(b*=p)/m,v=V(p-b)/m,g=v*s+w*l,x=v*h+w*d,_=v*a+w*f;return[pn(x,g)*mt,pn(_,Wt(g*g+x*x))*mt]}:function(){return[e*mt,r*mt]};return y.distance=p,y}const zu=t=>t;var El=new Bt,Uh=new Bt,$v,Av,Xh,Wh,Oe={point:kt,lineStart:kt,lineEnd:kt,polygonStart:function(){Oe.lineStart=bT,Oe.lineEnd=vT},polygonEnd:function(){Oe.lineStart=Oe.lineEnd=Oe.point=kt,El.add(st(Uh)),Uh=new Bt},result:function(){var t=El/2;return El=new Bt,t}};function bT(){Oe.point=wT}function wT(t,n){Oe.point=Sv,$v=Xh=t,Av=Wh=n}function Sv(t,n){Uh.add(Wh*t-Xh*n),Xh=t,Wh=n}function vT(){Sv($v,Av)}var co=1/0,ff=co,Lu=-co,sf=Lu,lf={point:xT,lineStart:kt,lineEnd:kt,polygonStart:kt,polygonEnd:kt,result:function(){var t=[[co,ff],[Lu,sf]];return Lu=sf=-(ff=co=1/0),t}};function xT(t,n){t<co&&(co=t),t>Lu&&(Lu=t),n<ff&&(ff=n),n>sf&&(sf=n)}var Hh=0,Gh=0,cu=0,hf=0,df=0,Bi=0,Vh=0,jh=0,fu=0,Tv,Ev,ae,ce,Dn={point:ci,lineStart:fy,lineEnd:sy,polygonStart:function(){Dn.lineStart=$T,Dn.lineEnd=AT},polygonEnd:function(){Dn.point=ci,Dn.lineStart=fy,Dn.lineEnd=sy},result:function(){var t=fu?[Vh/fu,jh/fu]:Bi?[hf/Bi,df/Bi]:cu?[Hh/cu,Gh/cu]:[NaN,NaN];return Hh=Gh=cu=hf=df=Bi=Vh=jh=fu=0,t}};function ci(t,n){Hh+=t,Gh+=n,++cu}function fy(){Dn.point=_T}function _T(t,n){Dn.point=MT,ci(ae=t,ce=n)}function MT(t,n){var e=t-ae,r=n-ce,i=Wt(e*e+r*r);hf+=i*(ae+t)/2,df+=i*(ce+n)/2,Bi+=i,ci(ae=t,ce=n)}function sy(){Dn.point=ci}function $T(){Dn.point=ST}function AT(){kv(Tv,Ev)}function ST(t,n){Dn.point=kv,ci(Tv=ae=t,Ev=ce=n)}function kv(t,n){var e=t-ae,r=n-ce,i=Wt(e*e+r*r);hf+=i*(ae+t)/2,df+=i*(ce+n)/2,Bi+=i,i=ce*t-ae*n,Vh+=i*(ae+t),jh+=i*(ce+n),fu+=i*3,ci(ae=t,ce=n)}function Nv(t){this._context=t}Nv.prototype={_radius:4.5,pointRadius:function(t){return this._radius=t,this},polygonStart:function(){this._line=0},polygonEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){this._line===0&&this._context.closePath(),this._point=NaN},point:function(t,n){switch(this._point){case 0:{this._context.moveTo(t,n),this._point=1;break}case 1:{this._context.lineTo(t,n);break}default:{this._context.moveTo(t+this._radius,n),this._context.arc(t,n,this._radius,0,gn);break}}},result:kt};var Zh=new Bt,kl,Cv,Rv,su,lu,Pu={point:kt,lineStart:function(){Pu.point=TT},lineEnd:function(){kl&&Iv(Cv,Rv),Pu.point=kt},polygonStart:function(){kl=!0},polygonEnd:function(){kl=null},result:function(){var t=+Zh;return Zh=new Bt,t}};function TT(t,n){Pu.point=Iv,Cv=su=t,Rv=lu=n}function Iv(t,n){su-=t,lu-=n,Zh.add(Wt(su*su+lu*lu)),su=t,lu=n}let ly,gf,hy,dy;class gy{constructor(n){this._append=n==null?zv:ET(n),this._radius=4.5,this._=""}pointRadius(n){return this._radius=+n,this}polygonStart(){this._line=0}polygonEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){this._line===0&&(this._+="Z"),this._point=NaN}point(n,e){switch(this._point){case 0:{this._append`M${n},${e}`,this._point=1;break}case 1:{this._append`L${n},${e}`;break}default:{if(this._append`M${n},${e}`,this._radius!==hy||this._append!==gf){const r=this._radius,i=this._;this._="",this._append`m0,${r}a${r},${r} 0 1,1 0,${-2*r}a${r},${r} 0 1,1 0,${2*r}z`,hy=r,gf=this._append,dy=this._,this._=i}this._+=dy;break}}}result(){const n=this._;return this._="",n.length?n:null}}function zv(t){let n=1;this._+=t[0];for(const e=t.length;n<e;++n)this._+=arguments[n]+t[n]}function ET(t){const n=Math.floor(t);if(!(n>=0))throw new RangeError(`invalid digits: ${t}`);if(n>15)return zv;if(n!==ly){const e=10**n;ly=n,gf=function(i){let o=1;this._+=i[0];for(const u=i.length;o<u;++o)this._+=Math.round(arguments[o]*e)/e+i[o]}}return gf}function ko(t,n){let e=3,r=4.5,i,o;function u(a){return a&&(typeof r=="function"&&o.pointRadius(+r.apply(this,arguments)),Pn(a,i(o))),o.result()}return u.area=function(a){return Pn(a,i(Oe)),Oe.result()},u.measure=function(a){return Pn(a,i(Pu)),Pu.result()},u.bounds=function(a){return Pn(a,i(lf)),lf.result()},u.centroid=function(a){return Pn(a,i(Dn)),Dn.result()},u.projection=function(a){return arguments.length?(i=a==null?(t=null,zu):(t=a).stream,u):t},u.context=function(a){return arguments.length?(o=a==null?(n=null,new gy(e)):new Nv(n=a),typeof r!="function"&&o.pointRadius(r),u):n},u.pointRadius=function(a){return arguments.length?(r=typeof a=="function"?a:(o.pointRadius(+a),+a),u):r},u.digits=function(a){if(!arguments.length)return e;if(a==null)e=null;else{const c=Math.floor(a);if(!(c>=0))throw new RangeError(`invalid digits: ${a}`);e=c}return n===null&&(o=new gy(e)),u},u.projection(t).digits(e).context(n)}function Du(t){return{stream:ga(t)}}function ga(t){return function(n){var e=new Kh;for(var r in t)e[r]=t[r];return e.stream=n,e}}function Kh(){}Kh.prototype={constructor:Kh,point:function(t,n){this.stream.point(t,n)},sphere:function(){this.stream.sphere()},lineStart:function(){this.stream.lineStart()},lineEnd:function(){this.stream.lineEnd()},polygonStart:function(){this.stream.polygonStart()},polygonEnd:function(){this.stream.polygonEnd()}};function o0(t,n,e){var r=t.clipExtent&&t.clipExtent();return t.scale(150).translate([0,0]),r!=null&&t.clipExtent(null),Pn(e,t.stream(lf)),n(lf.result()),r!=null&&t.clipExtent(r),t}function cs(t,n,e){return o0(t,function(r){var i=n[1][0]-n[0][0],o=n[1][1]-n[0][1],u=Math.min(i/(r[1][0]-r[0][0]),o/(r[1][1]-r[0][1])),a=+n[0][0]+(i-u*(r[1][0]+r[0][0]))/2,c=+n[0][1]+(o-u*(r[1][1]+r[0][1]))/2;t.scale(150*u).translate([a,c])},e)}function u0(t,n,e){return cs(t,[[0,0],n],e)}function a0(t,n,e){return o0(t,function(r){var i=+n,o=i/(r[1][0]-r[0][0]),u=(i-o*(r[1][0]+r[0][0]))/2,a=-o*r[0][1];t.scale(150*o).translate([u,a])},e)}function c0(t,n,e){return o0(t,function(r){var i=+n,o=i/(r[1][1]-r[0][1]),u=-o*r[0][0],a=(i-o*(r[1][1]+r[0][1]))/2;t.scale(150*o).translate([u,a])},e)}var py=16,kT=K(30*tt);function my(t,n){return+n?CT(t,n):NT(t)}function NT(t){return ga({point:function(n,e){n=t(n,e),this.stream.point(n[0],n[1])}})}function CT(t,n){function e(r,i,o,u,a,c,f,s,h,l,d,p,m,y){var b=f-r,w=s-i,v=b*b+w*w;if(v>4*n&&m--){var g=u+l,x=a+d,_=c+p,M=Wt(g*g+x*x+_*_),A=mn(_/=M),T=st(st(_)-1)<J||st(o-h)<J?(o+h)/2:pn(x,g),E=t(T,A),S=E[0],R=E[1],C=S-r,$=R-i,N=w*C-b*$;(N*N/v>n||st((b*C+w*$)/v-.5)>.3||u*l+a*d+c*p<kT)&&(e(r,i,o,u,a,c,S,R,T,g/=M,x/=M,_,m,y),y.point(S,R),e(S,R,T,g,x,_,f,s,h,l,d,p,m,y))}}return function(r){var i,o,u,a,c,f,s,h,l,d,p,m,y={point:b,lineStart:w,lineEnd:g,polygonStart:function(){r.polygonStart(),y.lineStart=x},polygonEnd:function(){r.polygonEnd(),y.lineStart=w}};function b(A,T){A=t(A,T),r.point(A[0],A[1])}function w(){h=NaN,y.point=v,r.lineStart()}function v(A,T){var E=ai([A,T]),S=t(A,T);e(h,l,s,d,p,m,h=S[0],l=S[1],s=A,d=E[0],p=E[1],m=E[2],py,r),r.point(h,l)}function g(){y.point=b,r.lineEnd()}function x(){w(),y.point=_,y.lineEnd=M}function _(A,T){v(i=A,T),o=h,u=l,a=d,c=p,f=m,y.point=v}function M(){e(h,l,s,d,p,m,o,u,i,a,c,f,py,r),y.lineEnd=g,g()}return y}}var RT=ga({point:function(t,n){this.stream.point(t*tt,n*tt)}});function IT(t){return ga({point:function(n,e){var r=t(n,e);return this.stream.point(r[0],r[1])}})}function zT(t,n,e,r,i){function o(u,a){return u*=r,a*=i,[n+t*u,e-t*a]}return o.invert=function(u,a){return[(u-n)/t*r,(e-a)/t*i]},o}function yy(t,n,e,r,i,o){if(!o)return zT(t,n,e,r,i);var u=K(o),a=V(o),c=u*t,f=a*t,s=u/t,h=a/t,l=(a*e-u*n)/t,d=(a*n+u*e)/t;function p(m,y){return m*=r,y*=i,[c*m-f*y+n,e-f*m-c*y]}return p.invert=function(m,y){return[r*(s*m-h*y+l),i*(d-h*m-s*y)]},p}function Me(t){return f0(function(){return t})()}function f0(t){var n,e=150,r=480,i=250,o=0,u=0,a=0,c=0,f=0,s,h=0,l=1,d=1,p=null,m=Fh,y=null,b,w,v,g=zu,x=.5,_,M,A,T,E;function S(N){return A(N[0]*tt,N[1]*tt)}function R(N){return N=A.invert(N[0],N[1]),N&&[N[0]*mt,N[1]*mt]}S.stream=function(N){return T&&E===N?T:T=RT(IT(s)(m(_(g(E=N)))))},S.preclip=function(N){return arguments.length?(m=N,p=void 0,$()):m},S.postclip=function(N){return arguments.length?(g=N,y=b=w=v=null,$()):g},S.clipAngle=function(N){return arguments.length?(m=+N?wv(p=N*tt):(p=null,Fh),$()):p*mt},S.clipExtent=function(N){return arguments.length?(g=N==null?(y=b=w=v=null,zu):da(y=+N[0][0],b=+N[0][1],w=+N[1][0],v=+N[1][1]),$()):y==null?null:[[y,b],[w,v]]},S.scale=function(N){return arguments.length?(e=+N,C()):e},S.translate=function(N){return arguments.length?(r=+N[0],i=+N[1],C()):[r,i]},S.center=function(N){return arguments.length?(o=N[0]%360*tt,u=N[1]%360*tt,C()):[o*mt,u*mt]},S.rotate=function(N){return arguments.length?(a=N[0]%360*tt,c=N[1]%360*tt,f=N.length>2?N[2]%360*tt:0,C()):[a*mt,c*mt,f*mt]},S.angle=function(N){return arguments.length?(h=N%360*tt,C()):h*mt},S.reflectX=function(N){return arguments.length?(l=N?-1:1,C()):l<0},S.reflectY=function(N){return arguments.length?(d=N?-1:1,C()):d<0},S.precision=function(N){return arguments.length?(_=my(M,x=N*N),$()):Wt(x)},S.fitExtent=function(N,k){return cs(S,N,k)},S.fitSize=function(N,k){return u0(S,N,k)},S.fitWidth=function(N,k){return a0(S,N,k)},S.fitHeight=function(N,k){return c0(S,N,k)};function C(){var N=yy(e,0,0,l,d,h).apply(null,n(o,u)),k=yy(e,r-N[0],i-N[1],l,d,h);return s=i0(a,c,f),M=Dh(n,k),A=Dh(s,M),_=my(M,x),$()}function $(){return T=E=null,S}return function(){return n=t.apply(this,arguments),S.invert=n.invert&&R,C()}}function s0(t){var n=0,e=at/3,r=f0(t),i=r(n,e);return i.parallels=function(o){return arguments.length?r(n=o[0]*tt,e=o[1]*tt):[n*mt,e*mt]},i}function LT(t){var n=K(t);function e(r,i){return[r*n,V(i)/n]}return e.invert=function(r,i){return[r/n,mn(i*n)]},e}function Lv(t,n){var e=V(t),r=(e+V(n))/2;if(st(r)<J)return LT(t);var i=1+e*(2*r-e),o=Wt(i)/r;function u(a,c){var f=Wt(i-2*r*V(c))/r;return[f*V(a*=r),o-f*K(a)]}return u.invert=function(a,c){var f=o-c,s=pn(a,st(f))*On(f);return f*r<0&&(s-=at*On(a)*On(f)),[s/r,mn((i-(a*a+f*f)*r*r)/(2*r))]},u}function Ou(){return s0(Lv).scale(155.424).center([0,33.6442])}function l0(){return Ou().parallels([29.5,45.5]).scale(1070).translate([480,250]).rotate([96,0]).center([-.6,38.7])}function PT(t){var n=t.length;return{point:function(e,r){for(var i=-1;++i<n;)t[i].point(e,r)},sphere:function(){for(var e=-1;++e<n;)t[e].sphere()},lineStart:function(){for(var e=-1;++e<n;)t[e].lineStart()},lineEnd:function(){for(var e=-1;++e<n;)t[e].lineEnd()},polygonStart:function(){for(var e=-1;++e<n;)t[e].polygonStart()},polygonEnd:function(){for(var e=-1;++e<n;)t[e].polygonEnd()}}}function Pv(){var t,n,e=l0(),r,i=Ou().rotate([154,0]).center([-2,58.5]).parallels([55,65]),o,u=Ou().rotate([157,0]).center([-3,19.9]).parallels([8,18]),a,c,f={point:function(l,d){c=[l,d]}};function s(l){var d=l[0],p=l[1];return c=null,r.point(d,p),c||(o.point(d,p),c)||(a.point(d,p),c)}s.invert=function(l){var d=e.scale(),p=e.translate(),m=(l[0]-p[0])/d,y=(l[1]-p[1])/d;return(y>=.12&&y<.234&&m>=-.425&&m<-.214?i:y>=.166&&y<.234&&m>=-.214&&m<-.115?u:e).invert(l)},s.stream=function(l){return t&&n===l?t:t=PT([e.stream(n=l),i.stream(l),u.stream(l)])},s.precision=function(l){return arguments.length?(e.precision(l),i.precision(l),u.precision(l),h()):e.precision()},s.scale=function(l){return arguments.length?(e.scale(l),i.scale(l*.35),u.scale(l),s.translate(e.translate())):e.scale()},s.translate=function(l){if(!arguments.length)return e.translate();var d=e.scale(),p=+l[0],m=+l[1];return r=e.translate(l).clipExtent([[p-.455*d,m-.238*d],[p+.455*d,m+.238*d]]).stream(f),o=i.translate([p-.307*d,m+.201*d]).clipExtent([[p-.425*d+J,m+.12*d+J],[p-.214*d-J,m+.234*d-J]]).stream(f),a=u.translate([p-.205*d,m+.212*d]).clipExtent([[p-.214*d+J,m+.166*d+J],[p-.115*d-J,m+.234*d-J]]).stream(f),h()},s.fitExtent=function(l,d){return cs(s,l,d)},s.fitSize=function(l,d){return u0(s,l,d)},s.fitWidth=function(l,d){return a0(s,l,d)},s.fitHeight=function(l,d){return c0(s,l,d)};function h(){return t=n=null,s}return s.scale(1070)}function Dv(t){return function(n,e){var r=K(n),i=K(e),o=t(r*i);return o===1/0?[2,0]:[o*i*V(n),o*V(e)]}}function pa(t){return function(n,e){var r=Wt(n*n+e*e),i=t(r),o=V(i),u=K(i);return[pn(n*o,r*u),mn(r&&e*o/r)]}}var h0=Dv(function(t){return Wt(2/(1+t))});h0.invert=pa(function(t){return 2*mn(t/2)});function Ov(){return Me(h0).scale(124.75).clipAngle(180-.001)}var d0=Dv(function(t){return(t=nv(t))&&t/V(t)});d0.invert=pa(function(t){return t});function Fv(){return Me(d0).scale(79.4188).clipAngle(180-.001)}function ma(t,n){return[t,Gc(e0((zt+n)/2))]}ma.invert=function(t,n){return[t,2*Eo(tv(n))-zt]};function Bv(){return qv(ma).scale(961/gn)}function qv(t){var n=Me(t),e=n.center,r=n.scale,i=n.translate,o=n.clipExtent,u=null,a,c,f;n.scale=function(h){return arguments.length?(r(h),s()):r()},n.translate=function(h){return arguments.length?(i(h),s()):i()},n.center=function(h){return arguments.length?(e(h),s()):e()},n.clipExtent=function(h){return arguments.length?(h==null?u=a=c=f=null:(u=+h[0][0],a=+h[0][1],c=+h[1][0],f=+h[1][1]),s()):u==null?null:[[u,a],[c,f]]};function s(){var h=at*r(),l=n(dv(n.rotate()).invert([0,0]));return o(u==null?[[l[0]-h,l[1]-h],[l[0]+h,l[1]+h]]:t===ma?[[Math.max(l[0]-h,u),a],[Math.min(l[0]+h,c),f]]:[[u,Math.max(l[1]-h,a)],[c,Math.min(l[1]+h,f)]])}return s()}function Ja(t){return e0((zt+t)/2)}function Yv(t,n){var e=K(t),r=t===n?V(t):Gc(e/K(n))/Gc(Ja(n)/Ja(t)),i=e*Al(Ja(t),r)/r;if(!r)return ma;function o(u,a){i>0?a<-zt+J&&(a=-zt+J):a>zt-J&&(a=zt-J);var c=i/Al(Ja(a),r);return[c*V(r*u),i-c*K(r*u)]}return o.invert=function(u,a){var c=i-a,f=On(r)*Wt(u*u+c*c),s=pn(u,st(c))*On(c);return c*r<0&&(s-=at*On(u)*On(c)),[s/r,2*Eo(Al(i/f,1/r))-zt]},o}function Uv(){return s0(Yv).scale(109.5).parallels([30,30])}function Fu(t,n){return[t,n]}Fu.invert=Fu;function Xv(){return Me(Fu).scale(152.63)}function Wv(t,n){var e=K(t),r=t===n?V(t):(e-K(n))/(n-t),i=e/r+t;if(st(r)<J)return Fu;function o(u,a){var c=i-a,f=r*u;return[c*V(f),i-c*K(f)]}return o.invert=function(u,a){var c=i-a,f=pn(u,st(c))*On(c);return c*r<0&&(f-=at*On(u)*On(c)),[f/r,i-On(r)*Wt(u*u+c*c)]},o}function Hv(){return s0(Wv).scale(131.154).center([0,13.9389])}var yu=1.340264,bu=-.081106,wu=893e-6,vu=.003796,pf=Wt(3)/2,DT=12;function g0(t,n){var e=mn(pf*V(n)),r=e*e,i=r*r*r;return[t*K(e)/(pf*(yu+3*bu*r+i*(7*wu+9*vu*r))),e*(yu+bu*r+i*(wu+vu*r))]}g0.invert=function(t,n){for(var e=n,r=e*e,i=r*r*r,o=0,u,a,c;o<DT&&(a=e*(yu+bu*r+i*(wu+vu*r))-n,c=yu+3*bu*r+i*(7*wu+9*vu*r),e-=u=a/c,r=e*e,i=r*r*r,!(st(u)<Iu));++o);return[pf*t*(yu+3*bu*r+i*(7*wu+9*vu*r))/K(e),mn(V(e)/pf)]};function Gv(){return Me(g0).scale(177.158)}function p0(t,n){var e=K(n),r=K(t)*e;return[e*V(t)/r,V(n)/r]}p0.invert=pa(Eo);function Vv(){return Me(p0).scale(144.049).clipAngle(60)}function OT(){var t=1,n=0,e=0,r=1,i=1,o=0,u,a,c=null,f,s,h,l=1,d=1,p=ga({point:function(g,x){var _=v([g,x]);this.stream.point(_[0],_[1])}}),m=zu,y,b;function w(){return l=t*r,d=t*i,y=b=null,v}function v(g){var x=g[0]*l,_=g[1]*d;if(o){var M=_*u-x*a;x=x*u+_*a,_=M}return[x+n,_+e]}return v.invert=function(g){var x=g[0]-n,_=g[1]-e;if(o){var M=_*u+x*a;x=x*u-_*a,_=M}return[x/l,_/d]},v.stream=function(g){return y&&b===g?y:y=p(m(b=g))},v.postclip=function(g){return arguments.length?(m=g,c=f=s=h=null,w()):m},v.clipExtent=function(g){return arguments.length?(m=g==null?(c=f=s=h=null,zu):da(c=+g[0][0],f=+g[0][1],s=+g[1][0],h=+g[1][1]),w()):c==null?null:[[c,f],[s,h]]},v.scale=function(g){return arguments.length?(t=+g,w()):t},v.translate=function(g){return arguments.length?(n=+g[0],e=+g[1],w()):[n,e]},v.angle=function(g){return arguments.length?(o=g%360*tt,a=V(o),u=K(o),w()):o*mt},v.reflectX=function(g){return arguments.length?(r=g?-1:1,w()):r<0},v.reflectY=function(g){return arguments.length?(i=g?-1:1,w()):i<0},v.fitExtent=function(g,x){return cs(v,g,x)},v.fitSize=function(g,x){return u0(v,g,x)},v.fitWidth=function(g,x){return a0(v,g,x)},v.fitHeight=function(g,x){return c0(v,g,x)},v}function m0(t,n){var e=n*n,r=e*e;return[t*(.8707-.131979*e+r*(-.013791+r*(.003971*e-.001529*r))),n*(1.007226+e*(.015085+r*(-.044475+.028874*e-.005916*r)))]}m0.invert=function(t,n){var e=n,r=25,i;do{var o=e*e,u=o*o;e-=i=(e*(1.007226+o*(.015085+u*(-.044475+.028874*o-.005916*u)))-n)/(1.007226+o*(.015085*3+u*(-.044475*7+.028874*9*o-.005916*11*u)))}while(st(i)>J&&--r>0);return[t/(.8707+(o=e*e)*(-.131979+o*(-.013791+o*o*o*(.003971-.001529*o)))),e]};function FT(){return Me(m0).scale(175.295)}function y0(t,n){return[K(n)*V(t),V(n)]}y0.invert=pa(mn);function jv(){return Me(y0).scale(249.5).clipAngle(90+J)}function b0(t,n){var e=K(n),r=1+K(t)*e;return[e*V(t)/r,V(n)/r]}b0.invert=pa(function(t){return 2*Eo(t)});function Zv(){return Me(b0).scale(250).clipAngle(142)}function w0(t,n){return[Gc(e0((zt+n)/2)),-t]}w0.invert=function(t,n){return[-n,2*Eo(tv(t))-zt]};function Kv(){var t=qv(w0),n=t.center,e=t.rotate;return t.center=function(r){return arguments.length?n([-r[1],r[0]]):(r=n(),[r[1],-r[0]])},t.rotate=function(r){return arguments.length?e([r[0],r[1],r.length>2?r[2]+90:90]):(r=e(),[r[0],r[1],r[2]-90])},e([0,0,90]).scale(159.155)}function BT(t,n){return t.parent===n.parent?1:2}function qT(t){return t.reduce(YT,0)/t.length}function YT(t,n){return t+n.x}function UT(t){return 1+t.reduce(XT,0)}function XT(t,n){return Math.max(t,n.y)}function WT(t){for(var n;n=t.children;)t=n[0];return t}function HT(t){for(var n;n=t.children;)t=n[n.length-1];return t}function v0(){var t=BT,n=1,e=1,r=!1;function i(o){var u,a=0;o.eachAfter(function(l){var d=l.children;d?(l.x=qT(d),l.y=UT(d)):(l.x=u?a+=t(l,u):0,l.y=0,u=l)});var c=WT(o),f=HT(o),s=c.x-t(c,f)/2,h=f.x+t(f,c)/2;return o.eachAfter(r?function(l){l.x=(l.x-o.x)*n,l.y=(o.y-l.y)*e}:function(l){l.x=(l.x-s)/(h-s)*n,l.y=(1-(o.y?l.y/o.y:1))*e})}return i.separation=function(o){return arguments.length?(t=o,i):t},i.size=function(o){return arguments.length?(r=!1,n=+o[0],e=+o[1],i):r?null:[n,e]},i.nodeSize=function(o){return arguments.length?(r=!0,n=+o[0],e=+o[1],i):r?[n,e]:null},i}function GT(t){var n=0,e=t.children,r=e&&e.length;if(!r)n=1;else for(;--r>=0;)n+=e[r].value;t.value=n}function VT(){return this.eachAfter(GT)}function jT(t,n){let e=-1;for(const r of this)t.call(n,r,++e,this);return this}function ZT(t,n){for(var e=this,r=[e],i,o,u=-1;e=r.pop();)if(t.call(n,e,++u,this),i=e.children)for(o=i.length-1;o>=0;--o)r.push(i[o]);return this}function KT(t,n){for(var e=this,r=[e],i=[],o,u,a,c=-1;e=r.pop();)if(i.push(e),o=e.children)for(u=0,a=o.length;u<a;++u)r.push(o[u]);for(;e=i.pop();)t.call(n,e,++c,this);return this}function QT(t,n){let e=-1;for(const r of this)if(t.call(n,r,++e,this))return r}function JT(t){return this.eachAfter(function(n){for(var e=+t(n.data)||0,r=n.children,i=r&&r.length;--i>=0;)e+=r[i].value;n.value=e})}function tE(t){return this.eachBefore(function(n){n.children&&n.children.sort(t)})}function nE(t){for(var n=this,e=eE(n,t),r=[n];n!==e;)n=n.parent,r.push(n);for(var i=r.length;t!==e;)r.splice(i,0,t),t=t.parent;return r}function eE(t,n){if(t===n)return t;var e=t.ancestors(),r=n.ancestors(),i=null;for(t=e.pop(),n=r.pop();t===n;)i=t,t=e.pop(),n=r.pop();return i}function rE(){for(var t=this,n=[t];t=t.parent;)n.push(t);return n}function iE(){return Array.from(this)}function oE(){var t=[];return this.eachBefore(function(n){n.children||t.push(n)}),t}function uE(){var t=this,n=[];return t.each(function(e){e!==t&&n.push({source:e.parent,target:e})}),n}function*aE(){var t=this,n,e=[t],r,i,o;do for(n=e.reverse(),e=[];t=n.pop();)if(yield t,r=t.children)for(i=0,o=r.length;i<o;++i)e.push(r[i]);while(e.length)}function x0(t,n){t instanceof Map?(t=[void 0,t],n===void 0&&(n=sE)):n===void 0&&(n=fE);for(var e=new fi(t),r,i=[e],o,u,a,c;r=i.pop();)if((u=n(r.data))&&(c=(u=Array.from(u)).length))for(r.children=u,a=c-1;a>=0;--a)i.push(o=u[a]=new fi(u[a])),o.parent=r,o.depth=r.depth+1;return e.eachBefore(Qv)}function cE(){return x0(this).eachBefore(lE)}function fE(t){return t.children}function sE(t){return Array.isArray(t)?t[1]:null}function lE(t){t.data.value!==void 0&&(t.value=t.data.value),t.data=t.data.data}function Qv(t){var n=0;do t.height=n;while((t=t.parent)&&t.height<++n)}function fi(t){this.data=t,this.depth=this.height=0,this.parent=null}fi.prototype=x0.prototype={constructor:fi,count:VT,each:jT,eachAfter:KT,eachBefore:ZT,find:QT,sum:JT,sort:tE,path:nE,ancestors:rE,descendants:iE,leaves:oE,links:uE,copy:cE,[Symbol.iterator]:aE};function kc(t){return t==null?null:Jv(t)}function Jv(t){if(typeof t!="function")throw new Error;return t}function Ur(){return 0}function Oi(t){return function(){return t}}const hE=1664525,dE=1013904223,by=4294967296;function _0(){let t=1;return()=>(t=(hE*t+dE)%by)/by}function gE(t){return typeof t=="object"&&"length"in t?t:Array.from(t)}function pE(t,n){let e=t.length,r,i;for(;e;)i=n()*e--|0,r=t[e],t[e]=t[i],t[i]=r;return t}function mE(t){return tx(t,_0())}function tx(t,n){for(var e=0,r=(t=pE(Array.from(t),n)).length,i=[],o,u;e<r;)o=t[e],u&&nx(u,o)?++e:(u=bE(i=yE(i,o)),e=0);return u}function yE(t,n){var e,r;if(Nl(n,t))return[n];for(e=0;e<t.length;++e)if(tc(n,t[e])&&Nl(hu(t[e],n),t))return[t[e],n];for(e=0;e<t.length-1;++e)for(r=e+1;r<t.length;++r)if(tc(hu(t[e],t[r]),n)&&tc(hu(t[e],n),t[r])&&tc(hu(t[r],n),t[e])&&Nl(ex(t[e],t[r],n),t))return[t[e],t[r],n];throw new Error}function tc(t,n){var e=t.r-n.r,r=n.x-t.x,i=n.y-t.y;return e<0||e*e<r*r+i*i}function nx(t,n){var e=t.r-n.r+Math.max(t.r,n.r,1)*1e-9,r=n.x-t.x,i=n.y-t.y;return e>0&&e*e>r*r+i*i}function Nl(t,n){for(var e=0;e<n.length;++e)if(!nx(t,n[e]))return!1;return!0}function bE(t){switch(t.length){case 1:return wE(t[0]);case 2:return hu(t[0],t[1]);case 3:return ex(t[0],t[1],t[2])}}function wE(t){return{x:t.x,y:t.y,r:t.r}}function hu(t,n){var e=t.x,r=t.y,i=t.r,o=n.x,u=n.y,a=n.r,c=o-e,f=u-r,s=a-i,h=Math.sqrt(c*c+f*f);return{x:(e+o+c/h*s)/2,y:(r+u+f/h*s)/2,r:(h+i+a)/2}}function ex(t,n,e){var r=t.x,i=t.y,o=t.r,u=n.x,a=n.y,c=n.r,f=e.x,s=e.y,h=e.r,l=r-u,d=r-f,p=i-a,m=i-s,y=c-o,b=h-o,w=r*r+i*i-o*o,v=w-u*u-a*a+c*c,g=w-f*f-s*s+h*h,x=d*p-l*m,_=(p*g-m*v)/(x*2)-r,M=(m*y-p*b)/x,A=(d*v-l*g)/(x*2)-i,T=(l*b-d*y)/x,E=M*M+T*T-1,S=2*(o+_*M+A*T),R=_*_+A*A-o*o,C=-(Math.abs(E)>1e-6?(S+Math.sqrt(S*S-4*E*R))/(2*E):R/S);return{x:r+_+M*C,y:i+A+T*C,r:C}}function wy(t,n,e){var r=t.x-n.x,i,o,u=t.y-n.y,a,c,f=r*r+u*u;f?(o=n.r+e.r,o*=o,c=t.r+e.r,c*=c,o>c?(i=(f+c-o)/(2*f),a=Math.sqrt(Math.max(0,c/f-i*i)),e.x=t.x-i*r-a*u,e.y=t.y-i*u+a*r):(i=(f+o-c)/(2*f),a=Math.sqrt(Math.max(0,o/f-i*i)),e.x=n.x+i*r-a*u,e.y=n.y+i*u+a*r)):(e.x=n.x+e.r,e.y=n.y)}function vy(t,n){var e=t.r+n.r-1e-6,r=n.x-t.x,i=n.y-t.y;return e>0&&e*e>r*r+i*i}function xy(t){var n=t._,e=t.next._,r=n.r+e.r,i=(n.x*e.r+e.x*n.r)/r,o=(n.y*e.r+e.y*n.r)/r;return i*i+o*o}function nc(t){this._=t,this.next=null,this.previous=null}function rx(t,n){if(!(o=(t=gE(t)).length))return 0;var e,r,i,o,u,a,c,f,s,h,l;if(e=t[0],e.x=0,e.y=0,!(o>1))return e.r;if(r=t[1],e.x=-r.r,r.x=e.r,r.y=0,!(o>2))return e.r+r.r;wy(r,e,i=t[2]),e=new nc(e),r=new nc(r),i=new nc(i),e.next=i.previous=r,r.next=e.previous=i,i.next=r.previous=e;t:for(c=3;c<o;++c){wy(e._,r._,i=t[c]),i=new nc(i),f=r.next,s=e.previous,h=r._.r,l=e._.r;do if(h<=l){if(vy(f._,i._)){r=f,e.next=r,r.previous=e,--c;continue t}h+=f._.r,f=f.next}else{if(vy(s._,i._)){e=s,e.next=r,r.previous=e,--c;continue t}l+=s._.r,s=s.previous}while(f!==s.next);for(i.previous=e,i.next=r,e.next=r.previous=r=i,u=xy(e);(i=i.next)!==r;)(a=xy(i))<u&&(e=i,u=a);r=e.next}for(e=[r._],i=r;(i=i.next)!==r;)e.push(i._);for(i=tx(e,n),c=0;c<o;++c)e=t[c],e.x-=i.x,e.y-=i.y;return i.r}function vE(t){return rx(t,_0()),t}function xE(t){return Math.sqrt(t.value)}function _E(){var t=null,n=1,e=1,r=Ur;function i(o){const u=_0();return o.x=n/2,o.y=e/2,t?o.eachBefore(_y(t)).eachAfter(Cl(r,.5,u)).eachBefore(My(1)):o.eachBefore(_y(xE)).eachAfter(Cl(Ur,1,u)).eachAfter(Cl(r,o.r/Math.min(n,e),u)).eachBefore(My(Math.min(n,e)/(2*o.r))),o}return i.radius=function(o){return arguments.length?(t=kc(o),i):t},i.size=function(o){return arguments.length?(n=+o[0],e=+o[1],i):[n,e]},i.padding=function(o){return arguments.length?(r=typeof o=="function"?o:Oi(+o),i):r},i}function _y(t){return function(n){n.children||(n.r=Math.max(0,+t(n)||0))}}function Cl(t,n,e){return function(r){if(i=r.children){var i,o,u=i.length,a=t(r)*n||0,c;if(a)for(o=0;o<u;++o)i[o].r+=a;if(c=rx(i,e),a)for(o=0;o<u;++o)i[o].r-=a;r.r=c+a}}}function My(t){return function(n){var e=n.parent;n.r*=t,e&&(n.x=e.x+t*n.x,n.y=e.y+t*n.y)}}function ix(t){t.x0=Math.round(t.x0),t.y0=Math.round(t.y0),t.x1=Math.round(t.x1),t.y1=Math.round(t.y1)}function ya(t,n,e,r,i){for(var o=t.children,u,a=-1,c=o.length,f=t.value&&(r-n)/t.value;++a<c;)u=o[a],u.y0=e,u.y1=i,u.x0=n,u.x1=n+=u.value*f}function ME(){var t=1,n=1,e=0,r=!1;function i(u){var a=u.height+1;return u.x0=u.y0=e,u.x1=t,u.y1=n/a,u.eachBefore(o(n,a)),r&&u.eachBefore(ix),u}function o(u,a){return function(c){c.children&&ya(c,c.x0,u*(c.depth+1)/a,c.x1,u*(c.depth+2)/a);var f=c.x0,s=c.y0,h=c.x1-e,l=c.y1-e;h<f&&(f=h=(f+h)/2),l<s&&(s=l=(s+l)/2),c.x0=f,c.y0=s,c.x1=h,c.y1=l}}return i.round=function(u){return arguments.length?(r=!!u,i):r},i.size=function(u){return arguments.length?(t=+u[0],n=+u[1],i):[t,n]},i.padding=function(u){return arguments.length?(e=+u,i):e},i}var $E={depth:-1},$y={},Rl={};function AE(t){return t.id}function SE(t){return t.parentId}function M0(){var t=AE,n=SE,e;function r(i){var o=Array.from(i),u=t,a=n,c,f,s,h,l,d,p,m,y=new Map;if(e!=null){const b=o.map((g,x)=>TE(e(g,x,i))),w=b.map(Ay),v=new Set(b).add("");for(const g of w)v.has(g)||(v.add(g),b.push(g),w.push(Ay(g)),o.push(Rl));u=(g,x)=>b[x],a=(g,x)=>w[x]}for(s=0,c=o.length;s<c;++s)f=o[s],d=o[s]=new fi(f),(p=u(f,s,i))!=null&&(p+="")&&(m=d.id=p,y.set(m,y.has(m)?$y:d)),(p=a(f,s,i))!=null&&(p+="")&&(d.parent=p);for(s=0;s<c;++s)if(d=o[s],p=d.parent){if(l=y.get(p),!l)throw new Error("missing: "+p);if(l===$y)throw new Error("ambiguous: "+p);l.children?l.children.push(d):l.children=[d],d.parent=l}else{if(h)throw new Error("multiple roots");h=d}if(!h)throw new Error("no root");if(e!=null){for(;h.data===Rl&&h.children.length===1;)h=h.children[0],--c;for(let b=o.length-1;b>=0&&(d=o[b],d.data===Rl);--b)d.data=null}if(h.parent=$E,h.eachBefore(function(b){b.depth=b.parent.depth+1,--c}).eachBefore(Qv),h.parent=null,c>0)throw new Error("cycle");return h}return r.id=function(i){return arguments.length?(t=kc(i),r):t},r.parentId=function(i){return arguments.length?(n=kc(i),r):n},r.path=function(i){return arguments.length?(e=kc(i),r):e},r}function TE(t){t=`${t}`;let n=t.length;return Qh(t,n-1)&&!Qh(t,n-2)&&(t=t.slice(0,-1)),t[0]==="/"?t:`/${t}`}function Ay(t){let n=t.length;if(n<2)return"";for(;--n>1&&!Qh(t,n););return t.slice(0,n)}function Qh(t,n){if(t[n]==="/"){let e=0;for(;n>0&&t[--n]==="\\";)++e;if((e&1)===0)return!0}return!1}function EE(t,n){return t.parent===n.parent?1:2}function Il(t){var n=t.children;return n?n[0]:t.t}function zl(t){var n=t.children;return n?n[n.length-1]:t.t}function kE(t,n,e){var r=e/(n.i-t.i);n.c-=r,n.s+=e,t.c+=r,n.z+=e,n.m+=e}function NE(t){for(var n=0,e=0,r=t.children,i=r.length,o;--i>=0;)o=r[i],o.z+=n,o.m+=n,n+=o.s+(e+=o.c)}function CE(t,n,e){return t.a.parent===n.parent?t.a:e}function Nc(t,n){this._=t,this.parent=null,this.children=null,this.A=null,this.a=this,this.z=0,this.m=0,this.c=0,this.s=0,this.t=null,this.i=n}Nc.prototype=Object.create(fi.prototype);function RE(t){for(var n=new Nc(t,0),e,r=[n],i,o,u,a;e=r.pop();)if(o=e._.children)for(e.children=new Array(a=o.length),u=a-1;u>=0;--u)r.push(i=e.children[u]=new Nc(o[u],u)),i.parent=e;return(n.parent=new Nc(null,0)).children=[n],n}function Bu(){var t=EE,n=1,e=1,r=null;function i(f){var s=RE(f);if(s.eachAfter(o),s.parent.m=-s.z,s.eachBefore(u),r)f.eachBefore(c);else{var h=f,l=f,d=f;f.eachBefore(function(w){w.x<h.x&&(h=w),w.x>l.x&&(l=w),w.depth>d.depth&&(d=w)});var p=h===l?1:t(h,l)/2,m=p-h.x,y=n/(l.x+p+m),b=e/(d.depth||1);f.eachBefore(function(w){w.x=(w.x+m)*y,w.y=w.depth*b})}return f}function o(f){var s=f.children,h=f.parent.children,l=f.i?h[f.i-1]:null;if(s){NE(f);var d=(s[0].z+s[s.length-1].z)/2;l?(f.z=l.z+t(f._,l._),f.m=f.z-d):f.z=d}else l&&(f.z=l.z+t(f._,l._));f.parent.A=a(f,l,f.parent.A||h[0])}function u(f){f._.x=f.z+f.parent.m,f.m+=f.parent.m}function a(f,s,h){if(s){for(var l=f,d=f,p=s,m=l.parent.children[0],y=l.m,b=d.m,w=p.m,v=m.m,g;p=zl(p),l=Il(l),p&&l;)m=Il(m),d=zl(d),d.a=f,g=p.z+w-l.z-y+t(p._,l._),g>0&&(kE(CE(p,f,h),f,g),y+=g,b+=g),w+=p.m,y+=l.m,v+=m.m,b+=d.m;p&&!zl(d)&&(d.t=p,d.m+=w-b),l&&!Il(m)&&(m.t=l,m.m+=y-v,h=f)}return h}function c(f){f.x*=n,f.y=f.depth*e}return i.separation=function(f){return arguments.length?(t=f,i):t},i.size=function(f){return arguments.length?(r=!1,n=+f[0],e=+f[1],i):r?null:[n,e]},i.nodeSize=function(f){return arguments.length?(r=!0,n=+f[0],e=+f[1],i):r?[n,e]:null},i}function fs(t,n,e,r,i){for(var o=t.children,u,a=-1,c=o.length,f=t.value&&(i-e)/t.value;++a<c;)u=o[a],u.x0=n,u.x1=r,u.y0=e,u.y1=e+=u.value*f}var ox=(1+Math.sqrt(5))/2;function ux(t,n,e,r,i,o){for(var u=[],a=n.children,c,f,s=0,h=0,l=a.length,d,p,m=n.value,y,b,w,v,g,x,_;s<l;){d=i-e,p=o-r;do y=a[h++].value;while(!y&&h<l);for(b=w=y,x=Math.max(p/d,d/p)/(m*t),_=y*y*x,g=Math.max(w/_,_/b);h<l;++h){if(y+=f=a[h].value,f<b&&(b=f),f>w&&(w=f),_=y*y*x,v=Math.max(w/_,_/b),v>g){y-=f;break}g=v}u.push(c={value:y,dice:d<p,children:a.slice(s,h)}),c.dice?ya(c,e,r,i,m?r+=p*y/m:o):fs(c,e,r,m?e+=d*y/m:i,o),m-=y,s=h}return u}const ax=function t(n){function e(r,i,o,u,a){ux(n,r,i,o,u,a)}return e.ratio=function(r){return t((r=+r)>1?r:1)},e}(ox);function IE(){var t=ax,n=!1,e=1,r=1,i=[0],o=Ur,u=Ur,a=Ur,c=Ur,f=Ur;function s(l){return l.x0=l.y0=0,l.x1=e,l.y1=r,l.eachBefore(h),i=[0],n&&l.eachBefore(ix),l}function h(l){var d=i[l.depth],p=l.x0+d,m=l.y0+d,y=l.x1-d,b=l.y1-d;y<p&&(p=y=(p+y)/2),b<m&&(m=b=(m+b)/2),l.x0=p,l.y0=m,l.x1=y,l.y1=b,l.children&&(d=i[l.depth+1]=o(l)/2,p+=f(l)-d,m+=u(l)-d,y-=a(l)-d,b-=c(l)-d,y<p&&(p=y=(p+y)/2),b<m&&(m=b=(m+b)/2),t(l,p,m,y,b))}return s.round=function(l){return arguments.length?(n=!!l,s):n},s.size=function(l){return arguments.length?(e=+l[0],r=+l[1],s):[e,r]},s.tile=function(l){return arguments.length?(t=Jv(l),s):t},s.padding=function(l){return arguments.length?s.paddingInner(l).paddingOuter(l):s.paddingInner()},s.paddingInner=function(l){return arguments.length?(o=typeof l=="function"?l:Oi(+l),s):o},s.paddingOuter=function(l){return arguments.length?s.paddingTop(l).paddingRight(l).paddingBottom(l).paddingLeft(l):s.paddingTop()},s.paddingTop=function(l){return arguments.length?(u=typeof l=="function"?l:Oi(+l),s):u},s.paddingRight=function(l){return arguments.length?(a=typeof l=="function"?l:Oi(+l),s):a},s.paddingBottom=function(l){return arguments.length?(c=typeof l=="function"?l:Oi(+l),s):c},s.paddingLeft=function(l){return arguments.length?(f=typeof l=="function"?l:Oi(+l),s):f},s}function zE(t,n,e,r,i){var o=t.children,u,a=o.length,c,f=new Array(a+1);for(f[0]=c=u=0;u<a;++u)f[u+1]=c+=o[u].value;s(0,a,t.value,n,e,r,i);function s(h,l,d,p,m,y,b){if(h>=l-1){var w=o[h];w.x0=p,w.y0=m,w.x1=y,w.y1=b;return}for(var v=f[h],g=d/2+v,x=h+1,_=l-1;x<_;){var M=x+_>>>1;f[M]<g?x=M+1:_=M}g-f[x-1]<f[x]-g&&h+1<x&&--x;var A=f[x]-v,T=d-A;if(y-p>b-m){var E=d?(p*T+y*A)/d:y;s(h,x,A,p,m,E,b),s(x,l,T,E,m,y,b)}else{var S=d?(m*T+b*A)/d:b;s(h,x,A,p,m,y,S),s(x,l,T,p,S,y,b)}}}function LE(t,n,e,r,i){(t.depth&1?fs:ya)(t,n,e,r,i)}const PE=function t(n){function e(r,i,o,u,a){if((c=r._squarify)&&c.ratio===n)for(var c,f,s,h,l=-1,d,p=c.length,m=r.value;++l<p;){for(f=c[l],s=f.children,h=f.value=0,d=s.length;h<d;++h)f.value+=s[h].value;f.dice?ya(f,i,o,u,m?o+=(a-o)*f.value/m:a):fs(f,i,o,m?i+=(u-i)*f.value/m:u,a),m-=f.value}else r._squarify=c=ux(n,r,i,o,u,a),c.ratio=n}return e.ratio=function(r){return t((r=+r)>1?r:1)},e}(ox);function DE(t){for(var n=-1,e=t.length,r,i=t[e-1],o=0;++n<e;)r=i,i=t[n],o+=r[1]*i[0]-r[0]*i[1];return o/2}function OE(t){for(var n=-1,e=t.length,r=0,i=0,o,u=t[e-1],a,c=0;++n<e;)o=u,u=t[n],c+=a=o[0]*u[1]-u[0]*o[1],r+=(o[0]+u[0])*a,i+=(o[1]+u[1])*a;return c*=3,[r/c,i/c]}function FE(t,n,e){return(n[0]-t[0])*(e[1]-t[1])-(n[1]-t[1])*(e[0]-t[0])}function BE(t,n){return t[0]-n[0]||t[1]-n[1]}function Sy(t){const n=t.length,e=[0,1];let r=2,i;for(i=2;i<n;++i){for(;r>1&&FE(t[e[r-2]],t[e[r-1]],t[i])<=0;)--r;e[r++]=i}return e.slice(0,r)}function qE(t){if((e=t.length)<3)return null;var n,e,r=new Array(e),i=new Array(e);for(n=0;n<e;++n)r[n]=[+t[n][0],+t[n][1],n];for(r.sort(BE),n=0;n<e;++n)i[n]=[r[n][0],-r[n][1]];var o=Sy(r),u=Sy(i),a=u[0]===o[0],c=u[u.length-1]===o[o.length-1],f=[];for(n=o.length-1;n>=0;--n)f.push(t[r[o[n]][2]]);for(n=+a;n<u.length-c;++n)f.push(t[r[u[n]][2]]);return f}function YE(t,n){for(var e=t.length,r=t[e-1],i=n[0],o=n[1],u=r[0],a=r[1],c,f,s=!1,h=0;h<e;++h)r=t[h],c=r[0],f=r[1],f>o!=a>o&&i<(u-c)*(o-f)/(a-f)+c&&(s=!s),u=c,a=f;return s}function UE(t){for(var n=-1,e=t.length,r=t[e-1],i,o,u=r[0],a=r[1],c=0;++n<e;)i=u,o=a,r=t[n],u=r[0],a=r[1],i-=u,o-=a,c+=Math.hypot(i,o);return c}const Qt=Math.random,XE=function t(n){function e(r,i){return r=r==null?0:+r,i=i==null?1:+i,arguments.length===1?(i=r,r=0):i-=r,function(){return n()*i+r}}return e.source=t,e}(Qt),WE=function t(n){function e(r,i){return arguments.length<2&&(i=r,r=0),r=Math.floor(r),i=Math.floor(i)-r,function(){return Math.floor(n()*i+r)}}return e.source=t,e}(Qt),$0=function t(n){function e(r,i){var o,u;return r=r==null?0:+r,i=i==null?1:+i,function(){var a;if(o!=null)a=o,o=null;else do o=n()*2-1,a=n()*2-1,u=o*o+a*a;while(!u||u>1);return r+i*a*Math.sqrt(-2*Math.log(u)/u)}}return e.source=t,e}(Qt),HE=function t(n){var e=$0.source(n);function r(){var i=e.apply(this,arguments);return function(){return Math.exp(i())}}return r.source=t,r}(Qt),cx=function t(n){function e(r){return(r=+r)<=0?()=>0:function(){for(var i=0,o=r;o>1;--o)i+=n();return i+o*n()}}return e.source=t,e}(Qt),GE=function t(n){var e=cx.source(n);function r(i){if((i=+i)==0)return n;var o=e(i);return function(){return o()/i}}return r.source=t,r}(Qt),VE=function t(n){function e(r){return function(){return-Math.log1p(-n())/r}}return e.source=t,e}(Qt),jE=function t(n){function e(r){if((r=+r)<0)throw new RangeError("invalid alpha");return r=1/-r,function(){return Math.pow(1-n(),r)}}return e.source=t,e}(Qt),ZE=function t(n){function e(r){if((r=+r)<0||r>1)throw new RangeError("invalid p");return function(){return Math.floor(n()+r)}}return e.source=t,e}(Qt),fx=function t(n){function e(r){if((r=+r)<0||r>1)throw new RangeError("invalid p");return r===0?()=>1/0:r===1?()=>1:(r=Math.log1p(-r),function(){return 1+Math.floor(Math.log1p(-n())/r)})}return e.source=t,e}(Qt),A0=function t(n){var e=$0.source(n)();function r(i,o){if((i=+i)<0)throw new RangeError("invalid k");if(i===0)return()=>0;if(o=o==null?1:+o,i===1)return()=>-Math.log1p(-n())*o;var u=(i<1?i+1:i)-1/3,a=1/(3*Math.sqrt(u)),c=i<1?()=>Math.pow(n(),1/i):()=>1;return function(){do{do var f=e(),s=1+a*f;while(s<=0);s*=s*s;var h=1-n()}while(h>=1-.0331*f*f*f*f&&Math.log(h)>=.5*f*f+u*(1-s+Math.log(s)));return u*s*c()*o}}return r.source=t,r}(Qt),sx=function t(n){var e=A0.source(n);function r(i,o){var u=e(i),a=e(o);return function(){var c=u();return c===0?0:c/(c+a())}}return r.source=t,r}(Qt),lx=function t(n){var e=fx.source(n),r=sx.source(n);function i(o,u){return o=+o,(u=+u)>=1?()=>o:u<=0?()=>0:function(){for(var a=0,c=o,f=u;c*f>16&&c*(1-f)>16;){var s=Math.floor((c+1)*f),h=r(s,c-s+1)();h<=f?(a+=s,c-=s,f=(f-h)/(1-h)):(c=s-1,f/=h)}for(var l=f<.5,d=l?f:1-f,p=e(d),m=p(),y=0;m<=c;++y)m+=p();return a+(l?y:c-y)}}return i.source=t,i}(Qt),KE=function t(n){function e(r,i,o){var u;return(r=+r)==0?u=a=>-Math.log(a):(r=1/r,u=a=>Math.pow(a,r)),i=i==null?0:+i,o=o==null?1:+o,function(){return i+o*u(-Math.log1p(-n()))}}return e.source=t,e}(Qt),QE=function t(n){function e(r,i){return r=r==null?0:+r,i=i==null?1:+i,function(){return r+i*Math.tan(Math.PI*n())}}return e.source=t,e}(Qt),JE=function t(n){function e(r,i){return r=r==null?0:+r,i=i==null?1:+i,function(){var o=n();return r+i*Math.log(o/(1-o))}}return e.source=t,e}(Qt),tk=function t(n){var e=A0.source(n),r=lx.source(n);function i(o){return function(){for(var u=0,a=o;a>16;){var c=Math.floor(.875*a),f=e(c)();if(f>a)return u+r(c-1,a/f)();u+=c,a-=f}for(var s=-Math.log1p(-n()),h=0;s<=a;++h)s-=Math.log1p(-n());return u+h}}return i.source=t,i}(Qt),nk=1664525,ek=1013904223,Ty=1/4294967296;function ss(t=Math.random()){let n=(0<=t&&t<1?t/Ty:Math.abs(t))|0;return()=>(n=nk*n+ek|0,Ty*(n>>>0))}function Hn(t,n){switch(arguments.length){case 0:break;case 1:this.range(t);break;default:this.range(n).domain(t);break}return this}function Ke(t,n){switch(arguments.length){case 0:break;case 1:{typeof t=="function"?this.interpolator(t):this.range(t);break}default:{this.domain(t),typeof n=="function"?this.interpolator(n):this.range(n);break}}return this}const mf=Symbol("implicit");function ls(){var t=new gr,n=[],e=[],r=mf;function i(o){let u=t.get(o);if(u===void 0){if(r!==mf)return r;t.set(o,u=n.push(o)-1)}return e[u%e.length]}return i.domain=function(o){if(!arguments.length)return n.slice();n=[],t=new gr;for(const u of o)t.has(u)||t.set(u,n.push(u)-1);return i},i.range=function(o){return arguments.length?(e=Array.from(o),i):e.slice()},i.unknown=function(o){return arguments.length?(r=o,i):r},i.copy=function(){return ls(n,e).unknown(r)},Hn.apply(i,arguments),i}function ba(){var t=ls().unknown(void 0),n=t.domain,e=t.range,r=0,i=1,o,u,a=!1,c=0,f=0,s=.5;delete t.unknown;function h(){var l=n().length,d=i<r,p=d?i:r,m=d?r:i;o=(m-p)/Math.max(1,l-c+f*2),a&&(o=Math.floor(o)),p+=(m-p-o*(l-c))*s,u=o*(1-c),a&&(p=Math.round(p),u=Math.round(u));var y=kn(l).map(function(b){return p+o*b});return e(d?y.reverse():y)}return t.domain=function(l){return arguments.length?(n(l),h()):n()},t.range=function(l){return arguments.length?([r,i]=l,r=+r,i=+i,h()):[r,i]},t.rangeRound=function(l){return[r,i]=l,r=+r,i=+i,a=!0,h()},t.bandwidth=function(){return u},t.step=function(){return o},t.round=function(l){return arguments.length?(a=!!l,h()):a},t.padding=function(l){return arguments.length?(c=Math.min(1,f=+l),h()):c},t.paddingInner=function(l){return arguments.length?(c=Math.min(1,l),h()):c},t.paddingOuter=function(l){return arguments.length?(f=+l,h()):f},t.align=function(l){return arguments.length?(s=Math.max(0,Math.min(1,l)),h()):s},t.copy=function(){return ba(n(),[r,i]).round(a).paddingInner(c).paddingOuter(f).align(s)},Hn.apply(h(),arguments)}function hx(t){var n=t.copy;return t.padding=t.paddingOuter,delete t.paddingInner,delete t.paddingOuter,t.copy=function(){return hx(n())},t}function dx(){return hx(ba.apply(null,arguments).paddingInner(1))}function rk(t){return function(){return t}}function yf(t){return+t}var Ey=[0,1];function hn(t){return t}function Jh(t,n){return(n-=t=+t)?function(e){return(e-t)/n}:rk(isNaN(n)?NaN:.5)}function ik(t,n){var e;return t>n&&(e=t,t=n,n=e),function(r){return Math.max(t,Math.min(n,r))}}function ok(t,n,e){var r=t[0],i=t[1],o=n[0],u=n[1];return i<r?(r=Jh(i,r),o=e(u,o)):(r=Jh(r,i),o=e(o,u)),function(a){return o(r(a))}}function uk(t,n,e){var r=Math.min(t.length,n.length)-1,i=new Array(r),o=new Array(r),u=-1;for(t[r]<t[0]&&(t=t.slice().reverse(),n=n.slice().reverse());++u<r;)i[u]=Jh(t[u],t[u+1]),o[u]=e(n[u],n[u+1]);return function(a){var c=He(t,a,1,r)-1;return o[c](i[c](a))}}function wa(t,n){return n.domain(t.domain()).range(t.range()).interpolate(t.interpolate()).clamp(t.clamp()).unknown(t.unknown())}function hs(){var t=Ey,n=Ey,e=Nr,r,i,o,u=hn,a,c,f;function s(){var l=Math.min(t.length,n.length);return u!==hn&&(u=ik(t[0],t[l-1])),a=l>2?uk:ok,c=f=null,h}function h(l){return l==null||isNaN(l=+l)?o:(c||(c=a(t.map(r),n,e)))(r(u(l)))}return h.invert=function(l){return u(i((f||(f=a(n,t.map(r),Gt)))(l)))},h.domain=function(l){return arguments.length?(t=Array.from(l,yf),s()):t.slice()},h.range=function(l){return arguments.length?(n=Array.from(l),s()):n.slice()},h.rangeRound=function(l){return n=Array.from(l),e=aa,s()},h.clamp=function(l){return arguments.length?(u=l?!0:hn,s()):u!==hn},h.interpolate=function(l){return arguments.length?(e=l,s()):e},h.unknown=function(l){return arguments.length?(o=l,h):o},function(l,d){return r=l,i=d,s()}}function S0(){return hs()(hn,hn)}function gx(t,n,e,r){var i=zc(t,n,e),o;switch(r=oo(r??",f"),r.type){case"s":{var u=Math.max(Math.abs(t),Math.abs(n));return r.precision==null&&!isNaN(o=Qw(i,u))&&(r.precision=o),n0(r,u)}case"":case"e":case"g":case"p":case"r":{r.precision==null&&!isNaN(o=Jw(i,Math.max(Math.abs(t),Math.abs(n))))&&(r.precision=o-(r.type==="e"));break}case"f":case"%":{r.precision==null&&!isNaN(o=Kw(i))&&(r.precision=o-(r.type==="%")*2);break}}return pi(r)}function Cr(t){var n=t.domain;return t.ticks=function(e){var r=n();return ge(r[0],r[r.length-1],e??10)},t.tickFormat=function(e,r){var i=n();return gx(i[0],i[i.length-1],e??10,r)},t.nice=function(e){e==null&&(e=10);var r=n(),i=0,o=r.length-1,u=r[i],a=r[o],c,f,s=10;for(a<u&&(f=u,u=a,a=f,f=i,i=o,o=f);s-- >0;){if(f=pr(u,a,e),f===c)return r[i]=u,r[o]=a,n(r);if(f>0)u=Math.floor(u/f)*f,a=Math.ceil(a/f)*f;else if(f<0)u=Math.ceil(u*f)/f,a=Math.floor(a*f)/f;else break;c=f}return t},t}function ds(){var t=S0();return t.copy=function(){return wa(t,ds())},Hn.apply(t,arguments),Cr(t)}function T0(t){var n;function e(r){return r==null||isNaN(r=+r)?n:r}return e.invert=e,e.domain=e.range=function(r){return arguments.length?(t=Array.from(r,yf),e):t.slice()},e.unknown=function(r){return arguments.length?(n=r,e):n},e.copy=function(){return T0(t).unknown(n)},t=arguments.length?Array.from(t,yf):[0,1],Cr(e)}function px(t,n){t=t.slice();var e=0,r=t.length-1,i=t[e],o=t[r],u;return o<i&&(u=e,e=r,r=u,u=i,i=o,o=u),t[e]=n.floor(i),t[r]=n.ceil(o),t}function ky(t){return Math.log(t)}function Ny(t){return Math.exp(t)}function ak(t){return-Math.log(-t)}function ck(t){return-Math.exp(-t)}function fk(t){return isFinite(t)?+("1e"+t):t<0?0:t}function sk(t){return t===10?fk:t===Math.E?Math.exp:n=>Math.pow(t,n)}function lk(t){return t===Math.E?Math.log:t===10&&Math.log10||t===2&&Math.log2||(t=Math.log(t),n=>Math.log(n)/t)}function Cy(t){return(n,e)=>-t(-n,e)}function E0(t){const n=t(ky,Ny),e=n.domain;let r=10,i,o;function u(){return i=lk(r),o=sk(r),e()[0]<0?(i=Cy(i),o=Cy(o),t(ak,ck)):t(ky,Ny),n}return n.base=function(a){return arguments.length?(r=+a,u()):r},n.domain=function(a){return arguments.length?(e(a),u()):e()},n.ticks=a=>{const c=e();let f=c[0],s=c[c.length-1];const h=s<f;h&&([f,s]=[s,f]);let l=i(f),d=i(s),p,m;const y=a==null?10:+a;let b=[];if(!(r%1)&&d-l<y){if(l=Math.floor(l),d=Math.ceil(d),f>0){for(;l<=d;++l)for(p=1;p<r;++p)if(m=l<0?p/o(-l):p*o(l),!(m<f)){if(m>s)break;b.push(m)}}else for(;l<=d;++l)for(p=r-1;p>=1;--p)if(m=l>0?p/o(-l):p*o(l),!(m<f)){if(m>s)break;b.push(m)}b.length*2<y&&(b=ge(f,s,y))}else b=ge(l,d,Math.min(d-l,y)).map(o);return h?b.reverse():b},n.tickFormat=(a,c)=>{if(a==null&&(a=10),c==null&&(c=r===10?"s":","),typeof c!="function"&&(!(r%1)&&(c=oo(c)).precision==null&&(c.trim=!0),c=pi(c)),a===1/0)return c;const f=Math.max(1,r*a/n.ticks().length);return s=>{let h=s/o(Math.round(i(s)));return h*r<r-.5&&(h*=r),h<=f?c(s):""}},n.nice=()=>e(px(e(),{floor:a=>o(Math.floor(i(a))),ceil:a=>o(Math.ceil(i(a)))})),n}function k0(){const t=E0(hs()).domain([1,10]);return t.copy=()=>wa(t,k0()).base(t.base()),Hn.apply(t,arguments),t}function Ry(t){return function(n){return Math.sign(n)*Math.log1p(Math.abs(n/t))}}function Iy(t){return function(n){return Math.sign(n)*Math.expm1(Math.abs(n))*t}}function N0(t){var n=1,e=t(Ry(n),Iy(n));return e.constant=function(r){return arguments.length?t(Ry(n=+r),Iy(n)):n},Cr(e)}function C0(){var t=N0(hs());return t.copy=function(){return wa(t,C0()).constant(t.constant())},Hn.apply(t,arguments)}function zy(t){return function(n){return n<0?-Math.pow(-n,t):Math.pow(n,t)}}function hk(t){return t<0?-Math.sqrt(-t):Math.sqrt(t)}function dk(t){return t<0?-t*t:t*t}function R0(t){var n=t(hn,hn),e=1;function r(){return e===1?t(hn,hn):e===.5?t(hk,dk):t(zy(e),zy(1/e))}return n.exponent=function(i){return arguments.length?(e=+i,r()):e},Cr(n)}function gs(){var t=R0(hs());return t.copy=function(){return wa(t,gs()).exponent(t.exponent())},Hn.apply(t,arguments),t}function gk(){return gs.apply(null,arguments).exponent(.5)}function Ly(t){return Math.sign(t)*t*t}function pk(t){return Math.sign(t)*Math.sqrt(Math.abs(t))}function mx(){var t=S0(),n=[0,1],e=!1,r;function i(o){var u=pk(t(o));return isNaN(u)?r:e?Math.round(u):u}return i.invert=function(o){return t.invert(Ly(o))},i.domain=function(o){return arguments.length?(t.domain(o),i):t.domain()},i.range=function(o){return arguments.length?(t.range((n=Array.from(o,yf)).map(Ly)),i):n.slice()},i.rangeRound=function(o){return i.range(o).round(!0)},i.round=function(o){return arguments.length?(e=!!o,i):e},i.clamp=function(o){return arguments.length?(t.clamp(o),i):t.clamp()},i.unknown=function(o){return arguments.length?(r=o,i):r},i.copy=function(){return mx(t.domain(),n).round(e).clamp(t.clamp()).unknown(r)},Hn.apply(i,arguments),Cr(i)}function I0(){var t=[],n=[],e=[],r;function i(){var u=0,a=Math.max(1,n.length);for(e=new Array(a-1);++u<a;)e[u-1]=Eb(t,u/a);return o}function o(u){return u==null||isNaN(u=+u)?r:n[He(e,u)]}return o.invertExtent=function(u){var a=n.indexOf(u);return a<0?[NaN,NaN]:[a>0?e[a-1]:t[0],a<e.length?e[a]:t[t.length-1]]},o.domain=function(u){if(!arguments.length)return t.slice();t=[];for(let a of u)a!=null&&!isNaN(a=+a)&&t.push(a);return t.sort(yt),i()},o.range=function(u){return arguments.length?(n=Array.from(u),i()):n.slice()},o.unknown=function(u){return arguments.length?(r=u,o):r},o.quantiles=function(){return e.slice()},o.copy=function(){return I0().domain(t).range(n).unknown(r)},Hn.apply(o,arguments)}function yx(){var t=0,n=1,e=1,r=[.5],i=[0,1],o;function u(c){return c!=null&&c<=c?i[He(r,c,0,e)]:o}function a(){var c=-1;for(r=new Array(e);++c<e;)r[c]=((c+1)*n-(c-e)*t)/(e+1);return u}return u.domain=function(c){return arguments.length?([t,n]=c,t=+t,n=+n,a()):[t,n]},u.range=function(c){return arguments.length?(e=(i=Array.from(c)).length-1,a()):i.slice()},u.invertExtent=function(c){var f=i.indexOf(c);return f<0?[NaN,NaN]:f<1?[t,r[0]]:f>=e?[r[e-1],n]:[r[f-1],r[f]]},u.unknown=function(c){return arguments.length&&(o=c),u},u.thresholds=function(){return r.slice()},u.copy=function(){return yx().domain([t,n]).range(i).unknown(o)},Hn.apply(Cr(u),arguments)}function z0(){var t=[.5],n=[0,1],e,r=1;function i(o){return o!=null&&o<=o?n[He(t,o,0,r)]:e}return i.domain=function(o){return arguments.length?(t=Array.from(o),r=Math.min(t.length,n.length-1),i):t.slice()},i.range=function(o){return arguments.length?(n=Array.from(o),r=Math.min(t.length,n.length-1),i):n.slice()},i.invertExtent=function(o){var u=n.indexOf(o);return[t[u-1],t[u]]},i.unknown=function(o){return arguments.length?(e=o,i):e},i.copy=function(){return z0().domain(t).range(n).unknown(e)},Hn.apply(i,arguments)}const Ll=new Date,Pl=new Date;function Ot(t,n,e,r){function i(o){return t(o=arguments.length===0?new Date:new Date(+o)),o}return i.floor=o=>(t(o=new Date(+o)),o),i.ceil=o=>(t(o=new Date(o-1)),n(o,1),t(o),o),i.round=o=>{const u=i(o),a=i.ceil(o);return o-u<a-o?u:a},i.offset=(o,u)=>(n(o=new Date(+o),u==null?1:Math.floor(u)),o),i.range=(o,u,a)=>{const c=[];if(o=i.ceil(o),a=a==null?1:Math.floor(a),!(o<u)||!(a>0))return c;let f;do c.push(f=new Date(+o)),n(o,a),t(o);while(f<o&&o<u);return c},i.filter=o=>Ot(u=>{if(u>=u)for(;t(u),!o(u);)u.setTime(u-1)},(u,a)=>{if(u>=u)if(a<0)for(;++a<=0;)for(;n(u,-1),!o(u););else for(;--a>=0;)for(;n(u,1),!o(u););}),e&&(i.count=(o,u)=>(Ll.setTime(+o),Pl.setTime(+u),t(Ll),t(Pl),Math.floor(e(Ll,Pl))),i.every=o=>(o=Math.floor(o),!isFinite(o)||!(o>0)?null:o>1?i.filter(r?u=>r(u)%o===0:u=>i.count(0,u)%o===0):i)),i}const fo=Ot(()=>{},(t,n)=>{t.setTime(+t+n)},(t,n)=>n-t);fo.every=t=>(t=Math.floor(t),!isFinite(t)||!(t>0)?null:t>1?Ot(n=>{n.setTime(Math.floor(n/t)*t)},(n,e)=>{n.setTime(+n+e*t)},(n,e)=>(e-n)/t):fo);const Py=fo.range,Fe=1e3,Fn=Fe*60,Be=Fn*60,Ve=Be*24,L0=Ve*7,Dy=Ve*30,Dl=Ve*365,Nn=Ot(t=>{t.setTime(t-t.getMilliseconds())},(t,n)=>{t.setTime(+t+n*Fe)},(t,n)=>(n-t)/Fe,t=>t.getUTCSeconds()),Oy=Nn.range,No=Ot(t=>{t.setTime(t-t.getMilliseconds()-t.getSeconds()*Fe)},(t,n)=>{t.setTime(+t+n*Fn)},(t,n)=>(n-t)/Fn,t=>t.getMinutes()),mk=No.range,Co=Ot(t=>{t.setUTCSeconds(0,0)},(t,n)=>{t.setTime(+t+n*Fn)},(t,n)=>(n-t)/Fn,t=>t.getUTCMinutes()),yk=Co.range,Ro=Ot(t=>{t.setTime(t-t.getMilliseconds()-t.getSeconds()*Fe-t.getMinutes()*Fn)},(t,n)=>{t.setTime(+t+n*Be)},(t,n)=>(n-t)/Be,t=>t.getHours()),bk=Ro.range,Io=Ot(t=>{t.setUTCMinutes(0,0,0)},(t,n)=>{t.setTime(+t+n*Be)},(t,n)=>(n-t)/Be,t=>t.getUTCHours()),wk=Io.range,Rr=Ot(t=>t.setHours(0,0,0,0),(t,n)=>t.setDate(t.getDate()+n),(t,n)=>(n-t-(n.getTimezoneOffset()-t.getTimezoneOffset())*Fn)/Ve,t=>t.getDate()-1),vk=Rr.range,va=Ot(t=>{t.setUTCHours(0,0,0,0)},(t,n)=>{t.setUTCDate(t.getUTCDate()+n)},(t,n)=>(n-t)/Ve,t=>t.getUTCDate()-1),xk=va.range,xa=Ot(t=>{t.setUTCHours(0,0,0,0)},(t,n)=>{t.setUTCDate(t.getUTCDate()+n)},(t,n)=>(n-t)/Ve,t=>Math.floor(t/Ve)),_k=xa.range;function mi(t){return Ot(n=>{n.setDate(n.getDate()-(n.getDay()+7-t)%7),n.setHours(0,0,0,0)},(n,e)=>{n.setDate(n.getDate()+e*7)},(n,e)=>(e-n-(e.getTimezoneOffset()-n.getTimezoneOffset())*Fn)/L0)}const wr=mi(0),so=mi(1),P0=mi(2),D0=mi(3),vr=mi(4),O0=mi(5),F0=mi(6),Fy=wr.range,Mk=so.range,$k=P0.range,Ak=D0.range,Sk=vr.range,Tk=O0.range,Ek=F0.range;function yi(t){return Ot(n=>{n.setUTCDate(n.getUTCDate()-(n.getUTCDay()+7-t)%7),n.setUTCHours(0,0,0,0)},(n,e)=>{n.setUTCDate(n.getUTCDate()+e*7)},(n,e)=>(e-n)/L0)}const xr=yi(0),lo=yi(1),B0=yi(2),q0=yi(3),_r=yi(4),Y0=yi(5),U0=yi(6),By=xr.range,kk=lo.range,Nk=B0.range,Ck=q0.range,Rk=_r.range,Ik=Y0.range,zk=U0.range,zo=Ot(t=>{t.setDate(1),t.setHours(0,0,0,0)},(t,n)=>{t.setMonth(t.getMonth()+n)},(t,n)=>n.getMonth()-t.getMonth()+(n.getFullYear()-t.getFullYear())*12,t=>t.getMonth()),Lk=zo.range,Lo=Ot(t=>{t.setUTCDate(1),t.setUTCHours(0,0,0,0)},(t,n)=>{t.setUTCMonth(t.getUTCMonth()+n)},(t,n)=>n.getUTCMonth()-t.getUTCMonth()+(n.getUTCFullYear()-t.getUTCFullYear())*12,t=>t.getUTCMonth()),Pk=Lo.range,Yn=Ot(t=>{t.setMonth(0,1),t.setHours(0,0,0,0)},(t,n)=>{t.setFullYear(t.getFullYear()+n)},(t,n)=>n.getFullYear()-t.getFullYear(),t=>t.getFullYear());Yn.every=t=>!isFinite(t=Math.floor(t))||!(t>0)?null:Ot(n=>{n.setFullYear(Math.floor(n.getFullYear()/t)*t),n.setMonth(0,1),n.setHours(0,0,0,0)},(n,e)=>{n.setFullYear(n.getFullYear()+e*t)});const Dk=Yn.range,Un=Ot(t=>{t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,n)=>{t.setUTCFullYear(t.getUTCFullYear()+n)},(t,n)=>n.getUTCFullYear()-t.getUTCFullYear(),t=>t.getUTCFullYear());Un.every=t=>!isFinite(t=Math.floor(t))||!(t>0)?null:Ot(n=>{n.setUTCFullYear(Math.floor(n.getUTCFullYear()/t)*t),n.setUTCMonth(0,1),n.setUTCHours(0,0,0,0)},(n,e)=>{n.setUTCFullYear(n.getUTCFullYear()+e*t)});const Ok=Un.range;function bx(t,n,e,r,i,o){const u=[[Nn,1,Fe],[Nn,5,5*Fe],[Nn,15,15*Fe],[Nn,30,30*Fe],[o,1,Fn],[o,5,5*Fn],[o,15,15*Fn],[o,30,30*Fn],[i,1,Be],[i,3,3*Be],[i,6,6*Be],[i,12,12*Be],[r,1,Ve],[r,2,2*Ve],[e,1,L0],[n,1,Dy],[n,3,3*Dy],[t,1,Dl]];function a(f,s,h){const l=s<f;l&&([f,s]=[s,f]);const d=h&&typeof h.range=="function"?h:c(f,s,h),p=d?d.range(f,+s+1):[];return l?p.reverse():p}function c(f,s,h){const l=Math.abs(s-f)/h,d=ta(([,,y])=>y).right(u,l);if(d===u.length)return t.every(zc(f/Dl,s/Dl,h));if(d===0)return fo.every(Math.max(zc(f,s,h),1));const[p,m]=u[l/u[d-1][2]<u[d][2]/l?d-1:d];return p.every(m)}return[a,c]}const[wx,X0]=bx(Un,Lo,xr,xa,Io,Co),[vx,xx]=bx(Yn,zo,wr,Rr,Ro,No);function Ol(t){if(0<=t.y&&t.y<100){var n=new Date(-1,t.m,t.d,t.H,t.M,t.S,t.L);return n.setFullYear(t.y),n}return new Date(t.y,t.m,t.d,t.H,t.M,t.S,t.L)}function Fl(t){if(0<=t.y&&t.y<100){var n=new Date(Date.UTC(-1,t.m,t.d,t.H,t.M,t.S,t.L));return n.setUTCFullYear(t.y),n}return new Date(Date.UTC(t.y,t.m,t.d,t.H,t.M,t.S,t.L))}function jo(t,n,e){return{y:t,m:n,d:e,H:0,M:0,S:0,L:0}}function _x(t){var n=t.dateTime,e=t.date,r=t.time,i=t.periods,o=t.days,u=t.shortDays,a=t.months,c=t.shortMonths,f=Zo(i),s=Ko(i),h=Zo(o),l=Ko(o),d=Zo(u),p=Ko(u),m=Zo(a),y=Ko(a),b=Zo(c),w=Ko(c),v={a:I,A:L,b:z,B:P,c:null,d:Hy,e:Hy,f:aN,g:yN,G:wN,H:iN,I:oN,j:uN,L:Mx,m:cN,M:fN,p:D,q:B,Q:jy,s:Zy,S:sN,u:lN,U:hN,V:dN,w:gN,W:pN,x:null,X:null,y:mN,Y:bN,Z:vN,"%":Vy},g={a:Y,A:U,b:W,B:Z,c:null,d:Gy,e:Gy,f:$N,g:zN,G:PN,H:xN,I:_N,j:MN,L:Ax,m:AN,M:SN,p:q,q:H,Q:jy,s:Zy,S:TN,u:EN,U:kN,V:NN,w:CN,W:RN,x:null,X:null,y:IN,Y:LN,Z:DN,"%":Vy},x={a:E,A:S,b:R,B:C,c:$,d:Xy,e:Xy,f:tN,g:Uy,G:Yy,H:Wy,I:Wy,j:Zk,L:Jk,m:jk,M:Kk,p:T,q:Vk,Q:eN,s:rN,S:Qk,u:Uk,U:Xk,V:Wk,w:Yk,W:Hk,x:N,X:k,y:Uy,Y:Yy,Z:Gk,"%":nN};v.x=_(e,v),v.X=_(r,v),v.c=_(n,v),g.x=_(e,g),g.X=_(r,g),g.c=_(n,g);function _(F,X){return function(G){var O=[],rt=-1,Q=0,ft=F.length,lt,j,gt;for(G instanceof Date||(G=new Date(+G));++rt<ft;)F.charCodeAt(rt)===37&&(O.push(F.slice(Q,rt)),(j=qy[lt=F.charAt(++rt)])!=null?lt=F.charAt(++rt):j=lt==="e"?" ":"0",(gt=X[lt])&&(lt=gt(G,j)),O.push(lt),Q=rt+1);return O.push(F.slice(Q,rt)),O.join("")}}function M(F,X){return function(G){var O=jo(1900,void 0,1),rt=A(O,F,G+="",0),Q,ft;if(rt!=G.length)return null;if("Q"in O)return new Date(O.Q);if("s"in O)return new Date(O.s*1e3+("L"in O?O.L:0));if(X&&!("Z"in O)&&(O.Z=0),"p"in O&&(O.H=O.H%12+O.p*12),O.m===void 0&&(O.m="q"in O?O.q:0),"V"in O){if(O.V<1||O.V>53)return null;"w"in O||(O.w=1),"Z"in O?(Q=Fl(jo(O.y,0,1)),ft=Q.getUTCDay(),Q=ft>4||ft===0?lo.ceil(Q):lo(Q),Q=va.offset(Q,(O.V-1)*7),O.y=Q.getUTCFullYear(),O.m=Q.getUTCMonth(),O.d=Q.getUTCDate()+(O.w+6)%7):(Q=Ol(jo(O.y,0,1)),ft=Q.getDay(),Q=ft>4||ft===0?so.ceil(Q):so(Q),Q=Rr.offset(Q,(O.V-1)*7),O.y=Q.getFullYear(),O.m=Q.getMonth(),O.d=Q.getDate()+(O.w+6)%7)}else("W"in O||"U"in O)&&("w"in O||(O.w="u"in O?O.u%7:"W"in O?1:0),ft="Z"in O?Fl(jo(O.y,0,1)).getUTCDay():Ol(jo(O.y,0,1)).getDay(),O.m=0,O.d="W"in O?(O.w+6)%7+O.W*7-(ft+5)%7:O.w+O.U*7-(ft+6)%7);return"Z"in O?(O.H+=O.Z/100|0,O.M+=O.Z%100,Fl(O)):Ol(O)}}function A(F,X,G,O){for(var rt=0,Q=X.length,ft=G.length,lt,j;rt<Q;){if(O>=ft)return-1;if(lt=X.charCodeAt(rt++),lt===37){if(lt=X.charAt(rt++),j=x[lt in qy?X.charAt(rt++):lt],!j||(O=j(F,G,O))<0)return-1}else if(lt!=G.charCodeAt(O++))return-1}return O}function T(F,X,G){var O=f.exec(X.slice(G));return O?(F.p=s.get(O[0].toLowerCase()),G+O[0].length):-1}function E(F,X,G){var O=d.exec(X.slice(G));return O?(F.w=p.get(O[0].toLowerCase()),G+O[0].length):-1}function S(F,X,G){var O=h.exec(X.slice(G));return O?(F.w=l.get(O[0].toLowerCase()),G+O[0].length):-1}function R(F,X,G){var O=b.exec(X.slice(G));return O?(F.m=w.get(O[0].toLowerCase()),G+O[0].length):-1}function C(F,X,G){var O=m.exec(X.slice(G));return O?(F.m=y.get(O[0].toLowerCase()),G+O[0].length):-1}function $(F,X,G){return A(F,n,X,G)}function N(F,X,G){return A(F,e,X,G)}function k(F,X,G){return A(F,r,X,G)}function I(F){return u[F.getDay()]}function L(F){return o[F.getDay()]}function z(F){return c[F.getMonth()]}function P(F){return a[F.getMonth()]}function D(F){return i[+(F.getHours()>=12)]}function B(F){return 1+~~(F.getMonth()/3)}function Y(F){return u[F.getUTCDay()]}function U(F){return o[F.getUTCDay()]}function W(F){return c[F.getUTCMonth()]}function Z(F){return a[F.getUTCMonth()]}function q(F){return i[+(F.getUTCHours()>=12)]}function H(F){return 1+~~(F.getUTCMonth()/3)}return{format:function(F){var X=_(F+="",v);return X.toString=function(){return F},X},parse:function(F){var X=M(F+="",!1);return X.toString=function(){return F},X},utcFormat:function(F){var X=_(F+="",g);return X.toString=function(){return F},X},utcParse:function(F){var X=M(F+="",!0);return X.toString=function(){return F},X}}}var qy={"-":"",_:" ",0:"0"},Ht=/^\s*\d+/,Fk=/^%/,Bk=/[\\^$*+?|[\]().{}]/g;function dt(t,n,e){var r=t<0?"-":"",i=(r?-t:t)+"",o=i.length;return r+(o<e?new Array(e-o+1).join(n)+i:i)}function qk(t){return t.replace(Bk,"\\$&")}function Zo(t){return new RegExp("^(?:"+t.map(qk).join("|")+")","i")}function Ko(t){return new Map(t.map((n,e)=>[n.toLowerCase(),e]))}function Yk(t,n,e){var r=Ht.exec(n.slice(e,e+1));return r?(t.w=+r[0],e+r[0].length):-1}function Uk(t,n,e){var r=Ht.exec(n.slice(e,e+1));return r?(t.u=+r[0],e+r[0].length):-1}function Xk(t,n,e){var r=Ht.exec(n.slice(e,e+2));return r?(t.U=+r[0],e+r[0].length):-1}function Wk(t,n,e){var r=Ht.exec(n.slice(e,e+2));return r?(t.V=+r[0],e+r[0].length):-1}function Hk(t,n,e){var r=Ht.exec(n.slice(e,e+2));return r?(t.W=+r[0],e+r[0].length):-1}function Yy(t,n,e){var r=Ht.exec(n.slice(e,e+4));return r?(t.y=+r[0],e+r[0].length):-1}function Uy(t,n,e){var r=Ht.exec(n.slice(e,e+2));return r?(t.y=+r[0]+(+r[0]>68?1900:2e3),e+r[0].length):-1}function Gk(t,n,e){var r=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(n.slice(e,e+6));return r?(t.Z=r[1]?0:-(r[2]+(r[3]||"00")),e+r[0].length):-1}function Vk(t,n,e){var r=Ht.exec(n.slice(e,e+1));return r?(t.q=r[0]*3-3,e+r[0].length):-1}function jk(t,n,e){var r=Ht.exec(n.slice(e,e+2));return r?(t.m=r[0]-1,e+r[0].length):-1}function Xy(t,n,e){var r=Ht.exec(n.slice(e,e+2));return r?(t.d=+r[0],e+r[0].length):-1}function Zk(t,n,e){var r=Ht.exec(n.slice(e,e+3));return r?(t.m=0,t.d=+r[0],e+r[0].length):-1}function Wy(t,n,e){var r=Ht.exec(n.slice(e,e+2));return r?(t.H=+r[0],e+r[0].length):-1}function Kk(t,n,e){var r=Ht.exec(n.slice(e,e+2));return r?(t.M=+r[0],e+r[0].length):-1}function Qk(t,n,e){var r=Ht.exec(n.slice(e,e+2));return r?(t.S=+r[0],e+r[0].length):-1}function Jk(t,n,e){var r=Ht.exec(n.slice(e,e+3));return r?(t.L=+r[0],e+r[0].length):-1}function tN(t,n,e){var r=Ht.exec(n.slice(e,e+6));return r?(t.L=Math.floor(r[0]/1e3),e+r[0].length):-1}function nN(t,n,e){var r=Fk.exec(n.slice(e,e+1));return r?e+r[0].length:-1}function eN(t,n,e){var r=Ht.exec(n.slice(e));return r?(t.Q=+r[0],e+r[0].length):-1}function rN(t,n,e){var r=Ht.exec(n.slice(e));return r?(t.s=+r[0],e+r[0].length):-1}function Hy(t,n){return dt(t.getDate(),n,2)}function iN(t,n){return dt(t.getHours(),n,2)}function oN(t,n){return dt(t.getHours()%12||12,n,2)}function uN(t,n){return dt(1+Rr.count(Yn(t),t),n,3)}function Mx(t,n){return dt(t.getMilliseconds(),n,3)}function aN(t,n){return Mx(t,n)+"000"}function cN(t,n){return dt(t.getMonth()+1,n,2)}function fN(t,n){return dt(t.getMinutes(),n,2)}function sN(t,n){return dt(t.getSeconds(),n,2)}function lN(t){var n=t.getDay();return n===0?7:n}function hN(t,n){return dt(wr.count(Yn(t)-1,t),n,2)}function $x(t){var n=t.getDay();return n>=4||n===0?vr(t):vr.ceil(t)}function dN(t,n){return t=$x(t),dt(vr.count(Yn(t),t)+(Yn(t).getDay()===4),n,2)}function gN(t){return t.getDay()}function pN(t,n){return dt(so.count(Yn(t)-1,t),n,2)}function mN(t,n){return dt(t.getFullYear()%100,n,2)}function yN(t,n){return t=$x(t),dt(t.getFullYear()%100,n,2)}function bN(t,n){return dt(t.getFullYear()%1e4,n,4)}function wN(t,n){var e=t.getDay();return t=e>=4||e===0?vr(t):vr.ceil(t),dt(t.getFullYear()%1e4,n,4)}function vN(t){var n=t.getTimezoneOffset();return(n>0?"-":(n*=-1,"+"))+dt(n/60|0,"0",2)+dt(n%60,"0",2)}function Gy(t,n){return dt(t.getUTCDate(),n,2)}function xN(t,n){return dt(t.getUTCHours(),n,2)}function _N(t,n){return dt(t.getUTCHours()%12||12,n,2)}function MN(t,n){return dt(1+va.count(Un(t),t),n,3)}function Ax(t,n){return dt(t.getUTCMilliseconds(),n,3)}function $N(t,n){return Ax(t,n)+"000"}function AN(t,n){return dt(t.getUTCMonth()+1,n,2)}function SN(t,n){return dt(t.getUTCMinutes(),n,2)}function TN(t,n){return dt(t.getUTCSeconds(),n,2)}function EN(t){var n=t.getUTCDay();return n===0?7:n}function kN(t,n){return dt(xr.count(Un(t)-1,t),n,2)}function Sx(t){var n=t.getUTCDay();return n>=4||n===0?_r(t):_r.ceil(t)}function NN(t,n){return t=Sx(t),dt(_r.count(Un(t),t)+(Un(t).getUTCDay()===4),n,2)}function CN(t){return t.getUTCDay()}function RN(t,n){return dt(lo.count(Un(t)-1,t),n,2)}function IN(t,n){return dt(t.getUTCFullYear()%100,n,2)}function zN(t,n){return t=Sx(t),dt(t.getUTCFullYear()%100,n,2)}function LN(t,n){return dt(t.getUTCFullYear()%1e4,n,4)}function PN(t,n){var e=t.getUTCDay();return t=e>=4||e===0?_r(t):_r.ceil(t),dt(t.getUTCFullYear()%1e4,n,4)}function DN(){return"+0000"}function Vy(){return"%"}function jy(t){return+t}function Zy(t){return Math.floor(+t/1e3)}var Ii,ps,Tx,bi,W0;Ex({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]});function Ex(t){return Ii=_x(t),ps=Ii.format,Tx=Ii.parse,bi=Ii.utcFormat,W0=Ii.utcParse,Ii}var kx="%Y-%m-%dT%H:%M:%S.%LZ";function ON(t){return t.toISOString()}var FN=Date.prototype.toISOString?ON:bi(kx);function BN(t){var n=new Date(t);return isNaN(n)?null:n}var qN=+new Date("2000-01-01T00:00:00.000Z")?BN:W0(kx);function YN(t){return new Date(t)}function UN(t){return t instanceof Date?+t:+new Date(+t)}function H0(t,n,e,r,i,o,u,a,c,f){var s=S0(),h=s.invert,l=s.domain,d=f(".%L"),p=f(":%S"),m=f("%I:%M"),y=f("%I %p"),b=f("%a %d"),w=f("%b %d"),v=f("%B"),g=f("%Y");function x(_){return(c(_)<_?d:a(_)<_?p:u(_)<_?m:o(_)<_?y:r(_)<_?i(_)<_?b:w:e(_)<_?v:g)(_)}return s.invert=function(_){return new Date(h(_))},s.domain=function(_){return arguments.length?l(Array.from(_,UN)):l().map(YN)},s.ticks=function(_){var M=l();return t(M[0],M[M.length-1],_??10)},s.tickFormat=function(_,M){return M==null?x:f(M)},s.nice=function(_){var M=l();return(!_||typeof _.range!="function")&&(_=n(M[0],M[M.length-1],_??10)),_?l(px(M,_)):s},s.copy=function(){return wa(s,H0(t,n,e,r,i,o,u,a,c,f))},s}function Nx(){return Hn.apply(H0(vx,xx,Yn,zo,wr,Rr,Ro,No,Nn,ps).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function Cx(){return Hn.apply(H0(wx,X0,Un,Lo,xr,va,Io,Co,Nn,bi).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function ms(){var t=0,n=1,e,r,i,o,u=hn,a=!1,c;function f(h){return h==null||isNaN(h=+h)?c:u(i===0?.5:(h=(o(h)-e)*i,a?Math.max(0,Math.min(1,h)):h))}f.domain=function(h){return arguments.length?([t,n]=h,e=o(t=+t),r=o(n=+n),i=e===r?0:1/(r-e),f):[t,n]},f.clamp=function(h){return arguments.length?(a=!!h,f):a},f.interpolator=function(h){return arguments.length?(u=h,f):u};function s(h){return function(l){var d,p;return arguments.length?([d,p]=l,u=h(d,p),f):[u(0),u(1)]}}return f.range=s(Nr),f.rangeRound=s(aa),f.unknown=function(h){return arguments.length?(c=h,f):c},function(h){return o=h,e=h(t),r=h(n),i=e===r?0:1/(r-e),f}}function Ir(t,n){return n.domain(t.domain()).interpolator(t.interpolator()).clamp(t.clamp()).unknown(t.unknown())}function Rx(){var t=Cr(ms()(hn));return t.copy=function(){return Ir(t,Rx())},Ke.apply(t,arguments)}function Ix(){var t=E0(ms()).domain([1,10]);return t.copy=function(){return Ir(t,Ix()).base(t.base())},Ke.apply(t,arguments)}function zx(){var t=N0(ms());return t.copy=function(){return Ir(t,zx()).constant(t.constant())},Ke.apply(t,arguments)}function G0(){var t=R0(ms());return t.copy=function(){return Ir(t,G0()).exponent(t.exponent())},Ke.apply(t,arguments)}function XN(){return G0.apply(null,arguments).exponent(.5)}function Lx(){var t=[],n=hn;function e(r){if(r!=null&&!isNaN(r=+r))return n((He(t,r,1)-1)/(t.length-1))}return e.domain=function(r){if(!arguments.length)return t.slice();t=[];for(let i of r)i!=null&&!isNaN(i=+i)&&t.push(i);return t.sort(yt),e},e.interpolator=function(r){return arguments.length?(n=r,e):n},e.range=function(){return t.map((r,i)=>n(i/(t.length-1)))},e.quantiles=function(r){return Array.from({length:r+1},(i,o)=>pe(t,o/r))},e.copy=function(){return Lx(n).domain(t)},Ke.apply(e,arguments)}function ys(){var t=0,n=.5,e=1,r=1,i,o,u,a,c,f=hn,s,h=!1,l;function d(m){return isNaN(m=+m)?l:(m=.5+((m=+s(m))-o)*(r*m<r*o?a:c),f(h?Math.max(0,Math.min(1,m)):m))}d.domain=function(m){return arguments.length?([t,n,e]=m,i=s(t=+t),o=s(n=+n),u=s(e=+e),a=i===o?0:.5/(o-i),c=o===u?0:.5/(u-o),r=o<i?-1:1,d):[t,n,e]},d.clamp=function(m){return arguments.length?(h=!!m,d):h},d.interpolator=function(m){return arguments.length?(f=m,d):f};function p(m){return function(y){var b,w,v;return arguments.length?([b,w,v]=y,f=ca(m,[b,w,v]),d):[f(0),f(.5),f(1)]}}return d.range=p(Nr),d.rangeRound=p(aa),d.unknown=function(m){return arguments.length?(l=m,d):l},function(m){return s=m,i=m(t),o=m(n),u=m(e),a=i===o?0:.5/(o-i),c=o===u?0:.5/(u-o),r=o<i?-1:1,d}}function V0(){var t=Cr(ys()(hn));return t.copy=function(){return Ir(t,V0())},Ke.apply(t,arguments)}function j0(){var t=E0(ys()).domain([.1,1,10]);return t.copy=function(){return Ir(t,j0()).base(t.base())},Ke.apply(t,arguments)}function Z0(){var t=N0(ys());return t.copy=function(){return Ir(t,Z0()).constant(t.constant())},Ke.apply(t,arguments)}function bs(){var t=R0(ys());return t.copy=function(){return Ir(t,bs()).exponent(t.exponent())},Ke.apply(t,arguments)}function WN(){return bs.apply(null,arguments).exponent(.5)}function ut(t){for(var n=t.length/6|0,e=new Array(n),r=0;r<n;)e[r]="#"+t.slice(r*6,++r*6);return e}const Px=ut("1f77b4ff7f0e2ca02cd627289467bd8c564be377c27f7f7fbcbd2217becf"),Dx=ut("7fc97fbeaed4fdc086ffff99386cb0f0027fbf5b17666666"),Ox=ut("1b9e77d95f027570b3e7298a66a61ee6ab02a6761d666666"),Fx=ut("4269d0efb118ff725c6cc5b03ca951ff8ab7a463f297bbf59c6b4e9498a0"),Bx=ut("a6cee31f78b4b2df8a33a02cfb9a99e31a1cfdbf6fff7f00cab2d66a3d9affff99b15928"),qx=ut("fbb4aeb3cde3ccebc5decbe4fed9a6ffffcce5d8bdfddaecf2f2f2"),Yx=ut("b3e2cdfdcdaccbd5e8f4cae4e6f5c9fff2aef1e2cccccccc"),Ux=ut("e41a1c377eb84daf4a984ea3ff7f00ffff33a65628f781bf999999"),Xx=ut("66c2a5fc8d628da0cbe78ac3a6d854ffd92fe5c494b3b3b3"),Wx=ut("8dd3c7ffffb3bebadafb807280b1d3fdb462b3de69fccde5d9d9d9bc80bdccebc5ffed6f"),Hx=ut("4e79a7f28e2ce1575976b7b259a14fedc949af7aa1ff9da79c755fbab0ab"),wt=t=>lw(t[t.length-1]);var K0=new Array(3).concat("d8b365f5f5f55ab4ac","a6611adfc27d80cdc1018571","a6611adfc27df5f5f580cdc1018571","8c510ad8b365f6e8c3c7eae55ab4ac01665e","8c510ad8b365f6e8c3f5f5f5c7eae55ab4ac01665e","8c510abf812ddfc27df6e8c3c7eae580cdc135978f01665e","8c510abf812ddfc27df6e8c3f5f5f5c7eae580cdc135978f01665e","5430058c510abf812ddfc27df6e8c3c7eae580cdc135978f01665e003c30","5430058c510abf812ddfc27df6e8c3f5f5f5c7eae580cdc135978f01665e003c30").map(ut);const Q0=wt(K0);var J0=new Array(3).concat("af8dc3f7f7f77fbf7b","7b3294c2a5cfa6dba0008837","7b3294c2a5cff7f7f7a6dba0008837","762a83af8dc3e7d4e8d9f0d37fbf7b1b7837","762a83af8dc3e7d4e8f7f7f7d9f0d37fbf7b1b7837","762a839970abc2a5cfe7d4e8d9f0d3a6dba05aae611b7837","762a839970abc2a5cfe7d4e8f7f7f7d9f0d3a6dba05aae611b7837","40004b762a839970abc2a5cfe7d4e8d9f0d3a6dba05aae611b783700441b","40004b762a839970abc2a5cfe7d4e8f7f7f7d9f0d3a6dba05aae611b783700441b").map(ut);const t1=wt(J0);var n1=new Array(3).concat("e9a3c9f7f7f7a1d76a","d01c8bf1b6dab8e1864dac26","d01c8bf1b6daf7f7f7b8e1864dac26","c51b7de9a3c9fde0efe6f5d0a1d76a4d9221","c51b7de9a3c9fde0eff7f7f7e6f5d0a1d76a4d9221","c51b7dde77aef1b6dafde0efe6f5d0b8e1867fbc414d9221","c51b7dde77aef1b6dafde0eff7f7f7e6f5d0b8e1867fbc414d9221","8e0152c51b7dde77aef1b6dafde0efe6f5d0b8e1867fbc414d9221276419","8e0152c51b7dde77aef1b6dafde0eff7f7f7e6f5d0b8e1867fbc414d9221276419").map(ut);const e1=wt(n1);var r1=new Array(3).concat("998ec3f7f7f7f1a340","5e3c99b2abd2fdb863e66101","5e3c99b2abd2f7f7f7fdb863e66101","542788998ec3d8daebfee0b6f1a340b35806","542788998ec3d8daebf7f7f7fee0b6f1a340b35806","5427888073acb2abd2d8daebfee0b6fdb863e08214b35806","5427888073acb2abd2d8daebf7f7f7fee0b6fdb863e08214b35806","2d004b5427888073acb2abd2d8daebfee0b6fdb863e08214b358067f3b08","2d004b5427888073acb2abd2d8daebf7f7f7fee0b6fdb863e08214b358067f3b08").map(ut);const i1=wt(r1);var bf=new Array(3).concat("ef8a62f7f7f767a9cf","ca0020f4a58292c5de0571b0","ca0020f4a582f7f7f792c5de0571b0","b2182bef8a62fddbc7d1e5f067a9cf2166ac","b2182bef8a62fddbc7f7f7f7d1e5f067a9cf2166ac","b2182bd6604df4a582fddbc7d1e5f092c5de4393c32166ac","b2182bd6604df4a582fddbc7f7f7f7d1e5f092c5de4393c32166ac","67001fb2182bd6604df4a582fddbc7d1e5f092c5de4393c32166ac053061","67001fb2182bd6604df4a582fddbc7f7f7f7d1e5f092c5de4393c32166ac053061").map(ut);const qu=wt(bf);var o1=new Array(3).concat("ef8a62ffffff999999","ca0020f4a582bababa404040","ca0020f4a582ffffffbababa404040","b2182bef8a62fddbc7e0e0e09999994d4d4d","b2182bef8a62fddbc7ffffffe0e0e09999994d4d4d","b2182bd6604df4a582fddbc7e0e0e0bababa8787874d4d4d","b2182bd6604df4a582fddbc7ffffffe0e0e0bababa8787874d4d4d","67001fb2182bd6604df4a582fddbc7e0e0e0bababa8787874d4d4d1a1a1a","67001fb2182bd6604df4a582fddbc7ffffffe0e0e0bababa8787874d4d4d1a1a1a").map(ut);const u1=wt(o1);var wf=new Array(3).concat("fc8d59ffffbf91bfdb","d7191cfdae61abd9e92c7bb6","d7191cfdae61ffffbfabd9e92c7bb6","d73027fc8d59fee090e0f3f891bfdb4575b4","d73027fc8d59fee090ffffbfe0f3f891bfdb4575b4","d73027f46d43fdae61fee090e0f3f8abd9e974add14575b4","d73027f46d43fdae61fee090ffffbfe0f3f8abd9e974add14575b4","a50026d73027f46d43fdae61fee090e0f3f8abd9e974add14575b4313695","a50026d73027f46d43fdae61fee090ffffbfe0f3f8abd9e974add14575b4313695").map(ut);const Yu=wt(wf);var a1=new Array(3).concat("fc8d59ffffbf91cf60","d7191cfdae61a6d96a1a9641","d7191cfdae61ffffbfa6d96a1a9641","d73027fc8d59fee08bd9ef8b91cf601a9850","d73027fc8d59fee08bffffbfd9ef8b91cf601a9850","d73027f46d43fdae61fee08bd9ef8ba6d96a66bd631a9850","d73027f46d43fdae61fee08bffffbfd9ef8ba6d96a66bd631a9850","a50026d73027f46d43fdae61fee08bd9ef8ba6d96a66bd631a9850006837","a50026d73027f46d43fdae61fee08bffffbfd9ef8ba6d96a66bd631a9850006837").map(ut);const c1=wt(a1);var f1=new Array(3).concat("fc8d59ffffbf99d594","d7191cfdae61abdda42b83ba","d7191cfdae61ffffbfabdda42b83ba","d53e4ffc8d59fee08be6f59899d5943288bd","d53e4ffc8d59fee08bffffbfe6f59899d5943288bd","d53e4ff46d43fdae61fee08be6f598abdda466c2a53288bd","d53e4ff46d43fdae61fee08bffffbfe6f598abdda466c2a53288bd","9e0142d53e4ff46d43fdae61fee08be6f598abdda466c2a53288bd5e4fa2","9e0142d53e4ff46d43fdae61fee08bffffbfe6f598abdda466c2a53288bd5e4fa2").map(ut);const s1=wt(f1);var l1=new Array(3).concat("e5f5f999d8c92ca25f","edf8fbb2e2e266c2a4238b45","edf8fbb2e2e266c2a42ca25f006d2c","edf8fbccece699d8c966c2a42ca25f006d2c","edf8fbccece699d8c966c2a441ae76238b45005824","f7fcfde5f5f9ccece699d8c966c2a441ae76238b45005824","f7fcfde5f5f9ccece699d8c966c2a441ae76238b45006d2c00441b").map(ut);const h1=wt(l1);var d1=new Array(3).concat("e0ecf49ebcda8856a7","edf8fbb3cde38c96c688419d","edf8fbb3cde38c96c68856a7810f7c","edf8fbbfd3e69ebcda8c96c68856a7810f7c","edf8fbbfd3e69ebcda8c96c68c6bb188419d6e016b","f7fcfde0ecf4bfd3e69ebcda8c96c68c6bb188419d6e016b","f7fcfde0ecf4bfd3e69ebcda8c96c68c6bb188419d810f7c4d004b").map(ut);const g1=wt(d1);var p1=new Array(3).concat("e0f3dba8ddb543a2ca","f0f9e8bae4bc7bccc42b8cbe","f0f9e8bae4bc7bccc443a2ca0868ac","f0f9e8ccebc5a8ddb57bccc443a2ca0868ac","f0f9e8ccebc5a8ddb57bccc44eb3d32b8cbe08589e","f7fcf0e0f3dbccebc5a8ddb57bccc44eb3d32b8cbe08589e","f7fcf0e0f3dbccebc5a8ddb57bccc44eb3d32b8cbe0868ac084081").map(ut);const m1=wt(p1);var y1=new Array(3).concat("fee8c8fdbb84e34a33","fef0d9fdcc8afc8d59d7301f","fef0d9fdcc8afc8d59e34a33b30000","fef0d9fdd49efdbb84fc8d59e34a33b30000","fef0d9fdd49efdbb84fc8d59ef6548d7301f990000","fff7ecfee8c8fdd49efdbb84fc8d59ef6548d7301f990000","fff7ecfee8c8fdd49efdbb84fc8d59ef6548d7301fb300007f0000").map(ut);const b1=wt(y1);var w1=new Array(3).concat("ece2f0a6bddb1c9099","f6eff7bdc9e167a9cf02818a","f6eff7bdc9e167a9cf1c9099016c59","f6eff7d0d1e6a6bddb67a9cf1c9099016c59","f6eff7d0d1e6a6bddb67a9cf3690c002818a016450","fff7fbece2f0d0d1e6a6bddb67a9cf3690c002818a016450","fff7fbece2f0d0d1e6a6bddb67a9cf3690c002818a016c59014636").map(ut);const v1=wt(w1);var x1=new Array(3).concat("ece7f2a6bddb2b8cbe","f1eef6bdc9e174a9cf0570b0","f1eef6bdc9e174a9cf2b8cbe045a8d","f1eef6d0d1e6a6bddb74a9cf2b8cbe045a8d","f1eef6d0d1e6a6bddb74a9cf3690c00570b0034e7b","fff7fbece7f2d0d1e6a6bddb74a9cf3690c00570b0034e7b","fff7fbece7f2d0d1e6a6bddb74a9cf3690c00570b0045a8d023858").map(ut);const _1=wt(x1);var M1=new Array(3).concat("e7e1efc994c7dd1c77","f1eef6d7b5d8df65b0ce1256","f1eef6d7b5d8df65b0dd1c77980043","f1eef6d4b9dac994c7df65b0dd1c77980043","f1eef6d4b9dac994c7df65b0e7298ace125691003f","f7f4f9e7e1efd4b9dac994c7df65b0e7298ace125691003f","f7f4f9e7e1efd4b9dac994c7df65b0e7298ace125698004367001f").map(ut);const $1=wt(M1);var A1=new Array(3).concat("fde0ddfa9fb5c51b8a","feebe2fbb4b9f768a1ae017e","feebe2fbb4b9f768a1c51b8a7a0177","feebe2fcc5c0fa9fb5f768a1c51b8a7a0177","feebe2fcc5c0fa9fb5f768a1dd3497ae017e7a0177","fff7f3fde0ddfcc5c0fa9fb5f768a1dd3497ae017e7a0177","fff7f3fde0ddfcc5c0fa9fb5f768a1dd3497ae017e7a017749006a").map(ut);const S1=wt(A1);var T1=new Array(3).concat("edf8b17fcdbb2c7fb8","ffffcca1dab441b6c4225ea8","ffffcca1dab441b6c42c7fb8253494","ffffccc7e9b47fcdbb41b6c42c7fb8253494","ffffccc7e9b47fcdbb41b6c41d91c0225ea80c2c84","ffffd9edf8b1c7e9b47fcdbb41b6c41d91c0225ea80c2c84","ffffd9edf8b1c7e9b47fcdbb41b6c41d91c0225ea8253494081d58").map(ut);const E1=wt(T1);var k1=new Array(3).concat("f7fcb9addd8e31a354","ffffccc2e69978c679238443","ffffccc2e69978c67931a354006837","ffffccd9f0a3addd8e78c67931a354006837","ffffccd9f0a3addd8e78c67941ab5d238443005a32","ffffe5f7fcb9d9f0a3addd8e78c67941ab5d238443005a32","ffffe5f7fcb9d9f0a3addd8e78c67941ab5d238443006837004529").map(ut);const N1=wt(k1);var C1=new Array(3).concat("fff7bcfec44fd95f0e","ffffd4fed98efe9929cc4c02","ffffd4fed98efe9929d95f0e993404","ffffd4fee391fec44ffe9929d95f0e993404","ffffd4fee391fec44ffe9929ec7014cc4c028c2d04","ffffe5fff7bcfee391fec44ffe9929ec7014cc4c028c2d04","ffffe5fff7bcfee391fec44ffe9929ec7014cc4c02993404662506").map(ut);const R1=wt(C1);var I1=new Array(3).concat("ffeda0feb24cf03b20","ffffb2fecc5cfd8d3ce31a1c","ffffb2fecc5cfd8d3cf03b20bd0026","ffffb2fed976feb24cfd8d3cf03b20bd0026","ffffb2fed976feb24cfd8d3cfc4e2ae31a1cb10026","ffffccffeda0fed976feb24cfd8d3cfc4e2ae31a1cb10026","ffffccffeda0fed976feb24cfd8d3cfc4e2ae31a1cbd0026800026").map(ut);const z1=wt(I1);var L1=new Array(3).concat("deebf79ecae13182bd","eff3ffbdd7e76baed62171b5","eff3ffbdd7e76baed63182bd08519c","eff3ffc6dbef9ecae16baed63182bd08519c","eff3ffc6dbef9ecae16baed64292c62171b5084594","f7fbffdeebf7c6dbef9ecae16baed64292c62171b5084594","f7fbffdeebf7c6dbef9ecae16baed64292c62171b508519c08306b").map(ut);const P1=wt(L1);var D1=new Array(3).concat("e5f5e0a1d99b31a354","edf8e9bae4b374c476238b45","edf8e9bae4b374c47631a354006d2c","edf8e9c7e9c0a1d99b74c47631a354006d2c","edf8e9c7e9c0a1d99b74c47641ab5d238b45005a32","f7fcf5e5f5e0c7e9c0a1d99b74c47641ab5d238b45005a32","f7fcf5e5f5e0c7e9c0a1d99b74c47641ab5d238b45006d2c00441b").map(ut);const O1=wt(D1);var F1=new Array(3).concat("f0f0f0bdbdbd636363","f7f7f7cccccc969696525252","f7f7f7cccccc969696636363252525","f7f7f7d9d9d9bdbdbd969696636363252525","f7f7f7d9d9d9bdbdbd969696737373525252252525","fffffff0f0f0d9d9d9bdbdbd969696737373525252252525","fffffff0f0f0d9d9d9bdbdbd969696737373525252252525000000").map(ut);const B1=wt(F1);var q1=new Array(3).concat("efedf5bcbddc756bb1","f2f0f7cbc9e29e9ac86a51a3","f2f0f7cbc9e29e9ac8756bb154278f","f2f0f7dadaebbcbddc9e9ac8756bb154278f","f2f0f7dadaebbcbddc9e9ac8807dba6a51a34a1486","fcfbfdefedf5dadaebbcbddc9e9ac8807dba6a51a34a1486","fcfbfdefedf5dadaebbcbddc9e9ac8807dba6a51a354278f3f007d").map(ut);const Y1=wt(q1);var U1=new Array(3).concat("fee0d2fc9272de2d26","fee5d9fcae91fb6a4acb181d","fee5d9fcae91fb6a4ade2d26a50f15","fee5d9fcbba1fc9272fb6a4ade2d26a50f15","fee5d9fcbba1fc9272fb6a4aef3b2ccb181d99000d","fff5f0fee0d2fcbba1fc9272fb6a4aef3b2ccb181d99000d","fff5f0fee0d2fcbba1fc9272fb6a4aef3b2ccb181da50f1567000d").map(ut);const X1=wt(U1);var W1=new Array(3).concat("fee6cefdae6be6550d","feeddefdbe85fd8d3cd94701","feeddefdbe85fd8d3ce6550da63603","feeddefdd0a2fdae6bfd8d3ce6550da63603","feeddefdd0a2fdae6bfd8d3cf16913d948018c2d04","fff5ebfee6cefdd0a2fdae6bfd8d3cf16913d948018c2d04","fff5ebfee6cefdd0a2fdae6bfd8d3cf16913d94801a636037f2704").map(ut);const H1=wt(W1);function G1(t){return t=Math.max(0,Math.min(1,t)),"rgb("+Math.max(0,Math.min(255,Math.round(-4.54-t*(35.34-t*(2381.73-t*(6402.7-t*(7024.72-t*2710.57)))))))+", "+Math.max(0,Math.min(255,Math.round(32.49+t*(170.73+t*(52.82-t*(131.46-t*(176.58-t*67.37)))))))+", "+Math.max(0,Math.min(255,Math.round(81.24+t*(442.36-t*(2482.43-t*(6167.24-t*(6614.94-t*2475.67)))))))+")"}const V1=ts(Jn(300,.5,0),Jn(-240,.5,1));var j1=ts(Jn(-100,.75,.35),Jn(80,1.5,.8)),Z1=ts(Jn(260,.75,.35),Jn(80,1.5,.8)),ec=Jn();function K1(t){(t<0||t>1)&&(t-=Math.floor(t));var n=Math.abs(t-.5);return ec.h=360*t-100,ec.s=1.5-1.5*n,ec.l=.8-.9*n,ec+""}var rc=Qn(),HN=Math.PI/3,GN=Math.PI*2/3;function Q1(t){var n;return t=(.5-t)*Math.PI,rc.r=255*(n=Math.sin(t))*n,rc.g=255*(n=Math.sin(t+HN))*n,rc.b=255*(n=Math.sin(t+GN))*n,rc+""}function J1(t){return t=Math.max(0,Math.min(1,t)),"rgb("+Math.max(0,Math.min(255,Math.round(34.61+t*(1172.33-t*(10793.56-t*(33300.12-t*(38394.49-t*14825.05)))))))+", "+Math.max(0,Math.min(255,Math.round(23.31+t*(557.33+t*(1225.33-t*(3574.96-t*(1073.77+t*707.56)))))))+", "+Math.max(0,Math.min(255,Math.round(27.2+t*(3211.1-t*(15327.97-t*(27814-t*(22569.18-t*6838.66)))))))+")"}function ws(t){var n=t.length;return function(e){return t[Math.max(0,Math.min(n-1,Math.floor(e*n)))]}}const tg=ws(ut("44015444025645045745055946075a46085c460a5d460b5e470d60470e6147106347116447136548146748166848176948186a481a6c481b6d481c6e481d6f481f70482071482173482374482475482576482677482878482979472a7a472c7a472d7b472e7c472f7d46307e46327e46337f463480453581453781453882443983443a83443b84433d84433e85423f854240864241864142874144874045884046883f47883f48893e49893e4a893e4c8a3d4d8a3d4e8a3c4f8a3c508b3b518b3b528b3a538b3a548c39558c39568c38588c38598c375a8c375b8d365c8d365d8d355e8d355f8d34608d34618d33628d33638d32648e32658e31668e31678e31688e30698e306a8e2f6b8e2f6c8e2e6d8e2e6e8e2e6f8e2d708e2d718e2c718e2c728e2c738e2b748e2b758e2a768e2a778e2a788e29798e297a8e297b8e287c8e287d8e277e8e277f8e27808e26818e26828e26828e25838e25848e25858e24868e24878e23888e23898e238a8d228b8d228c8d228d8d218e8d218f8d21908d21918c20928c20928c20938c1f948c1f958b1f968b1f978b1f988b1f998a1f9a8a1e9b8a1e9c891e9d891f9e891f9f881fa0881fa1881fa1871fa28720a38620a48621a58521a68522a78522a88423a98324aa8325ab8225ac8226ad8127ad8128ae8029af7f2ab07f2cb17e2db27d2eb37c2fb47c31b57b32b67a34b67935b77937b87838b9773aba763bbb753dbc743fbc7340bd7242be7144bf7046c06f48c16e4ac16d4cc26c4ec36b50c46a52c56954c56856c66758c7655ac8645cc8635ec96260ca6063cb5f65cb5e67cc5c69cd5b6ccd5a6ece5870cf5773d05675d05477d1537ad1517cd2507fd34e81d34d84d44b86d54989d5488bd6468ed64590d74393d74195d84098d83e9bd93c9dd93ba0da39a2da37a5db36a8db34aadc32addc30b0dd2fb2dd2db5de2bb8de29bade28bddf26c0df25c2df23c5e021c8e020cae11fcde11dd0e11cd2e21bd5e21ad8e219dae319dde318dfe318e2e418e5e419e7e419eae51aece51befe51cf1e51df4e61ef6e620f8e621fbe723fde725"));var ng=ws(ut("00000401000501010601010802010902020b02020d03030f03031204041405041606051806051a07061c08071e0907200a08220b09240c09260d0a290e0b2b100b2d110c2f120d31130d34140e36150e38160f3b180f3d19103f1a10421c10441d11471e114920114b21114e22115024125325125527125829115a2a115c2c115f2d11612f116331116533106734106936106b38106c390f6e3b0f703d0f713f0f72400f74420f75440f764510774710784910784a10794c117a4e117b4f127b51127c52137c54137d56147d57157e59157e5a167e5c167f5d177f5f187f601880621980641a80651a80671b80681c816a1c816b1d816d1d816e1e81701f81721f817320817521817621817822817922827b23827c23827e24828025828125818326818426818627818827818928818b29818c29818e2a81902a81912b81932b80942c80962c80982d80992d809b2e7f9c2e7f9e2f7fa02f7fa1307ea3307ea5317ea6317da8327daa337dab337cad347cae347bb0357bb2357bb3367ab5367ab73779b83779ba3878bc3978bd3977bf3a77c03a76c23b75c43c75c53c74c73d73c83e73ca3e72cc3f71cd4071cf4070d0416fd2426fd3436ed5446dd6456cd8456cd9466bdb476adc4869de4968df4a68e04c67e24d66e34e65e44f64e55064e75263e85362e95462ea5661eb5760ec5860ed5a5fee5b5eef5d5ef05f5ef1605df2625df2645cf3655cf4675cf4695cf56b5cf66c5cf66e5cf7705cf7725cf8745cf8765cf9785df9795df97b5dfa7d5efa7f5efa815ffb835ffb8560fb8761fc8961fc8a62fc8c63fc8e64fc9065fd9266fd9467fd9668fd9869fd9a6afd9b6bfe9d6cfe9f6dfea16efea36ffea571fea772fea973feaa74feac76feae77feb078feb27afeb47bfeb67cfeb77efeb97ffebb81febd82febf84fec185fec287fec488fec68afec88cfeca8dfecc8ffecd90fecf92fed194fed395fed597fed799fed89afdda9cfddc9efddea0fde0a1fde2a3fde3a5fde5a7fde7a9fde9aafdebacfcecaefceeb0fcf0b2fcf2b4fcf4b6fcf6b8fcf7b9fcf9bbfcfbbdfcfdbf")),eg=ws(ut("00000401000501010601010802010a02020c02020e03021004031204031405041706041907051b08051d09061f0a07220b07240c08260d08290e092b10092d110a30120a32140b34150b37160b39180c3c190c3e1b0c411c0c431e0c451f0c48210c4a230c4c240c4f260c51280b53290b552b0b572d0b592f0a5b310a5c320a5e340a5f3609613809623909633b09643d09653e0966400a67420a68440a68450a69470b6a490b6a4a0c6b4c0c6b4d0d6c4f0d6c510e6c520e6d540f6d550f6d57106e59106e5a116e5c126e5d126e5f136e61136e62146e64156e65156e67166e69166e6a176e6c186e6d186e6f196e71196e721a6e741a6e751b6e771c6d781c6d7a1d6d7c1d6d7d1e6d7f1e6c801f6c82206c84206b85216b87216b88226a8a226a8c23698d23698f24699025689225689326679526679727669827669a28659b29649d29649f2a63a02a63a22b62a32c61a52c60a62d60a82e5fa92e5eab2f5ead305dae305cb0315bb1325ab3325ab43359b63458b73557b93556ba3655bc3754bd3853bf3952c03a51c13a50c33b4fc43c4ec63d4dc73e4cc83f4bca404acb4149cc4248ce4347cf4446d04545d24644d34743d44842d54a41d74b3fd84c3ed94d3dda4e3cdb503bdd513ade5238df5337e05536e15635e25734e35933e45a31e55c30e65d2fe75e2ee8602de9612bea632aeb6429eb6628ec6726ed6925ee6a24ef6c23ef6e21f06f20f1711ff1731df2741cf3761bf37819f47918f57b17f57d15f67e14f68013f78212f78410f8850ff8870ef8890cf98b0bf98c0af98e09fa9008fa9207fa9407fb9606fb9706fb9906fb9b06fb9d07fc9f07fca108fca309fca50afca60cfca80dfcaa0ffcac11fcae12fcb014fcb216fcb418fbb61afbb81dfbba1ffbbc21fbbe23fac026fac228fac42afac62df9c72ff9c932f9cb35f8cd37f8cf3af7d13df7d340f6d543f6d746f5d949f5db4cf4dd4ff4df53f4e156f3e35af3e55df2e661f2e865f2ea69f1ec6df1ed71f1ef75f1f179f2f27df2f482f3f586f3f68af4f88ef5f992f6fa96f8fb9af9fc9dfafda1fcffa4")),rg=ws(ut("0d088710078813078916078a19068c1b068d1d068e20068f2206902406912605912805922a05932c05942e05952f059631059733059735049837049938049a3a049a3c049b3e049c3f049c41049d43039e44039e46039f48039f4903a04b03a14c02a14e02a25002a25102a35302a35502a45601a45801a45901a55b01a55c01a65e01a66001a66100a76300a76400a76600a76700a86900a86a00a86c00a86e00a86f00a87100a87201a87401a87501a87701a87801a87a02a87b02a87d03a87e03a88004a88104a78305a78405a78606a68707a68808a68a09a58b0aa58d0ba58e0ca48f0da4910ea3920fa39410a29511a19613a19814a099159f9a169f9c179e9d189d9e199da01a9ca11b9ba21d9aa31e9aa51f99a62098a72197a82296aa2395ab2494ac2694ad2793ae2892b02991b12a90b22b8fb32c8eb42e8db52f8cb6308bb7318ab83289ba3388bb3488bc3587bd3786be3885bf3984c03a83c13b82c23c81c33d80c43e7fc5407ec6417dc7427cc8437bc9447aca457acb4679cc4778cc4977cd4a76ce4b75cf4c74d04d73d14e72d24f71d35171d45270d5536fd5546ed6556dd7566cd8576bd9586ada5a6ada5b69db5c68dc5d67dd5e66de5f65de6164df6263e06363e16462e26561e26660e3685fe4695ee56a5de56b5de66c5ce76e5be76f5ae87059e97158e97257ea7457eb7556eb7655ec7754ed7953ed7a52ee7b51ef7c51ef7e50f07f4ff0804ef1814df1834cf2844bf3854bf3874af48849f48948f58b47f58c46f68d45f68f44f79044f79143f79342f89441f89540f9973ff9983ef99a3efa9b3dfa9c3cfa9e3bfb9f3afba139fba238fca338fca537fca636fca835fca934fdab33fdac33fdae32fdaf31fdb130fdb22ffdb42ffdb52efeb72dfeb82cfeba2cfebb2bfebd2afebe2afec029fdc229fdc328fdc527fdc627fdc827fdca26fdcb26fccd25fcce25fcd025fcd225fbd324fbd524fbd724fad824fada24f9dc24f9dd25f8df25f8e125f7e225f7e425f6e626f6e826f5e926f5eb27f4ed27f3ee27f3f027f2f227f1f426f1f525f0f724f0f921"));function ot(t){return function(){return t}}const Ky=Math.abs,en=Math.atan2,ze=Math.cos,VN=Math.max,ji=Math.min,xn=Math.sin,Mt=Math.sqrt,rn=1e-12,Mr=Math.PI,vf=Mr/2,hr=2*Mr;function jN(t){return t>1?0:t<-1?Mr:Math.acos(t)}function Qy(t){return t>=1?vf:t<=-1?-vf:Math.asin(t)}function _a(t){let n=3;return t.digits=function(e){if(!arguments.length)return n;if(e==null)n=null;else{const r=Math.floor(e);if(!(r>=0))throw new RangeError(`invalid digits: ${e}`);n=r}return t},()=>new sa(n)}function ZN(t){return t.innerRadius}function KN(t){return t.outerRadius}function QN(t){return t.startAngle}function JN(t){return t.endAngle}function tC(t){return t&&t.padAngle}function nC(t,n,e,r,i,o,u,a){var c=e-t,f=r-n,s=u-i,h=a-o,l=h*c-s*f;if(!(l*l<rn))return l=(s*(n-o)-h*(t-i))/l,[t+l*c,n+l*f]}function ic(t,n,e,r,i,o,u){var a=t-e,c=n-r,f=(u?o:-o)/Mt(a*a+c*c),s=f*c,h=-f*a,l=t+s,d=n+h,p=e+s,m=r+h,y=(l+p)/2,b=(d+m)/2,w=p-l,v=m-d,g=w*w+v*v,x=i-o,_=l*m-p*d,M=(v<0?-1:1)*Mt(VN(0,x*x*g-_*_)),A=(_*v-w*M)/g,T=(-_*w-v*M)/g,E=(_*v+w*M)/g,S=(-_*w+v*M)/g,R=A-y,C=T-b,$=E-y,N=S-b;return R*R+C*C>$*$+N*N&&(A=E,T=S),{cx:A,cy:T,x01:-s,y01:-h,x11:A*(i/x-1),y11:T*(i/x-1)}}function eC(){var t=ZN,n=KN,e=ot(0),r=null,i=QN,o=JN,u=tC,a=null,c=_a(f);function f(){var s,h,l=+t.apply(this,arguments),d=+n.apply(this,arguments),p=i.apply(this,arguments)-vf,m=o.apply(this,arguments)-vf,y=Ky(m-p),b=m>p;if(a||(a=s=c()),d<l&&(h=d,d=l,l=h),!(d>rn))a.moveTo(0,0);else if(y>hr-rn)a.moveTo(d*ze(p),d*xn(p)),a.arc(0,0,d,p,m,!b),l>rn&&(a.moveTo(l*ze(m),l*xn(m)),a.arc(0,0,l,m,p,b));else{var w=p,v=m,g=p,x=m,_=y,M=y,A=u.apply(this,arguments)/2,T=A>rn&&(r?+r.apply(this,arguments):Mt(l*l+d*d)),E=ji(Ky(d-l)/2,+e.apply(this,arguments)),S=E,R=E,C,$;if(T>rn){var N=Qy(T/l*xn(A)),k=Qy(T/d*xn(A));(_-=N*2)>rn?(N*=b?1:-1,g+=N,x-=N):(_=0,g=x=(p+m)/2),(M-=k*2)>rn?(k*=b?1:-1,w+=k,v-=k):(M=0,w=v=(p+m)/2)}var I=d*ze(w),L=d*xn(w),z=l*ze(x),P=l*xn(x);if(E>rn){var D=d*ze(v),B=d*xn(v),Y=l*ze(g),U=l*xn(g),W;if(y<Mr)if(W=nC(I,L,Y,U,D,B,z,P)){var Z=I-W[0],q=L-W[1],H=D-W[0],F=B-W[1],X=1/xn(jN((Z*H+q*F)/(Mt(Z*Z+q*q)*Mt(H*H+F*F)))/2),G=Mt(W[0]*W[0]+W[1]*W[1]);S=ji(E,(l-G)/(X-1)),R=ji(E,(d-G)/(X+1))}else S=R=0}M>rn?R>rn?(C=ic(Y,U,I,L,d,R,b),$=ic(D,B,z,P,d,R,b),a.moveTo(C.cx+C.x01,C.cy+C.y01),R<E?a.arc(C.cx,C.cy,R,en(C.y01,C.x01),en($.y01,$.x01),!b):(a.arc(C.cx,C.cy,R,en(C.y01,C.x01),en(C.y11,C.x11),!b),a.arc(0,0,d,en(C.cy+C.y11,C.cx+C.x11),en($.cy+$.y11,$.cx+$.x11),!b),a.arc($.cx,$.cy,R,en($.y11,$.x11),en($.y01,$.x01),!b))):(a.moveTo(I,L),a.arc(0,0,d,w,v,!b)):a.moveTo(I,L),!(l>rn)||!(_>rn)?a.lineTo(z,P):S>rn?(C=ic(z,P,D,B,l,-S,b),$=ic(I,L,Y,U,l,-S,b),a.lineTo(C.cx+C.x01,C.cy+C.y01),S<E?a.arc(C.cx,C.cy,S,en(C.y01,C.x01),en($.y01,$.x01),!b):(a.arc(C.cx,C.cy,S,en(C.y01,C.x01),en(C.y11,C.x11),!b),a.arc(0,0,l,en(C.cy+C.y11,C.cx+C.x11),en($.cy+$.y11,$.cx+$.x11),b),a.arc($.cx,$.cy,S,en($.y11,$.x11),en($.y01,$.x01),!b))):a.arc(0,0,l,x,g,b)}if(a.closePath(),s)return a=null,s+""||null}return f.centroid=function(){var s=(+t.apply(this,arguments)+ +n.apply(this,arguments))/2,h=(+i.apply(this,arguments)+ +o.apply(this,arguments))/2-Mr/2;return[ze(h)*s,xn(h)*s]},f.innerRadius=function(s){return arguments.length?(t=typeof s=="function"?s:ot(+s),f):t},f.outerRadius=function(s){return arguments.length?(n=typeof s=="function"?s:ot(+s),f):n},f.cornerRadius=function(s){return arguments.length?(e=typeof s=="function"?s:ot(+s),f):e},f.padRadius=function(s){return arguments.length?(r=s==null?null:typeof s=="function"?s:ot(+s),f):r},f.startAngle=function(s){return arguments.length?(i=typeof s=="function"?s:ot(+s),f):i},f.endAngle=function(s){return arguments.length?(o=typeof s=="function"?s:ot(+s),f):o},f.padAngle=function(s){return arguments.length?(u=typeof s=="function"?s:ot(+s),f):u},f.context=function(s){return arguments.length?(a=s??null,f):a},f}var rC=Array.prototype.slice;function vs(t){return typeof t=="object"&&"length"in t?t:Array.from(t)}function Gx(t){this._context=t}Gx.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2;default:this._context.lineTo(t,n);break}}};function wi(t){return new Gx(t)}function ig(t){return t[0]}function og(t){return t[1]}function xs(t,n){var e=ot(!0),r=null,i=wi,o=null,u=_a(a);t=typeof t=="function"?t:t===void 0?ig:ot(t),n=typeof n=="function"?n:n===void 0?og:ot(n);function a(c){var f,s=(c=vs(c)).length,h,l=!1,d;for(r==null&&(o=i(d=u())),f=0;f<=s;++f)!(f<s&&e(h=c[f],f,c))===l&&((l=!l)?o.lineStart():o.lineEnd()),l&&o.point(+t(h,f,c),+n(h,f,c));if(d)return o=null,d+""||null}return a.x=function(c){return arguments.length?(t=typeof c=="function"?c:ot(+c),a):t},a.y=function(c){return arguments.length?(n=typeof c=="function"?c:ot(+c),a):n},a.defined=function(c){return arguments.length?(e=typeof c=="function"?c:ot(!!c),a):e},a.curve=function(c){return arguments.length?(i=c,r!=null&&(o=i(r)),a):i},a.context=function(c){return arguments.length?(c==null?r=o=null:o=i(r=c),a):r},a}function Ma(t,n,e){var r=null,i=ot(!0),o=null,u=wi,a=null,c=_a(f);t=typeof t=="function"?t:t===void 0?ig:ot(+t),n=typeof n=="function"?n:ot(n===void 0?0:+n),e=typeof e=="function"?e:e===void 0?og:ot(+e);function f(h){var l,d,p,m=(h=vs(h)).length,y,b=!1,w,v=new Array(m),g=new Array(m);for(o==null&&(a=u(w=c())),l=0;l<=m;++l){if(!(l<m&&i(y=h[l],l,h))===b)if(b=!b)d=l,a.areaStart(),a.lineStart();else{for(a.lineEnd(),a.lineStart(),p=l-1;p>=d;--p)a.point(v[p],g[p]);a.lineEnd(),a.areaEnd()}b&&(v[l]=+t(y,l,h),g[l]=+n(y,l,h),a.point(r?+r(y,l,h):v[l],e?+e(y,l,h):g[l]))}if(w)return a=null,w+""||null}function s(){return xs().defined(i).curve(u).context(o)}return f.x=function(h){return arguments.length?(t=typeof h=="function"?h:ot(+h),r=null,f):t},f.x0=function(h){return arguments.length?(t=typeof h=="function"?h:ot(+h),f):t},f.x1=function(h){return arguments.length?(r=h==null?null:typeof h=="function"?h:ot(+h),f):r},f.y=function(h){return arguments.length?(n=typeof h=="function"?h:ot(+h),e=null,f):n},f.y0=function(h){return arguments.length?(n=typeof h=="function"?h:ot(+h),f):n},f.y1=function(h){return arguments.length?(e=h==null?null:typeof h=="function"?h:ot(+h),f):e},f.lineX0=f.lineY0=function(){return s().x(t).y(n)},f.lineY1=function(){return s().x(t).y(e)},f.lineX1=function(){return s().x(r).y(n)},f.defined=function(h){return arguments.length?(i=typeof h=="function"?h:ot(!!h),f):i},f.curve=function(h){return arguments.length?(u=h,o!=null&&(a=u(o)),f):u},f.context=function(h){return arguments.length?(h==null?o=a=null:a=u(o=h),f):o},f}function iC(t,n){return n<t?-1:n>t?1:n>=t?0:NaN}function oC(t){return t}function uC(){var t=oC,n=iC,e=null,r=ot(0),i=ot(hr),o=ot(0);function u(a){var c,f=(a=vs(a)).length,s,h,l=0,d=new Array(f),p=new Array(f),m=+r.apply(this,arguments),y=Math.min(hr,Math.max(-hr,i.apply(this,arguments)-m)),b,w=Math.min(Math.abs(y)/f,o.apply(this,arguments)),v=w*(y<0?-1:1),g;for(c=0;c<f;++c)(g=p[d[c]=c]=+t(a[c],c,a))>0&&(l+=g);for(n!=null?d.sort(function(x,_){return n(p[x],p[_])}):e!=null&&d.sort(function(x,_){return e(a[x],a[_])}),c=0,h=l?(y-f*v)/l:0;c<f;++c,m=b)s=d[c],g=p[s],b=m+(g>0?g*h:0)+v,p[s]={data:a[s],index:c,value:g,startAngle:m,endAngle:b,padAngle:w};return p}return u.value=function(a){return arguments.length?(t=typeof a=="function"?a:ot(+a),u):t},u.sortValues=function(a){return arguments.length?(n=a,e=null,u):n},u.sort=function(a){return arguments.length?(e=a,n=null,u):e},u.startAngle=function(a){return arguments.length?(r=typeof a=="function"?a:ot(+a),u):r},u.endAngle=function(a){return arguments.length?(i=typeof a=="function"?a:ot(+a),u):i},u.padAngle=function(a){return arguments.length?(o=typeof a=="function"?a:ot(+a),u):o},u}var Vx=ug(wi);function jx(t){this._curve=t}jx.prototype={areaStart:function(){this._curve.areaStart()},areaEnd:function(){this._curve.areaEnd()},lineStart:function(){this._curve.lineStart()},lineEnd:function(){this._curve.lineEnd()},point:function(t,n){this._curve.point(n*Math.sin(t),n*-Math.cos(t))}};function ug(t){function n(e){return new jx(t(e))}return n._curve=t,n}function du(t){var n=t.curve;return t.angle=t.x,delete t.x,t.radius=t.y,delete t.y,t.curve=function(e){return arguments.length?n(ug(e)):n()._curve},t}function Jy(){return du(xs().curve(Vx))}function t2(){var t=Ma().curve(Vx),n=t.curve,e=t.lineX0,r=t.lineX1,i=t.lineY0,o=t.lineY1;return t.angle=t.x,delete t.x,t.startAngle=t.x0,delete t.x0,t.endAngle=t.x1,delete t.x1,t.radius=t.y,delete t.y,t.innerRadius=t.y0,delete t.y0,t.outerRadius=t.y1,delete t.y1,t.lineStartAngle=function(){return du(e())},delete t.lineX0,t.lineEndAngle=function(){return du(r())},delete t.lineX1,t.lineInnerRadius=function(){return du(i())},delete t.lineY0,t.lineOuterRadius=function(){return du(o())},delete t.lineY1,t.curve=function(u){return arguments.length?n(ug(u)):n()._curve},t}function gu(t,n){return[(n=+n)*Math.cos(t-=Math.PI/2),n*Math.sin(t)]}class Zx{constructor(n,e){this._context=n,this._x=e}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line}point(n,e){switch(n=+n,e=+e,this._point){case 0:{this._point=1,this._line?this._context.lineTo(n,e):this._context.moveTo(n,e);break}case 1:this._point=2;default:{this._x?this._context.bezierCurveTo(this._x0=(this._x0+n)/2,this._y0,this._x0,e,n,e):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+e)/2,n,this._y0,n,e);break}}this._x0=n,this._y0=e}}class aC{constructor(n){this._context=n}lineStart(){this._point=0}lineEnd(){}point(n,e){if(n=+n,e=+e,this._point===0)this._point=1;else{const r=gu(this._x0,this._y0),i=gu(this._x0,this._y0=(this._y0+e)/2),o=gu(n,this._y0),u=gu(n,e);this._context.moveTo(...r),this._context.bezierCurveTo(...i,...o,...u)}this._x0=n,this._y0=e}}function ag(t){return new Zx(t,!0)}function cg(t){return new Zx(t,!1)}function cC(t){return new aC(t)}function fC(t){return t.source}function sC(t){return t.target}function _s(t){let n=fC,e=sC,r=ig,i=og,o=null,u=null,a=_a(c);function c(){let f;const s=rC.call(arguments),h=n.apply(this,s),l=e.apply(this,s);if(o==null&&(u=t(f=a())),u.lineStart(),s[0]=h,u.point(+r.apply(this,s),+i.apply(this,s)),s[0]=l,u.point(+r.apply(this,s),+i.apply(this,s)),u.lineEnd(),f)return u=null,f+""||null}return c.source=function(f){return arguments.length?(n=f,c):n},c.target=function(f){return arguments.length?(e=f,c):e},c.x=function(f){return arguments.length?(r=typeof f=="function"?f:ot(+f),c):r},c.y=function(f){return arguments.length?(i=typeof f=="function"?f:ot(+f),c):i},c.context=function(f){return arguments.length?(f==null?o=u=null:u=t(o=f),c):o},c}function lC(){return _s(ag)}function hC(){return _s(cg)}function dC(){const t=_s(cC);return t.angle=t.x,delete t.x,t.radius=t.y,delete t.y,t}const gC=Mt(3),fg={draw(t,n){const e=Mt(n+ji(n/28,.75))*.59436,r=e/2,i=r*gC;t.moveTo(0,e),t.lineTo(0,-e),t.moveTo(-i,-r),t.lineTo(i,r),t.moveTo(-i,r),t.lineTo(i,-r)}},si={draw(t,n){const e=Mt(n/Mr);t.moveTo(e,0),t.arc(0,0,e,0,hr)}},sg={draw(t,n){const e=Mt(n/5)/2;t.moveTo(-3*e,-e),t.lineTo(-e,-e),t.lineTo(-e,-3*e),t.lineTo(e,-3*e),t.lineTo(e,-e),t.lineTo(3*e,-e),t.lineTo(3*e,e),t.lineTo(e,e),t.lineTo(e,3*e),t.lineTo(-e,3*e),t.lineTo(-e,e),t.lineTo(-3*e,e),t.closePath()}},Kx=Mt(1/3),pC=Kx*2,lg={draw(t,n){const e=Mt(n/pC),r=e*Kx;t.moveTo(0,-e),t.lineTo(r,0),t.lineTo(0,e),t.lineTo(-r,0),t.closePath()}},hg={draw(t,n){const e=Mt(n)*.62625;t.moveTo(0,-e),t.lineTo(e,0),t.lineTo(0,e),t.lineTo(-e,0),t.closePath()}},dg={draw(t,n){const e=Mt(n-ji(n/7,2))*.87559;t.moveTo(-e,0),t.lineTo(e,0),t.moveTo(0,e),t.lineTo(0,-e)}},gg={draw(t,n){const e=Mt(n),r=-e/2;t.rect(r,r,e,e)}},pg={draw(t,n){const e=Mt(n)*.4431;t.moveTo(e,e),t.lineTo(e,-e),t.lineTo(-e,-e),t.lineTo(-e,e),t.closePath()}},mC=.8908130915292852,Qx=xn(Mr/10)/xn(7*Mr/10),yC=xn(hr/10)*Qx,bC=-ze(hr/10)*Qx,mg={draw(t,n){const e=Mt(n*mC),r=yC*e,i=bC*e;t.moveTo(0,-e),t.lineTo(r,i);for(let o=1;o<5;++o){const u=hr*o/5,a=ze(u),c=xn(u);t.lineTo(c*e,-a*e),t.lineTo(a*r-c*i,c*r+a*i)}t.closePath()}},Bl=Mt(3),yg={draw(t,n){const e=-Mt(n/(Bl*3));t.moveTo(0,e*2),t.lineTo(-Bl*e,-e),t.lineTo(Bl*e,-e),t.closePath()}},wC=Mt(3),bg={draw(t,n){const e=Mt(n)*.6824,r=e/2,i=e*wC/2;t.moveTo(0,-e),t.lineTo(i,r),t.lineTo(-i,r),t.closePath()}},zn=-.5,Ln=Mt(3)/2,td=1/Mt(12),vC=(td/2+1)*3,wg={draw(t,n){const e=Mt(n/vC),r=e/2,i=e*td,o=r,u=e*td+e,a=-o,c=u;t.moveTo(r,i),t.lineTo(o,u),t.lineTo(a,c),t.lineTo(zn*r-Ln*i,Ln*r+zn*i),t.lineTo(zn*o-Ln*u,Ln*o+zn*u),t.lineTo(zn*a-Ln*c,Ln*a+zn*c),t.lineTo(zn*r+Ln*i,zn*i-Ln*r),t.lineTo(zn*o+Ln*u,zn*u-Ln*o),t.lineTo(zn*a+Ln*c,zn*c-Ln*a),t.closePath()}},xf={draw(t,n){const e=Mt(n-ji(n/6,1.7))*.6189;t.moveTo(-e,-e),t.lineTo(e,e),t.moveTo(-e,e),t.lineTo(e,-e)}},nd=[si,sg,lg,gg,mg,yg,wg],Jx=[si,dg,xf,bg,fg,pg,hg];function xC(t,n){let e=null,r=_a(i);t=typeof t=="function"?t:ot(t||si),n=typeof n=="function"?n:ot(n===void 0?64:+n);function i(){let o;if(e||(e=o=r()),t.apply(this,arguments).draw(e,+n.apply(this,arguments)),o)return e=null,o+""||null}return i.type=function(o){return arguments.length?(t=typeof o=="function"?o:ot(o),i):t},i.size=function(o){return arguments.length?(n=typeof o=="function"?o:ot(+o),i):n},i.context=function(o){return arguments.length?(e=o??null,i):e},i}function $r(){}function _f(t,n,e){t._context.bezierCurveTo((2*t._x0+t._x1)/3,(2*t._y0+t._y1)/3,(t._x0+2*t._x1)/3,(t._y0+2*t._y1)/3,(t._x0+4*t._x1+n)/6,(t._y0+4*t._y1+e)/6)}function Ms(t){this._context=t}Ms.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:_f(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1);break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:_f(this,t,n);break}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=n}};function t_(t){return new Ms(t)}function n_(t){this._context=t}n_.prototype={areaStart:$r,areaEnd:$r,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:{this._context.moveTo(this._x2,this._y2),this._context.closePath();break}case 2:{this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break}case 3:{this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4);break}}},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1,this._x2=t,this._y2=n;break;case 1:this._point=2,this._x3=t,this._y3=n;break;case 2:this._point=3,this._x4=t,this._y4=n,this._context.moveTo((this._x0+4*this._x1+t)/6,(this._y0+4*this._y1+n)/6);break;default:_f(this,t,n);break}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=n}};function e_(t){return new n_(t)}function r_(t){this._context=t}r_.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===3)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var e=(this._x0+4*this._x1+t)/6,r=(this._y0+4*this._y1+n)/6;this._line?this._context.lineTo(e,r):this._context.moveTo(e,r);break;case 3:this._point=4;default:_f(this,t,n);break}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=n}};function i_(t){return new r_(t)}function o_(t,n){this._basis=new Ms(t),this._beta=n}o_.prototype={lineStart:function(){this._x=[],this._y=[],this._basis.lineStart()},lineEnd:function(){var t=this._x,n=this._y,e=t.length-1;if(e>0)for(var r=t[0],i=n[0],o=t[e]-r,u=n[e]-i,a=-1,c;++a<=e;)c=a/e,this._basis.point(this._beta*t[a]+(1-this._beta)*(r+c*o),this._beta*n[a]+(1-this._beta)*(i+c*u));this._x=this._y=null,this._basis.lineEnd()},point:function(t,n){this._x.push(+t),this._y.push(+n)}};const u_=function t(n){function e(r){return n===1?new Ms(r):new o_(r,n)}return e.beta=function(r){return t(+r)},e}(.85);function Mf(t,n,e){t._context.bezierCurveTo(t._x1+t._k*(t._x2-t._x0),t._y1+t._k*(t._y2-t._y0),t._x2+t._k*(t._x1-n),t._y2+t._k*(t._y1-e),t._x2,t._y2)}function vg(t,n){this._context=t,this._k=(1-n)/6}vg.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x2,this._y2);break;case 3:Mf(this,this._x1,this._y1);break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2,this._x1=t,this._y1=n;break;case 2:this._point=3;default:Mf(this,t,n);break}this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}};const a_=function t(n){function e(r){return new vg(r,n)}return e.tension=function(r){return t(+r)},e}(0);function xg(t,n){this._context=t,this._k=(1-n)/6}xg.prototype={areaStart:$r,areaEnd:$r,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._x5=this._y0=this._y1=this._y2=this._y3=this._y4=this._y5=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:{this._context.moveTo(this._x3,this._y3),this._context.closePath();break}case 2:{this._context.lineTo(this._x3,this._y3),this._context.closePath();break}case 3:{this.point(this._x3,this._y3),this.point(this._x4,this._y4),this.point(this._x5,this._y5);break}}},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1,this._x3=t,this._y3=n;break;case 1:this._point=2,this._context.moveTo(this._x4=t,this._y4=n);break;case 2:this._point=3,this._x5=t,this._y5=n;break;default:Mf(this,t,n);break}this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}};const c_=function t(n){function e(r){return new xg(r,n)}return e.tension=function(r){return t(+r)},e}(0);function _g(t,n){this._context=t,this._k=(1-n)/6}_g.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===3)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3,this._line?this._context.lineTo(this._x2,this._y2):this._context.moveTo(this._x2,this._y2);break;case 3:this._point=4;default:Mf(this,t,n);break}this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}};const f_=function t(n){function e(r){return new _g(r,n)}return e.tension=function(r){return t(+r)},e}(0);function Mg(t,n,e){var r=t._x1,i=t._y1,o=t._x2,u=t._y2;if(t._l01_a>rn){var a=2*t._l01_2a+3*t._l01_a*t._l12_a+t._l12_2a,c=3*t._l01_a*(t._l01_a+t._l12_a);r=(r*a-t._x0*t._l12_2a+t._x2*t._l01_2a)/c,i=(i*a-t._y0*t._l12_2a+t._y2*t._l01_2a)/c}if(t._l23_a>rn){var f=2*t._l23_2a+3*t._l23_a*t._l12_a+t._l12_2a,s=3*t._l23_a*(t._l23_a+t._l12_a);o=(o*f+t._x1*t._l23_2a-n*t._l12_2a)/s,u=(u*f+t._y1*t._l23_2a-e*t._l12_2a)/s}t._context.bezierCurveTo(r,i,o,u,t._x2,t._y2)}function s_(t,n){this._context=t,this._alpha=n}s_.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._l01_a=this._l12_a=this._l23_a=this._l01_2a=this._l12_2a=this._l23_2a=this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x2,this._y2);break;case 3:this.point(this._x2,this._y2);break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){if(t=+t,n=+n,this._point){var e=this._x2-t,r=this._y2-n;this._l23_a=Math.sqrt(this._l23_2a=Math.pow(e*e+r*r,this._alpha))}switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2;break;case 2:this._point=3;default:Mg(this,t,n);break}this._l01_a=this._l12_a,this._l12_a=this._l23_a,this._l01_2a=this._l12_2a,this._l12_2a=this._l23_2a,this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}};const l_=function t(n){function e(r){return n?new s_(r,n):new vg(r,0)}return e.alpha=function(r){return t(+r)},e}(.5);function h_(t,n){this._context=t,this._alpha=n}h_.prototype={areaStart:$r,areaEnd:$r,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._x5=this._y0=this._y1=this._y2=this._y3=this._y4=this._y5=NaN,this._l01_a=this._l12_a=this._l23_a=this._l01_2a=this._l12_2a=this._l23_2a=this._point=0},lineEnd:function(){switch(this._point){case 1:{this._context.moveTo(this._x3,this._y3),this._context.closePath();break}case 2:{this._context.lineTo(this._x3,this._y3),this._context.closePath();break}case 3:{this.point(this._x3,this._y3),this.point(this._x4,this._y4),this.point(this._x5,this._y5);break}}},point:function(t,n){if(t=+t,n=+n,this._point){var e=this._x2-t,r=this._y2-n;this._l23_a=Math.sqrt(this._l23_2a=Math.pow(e*e+r*r,this._alpha))}switch(this._point){case 0:this._point=1,this._x3=t,this._y3=n;break;case 1:this._point=2,this._context.moveTo(this._x4=t,this._y4=n);break;case 2:this._point=3,this._x5=t,this._y5=n;break;default:Mg(this,t,n);break}this._l01_a=this._l12_a,this._l12_a=this._l23_a,this._l01_2a=this._l12_2a,this._l12_2a=this._l23_2a,this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}};const d_=function t(n){function e(r){return n?new h_(r,n):new xg(r,0)}return e.alpha=function(r){return t(+r)},e}(.5);function g_(t,n){this._context=t,this._alpha=n}g_.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._l01_a=this._l12_a=this._l23_a=this._l01_2a=this._l12_2a=this._l23_2a=this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===3)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){if(t=+t,n=+n,this._point){var e=this._x2-t,r=this._y2-n;this._l23_a=Math.sqrt(this._l23_2a=Math.pow(e*e+r*r,this._alpha))}switch(this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3,this._line?this._context.lineTo(this._x2,this._y2):this._context.moveTo(this._x2,this._y2);break;case 3:this._point=4;default:Mg(this,t,n);break}this._l01_a=this._l12_a,this._l12_a=this._l23_a,this._l01_2a=this._l12_2a,this._l12_2a=this._l23_2a,this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}};const p_=function t(n){function e(r){return n?new g_(r,n):new _g(r,0)}return e.alpha=function(r){return t(+r)},e}(.5);function m_(t){this._context=t}m_.prototype={areaStart:$r,areaEnd:$r,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(t,n){t=+t,n=+n,this._point?this._context.lineTo(t,n):(this._point=1,this._context.moveTo(t,n))}};function y_(t){return new m_(t)}function n2(t){return t<0?-1:1}function e2(t,n,e){var r=t._x1-t._x0,i=n-t._x1,o=(t._y1-t._y0)/(r||i<0&&-0),u=(e-t._y1)/(i||r<0&&-0),a=(o*i+u*r)/(r+i);return(n2(o)+n2(u))*Math.min(Math.abs(o),Math.abs(u),.5*Math.abs(a))||0}function r2(t,n){var e=t._x1-t._x0;return e?(3*(t._y1-t._y0)/e-n)/2:n}function ql(t,n,e){var r=t._x0,i=t._y0,o=t._x1,u=t._y1,a=(o-r)/3;t._context.bezierCurveTo(r+a,i+a*n,o-a,u-a*e,o,u)}function $f(t){this._context=t}$f.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:ql(this,this._t0,r2(this,this._t0));break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){var e=NaN;if(t=+t,n=+n,!(t===this._x1&&n===this._y1)){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2;break;case 2:this._point=3,ql(this,r2(this,e=e2(this,t,n)),e);break;default:ql(this,this._t0,e=e2(this,t,n));break}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=n,this._t0=e}}};function b_(t){this._context=new w_(t)}(b_.prototype=Object.create($f.prototype)).point=function(t,n){$f.prototype.point.call(this,n,t)};function w_(t){this._context=t}w_.prototype={moveTo:function(t,n){this._context.moveTo(n,t)},closePath:function(){this._context.closePath()},lineTo:function(t,n){this._context.lineTo(n,t)},bezierCurveTo:function(t,n,e,r,i,o){this._context.bezierCurveTo(n,t,r,e,o,i)}};function v_(t){return new $f(t)}function x_(t){return new b_(t)}function __(t){this._context=t}__.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var t=this._x,n=this._y,e=t.length;if(e)if(this._line?this._context.lineTo(t[0],n[0]):this._context.moveTo(t[0],n[0]),e===2)this._context.lineTo(t[1],n[1]);else for(var r=i2(t),i=i2(n),o=0,u=1;u<e;++o,++u)this._context.bezierCurveTo(r[0][o],i[0][o],r[1][o],i[1][o],t[u],n[u]);(this._line||this._line!==0&&e===1)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(t,n){this._x.push(+t),this._y.push(+n)}};function i2(t){var n,e=t.length-1,r,i=new Array(e),o=new Array(e),u=new Array(e);for(i[0]=0,o[0]=2,u[0]=t[0]+2*t[1],n=1;n<e-1;++n)i[n]=1,o[n]=4,u[n]=4*t[n]+2*t[n+1];for(i[e-1]=2,o[e-1]=7,u[e-1]=8*t[e-1]+t[e],n=1;n<e;++n)r=i[n]/o[n-1],o[n]-=r,u[n]-=r*u[n-1];for(i[e-1]=u[e-1]/o[e-1],n=e-2;n>=0;--n)i[n]=(u[n]-i[n+1])/o[n];for(o[e-1]=(t[e]+i[e-1])/2,n=0;n<e-1;++n)o[n]=2*t[n+1]-i[n+1];return[i,o]}function M_(t){return new __(t)}function $s(t,n){this._context=t,this._t=n}$s.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&this._point===2&&this._context.lineTo(this._x,this._y),(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2;default:{if(this._t<=0)this._context.lineTo(this._x,n),this._context.lineTo(t,n);else{var e=this._x*(1-this._t)+t*this._t;this._context.lineTo(e,this._y),this._context.lineTo(e,n)}break}}this._x=t,this._y=n}};function $_(t){return new $s(t,.5)}function A_(t){return new $s(t,0)}function S_(t){return new $s(t,1)}function ho(t,n){if((u=t.length)>1)for(var e=1,r,i,o=t[n[0]],u,a=o.length;e<u;++e)for(i=o,o=t[n[e]],r=0;r<a;++r)o[r][1]+=o[r][0]=isNaN(i[r][1])?i[r][0]:i[r][1]}function go(t){for(var n=t.length,e=new Array(n);--n>=0;)e[n]=n;return e}function _C(t,n){return t[n]}function MC(t){const n=[];return n.key=t,n}function $C(){var t=ot([]),n=go,e=ho,r=_C;function i(o){var u=Array.from(t.apply(this,arguments),MC),a,c=u.length,f=-1,s;for(const h of o)for(a=0,++f;a<c;++a)(u[a][f]=[0,+r(h,u[a].key,f,o)]).data=h;for(a=0,s=vs(n(u));a<c;++a)u[s[a]].index=a;return e(u,s),u}return i.keys=function(o){return arguments.length?(t=typeof o=="function"?o:ot(Array.from(o)),i):t},i.value=function(o){return arguments.length?(r=typeof o=="function"?o:ot(+o),i):r},i.order=function(o){return arguments.length?(n=o==null?go:typeof o=="function"?o:ot(Array.from(o)),i):n},i.offset=function(o){return arguments.length?(e=o??ho,i):e},i}function AC(t,n){if((r=t.length)>0){for(var e,r,i=0,o=t[0].length,u;i<o;++i){for(u=e=0;e<r;++e)u+=t[e][i][1]||0;if(u)for(e=0;e<r;++e)t[e][i][1]/=u}ho(t,n)}}function SC(t,n){if((c=t.length)>0)for(var e,r=0,i,o,u,a,c,f=t[n[0]].length;r<f;++r)for(u=a=0,e=0;e<c;++e)(o=(i=t[n[e]][r])[1]-i[0])>0?(i[0]=u,i[1]=u+=o):o<0?(i[1]=a,i[0]=a+=o):(i[0]=0,i[1]=o)}function TC(t,n){if((i=t.length)>0){for(var e=0,r=t[n[0]],i,o=r.length;e<o;++e){for(var u=0,a=0;u<i;++u)a+=t[u][e][1]||0;r[e][1]+=r[e][0]=-a/2}ho(t,n)}}function EC(t,n){if(!(!((u=t.length)>0)||!((o=(i=t[n[0]]).length)>0))){for(var e=0,r=1,i,o,u;r<o;++r){for(var a=0,c=0,f=0;a<u;++a){for(var s=t[n[a]],h=s[r][1]||0,l=s[r-1][1]||0,d=(h-l)/2,p=0;p<a;++p){var m=t[n[p]],y=m[r][1]||0,b=m[r-1][1]||0;d+=y-b}c+=h,f+=d*h}i[r-1][1]+=i[r-1][0]=e,c&&(e-=f/c)}i[r-1][1]+=i[r-1][0]=e,ho(t,n)}}function T_(t){var n=t.map(kC);return go(t).sort(function(e,r){return n[e]-n[r]})}function kC(t){for(var n=-1,e=0,r=t.length,i,o=-1/0;++n<r;)(i=+t[n][1])>o&&(o=i,e=n);return e}function E_(t){var n=t.map(k_);return go(t).sort(function(e,r){return n[e]-n[r]})}function k_(t){for(var n=0,e=-1,r=t.length,i;++e<r;)(i=+t[e][1])&&(n+=i);return n}function NC(t){return E_(t).reverse()}function CC(t){var n=t.length,e,r,i=t.map(k_),o=T_(t),u=0,a=0,c=[],f=[];for(e=0;e<n;++e)r=o[e],u<a?(u+=i[r],c.push(r)):(a+=i[r],f.push(r));return f.reverse().concat(c)}function RC(t){return go(t).reverse()}const oc=t=>()=>t;function IC(t,{sourceEvent:n,target:e,transform:r,dispatch:i}){Object.defineProperties(this,{type:{value:t,enumerable:!0,configurable:!0},sourceEvent:{value:n,enumerable:!0,configurable:!0},target:{value:e,enumerable:!0,configurable:!0},transform:{value:r,enumerable:!0,configurable:!0},_:{value:i}})}function le(t,n,e){this.k=t,this.x=n,this.y=e}le.prototype={constructor:le,scale:function(t){return t===1?this:new le(this.k*t,this.x,this.y)},translate:function(t,n){return t===0&n===0?this:new le(this.k,this.x+this.k*t,this.y+this.k*n)},apply:function(t){return[t[0]*this.k+this.x,t[1]*this.k+this.y]},applyX:function(t){return t*this.k+this.x},applyY:function(t){return t*this.k+this.y},invert:function(t){return[(t[0]-this.x)/this.k,(t[1]-this.y)/this.k]},invertX:function(t){return(t-this.x)/this.k},invertY:function(t){return(t-this.y)/this.k},rescaleX:function(t){return t.copy().domain(t.range().map(this.invertX,this).map(t.invert,t))},rescaleY:function(t){return t.copy().domain(t.range().map(this.invertY,this).map(t.invert,t))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};var As=new le(1,0,0);N_.prototype=le.prototype;function N_(t){for(;!t.__zoom;)if(!(t=t.parentNode))return As;return t.__zoom}function Yl(t){t.stopImmediatePropagation()}function Qo(t){t.preventDefault(),t.stopImmediatePropagation()}function zC(t){return(!t.ctrlKey||t.type==="wheel")&&!t.button}function LC(){var t=this;return t instanceof SVGElement?(t=t.ownerSVGElement||t,t.hasAttribute("viewBox")?(t=t.viewBox.baseVal,[[t.x,t.y],[t.x+t.width,t.y+t.height]]):[[0,0],[t.width.baseVal.value,t.height.baseVal.value]]):[[0,0],[t.clientWidth,t.clientHeight]]}function o2(){return this.__zoom||As}function PC(t){return-t.deltaY*(t.deltaMode===1?.05:t.deltaMode?1:.002)*(t.ctrlKey?10:1)}function DC(){return navigator.maxTouchPoints||"ontouchstart"in this}function OC(t,n,e){var r=t.invertX(n[0][0])-e[0][0],i=t.invertX(n[1][0])-e[1][0],o=t.invertY(n[0][1])-e[0][1],u=t.invertY(n[1][1])-e[1][1];return t.translate(i>r?(r+i)/2:Math.min(0,r)||Math.max(0,i),u>o?(o+u)/2:Math.min(0,o)||Math.max(0,u))}function FC(){var t=zC,n=LC,e=OC,r=PC,i=DC,o=[0,1/0],u=[[-1/0,-1/0],[1/0,1/0]],a=250,c=vw,f=li("start","zoom","end"),s,h,l,d=500,p=150,m=0,y=10;function b($){$.property("__zoom",o2).on("wheel.zoom",A,{passive:!1}).on("mousedown.zoom",T).on("dblclick.zoom",E).filter(i).on("touchstart.zoom",S).on("touchmove.zoom",R).on("touchend.zoom touchcancel.zoom",C).style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}b.transform=function($,N,k,I){var L=$.selection?$.selection():$;L.property("__zoom",o2),$!==L?x($,N,k,I):L.interrupt().each(function(){_(this,arguments).event(I).start().zoom(null,typeof N=="function"?N.apply(this,arguments):N).end()})},b.scaleBy=function($,N,k,I){b.scaleTo($,function(){var L=this.__zoom.k,z=typeof N=="function"?N.apply(this,arguments):N;return L*z},k,I)},b.scaleTo=function($,N,k,I){b.transform($,function(){var L=n.apply(this,arguments),z=this.__zoom,P=k==null?g(L):typeof k=="function"?k.apply(this,arguments):k,D=z.invert(P),B=typeof N=="function"?N.apply(this,arguments):N;return e(v(w(z,B),P,D),L,u)},k,I)},b.translateBy=function($,N,k,I){b.transform($,function(){return e(this.__zoom.translate(typeof N=="function"?N.apply(this,arguments):N,typeof k=="function"?k.apply(this,arguments):k),n.apply(this,arguments),u)},null,I)},b.translateTo=function($,N,k,I,L){b.transform($,function(){var z=n.apply(this,arguments),P=this.__zoom,D=I==null?g(z):typeof I=="function"?I.apply(this,arguments):I;return e(As.translate(D[0],D[1]).scale(P.k).translate(typeof N=="function"?-N.apply(this,arguments):-N,typeof k=="function"?-k.apply(this,arguments):-k),z,u)},I,L)};function w($,N){return N=Math.max(o[0],Math.min(o[1],N)),N===$.k?$:new le(N,$.x,$.y)}function v($,N,k){var I=N[0]-k[0]*$.k,L=N[1]-k[1]*$.k;return I===$.x&&L===$.y?$:new le($.k,I,L)}function g($){return[(+$[0][0]+ +$[1][0])/2,(+$[0][1]+ +$[1][1])/2]}function x($,N,k,I){$.on("start.zoom",function(){_(this,arguments).event(I).start()}).on("interrupt.zoom end.zoom",function(){_(this,arguments).event(I).end()}).tween("zoom",function(){var L=this,z=arguments,P=_(L,z).event(I),D=n.apply(L,z),B=k==null?g(D):typeof k=="function"?k.apply(L,z):k,Y=Math.max(D[1][0]-D[0][0],D[1][1]-D[0][1]),U=L.__zoom,W=typeof N=="function"?N.apply(L,z):N,Z=c(U.invert(B).concat(Y/U.k),W.invert(B).concat(Y/W.k));return function(q){if(q===1)q=W;else{var H=Z(q),F=Y/H[2];q=new le(F,B[0]-H[0]*F,B[1]-H[1]*F)}P.zoom(null,q)}})}function _($,N,k){return!k&&$.__zooming||new M($,N)}function M($,N){this.that=$,this.args=N,this.active=0,this.sourceEvent=null,this.extent=n.apply($,N),this.taps=0}M.prototype={event:function($){return $&&(this.sourceEvent=$),this},start:function(){return++this.active===1&&(this.that.__zooming=this,this.emit("start")),this},zoom:function($,N){return this.mouse&&$!=="mouse"&&(this.mouse[1]=N.invert(this.mouse[0])),this.touch0&&$!=="touch"&&(this.touch0[1]=N.invert(this.touch0[0])),this.touch1&&$!=="touch"&&(this.touch1[1]=N.invert(this.touch1[0])),this.that.__zoom=N,this.emit("zoom"),this},end:function(){return--this.active===0&&(delete this.that.__zooming,this.emit("end")),this},emit:function($){var N=_t(this.that).datum();f.call($,this.that,new IC($,{sourceEvent:this.sourceEvent,target:b,transform:this.that.__zoom,dispatch:f}),N)}};function A($,...N){if(!t.apply(this,arguments))return;var k=_(this,N).event($),I=this.__zoom,L=Math.max(o[0],Math.min(o[1],I.k*Math.pow(2,r.apply(this,arguments)))),z=Mn($);if(k.wheel)(k.mouse[0][0]!==z[0]||k.mouse[0][1]!==z[1])&&(k.mouse[1]=I.invert(k.mouse[0]=z)),clearTimeout(k.wheel);else{if(I.k===L)return;k.mouse=[z,I.invert(z)],Qr(this),k.start()}Qo($),k.wheel=setTimeout(P,p),k.zoom("mouse",e(v(w(I,L),k.mouse[0],k.mouse[1]),k.extent,u));function P(){k.wheel=null,k.end()}}function T($,...N){if(l||!t.apply(this,arguments))return;var k=$.currentTarget,I=_(this,N,!0).event($),L=_t($.view).on("mousemove.zoom",B,!0).on("mouseup.zoom",Y,!0),z=Mn($,k),P=$.clientX,D=$.clientY;jf($.view),Yl($),I.mouse=[z,this.__zoom.invert(z)],Qr(this),I.start();function B(U){if(Qo(U),!I.moved){var W=U.clientX-P,Z=U.clientY-D;I.moved=W*W+Z*Z>m}I.event(U).zoom("mouse",e(v(I.that.__zoom,I.mouse[0]=Mn(U,k),I.mouse[1]),I.extent,u))}function Y(U){L.on("mousemove.zoom mouseup.zoom",null),Zf(U.view,I.moved),Qo(U),I.event(U).end()}}function E($,...N){if(t.apply(this,arguments)){var k=this.__zoom,I=Mn($.changedTouches?$.changedTouches[0]:$,this),L=k.invert(I),z=k.k*($.shiftKey?.5:2),P=e(v(w(k,z),I,L),n.apply(this,N),u);Qo($),a>0?_t(this).transition().duration(a).call(x,P,I,$):_t(this).call(b.transform,P,I,$)}}function S($,...N){if(t.apply(this,arguments)){var k=$.touches,I=k.length,L=_(this,N,$.changedTouches.length===I).event($),z,P,D,B;for(Yl($),P=0;P<I;++P)D=k[P],B=Mn(D,this),B=[B,this.__zoom.invert(B),D.identifier],L.touch0?!L.touch1&&L.touch0[2]!==B[2]&&(L.touch1=B,L.taps=0):(L.touch0=B,z=!0,L.taps=1+!!s);s&&(s=clearTimeout(s)),z&&(L.taps<2&&(h=B[0],s=setTimeout(function(){s=null},d)),Qr(this),L.start())}}function R($,...N){if(this.__zooming){var k=_(this,N).event($),I=$.changedTouches,L=I.length,z,P,D,B;for(Qo($),z=0;z<L;++z)P=I[z],D=Mn(P,this),k.touch0&&k.touch0[2]===P.identifier?k.touch0[0]=D:k.touch1&&k.touch1[2]===P.identifier&&(k.touch1[0]=D);if(P=k.that.__zoom,k.touch1){var Y=k.touch0[0],U=k.touch0[1],W=k.touch1[0],Z=k.touch1[1],q=(q=W[0]-Y[0])*q+(q=W[1]-Y[1])*q,H=(H=Z[0]-U[0])*H+(H=Z[1]-U[1])*H;P=w(P,Math.sqrt(q/H)),D=[(Y[0]+W[0])/2,(Y[1]+W[1])/2],B=[(U[0]+Z[0])/2,(U[1]+Z[1])/2]}else if(k.touch0)D=k.touch0[0],B=k.touch0[1];else return;k.zoom("touch",e(v(P,D,B),k.extent,u))}}function C($,...N){if(this.__zooming){var k=_(this,N).event($),I=$.changedTouches,L=I.length,z,P;for(Yl($),l&&clearTimeout(l),l=setTimeout(function(){l=null},d),z=0;z<L;++z)P=I[z],k.touch0&&k.touch0[2]===P.identifier?delete k.touch0:k.touch1&&k.touch1[2]===P.identifier&&delete k.touch1;if(k.touch1&&!k.touch0&&(k.touch0=k.touch1,delete k.touch1),k.touch0)k.touch0[1]=this.__zoom.invert(k.touch0[0]);else if(k.end(),k.taps===2&&(P=Mn(P,this),Math.hypot(h[0]-P[0],h[1]-P[1])<y)){var D=_t(this).on("dblclick.zoom");D&&D.apply(this,arguments)}}}return b.wheelDelta=function($){return arguments.length?(r=typeof $=="function"?$:oc(+$),b):r},b.filter=function($){return arguments.length?(t=typeof $=="function"?$:oc(!!$),b):t},b.touchable=function($){return arguments.length?(i=typeof $=="function"?$:oc(!!$),b):i},b.extent=function($){return arguments.length?(n=typeof $=="function"?$:oc([[+$[0][0],+$[0][1]],[+$[1][0],+$[1][1]]]),b):n},b.scaleExtent=function($){return arguments.length?(o[0]=+$[0],o[1]=+$[1],b):[o[0],o[1]]},b.translateExtent=function($){return arguments.length?(u[0][0]=+$[0][0],u[1][0]=+$[1][0],u[0][1]=+$[0][1],u[1][1]=+$[1][1],b):[[u[0][0],u[0][1]],[u[1][0],u[1][1]]]},b.constrain=function($){return arguments.length?(e=$,b):e},b.duration=function($){return arguments.length?(a=+$,b):a},b.interpolate=function($){return arguments.length?(c=$,b):c},b.on=function(){var $=f.on.apply(f,arguments);return $===f?b:$},b.clickDistance=function($){return arguments.length?(m=($=+$)*$,b):Math.sqrt(m)},b.tapDistance=function($){return arguments.length?(y=+$,b):y},b}const jO=Object.freeze(Object.defineProperty({__proto__:null,Adder:Bt,Delaunay:Ze,FormatSpecifier:as,InternMap:gr,InternSet:Bn,Node:fi,Path:sa,Voronoi:Yw,ZoomTransform:le,active:w$,arc:eC,area:Ma,areaRadial:t2,ascending:yt,autoType:qA,axisBottom:Db,axisLeft:g8,axisRight:d8,axisTop:h8,bin:Wp,bisect:He,bisectCenter:_4,bisectLeft:x4,bisectRight:He,bisector:ta,blob:XA,blur:M4,blur2:Sd,blurImage:yb,brush:R$,brushSelection:k$,brushX:N$,brushY:C$,buffer:HA,chord:z$,chordDirected:P$,chordTranspose:L$,cluster:v0,color:mr,contourDensity:qw,contours:Uc,count:na,create:jM,creator:So,cross:wb,csv:jA,csvFormat:NA,csvFormatBody:CA,csvFormatRow:IA,csvFormatRows:RA,csvFormatValue:zA,csvParse:Xw,csvParseRows:kA,cubehelix:Jn,cumsum:vb,curveBasis:t_,curveBasisClosed:e_,curveBasisOpen:i_,curveBumpX:ag,curveBumpY:cg,curveBundle:u_,curveCardinal:a_,curveCardinalClosed:c_,curveCardinalOpen:f_,curveCatmullRom:l_,curveCatmullRomClosed:d_,curveCatmullRomOpen:p_,curveLinear:wi,curveLinearClosed:y_,curveMonotoneX:v_,curveMonotoneY:x_,curveNatural:M_,curveStep:$_,curveStepAfter:S_,curveStepBefore:A_,descending:dr,deviation:$o,difference:t8,disjoint:n8,dispatch:li,drag:i7,dragDisable:jf,dragEnable:Zf,dsv:VA,dsvFormat:is,easeBack:pm,easeBackIn:l$,easeBackInOut:pm,easeBackOut:h$,easeBounce:Cu,easeBounceIn:f$,easeBounceInOut:s$,easeBounceOut:Cu,easeCircle:gm,easeCircleIn:J9,easeCircleInOut:gm,easeCircleOut:t$,easeCubic:_h,easeCubicIn:W9,easeCubicInOut:_h,easeCubicOut:H9,easeElastic:mm,easeElasticIn:d$,easeElasticInOut:g$,easeElasticOut:mm,easeExp:dm,easeExpIn:K9,easeExpInOut:dm,easeExpOut:Q9,easeLinear:Y9,easePoly:lm,easePolyIn:G9,easePolyInOut:lm,easePolyOut:V9,easeQuad:sm,easeQuadIn:U9,easeQuadInOut:sm,easeQuadOut:X9,easeSin:hm,easeSinIn:j9,easeSinInOut:hm,easeSinOut:Z9,every:j4,extent:Tt,fcumsum:C4,filter:K4,flatGroup:R4,flatRollup:I4,forceCenter:rS,forceCollide:_S,forceLink:$S,forceManyBody:IS,forceRadial:zS,forceSimulation:RS,forceX:LS,forceY:PS,get format(){return pi},formatDefaultLocale:Zw,formatLocale:jw,get formatPrefix(){return n0},formatSpecifier:oo,fsum:N4,geoAlbers:l0,geoAlbersUsa:Pv,geoArea:HS,geoAzimuthalEqualArea:Ov,geoAzimuthalEqualAreaRaw:h0,geoAzimuthalEquidistant:Fv,geoAzimuthalEquidistantRaw:d0,geoBounds:ZS,geoCentroid:hv,geoCircle:eT,geoClipAntimeridian:Fh,geoClipCircle:wv,geoClipExtent:fT,geoClipRectangle:da,geoConicConformal:Uv,geoConicConformalRaw:Yv,geoConicEqualArea:Ou,geoConicEqualAreaRaw:Lv,geoConicEquidistant:Hv,geoConicEquidistantRaw:Wv,geoContains:mT,geoDistance:af,geoEqualEarth:Gv,geoEqualEarthRaw:g0,geoEquirectangular:Xv,geoEquirectangularRaw:Fu,geoGnomonic:Vv,geoGnomonicRaw:p0,geoGraticule:_v,geoGraticule10:Mv,geoIdentity:OT,geoInterpolate:yT,geoLength:vv,geoMercator:Bv,geoMercatorRaw:ma,geoNaturalEarth1:FT,geoNaturalEarth1Raw:m0,geoOrthographic:jv,geoOrthographicRaw:y0,geoPath:ko,geoProjection:Me,geoProjectionMutator:f0,geoRotation:dv,geoStereographic:Zv,geoStereographicRaw:b0,geoStream:Pn,geoTransform:Du,geoTransverseMercator:Kv,geoTransverseMercatorRaw:w0,gray:m7,greatest:ra,greatestIndex:X4,group:je,groupSort:Yf,groups:$b,hcl:Bc,hierarchy:x0,histogram:Wp,hsl:Dc,html:nS,image:KA,index:z4,indexes:L4,interpolate:Nr,interpolateArray:_7,interpolateBasis:aw,interpolateBasisClosed:cw,interpolateBlues:P1,interpolateBrBG:Q0,interpolateBuGn:h1,interpolateBuPu:g1,interpolateCividis:G1,interpolateCool:Z1,interpolateCubehelix:z7,interpolateCubehelixDefault:V1,interpolateCubehelixLong:ts,interpolateDate:gw,interpolateDiscrete:A7,interpolateGnBu:m1,interpolateGreens:O1,interpolateGreys:B1,interpolateHcl:Aw,interpolateHclLong:I7,interpolateHsl:_w,interpolateHslLong:R7,interpolateHue:S7,interpolateInferno:eg,interpolateLab:Mw,interpolateMagma:ng,interpolateNumber:Gt,interpolateNumberArray:Yd,interpolateObject:pw,interpolateOrRd:b1,interpolateOranges:H1,interpolatePRGn:t1,interpolatePiYG:e1,interpolatePlasma:rg,interpolatePuBu:_1,interpolatePuBuGn:v1,interpolatePuOr:i1,interpolatePuRd:$1,interpolatePurples:Y1,interpolateRainbow:K1,interpolateRdBu:qu,interpolateRdGy:u1,interpolateRdPu:S1,interpolateRdYlBu:Yu,interpolateRdYlGn:c1,interpolateReds:X1,interpolateRgb:yr,interpolateRgbBasis:lw,interpolateRgbBasisClosed:x7,interpolateRound:aa,interpolateSinebow:Q1,interpolateSpectral:s1,interpolateString:Ud,interpolateTransformCss:bw,interpolateTransformSvg:ww,interpolateTurbo:J1,interpolateViridis:tg,interpolateWarm:j1,interpolateYlGn:N1,interpolateYlGnBu:E1,interpolateYlOrBr:R1,interpolateYlOrRd:z1,interpolateZoom:vw,interrupt:Qr,intersection:e8,interval:O7,isoFormat:FN,isoParse:qN,json:JA,lab:Fc,lch:y7,least:Rb,leastIndex:Ib,line:xs,lineRadial:Jy,link:_s,linkHorizontal:lC,linkRadial:dC,linkVertical:hC,local:Hb,map:Q4,matcher:Ld,max:qt,maxIndex:Xf,mean:ia,median:to,medianIndex:q4,merge:Cd,min:on,minIndex:Wf,mode:Rd,namespace:oa,namespaces:me,nice:Uf,now:fa,pack:_E,packEnclose:mE,packSiblings:vE,pairs:Cb,partition:ME,path:Qd,pathRound:se,permute:Tb,pie:uC,piecewise:ca,pointRadial:gu,pointer:Mn,pointers:KM,polygonArea:DE,polygonCentroid:OE,polygonContains:YE,polygonHull:qE,polygonLength:UE,precisionFixed:Kw,precisionPrefix:Qw,precisionRound:Jw,quadtree:us,quantile:pe,quantileIndex:kb,quantileSorted:Eb,quantize:qn,quickselect:Hf,radialArea:t2,radialLine:Jy,randomBates:GE,randomBernoulli:ZE,randomBeta:sx,randomBinomial:lx,randomCauchy:QE,randomExponential:VE,randomGamma:A0,randomGeometric:fx,randomInt:WE,randomIrwinHall:cx,randomLcg:ss,randomLogNormal:HE,randomLogistic:JE,randomNormal:$0,randomPareto:jE,randomPoisson:tk,randomUniform:XE,randomWeibull:KE,range:kn,rank:Id,reduce:J4,reverse:Au,rgb:Qn,ribbon:H$,ribbonArrow:G$,rollup:jr,rollups:Ed,scaleBand:ba,scaleDiverging:V0,scaleDivergingLog:j0,scaleDivergingPow:bs,scaleDivergingSqrt:WN,scaleDivergingSymlog:Z0,scaleIdentity:T0,scaleImplicit:mf,scaleLinear:ds,scaleLog:k0,scaleOrdinal:ls,scalePoint:dx,scalePow:gs,scaleQuantile:I0,scaleQuantize:yx,scaleRadial:mx,scaleSequential:Rx,scaleSequentialLog:Ix,scaleSequentialPow:G0,scaleSequentialQuantile:Lx,scaleSequentialSqrt:XN,scaleSequentialSymlog:zx,scaleSqrt:gk,scaleSymlog:C0,scaleThreshold:z0,scaleTime:Nx,scaleUtc:Cx,scan:W4,schemeAccent:Dx,schemeBlues:L1,schemeBrBG:K0,schemeBuGn:l1,schemeBuPu:d1,schemeCategory10:Px,schemeDark2:Ox,schemeGnBu:p1,schemeGreens:D1,schemeGreys:F1,schemeObservable10:Fx,schemeOrRd:y1,schemeOranges:W1,schemePRGn:J0,schemePaired:Bx,schemePastel1:qx,schemePastel2:Yx,schemePiYG:n1,schemePuBu:x1,schemePuBuGn:w1,schemePuOr:r1,schemePuRd:M1,schemePurples:q1,schemeRdBu:bf,schemeRdGy:o1,schemeRdPu:A1,schemeRdYlBu:wf,schemeRdYlGn:a1,schemeReds:U1,schemeSet1:Ux,schemeSet2:Xx,schemeSet3:Wx,schemeSpectral:f1,schemeTableau10:Hx,schemeYlGn:k1,schemeYlGnBu:T1,schemeYlOrBr:C1,schemeYlOrRd:I1,select:_t,selectAll:QM,selection:hi,selector:Vf,selectorAll:zd,shuffle:H4,shuffler:zb,some:Z4,sort:$u,stack:$C,stackOffsetDiverging:SC,stackOffsetExpand:AC,stackOffsetNone:ho,stackOffsetSilhouette:TC,stackOffsetWiggle:EC,stackOrderAppearance:T_,stackOrderAscending:E_,stackOrderDescending:NC,stackOrderInsideOut:CC,stackOrderNone:go,stackOrderReverse:RC,stratify:M0,style:ii,subset:i8,sum:Kn,superset:Pb,svg:eS,symbol:xC,symbolAsterisk:fg,symbolCircle:si,symbolCross:sg,symbolDiamond:lg,symbolDiamond2:hg,symbolPlus:dg,symbolSquare:gg,symbolSquare2:pg,symbolStar:mg,symbolTimes:xf,symbolTriangle:yg,symbolTriangle2:bg,symbolWye:wg,symbolX:xf,symbols:nd,symbolsFill:nd,symbolsStroke:Jx,text:os,thresholdFreedmanDiaconis:Nb,thresholdScott:Nd,thresholdSturges:ea,tickFormat:gx,tickIncrement:pr,tickStep:zc,ticks:ge,timeDay:Rr,timeDays:vk,get timeFormat(){return ps},timeFormatDefaultLocale:Ex,timeFormatLocale:_x,timeFriday:O0,timeFridays:Tk,timeHour:Ro,timeHours:bk,timeInterval:Ot,timeMillisecond:fo,timeMilliseconds:Py,timeMinute:No,timeMinutes:mk,timeMonday:so,timeMondays:Mk,timeMonth:zo,timeMonths:Lk,get timeParse(){return Tx},timeSaturday:F0,timeSaturdays:Ek,timeSecond:Nn,timeSeconds:Oy,timeSunday:wr,timeSundays:Fy,timeThursday:vr,timeThursdays:Sk,timeTickInterval:xx,timeTicks:vx,timeTuesday:P0,timeTuesdays:$k,timeWednesday:D0,timeWednesdays:Ak,timeWeek:wr,timeWeeks:Fy,timeYear:Yn,timeYears:Dk,timeout:bh,timer:es,timerFlush:kw,transition:Iw,transpose:Lb,tree:Bu,treemap:IE,treemapBinary:zE,treemapDice:ya,treemapResquarify:PE,treemapSlice:fs,treemapSliceDice:LE,treemapSquarify:ax,tsv:ZA,tsvFormat:PA,tsvFormatBody:DA,tsvFormatRow:FA,tsvFormatRows:OA,tsvFormatValue:BA,tsvParse:Ww,tsvParseRows:LA,union:o8,unixDay:xa,unixDays:_k,utcDay:va,utcDays:xk,get utcFormat(){return bi},utcFriday:Y0,utcFridays:Ik,utcHour:Io,utcHours:wk,utcMillisecond:fo,utcMilliseconds:Py,utcMinute:Co,utcMinutes:yk,utcMonday:lo,utcMondays:kk,utcMonth:Lo,utcMonths:Pk,get utcParse(){return W0},utcSaturday:U0,utcSaturdays:zk,utcSecond:Nn,utcSeconds:Oy,utcSunday:xr,utcSundays:By,utcThursday:_r,utcThursdays:Rk,utcTickInterval:X0,utcTicks:wx,utcTuesday:B0,utcTuesdays:Nk,utcWednesday:q0,utcWednesdays:Ck,utcWeek:xr,utcWeeks:By,utcYear:Un,utcYears:Ok,variance:qf,window:Pd,xml:tS,zip:V4,zoom:FC,zoomIdentity:As,zoomTransform:N_},Symbol.toStringTag,{value:"Module"}));function Kt(t){return t!=null&&!Number.isNaN(t)}function Xn(t,n){return+Kt(n)-+Kt(t)||yt(t,n)}function Af(t,n){return+Kt(n)-+Kt(t)||dr(t,n)}function $g(t){return t!=null&&`${t}`!=""}function Sf(t){return isFinite(t)?t:NaN}function Ue(t){return t>0&&isFinite(t)?t:NaN}function Ss(t){return t<0&&isFinite(t)?t:NaN}function BC(t,n){if(t instanceof Date||(t=new Date(+t)),isNaN(t))return typeof n=="function"?n(t):n;const e=t.getUTCHours(),r=t.getUTCMinutes(),i=t.getUTCSeconds(),o=t.getUTCMilliseconds();return`${qC(t.getUTCFullYear())}-${De(t.getUTCMonth()+1,2)}-${De(t.getUTCDate(),2)}${e||r||i||o?`T${De(e,2)}:${De(r,2)}${i||o?`:${De(i,2)}${o?`.${De(o,3)}`:""}`:""}Z`:""}`}function qC(t){return t<0?`-${De(-t,6)}`:t>9999?`+${De(t,6)}`:De(t,4)}function De(t,n){return`${t}`.padStart(n,"0")}const YC=/^(?:[-+]\d{2})?\d{4}(?:-\d{2}(?:-\d{2})?)?(?:T\d{2}:\d{2}(?::\d{2}(?:\.\d{3})?)?(?:Z|[-+]\d{2}:?\d{2})?)?$/;function C_(t,n){return YC.test(t+="")?new Date(t):typeof n=="function"?n(t):n}function po(t){if(t==null)return;const n=t[0],e=t[t.length-1];return dr(n,e)}const qi=1e3,fr=qi*60,sr=fr*60,we=sr*24,oe=we*7,Jr=we*30,rr=we*365,Ul=[["millisecond",1],["2 milliseconds",2],["5 milliseconds",5],["10 milliseconds",10],["20 milliseconds",20],["50 milliseconds",50],["100 milliseconds",100],["200 milliseconds",200],["500 milliseconds",500],["second",qi],["5 seconds",5*qi],["15 seconds",15*qi],["30 seconds",30*qi],["minute",fr],["5 minutes",5*fr],["15 minutes",15*fr],["30 minutes",30*fr],["hour",sr],["3 hours",3*sr],["6 hours",6*sr],["12 hours",12*sr],["day",we],["2 days",2*we],["week",oe],["2 weeks",2*oe],["month",Jr],["3 months",3*Jr],["6 months",6*Jr],["year",rr],["2 years",2*rr],["5 years",5*rr],["10 years",10*rr],["20 years",20*rr],["50 years",50*rr],["100 years",100*rr]],Ag=new Map([["second",qi],["minute",fr],["hour",sr],["day",we],["monday",oe],["tuesday",oe],["wednesday",oe],["thursday",oe],["friday",oe],["saturday",oe],["sunday",oe],["week",oe],["month",Jr],["year",rr]]),R_=new Map([["second",Nn],["minute",No],["hour",Ro],["day",Rr],["monday",so],["tuesday",P0],["wednesday",D0],["thursday",vr],["friday",O0],["saturday",F0],["sunday",wr],["week",wr],["month",zo],["year",Yn]]),Sg=new Map([["second",Nn],["minute",Co],["hour",Io],["day",xa],["monday",lo],["tuesday",B0],["wednesday",q0],["thursday",_r],["friday",Y0],["saturday",U0],["sunday",xr],["week",xr],["month",Lo],["year",Un]]),$a=Symbol("intervalDuration"),Ts=Symbol("intervalType");for(const[t,n]of R_)n[$a]=Ag.get(t),n[Ts]="time";for(const[t,n]of Sg)n[$a]=Ag.get(t),n[Ts]="utc";const pu=[["year",Un,"utc"],["month",Lo,"utc"],["day",xa,"utc",6*Jr],["hour",Io,"utc",3*we],["minute",Co,"utc",6*sr],["second",Nn,"utc",30*fr]],Cc=[["year",Yn,"time"],["month",zo,"time"],["day",Rr,"time",6*Jr],["hour",Ro,"time",3*we],["minute",No,"time",6*sr],["second",Nn,"time",30*fr]],UC=[pu[0],Cc[0],pu[1],Cc[1],pu[2],Cc[2],...pu.slice(3)];function Tg(t){let n=`${t}`.toLowerCase();n.endsWith("s")&&(n=n.slice(0,-1));let e=1;const r=/^(?:(\d+)\s+)/.exec(n);switch(r&&(n=n.slice(r[0].length),e=+r[1]),n){case"quarter":n="month",e*=3;break;case"half":n="month",e*=6;break}let i=Sg.get(n);if(!i)throw new Error(`unknown interval: ${t}`);if(e>1&&!i.every)throw new Error(`non-periodic interval: ${n}`);return[n,e]}function Eg(t){return I_(Tg(t),"time")}function Es(t){return I_(Tg(t),"utc")}function I_([t,n],e){let r=(e==="time"?R_:Sg).get(t);return n>1&&(r=r.every(n),r[$a]=Ag.get(t)*n,r[Ts]=e),r}function u2(t,n){if(!(n>1))return;const e=t[$a];if(!Ul.some(([,i])=>i===e)||e%we===0&&we<e&&e<Jr)return;const[r]=Ul[ta(([,i])=>Math.log(i)).center(Ul,Math.log(e*n))];return(t[Ts]==="time"?Eg:Es)(r)}function a2(t,n,e){const r=n==="time"?ps:bi;if(e==null)return r(t==="year"?"%Y":t==="month"?"%Y-%m":t==="day"?"%Y-%m-%d":t==="hour"||t==="minute"?"%Y-%m-%dT%H:%M":t==="second"?"%Y-%m-%dT%H:%M:%S":"%Y-%m-%dT%H:%M:%S.%L");const i=XC(e);switch(t){case"millisecond":return zi(r(".%L"),r(":%M:%S"),i);case"second":return zi(r(":%S"),r("%-I:%M"),i);case"minute":return zi(r("%-I:%M"),r("%p"),i);case"hour":return zi(r("%-I %p"),r("%b %-d"),i);case"day":return zi(r("%-d"),r("%b"),i);case"month":return zi(r("%b"),r("%Y"),i);case"year":return r("%Y")}throw new Error("unable to format time ticks")}function XC(t){return t==="left"||t==="right"?(n,e)=>`
${n}
${e}`:t==="top"?(n,e)=>`${e}
${n}`:(n,e)=>`${n}
${e}`}function WC(t){return t==="time"?Cc:t==="utc"?pu:UC}function HC(t,n,e){const r=qt(Cb(n,(i,o)=>Math.abs(o-i)));if(r<1e3)return a2("millisecond","utc",e);for(const[i,o,u,a]of WC(t)){if(r>a||i==="hour"&&!r)break;if(n.every(c=>o.floor(c)>=c))return a2(i,u,e)}}function zi(t,n,e){return(r,i,o)=>{const u=t(r,i),a=n(r,i),c=i-po(o);return i!==c&&o[c]!==void 0&&a===n(o[c],c)?u:e(u,a)}}const ks=Object.getPrototypeOf(Uint8Array),GC=Object.prototype.toString;function Qe(t){return t instanceof Array||t instanceof ks}function z_(t){return t instanceof ks&&!VC(t)}function L_(t){return t?.prototype instanceof ks&&!jC(t)}function VC(t){return t instanceof BigInt64Array||t instanceof BigUint64Array}function jC(t){return t===BigInt64Array||t===BigUint64Array}const P_=Symbol("reindex");function ct(t,n,e){const r=typeof n;return r==="string"?Z_(t)?kg(t.getChild(n),e):c2(t,D_(n),e):r==="function"?c2(t,n,e):r==="number"||n instanceof Date||r==="boolean"?bt(t,jt(n),e):typeof n?.transform=="function"?ed(n.transform(t),e):ZC(ed(n,e),t?.[P_])}function ZC(t,n){return t!=null&&n?Cs(t,n):t}function c2(t,n,e){return bt(t,L_(e)?(r,i)=>Rg(n(r,i)):n,e)}function ed(t,n){return n===void 0?zr(t):K_(t)?kg(t,n):t instanceof n?t:n.from(t,L_(n)&&!z_(t)?Rg:void 0)}function kg(t,n){return t==null?t:(n===void 0||n===Array)&&sR(t.type)?O_(f2(t)):ed(f2(t),n)}function f2(t){return t.nullCount?t.toJSON():t.toArray()}const xu=[null],D_=t=>n=>{const e=n[t];return e===void 0&&n.type==="Feature"?n.properties?.[t]:e},Dt={transform:Je},nt={transform:t=>t},Ng=()=>1,KC=()=>!0,Vt=t=>t==null?t:`${t}`,$t=t=>t==null?t:+t,Ns=t=>t?t[0]:void 0,Uu=t=>t?t[1]:void 0,QC=t=>t?t[2]:void 0,jt=t=>()=>t;function Cg(t){const n=+`${t}`.slice(1)/100;return(e,r)=>pe(e,n,r)}function An(t){return z_(t)?t:bt(t,Rg,Float64Array)}function Rg(t){return t==null?NaN:Number(t)}function O_(t){return bt(t,F_)}function F_(t){return t instanceof Date&&!isNaN(t)?t:typeof t=="string"?C_(t):t==null||isNaN(t=Number(t))?void 0:new Date(t)}function yn(t,n){return t===void 0&&(t=n),t===null?[void 0,"none"]:Aa(t)?[void 0,t]:[t,void 0]}function Lt(t,n){return t===void 0&&(t=n),t===null||typeof t=="number"?[void 0,t]:[t,void 0]}function B_(t,n,e){if(t!=null)return Rn(t,n,e)}function Rn(t,n,e){const r=`${t}`.toLowerCase();if(!e.includes(r))throw new Error(`invalid ${n}: ${t}`);return r}function mo(t){return Z_(t)?t:zr(t)}function zr(t){if(t==null||Qe(t))return t;if(K_(t))return kg(t);if(q_(t))switch(t.type){case"FeatureCollection":return t.features;case"GeometryCollection":return t.geometries;default:return[t]}return Array.from(t)}function q_(t){switch(t?.type){case"FeatureCollection":case"GeometryCollection":case"Feature":case"LineString":case"MultiLineString":case"MultiPoint":case"MultiPolygon":case"Point":case"Polygon":case"Sphere":return!0;default:return!1}}function bt(t,n,e=Array){return t==null?t:t instanceof e?t.map(n):e.from(t,n)}function Xu(t,n=Array){return t instanceof n?t.slice():n.from(t)}function Y_({x:t,x1:n,x2:e}){return t!==void 0||n!==void 0||e!==void 0}function U_({y:t,y1:n,y2:e}){return t!==void 0||n!==void 0||e!==void 0}function Po(t){return Y_(t)||U_(t)||t.interval!==void 0}function In(t){return t?.toString===GC}function ti(t){return In(t)&&(t.type!==void 0||t.domain!==void 0)}function ue(t){return In(t)&&typeof t.transform!="function"}function Ar(t){return ue(t)&&t.value===void 0&&t.channel===void 0}function X_(t,n,e,r=nt){return n===void 0&&e===void 0?(n=0,e=t===void 0?r:t):n===void 0?n=t===void 0?0:t:e===void 0&&(e=t===void 0?0:t),[n,e]}function ne(t,n){return t===void 0&&n===void 0?[Ns,Uu]:[t,n]}function Lr({z:t,fill:n,stroke:e}={}){return t===void 0&&([t]=yn(n)),t===void 0&&([t]=yn(e)),t}function Wu(t){return Qe(t)?t.length:t?.numRows}function Je(t){const n=Wu(t),e=new Uint32Array(n);for(let r=0;r<n;++r)e[r]=r;return e}function Cs(t,n){return Qe(t)?bt(n,e=>t[e],t.constructor):bt(n,e=>t.at(e))}function Ig(t){return t.length===1?(n,e)=>t(Cs(e,n)):t}function ni(t,n,e){return t.subarray?t.subarray(n,e):t.slice(n,e)}function qe(t){return t!==null&&typeof t=="object"?t.valueOf():t}function W_(t,n){if(n[t]!==void 0)return n[t];switch(t){case"x1":case"x2":t="x";break;case"y1":case"y2":t="y";break}return n[t]}function Cn(t){let n;return[{transform:()=>n,label:$e(t)},e=>n=e]}function _n(t){return t==null?[t]:Cn(t)}function $e(t,n){return typeof t=="string"?t:t&&t.label!==void 0?t.label:n}function Tf(t,n){return{transform(e){const r=t.transform(e),i=n.transform(e);return bn(r)||bn(i)?bt(r,(o,u)=>new Date((+r[u]+ +i[u])/2)):bt(r,(o,u)=>(+r[u]+ +i[u])/2,Float64Array)},label:t.label}}function Ef(t,n){const e=H_(n?.interval,n?.type);return e?bt(t,e):t}function H_(t,n){const e=Rs(t,n);return e&&(r=>Kt(r)?e.floor(r):r)}function Rs(t,n){if(t!=null){if(typeof t=="number")return G_(t);if(typeof t=="string")return(n==="time"?Eg:Es)(t);if(typeof t.floor!="function")throw new Error("invalid interval; missing floor method");if(typeof t.offset!="function")throw new Error("invalid interval; missing offset method");return t}}function G_(t){t=+t,0<t&&t<1&&Number.isInteger(1/t)&&(t=-1/t);const n=Math.abs(t);return t<0?{floor:e=>Math.floor(e*n)/n,offset:(e,r=1)=>(e*n+Math.floor(r))/n,range:(e,r)=>kn(Math.ceil(e*n),r*n).map(i=>i/n)}:{floor:e=>Math.floor(e/n)*n,offset:(e,r=1)=>e+n*Math.floor(r),range:(e,r)=>kn(Math.ceil(e/n),r/n).map(i=>i*n)}}function Do(t,n){if(t=Rs(t,n),t&&typeof t.range!="function")throw new Error("invalid interval: missing range method");return t}function JC(t,n){if(t=Do(t,n),t&&typeof t.ceil!="function")throw new Error("invalid interval: missing ceil method");return t}function tR(t){return Yi(t)&&typeof t?.floor=="function"&&t.floor()instanceof Date}function Yi(t){return typeof t?.range=="function"}function Oo(t){return t===void 0||ue(t)?t:{value:t}}function nR(t){return t==null?null:{transform:n=>ct(n,t,Float64Array),label:$e(t)}}function eR(t){if(!tr(t))return!1;for(const n of t)if(n!=null)return typeof n=="object"&&"0"in n&&"1"in n}function tr(t){return t&&typeof t[Symbol.iterator]=="function"}function V_(t){for(const n of t)if(n!=null)return typeof n!="object"||n instanceof Date}function Rt(t){for(const n of t){if(n==null)continue;const e=typeof n;return e==="string"||e==="boolean"}}function bn(t){for(const n of t)if(n!=null)return n instanceof Date}function rR(t){for(const n of t)if(n!=null)return typeof n=="string"&&isNaN(n)&&C_(n)}function iR(t){for(const n of t)if(n!=null){if(typeof n!="string")return!1;if(n.trim())return!isNaN(n)}}function Ui(t){for(const n of t)if(n!=null)return typeof n=="number"}function Xl(t,n){let e;for(const r of t)if(r!=null){if(!n(r))return!1;e=!0}return e}const oR=new Set("none,currentcolor,transparent,aliceblue,antiquewhite,aqua,aquamarine,azure,beige,bisque,black,blanchedalmond,blue,blueviolet,brown,burlywood,cadetblue,chartreuse,chocolate,coral,cornflowerblue,cornsilk,crimson,cyan,darkblue,darkcyan,darkgoldenrod,darkgray,darkgreen,darkgrey,darkkhaki,darkmagenta,darkolivegreen,darkorange,darkorchid,darkred,darksalmon,darkseagreen,darkslateblue,darkslategray,darkslategrey,darkturquoise,darkviolet,deeppink,deepskyblue,dimgray,dimgrey,dodgerblue,firebrick,floralwhite,forestgreen,fuchsia,gainsboro,ghostwhite,gold,goldenrod,gray,green,greenyellow,grey,honeydew,hotpink,indianred,indigo,ivory,khaki,lavender,lavenderblush,lawngreen,lemonchiffon,lightblue,lightcoral,lightcyan,lightgoldenrodyellow,lightgray,lightgreen,lightgrey,lightpink,lightsalmon,lightseagreen,lightskyblue,lightslategray,lightslategrey,lightsteelblue,lightyellow,lime,limegreen,linen,magenta,maroon,mediumaquamarine,mediumblue,mediumorchid,mediumpurple,mediumseagreen,mediumslateblue,mediumspringgreen,mediumturquoise,mediumvioletred,midnightblue,mintcream,mistyrose,moccasin,navajowhite,navy,oldlace,olive,olivedrab,orange,orangered,orchid,palegoldenrod,palegreen,paleturquoise,palevioletred,papayawhip,peachpuff,peru,pink,plum,powderblue,purple,rebeccapurple,red,rosybrown,royalblue,saddlebrown,salmon,sandybrown,seagreen,seashell,sienna,silver,skyblue,slateblue,slategray,slategrey,snow,springgreen,steelblue,tan,teal,thistle,tomato,turquoise,violet,wheat,white,whitesmoke,yellow".split(","));function Aa(t){return typeof t!="string"?!1:(t=t.toLowerCase().trim(),/^#[0-9a-f]{3,8}$/.test(t)||/^(?:url|var|rgb|rgba|hsl|hsla|hwb|lab|lch|oklab|oklch|color|color-mix)\(.*\)$/.test(t)||oR.has(t))}function uR(t){return typeof t=="number"&&(0<=t&&t<=1||isNaN(t))}function St(t){return t==null||yo(t)}function yo(t){return/^\s*none\s*$/i.test(t)}function aR(t){return/^\s*round\s*$/i.test(t)}function rd(t,n){return B_(t,n,["middle","top-left","top","top-right","right","bottom-right","bottom","bottom-left","left"])}function Sa(t="middle"){return rd(t,"frameAnchor")}function cR(t={},...n){let e=t;for(const r of n)for(const i in r)if(e[i]===void 0){const o=r[i];e===t?e={...e,[i]:o}:e[i]=o}return e}function fR(t){console.warn("named iterables are deprecated; please use an object instead");const n=new Set;return Object.fromEntries(Array.from(t,e=>{const{name:r}=e;if(r==null)throw new Error("missing name");const i=`${r}`;if(i==="__proto__")throw new Error(`illegal name: ${i}`);if(n.has(i))throw new Error(`duplicate name: ${i}`);return n.add(i),[r,e]}))}function zg(t){return tr(t)?fR(t):t}function j_(t){return t===!0?t="frame":t===!1?t=null:!q_(t)&&t!=null&&(t=Rn(t,"clip",["frame","sphere"]),t==="sphere"&&(t={type:"Sphere"})),t}function Z_(t){return t&&typeof t.getChild=="function"&&typeof t.toArray=="function"&&t.schema&&Array.isArray(t.schema.fields)}function K_(t){return t&&typeof t.toArray=="function"&&t.type}function sR(t){return t&&(t.typeId===8||t.typeId===10)&&t.unit===1}const he=Symbol("position"),nr=Symbol("color"),Ta=Symbol("radius"),Ea=Symbol("length"),ka=Symbol("opacity"),Is=Symbol("symbol"),Q_=Symbol("projection"),Nt=new Map([["x",he],["y",he],["fx",he],["fy",he],["r",Ta],["color",nr],["opacity",ka],["symbol",Is],["length",Ea],["projection",Q_]]);function lR(t){return t===he||t===Q_}function hR(t){return t===he||t===Ta||t===Ea||t===ka}const J_=Math.sqrt(3),t3=2/J_,dR={draw(t,n){const e=Math.sqrt(n/Math.PI),r=e*t3,i=r/2;t.moveTo(0,r),t.lineTo(e,i),t.lineTo(e,-i),t.lineTo(0,-r),t.lineTo(-e,-i),t.lineTo(-e,i),t.closePath()}},Lg=new Map([["asterisk",fg],["circle",si],["cross",sg],["diamond",lg],["diamond2",hg],["hexagon",dR],["plus",dg],["square",gg],["square2",pg],["star",mg],["times",xf],["triangle",yg],["triangle2",bg],["wye",wg]]);function Pg(t){return t&&typeof t.draw=="function"}function gR(t){return Pg(t)?!0:typeof t!="string"?!1:Lg.has(t.toLowerCase())}function Dg(t){if(t==null||Pg(t))return t;const n=Lg.get(`${t}`.toLowerCase());if(n)return n;throw new Error(`invalid symbol: ${t}`)}function pR(t){if(t==null||Pg(t))return[void 0,t];if(typeof t=="string"){const n=Lg.get(`${t}`.toLowerCase());if(n)return[void 0,n]}return[t,void 0]}function ee({filter:t,sort:n,reverse:e,transform:r,initializer:i,...o}={},u){if(r===void 0&&(t!=null&&(r=Og(t)),n!=null&&!Ar(n)&&(r=Wl(r,Bg(n))),e&&(r=Wl(r,Fg))),u!=null&&i!=null)throw new Error("transforms cannot be applied after initializers");return{...o,...(n===null||Ar(n))&&{sort:n},transform:Wl(r,u)}}function un({filter:t,sort:n,reverse:e,initializer:r,...i}={},o){return r===void 0&&(t!=null&&(r=Og(t)),n!=null&&!Ar(n)&&(r=Hl(r,Bg(n))),e&&(r=Hl(r,Fg))),{...i,...(n===null||Ar(n))&&{sort:n},initializer:Hl(r,o)}}function Wl(t,n){return t==null?n===null?void 0:n:n==null?t===null?void 0:t:function(e,r,i){return{data:e,facets:r}=t.call(this,e,r,i),n.call(this,mo(e),r,i)}}function Hl(t,n){return t==null?n===null?void 0:n:n==null?t===null?void 0:t:function(e,r,i,...o){let u,a,c,f,s,h;return{data:a=e,facets:c=r,channels:u}=t.call(this,e,r,i,...o),{data:s=a,facets:h=c,channels:f}=n.call(this,a,c,{...i,...u},...o),{data:s,facets:h,channels:{...u,...f}}}}function zs(t,n){return(t.initializer!=null?un:ee)(t,n)}function mR(t,n){return zs(n,Og(t))}function Og(t){return(n,e)=>{const r=ct(n,t);return{data:n,facets:e.map(i=>i.filter(o=>r[o]))}}}function yR({sort:t,...n}={}){return{...zs(n,Fg),sort:Ar(t)?t:null}}function Fg(t,n){return{data:t,facets:n.map(e=>e.slice().reverse())}}function bR({seed:t,sort:n,...e}={}){return{...zs(e,e3(t==null?Math.random:ss(t))),sort:Ar(n)?n:null}}function n3(t,{sort:n,...e}={}){return{...(ue(t)&&t.channel!==void 0?un:zs)(e,Bg(t)),sort:Ar(n)?n:null}}function Bg(t){return(typeof t=="function"&&t.length!==1?wR:e3)(t)}function wR(t){return(n,e)=>{const r=Qe(n)?(i,o)=>t(n[i],n[o]):(i,o)=>t(n.get(i),n.get(o));return{data:n,facets:e.map(i=>i.slice().sort(r))}}}function e3(t){let n,e;({channel:n,value:t,order:e}={...Oo(t)});const r=n?.startsWith("-");if(r&&(n=n.slice(1)),e===void 0&&(e=r?Af:Xn),typeof e!="function")switch(`${e}`.toLowerCase()){case"ascending":e=Xn;break;case"descending":e=Af;break;default:throw new Error(`invalid order: ${e}`)}return(i,o,u)=>{let a;if(n===void 0)a=ct(i,t);else{if(u===void 0)throw new Error("channel sort requires an initializer");if(a=u[n],!a)return{};a=a.value}const c=(f,s)=>e(a[f],a[s]);return{data:i,facets:o.map(f=>f.slice().sort(c))}}}function qg(t,n){return Ls(null,null,t,n)}function Hu(t={y:"count"},n={}){const{x:e=nt}=n;if(e==null)throw new Error("missing channel: x");return Ls(e,null,t,n)}function Gu(t={x:"count"},n={}){const{y:e=nt}=n;if(e==null)throw new Error("missing channel: y");return Ls(null,e,t,n)}function Yg(t={fill:"count"},n={}){let{x:e,y:r}=n;if([e,r]=ne(e,r),e==null)throw new Error("missing channel: x");if(r==null)throw new Error("missing channel: y");return Ls(e,r,t,n)}function Ls(t,n,{data:e=Hg,filter:r,sort:i,reverse:o,...u}={},a={}){u=i3(u,a),e=a3(e,nt),i=i==null?void 0:o3("sort",i,a),r=r==null?void 0:u3("filter",r,a);const[c,f]=_n(t),[s,h]=_n(n),{z:l,fill:d,stroke:p,x1:m,x2:y,y1:b,y2:w,...v}=a,[g,x]=_n(l),[_]=yn(d),[M]=yn(p),[A,T]=_n(_),[E,S]=_n(M);return{..."z"in a&&{z:g||l},..."fill"in a&&{fill:A||d},..."stroke"in a&&{stroke:E||p},...ee(v,(R,C,$)=>{const N=Ef(ct(R,t),$?.x),k=Ef(ct(R,n),$?.y),I=ct(R,l),L=ct(R,_),z=ct(R,M),P=Wg(u,{z:I,fill:L,stroke:z}),D=[],B=[],Y=N&&f([]),U=k&&h([]),W=I&&x([]),Z=L&&T([]),q=z&&S([]);let H=0;for(const F of u)F.initialize(R);i&&i.initialize(R),r&&r.initialize(R);for(const F of C){const X=[];for(const G of u)G.scope("facet",F);i&&i.scope("facet",F),r&&r.scope("facet",F);for(const[G,O]of ei(F,P))for(const[rt,Q]of ei(O,k))for(const[ft,lt]of ei(Q,N)){const j={data:R};if(N&&(j.x=ft),k&&(j.y=rt),P&&(j.z=G),!(r&&!r.reduce(lt,j))){X.push(H++),B.push(e.reduceIndex(lt,R,j)),N&&Y.push(ft),k&&U.push(rt),I&&W.push(P===I?G:I[lt[0]]),L&&Z.push(P===L?G:L[lt[0]]),z&&q.push(P===z?G:z[lt[0]]);for(const gt of u)gt.reduce(lt,j);i&&i.reduce(lt,j)}}D.push(X)}return c3(D,i,o),{data:B,facets:D}}),...!lr(u,"x")&&(c?{x:c}:{x1:m,x2:y}),...!lr(u,"y")&&(s?{y:s}:{y1:b,y2:w}),...Object.fromEntries(u.map(({name:R,output:C})=>[R,C]))}}function lr(t,...n){for(const{name:e}of t)if(n.includes(e))return!0;return!1}function r3(t,n,e=Ug){const r=Object.entries(t);return n.title!=null&&t.title===void 0&&r.push(["title",AR]),n.href!=null&&t.href===void 0&&r.push(["href",Gg]),r.filter(([,i])=>i!==void 0).map(([i,o])=>o===null?vR(i):e(i,o,n))}function Ug(t,n,e,r=Xg){let i;In(n)&&"reduce"in n&&(i=n.scale,n=n.reduce);const o=r(t,n,e),[u,a]=Cn(o.label);let c;return{name:t,output:i===void 0?u:{value:u,scale:i},initialize(f){o.initialize(f),c=a([])},scope(f,s){o.scope(f,s)},reduce(f,s){c.push(o.reduce(f,s))}}}function vR(t){return{name:t,initialize(){},scope(){},reduce(){}}}function Xg(t,n,e,r=Ps){const i=W_(t,e),o=r(n,i);let u,a;return{label:$e(o===kf?null:i,o.label),initialize(c){u=i===void 0?c:ct(c,i),o.scope==="data"&&(a=o.reduceIndex(Je(c),u))},scope(c,f){o.scope===c&&(a=o.reduceIndex(f,u))},reduce(c,f){return o.scope==null?o.reduceIndex(c,u,f):o.reduceIndex(c,u,a,f)}}}function ei(t,n){return n?je(t,e=>n[e]):[[,t]]}function Ps(t,n,e=xR){if(t==null)return e(t);if(typeof t.reduceIndex=="function")return t;if(typeof t.reduce=="function"&&In(t))return MR(t);if(typeof t=="function")return $R(t);if(/^p\d{2}$/i.test(t))return Ie(Cg(t));switch(`${t}`.toLowerCase()){case"first":return Gg;case"last":return SR;case"identity":return Hg;case"count":return kf;case"distinct":return TR;case"sum":return n==null?kf:ER;case"proportion":return l2(n,"data");case"proportion-facet":return l2(n,"facet");case"deviation":return Ie($o);case"min":return Ie(on);case"min-index":return Ie(Wf);case"max":return Ie(qt);case"max-index":return Ie(Xf);case"mean":return s2(ia);case"median":return s2(to);case"variance":return Ie(qf);case"mode":return Ie(Rd)}return e(t)}function xR(t){throw new Error(`invalid reduce: ${t}`)}function i3(t,n){return r3(t,n,o3)}function o3(t,n,e){return Ug(t,n,e,u3)}function u3(t,n,e){return Xg(t,n,e,a3)}function a3(t,n){return Ps(t,n,_R)}function _R(t){switch(`${t}`.toLowerCase()){case"x":return kR;case"y":return NR;case"z":return f3}throw new Error(`invalid group reduce: ${t}`)}function Wg(t,n){for(const e in n){const r=n[e];if(r!==void 0&&!t.some(i=>i.name===e))return r}}function c3(t,n,e){if(n){const r=n.output.transform(),i=(o,u)=>Xn(r[o],r[u]);t.forEach(o=>o.sort(i))}e&&t.forEach(r=>r.reverse())}function MR(t){return console.warn("deprecated reduce interface; implement reduceIndex instead."),{...t,reduceIndex:t.reduce.bind(t)}}function $R(t){return{reduceIndex(n,e,r){return t(Cs(e,n),r)}}}function Ie(t){return{reduceIndex(n,e){return t(n,r=>e[r])}}}function s2(t){return{reduceIndex(n,e){const r=t(n,i=>e[i]);return bn(e)?new Date(r):r}}}const Hg={reduceIndex(t,n){return Cs(n,t)}},Gg={reduceIndex(t,n){return n[t[0]]}},AR={reduceIndex(t,n){const r=$u(jr(t,o=>o.length,o=>n[o]),Uu),i=r.slice(-5).reverse();if(i.length<r.length){const o=r.slice(0,-4);i[4]=[`… ${o.length.toLocaleString("en-US")} more`,Kn(o,Uu)]}return i.map(([o,u])=>`${o} (${u.toLocaleString("en-US")})`).join(`
`)}},SR={reduceIndex(t,n){return n[t[t.length-1]]}},kf={label:"Frequency",reduceIndex(t){return t.length}},TR={label:"Distinct",reduceIndex(t,n){const e=new Bn;for(const r of t)e.add(n[r]);return e.size}},ER=Ie(Kn);function l2(t,n){return t==null?{scope:n,label:"Frequency",reduceIndex:(e,r,i=1)=>e.length/i}:{scope:n,reduceIndex:(e,r,i=1)=>Kn(e,o=>r[o])/i}}const kR={reduceIndex(t,n,{x:e}){return e}},NR={reduceIndex(t,n,{y:e}){return e}},f3={reduceIndex(t,n,{z:e}){return e}};function CR(t){if(typeof t!="function")throw new Error(`invalid test function: ${t}`);return{reduceIndex(n,e,{data:r}){return e[n.find(Qe(r)?i=>t(r[i],i,r):i=>t(r.get(i),i,r))]}}}function Vu(t,{scale:n,type:e,value:r,filter:i,hint:o,label:u=$e(r)},a){return o===void 0&&typeof r?.transform=="function"&&(o=r.hint),l3(a,{scale:n,type:e,value:ct(t,r),label:u,filter:i,hint:o})}function s3(t,n){return Object.fromEntries(Object.entries(t).map(([e,r])=>[e,Vu(n,r,e)]))}function Ds(t,n){const e=Object.fromEntries(Object.entries(t).map(([r,{scale:i,value:o}])=>{const u=i==null?null:n[i];return[r,u==null?o:bt(o,u)]}));return e.channels=t,e}function l3(t,n){const{scale:e,value:r}=n;if(e===!0||e==="auto")switch(t){case"fill":case"stroke":case"color":n.scale=e!==!0&&Xl(r,Aa)?null:"color",n.defaultScale="color";break;case"fillOpacity":case"strokeOpacity":case"opacity":n.scale=e!==!0&&Xl(r,uR)?null:"opacity",n.defaultScale="opacity";break;case"symbol":e!==!0&&Xl(r,gR)?(n.scale=null,n.value=bt(r,Dg)):n.scale="symbol",n.defaultScale="symbol";break;default:n.scale=Nt.has(t)?t:null;break}else if(e===!1)n.scale=null;else if(e!=null&&!Nt.has(e))throw new Error(`unknown scale: ${e}`);return n}function RR(t,n,e,r,i){const{order:o,reverse:u,reduce:a=!0,limit:c}=i;for(const f in i){if(!Nt.has(f))continue;let{value:s,order:h=o,reverse:l=u,reduce:d=a,limit:p=c}=Oo(i[f]);const m=s?.startsWith("-");if(m&&(s=s.slice(1)),h=h===void 0?m!==(s==="width"||s==="height")?d3:h3:LR(h),d==null||d===!1)continue;const y=f==="fx"||f==="fy"?zR(n,r[f]):IR(e,f);if(!y)throw new Error(`missing channel for scale: ${f}`);const b=y.value,[w=0,v=1/0]=tr(p)?p:p<0?[p]:[0,p];if(s==null)y.domain=()=>{let g=Array.from(new Bn(b));return l&&(g=g.reverse()),(w!==0||v!==1/0)&&(g=g.slice(w,v)),g};else{const g=s==="data"?t:s==="height"?h2(e,"y1","y2"):s==="width"?h2(e,"x1","x2"):id(e,s,s==="y"?"y2":s==="x"?"x2":void 0),x=Ps(d===!0?"max":d,g);y.domain=()=>{let _=Ed(Je(b),M=>x.reduceIndex(M,g),M=>b[M]);return h&&_.sort(h),l&&_.reverse(),(w!==0||v!==1/0)&&(_=_.slice(w,v)),_.map(Ns)}}}}function IR(t,n){for(const e in t){const r=t[e];if(r.scale===n)return r}}function zR(t,n){const e=t.original;if(e===t)return n;const r=n.value,i=n.value=[];for(let o=0;o<e.length;++o){const u=r[e[o][0]];for(const a of t[o])i[a]=u}return n}function h2(t,n,e){const r=id(t,n),i=id(t,e);return bt(i,(o,u)=>Math.abs(o-r[u]),Float64Array)}function id(t,n,e){let r=t[n];if(!r&&e!==void 0&&(r=t[e]),r)return r.value;throw new Error(`missing channel: ${n}`)}function LR(t){if(t==null||typeof t=="function")return t;switch(`${t}`.toLowerCase()){case"ascending":return h3;case"descending":return d3}throw new Error(`invalid order: ${t}`)}function h3([t,n],[e,r]){return Xn(n,r)||Xn(t,e)}function d3([t,n],[e,r]){return Af(n,r)||Xn(t,e)}function od(t,n){let e=t[n];if(e){for(;e.source;)e=e.source;return e.source===null?null:e}}const g3=new Map([["accent",Dx],["category10",Px],["dark2",Ox],["observable10",Fx],["paired",Bx],["pastel1",qx],["pastel2",Yx],["set1",Ux],["set2",Xx],["set3",Wx],["tableau10",Hx]]);function PR(t){return t!=null&&g3.has(`${t}`.toLowerCase())}const d2=new Map([...g3,["brbg",Ne(K0,Q0)],["prgn",Ne(J0,t1)],["piyg",Ne(n1,e1)],["puor",Ne(r1,i1)],["rdbu",Ne(bf,qu)],["rdgy",Ne(o1,u1)],["rdylbu",Ne(wf,Yu)],["rdylgn",Ne(a1,c1)],["spectral",Ne(f1,s1)],["burd",g2(bf,qu)],["buylrd",g2(wf,Yu)],["blues",Yt(L1,P1)],["greens",Yt(D1,O1)],["greys",Yt(F1,B1)],["oranges",Yt(W1,H1)],["purples",Yt(q1,Y1)],["reds",Yt(U1,X1)],["turbo",Ce(J1)],["viridis",Ce(tg)],["magma",Ce(ng)],["inferno",Ce(eg)],["plasma",Ce(rg)],["cividis",Ce(G1)],["cubehelix",Ce(V1)],["warm",Ce(j1)],["cool",Ce(Z1)],["bugn",Yt(l1,h1)],["bupu",Yt(d1,g1)],["gnbu",Yt(p1,m1)],["orrd",Yt(y1,b1)],["pubu",Yt(x1,_1)],["pubugn",Yt(w1,v1)],["purd",Yt(M1,$1)],["rdpu",Yt(A1,S1)],["ylgn",Yt(k1,N1)],["ylgnbu",Yt(T1,E1)],["ylorbr",Yt(C1,R1)],["ylorrd",Yt(I1,z1)],["rainbow",p2(K1)],["sinebow",p2(Q1)]]);function Yt(t,n){return({length:e})=>e===1?[t[3][1]]:e===2?[t[3][1],t[3][2]]:(e=Math.max(3,Math.floor(e)),e>9?qn(n,e):t[e])}function Ne(t,n){return({length:e})=>e===2?[t[3][0],t[3][2]]:(e=Math.max(3,Math.floor(e)),e>11?qn(n,e):t[e])}function g2(t,n){return({length:e})=>e===2?[t[3][2],t[3][0]]:(e=Math.max(3,Math.floor(e)),e>11?qn(r=>n(1-r),e):t[e].slice().reverse())}function Ce(t){return({length:n})=>qn(t,Math.max(2,Math.floor(n)))}function p2(t){return({length:n})=>qn(t,Math.floor(n)+1).slice(0,-1)}function p3(t){const n=`${t}`.toLowerCase();if(!d2.has(n))throw new Error(`unknown ordinal scheme: ${n}`);return d2.get(n)}function Os(t,n){const e=p3(t),r=typeof e=="function"?e({length:n}):e;return r.length!==n?r.slice(0,n):r}function DR(t,n="greys"){const e=new Set,[r,i]=Os(n,2);for(const o of t)if(o!=null)if(o===!0)e.add(i);else if(o===!1)e.add(r);else return;return[...e]}const m2=new Map([["brbg",Q0],["prgn",t1],["piyg",e1],["puor",i1],["rdbu",qu],["rdgy",u1],["rdylbu",Yu],["rdylgn",c1],["spectral",s1],["burd",t=>qu(1-t)],["buylrd",t=>Yu(1-t)],["blues",P1],["greens",O1],["greys",B1],["purples",Y1],["reds",X1],["oranges",H1],["turbo",J1],["viridis",tg],["magma",ng],["inferno",eg],["plasma",rg],["cividis",G1],["cubehelix",V1],["warm",j1],["cool",Z1],["bugn",h1],["bupu",g1],["gnbu",m1],["orrd",b1],["pubugn",v1],["pubu",_1],["purd",$1],["rdpu",S1],["ylgnbu",E1],["ylgn",N1],["ylorbr",R1],["ylorrd",z1],["rainbow",K1],["sinebow",Q1]]);function Vg(t){const n=`${t}`.toLowerCase();if(!m2.has(n))throw new Error(`unknown quantitative scheme: ${n}`);return m2.get(n)}const OR=new Set(["brbg","prgn","piyg","puor","rdbu","rdgy","rdylbu","rdylgn","spectral","burd","buylrd"]);function FR(t){return t!=null&&OR.has(`${t}`.toLowerCase())}const m3=t=>n=>t(1-n),Gl=[0,1],y2=new Map([["number",Gt],["rgb",yr],["hsl",_w],["hcl",Aw],["lab",Mw]]);function y3(t){const n=`${t}`.toLowerCase();if(!y2.has(n))throw new Error(`unknown interpolator: ${n}`);return y2.get(n)}function Na(t,n,e,{type:r,nice:i,clamp:o,zero:u,domain:a=w3(t,e),unknown:c,round:f,scheme:s,interval:h,range:l=Nt.get(t)===Ta?ZR(e,a):Nt.get(t)===Ea?KR(e,a):Nt.get(t)===ka?Gl:void 0,interpolate:d=Nt.get(t)===nr?s==null&&l!==void 0?yr:Vg(s!==void 0?s:r==="cyclical"?"rainbow":"turbo"):f?aa:Gt,reverse:p}){if(a=b2(a),h=Do(h,r),(r==="cyclical"||r==="sequential")&&(r="linear"),typeof d!="function"&&(d=y3(d)),p=!!p,l!==void 0){const m=a.length,y=(l=b2(l)).length;if(m!==y){if(d.length===1)throw new Error("invalid piecewise interpolator");d=ca(d,l),l=void 0}}if(d.length===1?(p&&(d=m3(d),p=!1),l===void 0&&(l=Float64Array.from(a,(m,y)=>y/(a.length-1)),l.length===2&&(l=Gl)),n.interpolate((l===Gl?jt:Zg)(d))):n.interpolate(d),u){const[m,y]=Tt(a);(m>0||y<0)&&(a=Xu(a),(po(a)||1)===Math.sign(m)?a[0]=0:a[a.length-1]=0)}return p&&(a=Au(a)),n.domain(a).unknown(c),i&&(n.nice(BR(i,r)),a=n.domain()),l!==void 0&&n.range(l),o&&n.clamp(o),{type:r,domain:a,range:l,scale:n,interpolate:d,interval:h}}function b2(t){return t=zr(t),t.length>=2?t:[t[0],t[0]]}function BR(t,n){return t===!0?void 0:typeof t=="number"?t:JC(t,n)}function qR(t,n,e){return Na(t,ds(),n,e)}function YR(t,n,e){return b3(t,n,{...e,exponent:.5})}function b3(t,n,{exponent:e=1,...r}){return Na(t,gs().exponent(e),n,{...r,type:"pow"})}function UR(t,n,{base:e=10,domain:r=QR(n),...i}){return Na(t,k0().base(e),n,{...i,domain:r})}function XR(t,n,{constant:e=1,...r}){return Na(t,C0().constant(e),n,r)}function WR(t,n,{range:e,quantiles:r=e===void 0?5:(e=[...e]).length,n:i=r,scheme:o="rdylbu",domain:u=JR(n),unknown:a,interpolate:c,reverse:f}){return e===void 0&&(e=c!==void 0?qn(c,i):Nt.get(t)===nr?Os(o,i):void 0),u.length>0&&(u=I0(u,e===void 0?{length:i}:e).quantiles()),jg(t,n,{domain:u,range:e,reverse:f,unknown:a})}function HR(t,n,{range:e,n:r=e===void 0?5:(e=[...e]).length,scheme:i="rdylbu",domain:o=w3(t,n),unknown:u,interpolate:a,reverse:c}){const[f,s]=Tt(o);let h;return e===void 0?(h=ge(f,s,r),h[0]<=f&&h.splice(0,1),h[h.length-1]>=s&&h.pop(),r=h.length+1,e=a!==void 0?qn(a,r):Nt.get(t)===nr?Os(i,r):void 0):(h=qn(Gt(f,s),r+1).slice(1,-1),f instanceof Date&&(h=h.map(l=>new Date(l)))),po(zr(o))<0&&h.reverse(),jg(t,n,{domain:h,range:e,reverse:c,unknown:u})}function jg(t,n,{domain:e=[0],unknown:r,scheme:i="rdylbu",interpolate:o,range:u=o!==void 0?qn(o,e.length+1):Nt.get(t)===nr?Os(i,e.length+1):void 0,reverse:a}){e=zr(e);const c=po(e);if(!isNaN(c)&&!GR(e,c))throw new Error(`the ${t} scale has a non-monotonic domain`);return a&&(u=Au(u)),{type:"threshold",scale:z0(c<0?Au(e):e,u===void 0?[]:u).unknown(r),domain:e,range:u}}function GR(t,n){for(let e=1,r=t.length,i=t[0];e<r;++e){const o=dr(i,i=t[e]);if(o!==0&&o!==n)return!1}return!0}function VR(t){return{type:"identity",scale:hR(Nt.get(t))?T0():n=>n}}function ju(t,n=Sf){return t.length?[on(t,({value:e})=>e===void 0?e:on(e,n)),qt(t,({value:e})=>e===void 0?e:qt(e,n))]:[0,1]}function w3(t,n){const e=Nt.get(t);return(e===Ta||e===ka||e===Ea?jR:ju)(n)}function jR(t){return[0,t.length?qt(t,({value:n})=>n===void 0?n:qt(n,Sf)):1]}function ZR(t,n){const e=t.find(({radius:u})=>u!==void 0);if(e!==void 0)return[0,e.radius];const r=pe(t,.5,({value:u})=>u===void 0?NaN:pe(u,.25,Ue)),i=n.map(u=>3*Math.sqrt(u/r)),o=30/qt(i);return o<1?i.map(u=>u*o):i}function KR(t,n){const e=to(t,({value:o})=>o===void 0?NaN:to(o,Math.abs)),r=n.map(o=>12*o/e),i=60/qt(r);return i<1?r.map(o=>o*i):r}function QR(t){for(const{value:n}of t)if(n!==void 0)for(let e of n){if(e>0)return ju(t,Ue);if(e<0)return ju(t,Ss)}return[1,10]}function JR(t){const n=[];for(const{value:e}of t)if(e!==void 0)for(const r of e)n.push(r);return n}function Zg(t){return(n,e)=>r=>t(n+r*(e-n))}let ud=0,ad;function tI(){const t=ud;return ud=0,ad=void 0,t}function Xe(t){t!==ad&&(ad=t,console.warn(t),++ud)}function Fs(t,n,e,r,{type:i,nice:o,clamp:u,domain:a=ju(r),unknown:c,pivot:f=0,scheme:s,range:h,symmetric:l=!0,interpolate:d=Nt.get(t)===nr?s==null&&h!==void 0?yr:Vg(s!==void 0?s:"rdbu"):Gt,reverse:p}){f=+f,a=zr(a);let[m,y]=a;if(a.length>2&&Xe(`Warning: the diverging ${t} scale domain contains extra elements.`),dr(m,y)<0&&([m,y]=[y,m],p=!p),m=Math.min(m,f),y=Math.max(y,f),typeof d!="function"&&(d=y3(d)),h!==void 0&&(d=d.length===1?Zg(d)(...h):ca(d,h)),p&&(d=m3(d)),l){const b=e.apply(f),w=b-e.apply(m),v=e.apply(y)-b;w<v?m=e.invert(b-v):w>v&&(y=e.invert(b+w))}return n.domain([m,f,y]).unknown(c).interpolator(d),u&&n.clamp(u),o&&n.nice(o),{type:i,domain:[m,y],pivot:f,interpolate:d,scale:n}}function nI(t,n,e){return Fs(t,V0(),oI,n,e)}function eI(t,n,e){return v3(t,n,{...e,exponent:.5})}function v3(t,n,{exponent:e=1,...r}){return Fs(t,bs().exponent(e=+e),cI(e),n,{...r,type:"diverging-pow"})}function rI(t,n,{base:e=10,pivot:r=1,domain:i=ju(n,r<0?Ss:Ue),...o}){return Fs(t,j0().base(e=+e),uI,n,{domain:i,pivot:r,...o})}function iI(t,n,{constant:e=1,...r}){return Fs(t,Z0().constant(e=+e),fI(e),n,r)}const oI={apply(t){return t},invert(t){return t}},uI={apply:Math.log,invert:Math.exp},aI={apply(t){return Math.sign(t)*Math.sqrt(Math.abs(t))},invert(t){return Math.sign(t)*(t*t)}};function cI(t){return t===.5?aI:{apply(n){return Math.sign(n)*Math.pow(Math.abs(n),t)},invert(n){return Math.sign(n)*Math.pow(Math.abs(n),1/t)}}}function fI(t){return{apply(n){return Math.sign(n)*Math.log1p(Math.abs(n/t))},invert(n){return Math.sign(n)*Math.expm1(Math.abs(n))*t}}}function x3(t,n,e,r){return Na(t,n,e,r)}function sI(t,n,e){return x3(t,Nx(),n,e)}function lI(t,n,e){return x3(t,Cx(),n,e)}const Ca=Symbol("ordinal");function _3(t,n,e,{type:r,interval:i,domain:o,range:u,reverse:a,hint:c}){return i=Do(i,r),o===void 0&&(o=$3(e,i,t)),(r==="categorical"||r===Ca)&&(r="ordinal"),a&&(o=Au(o)),o=n.domain(o).domain(),u!==void 0&&(typeof u=="function"&&(u=u(o)),n.range(u)),{type:r,domain:o,range:u,scale:n,hint:c,interval:i}}function hI(t,n,{type:e,interval:r,domain:i,range:o,scheme:u,unknown:a,...c}){r=Do(r,e),i===void 0&&(i=$3(n,r,t));let f;if(Nt.get(t)===Is)f=pI(n),o=o===void 0?mI(f):bt(o,Dg);else if(Nt.get(t)===nr&&(o===void 0&&(e==="ordinal"||e===Ca)&&(o=DR(i,u),o!==void 0&&(u=void 0)),u===void 0&&o===void 0&&(u=e==="ordinal"?"turbo":"observable10"),u!==void 0))if(o!==void 0){const s=Vg(u),h=o[0],l=o[1]-o[0];o=({length:d})=>qn(p=>s(h+l*p),d)}else o=p3(u);if(a===mf)throw new Error(`implicit unknown on ${t} scale is not supported`);return _3(t,ls().unknown(a),n,{...c,type:e,domain:i,range:o,hint:f})}function dI(t,n,{align:e=.5,padding:r=.5,...i}){return M3(dx().align(e).padding(r),n,i,t)}function gI(t,n,{align:e=.5,padding:r=.1,paddingInner:i=r,paddingOuter:o=t==="fx"||t==="fy"?0:r,...u}){return M3(ba().align(e).paddingInner(i).paddingOuter(o),n,u,t)}function M3(t,n,e,r){let{round:i}=e;return i!==void 0&&t.round(i=!!i),t=_3(r,t,n,e),t.round=i,t}function $3(t,n,e){const r=new Bn;for(const{value:i,domain:o}of t){if(o!==void 0)return o();if(i!==void 0)for(const u of i)r.add(u)}if(n!==void 0){const[i,o]=Tt(r).map(n.floor,n);return n.range(i,n.offset(o))}if(r.size>1e4&&Nt.get(e)===he)throw new Error(`implicit ordinal domain of ${e} scale has more than 10,000 values`);return $u(r,Xn)}function w2(t,n){let e;for(const{hint:r}of t){const i=r?.[n];if(i!==void 0){if(e===void 0)e=i;else if(e!==i)return}}return e}function pI(t){return{fill:w2(t,"fill"),stroke:w2(t,"stroke")}}function mI(t){return St(t.fill)?Jx:nd}function cd(t,{label:n,inset:e=0,insetTop:r=e,insetRight:i=e,insetBottom:o=e,insetLeft:u=e,round:a,nice:c,clamp:f,zero:s,align:h,padding:l,projection:d,facet:{label:p=n}={},...m}={}){const y={};for(const[b,w]of t){const v=m[b],g=k3(b,w,{round:Nt.get(b)===he?a:void 0,nice:c,clamp:f,zero:s,align:h,padding:l,projection:d,...v});if(g){let{label:x=b==="fx"||b==="fy"?p:n,percent:_,transform:M,inset:A,insetTop:T=A!==void 0?A:b==="y"?r:0,insetRight:E=A!==void 0?A:b==="x"?i:0,insetBottom:S=A!==void 0?A:b==="y"?o:0,insetLeft:R=A!==void 0?A:b==="x"?u:0}=v||{};if(M==null)M=void 0;else if(typeof M!="function")throw new Error("invalid scale transform; not a function");g.percent=!!_,g.label=x===void 0?bI(w,g):x,g.transform=M,b==="x"||b==="fx"?(g.insetLeft=+R,g.insetRight=+E):(b==="y"||b==="fy")&&(g.insetTop=+T,g.insetBottom=+S),y[b]=g}}return y}function v2(t){const n={},e={scales:n};for(const[r,i]of Object.entries(t)){const{scale:o,type:u,interval:a,label:c}=i;n[r]=C3(i),e[r]=o,o.type=u,a!=null&&(o.interval=a),c!=null&&(o.label=c)}return e}function yI(t,n){const{x:e,y:r,fx:i,fy:o}=t,u=i||o?Kg(n):n;i&&x2(i,u),o&&_2(o,u);const a=i||o?S3(t,n):n;e&&x2(e,a),r&&_2(r,a)}function bI(t=[],n){let e;for(const{label:r}of t)if(r!==void 0){if(e===void 0)e=r;else if(e!==r)return}if(e!==void 0)return!vi(n)&&n.percent&&(e=`${e} (%)`),{inferred:!0,toString:()=>e}}function A3(t){return Math.sign(po(t.domain()))*Math.sign(po(t.range()))}function Kg(t){const{marginTop:n,marginRight:e,marginBottom:r,marginLeft:i,width:o,height:u,facet:{marginTop:a,marginRight:c,marginBottom:f,marginLeft:s}}=t;return{marginTop:Math.max(n,a),marginRight:Math.max(e,c),marginBottom:Math.max(r,f),marginLeft:Math.max(i,s),width:o,height:u}}function S3({fx:t,fy:n},e){const{marginTop:r,marginRight:i,marginBottom:o,marginLeft:u,width:a,height:c}=Kg(e);return{marginTop:r,marginRight:i,marginBottom:o,marginLeft:u,width:t?t.scale.bandwidth()+u+i:a,height:n?n.scale.bandwidth()+r+o:c,facet:{width:a,height:c}}}function x2(t,n){if(t.range===void 0){const{insetLeft:e,insetRight:r}=t,{width:i,marginLeft:o=0,marginRight:u=0}=n,a=o+e,c=i-u-r;t.range=[a,Math.max(a,c)],vi(t)||(t.range=E3(t)),t.scale.range(t.range)}T3(t)}function _2(t,n){if(t.range===void 0){const{insetTop:e,insetBottom:r}=t,{height:i,marginTop:o=0,marginBottom:u=0}=n,a=o+e,c=i-u-r;t.range=[Math.max(a,c),a],vi(t)?t.range.reverse():t.range=E3(t),t.scale.range(t.range)}T3(t)}function T3(t){t.round===void 0&&xI(t)&&wI(t)<=30&&t.scale.round(!0)}function wI({scale:t}){const n=t.domain().length,[e,r]=t.range(),i=t.paddingInner?t.paddingInner():1,o=t.paddingOuter?t.paddingOuter():t.padding(),u=n-i,a=Math.abs(r-e)/Math.max(1,u+o*2);return(a-Math.floor(a))*u}function E3(t){const n=t.scale.domain().length+N3(t);if(!(n>2))return t.range;const[e,r]=t.range;return Array.from({length:n},(i,o)=>e+o/(n-1)*(r-e))}function fd(t,n,e){return k3(t,e===void 0?void 0:[{hint:e}],{...n})}function k3(t,n=[],e={}){const r=vI(t,n,e);if(e.type===void 0&&e.domain===void 0&&e.range===void 0&&e.interval==null&&t!=="fx"&&t!=="fy"&&vi({type:r})){const i=n.map(({value:o})=>o).filter(o=>o!==void 0);i.some(bn)?Xe(`Warning: some data associated with the ${t} scale are dates. Dates are typically associated with a "utc" or "time" scale rather than a "${Li(r)}" scale. If you are using a bar mark, you probably want a rect mark with the interval option instead; if you are using a group transform, you probably want a bin transform instead. If you want to treat this data as ordinal, you can specify the interval of the ${t} scale (e.g., d3.utcDay), or you can suppress this warning by setting the type of the ${t} scale to "${Li(r)}".`):i.some(rR)?Xe(`Warning: some data associated with the ${t} scale are strings that appear to be dates (e.g., YYYY-MM-DD). If these strings represent dates, you should parse them to Date objects. Dates are typically associated with a "utc" or "time" scale rather than a "${Li(r)}" scale. If you are using a bar mark, you probably want a rect mark with the interval option instead; if you are using a group transform, you probably want a bin transform instead. If you want to treat this data as ordinal, you can suppress this warning by setting the type of the ${t} scale to "${Li(r)}".`):i.some(iR)&&Xe(`Warning: some data associated with the ${t} scale are strings that appear to be numbers. If these strings represent numbers, you should parse or coerce them to numbers. Numbers are typically associated with a "linear" scale rather than a "${Li(r)}" scale. If you want to treat this data as ordinal, you can specify the interval of the ${t} scale (e.g., 1 for integers), or you can suppress this warning by setting the type of the ${t} scale to "${Li(r)}".`)}switch(e.type=r,r){case"diverging":case"diverging-sqrt":case"diverging-pow":case"diverging-log":case"diverging-symlog":case"cyclical":case"sequential":case"linear":case"sqrt":case"threshold":case"quantile":case"pow":case"log":case"symlog":e=uc(n,e,An);break;case"identity":switch(Nt.get(t)){case he:e=uc(n,e,An);break;case Is:e=uc(n,e,_I);break}break;case"utc":case"time":e=uc(n,e,O_);break}switch(r){case"diverging":return nI(t,n,e);case"diverging-sqrt":return eI(t,n,e);case"diverging-pow":return v3(t,n,e);case"diverging-log":return rI(t,n,e);case"diverging-symlog":return iI(t,n,e);case"categorical":case"ordinal":case Ca:return hI(t,n,e);case"cyclical":case"sequential":case"linear":return qR(t,n,e);case"sqrt":return YR(t,n,e);case"threshold":return jg(t,n,e);case"quantile":return WR(t,n,e);case"quantize":return HR(t,n,e);case"pow":return b3(t,n,e);case"log":return UR(t,n,e);case"symlog":return XR(t,n,e);case"utc":return lI(t,n,e);case"time":return sI(t,n,e);case"point":return dI(t,n,e);case"band":return gI(t,n,e);case"identity":return VR(t);case void 0:return;default:throw new Error(`unknown scale type: ${r}`)}}function Li(t){return typeof t=="symbol"?t.description:t}function M2(t){return typeof t=="string"?`${t}`.toLowerCase():t}const $2={toString:()=>"projection"};function vI(t,n,{type:e,domain:r,range:i,scheme:o,pivot:u,projection:a}){if(e=M2(e),t==="fx"||t==="fy")return"band";(t==="x"||t==="y")&&a!=null&&(e=$2);for(const s of n){const h=M2(s.type);if(h!==void 0){if(e===void 0)e=h;else if(e!==h)throw new Error(`scale incompatible with channel: ${e} !== ${h}`)}}if(e===$2)return;if(e!==void 0)return e;if(r===void 0&&!n.some(({value:s})=>s!==void 0))return;const c=Nt.get(t);if(c===Ta)return"sqrt";if(c===ka||c===Ea)return"linear";if(c===Is)return"ordinal";const f=(r??i)?.length;if(f<2||f>2)return Vl(c);if(r!==void 0){if(Rt(r))return Vl(c);if(bn(r))return"utc"}else{const s=n.map(({value:h})=>h).filter(h=>h!==void 0);if(s.some(Rt))return Vl(c);if(s.some(bn))return"utc"}if(c===nr){if(u!=null||FR(o))return"diverging";if(PR(o))return"categorical"}return"linear"}function Vl(t){switch(t){case he:return"point";case nr:return Ca;default:return"ordinal"}}function vi({type:t}){return t==="ordinal"||t==="point"||t==="band"||t===Ca}function N3({type:t}){return t==="threshold"}function xI({type:t}){return t==="point"||t==="band"}function ve(t){if(t===void 0)return!0;const n=t.domain(),e=t(n[0]);for(let r=1,i=n.length;r<i;++r)if(t(n[r])-e)return!1;return!0}function uc(t,{domain:n,...e},r){for(const i of t)i.value!==void 0&&(n===void 0&&(n=i.value?.domain),i.value=r(i.value));return{domain:n===void 0?n:r(n),...e}}function _I(t){return bt(t,Dg)}function MI(t={}){let n;for(const e in t)if(Nt.has(e)&&ti(t[e])){if(n!==void 0)throw new Error("ambiguous scale definition; multiple scales found");n=C3(fd(e,t[e]))}if(n===void 0)throw new Error("invalid scale definition; no scale found");return n}function $I(t){return n=>{if(!Nt.has(n=`${n}`))throw new Error(`unknown scale: ${n}`);return t[n]}}function C3({scale:t,type:n,domain:e,range:r,interpolate:i,interval:o,transform:u,percent:a,pivot:c}){if(n==="identity")return{type:"identity",apply:s=>s,invert:s=>s};const f=t.unknown?t.unknown():void 0;return{type:n,domain:Xu(e),...r!==void 0&&{range:Xu(r)},...u!==void 0&&{transform:u},...a&&{percent:a},...f!==void 0&&{unknown:f},...o!==void 0&&{interval:o},...i!==void 0&&{interpolate:i},...t.clamp&&{clamp:t.clamp()},...c!==void 0&&{pivot:c,symmetric:!1},...t.base&&{base:t.base()},...t.exponent&&{exponent:t.exponent()},...t.constant&&{constant:t.constant()},...t.align&&{align:t.align(),round:t.round()},...t.padding&&(t.paddingInner?{paddingInner:t.paddingInner(),paddingOuter:t.paddingOuter()}:{padding:t.padding()}),...t.bandwidth&&{bandwidth:t.bandwidth(),step:t.step()},apply:s=>t(s),...t.invert&&{invert:s=>t.invert(s)}}}function AI(t,n){const{fx:e,fy:r}=cd(t,n),i=e?.scale.domain(),o=r?.scale.domain();return i&&o?wb(i,o).map(([u,a],c)=>({x:u,y:a,i:c})):i?i.map((u,a)=>({x:u,i:a})):o?o.map((u,a)=>({y:u,i:a})):void 0}function SI(t,{x:n,y:e}){return n&&=sd(n),e&&=sd(e),t.filter(n&&e?r=>n.has(r.x)&&e.has(r.y):n?r=>n.has(r.x):r=>e.has(r.y)).sort(n&&e?(r,i)=>n.get(r.x)-n.get(i.x)||e.get(r.y)-e.get(i.y):n?(r,i)=>n.get(r.x)-n.get(i.x):(r,i)=>e.get(r.y)-e.get(i.y))}function Qg(t,{fx:n,fy:e}){const r=Je(t),i=n?.value,o=e?.value;return n&&e?jr(r,u=>(u.fx=i[u[0]],u.fy=o[u[0]],u),u=>i[u],u=>o[u]):n?jr(r,u=>(u.fx=i[u[0]],u),u=>i[u]):jr(r,u=>(u.fy=o[u[0]],u),u=>o[u])}function TI(t,n,{marginTop:e,marginLeft:r}){const i=t?({x:u})=>t(u)-r:()=>0,o=n?({y:u})=>n(u)-e:()=>0;return function(u){this.tagName==="svg"?(this.setAttribute("x",i(u)),this.setAttribute("y",o(u))):this.setAttribute("transform",`translate(${i(u)},${o(u)})`)}}function EI(t){const n=[],e=new Uint32Array(Kn(t,r=>r.length));for(const r of t){let i=0;for(const o of t)r!==o&&(e.set(o,i),i+=o.length);n.push(e.slice(0,i))}return n}const kI=new Map([["top",jl],["right",Ql],["bottom",Zl],["left",Kl],["top-left",ac(jl,Kl)],["top-right",ac(jl,Ql)],["bottom-left",ac(Zl,Kl)],["bottom-right",ac(Zl,Ql)],["top-empty",RI],["right-empty",LI],["bottom-empty",II],["left-empty",zI],["empty",PI]]);function NI(t){if(t==null)return null;const n=kI.get(`${t}`.toLowerCase());if(n)return n;throw new Error(`invalid facet anchor: ${t}`)}const A2=new WeakMap;function sd(t){let n=A2.get(t);return n||A2.set(t,n=new gr(bt(t,(e,r)=>[e,r]))),n}function Pr(t,n){return sd(t).get(n)}function CI(t,n,e){return n=qe(n),e=qe(e),t.find(r=>Object.is(qe(r.x),n)&&Object.is(qe(r.y),e))}function Bs(t,n,e){return CI(t,n,e)?.empty}function jl(t,{y:n},{y:e}){return n?Pr(n,e)===0:!0}function Zl(t,{y:n},{y:e}){return n?Pr(n,e)===n.length-1:!0}function Kl(t,{x:n},{x:e}){return n?Pr(n,e)===0:!0}function Ql(t,{x:n},{x:e}){return n?Pr(n,e)===n.length-1:!0}function RI(t,{y:n},{x:e,y:r,empty:i}){if(i)return!1;if(!n)return;const o=Pr(n,r);if(o>0)return Bs(t,e,n[o-1])}function II(t,{y:n},{x:e,y:r,empty:i}){if(i)return!1;if(!n)return;const o=Pr(n,r);if(o<n.length-1)return Bs(t,e,n[o+1])}function zI(t,{x:n},{x:e,y:r,empty:i}){if(i)return!1;if(!n)return;const o=Pr(n,e);if(o>0)return Bs(t,n[o-1],r)}function LI(t,{x:n},{x:e,y:r,empty:i}){if(i)return!1;if(!n)return;const o=Pr(n,e);if(o<n.length-1)return Bs(t,n[o+1],r)}function PI(t,n,{empty:e}){return e}function ac(t,n){return function(){return t.apply(null,arguments)&&n.apply(null,arguments)}}function Jl(t,{channels:{fx:n,fy:e},groups:r}){return n&&e?t.map(({x:i,y:o})=>r.get(i)?.get(o)??[]):n?t.map(({x:i})=>r.get(i)??[]):t.map(({y:i})=>r.get(i)??[])}const R3=Math.PI,Re=2*R3,th=.618;function DI({projection:t,inset:n=0,insetTop:e=n,insetRight:r=n,insetBottom:i=n,insetLeft:o=n}={},u){if(t==null)return;if(typeof t.stream=="function")return t;let a,c,f="frame";if(In(t)){let x;if({type:t,domain:c,inset:x,insetTop:e=x!==void 0?x:e,insetRight:r=x!==void 0?x:r,insetBottom:i=x!==void 0?x:i,insetLeft:o=x!==void 0?x:o,clip:f=f,...a}=t,t==null)return}typeof t!="function"&&({type:t}=ld(t));const{width:s,height:h,marginLeft:l,marginRight:d,marginTop:p,marginBottom:m}=u,y=s-l-d-o-r,b=h-p-m-e-i;if(t=t?.({width:y,height:b,clip:f,...a}),t==null)return;f=OI(f,l,p,s-d,h-m);let w=l+o,v=p+e,g;if(c!=null){const[[x,_],[M,A]]=ko(t).bounds(c),T=Math.min(y/(M-x),b/(A-_));T>0?(w-=(T*(x+M)-y)/2,v-=(T*(_+A)-b)/2,g=Du({point(E,S){this.stream.point(E*T+w,S*T+v)}})):Xe("Warning: the projection could not be fit to the specified domain; using the default scale.")}return g??=w===0&&v===0?I3():Du({point(x,_){this.stream.point(x+w,_+v)}}),{stream:x=>t.stream(g.stream(f(x)))}}function ld(t){switch(`${t}`.toLowerCase()){case"albers-usa":return Gn(Pv,.7463,.4673);case"albers":return cc(l0,.7463,.4673);case"azimuthal-equal-area":return Gn(Ov,4,4);case"azimuthal-equidistant":return Gn(Fv,Re,Re);case"conic-conformal":return cc(Uv,Re,Re);case"conic-equal-area":return cc(Ou,6.1702,2.9781);case"conic-equidistant":return cc(Hv,7.312,3.6282);case"equal-earth":return Gn(Gv,5.4133,2.6347);case"equirectangular":return Gn(Xv,Re,R3);case"gnomonic":return Gn(Vv,3.4641,3.4641);case"identity":return{type:I3};case"reflect-y":return{type:FI};case"mercator":return Gn(Bv,Re,Re);case"orthographic":return Gn(jv,2,2);case"stereographic":return Gn(Zv,2,2);case"transverse-mercator":return Gn(Kv,Re,Re);default:throw new Error(`unknown projection type: ${t}`)}}function OI(t,n,e,r,i){if(t===!1||t==null||typeof t=="number")return o=>o;switch(t===!0&&(t="frame"),`${t}`.toLowerCase()){case"frame":return da(n,e,r,i);default:throw new Error(`unknown projection clip type: ${t}`)}}function Gn(t,n,e){return{type:({width:r,height:i,rotate:o,precision:u=.15,clip:a})=>{const c=t();return u!=null&&c.precision?.(u),o!=null&&c.rotate?.(o),typeof a=="number"&&c.clipAngle?.(a),r!=null&&(c.scale(Math.min(r/n,i/e)),c.translate([r/2,i/2])),c},aspectRatio:e/n}}function cc(t,n,e){const{type:r,aspectRatio:i}=Gn(t,n,e);return{type:o=>{const{parallels:u,domain:a,width:c,height:f}=o,s=r(o);return u!=null&&(s.parallels(u),a===void 0&&c!=null&&s.fitSize([c,f],{type:"Sphere"})),s},aspectRatio:i}}const I3=jt({stream:t=>t}),FI=jt(Du({point(t,n){this.stream.point(t,-n)}}));function z3(t,n,e,r){const i=e[t],o=e[n],u=i.length,a=e[t]=new Float64Array(u).fill(NaN),c=e[n]=new Float64Array(u).fill(NaN);let f;const s=r.stream({point(h,l){a[f]=h,c[f]=l}});for(f=0;f<u;++f)s.point(i[f],o[f])}function BI({projection:t}={}){return t==null?!1:typeof t.stream=="function"?!0:(In(t)&&(t=t.type),t!=null)}function qI(t){if(typeof t?.stream=="function")return th;if(In(t)){let n,e;if({domain:n,type:t,...e}=t,n!=null&&t!=null){const r=typeof t=="string"?ld(t).type:t,[[i,o],[u,a]]=ko(r({...e,width:100,height:100})).bounds(n),c=(a-o)/(u-i);return c&&isFinite(c)?c<.2?.2:c>5?5:c:th}}if(t!=null){if(typeof t!="function"){const{aspectRatio:n}=ld(t);if(n)return n}return th}}function Ra(t,n,{projection:e}){const{x:r,y:i}=t;let o={};return r&&(o.x=r),i&&(o.y=i),o=Ds(o,n),e&&r?.scale==="x"&&i?.scale==="y"&&z3("x","y",o,e),r&&(o.x=An(o.x)),i&&(o.y=An(o.y)),o}function YI(t){const n=[],e=[],r={scale:"x",value:n},i={scale:"y",value:e},o={point(u,a){n.push(u),e.push(a)},lineStart(){},lineEnd(){},polygonStart(){},polygonEnd(){},sphere(){}};for(const u of t.value)Pn(u,o);return[r,i]}function UI({x:t,y:n}){if(t||n)return t??=e=>e,n??=e=>e,Du({point(e,r){this.stream.point(t(e),n(r))}})}function qs(t={}){const{document:n=typeof window<"u"?window.document:void 0,clip:e}=t;return{document:n,clip:j_(e)}}function ht(t,{document:n}){return _t(So(t).call(n.documentElement))}const hd=Symbol("unset");function bo(t){return(t.length===1?XI:WI)(t)}function XI(t){let n,e=hd;return r=>(Object.is(e,r)||(e=r,n=t(r)),n)}function WI(t){let n,e;return(...r)=>((e?.length!==r.length||e.some((i,o)=>!Object.is(i,r[o])))&&(e=r,n=t(...r)),n)}const HI=bo(t=>new Intl.NumberFormat(t)),GI=bo((t,n)=>new Intl.DateTimeFormat(t,{timeZone:"UTC",...n&&{month:n}})),VI=bo((t,n)=>new Intl.DateTimeFormat(t,{timeZone:"UTC",...n&&{weekday:n}}));function L3(t="en-US"){const n=HI(t);return e=>e!=null&&!isNaN(e)?n.format(e):void 0}function jI(t="en-US",n="short"){const e=GI(t,n);return r=>r!=null&&!isNaN(r=+new Date(Date.UTC(2e3,+r)))?e.format(r):void 0}function ZI(t="en-US",n="short"){const e=VI(t,n);return r=>r!=null&&!isNaN(r=+new Date(Date.UTC(2001,0,+r)))?e.format(r):void 0}function P3(t){return BC(t,"Invalid Date")}function KI(t="en-US"){const n=L3(t);return e=>(e instanceof Date?P3:typeof e=="number"?n:Vt)(e)}const wo=KI(),Zt=(typeof window<"u"?window.devicePixelRatio>1:typeof it>"u")?0:.5;let QI=0,JI=0;function Jg(){return`plot-clip-${++QI}`}function tz(){return`plot-pattern-${++JI}`}function D3(t,{title:n,href:e,ariaLabel:r,ariaDescription:i,ariaHidden:o,target:u,fill:a,fillOpacity:c,stroke:f,strokeWidth:s,strokeOpacity:h,strokeLinejoin:l,strokeLinecap:d,strokeMiterlimit:p,strokeDasharray:m,strokeDashoffset:y,opacity:b,mixBlendMode:w,imageFilter:v,paintOrder:g,pointerEvents:x,shapeRendering:_,channels:M},{ariaLabel:A,fill:T="currentColor",fillOpacity:E,stroke:S="none",strokeOpacity:R,strokeWidth:C,strokeLinecap:$,strokeLinejoin:N,strokeMiterlimit:k,paintOrder:I}){T===null&&(a=null,c=null),S===null&&(f=null,h=null),St(T)?!St(S)&&(!St(a)||M?.fill)&&(S="none"):St(S)&&(!St(f)||M?.stroke)&&(T="none");const[L,z]=yn(a,T),[P,D]=Lt(c,E),[B,Y]=yn(f,S),[U,W]=Lt(h,R),[Z,q]=Lt(b);yo(Y)||(s===void 0&&(s=C),d===void 0&&(d=$),l===void 0&&(l=N),p===void 0&&!aR(l)&&(p=k),!yo(z)&&g===void 0&&(g=I));const[H,F]=Lt(s);return T!==null&&(t.fill=Pt(z,"currentColor"),t.fillOpacity=Jo(D,1)),S!==null&&(t.stroke=Pt(Y,"none"),t.strokeWidth=Jo(F,1),t.strokeOpacity=Jo(W,1),t.strokeLinejoin=Pt(l,"miter"),t.strokeLinecap=Pt(d,"butt"),t.strokeMiterlimit=Jo(p,4),t.strokeDasharray=Pt(m,"none"),t.strokeDashoffset=Pt(y,"0")),t.target=Vt(u),t.ariaLabel=Vt(A),t.ariaDescription=Vt(i),t.ariaHidden=Vt(o),t.opacity=Jo(q,1),t.mixBlendMode=Pt(w,"normal"),t.imageFilter=Pt(v,"none"),t.paintOrder=Pt(g,"normal"),t.pointerEvents=Pt(x,"auto"),t.shapeRendering=Pt(_,"auto"),{title:{value:n,optional:!0,filter:null},href:{value:e,optional:!0,filter:null},ariaLabel:{value:r,optional:!0,filter:null},fill:{value:L,scale:"auto",optional:!0},fillOpacity:{value:P,scale:"auto",optional:!0},stroke:{value:B,scale:"auto",optional:!0},strokeOpacity:{value:U,scale:"auto",optional:!0},strokeWidth:{value:H,optional:!0},opacity:{value:Z,scale:"auto",optional:!0}}}function nz(t,n){n&&t.filter(e=>$g(n[e])).append("title").call(rz,n)}function ez(t,n){n&&t.filter(([e])=>$g(n[e])).append("title").call(iz,n)}function rz(t,n){n&&t.text(e=>wo(n[e]))}function iz(t,n){n&&t.text(([e])=>wo(n[e]))}function Ct(t,{target:n,tip:e},{ariaLabel:r,title:i,fill:o,fillOpacity:u,stroke:a,strokeOpacity:c,strokeWidth:f,opacity:s,href:h}){r&&et(t,"aria-label",l=>r[l]),o&&et(t,"fill",l=>o[l]),u&&et(t,"fill-opacity",l=>u[l]),a&&et(t,"stroke",l=>a[l]),c&&et(t,"stroke-opacity",l=>c[l]),f&&et(t,"stroke-width",l=>f[l]),s&&et(t,"opacity",l=>s[l]),h&&F3(t,l=>h[l],n),e||nz(t,i)}function Nf(t,{target:n,tip:e},{ariaLabel:r,title:i,fill:o,fillOpacity:u,stroke:a,strokeOpacity:c,strokeWidth:f,opacity:s,href:h}){r&&et(t,"aria-label",([l])=>r[l]),o&&et(t,"fill",([l])=>o[l]),u&&et(t,"fill-opacity",([l])=>u[l]),a&&et(t,"stroke",([l])=>a[l]),c&&et(t,"stroke-opacity",([l])=>c[l]),f&&et(t,"stroke-width",([l])=>f[l]),s&&et(t,"opacity",([l])=>s[l]),h&&F3(t,([l])=>h[l],n),e||ez(t,i)}function oz({ariaLabel:t,title:n,fill:e,fillOpacity:r,stroke:i,strokeOpacity:o,strokeWidth:u,opacity:a,href:c},{tip:f}){return[t,f?void 0:n,e,r,i,o,u,a,c].filter(s=>s!==void 0)}function tp(t,n,e){const r=je(t,i=>n[i]);return e===void 0&&r.size>1+t.length>>1&&Xe("Warning: the implicit z channel has high cardinality. This may occur when the fill or stroke channel is associated with quantitative data rather than ordinal or categorical data. You can suppress this warning by setting the z option explicitly; if this data represents a single series, set z to null."),r.values()}function*O3(t,n,e,r){const{z:i}=e,{z:o}=r,u=oz(r,e),a=[...n,...u];for(const c of o?tp(t,o,i):[t]){let f,s;t:for(const h of c){for(const l of a)if(!Kt(l[h])){s&&s.push(-1);continue t}if(f===void 0){s&&(yield s),f=u.map(l=>qe(l[h])),s=[h];continue}s.push(h);for(let l=0;l<u.length;++l)if(qe(u[l][h])!==f[l]){yield s,f=u.map(p=>qe(p[h])),s=[h];continue t}}s&&(yield s)}}function uz(t,n,e,r){let i;const{clip:o=r.clip}=n;o==="frame"?(t=ht("svg:g",r).each(function(){this.appendChild(t.node()),t.node=()=>this}),i=cz(r,e)):o&&(i=sz(o,r)),et(t,"aria-label",n.ariaLabel),et(t,"aria-description",n.ariaDescription),et(t,"aria-hidden",n.ariaHidden),et(t,"clip-path",i)}function az(t){const n=new WeakMap;return(e,r)=>{let i=n.get(e);if(!i){const o=Jg();_t(e.ownerSVGElement).append("clipPath").attr("id",o).call(t,e,r),n.set(e,i=`url(#${o})`)}return i}}const cz=az((t,n,e)=>{const{width:r,height:i,marginLeft:o,marginRight:u,marginTop:a,marginBottom:c}=e;t.append("rect").attr("x",o).attr("y",a).attr("width",r-u-o).attr("height",i-a-c)}),S2=new WeakMap,fz={type:"Sphere"};function sz(t,n){let e,r;if((e=S2.get(n))||S2.set(n,e=new WeakMap),t.type==="Sphere"&&(t=fz),!(r=e.get(t))){const i=Jg();_t(n.ownerSVGElement).append("clipPath").attr("id",i).append("path").attr("d",n.path()(t)),e.set(t,r=`url(#${i})`)}return r}function At(t,n,e,r){uz(t,n,e,r),et(t,"class",n.className),et(t,"fill",n.fill),et(t,"fill-opacity",n.fillOpacity),et(t,"stroke",n.stroke),et(t,"stroke-width",n.strokeWidth),et(t,"stroke-opacity",n.strokeOpacity),et(t,"stroke-linejoin",n.strokeLinejoin),et(t,"stroke-linecap",n.strokeLinecap),et(t,"stroke-miterlimit",n.strokeMiterlimit),et(t,"stroke-dasharray",n.strokeDasharray),et(t,"stroke-dashoffset",n.strokeDashoffset),et(t,"shape-rendering",n.shapeRendering),et(t,"filter",n.imageFilter),et(t,"paint-order",n.paintOrder);const{pointerEvents:i=r.pointerSticky===!1?"none":void 0}=n;et(t,"pointer-events",i)}function pt(t,n){lz(t,"mix-blend-mode",n.mixBlendMode),et(t,"opacity",n.opacity)}function F3(t,n,e){t.each(function(r){const i=n(r);if(i!=null){const o=this.ownerDocument.createElementNS(me.svg,"a");o.setAttribute("fill","inherit"),o.setAttributeNS(me.xlink,"href",i),e!=null&&o.setAttribute("target",e),this.parentNode.insertBefore(o,this).appendChild(this)}})}function et(t,n,e){e!=null&&t.attr(n,e)}function lz(t,n,e){e!=null&&t.style(n,e)}function vt(t,n,{x:e,y:r},i=Zt,o=Zt){i+=n.dx,o+=n.dy,e?.bandwidth&&(i+=e.bandwidth()/2),r?.bandwidth&&(o+=r.bandwidth()/2),(i||o)&&t.attr("transform",`translate(${i},${o})`)}function Pt(t,n){if((t=Vt(t))!==n)return t}function Jo(t,n){if((t=$t(t))!==n)return t}const hz=/^-?([_a-z]|[\240-\377]|\\[0-9a-f]{1,6}(\r\n|[ \t\r\n\f])?|\\[^\r\n\f0-9a-f])([_a-z0-9-]|[\240-\377]|\\[0-9a-f]{1,6}(\r\n|[ \t\r\n\f])?|\\[^\r\n\f0-9a-f])*$/i;function np(t){if(t===void 0)return"plot-d6a7b5";if(t=`${t}`,!hz.test(t))throw new Error(`invalid class name: ${t}`);return t}function ep(t,n){if(typeof n=="string")t.property("style",n);else if(n!=null)for(const e of t)Object.assign(e.style,n)}function Ae({frameAnchor:t},{width:n,height:e,marginTop:r,marginRight:i,marginBottom:o,marginLeft:u}){return[/left$/.test(t)?u:/right$/.test(t)?n-i:(u+n-i)/2,/^top/.test(t)?r:/^bottom/.test(t)?e-o:(r+e-o)/2]}class xt{constructor(n,e={},r={},i){const{facet:o="auto",facetAnchor:u,fx:a,fy:c,sort:f,dx:s=0,dy:h=0,margin:l=0,marginTop:d=l,marginRight:p=l,marginBottom:m=l,marginLeft:y=l,className:b,clip:w=i?.clip,channels:v,tip:g,render:x}=r;if(this.data=n,this.sort=Ar(f)?f:null,this.initializer=un(r).initializer,this.transform=this.initializer?r.transform:ee(r).transform,o===null||o===!1?this.facet=null:(this.facet=Rn(o===!0?"include":o,"facet",["auto","include","exclude","super"]),this.fx=n===xu&&typeof a=="string"?[a]:a,this.fy=n===xu&&typeof c=="string"?[c]:c),this.facetAnchor=NI(u),e=zg(e),v!==void 0&&(e={...dz(v),...e}),i!==void 0&&(e={...D3(this,r,i),...e}),this.channels=Object.fromEntries(Object.entries(e).map(([_,M])=>{if(ue(M.value)){const{value:A,label:T=M.label,scale:E=M.scale}=M.value;M={...M,label:T,scale:E,value:A}}if(n===xu&&typeof M.value=="string"){const{value:A}=M;M={...M,value:[A]}}return[_,M]}).filter(([_,{value:M,optional:A}])=>{if(M!=null)return!0;if(A)return!1;throw new Error(`missing channel value: ${_}`)})),this.dx=+s,this.dy=+h,this.marginTop=+d,this.marginRight=+p,this.marginBottom=+m,this.marginLeft=+y,this.clip=j_(w),this.tip=gz(g),this.className=Vt(b),this.facet==="super"){if(a||c)throw new Error("super-faceting cannot use fx or fy");for(const _ in this.channels){const{scale:M}=e[_];if(!(M!=="x"&&M!=="y"))throw new Error("super-faceting cannot use x or y")}}x!=null&&(this.render=Zu(x,this.render))}initialize(n,e,r){let i=mo(this.data);n===void 0&&i!=null&&(n=[Je(i)]);const o=n;this.transform!=null&&({facets:n,data:i}=this.transform(i,n,r),i=mo(i)),n!==void 0&&(n.original=o);const u=s3(this.channels,i);return this.sort!=null&&RR(i,n,u,e,this.sort),{data:i,facets:n,channels:u}}filter(n,e,r){for(const i in e){const{filter:o=Kt}=e[i];if(o!==null){const u=r[i];n=n.filter(a=>o(u[a]))}}return n}project(n,e,r){for(const i in n)if(n[i].scale==="x"&&/^x|x$/.test(i)){const o=i.replace(/^x|x$/,"y");o in n&&n[o].scale==="y"&&z3(i,o,e,r.projection)}}scale(n,e,r){const i=Ds(n,e);return r.projection&&this.project(n,i,r),i}}function Wn(...t){return t.plot=xt.prototype.plot,t}function Zu(t,n){if(t==null)return n===null?void 0:n;if(n==null)return t===null?void 0:t;if(typeof t!="function")throw new TypeError(`invalid render transform: ${t}`);if(typeof n!="function")throw new TypeError(`invalid render transform: ${n}`);return function(e,r,i,o,u,a){return t.call(this,e,r,i,o,u,(c,f,s,h,l)=>n.call(this,c,f,s,h,l,a))}}function dz(t){return Object.fromEntries(Object.entries(zg(t)).map(([n,e])=>(e=typeof e=="string"?{value:e,label:n}:Oo(e),e.filter===void 0&&e.scale==null&&(e={...e,filter:null}),[n,e])))}function gz(t){return t===!0?"xy":t===!1||t==null?null:typeof t=="string"?Rn(t,"tip",["x","y","xy"]):t}function xi(t,n){return t?.tip===!0?{...t,tip:n}:In(t?.tip)&&t.tip.pointer===void 0?{...t,tip:{...t.tip,pointer:n}}:t}function pz(t,n,e={}){let r=.5-Zt,i=.5+Zt,o=.5+Zt,u=.5-Zt;for(const{marginTop:m,marginRight:y,marginBottom:b,marginLeft:w}of n)m>r&&(r=m),y>i&&(i=y),b>o&&(o=b),w>u&&(u=w);let{margin:a,marginTop:c=a!==void 0?a:r,marginRight:f=a!==void 0?a:i,marginBottom:s=a!==void 0?a:o,marginLeft:h=a!==void 0?a:u}=e;c=+c,f=+f,s=+s,h=+h;let{width:l=640,height:d=mz(t,e,{width:l,marginTopDefault:r,marginRightDefault:i,marginBottomDefault:o,marginLeftDefault:u})+Math.max(0,c-r+s-o)}=e;l=+l,d=+d;const p={width:l,height:d,marginTop:c,marginRight:f,marginBottom:s,marginLeft:h};if(t.fx||t.fy){let{margin:m,marginTop:y=m!==void 0?m:c,marginRight:b=m!==void 0?m:f,marginBottom:w=m!==void 0?m:s,marginLeft:v=m!==void 0?m:h}=e.facet??{};y=+y,b=+b,w=+w,v=+v,p.facet={marginTop:y,marginRight:b,marginBottom:w,marginLeft:v}}return p}function mz({x:t,y:n,fy:e,fx:r},{projection:i,aspectRatio:o},{width:u,marginTopDefault:a,marginRightDefault:c,marginBottomDefault:f,marginLeftDefault:s}){const h=e&&e.scale.domain().length||1,l=qI(i);if(l){const p=r?r.scale.domain().length:1,m=(1.1*h-.1)/(1.1*p-.1)*l,y=Math.max(.1,Math.min(10,m));return Math.round((u-s-c)*y+a+f)}const d=n?vi(n)?n.scale.domain().length||1:Math.max(7,17/h):1;if(o!=null){if(o=+o,!(isFinite(o)&&o>0))throw new Error(`invalid aspectRatio: ${o}`);const p=T2("y",n)/(T2("x",t)*o),m=r?r.scale.bandwidth():1,y=e?e.scale.bandwidth():1,b=m*(u-s-c)-t.insetLeft-t.insetRight;return(p*b+n.insetTop+n.insetBottom)/y+a+f}return!!(n||e)*Math.max(1,Math.min(60,d*h))*20+!!r*30+60}function T2(t,n){if(!n)throw new Error(`aspectRatio requires ${t} scale`);const{type:e,domain:r}=n;let i;switch(e){case"linear":case"utc":case"time":i=Number;break;case"pow":{const a=n.scale.exponent();i=c=>Math.pow(c,a);break}case"log":i=Math.log;break;case"point":case"band":return r.length;default:throw new Error(`unsupported ${t} scale for aspectRatio: ${e}`)}const[o,u]=Tt(r);return Math.abs(i(u)-i(o))}const E2=new WeakMap;function rp(t,n,{x:e,y:r,px:i,py:o,maxRadius:u=40,channels:a,render:c,...f}={}){return u=+u,i!=null&&(e??=null,a={...a,px:{value:i,scale:"x"}}),o!=null&&(r??=null,a={...a,py:{value:o,scale:"y"}}),{x:e,y:r,channels:a,...f,render:Zu(function(s,h,l,d,p,m){p={...p,pointerSticky:!1};const y=p.ownerSVGElement,{data:b}=p.getMarkState(this);let w=E2.get(y);w||E2.set(y,w={sticky:!1,roots:[],renders:[]});let v=w.renders.push(Y)-1;const{x:g,y:x,fx:_,fy:M}=h;let A=_?_(s.fx)-d.marginLeft:0,T=M?M(s.fy)-d.marginTop:0;g?.bandwidth&&(A+=g.bandwidth()/2),x?.bandwidth&&(T+=x.bandwidth()/2);const E=s.fi!=null;let S;if(E){let q=w.facetStates;q||(w.facetStates=q=new Map),S=q.get(this),S||q.set(this,S=new Map)}const[R,C]=Ae(this,d),{px:$,py:N}=l,k=$?q=>$[q]:B3(l,R),I=N?q=>N[q]:q3(l,C);let L,z,P,D;function B(q,H){if(E)if(D&&(D=cancelAnimationFrame(D)),q==null)S.delete(s.fi);else{S.set(s.fi,H),D=requestAnimationFrame(()=>{D=null;for(const[F,X]of S)if(X<H||X===H&&F<s.fi){q=null;break}Y(q)});return}Y(q)}function Y(q){if(L===q&&P===w.sticky)return;L=q,P=p.pointerSticky=w.sticky;const H=L==null?[]:[L];E&&(H.fx=s.fx,H.fy=s.fy,H.fi=s.fi);const F=m(H,h,l,d,p);if(z){if(E){const X=z.parentNode,G=z.getAttribute("transform"),O=F.getAttribute("transform");G?F.setAttribute("transform",G):F.removeAttribute("transform"),O?X.setAttribute("transform",O):X.removeAttribute("transform"),F.removeAttribute("aria-label"),F.removeAttribute("aria-description"),F.removeAttribute("aria-hidden")}z.replaceWith(F)}if(w.roots[v]=z=F,!(L==null&&S?.size>1)){const X=L==null?null:Qe(b)?b[L]:b.get(L);p.dispatchValue(X)}return F}function U(q){if(w.sticky||q.pointerType==="mouse"&&q.buttons===1)return;let[H,F]=Mn(q);H-=A,F-=T;const X=H<d.marginLeft||H>d.width-d.marginRight?1:t,G=F<d.marginTop||F>d.height-d.marginBottom?1:n;let O=null,rt=u*u;for(const Q of s){const ft=X*(k(Q)-H),lt=G*(I(Q)-F),j=ft*ft+lt*lt;j<=rt&&(O=Q,rt=j)}if(O!=null&&(t!==1||n!==1)){const Q=k(O)-H,ft=I(O)-F;rt=Q*Q+ft*ft}B(O,rt)}function W(q){q.pointerType==="mouse"&&L!=null&&(w.sticky&&w.roots.some(H=>H?.contains(q.target))||(w.sticky?(w.sticky=!1,w.renders.forEach(H=>H(null))):(w.sticky=!0,Y(L)),q.stopImmediatePropagation()))}function Z(q){q.pointerType==="mouse"&&(w.sticky||B(null))}return y.addEventListener("pointerenter",U),y.addEventListener("pointermove",U),y.addEventListener("pointerdown",W),y.addEventListener("pointerleave",Z),Y(null)},c)}}function ip(t){return rp(1,1,t)}function op(t){return rp(1,.01,t)}function Cf(t){return rp(.01,1,t)}function B3({x1:t,x2:n,x:e=t},r){return t&&n?i=>(t[i]+n[i])/2:e?i=>e[i]:()=>r}function q3({y1:t,y2:n,y:e=t},r){return t&&n?i=>(t[i]+n[i])/2:e?i=>e[i]:()=>r}function Y3(t){return vi(t)&&t.interval===void 0?void 0:"tabular-nums"}function yz(t,n){let{label:e=t.label,tickSize:r=6,width:i=240,height:o=44+r,marginTop:u=18,marginRight:a=0,marginBottom:c=16+r,marginLeft:f=0,style:s,ticks:h=(i-f-a)/64,tickFormat:l,fontVariant:d=Y3(t),round:p=!0,opacity:m,className:y}=n;const b=qs(n);y=np(y),m=Lt(m)[1],l===null&&(l=()=>null);const w=ht("svg",b).attr("class",`${y}-ramp`).attr("font-family","system-ui, sans-serif").attr("font-size",10).attr("width",i).attr("height",o).attr("viewBox",`0 0 ${i} ${o}`).call(R=>R.append("style").text(`:where(.${y}-ramp) {
  display: block;
  height: auto;
  height: intrinsic;
  max-width: 100%;
  overflow: visible;
}
:where(.${y}-ramp text) {
  white-space: pre;
}`)).call(ep,s);let v=R=>R.selectAll(".tick line").attr("y1",u+c-o),g;const x=p?(R,C)=>R.rangeRound(C):(R,C)=>R.range(C),{type:_,domain:M,range:A,interpolate:T,scale:E,pivot:S}=t;if(T){const R=A===void 0?T:ca(T.length===1?Zg(T):T,A);g=x(E.copy(),qn(Gt(f,i-a),Math.min(M.length+(S!==void 0),A===void 0?1/0:A.length)));const C=256,$=b.document.createElement("canvas");$.width=C,$.height=1;const N=$.getContext("2d");for(let k=0,I=C-1;k<C;++k)N.fillStyle=R(k/I),N.fillRect(k,0,1,1);w.append("image").attr("opacity",m).attr("x",f).attr("y",u).attr("width",i-f-a).attr("height",o-u-c).attr("preserveAspectRatio","none").attr("xlink:href",$.toDataURL())}else if(_==="threshold"){const R=M,C=l===void 0?$=>$:typeof l=="string"?pi(l):l;g=x(ds().domain([-1,A.length-1]),[f,i-a]),w.append("g").attr("fill-opacity",m).selectAll().data(A).enter().append("rect").attr("x",($,N)=>g(N-1)).attr("y",u).attr("width",($,N)=>g(N)-g(N-1)).attr("height",o-u-c).attr("fill",$=>$),h=bt(R,($,N)=>N),l=$=>C(R[$],$)}else g=x(ba().domain(M),[f,i-a]),w.append("g").attr("fill-opacity",m).selectAll().data(M).enter().append("rect").attr("x",g).attr("y",u).attr("width",Math.max(0,g.bandwidth()-1)).attr("height",o-u-c).attr("fill",E),v=()=>{};return w.append("g").attr("transform",`translate(0,${o-c})`).call(Db(g).ticks(Array.isArray(h)?null:h,typeof l=="string"?l:void 0).tickFormat(typeof l=="function"?l:void 0).tickSize(r).tickValues(Array.isArray(h)?h:null)).attr("font-size",null).attr("font-family",null).attr("font-variant",Pt(d,"normal")).call(v).call(R=>R.select(".domain").remove()),e!==void 0&&w.append("text").attr("x",f).attr("y",u-6).attr("fill","currentColor").attr("font-weight","bold").text(e),w.node()}const Rf=Math.PI/180;function Fo(t,{marker:n,markerStart:e=n,markerMid:r=n,markerEnd:i=n}={}){t.markerStart=nh(e),t.markerMid=nh(r),t.markerEnd=nh(i)}function nh(t){if(t==null||t===!1)return null;if(t===!0)return N2;if(typeof t=="function")return t;switch(`${t}`.toLowerCase()){case"none":return null;case"arrow":return k2("auto");case"arrow-reverse":return k2("auto-start-reverse");case"dot":return bz;case"circle":case"circle-fill":return N2;case"circle-stroke":return wz;case"tick":return eh("auto");case"tick-x":return eh(90);case"tick-y":return eh(0)}throw new Error(`invalid marker: ${t}`)}function k2(t){return(n,e)=>ht("svg:marker",e).attr("viewBox","-5 -5 10 10").attr("markerWidth",6.67).attr("markerHeight",6.67).attr("orient",t).attr("fill","none").attr("stroke",n).attr("stroke-width",1.5).attr("stroke-linecap","round").attr("stroke-linejoin","round").call(r=>r.append("path").attr("d","M-1.5,-3l3,3l-3,3")).node()}function bz(t,n){return ht("svg:marker",n).attr("viewBox","-5 -5 10 10").attr("markerWidth",6.67).attr("markerHeight",6.67).attr("fill",t).attr("stroke","none").call(e=>e.append("circle").attr("r",2.5)).node()}function N2(t,n){return ht("svg:marker",n).attr("viewBox","-5 -5 10 10").attr("markerWidth",6.67).attr("markerHeight",6.67).attr("fill",t).attr("stroke","var(--plot-background)").attr("stroke-width",1.5).call(e=>e.append("circle").attr("r",3)).node()}function wz(t,n){return ht("svg:marker",n).attr("viewBox","-5 -5 10 10").attr("markerWidth",6.67).attr("markerHeight",6.67).attr("fill","var(--plot-background)").attr("stroke",t).attr("stroke-width",1.5).call(e=>e.append("circle").attr("r",3)).node()}function eh(t){return(n,e)=>ht("svg:marker",e).attr("viewBox","-3 -3 6 6").attr("markerWidth",6).attr("markerHeight",6).attr("orient",t).attr("stroke",n).call(r=>r.append("path").attr("d","M0,-3v6")).node()}let vz=0;function Ia(t,n,{stroke:e},r){return X3(t,n,e&&(i=>e[i]),null,r)}function xz(t,n,{stroke:e,z:r},i){return X3(t,n,e&&(([o])=>e[o]),r,i)}const dd=1,U3=2;function _z(t,n){const e=new Uint8Array(n.length),r=t.data().filter(o=>o.length>1),i=r.length;for(let o=0,u=hd;o<i;++o){const a=r[o];if(a.length>1){const c=a[0];u!==(u=qe(n[c]))&&(e[c]|=dd)}}for(let o=i-1,u=hd;o>=0;--o){const a=r[o];if(a.length>1){const c=a[0];u!==(u=qe(n[c]))&&(e[c]|=U3)}}return([o])=>e[o]}function X3(t,{markerStart:n,markerMid:e,markerEnd:r,stroke:i},o=()=>i,u,a){if(!n&&!e&&!r)return;const c=new Map,f=u&&_z(t,u);function s(h,l,d){return function(p){if(d&&!d(p))return;const m=o(p);let y=c.get(l);y||c.set(l,y=new Map);let b=y.get(m);if(!b){const w=this.parentNode.insertBefore(l(m,a),this),v=`plot-marker-${++vz}`;w.setAttribute("id",v),y.set(m,b=`url(#${v})`)}this.setAttribute(h,b)}}n&&t.each(s("marker-start",n,f&&(h=>f(h)&dd))),e&&f&&t.each(s("marker-start",e,h=>!(f(h)&dd))),e&&t.each(s("marker-mid",e)),r&&t.each(s("marker-end",r,f&&(h=>f(h)&U3)))}function za({inset:t,insetLeft:n,insetRight:e,...r}={}){return[n,e]=W3(t,n,e),{inset:t,insetLeft:n,insetRight:e,...r}}function La({inset:t,insetTop:n,insetBottom:e,...r}={}){return[n,e]=W3(t,n,e),{inset:t,insetTop:n,insetBottom:e,...r}}function W3(t,n,e){return t===void 0&&n===void 0&&e===void 0?Zt?[1,0]:[.5,.5]:[n,e]}function H3(t,{interval:n}){return t={...Oo(t)},t.interval=Rs(t.interval===void 0?n:t.interval),t}function Ys(t,n,e,r){const{[t]:i,[`${t}1`]:o,[`${t}2`]:u}=e,{value:a,interval:c}=H3(i,e);if(a==null||c==null&&!r)return e;const f=$e(i);if(c==null){let d;const p={transform:m=>d||(d=ct(m,a)),label:f};return{...e,[t]:void 0,[`${t}1`]:o===void 0?p:o,[`${t}2`]:u===void 0&&!(o===u&&r)?p:u}}let s,h;function l(d){return h!==void 0&&d===s?h:h=bt(ct(s=d,a),p=>c.floor(p))}return n({...e,[t]:void 0,[`${t}1`]:o===void 0?{transform:l,label:f}:o,[`${t}2`]:u===void 0?{transform:d=>l(d).map(p=>c.offset(p)),label:f}:u})}function G3(t,n,e){const{[t]:r}=e,{value:i,interval:o}=H3(r,e);return i==null||o==null?e:n({...e,[t]:{label:$e(r),transform:u=>{const a=bt(ct(u,i),f=>o.floor(f)),c=a.map(f=>o.offset(f));return a.map(bn(a)?(f,s)=>f==null||isNaN(f=+f)||(s=c[s],s==null)||isNaN(s=+s)?void 0:new Date((f+s)/2):(f,s)=>f==null||(s=c[s],s==null)?NaN:(+f+ +s)/2)}}})}function V3(t={}){return Ys("x",za,t,!0)}function j3(t={}){return Ys("y",La,t,!0)}function up(t={}){return Ys("x",za,t)}function ap(t={}){return Ys("y",La,t)}function Z3(t={}){return G3("x",za,t)}function K3(t={}){return G3("y",La,t)}const Q3={ariaLabel:"rule",fill:null,stroke:"currentColor"};class J3 extends xt{constructor(n,e={}){const{x:r,y1:i,y2:o,inset:u=0,insetTop:a=u,insetBottom:c=u}=e;super(n,{x:{value:r,scale:"x",optional:!0},y1:{value:i,scale:"y",optional:!0},y2:{value:o,scale:"y",optional:!0}},xi(e,"x"),Q3),this.insetTop=$t(a),this.insetBottom=$t(c),Fo(this,e)}render(n,e,r,i,o){const{x:u,y:a}=e,{x:c,y1:f,y2:s}=r,{width:h,height:l,marginTop:d,marginRight:p,marginLeft:m,marginBottom:y}=i,{insetTop:b,insetBottom:w}=this;return ht("svg:g",o).call(At,this,i,o).call(vt,this,{x:c&&u},Zt,0).call(v=>v.selectAll().data(n).enter().append("line").call(pt,this).attr("x1",c?g=>c[g]:(m+h-p)/2).attr("x2",c?g=>c[g]:(m+h-p)/2).attr("y1",f&&!ve(a)?g=>f[g]+b:d+b).attr("y2",s&&!ve(a)?a.bandwidth?g=>s[g]+a.bandwidth()-w:g=>s[g]-w:l-y-w).call(Ct,this,r).call(Ia,this,r,o)).node()}}class t6 extends xt{constructor(n,e={}){const{x1:r,x2:i,y:o,inset:u=0,insetRight:a=u,insetLeft:c=u}=e;super(n,{y:{value:o,scale:"y",optional:!0},x1:{value:r,scale:"x",optional:!0},x2:{value:i,scale:"x",optional:!0}},xi(e,"y"),Q3),this.insetRight=$t(a),this.insetLeft=$t(c),Fo(this,e)}render(n,e,r,i,o){const{x:u,y:a}=e,{y:c,x1:f,x2:s}=r,{width:h,height:l,marginTop:d,marginRight:p,marginLeft:m,marginBottom:y}=i,{insetLeft:b,insetRight:w}=this;return ht("svg:g",o).call(At,this,i,o).call(vt,this,{y:c&&a},0,Zt).call(v=>v.selectAll().data(n).enter().append("line").call(pt,this).attr("x1",f&&!ve(u)?g=>f[g]+b:m+b).attr("x2",s&&!ve(u)?u.bandwidth?g=>s[g]+u.bandwidth()-w:g=>s[g]-w:h-p-w).attr("y1",c?g=>c[g]:(d+l-y)/2).attr("y2",c?g=>c[g]:(d+l-y)/2).call(Ct,this,r).call(Ia,this,r,o)).node()}}function Sr(t,n){let{x:e=nt,y:r,y1:i,y2:o,...u}=ap(n);return[i,o]=n6(r,i,o),new J3(t,{...u,x:e,y1:i,y2:o})}function Tr(t,n){let{y:e=nt,x:r,x1:i,x2:o,...u}=up(n);return[i,o]=n6(r,i,o),new t6(t,{...u,y:e,x1:i,x2:o})}function n6(t,n,e){if(t==null){if(n===void 0){if(e!==void 0)return[0,e]}else if(e===void 0)return[0,n]}else{if(n===void 0)return e===void 0?[0,t]:[t,e];if(e===void 0)return[t,n]}return[n,e]}function Pa(t,...n){let e=n.length;for(let r=0,i=!0;r<e;++r)typeof n[r]!="function"&&(i&&(t=t.slice(),i=!1),t.splice(r,2,t[r]+n[r]+t[r+1]),n.splice(r,1),--r,--e);return r=>{let i=t[0];for(let o=0;o<e;++o)i+=n[o](r)+t[o+1];return i}}const Mz={ariaLabel:"text",strokeLinejoin:"round",strokeWidth:3,paintOrder:"stroke"},e6="­";class Us extends xt{constructor(n,e={}){const{x:r,y:i,text:o=tr(n)&&V_(n)?nt:Dt,frameAnchor:u,textAnchor:a=/right$/i.test(u)?"end":/left$/i.test(u)?"start":"middle",lineAnchor:c=/^top/i.test(u)?"top":/^bottom/i.test(u)?"bottom":"middle",lineHeight:f=1,lineWidth:s=1/0,textOverflow:h,monospace:l,fontFamily:d=l?"ui-monospace, monospace":void 0,fontSize:p,fontStyle:m,fontVariant:y,fontWeight:b,rotate:w}=e,[v,g]=Lt(w,0),[x,_]=Tz(p);if(super(n,{x:{value:r,scale:"x",optional:!0},y:{value:i,scale:"y",optional:!0},fontSize:{value:x,optional:!0},rotate:{value:nR(v),optional:!0},text:{value:o,filter:$g,optional:!0}},e,Mz),this.rotate=g,this.textAnchor=Pt(a,"middle"),this.lineAnchor=Rn(c,"lineAnchor",["top","middle","bottom"]),this.lineHeight=+f,this.lineWidth=+s,this.textOverflow=r6(h),this.monospace=!!l,this.fontFamily=Vt(d),this.fontSize=_,this.fontStyle=Vt(m),this.fontVariant=Vt(y),this.fontWeight=Vt(b),this.frameAnchor=Sa(u),!(this.lineWidth>=0))throw new Error(`invalid lineWidth: ${s}`);this.splitLines=a6(this),this.clipLine=c6(this)}render(n,e,r,i,o){const{x:u,y:a}=e,{x:c,y:f,rotate:s,text:h,title:l,fontSize:d}=r,{rotate:p}=this,[m,y]=Ae(this,i);return ht("svg:g",o).call(At,this,i,o).call(u6,this,h,i).call(vt,this,{x:c&&u,y:f&&a}).call(b=>b.selectAll().data(n).enter().append("text").call(pt,this).call($z,this,h,l).attr("transform",Pa`translate(${c?w=>c[w]:m},${f?w=>f[w]:y})${s?w=>` rotate(${s[w]})`:p?` rotate(${p})`:""}`).call(et,"font-size",d&&(w=>d[w])).call(Ct,this,r)).node()}}function r6(t){return t==null?null:Rn(t,"textOverflow",["clip","ellipsis","clip-start","clip-end","ellipsis-start","ellipsis-middle","ellipsis-end"]).replace(/^(clip|ellipsis)$/,"$1-end")}function $z(t,n,e,r){if(!e)return;const{lineAnchor:i,lineHeight:o,textOverflow:u,splitLines:a,clipLine:c}=n;t.each(function(f){const s=a(wo(e[f])??"").map(c),h=s.length,l=i==="top"?.71:i==="bottom"?1-h:(164-h*100)/200;if(h>1){let d=0;for(let p=0;p<h;++p){if(++d,!s[p])continue;const m=this.ownerDocument.createElementNS(me.svg,"tspan");m.setAttribute("x",0),p===d-1?m.setAttribute("y",`${(l+p)*o}em`):m.setAttribute("dy",`${d*o}em`),m.textContent=s[p],this.appendChild(m),d=0}}else l&&this.setAttribute("y",`${l*o}em`),this.textContent=s[0];if(u&&!r&&s[0]!==e[f]){const d=this.ownerDocument.createElementNS(me.svg,"title");d.textContent=e[f],this.appendChild(d)}})}function vo(t,{x:n,y:e,...r}={}){return r.frameAnchor===void 0&&([n,e]=ne(n,e)),new Us(t,{...r,x:n,y:e})}function i6(t,{x:n=nt,...e}={}){return new Us(t,K3({...e,x:n}))}function o6(t,{y:n=nt,...e}={}){return new Us(t,Z3({...e,y:n}))}function u6(t,n,e){et(t,"text-anchor",n.textAnchor),et(t,"font-family",n.fontFamily),et(t,"font-size",n.fontSize),et(t,"font-style",n.fontStyle),et(t,"font-variant",n.fontVariant===void 0?Az(e):n.fontVariant),et(t,"font-weight",n.fontWeight)}function Az(t){return t&&(Ui(t)||bn(t))?"tabular-nums":void 0}const Sz=new Set(["inherit","initial","revert","unset","xx-small","x-small","small","medium","large","x-large","xx-large","xxx-large","larger","smaller"]);function Tz(t){return t==null||typeof t=="number"?[void 0,t]:typeof t!="string"?[t,void 0]:(t=t.trim().toLowerCase(),Sz.has(t)||/^[+-]?\d*\.?\d+(e[+-]?\d+)?(\w*|%)$/.test(t)?[void 0,t]:[t,void 0])}function Ez(t,n,e){const r=[];let i,o=0;for(const[u,a,c]of kz(t)){if(i===void 0&&(i=u),o>i&&e(t,i,a)>n&&(r.push(t.slice(i,o)+(t[o-1]===e6?"-":"")),i=u),c){r.push(t.slice(i,a)),i=void 0;continue}o=a}return r}function*kz(t){let n=0,e=0;const r=t.length;for(;e<r;){let i=1;switch(t[e]){case e6:case"-":++e,yield[n,e,!1],n=e;break;case" ":for(yield[n,e,!1];t[++e]===" ";);n=e;break;case"\r":t[e+1]===`
`&&++i;case`
`:yield[n,e,!0],e+=i,n=e;break;default:++e;break}}yield[n,e,!0]}const C2={a:56,b:63,c:57,d:63,e:58,f:37,g:62,h:60,i:26,j:26,k:55,l:26,m:88,n:60,o:60,p:62,q:62,r:39,s:54,t:38,u:60,v:55,w:79,x:54,y:55,z:55,A:69,B:67,C:73,D:74,E:61,F:58,G:76,H:75,I:28,J:55,K:67,L:58,M:89,N:75,O:78,P:65,Q:78,R:67,S:65,T:65,U:75,V:69,W:98,X:69,Y:67,Z:67,0:64,1:48,2:62,3:64,4:66,5:63,6:65,7:58,8:65,9:65," ":29,"!":32,'"':49,"'":31,"(":39,")":39,",":31,"-":48,".":31,"/":32,":":31,";":31,"?":52,"‘":31,"’":31,"“":47,"”":47,"…":82};function cp(t,n=0,e=t.length){let r=0;for(let i=n;i<e;i=Bo(t,i))r+=C2[t[i]]??(s6(t,i)?120:C2.e);return r}function fp(t,n=0,e=t.length){let r=0;for(let i=n;i<e;i=Bo(t,i))r+=s6(t,i)?126:63;return r}function a6({monospace:t,lineWidth:n,textOverflow:e}){if(e!=null||n==1/0)return o=>o.split(/\r\n?|\n/g);const r=t?fp:cp,i=n*100;return o=>Ez(o,i,r)}function c6({monospace:t,lineWidth:n,textOverflow:e}){if(e==null||n==1/0)return o=>o;const r=t?fp:cp,i=n*100;switch(e){case"clip-start":return o=>I2(o,i,r,"");case"clip-end":return o=>R2(o,i,r,"");case"ellipsis-start":return o=>I2(o,i,r,Zi);case"ellipsis-middle":return o=>Nz(o,i,r,Zi);case"ellipsis-end":return o=>R2(o,i,r,Zi)}}const Zi="…";function xo(t,n,e,r){const i=[];let o=0;for(let u=0,a=0,c=t.length;u<c;u=a){a=Bo(t,u);const f=e(t,u,a);if(o+f>n){for(o+=r;o>n&&u>0;)a=u,u=i.pop(),o-=e(t,u,a);return[u,n-o]}o+=f,i.push(u)}return[-1,0]}function R2(t,n,e,r){t=t.trim();const i=e(r),[o]=xo(t,n,e,i);return o<0?t:t.slice(0,o).trimEnd()+r}function Nz(t,n,e,r){t=t.trim();const i=e(t);if(i<=n)return t;const o=e(r)/2,[u,a]=xo(t,n/2,e,o),[c]=xo(t,i-n/2-a+o,e,-o);return c<0?r:t.slice(0,u).trimEnd()+r+t.slice(Bo(t,c)).trimStart()}function I2(t,n,e,r){t=t.trim();const i=e(t);if(i<=n)return t;const o=e(r),[u]=xo(t,i-n+o,e,-o);return u<0?r:r+t.slice(Bo(t,u)).trimStart()}const gd=/[\p{Combining_Mark}\p{Emoji_Modifier}]+/uy,z2=new RegExp("\\p{Extended_Pictographic}","uy");function Bo(t,n){return n+=Cz(t,n)?2:1,Iz(t,n)&&(n=gd.lastIndex),Rz(t,n)?Bo(t,n+1):n}function f6(t,n){return t.charCodeAt(n)<128}function Cz(t,n){const e=t.charCodeAt(n);if(e>=55296&&e<56320){const r=t.charCodeAt(n+1);return r>=56320&&r<57344}return!1}function Rz(t,n){return t.charCodeAt(n)===8205}function Iz(t,n){return f6(t,n)?!1:(gd.lastIndex=n,gd.test(t))}function s6(t,n){return f6(t,n)?!1:(z2.lastIndex=n,z2.test(t))}const l6={ariaLabel:"vector",fill:"none",stroke:"currentColor",strokeWidth:1.5,strokeLinejoin:"round",strokeLinecap:"round"},h6=3.5,zz=h6*5,d6={draw(t,n,e){const r=n*e/zz;t.moveTo(0,0),t.lineTo(0,-n),t.moveTo(-r,r-n),t.lineTo(0,-n),t.lineTo(r,r-n)}},g6={draw(t,n,e){t.moveTo(-e,0),t.lineTo(0,-n),t.lineTo(e,0)}},Lz=new Map([["arrow",d6],["spike",g6]]);function Pz(t){return t&&typeof t.draw=="function"}function Dz(t){if(Pz(t))return t;const n=Lz.get(`${t}`.toLowerCase());if(n)return n;throw new Error(`invalid shape: ${t}`)}class Xs extends xt{constructor(n,e={}){const{x:r,y:i,r:o=h6,length:u,rotate:a,shape:c=d6,anchor:f="middle",frameAnchor:s}=e,[h,l]=Lt(u,12),[d,p]=Lt(a,0);super(n,{x:{value:r,scale:"x",optional:!0},y:{value:i,scale:"y",optional:!0},length:{value:h,scale:"length",optional:!0},rotate:{value:d,optional:!0}},e,l6),this.r=+o,this.length=l,this.rotate=p,this.shape=Dz(c),this.anchor=Rn(f,"anchor",["start","middle","end"]),this.frameAnchor=Sa(s)}render(n,e,r,i,o){const{x:u,y:a}=e,{x:c,y:f,length:s,rotate:h}=r,{length:l,rotate:d,anchor:p,shape:m,r:y}=this,[b,w]=Ae(this,i);return ht("svg:g",o).call(At,this,i,o).call(vt,this,{x:c&&u,y:f&&a}).call(v=>v.selectAll().data(n).enter().append("path").call(pt,this).attr("transform",Pa`translate(${c?g=>c[g]:b},${f?g=>f[g]:w})${h?g=>` rotate(${h[g]})`:d?` rotate(${d})`:""}${p==="start"?"":p==="end"?s?g=>` translate(0,${s[g]})`:` translate(0,${l})`:s?g=>` translate(0,${s[g]/2})`:` translate(0,${l/2})`}`).attr("d",s?g=>{const x=se();return m.draw(x,s[g],y),x}:(()=>{const g=se();return m.draw(g,l,y),g})()).call(Ct,this,r)).node()}}function p6(t,n={}){let{x:e,y:r,...i}=n;return n.frameAnchor===void 0&&([e,r]=ne(e,r)),new Xs(t,{...i,x:e,y:r})}function m6(t,n={}){const{x:e=nt,...r}=n;return new Xs(t,{...r,x:e})}function y6(t,n={}){const{y:e=nt,...r}=n;return new Xs(t,{...r,y:e})}function Oz(t,n={}){const{shape:e=g6,stroke:r=l6.stroke,strokeWidth:i=1,fill:o=r,fillOpacity:u=.3,anchor:a="start",...c}=n;return p6(t,{...c,shape:e,stroke:r,strokeWidth:i,fill:o,fillOpacity:u,anchor:a})}function Dr(t,n){return arguments.length<2&&!tr(t)&&(n=t,t=null),n===void 0&&(n={}),[t,n]}function Ws({anchor:t}={},n){return t===void 0?n[0]:Rn(t,"anchor",n)}function b6(t){return Ws(t,["left","right"])}function w6(t){return Ws(t,["right","left"])}function v6(t){return Ws(t,["bottom","top"])}function x6(t){return Ws(t,["top","bottom"])}function sp(){const[t,n]=Dr(...arguments);return $6("y",b6(n),t,n)}function _6(){const[t,n]=Dr(...arguments);return $6("fy",w6(n),t,n)}function lp(){const[t,n]=Dr(...arguments);return A6("x",v6(n),t,n)}function M6(){const[t,n]=Dr(...arguments);return A6("fx",x6(n),t,n)}function $6(t,n,e,{color:r="currentColor",opacity:i=1,stroke:o=r,strokeOpacity:u=i,strokeWidth:a=1,fill:c=r,fillOpacity:f=i,textAnchor:s,textStroke:h,textStrokeOpacity:l,textStrokeWidth:d,tickSize:p=t==="y"?6:0,tickPadding:m,tickRotate:y,x:b,margin:w,marginTop:v=w===void 0?20:w,marginRight:g=w===void 0?n==="right"?40:0:w,marginBottom:x=w===void 0?20:w,marginLeft:_=w===void 0?n==="left"?40:0:w,label:M,labelAnchor:A,labelArrow:T,labelOffset:E,ariaLabel:S=`${t}-axis`,...R}){return p=$t(p),m=$t(m),y=$t(y),A!==void 0&&(A=Rn(A,"labelAnchor",["center","top","bottom"])),T=D6(T),Wn(p&&!St(o)?Fz(t,n,e,{stroke:o,strokeOpacity:u,strokeWidth:a,tickSize:p,tickPadding:m,tickRotate:y,x:b,ariaLabel:S,...R}):null,St(c)?null:qz(t,n,e,{fill:c,fillOpacity:f,stroke:h,strokeOpacity:l,strokeWidth:d,textAnchor:s,tickSize:p,tickPadding:m,tickRotate:y,x:b,marginTop:v,marginRight:g,marginBottom:x,marginLeft:_,ariaLabel:S,...R}),!St(c)&&M!==null?vo([],I6({fill:c,fillOpacity:f,...R},function(C,$,N,k,I){const L=k[t],{marginTop:z,marginRight:P,marginBottom:D,marginLeft:B}=t==="y"&&I.inset||I,Y=A??(L.bandwidth?"center":"top"),U=E??(n==="right"?P:B)-3;return Y==="center"?(this.textAnchor=void 0,this.lineAnchor=n==="right"?"bottom":"top",this.frameAnchor=n,this.rotate=-90):(this.textAnchor=n==="right"?"end":"start",this.lineAnchor=Y,this.frameAnchor=`${Y}-${n}`,this.rotate=0),this.dy=Y==="top"?3-z:Y==="bottom"?D-3:0,this.dx=n==="right"?U:-U,this.ariaLabel=`${S} label`,{facets:[[0]],channels:{text:{value:[P6(t,L,{anchor:n,label:M,labelAnchor:Y,labelArrow:T})]}}}})):null)}function A6(t,n,e,{color:r="currentColor",opacity:i=1,stroke:o=r,strokeOpacity:u=i,strokeWidth:a=1,fill:c=r,fillOpacity:f=i,textAnchor:s,textStroke:h,textStrokeOpacity:l,textStrokeWidth:d,tickSize:p=t==="x"?6:0,tickPadding:m,tickRotate:y,y:b,margin:w,marginTop:v=w===void 0?n==="top"?30:0:w,marginRight:g=w===void 0?20:w,marginBottom:x=w===void 0?n==="bottom"?30:0:w,marginLeft:_=w===void 0?20:w,label:M,labelAnchor:A,labelArrow:T,labelOffset:E,ariaLabel:S=`${t}-axis`,...R}){return p=$t(p),m=$t(m),y=$t(y),A!==void 0&&(A=Rn(A,"labelAnchor",["center","left","right"])),T=D6(T),Wn(p&&!St(o)?Bz(t,n,e,{stroke:o,strokeOpacity:u,strokeWidth:a,tickSize:p,tickPadding:m,tickRotate:y,y:b,ariaLabel:S,...R}):null,St(c)?null:Yz(t,n,e,{fill:c,fillOpacity:f,stroke:h,strokeOpacity:l,strokeWidth:d,textAnchor:s,tickSize:p,tickPadding:m,tickRotate:y,y:b,marginTop:v,marginRight:g,marginBottom:x,marginLeft:_,ariaLabel:S,...R}),!St(c)&&M!==null?vo([],I6({fill:c,fillOpacity:f,...R},function(C,$,N,k,I){const L=k[t],{marginTop:z,marginRight:P,marginBottom:D,marginLeft:B}=t==="x"&&I.inset||I,Y=A??(L.bandwidth?"center":"right"),U=E??(n==="top"?z:D)-3;return Y==="center"?(this.frameAnchor=n,this.textAnchor=void 0):(this.frameAnchor=`${n}-${Y}`,this.textAnchor=Y==="right"?"end":"start"),this.lineAnchor=n,this.dy=n==="top"?-U:U,this.dx=Y==="right"?P-3:Y==="left"?3-B:0,this.ariaLabel=`${S} label`,{facets:[[0]],channels:{text:{value:[P6(t,L,{anchor:n,label:M,labelAnchor:Y,labelArrow:T})]}}}})):null)}function Fz(t,n,e,{strokeWidth:r=1,strokeLinecap:i=null,strokeLinejoin:o=null,facetAnchor:u=n+(t==="y"?"-empty":""),frameAnchor:a=n,tickSize:c,inset:f=0,insetLeft:s=f,insetRight:h=f,dx:l=0,y:d=t==="y"?void 0:null,ariaLabel:p,...m}){return qo(y6,t,e,{ariaLabel:`${p} tick`,ariaHidden:!0},{strokeWidth:r,strokeLinecap:i,strokeLinejoin:o,facetAnchor:u,frameAnchor:a,y:d,...m,dx:n==="left"?+l-Zt+ +s:+l+Zt-h,anchor:"start",length:c,shape:n==="left"?Hz:Gz})}function Bz(t,n,e,{strokeWidth:r=1,strokeLinecap:i=null,strokeLinejoin:o=null,facetAnchor:u=n+(t==="x"?"-empty":""),frameAnchor:a=n,tickSize:c,inset:f=0,insetTop:s=f,insetBottom:h=f,dy:l=0,x:d=t==="x"?void 0:null,ariaLabel:p,...m}){return qo(m6,t,e,{ariaLabel:`${p} tick`,ariaHidden:!0},{strokeWidth:r,strokeLinejoin:o,strokeLinecap:i,facetAnchor:u,frameAnchor:a,x:d,...m,dy:n==="bottom"?+l-Zt-h:+l+Zt+ +s,anchor:"start",length:c,shape:n==="bottom"?Xz:Wz})}function qz(t,n,e,{facetAnchor:r=n+(t==="y"?"-empty":""),frameAnchor:i=n,tickSize:o,tickRotate:u=0,tickPadding:a=Math.max(3,9-o)+(Math.abs(u)>60?4*Math.cos(u*Rf):0),text:c,textAnchor:f=Math.abs(u)>60?"middle":n==="left"?"end":"start",lineAnchor:s=u>60?"top":u<-60?"bottom":"middle",fontVariant:h,inset:l=0,insetLeft:d=l,insetRight:p=l,dx:m=0,ariaLabel:y,y:b=t==="y"?void 0:null,...w}){return qo(o6,t,e,{ariaLabel:`${y} tick label`},{facetAnchor:r,frameAnchor:i,text:c,textAnchor:f,lineAnchor:s,fontVariant:h,rotate:u,y:b,...w,dx:n==="left"?+m-o-a+ +d:+m+ +o+ +a-p},function(v,g,x,_,M){h===void 0&&(this.fontVariant=L6(v)),c===void 0&&(M.text=z6(v,g,x,_,n))})}function Yz(t,n,e,{facetAnchor:r=n+(t==="x"?"-empty":""),frameAnchor:i=n,tickSize:o,tickRotate:u=0,tickPadding:a=Math.max(3,9-o)+(Math.abs(u)>=10?4*Math.cos(u*Rf):0),text:c,textAnchor:f=Math.abs(u)>=10?u<0^n==="bottom"?"start":"end":"middle",lineAnchor:s=Math.abs(u)>=10?"middle":n==="bottom"?"top":"bottom",fontVariant:h,inset:l=0,insetTop:d=l,insetBottom:p=l,dy:m=0,x:y=t==="x"?void 0:null,ariaLabel:b,...w}){return qo(i6,t,e,{ariaLabel:`${b} tick label`},{facetAnchor:r,frameAnchor:i,text:c===void 0?null:c,textAnchor:f,lineAnchor:s,fontVariant:h,rotate:u,x:y,...w,dy:n==="bottom"?+m+ +o+ +a-p:+m-o-a+ +d},function(v,g,x,_,M){h===void 0&&(this.fontVariant=L6(v)),c===void 0&&(M.text=z6(v,g,x,_,n))})}function S6(){const[t,n]=Dr(...arguments);return N6("y",b6(n),t,n)}function T6(){const[t,n]=Dr(...arguments);return N6("fy",w6(n),t,n)}function E6(){const[t,n]=Dr(...arguments);return C6("x",v6(n),t,n)}function k6(){const[t,n]=Dr(...arguments);return C6("fx",x6(n),t,n)}function N6(t,n,e,{y:r=t==="y"?void 0:null,x:i=null,x1:o=n==="left"?i:null,x2:u=n==="right"?i:null,ariaLabel:a=`${t}-grid`,ariaHidden:c=!0,...f}){return qo(Tr,t,e,{ariaLabel:a,ariaHidden:c},{y:r,x1:o,x2:u,...R6(f)})}function C6(t,n,e,{x:r=t==="x"?void 0:null,y:i=null,y1:o=n==="top"?i:null,y2:u=n==="bottom"?i:null,ariaLabel:a=`${t}-grid`,ariaHidden:c=!0,...f}){return qo(Sr,t,e,{ariaLabel:a,ariaHidden:c},{x:r,y1:o,y2:u,...R6(f)})}function R6({color:t="currentColor",opacity:n=.1,stroke:e=t,strokeOpacity:r=n,strokeWidth:i=1,...o}){return{stroke:e,strokeOpacity:r,strokeWidth:i,...o}}function I6({fill:t,fillOpacity:n,fontFamily:e,fontSize:r,fontStyle:i,fontVariant:o,fontWeight:u,monospace:a,pointerEvents:c,shapeRendering:f,clip:s=!1},h){return[,t]=yn(t),[,n]=Lt(n),{facet:"super",x:null,y:null,fill:t,fillOpacity:n,fontFamily:e,fontSize:r,fontStyle:i,fontVariant:o,fontWeight:u,monospace:a,pointerEvents:c,shapeRendering:f,clip:s,initializer:h}}function qo(t,n,e,r,i,o){let u;function a(s,h,l,d,p,m){const y=s==null&&(n==="fx"||n==="fy"),{[n]:b}=d;if(!b)throw new Error(`missing scale: ${n}`);const w=b.domain();let{interval:v,ticks:g,tickFormat:x,tickSpacing:_=n==="x"?80:35}=i;if(typeof g=="string"&&O6(b)&&(v=g,g=void 0),g===void 0&&(g=Do(v,b.type)??Uz(b,_)),s==null){if(tr(g))s=zr(g);else if(Yi(g))s=rh(g,...Tt(w));else if(b.interval){let A=b.interval;if(b.ticks){const[T,E]=Tt(w),S=(E-T)/A[$a];A=u2(A,S/g)??A,s=rh(A,T,E)}else{s=w;const T=s.length;A=u2(A,T/g)??A,A!==b.interval&&(s=rh(A,...Tt(s)))}if(A===b.interval){const T=Math.round(s.length/g);T>1&&(s=s.filter((E,S)=>S%T===0))}}else b.ticks?s=b.ticks(g):s=w;if(!b.ticks&&s.length&&s!==w){const A=new Bn(w);s=s.filter(T=>A.has(T)),s.length||Xe(`Warning: the ${n}-axis ticks appear to not align with the scale domain, resulting in no ticks. Try different ticks?`)}n==="y"||n==="x"?h=[Je(s)]:u[n]={scale:n,value:nt}}o?.call(this,b,s,g,x,u);const M=Object.fromEntries(Object.entries(u).map(([A,T])=>[A,{...T,value:ct(s,T.value)}]));return y&&(h=m.filterFacets(s,M)),{data:s,facets:h,channels:M}}const c=un(i).initializer,f=t(e,un({...i,initializer:a},c));return e==null?(u=f.channels,f.channels={}):u={},r!==void 0&&Object.assign(f,r),f.clip===void 0&&(f.clip=!1),f}function Uz(t,n){const[e,r]=Tt(t.range());return(r-e)/n}function z6(t,n,e,r,i){return{value:hp(t,n,e,r,i)}}function hp(t,n,e,r,i){return typeof r=="function"&&!(t.type==="log"&&t.tickFormat)?r:r===void 0&&n&&bn(n)?HC(t.type,n,i)??wo:t.tickFormat?t.tickFormat(typeof e=="number"?e:null,r):typeof r=="string"&&t.domain().length>0?(bn(t.domain())?bi:pi)(r):r===void 0?wo:jt(r)}function rh(t,n,e){return t.range(n,t.offset(t.floor(e)))}const Xz={draw(t,n){t.moveTo(0,0),t.lineTo(0,n)}},Wz={draw(t,n){t.moveTo(0,0),t.lineTo(0,-n)}},Hz={draw(t,n){t.moveTo(0,0),t.lineTo(-n,0)}},Gz={draw(t,n){t.moveTo(0,0),t.lineTo(n,0)}};function L6(t){return t.bandwidth&&!t.interval?void 0:"tabular-nums"}function P6(t,n,{anchor:e,label:r=n.label,labelAnchor:i,labelArrow:o}={}){if(!(r==null||r.inferred&&O6(n)&&/^(date|time|year)$/i.test(r))){if(r=String(r),o==="auto"&&(o=(!n.bandwidth||n.interval)&&!/[↑↓→←]/.test(r)),!o)return r;if(o===!0){const u=A3(n);u&&(o=/x$/.test(t)||i==="center"?/x$/.test(t)===u<0?"left":"right":u<0?"up":"down")}switch(o){case"left":return`← ${r}`;case"right":return`${r} →`;case"up":return e==="right"?`${r} ↑`:`↑ ${r}`;case"down":return e==="right"?`${r} ↓`:`↓ ${r}`}return r}}function D6(t="auto"){return St(t)?!1:typeof t=="boolean"?t:Rn(t,"labelArrow",["auto","up","right","down","left"])}function O6(t){return bn(t.domain())}function L2(t,n){if(n==null)return n;const e=t(n);if(!e)throw new Error(`scale not found: ${n}`);return e}function Vz(t,{opacity:n,...e}={}){if(!vi(t)&&!N3(t))throw new Error(`swatches legend requires ordinal or threshold color scale (not ${t.type})`);return F6(t,e,(r,i,o,u)=>r.append("svg").attr("width",o).attr("height",u).attr("fill",i.scale).attr("fill-opacity",Lt(n)[1]).append("rect").attr("width","100%").attr("height","100%"))}function jz(t,{fill:n=t.hint?.fill!==void 0?t.hint.fill:"none",fillOpacity:e=1,stroke:r=t.hint?.stroke!==void 0?t.hint.stroke:St(n)?"currentColor":"none",strokeOpacity:i=1,strokeWidth:o=1.5,r:u=4.5,...a}={},c){const[f,s]=yn(n),[h,l]=yn(r),d=L2(c,f),p=L2(c,h),m=u*u*Math.PI;return e=Lt(e)[1],i=Lt(i)[1],o=Lt(o)[1],F6(t,a,(y,b,w,v)=>y.append("svg").attr("viewBox","-8 -8 16 16").attr("width",w).attr("height",v).attr("fill",f==="color"?g=>d.scale(g):s).attr("fill-opacity",e).attr("stroke",h==="color"?g=>p.scale(g):l).attr("stroke-opacity",i).attr("stroke-width",o).append("path").attr("d",g=>{const x=se();return t.scale(g).draw(x,m),x}))}function F6(t,n={},e){let{columns:r,tickFormat:i,fontVariant:o=Y3(t),swatchSize:u=15,swatchWidth:a=u,swatchHeight:c=u,marginLeft:f=0,className:s,style:h,width:l}=n;const d=qs(n);s=np(s),i=hp(t.scale,t.domain,void 0,i);const p=ht("div",d).attr("class",`${s}-swatches ${s}-swatches-${r!=null?"columns":"wrap"}`);let m;return r!=null?(m=`:where(.${s}-swatches-columns .${s}-swatch) {
  display: flex;
  align-items: center;
  break-inside: avoid;
  padding-bottom: 1px;
}
:where(.${s}-swatches-columns .${s}-swatch::before) {
  flex-shrink: 0;
}
:where(.${s}-swatches-columns .${s}-swatch-label) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}`,p.style("columns",r).selectAll().data(t.domain).enter().append("div").attr("class",`${s}-swatch`).call(e,t,a,c).call(y=>y.append("div").attr("class",`${s}-swatch-label`).attr("title",i).text(i))):(m=`:where(.${s}-swatches-wrap) {
  display: flex;
  align-items: center;
  min-height: 33px;
  flex-wrap: wrap;
}
:where(.${s}-swatches-wrap .${s}-swatch) {
  display: inline-flex;
  align-items: center;
  margin-right: 1em;
}`,p.selectAll().data(t.domain).enter().append("span").attr("class",`${s}-swatch`).call(e,t,a,c).append(function(){return this.ownerDocument.createTextNode(i.apply(this,arguments))})),p.call(y=>y.insert("style","*").text(`:where(.${s}-swatches) {
  font-family: system-ui, sans-serif;
  font-size: 10px;
  margin-bottom: 0.5em;
}
:where(.${s}-swatch > svg) {
  margin-right: 0.5em;
  overflow: visible;
}
${m}`)).style("margin-left",f?`${+f}px`:null).style("width",l===void 0?null:`${+l}px`).style("font-variant",Pt(o,"normal")).call(ep,h).node()}const If=new Map([["symbol",jz],["color",B6],["opacity",Qz]]);function Zz(t={}){for(const[n,e]of If){const r=t[n];if(ti(r)){const i=qs(t);let o;if(n==="symbol"){const{fill:u,stroke:a=u===void 0&&ti(t.color)?"color":void 0}=t;o={fill:u,stroke:a}}return e(fd(n,r,o),dp(i,r,t),u=>ti(t[u])?fd(u,t[u]):null)}}throw new Error("unknown legend type; no scale found")}function Kz(t,n,e={}){return(r,i)=>{if(!If.has(r))throw new Error(`unknown legend type: ${r}`);if(r in t)return If.get(r)(t[r],dp(n,e[r],i),o=>t[o])}}function dp({className:t,...n},{label:e,ticks:r,tickFormat:i}={},o){return cR(o,{className:t,...n},{label:e,ticks:r,tickFormat:i})}function B6(t,{legend:n=!0,...e}){if(n===!0&&(n=t.type==="ordinal"?"swatches":"ramp"),t.domain!==void 0)switch(`${n}`.toLowerCase()){case"swatches":return Vz(t,e);case"ramp":return yz(t,e);default:throw new Error(`unknown legend type: ${n}`)}}function Qz({type:t,interpolate:n,...e},{legend:r=!0,color:i=Qn(0,0,0),...o}){if(!n)throw new Error(`${t} opacity scales are not supported`);if(r===!0&&(r="ramp"),`${r}`.toLowerCase()!=="ramp")throw new Error(`${r} opacity legends are not supported`);return B6({type:t,...e,interpolate:Jz(i)},{legend:r,...o})}function Jz(t){const{r:n,g:e,b:r}=Qn(t)||Qn(0,0,0);return i=>`rgba(${n},${e},${r},${i})`}function tL(t,n,e){const r=[];for(const[i,o]of If){const u=e[i];if(u?.legend&&i in t){const a=o(t[i],dp(n,t[i],u),c=>t[c]);a!=null&&r.push(a)}}return r}function Hs(t={},n="x"){return Y_(t)?t:{...t,[n]:nt}}function Gs(t={},n="y"){return U_(t)?t:{...t,[n]:nt}}function q6(t,n){if(n.length===1)return{data:t,facets:n};const e=Wu(t),r=new Uint8Array(e);let i=0;for(const a of n)for(const c of a)r[c]&&++i,r[c]=1;if(i===0)return{data:t,facets:n};t=Xu(t);const o=t[P_]=new Uint32Array(e+i);n=n.map(a=>Xu(a,Uint32Array));let u=e;r.fill(0);for(const a of n)for(let c=0,f=a.length;c<f;++c){const s=a[c];r[s]?(a[c]=u,t[u]=t[s],o[u]=s,++u):o[s]=s,r[s]=1}return{data:t,facets:n}}function Y6(t={},n={}){arguments.length===1&&([t,n]=Yo(t));const{y1:e,y:r=e,x:i,...o}=n,[u,a,c,f]=Uo(r,i,"y","x",t,o);return{...u,y1:e,y:a,x1:c,x2:f,x:Tf(c,f)}}function nL(t={},n={}){arguments.length===1&&([t,n]=Yo(t));const{y1:e,y:r=e,x:i}=n,[o,u,a]=Uo(r,i,"y","x",t,n);return{...o,y1:e,y:u,x:a}}function eL(t={},n={}){arguments.length===1&&([t,n]=Yo(t));const{y1:e,y:r=e,x:i}=n,[o,u,,a]=Uo(r,i,"y","x",t,n);return{...o,y1:e,y:u,x:a}}function U6(t={},n={}){arguments.length===1&&([t,n]=Yo(t));const{x1:e,x:r=e,y:i,...o}=n,[u,a,c,f]=Uo(r,i,"x","y",t,o);return{...u,x1:e,x:a,y1:c,y2:f,y:Tf(c,f)}}function rL(t={},n={}){arguments.length===1&&([t,n]=Yo(t));const{x1:e,x:r=e,y:i}=n,[o,u,a]=Uo(r,i,"x","y",t,n);return{...o,x1:e,x:u,y:a}}function iL(t={},n={}){arguments.length===1&&([t,n]=Yo(t));const{x1:e,x:r=e,y:i}=n,[o,u,,a]=Uo(r,i,"x","y",t,n);return{...o,x1:e,x:u,y:a}}function Vs({x:t,x1:n,x2:e,...r}={}){return r=xi(r,"y"),n===void 0&&e===void 0?Y6({x:t,...r}):([n,e]=X_(t,n,e),{...r,x1:n,x2:e})}function js({y:t,y1:n,y2:e,...r}={}){return r=xi(r,"x"),n===void 0&&e===void 0?U6({y:t,...r}):([n,e]=X_(t,n,e),{...r,y1:n,y2:e})}function Yo(t){const{offset:n,order:e,reverse:r,...i}=t;return[{offset:n,order:e,reverse:r},i]}const oL={length:!0};function Uo(t,n=Ng,e,r,{offset:i,order:o,reverse:u},a){if(n===null)throw new Error(`stack requires ${r}`);const c=Lr(a),[f,s]=_n(t),[h,l]=Cn(n),[d,p]=Cn(n);return h.hint=d.hint=oL,i=uL(i),o=fL(o,i,r),[ee(a,(m,y,b)=>{({data:m,facets:y}=q6(m,y));const w=t==null?void 0:s(Ef(ct(m,t),b?.[e])),v=ct(m,n,Float64Array),g=ct(m,c),x=o&&o(m,w,v,g),_=Wu(m),M=l(new Float64Array(_)),A=p(new Float64Array(_)),T=[];for(const E of y){const S=w?Array.from(je(E,R=>w[R]).values()):[E];if(x)for(const R of S)R.sort(x);for(const R of S){let C=0,$=0;u&&R.reverse();for(const N of R){const k=v[N];k<0?C=A[N]=(M[N]=C)+k:k>0?$=A[N]=(M[N]=$)+k:A[N]=M[N]=$}}T.push(S)}return i&&i(T,M,A,g),{data:m,facets:y}}),f,h,d]}function uL(t){if(t!=null){if(typeof t=="function")return t;switch(`${t}`.toLowerCase()){case"expand":case"normalize":return aL;case"center":case"silhouette":return cL;case"wiggle":return W6}throw new Error(`unknown offset: ${t}`)}}function X6(t,n){let e=0,r=0;for(const i of t){const o=n[i];o<e&&(e=o),o>r&&(r=o)}return[e,r]}function aL(t,n,e){for(const r of t)for(const i of r){const[o,u]=X6(i,e);for(const a of i){const c=1/(u-o||1);n[a]=c*(n[a]-o),e[a]=c*(e[a]-o)}}}function cL(t,n,e){for(const r of t){for(const i of r){const[o,u]=X6(i,e);for(const a of i){const c=(u+o)/2;n[a]-=c,e[a]-=c}}H6(r,n,e)}G6(t,n,e)}function W6(t,n,e,r){for(const i of t){const o=new gr;let u=0;for(const a of i){let c=-1;const f=a.map(d=>Math.abs(e[d]-n[d])),s=a.map(d=>{c=r?r[d]:++c;const p=e[d]-n[d],m=o.has(c)?p-o.get(c):0;return o.set(c,p),m}),h=[0,...vb(s)];for(const d of a)n[d]+=u,e[d]+=u;const l=Kn(f);l&&(u-=Kn(f,(d,p)=>(s[p]/2+h[p])*d)/l)}H6(i,n,e)}G6(t,n,e)}function H6(t,n,e){const r=on(t,i=>on(i,o=>n[o]));for(const i of t)for(const o of i)n[o]-=r,e[o]-=r}function G6(t,n,e){const r=t.length;if(r===1)return;const i=t.map(a=>a.flat()),o=i.map(a=>(on(a,c=>n[c])+qt(a,c=>e[c]))/2),u=on(o);for(let a=0;a<r;a++){const c=u-o[a];for(const f of i[a])n[f]+=c,e[f]+=c}}function fL(t,n,e){if(t===void 0&&n===W6)return P2(Xn);if(t!=null){if(typeof t=="string"){const r=t.startsWith("-"),i=r?Af:Xn;switch((r?t.slice(1):t).toLowerCase()){case"value":case e:return sL(i);case"z":return lL(i);case"sum":return hL(i);case"appearance":return dL(i);case"inside-out":return P2(i)}return D2(D_(t))}if(typeof t=="function")return(t.length===1?D2:gL)(t);if(Qe(t))return pL(t);throw new Error(`invalid order: ${t}`)}}function sL(t){return(n,e,r)=>(i,o)=>t(r[i],r[o])}function lL(t){return(n,e,r,i)=>(o,u)=>t(i[o],i[u])}function hL(t){return Zs(t,(n,e,r,i)=>Yf(Je(n),o=>Kn(o,u=>r[u]),o=>i[o]))}function dL(t){return Zs(t,(n,e,r,i)=>Yf(Je(n),o=>e[ra(o,u=>r[u])],o=>i[o]))}function P2(t){return Zs(t,(n,e,r,i)=>{const o=Je(n),u=Yf(o,h=>e[ra(h,l=>r[l])],h=>i[h]),a=jr(o,h=>Kn(h,l=>r[l]),h=>i[h]),c=[],f=[];let s=0;for(const h of u)s<0?(s+=a.get(h),c.push(h)):(s-=a.get(h),f.push(h));return f.reverse().concat(c)})}function D2(t){return n=>{const e=ct(n,t);return(r,i)=>Xn(e[r],e[i])}}function gL(t){return n=>Qe(n)?(e,r)=>t(n[e],n[r]):(e,r)=>t(n.get(e),n.get(r))}function pL(t){return Zs(Xn,()=>t)}function Zs(t,n){return(e,r,i,o)=>{if(!o)throw new Error("missing channel: z");const u=new gr(n(e,r,i,o).map((a,c)=>[a,c]));return(a,c)=>t(u.get(o[a]),u.get(o[c]))}}const mL={ariaLabel:"rect"};class Ks extends xt{constructor(n,e={}){const{x1:r,y1:i,x2:o,y2:u}=e;super(n,{x1:{value:r,scale:"x",type:r!=null&&o==null?"band":void 0,optional:!0},y1:{value:i,scale:"y",type:i!=null&&u==null?"band":void 0,optional:!0},x2:{value:o,scale:"x",optional:!0},y2:{value:u,scale:"y",optional:!0}},e,mL),gp(this,e),pp(this,e)}render(n,e,r,i,o){const{x:u,y:a}=e;let{x1:c,y1:f,x2:s,y2:h}=r;const{marginTop:l,marginRight:d,marginBottom:p,marginLeft:m,width:y,height:b}=i,{projection:w}=o,{insetTop:v,insetRight:g,insetBottom:x,insetLeft:_}=this,{rx:M,ry:A,rx1y1:T,rx1y2:E,rx2y1:S,rx2y2:R}=this;(c||s)&&!w&&ve(u)&&(c=s=null),(f||h)&&!w&&ve(a)&&(f=h=null);const C=u?.bandwidth?u.bandwidth():0,$=a?.bandwidth?a.bandwidth():0;return ht("svg:g",o).call(At,this,i,o).call(vt,this,{},0,0).call(N=>N.selectAll().data(n).enter().call(T||E||S||R?k=>k.append("path").call(pt,this).call(mp,c&&s?I=>c[I]+(s[I]<c[I]?-g:_):c?I=>c[I]+_:m+_,f&&h?I=>f[I]+(h[I]<f[I]?-x:v):f?I=>f[I]+v:l+v,c&&s?I=>s[I]-(s[I]<c[I]?-_:g):c?I=>c[I]+C-g:y-d-g,f&&h?I=>h[I]-(h[I]<f[I]?-v:x):f?I=>f[I]+$-x:b-p-x,this).call(Ct,this,r):k=>k.append("rect").call(pt,this).attr("x",c?s?I=>Math.min(c[I],s[I])+_:I=>c[I]+_:m+_).attr("y",f?h?I=>Math.min(f[I],h[I])+v:I=>f[I]+v:l+v).attr("width",c?s?I=>Math.max(0,Math.abs(s[I]-c[I])+C-_-g):C-_-g:y-d-m-g-_).attr("height",f?h?I=>Math.max(0,Math.abs(f[I]-h[I])+$-v-x):$-v-x:b-l-p-v-x).call(et,"rx",M).call(et,"ry",A).call(Ct,this,r))).node()}}function gp(t,{inset:n=0,insetTop:e=n,insetRight:r=n,insetBottom:i=n,insetLeft:o=n}={}){t.insetTop=$t(e),t.insetRight=$t(r),t.insetBottom=$t(i),t.insetLeft=$t(o)}function pp(t,{r:n,rx:e,ry:r,rx1:i=n,ry1:o=n,rx2:u=n,ry2:a=n,rx1y1:c=i!==void 0?+i:o!==void 0?+o:0,rx1y2:f=i!==void 0?+i:a!==void 0?+a:0,rx2y1:s=u!==void 0?+u:o!==void 0?+o:0,rx2y2:h=u!==void 0?+u:a!==void 0?+a:0}={}){c||f||s||h?(t.rx1y1=c,t.rx1y2=f,t.rx2y1=s,t.rx2y2=h):(t.rx=Pt(e,"auto"),t.ry=Pt(r,"auto"))}function mp(t,n,e,r,i,o){const{rx1y1:u,rx1y2:a,rx2y1:c,rx2y2:f}=o;typeof n!="function"&&(n=jt(n)),typeof e!="function"&&(e=jt(e)),typeof r!="function"&&(r=jt(r)),typeof i!="function"&&(i=jt(i));const s=Math.max(Math.abs(u+c),Math.abs(a+f)),h=Math.max(Math.abs(u+a),Math.abs(c+f));t.attr("d",l=>{const d=n(l),p=e(l),m=r(l),y=i(l),b=d>m,w=p>y,v=b?m:d,g=b?d:m,x=w?y:p,_=w?p:y,M=Math.min(1,(g-v)/s,(_-x)/h),A=M*(b?w?f:c:w?a:u),T=M*(b?w?a:u:w?f:c),E=M*(b?w?u:a:w?c:f),S=M*(b?w?c:f:w?u:a);return`M${v},${x+sc(A,S)}A${A},${A} 0 0 ${A<0?0:1} ${v+fc(A,S)},${x}H${g-fc(T,E)}A${T},${T} 0 0 ${T<0?0:1} ${g},${x+sc(T,E)}V${_-sc(E,T)}A${E},${E} 0 0 ${E<0?0:1} ${g-fc(E,T)},${_}H${v+fc(S,A)}A${S},${S} 0 0 ${S<0?0:1} ${v},${_-sc(S,A)}Z`})}function fc(t,n){return n<0?t:Math.abs(t)}function sc(t,n){return n<0?Math.abs(t):t}function yp(t,n){return new Ks(t,V3(j3(n)))}function zf(t,n={}){return Po(n)||(n={...n,y:Dt,x2:nt,interval:1}),new Ks(t,Vs(j3(Hs(n))))}function Lf(t,n={}){return Po(n)||(n={...n,x:Dt,y2:nt,interval:1}),new Ks(t,js(V3(Gs(n))))}const yL={ariaLabel:"frame",fill:"none",stroke:"currentColor",clip:!1},bL={ariaLabel:"frame",fill:null,stroke:"currentColor",strokeLinecap:"square",clip:!1};class V6 extends xt{constructor(n={}){const{anchor:e=null}=n;super(xu,void 0,n,e==null?yL:bL),this.anchor=B_(e,"anchor",["top","right","bottom","left"]),gp(this,n),e||pp(this,n)}render(n,e,r,i,o){const{marginTop:u,marginRight:a,marginBottom:c,marginLeft:f,width:s,height:h}=i,{anchor:l,insetTop:d,insetRight:p,insetBottom:m,insetLeft:y}=this,{rx:b,ry:w,rx1y1:v,rx1y2:g,rx2y1:x,rx2y2:_}=this,M=f+y,A=s-a-p,T=u+d,E=h-c-m;return ht(l?"svg:line":v||g||x||_?"svg:path":"svg:rect",o).datum(0).call(At,this,i,o).call(pt,this).call(Ct,this,r).call(vt,this,{}).call(l==="left"?S=>S.attr("x1",M).attr("x2",M).attr("y1",T).attr("y2",E):l==="right"?S=>S.attr("x1",A).attr("x2",A).attr("y1",T).attr("y2",E):l==="top"?S=>S.attr("x1",M).attr("x2",A).attr("y1",T).attr("y2",T):l==="bottom"?S=>S.attr("x1",M).attr("x2",A).attr("y1",E).attr("y2",E):v||g||x||_?S=>S.call(mp,M,T,A,E,this):S=>S.attr("x",M).attr("y",T).attr("width",A-M).attr("height",E-T).attr("rx",b).attr("ry",w)).node()}}function bp(t){return new V6(t)}const ih={ariaLabel:"tip",fill:"var(--plot-background)",stroke:"currentColor"},wL=new Set(["geometry","href","src","ariaLabel","scales"]);class j6 extends xt{constructor(n,e={}){e.tip&&(e={...e,tip:!1}),e.title===void 0&&tr(n)&&V_(n)&&(e={...e,title:nt});const{x:r,y:i,x1:o,x2:u,y1:a,y2:c,anchor:f,preferredAnchor:s="bottom",monospace:h,fontFamily:l=h?"ui-monospace, monospace":void 0,fontSize:d,fontStyle:p,fontVariant:m,fontWeight:y,lineHeight:b=1,lineWidth:w=20,frameAnchor:v,format:g,textAnchor:x="start",textOverflow:_,textPadding:M=8,title:A,pointerSize:T=12,pathFilter:E="drop-shadow(0 3px 4px rgba(0,0,0,0.2))"}=e;super(n,{x:{value:o!=null&&u!=null?null:r,scale:"x",optional:!0},y:{value:a!=null&&c!=null?null:i,scale:"y",optional:!0},x1:{value:o,scale:"x",optional:u==null},y1:{value:a,scale:"y",optional:c==null},x2:{value:u,scale:"x",optional:o==null},y2:{value:c,scale:"y",optional:a==null},title:{value:A,optional:!0}},e,ih),this.anchor=rd(f,"anchor"),this.preferredAnchor=rd(s,"preferredAnchor"),this.frameAnchor=Sa(v),this.textAnchor=Pt(x,"middle"),this.textPadding=+M,this.pointerSize=+T,this.pathFilter=Vt(E),this.lineHeight=+b,this.lineWidth=+w,this.textOverflow=r6(_),this.monospace=!!h,this.fontFamily=Vt(l),this.fontSize=$t(d),this.fontStyle=Vt(p),this.fontVariant=Vt(m),this.fontWeight=Vt(y);for(const S in ih)S in this.channels&&(this[S]=ih[S]);this.splitLines=a6(this),this.clipLine=c6(this),this.format=typeof g=="string"||typeof g=="function"?{title:g}:{...g}}render(n,e,r,i,o){const u=this,{x:a,y:c,fx:f,fy:s}=e,{ownerSVGElement:h,document:l}=o,{anchor:d,monospace:p,lineHeight:m,lineWidth:y}=this,{textPadding:b,pointerSize:w,pathFilter:v}=this,{marginTop:g,marginLeft:x}=i,{x1:_,y1:M,x2:A,y2:T,x:E=_??A,y:S=M??T}=r,R=f?f(n.fx)-x:0,C=s?s(n.fy)-g:0,[$,N]=Ae(this,i),k=B3(r,$),I=q3(r,N),L=p?fp:cp,z=L(Zi);let P,D;"title"in r?(P=O2.call(this,{title:r.channels.title},e),D=ML):(P=O2.call(this,r.channels,e),D=$L);const B=ht("svg:g",o).call(At,this,i,o).call(u6,this).call(vt,this,{x:E&&a,y:S&&c}).call(W=>W.selectAll().data(n).enter().append("g").attr("transform",Z=>`translate(${Math.round(k(Z))},${Math.round(I(Z))})`).call(pt,this).call(Z=>Z.append("path").attr("filter",v)).call(Z=>Z.append("text").each(function(q){const H=_t(this);this.setAttribute("fill","currentColor"),this.setAttribute("fill-opacity",1),this.setAttribute("stroke","none");const F=D.call(u,q,n,P,e,r);if(typeof F=="string")for(const X of u.splitLines(F))Y(H,{value:u.clipLine(X)});else{const X=new Set;for(const G of F){const{label:O=""}=G;O&&X.has(O)||(X.add(O),Y(H,G))}}})));function Y(W,{label:Z,value:q,color:H,opacity:F}){Z??="",q??="";const X=H!=null||F!=null;let G,O=y*100;const[rt]=xo(Z,O,L,z);if(rt>=0)Z=Z.slice(0,rt).trimEnd()+Zi,G=q.trim(),q="";else{(Z||!q&&!X)&&(q=" "+q);const[ft]=xo(q,O-L(Z),L,z);ft>=0&&(G=q.trim(),q=q.slice(0,ft).trimEnd()+Zi)}const Q=W.append("tspan").attr("x",0).attr("dy",`${m}em`).text("​");Z&&Q.append("tspan").attr("font-weight","bold").text(Z),q&&Q.append(()=>l.createTextNode(q)),X&&Q.append("tspan").text(" ■").attr("fill",H).attr("fill-opacity",F).style("user-select","none"),G&&Q.append("title").text(G)}function U(){const{width:W,height:Z}=i.facet??i;B.selectChildren().each(function(q){let{x:H,width:F,height:X}=this.getBBox();F=Math.round(F),X=Math.round(X);let G=d;if(G===void 0){const Q=k(q)+R,ft=I(q)+C,lt=Q+F+w+b*2<W,j=Q-F-w-b*2>0,gt=ft+X+w+b*2<Z,Sn=ft-X-w-b*2>0;G=lt&&j?gt&&Sn?u.preferredAnchor:Sn?"bottom":"top":gt&&Sn?lt?"left":"right":(lt||j)&&(gt||Sn)?`${Sn?"bottom":"top"}-${lt?"left":"right"}`:u.preferredAnchor}const O=this.firstChild,rt=this.lastChild;if(O.setAttribute("d",_L(G,w,b,F,X)),H)for(const Q of rt.childNodes)Q.setAttribute("x",-H);rt.setAttribute("y",`${+vL(G,rt.childNodes.length,m).toFixed(6)}em`),rt.setAttribute("transform",`translate(${xL(G,w,b,F,X)})`)}),B.attr("visibility",null)}return n.length&&(B.attr("visibility","hidden"),h.isConnected?Promise.resolve().then(U):typeof requestAnimationFrame<"u"&&requestAnimationFrame(U)),B.node()}}function Z6(t,{x:n,y:e,...r}={}){return r.frameAnchor===void 0&&([n,e]=ne(n,e)),new j6(t,{...r,x:n,y:e})}function vL(t,n,e){return/^top(?:-|$)/.test(t)?.94-e:-.29-n*e}function xL(t,n,e,r,i){switch(t){case"middle":return[-r/2,i/2];case"top-left":return[e,n+e];case"top":return[-r/2,n/2+e];case"top-right":return[-r-e,n+e];case"right":return[-n/2-r-e,i/2];case"bottom-left":return[e,-n-e];case"bottom":return[-r/2,-n/2-e];case"bottom-right":return[-r-e,-n-e];case"left":return[e+n/2,i/2]}}function _L(t,n,e,r,i){const o=r+e*2,u=i+e*2;switch(t){case"middle":return`M${-o/2},${-u/2}h${o}v${u}h${-o}z`;case"top-left":return`M0,0l${n},${n}h${o-n}v${u}h${-o}z`;case"top":return`M0,0l${n/2},${n/2}h${(o-n)/2}v${u}h${-o}v${-u}h${(o-n)/2}z`;case"top-right":return`M0,0l${-n},${n}h${n-o}v${u}h${o}z`;case"right":return`M0,0l${-n/2},${-n/2}v${n/2-u/2}h${-o}v${u}h${o}v${n/2-u/2}z`;case"bottom-left":return`M0,0l${n},${-n}h${o-n}v${-u}h${-o}z`;case"bottom":return`M0,0l${n/2},${-n/2}h${(o-n)/2}v${-u}h${-o}v${u}h${(o-n)/2}z`;case"bottom-right":return`M0,0l${-n},${-n}h${n-o}v${-u}h${o}z`;case"left":return`M0,0l${n/2},${-n/2}v${n/2-u/2}h${o}v${u}h${-o}v${n/2-u/2}z`}}function O2(t,n){const e={};let r=this.format;r=F2(r,t,"x"),r=F2(r,t,"y"),this.format=r;for(const i in r){const o=r[i];if(!(o===null||o===!1))if(i==="fx"||i==="fy")e[i]=!0;else{const u=od(t,i);u&&(e[i]=u)}}for(const i in t){if(i in e||i in r||wL.has(i)||(i==="x"||i==="y")&&t.geometry)continue;const o=od(t,i);if(o){if(o.scale==null&&o.defaultScale==="color")continue;e[i]=o}}this.facet&&(n.fx&&!("fx"in r)&&(e.fx=!0),n.fy&&!("fy"in r)&&(e.fy=!0));for(const i in e){const o=this.format[i];if(typeof o=="string"){const u=e[i]?.value??n[i]?.domain()??[];this.format[i]=(bn(u)?bi:pi)(o)}else if(o===void 0||o===!0){const u=n[i];this.format[i]=u?.bandwidth?hp(u,u.domain()):wo}}return e}function F2(t,n,e){if(!(e in t))return t;const r=`${e}1`,i=`${e}2`;if((r in t||!(r in n))&&(i in t||!(i in n)))return t;const o=Object.entries(t),u=t[e];return o.splice(o.findIndex(([a])=>a===e)+1,0,[r,u],[i,u]),Object.fromEntries(o)}function ML(t,n,{title:e}){return this.format.title(e.value[t],t)}function*$L(t,n,e,r,i){for(const o in e){if(o==="fx"||o==="fy"){yield{label:Pf(r,e,o),value:this.format[o](n[o],t)};continue}if(o==="x1"&&"x2"in e||o==="y1"&&"y2"in e)continue;const u=e[o];if(o==="x2"&&"x1"in e)yield{label:q2(r,e,"x"),value:B2(this.format.x2,e.x1,u,t)};else if(o==="y2"&&"y1"in e)yield{label:q2(r,e,"y"),value:B2(this.format.y2,e.y1,u,t)};else{const a=u.value[t],c=u.scale;if(!Kt(a)&&c==null)continue;yield{label:Pf(r,e,o),value:this.format[o](a,t),color:c==="color"?i[o][t]:null,opacity:c==="opacity"?i[o][t]:null}}}}function B2(t,n,e,r){return e.hint?.length?`${t(e.value[r]-n.value[r],r)}`:`${t(n.value[r],r)}–${t(e.value[r],r)}`}function q2(t,n,e){const r=Pf(t,n,`${e}1`,e),i=Pf(t,n,`${e}2`,e);return r===i?r:`${r}–${i}`}function Pf(t,n,e,r=e){const i=n[e],o=t[i?.scale??e];return String(o?.label??i?.label??r)}function K6(t={}){const{facet:n,style:e,title:r,subtitle:i,caption:o,ariaLabel:u,ariaDescription:a}=t,c=np(t.className),f=t.marks===void 0?[]:U2(t.marks);f.push(...CL(f));const s=kL(n,t),h=new Map;for(const z of f){const P=X2(z,s,t);P&&h.set(z,P)}const l=new Map;s&&tu(l,[s],t),tu(l,h,t);const d=U2(RL(f,l,t));for(const z of d){const P=X2(z,s,t);P&&h.set(z,P)}f.unshift(...d);let p=AI(l,t);if(p!==void 0){const z=s?Jl(p,s):void 0;for(const D of f){if(D.facet===null||D.facet==="super")continue;const B=h.get(D);B!==void 0&&(B.facetsIndex=D.fx!=null||D.fy!=null?Jl(p,B):z)}const P=new Set;for(const{facetsIndex:D}of h.values())D?.forEach((B,Y)=>{B?.length>0&&P.add(Y)});p.forEach(0<P.size&&P.size<p.length?(D,B)=>D.empty=!P.has(B):D=>D.empty=!1);for(const D of f)if(D.facet==="exclude"){const B=h.get(D);B!==void 0&&(B.facetsIndex=EI(B.facetsIndex))}}for(const z of Nt.keys())ti(t[z])&&z!=="fx"&&z!=="fy"&&l.set(z,[]);const m=new Map;for(const z of f){if(m.has(z))throw new Error("duplicate mark; each mark must be unique");const{facetsIndex:P,channels:D}=h.get(z)??{},{data:B,facets:Y,channels:U}=z.initialize(P,D,t);wp(U,t),m.set(z,{data:B,facets:Y,channels:U})}const y=cd(tu(l,m,t),t),b=pz(y,f,t);yI(y,b);const w=v2(y),{fx:v,fy:g}=w,x=v||g?S3(y,b):b,_=v||g?FL(w,b):b,M=qs(t),A=M.document,T=So("svg").call(A.documentElement);let E=T;M.ownerSVGElement=T,M.className=c,M.projection=DI(t,x),M.path=function(){return ko(this.projection??UI(w))},M.filterFacets=(z,P)=>Jl(p,{channels:P,groups:Qg(z,P)}),M.getMarkState=z=>{const P=m.get(z),D=h.get(z);return{...P,channels:{...P.channels,...D?.channels}}},M.dispatchValue=z=>{E.value!==z&&(E.value=z,E.dispatchEvent(new M.document.defaultView.Event("input",{bubbles:!0})))};const S=new Set;for(const[z,P]of m)if(z.initializer!=null){const D=z.facet==="super"?_:x,B=z.initializer(P.data,P.facets,P.channels,w,D,M);if(B.data!==void 0&&(P.data=B.data),B.facets!==void 0&&(P.facets=B.facets),B.channels!==void 0){const{fx:Y,fy:U,...W}=B.channels;EL(W),Object.assign(P.channels,W);for(const Z of Object.values(W)){const{scale:q}=Z;q!=null&&!lR(Nt.get(q))&&(Q6(Z,t),S.add(q))}(Y!=null||U!=null)&&h.set(z,!0)}}if(S.size){const z=new Map;tu(z,m,t,Y=>S.has(Y)),tu(l,m,t,Y=>S.has(Y));const P=OL(cd(z,t),y),{scales:D,...B}=v2(P);Object.assign(y,P),Object.assign(w,B),Object.assign(w.scales,D)}let R,C;p!==void 0&&(R={x:v?.domain(),y:g?.domain()},p=SI(p,R),C=TI(v,g,b));for(const[z,P]of m)P.values=z.scale(P.channels,w,M);const{width:$,height:N}=b;_t(T).attr("class",c).attr("fill","currentColor").attr("font-family","system-ui, sans-serif").attr("font-size",10).attr("text-anchor","middle").attr("width",$).attr("height",N).attr("viewBox",`0 0 ${$} ${N}`).attr("aria-label",u).attr("aria-description",a).call(z=>z.append("style").text(`:where(.${c}) {
  --plot-background: white;
  display: block;
  height: auto;
  height: intrinsic;
  max-width: 100%;
}
:where(.${c} text),
:where(.${c} tspan) {
  white-space: pre;
}`)).call(ep,e);for(const z of f){const{channels:P,values:D,facets:B}=m.get(z);if(p===void 0||z.facet==="super"){let Y=null;if(B&&(Y=B[0],Y=z.filter(Y,P,D),Y.length===0))continue;const U=z.render(Y,w,D,_,M);if(U==null)continue;T.appendChild(U)}else{let Y;for(const U of p){if(!(z.facetAnchor?.(p,R,U)??!U.empty))continue;let W=null;if(B){const q=h.has(z);if(W=B[q?U.i:0],W=z.filter(W,P,D),W.length===0)continue;!q&&W===B[0]&&(W=ni(W)),W.fx=U.x,W.fy=U.y,W.fi=U.i}const Z=z.render(W,w,D,x,M);if(Z!=null){(Y??=_t(T).append("g")).append(()=>Z).datum(U);for(const q of["aria-label","aria-description","aria-hidden","transform"])Z.hasAttribute(q)&&(Y.attr(q,Z.getAttribute(q)),Z.removeAttribute(q))}}Y?.selectChildren().each(C)}}const k=tL(y,M,t),{figure:I=r!=null||i!=null||o!=null||k.length>0}=t;I&&(E=A.createElement("figure"),E.className=`${c}-figure`,E.style.maxWidth="initial",r!=null&&E.append(Y2(A,r,"h2")),i!=null&&E.append(Y2(A,i,"h3")),E.append(...k,T),o!=null&&E.append(AL(A,o)),"value"in T&&(E.value=T.value,delete T.value)),E.scale=$I(w.scales),E.legend=Kz(y,M,t);const L=tI();return L>0&&_t(T).append("text").attr("x",$).attr("y",20).attr("dy","-1em").attr("text-anchor","end").attr("font-family","initial").text("⚠️").append("title").text(`${L.toLocaleString("en-US")} warning${L===1?"":"s"}. Please check the console.`),E}function Y2(t,n,e){if(n.ownerDocument)return n;const r=t.createElement(e);return r.append(n),r}function AL(t,n){const e=t.createElement("figcaption");return e.append(n),e}function U2(t){return t.flat(1/0).filter(n=>n!=null).map(SL)}function SL(t){return typeof t.render=="function"?t:new TL(t)}class TL extends xt{constructor(n){if(typeof n!="function")throw new TypeError("invalid mark; missing render function");super(),this.render=n}render(){}}function wp(t,n){for(const e in t)Q6(t[e],n);return t}function Q6(t,n){const{scale:e,transform:r=!0}=t;if(e==null||!r)return;const{type:i,percent:o,interval:u,transform:a=o?c=>c==null?NaN:c*100:H_(u,i)}=n[e]??{};a!=null&&(t.value=bt(t.value,a),t.transform=!1)}function EL(t){for(const n in t)l3(n,t[n])}function tu(t,n,e,r=KC){for(const{channels:i}of n.values())for(const o in i){const u=i[o],{scale:a}=u;if(a!=null&&r(a))if(a==="projection"){if(!BI(e)){const c=e.x?.domain===void 0,f=e.y?.domain===void 0;if(c||f){const[s,h]=YI(u);c&&oh(t,"x",s),f&&oh(t,"y",h)}}}else oh(t,a,u)}return t}function oh(t,n,e){const r=t.get(n);r!==void 0?r.push(e):t.set(n,[e])}function kL(t,n){if(t==null)return;const{x:e,y:r}=t;if(e==null&&r==null)return;const i=mo(t.data);if(i==null)throw new Error("missing facet data");const o={};e!=null&&(o.fx=Vu(i,{value:e,scale:"fx"})),r!=null&&(o.fy=Vu(i,{value:r,scale:"fy"})),wp(o,n);const u=Qg(i,o);return{channels:o,groups:u,data:t.data}}function X2(t,n,e){if(t.facet===null||t.facet==="super")return;const{fx:r,fy:i}=t;if(r!=null||i!=null){const c=mo(t.data??r??i);if(c===void 0)throw new Error(`missing facet data in ${t.ariaLabel}`);if(c===null)return;const f={};return r!=null&&(f.fx=Vu(c,{value:r,scale:"fx"})),i!=null&&(f.fy=Vu(c,{value:i,scale:"fy"})),wp(f,e),{channels:f,groups:Qg(c,f)}}if(n===void 0)return;const{channels:o,groups:u,data:a}=n;if(t.facet!=="auto"||t.data===a)return{channels:o,groups:u};a.length>0&&(u.size>1||u.size===1&&o.fx&&o.fy&&[...u][0][1].size>1)&&Wu(mo(t.data))===Wu(a)&&Xe(`Warning: the ${t.ariaLabel} mark appears to use faceted data, but isn’t faceted. The mark data has the same length as the facet data and the mark facet option is "auto", but the mark data and facet data are distinct. If this mark should be faceted, set the mark facet option to true; otherwise, suppress this warning by setting the mark facet option to false.`)}function NL(t,n={}){return un({...n,x:null,y:null},(e,r,i,o,u,a)=>a.getMarkState(t))}function CL(t){const n=[];for(const e of t){let r=e.tip;if(r){r===!0?r={}:typeof r=="string"&&(r={pointer:r});let{pointer:i,preferredAnchor:o}=r;i=/^x$/i.test(i)?op:/^y$/i.test(i)?Cf:ip,r=i(NL(e,r)),r.title=null,o===void 0&&(r.preferredAnchor=i===Cf?"left":"bottom");const u=Z6(e.data,r);u.facet=e.facet,u.facetAnchor=e.facetAnchor,n.push(u)}}return n}function RL(t,n,e){let{projection:r,x:i={},y:o={},fx:u={},fy:a={},axis:c,grid:f,facet:s={},facet:{axis:h=c,grid:l}=s,x:{axis:d=c,grid:p=d===null?null:f}=i,y:{axis:m=c,grid:y=m===null?null:f}=o,fx:{axis:b=h,grid:w=b===null?null:l}=u,fy:{axis:v=h,grid:g=v===null?null:l}=a}=e;(r||!ti(i)&&!W2("x",t))&&(d=p=null),(r||!ti(o)&&!W2("y",t))&&(m=y=null),n.has("fx")||(b=w=null),n.has("fy")||(v=g=null),d===void 0&&(d=!dc(t,"x")),m===void 0&&(m=!dc(t,"y")),b===void 0&&(b=!dc(t,"fx")),v===void 0&&(v=!dc(t,"fy")),d===!0&&(d="bottom"),m===!0&&(m="left"),b===!0&&(b=d==="top"||d===null?"bottom":"top"),v===!0&&(v=m==="right"||m===null?"left":"right");const x=[];return hc(x,g,T6,a),lc(x,v,_6,"right","left",s,a),hc(x,w,k6,u),lc(x,b,M6,"top","bottom",s,u),hc(x,y,S6,o),lc(x,m,sp,"left","right",e,o),hc(x,p,E6,i),lc(x,d,lp,"bottom","top",e,i),x}function lc(t,n,e,r,i,o,u){if(!n)return;const a=IL(n);u=zL(a?r:n,o,u);const{line:c}=u;(e===sp||e===lp)&&c&&!yo(c)&&t.push(bp(LL(u))),t.push(e(u)),a&&t.push(e({...u,anchor:i,label:null}))}function hc(t,n,e,r){!n||yo(n)||t.push(e(PL(n,r)))}function IL(t){return/^\s*both\s*$/i.test(t)}function zL(t,n,{line:e=n.line,ticks:r,tickSize:i,tickSpacing:o,tickPadding:u,tickFormat:a,tickRotate:c,fontVariant:f,ariaLabel:s,ariaDescription:h,label:l=n.label,labelAnchor:d,labelArrow:p=n.labelArrow,labelOffset:m}){return{anchor:t,line:e,ticks:r,tickSize:i,tickSpacing:o,tickPadding:u,tickFormat:a,tickRotate:c,fontVariant:f,ariaLabel:s,ariaDescription:h,label:l,labelAnchor:d,labelArrow:p,labelOffset:m}}function LL(t){const{anchor:n,line:e}=t;return{anchor:n,facetAnchor:n+"-empty",stroke:e===!0?void 0:e}}function PL(t,{stroke:n=Aa(t)?t:void 0,ticks:e=DL(t)?t:void 0,tickSpacing:r,ariaLabel:i,ariaDescription:o}){return{stroke:n,ticks:e,tickSpacing:r,ariaLabel:i,ariaDescription:o}}function DL(t){switch(typeof t){case"number":return!0;case"string":return!Aa(t)}return tr(t)||typeof t?.range=="function"}function dc(t,n){const e=`${n}-axis `;return t.some(r=>r.ariaLabel?.startsWith(e))}function W2(t,n){for(const e of n)for(const r in e.channels){const{scale:i}=e.channels[r];if(i===t||i==="projection")return!0}return!1}function OL(t,n){for(const e in t){const r=t[e],i=n[e];r.label===void 0&&i&&(r.label=i.label)}return t}function FL({fx:t,fy:n},e){const{marginTop:r,marginRight:i,marginBottom:o,marginLeft:u,width:a,height:c}=Kg(e),f=t&&H2(t),s=n&&H2(n);return{marginTop:n?s[0]:r,marginRight:t?a-f[1]:i,marginBottom:n?c-s[1]:o,marginLeft:t?f[0]:u,inset:{marginTop:e.marginTop,marginRight:e.marginRight,marginBottom:e.marginBottom,marginLeft:e.marginLeft},width:a,height:c}}function H2(t){const n=t.domain();if(n.length===0)return[0,t.bandwidth()];let e=t(n[0]),r=t(n[n.length-1]);return r<e&&([e,r]=[r,e]),[e,r+t.bandwidth()]}const BL=new Map([["basis",t_],["basis-closed",e_],["basis-open",i_],["bundle",u_],["bump-x",ag],["bump-y",cg],["cardinal",a_],["cardinal-closed",c_],["cardinal-open",f_],["catmull-rom",l_],["catmull-rom-closed",d_],["catmull-rom-open",p_],["linear",wi],["linear-closed",y_],["monotone-x",v_],["monotone-y",x_],["natural",M_],["step",$_],["step-after",S_],["step-before",A_]]);function vp(t=wi,n){if(typeof t=="function")return t;const e=BL.get(`${t}`.toLowerCase());if(!e)throw new Error(`unknown curve: ${t}`);if(n!==void 0){if("beta"in e)return e.beta(n);if("tension"in e)return e.tension(n);if("alpha"in e)return e.alpha(n)}return e}function J6(t=_o,n){return typeof t!="function"&&`${t}`.toLowerCase()==="auto"?_o:vp(t,n)}function _o(t){return wi(t)}function ur(t={y:"count"},n={}){[t,n]=$p(t,n);const{x:e,y:r}=n;return Mp(Df(e,n,nt),null,null,r,t,za(n))}function ar(t={x:"count"},n={}){[t,n]=$p(t,n);const{x:e,y:r}=n;return Mp(null,Df(r,n,nt),e,null,t,La(n))}function Xr(t={fill:"count"},n={}){[t,n]=$p(t,n);const{x:e,y:r}=qL(n);return Mp(e,r,null,null,t,za(La(n)))}function t5(t,n,e={}){if(e?.interval==null)return e;const{reduce:r=Gg}=e,i={filter:null};return e[n]!=null&&(i[n]=r),e[`${n}1`]!=null&&(i[`${n}1`]=r),e[`${n}2`]!=null&&(i[`${n}2`]=r),t(i,e)}function xp(t={}){return t5(ur,"y",xi(t,"x"))}function _p(t={}){return t5(ar,"x",xi(t,"y"))}function Mp(t,n,e,r,{data:i=Hg,filter:o=kf,sort:u,reverse:a,...c}={},f={}){t=G2(t),n=G2(n),c=YL(c,f),i=i5(i,nt),u=u==null?void 0:e5("sort",u,f),o=o==null?void 0:r5("filter",o,f),e!=null&&lr(c,"x","x1","x2")&&(e=null),r!=null&&lr(c,"y","y1","y2")&&(r=null);const[s,h]=_n(t),[l,d]=_n(t),[p,m]=_n(n),[y,b]=_n(n),[w,v]=e!=null?[e,"x"]:r!=null?[r,"y"]:[],[g,x]=_n(w),{x:_,y:M,z:A,fill:T,stroke:E,x1:S,x2:R,y1:C,y2:$,domain:N,cumulative:k,thresholds:I,interval:L,...z}=f,[P,D]=_n(A),[B]=yn(T),[Y]=yn(E),[U,W]=_n(B),[Z,q]=_n(Y);return{..."z"in f&&{z:P||A},..."fill"in f&&{fill:U||T},..."stroke"in f&&{stroke:Z||E},...ee(z,(H,F,X)=>{const G=Ef(ct(H,w),X?.[v]),O=ct(H,A),rt=ct(H,B),Q=ct(H,Y),ft=Wg(c,{z:O,fill:rt,stroke:Q}),lt=[],j=[],gt=G&&x([]),Sn=O&&D([]),re=rt&&W([]),s4=Q&&q([]),Yp=t&&h([]),l4=t&&d([]),Up=n&&m([]),h4=n&&b([]),d4=WL(t,n,H);let g4=0;for(const _i of c)_i.initialize(H);u&&u.initialize(H),o&&o.initialize(H);for(const _i of F){const Xp=[];for(const Mi of c)Mi.scope("facet",_i);u&&u.scope("facet",_i),o&&o.scope("facet",_i);for(const[Mi,p4]of ei(_i,ft))for(const[m4,Oa]of ei(p4,G))for(const[ie,Se]of d4(Oa))if(ft&&(Se.z=Mi),!(o&&!o.reduce(ie,Se))){Xp.push(g4++),j.push(i.reduceIndex(ie,H,Se)),G&&gt.push(m4),O&&Sn.push(ft===O?Mi:O[(ie.length>0?ie:Oa)[0]]),rt&&re.push(ft===rt?Mi:rt[(ie.length>0?ie:Oa)[0]]),Q&&s4.push(ft===Q?Mi:Q[(ie.length>0?ie:Oa)[0]]),Yp&&(Yp.push(Se.x1),l4.push(Se.x2)),Up&&(Up.push(Se.y1),h4.push(Se.y2));for(const y4 of c)y4.reduce(ie,Se);u&&u.reduce(ie,Se)}lt.push(Xp)}return c3(lt,u,a),{data:j,facets:lt}}),...!lr(c,"x")&&(s?{x1:s,x2:l,x:Tf(s,l)}:{x:_,x1:S,x2:R}),...!lr(c,"y")&&(p?{y1:p,y2:y,y:Tf(p,y)}:{y:M,y1:C,y2:$}),...g&&{[v]:g},...Object.fromEntries(c.map(({name:H,output:F})=>[H,F]))}}function $p({cumulative:t,domain:n,thresholds:e,interval:r,...i},o){return[i,{cumulative:t,domain:n,thresholds:e,interval:r,...o}]}function Df(t,{cumulative:n,domain:e,thresholds:r,interval:i},o){return t={...Oo(t)},t.domain===void 0&&(t.domain=e),t.cumulative===void 0&&(t.cumulative=n),t.thresholds===void 0&&(t.thresholds=r),t.interval===void 0&&(t.interval=i),t.value===void 0&&(t.value=o),t.thresholds=n5(t.thresholds,t.interval),t}function qL(t){let{x:n,y:e}=t;return n=Df(n,t),e=Df(e,t),[n.value,e.value]=ne(n.value,e.value),{x:n,y:e}}function G2(t){if(t==null)return;const{value:n,cumulative:e,domain:r=Tt,thresholds:i}=t,o=u=>{let a=ct(u,n),c;if(bn(a)||XL(i)){a=bt(a,F_,Float64Array);let[s,h]=typeof r=="function"?r(a):r,l=typeof i=="function"&&!Yi(i)?i(a,s,h):i;typeof l=="number"&&(l=X0(s,h,l)),Yi(l)&&(r===Tt&&(s=l.floor(s),h=l.offset(l.floor(h))),l=l.range(s,l.offset(h))),c=l}else{a=An(a);let[s,h]=typeof r=="function"?r(a):r,l=typeof i=="function"&&!Yi(i)?i(a,s,h):i;if(typeof l=="number")if(r===Tt){let d=pr(s,h,l);if(isFinite(d))if(d>0){let p=Math.round(s/d),m=Math.round(h/d);p*d<=s||--p,m*d>h||++m;let y=m-p+1;l=new Float64Array(y);for(let b=0;b<y;++b)l[b]=(p+b)*d}else if(d<0){d=-d;let p=Math.round(s*d),m=Math.round(h*d);p/d<=s||--p,m/d>h||++m;let y=m-p+1;l=new Float64Array(y);for(let b=0;b<y;++b)l[b]=(p+b)/d}else l=[s];else l=[s]}else l=ge(s,h,l);else Yi(l)&&(r===Tt&&(s=l.floor(s),h=l.offset(l.floor(h))),l=l.range(s,l.offset(h)));c=l}const f=[];if(c.length===1)f.push([c[0],c[0]]);else for(let s=1;s<c.length;++s)f.push([c[s-1],c[s]]);return f.bin=(e<0?GL:e>0?HL:Ap)(f,c,a),f};return o.label=$e(n),o}function n5(t,n,e=V2){if(t===void 0)return n===void 0?e:Do(n);if(typeof t=="string"){switch(t.toLowerCase()){case"freedman-diaconis":return Nb;case"scott":return Nd;case"sturges":return ea;case"auto":return V2}return Es(t)}return t}function YL(t,n){return r3(t,n,e5)}function e5(t,n,e){return Ug(t,n,e,r5)}function r5(t,n,e){return Xg(t,n,e,i5)}function i5(t,n){return Ps(t,n,UL)}function UL(t){switch(`${t}`.toLowerCase()){case"x":return VL;case"x1":return ZL;case"x2":return KL;case"y":return jL;case"y1":return QL;case"y2":return JL;case"z":return f3}throw new Error(`invalid bin reduce: ${t}`)}function V2(t,n,e){return Math.min(200,Nd(t,n,e))}function XL(t){return tR(t)||tr(t)&&bn(t)}function WL(t,n,e){const r=t?.(e),i=n?.(e);return r&&i?function*(o){const u=r.bin(o);for(const[a,[c,f]]of r.entries()){const s=i.bin(u[a]);for(const[h,[l,d]]of i.entries())yield[s[h],{data:e,x1:c,y1:l,x2:f,y2:d}]}}:r?function*(o){const u=r.bin(o);for(const[a,[c,f]]of r.entries())yield[u[a],{data:e,x1:c,x2:f}]}:function*(o){const u=i.bin(o);for(const[a,[c,f]]of i.entries())yield[u[a],{data:e,y1:c,y2:f}]}}function Ap(t,n,e){return n=An(n),r=>{const i=t.map(()=>[]);for(const o of r)i[He(n,e[o])-1]?.push(o);return i}}function HL(t,n,e){const r=Ap(t,n,e);return i=>{const o=r(i);for(let u=1,a=o.length;u<a;++u){const c=o[u-1],f=o[u];for(const s of c)f.push(s)}return o}}function GL(t,n,e){const r=Ap(t,n,e);return i=>{const o=r(i);for(let u=o.length-2;u>=0;--u){const a=o[u+1],c=o[u];for(const f of a)c.push(f)}return o}}function o5(t,n){const e=(+t+ +n)/2;return t instanceof Date?new Date(e):e}const VL={reduceIndex(t,n,{x1:e,x2:r}){return o5(e,r)}},jL={reduceIndex(t,n,{y1:e,y2:r}){return o5(e,r)}},ZL={reduceIndex(t,n,{x1:e}){return e}},KL={reduceIndex(t,n,{x2:e}){return e}},QL={reduceIndex(t,n,{y1:e}){return e}},JL={reduceIndex(t,n,{y2:e}){return e}},tP={ariaLabel:"area",strokeWidth:1,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:1};class Qs extends xt{constructor(n,e={}){const{x1:r,y1:i,x2:o,y2:u,z:a,curve:c,tension:f}=e;super(n,{x1:{value:r,scale:"x"},y1:{value:i,scale:"y"},x2:{value:o,scale:"x",optional:!0},y2:{value:u,scale:"y",optional:!0},z:{value:Lr(e),optional:!0}},e,tP),this.z=a,this.curve=vp(c,f)}filter(n){return n}render(n,e,r,i,o){const{x1:u,y1:a,x2:c=u,y2:f=a}=r;return ht("svg:g",o).call(At,this,i,o).call(vt,this,e,0,0).call(s=>s.selectAll().data(O3(n,[u,a,c,f],this,r)).enter().append("path").call(pt,this).call(Nf,this,r).attr("d",Ma().curve(this.curve).defined(h=>h>=0).x0(h=>u[h]).y0(h=>a[h]).x1(h=>c[h]).y1(h=>f[h]))).node()}}function pd(t,n){return n===void 0?Mo(t,{x:Ns,y:Uu}):new Qs(t,n)}function Ku(t,n){const{y:e=Dt,...r}=_p(n);return new Qs(t,Vs(Hs({...r,y1:e,y2:void 0},e===Dt?"x2":"x")))}function Mo(t,n){const{x:e=Dt,...r}=xp(n);return new Qs(t,js(Gs({...r,x1:e,x2:void 0},e===Dt?"y2":"y")))}const nP={ariaLabel:"link",fill:"none",stroke:"currentColor",strokeMiterlimit:1};class u5 extends xt{constructor(n,e={}){const{x1:r,y1:i,x2:o,y2:u,curve:a,tension:c}=e;super(n,{x1:{value:r,scale:"x"},y1:{value:i,scale:"y"},x2:{value:o,scale:"x",optional:!0},y2:{value:u,scale:"y",optional:!0}},e,nP),this.curve=J6(a,c),Fo(this,e)}project(n,e,r){this.curve!==_o&&super.project(n,e,r)}render(n,e,r,i,o){const{x1:u,y1:a,x2:c=u,y2:f=a}=r,{curve:s}=this;return ht("svg:g",o).call(At,this,i,o).call(vt,this,e).call(h=>h.selectAll().data(n).enter().append("path").call(pt,this).attr("d",s===_o&&o.projection?eP(o.path(),u,a,c,f):l=>{const d=se(),p=s(d);return p.lineStart(),p.point(u[l],a[l]),p.point(c[l],f[l]),p.lineEnd(),d}).call(Ct,this,r).call(Ia,this,r,o)).node()}}function eP(t,n,e,r,i){return n=An(n),e=An(e),r=An(r),i=An(i),o=>t({type:"LineString",coordinates:[[n[o],e[o]],[r[o],i[o]]]})}function a5(t,{x:n,x1:e,x2:r,y:i,y1:o,y2:u,...a}={}){return[e,r]=Of(n,e,r),[o,u]=Of(i,o,u),new u5(t,{...a,x1:e,x2:r,y1:o,y2:u})}function Of(t,n,e){if(t===void 0){if(n===void 0){if(e!==void 0)return[e]}else if(e===void 0)return[n]}else{if(n===void 0)return e===void 0?[t]:[t,e];if(e===void 0)return[t,n]}return[n,e]}const rP={ariaLabel:"arrow",fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeMiterlimit:1,strokeWidth:1.5};class c5 extends xt{constructor(n,e={}){const{x1:r,y1:i,x2:o,y2:u,bend:a=0,headAngle:c=60,headLength:f=8,inset:s=0,insetStart:h=s,insetEnd:l=s,sweep:d}=e;super(n,{x1:{value:r,scale:"x"},y1:{value:i,scale:"y"},x2:{value:o,scale:"x",optional:!0},y2:{value:u,scale:"y",optional:!0}},e,rP),this.bend=a===!0?22.5:Math.max(-90,Math.min(90,a)),this.headAngle=+c,this.headLength=+f,this.insetStart=+h,this.insetEnd=+l,this.sweep=iP(d)}render(n,e,r,i,o){const{x1:u,y1:a,x2:c=u,y2:f=a,SW:s}=r,{strokeWidth:h,bend:l,headAngle:d,headLength:p,insetStart:m,insetEnd:y}=this,b=s?g=>s[g]:jt(h===void 0?1:h),w=d*Rf/2,v=p/1.5;return ht("svg:g",o).call(At,this,i,o).call(vt,this,e).call(g=>g.selectAll().data(n).enter().append("path").call(pt,this).attr("d",x=>{let _=u[x],M=a[x],A=c[x],T=f[x];const E=Math.hypot(A-_,T-M);if(E<=m+y)return null;let S=Math.atan2(T-M,A-_);const R=Math.min(v*b(x),E/3),C=this.sweep(_,M,A,T)*l*Rf,$=Math.hypot(E/Math.tan(C),E)/2;if(m||y)if($<1e5){const U=Math.sign(C),[W,Z]=oP([_,M],[A,T],$,U);if(m&&([_,M]=j2([W,Z,$],[_,M,m],-U*Math.sign(m))),y){const[q,H]=j2([W,Z,$],[A,T,y],U*Math.sign(y));S+=Math.atan2(H-Z,q-W)-Math.atan2(T-Z,A-W),A=q,T=H}}else{const U=A-_,W=T-M,Z=Math.hypot(U,W);m&&(_+=U/Z*m,M+=W/Z*m),y&&(A-=U/Z*y,T-=W/Z*y)}const N=S+C,k=N+w,I=N-w,L=A-R*Math.cos(k),z=T-R*Math.sin(k),P=A-R*Math.cos(I),D=T-R*Math.sin(I),B=$<1e5?`A${$},${$} 0,0,${C>0?1:0} `:"L",Y=R?`M${L},${z}L${A},${T}L${P},${D}`:"";return`M${_},${M}${B}${A},${T}${Y}`}).call(Ct,this,r)).node()}}function iP(t=1){if(typeof t=="number")return jt(Math.sign(t));if(typeof t=="function")return(n,e,r,i)=>Math.sign(t(n,e,r,i));switch(Rn(t,"sweep",["+x","-x","+y","-y"])){case"+x":return(n,e,r)=>yt(n,r);case"-x":return(n,e,r)=>dr(n,r);case"+y":return(n,e,r,i)=>yt(e,i);case"-y":return(n,e,r,i)=>dr(e,i)}}function oP([t,n],[e,r],i,o){const u=e-t,a=r-n,c=Math.hypot(u,a),f=o*Math.sqrt(i*i-c*c/4)/c;return[(t+e)/2-a*f,(n+r)/2+u*f]}function j2([t,n,e],[r,i,o],u){const a=r-t,c=i-n,f=Math.hypot(a,c),s=(a*a+c*c-o*o+e*e)/(2*f),h=u*Math.sqrt(e*e-s*s);return[t+(a*s+c*h)/f,n+(c*s-a*h)/f]}function uP(t,{x:n,x1:e,x2:r,y:i,y1:o,y2:u,...a}={}){return[e,r]=Of(n,e,r),[o,u]=Of(i,o,u),new c5(t,{...a,x1:e,x2:r,y1:o,y2:u})}const aP={ariaLabel:"bar"};class Sp extends xt{constructor(n,e,r={},i=aP){super(n,e,r,i),gp(this,r),pp(this,r)}render(n,e,r,i,o){const{rx:u,ry:a,rx1y1:c,rx1y2:f,rx2y1:s,rx2y2:h}=this,l=this._x(e,r,i),d=this._y(e,r,i),p=this._width(e,r,i),m=this._height(e,r,i);return ht("svg:g",o).call(At,this,i,o).call(this._transform,this,e).call(y=>y.selectAll().data(n).enter().call(c||f||s||h?b=>b.append("path").call(pt,this).call(mp,l,d,Z2(l,p),Z2(d,m),this).call(Ct,this,r):b=>b.append("rect").call(pt,this).attr("x",l).attr("width",p).attr("y",d).attr("height",m).call(et,"rx",u).call(et,"ry",a).call(Ct,this,r))).node()}_x(n,{x:e},{marginLeft:r}){const{insetLeft:i}=this;return e?o=>e[o]+i:r+i}_y(n,{y:e},{marginTop:r}){const{insetTop:i}=this;return e?o=>e[o]+i:r+i}_width({x:n},{x:e},{marginRight:r,marginLeft:i,width:o}){const{insetLeft:u,insetRight:a}=this,c=e&&n?n.bandwidth():o-r-i;return Math.max(0,c-u-a)}_height({y:n},{y:e},{marginTop:r,marginBottom:i,height:o}){const{insetTop:u,insetBottom:a}=this,c=e&&n?n.bandwidth():o-r-i;return Math.max(0,c-u-a)}}function Z2(t,n){return typeof t=="function"&&typeof n=="function"?e=>t(e)+n(e):typeof t=="function"?e=>t(e)+n:typeof n=="function"?e=>t+n(e):t+n}class Tp extends Sp{constructor(n,e={},r){const{x1:i,x2:o,y:u}=e;super(n,{x1:{value:i,scale:"x"},x2:{value:o,scale:"x"},y:{value:u,scale:"y",type:"band",optional:!0}},e,r)}_transform(n,e,{x:r}){n.call(vt,e,{x:r},0,0)}_x({x:n},{x1:e,x2:r},{marginLeft:i}){const{insetLeft:o}=this;return ve(n)?i+o:u=>Math.min(e[u],r[u])+o}_width({x:n},{x1:e,x2:r},{marginRight:i,marginLeft:o,width:u}){const{insetLeft:a,insetRight:c}=this;return ve(n)?u-i-o-a-c:f=>Math.max(0,Math.abs(r[f]-e[f])-a-c)}}class Ep extends Sp{constructor(n,e={},r){const{x:i,y1:o,y2:u}=e;super(n,{y1:{value:o,scale:"y"},y2:{value:u,scale:"y"},x:{value:i,scale:"x",type:"band",optional:!0}},e,r)}_transform(n,e,{y:r}){n.call(vt,e,{y:r},0,0)}_y({y:n},{y1:e,y2:r},{marginTop:i}){const{insetTop:o}=this;return ve(n)?i+o:u=>Math.min(e[u],r[u])+o}_height({y:n},{y1:e,y2:r},{marginTop:i,marginBottom:o,height:u}){const{insetTop:a,insetBottom:c}=this;return ve(n)?u-i-o-a-c:f=>Math.max(0,Math.abs(r[f]-e[f])-a-c)}}function Gr(t,n={}){return Po(n)||(n={...n,y:Dt,x2:nt}),new Tp(t,Vs(up(Hs(n))))}function Vr(t,n={}){return Po(n)||(n={...n,x:Dt,y2:nt}),new Ep(t,js(ap(Gs(n))))}const cP={ariaLabel:"cell"};class Js extends Sp{constructor(n,{x:e,y:r,...i}={}){super(n,{x:{value:e,scale:"x",type:"band",optional:!0},y:{value:r,scale:"y",type:"band",optional:!0}},i,cP)}_transform(n,e){n.call(vt,e,{},0,0)}}function Xi(t,{x:n,y:e,...r}={}){return[n,e]=ne(n,e),new Js(t,{...r,x:n,y:e})}function fP(t,{x:n=Dt,fill:e,stroke:r,...i}={}){return e===void 0&&yn(r)[0]===void 0&&(e=nt),new Js(t,{...i,x:n,fill:e,stroke:r})}function sP(t,{y:n=Dt,fill:e,stroke:r,...i}={}){return e===void 0&&yn(r)[0]===void 0&&(e=nt),new Js(t,{...i,y:n,fill:e,stroke:r})}const lP={ariaLabel:"dot",fill:"none",stroke:"currentColor",strokeWidth:1.5};function kp(t){return t.sort===void 0&&t.reverse===void 0?n3({channel:"-r"},t):t}class tl extends xt{constructor(n,e={}){const{x:r,y:i,r:o,rotate:u,symbol:a=si,frameAnchor:c}=e,[f,s]=Lt(u,0),[h,l]=pR(a),[d,p]=Lt(o,h==null?3:4.5);super(n,{x:{value:r,scale:"x",optional:!0},y:{value:i,scale:"y",optional:!0},r:{value:d,scale:"r",filter:Ue,optional:!0},rotate:{value:f,optional:!0},symbol:{value:h,scale:"auto",optional:!0}},kp(e),lP),this.r=p,this.rotate=s,this.symbol=l,this.frameAnchor=Sa(c);const{channels:m}=this,{symbol:y}=m;if(y){const{fill:b,stroke:w}=m;y.hint={fill:b?b.value===y.value?"color":"currentColor":this.fill??"currentColor",stroke:w?w.value===y.value?"color":"currentColor":this.stroke??"none"}}}render(n,e,r,i,o){const{x:u,y:a}=e,{x:c,y:f,r:s,rotate:h,symbol:l}=r,{r:d,rotate:p,symbol:m}=this,[y,b]=Ae(this,i),w=m===si,v=s?void 0:d*d*Math.PI;return Ss(d)&&(n=[]),ht("svg:g",o).call(At,this,i,o).call(vt,this,{x:c&&u,y:f&&a}).call(g=>g.selectAll().data(n).enter().append(w?"circle":"path").call(pt,this).call(w?x=>{x.attr("cx",c?_=>c[_]:y).attr("cy",f?_=>f[_]:b).attr("r",s?_=>s[_]:d)}:x=>{x.attr("transform",Pa`translate(${c?_=>c[_]:y},${f?_=>f[_]:b})${h?_=>` rotate(${h[_]})`:p?` rotate(${p})`:""}`).attr("d",s&&l?_=>{const M=se();return l[_].draw(M,s[_]*s[_]*Math.PI),M}:s?_=>{const M=se();return m.draw(M,s[_]*s[_]*Math.PI),M}:l?_=>{const M=se();return l[_].draw(M,v),M}:(()=>{const _=se();return m.draw(_,v),_})())}).call(Ct,this,r)).node()}}function Or(t,{x:n,y:e,...r}={}){return r.frameAnchor===void 0&&([n,e]=ne(n,e)),new tl(t,{...r,x:n,y:e})}function hP(t,{x:n=nt,...e}={}){return new tl(t,K3({...e,x:n}))}function dP(t,{y:n=nt,...e}={}){return new tl(t,Z3({...e,y:n}))}function gP(t,n){return Or(t,{...n,symbol:"circle"})}function pP(t,n){return Or(t,{...n,symbol:"hexagon"})}const mP={ariaLabel:"line",fill:"none",stroke:"currentColor",strokeWidth:1.5,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:1};class nl extends xt{constructor(n,e={}){const{x:r,y:i,z:o,curve:u,tension:a}=e;super(n,{x:{value:r,scale:"x"},y:{value:i,scale:"y"},z:{value:Lr(e),optional:!0}},e,mP),this.z=o,this.curve=J6(u,a),Fo(this,e)}filter(n){return n}project(n,e,r){this.curve!==_o&&super.project(n,e,r)}render(n,e,r,i,o){const{x:u,y:a}=r,{curve:c}=this;return ht("svg:g",o).call(At,this,i,o).call(vt,this,e).call(f=>f.selectAll().data(O3(n,[u,a],this,r)).enter().append("path").call(pt,this).call(Nf,this,r).call(xz,this,r,o).attr("d",c===_o&&o.projection?yP(o.path(),u,a):xs().curve(c).defined(s=>s>=0).x(s=>u[s]).y(s=>a[s]))).node()}}function yP(t,n,e){return n=An(n),e=An(e),r=>{let i=[];const o=[i];for(const u of r)u===-1?(i=[],o.push(i)):i.push([n[u],e[u]]);return t({type:"MultiLineString",coordinates:o})}}function el(t,{x:n,y:e,...r}={}){return[n,e]=ne(n,e),new nl(t,{...r,x:n,y:e})}function Qu(t,{x:n=nt,y:e=Dt,...r}={}){return new nl(t,_p({...r,x:n,y:e}))}function Ju(t,{x:n=Dt,y:e=nt,...r}={}){return new nl(t,xp({...r,x:n,y:e}))}function f5(t,n){n=wP(n);const{x:e,y:r,color:i,size:o}=n,u=gc(t,e),a=gc(t,r),c=gc(t,i),f=gc(t,o);let{fx:s,fy:h,x:{value:l,reduce:d,zero:p,...m},y:{value:y,reduce:b,zero:w,...v},color:{value:g,color:x,reduce:_},size:{value:M,reduce:A},mark:T}=n;if(d===void 0&&(d=b==null&&l==null&&M==null&&y!=null?"count":null),b===void 0&&(b=d==null&&y==null&&M==null&&l!=null?"count":null),A===void 0&&M==null&&_==null&&d==null&&b==null&&(l==null||Rt(u))&&(y==null||Rt(a))&&(A="count"),p===void 0&&(p=pc(d)?!0:void 0),w===void 0&&(w=pc(b)?!0:void 0),l==null&&y==null)throw new Error("must specify x or y");if(d!=null&&y==null)throw new Error("reducing x requires y");if(b!=null&&l==null)throw new Error("reducing y requires x");T===void 0&&(T=M!=null||A!=null?"dot":pc(d)||pc(b)||_!=null?"bar":l!=null&&y!=null?Rt(u)||Rt(a)||d==null&&b==null&&!nu(u)&&!nu(a)?"dot":"line":l!=null||y!=null?"rule":null);let E,S,R;switch(T){case"dot":R=Or,S="stroke";break;case"line":R=u&&a||d!=null||b!=null?w||b!=null||u&&nu(u)?Ju:p||d!=null||a&&nu(a)?Qu:el:u?Qu:Ju,S="stroke",Q2(c)&&(E=null);break;case"area":R=!(w||b!=null)&&(p||d!=null||a&&nu(a))?Ku:Mo,S="fill",Q2(c)&&(E=null);break;case"rule":R=u?Sr:Tr,S="stroke";break;case"bar":R=d!=null?Rt(a)?K2(d)&&u&&Rt(u)?Xi:Gr:zf:b!=null?Rt(u)?K2(b)&&a&&Rt(a)?Xi:Vr:Lf:_!=null||A!=null?u&&Rt(u)&&a&&Rt(a)?Xi:u&&Rt(u)?Vr:a&&Rt(a)?Gr:yp:u&&Ui(u)&&!(a&&Ui(a))?Gr:a&&Ui(a)&&!(u&&Ui(u))?Vr:Xi,S="fill";break;default:throw new Error(`invalid mark: ${T}`)}let C={fx:s,fy:h,x:u??void 0,y:a??void 0,[S]:c??x,z:E,r:f??void 0,tip:!0},$,N={[S]:_??void 0,r:A??void 0};if(d!=null&&b!=null)throw new Error("cannot reduce both x and y");return b!=null?(N.y=b,$=Rt(u)?Hu:ur):d!=null?(N.x=d,$=Rt(a)?Gu:ar):(_!=null||A!=null)&&(u&&a?$=Rt(u)&&Rt(a)?Yg:Rt(u)?ar:Rt(a)?ur:Xr:u?$=Rt(u)?Hu:ur:a&&($=Rt(a)?Gu:ar)),($===Xr||$===ur)&&(C.x={value:u,...m}),($===Xr||$===ar)&&(C.y={value:a,...v}),p===void 0&&(p=u&&!($===Xr||$===ur)&&(R===Gr||R===Ku||R===zf||R===Tr)),w===void 0&&(w=a&&!($===Xr||$===ar)&&(R===Vr||R===Mo||R===Lf||R===Sr)),{fx:s??null,fy:h??null,x:{value:l??null,reduce:d??null,zero:!!p,...m},y:{value:y??null,reduce:b??null,zero:!!w,...v},color:{value:g??null,reduce:_??null,...x!==void 0&&{color:x}},size:{value:M??null,reduce:A??null},mark:T,markImpl:J2[R],markOptions:C,transformImpl:J2[$],transformOptions:N,colorMode:S}}function bP(t,n){const e=f5(t,n),{fx:r,fy:i,x:{zero:o},y:{zero:u},markOptions:a,transformOptions:c,colorMode:f}=e,s=md[e.markImpl],h=md[e.transformImpl],l=r!=null||i!=null?bp({strokeOpacity:.1}):null,d=[o?Sr([0]):null,u?Tr([0]):null],p=s(t,h?h(c,a):a);return f==="stroke"?Wn(l,d,p):Wn(l,p,d)}function nu(t){let n,e;for(const r of t){if(r==null)continue;if(n===void 0){n=r;continue}const i=Math.sign(yt(n,r));if(i){if(e!==void 0&&i!==e)return!1;n=r,e=i}}return!0}function wP({x:t,y:n,color:e,size:r,fx:i,fy:o,mark:u}={}){return ue(t)||(t=Pi(t)),ue(n)||(n=Pi(n)),ue(e)||(e=Aa(e)?{color:e}:Pi(e)),ue(r)||(r=Pi(r)),ue(i)&&({value:i}=Pi(i)),ue(o)&&({value:o}=Pi(o)),u!=null&&(u=`${u}`.toLowerCase()),{x:t,y:n,color:e,size:r,fx:i,fy:o,mark:u}}function gc(t,n){const e=ct(t,n.value);return e&&(e.label=$e(n.value)),e}function Pi(t){return vP(t)?{reduce:t}:{value:t}}function pc(t){return/^(?:distinct|count|sum|proportion)$/i.test(t)}function K2(t){return/^(?:first|last|mode)$/i.test(t)}function vP(t){if(t==null)return!1;if(typeof t.reduceIndex=="function"||typeof t.reduce=="function"&&In(t)||/^p\d{2}$/i.test(t))return!0;switch(`${t}`.toLowerCase()){case"first":case"last":case"count":case"distinct":case"sum":case"proportion":case"proportion-facet":case"deviation":case"min":case"min-index":case"max":case"max-index":case"mean":case"median":case"variance":case"mode":return!0}return!1}function Q2(t){return t?new Bn(t).size>t.length>>1:!1}const md={dot:Or,line:el,lineX:Qu,lineY:Ju,areaX:Ku,areaY:Mo,ruleX:Sr,ruleY:Tr,barX:Gr,barY:Vr,rect:yp,rectX:zf,rectY:Lf,cell:Xi,bin:Xr,binX:ur,binY:ar,group:Yg,groupX:Hu,groupY:Gu},J2=Object.fromEntries(Object.entries(md).map(([t,n])=>[n,t]));function Np(t,n={}){let{x:e,x1:r,x2:i}=n;e===void 0&&r===void 0&&i===void 0&&(n={...n,x:e=nt});const o={};return e!=null&&(o.x=t),r!=null&&(o.x1=t),i!=null&&(o.x2=t),xe(o,n)}function Cp(t,n={}){let{y:e,y1:r,y2:i}=n;e===void 0&&r===void 0&&i===void 0&&(n={...n,y:e=nt});const o={};return e!=null&&(o.y=t),r!=null&&(o.y1=t),i!=null&&(o.y2=t),xe(o,n)}function xe(t={},n={}){const e=Lr(n),r=Object.entries(t).map(([i,o])=>{const u=W_(i,n);if(u==null)throw new Error(`missing channel: ${i}`);const[a,c]=Cn(u);return{key:i,input:u,output:a,setOutput:c,map:xP(o)}});return{...ee(n,(i,o)=>{const u=ct(i,e),a=r.map(({input:f})=>ct(i,f)),c=r.map(({setOutput:f})=>f(new Array(i.length)));for(const f of o)for(const s of u?je(f,h=>u[h]).values():[f])r.forEach(({map:h},l)=>h.mapIndex(s,a[l],c[l]));return{data:i,facets:o}}),...Object.fromEntries(r.map(({key:i,output:o})=>[i,o]))}}function xP(t){if(t==null)throw new Error("missing map");if(typeof t.mapIndex=="function")return t;if(typeof t.map=="function"&&In(t))return _P(t);if(typeof t=="function")return uh(Ig(t));switch(`${t}`.toLowerCase()){case"cumsum":return $P;case"rank":return uh((n,e)=>Id(n,r=>e[r]));case"quantile":return uh((n,e)=>MP(n,r=>e[r]))}throw new Error(`invalid map: ${t}`)}function _P(t){return console.warn("deprecated map interface; implement mapIndex instead."),{mapIndex:t.map.bind(t)}}function MP(t,n){const e=na(t,n)-1;return Id(t,n).map(r=>r/e)}function uh(t){return{mapIndex(n,e,r){const i=t(n,e);if(i.length!==n.length)throw new Error("map function returned a mismatched length");for(let o=0,u=n.length;o<u;++o)r[n[o]]=i[o]}}}const $P={mapIndex(t,n,e){let r=0;for(const i of t)e[i]=r+=n[i]}};function AP(t={},n){return arguments.length===1&&(n=t),Np(rl(t),n)}function SP(t={},n){return arguments.length===1&&(n=t),Cp(rl(t),n)}function rl(t={}){typeof t=="number"&&(t={k:t});let{k:n,reduce:e,shift:r,anchor:i,strict:o}=t;if(i===void 0&&r!==void 0&&(i=EP(r),Xe(`Warning: the shift option is deprecated; please use anchor "${i}" instead.`)),!((n=Math.floor(n))>0))throw new Error(`invalid k: ${n}`);return kP(e)(n,TP(i,n),o)}function TP(t="middle",n){switch(`${t}`.toLowerCase()){case"middle":return n-1>>1;case"start":return 0;case"end":return n-1}throw new Error(`invalid anchor: ${t}`)}function EP(t){switch(`${t}`.toLowerCase()){case"centered":return"middle";case"leading":return"start";case"trailing":return"end"}throw new Error(`invalid shift: ${t}`)}function kP(t="mean"){if(typeof t=="string"){if(/^p\d{2}$/i.test(t))return mc(Cg(t));switch(t.toLowerCase()){case"deviation":return mc($o);case"max":return yc((n,e)=>qt(n,r=>e[r]));case"mean":return NP;case"median":return mc(to);case"min":return yc((n,e)=>on(n,r=>e[r]));case"mode":return yc((n,e)=>Rd(n,r=>e[r]));case"sum":return s5;case"variance":return mc(qf);case"difference":return IP;case"ratio":return zP;case"first":return LP;case"last":return PP}}if(typeof t!="function")throw new Error(`invalid reduce: ${t}`);return yc(Ig(t))}function mc(t){return(n,e,r)=>r?{mapIndex(i,o,u){const a=f=>o[f]==null?NaN:+o[f];let c=0;for(let f=0;f<n-1;++f)isNaN(a(f))&&++c;for(let f=0,s=i.length-n+1;f<s;++f)isNaN(a(f+n-1))&&++c,u[i[f+e]]=c===0?t(ni(i,f,f+n),a):NaN,isNaN(a(f))&&--c}}:{mapIndex(i,o,u){const a=c=>o[c]==null?NaN:+o[c];for(let c=-e;c<0;++c)u[i[c+e]]=t(ni(i,0,c+n),a);for(let c=0,f=i.length-e;c<f;++c)u[i[c+e]]=t(ni(i,c,c+n),a)}}}function yc(t){return(n,e,r)=>r?{mapIndex(i,o,u){let a=0;for(let c=0;c<n-1;++c)a+=Kt(o[i[c]]);for(let c=0,f=i.length-n+1;c<f;++c)a+=Kt(o[i[c+n-1]]),a===n&&(u[i[c+e]]=t(ni(i,c,c+n),o)),a-=Kt(o[i[c]])}}:{mapIndex(i,o,u){for(let a=-e;a<0;++a)u[i[a+e]]=t(ni(i,0,a+n),o);for(let a=0,c=i.length-e;a<c;++a)u[i[a+e]]=t(ni(i,a,a+n),o)}}}function s5(t,n,e){return e?{mapIndex(r,i,o){let u=0,a=0;for(let c=0;c<t-1;++c){const f=i[r[c]];f===null||isNaN(f)?++u:a+=+f}for(let c=0,f=r.length-t+1;c<f;++c){const s=i[r[c]],h=i[r[c+t-1]];h===null||isNaN(h)?++u:a+=+h,o[r[c+n]]=u===0?a:NaN,s===null||isNaN(s)?--u:a-=+s}}}:{mapIndex(r,i,o){let u=0;const a=r.length;for(let c=0,f=Math.min(a,t-n-1);c<f;++c)u+=+i[r[c]]||0;for(let c=-n,f=a-n;c<f;++c)u+=+i[r[c+t-1]]||0,o[r[c+n]]=u,u-=+i[r[c]]||0}}}function NP(t,n,e){if(e){const r=s5(t,n,e);return{mapIndex(i,o,u){r.mapIndex(i,o,u);for(let a=0,c=i.length-t+1;a<c;++a)u[i[a+n]]/=t}}}else return{mapIndex(r,i,o){let u=0,a=0;const c=r.length;for(let f=0,s=Math.min(c,t-n-1);f<s;++f){let h=i[r[f]];h!==null&&!isNaN(h=+h)&&(u+=h,++a)}for(let f=-n,s=c-n;f<s;++f){let h=i[r[f+t-1]],l=i[r[f]];h!==null&&!isNaN(h=+h)&&(u+=h,++a),o[r[f+n]]=u/a,l!==null&&!isNaN(l=+l)&&(u-=l,--a)}}}}function CP(t,n,e,r){for(let i=e+r;e<i;++e){const o=t[n[e]];if(Kt(o))return o}}function RP(t,n,e,r){for(let i=e+r-1;i>=e;--i){const o=t[n[i]];if(Kt(o))return o}}function l5(t,n,e,r){for(let i=e+r;e<i;++e){let o=t[n[e]];if(o!==null&&!isNaN(o=+o))return o}}function h5(t,n,e,r){for(let i=e+r-1;i>=e;--i){let o=t[n[i]];if(o!==null&&!isNaN(o=+o))return o}}function IP(t,n,e){return e?{mapIndex(r,i,o){for(let u=0,a=r.length-t;u<a;++u){const c=i[r[u]],f=i[r[u+t-1]];o[r[u+n]]=c===null||f===null?NaN:f-c}}}:{mapIndex(r,i,o){for(let u=-n,a=r.length-t+n+1;u<a;++u)o[r[u+n]]=h5(i,r,u,t)-l5(i,r,u,t)}}}function zP(t,n,e){return e?{mapIndex(r,i,o){for(let u=0,a=r.length-t;u<a;++u){const c=i[r[u]],f=i[r[u+t-1]];o[r[u+n]]=c===null||f===null?NaN:f/c}}}:{mapIndex(r,i,o){for(let u=-n,a=r.length-t+n+1;u<a;++u)o[r[u+n]]=h5(i,r,u,t)/l5(i,r,u,t)}}}function LP(t,n,e){return e?{mapIndex(r,i,o){for(let u=0,a=r.length-t;u<a;++u)o[r[u+n]]=i[r[u]]}}:{mapIndex(r,i,o){for(let u=-n,a=r.length-t+n+1;u<a;++u)o[r[u+n]]=CP(i,r,u,t)}}}function PP(t,n,e){return e?{mapIndex(r,i,o){for(let u=0,a=r.length-t;u<a;++u)o[r[u+n]]=i[r[u+t-1]]}}:{mapIndex(r,i,o){for(let u=-n,a=r.length-t+n+1;u<a;++u)o[r[u+n]]=RP(i,r,u,t)}}}const We={n:20,k:2,color:"currentColor",opacity:.2,strict:!0,anchor:"end"};function DP(t,{x:n=nt,y:e,k:r=We.k,color:i=We.color,opacity:o=We.opacity,fill:u=i,fillOpacity:a=o,stroke:c=i,strokeOpacity:f,strokeWidth:s,...h}={}){return Wn(St(u)?null:Ku(t,xe({x1:ri({k:-r,...h}),x2:ri({k:r,...h})},{x1:n,x2:n,y:e,fill:u,fillOpacity:a,...h})),St(c)?null:Qu(t,xe({x:ri(h)},{x:n,y:e,stroke:c,strokeOpacity:f,strokeWidth:s,...h})))}function OP(t,{x:n,y:e=nt,k:r=We.k,color:i=We.color,opacity:o=We.opacity,fill:u=i,fillOpacity:a=o,stroke:c=i,strokeOpacity:f,strokeWidth:s,...h}={}){return Wn(St(u)?null:Mo(t,xe({y1:ri({k:-r,...h}),y2:ri({k:r,...h})},{x:n,y1:e,y2:e,fill:u,fillOpacity:a,...h})),St(c)?null:Ju(t,xe({y:ri(h)},{x:n,y:e,stroke:c,strokeOpacity:f,strokeWidth:s,...h})))}function ri({n:t=We.n,k:n=0,strict:e=We.strict,anchor:r=We.anchor}={}){return rl({k:t,reduce:i=>ia(i)+n*($o(i)||0),strict:e,anchor:r})}const FP={ariaLabel:"tick",fill:null,stroke:"currentColor"};class d5 extends xt{constructor(n,e,r){super(n,e,r,FP),Fo(this,r)}render(n,e,r,i,o){return ht("svg:g",o).call(At,this,i,o).call(this._transform,this,e).call(u=>u.selectAll().data(n).enter().append("line").call(pt,this).attr("x1",this._x1(e,r,i)).attr("x2",this._x2(e,r,i)).attr("y1",this._y1(e,r,i)).attr("y2",this._y2(e,r,i)).call(Ct,this,r).call(Ia,this,r,o)).node()}}class g5 extends d5{constructor(n,e={}){const{x:r,y:i,inset:o=0,insetTop:u=o,insetBottom:a=o}=e;super(n,{x:{value:r,scale:"x"},y:{value:i,scale:"y",type:"band",optional:!0}},e),this.insetTop=$t(u),this.insetBottom=$t(a)}_transform(n,e,{x:r}){n.call(vt,e,{x:r},Zt,0)}_x1(n,{x:e}){return r=>e[r]}_x2(n,{x:e}){return r=>e[r]}_y1({y:n},{y:e},{marginTop:r}){const{insetTop:i}=this;return e&&n?o=>e[o]+i:r+i}_y2({y:n},{y:e},{height:r,marginBottom:i}){const{insetBottom:o}=this;return e&&n?u=>e[u]+n.bandwidth()-o:r-i-o}}class p5 extends d5{constructor(n,e={}){const{x:r,y:i,inset:o=0,insetRight:u=o,insetLeft:a=o}=e;super(n,{y:{value:i,scale:"y"},x:{value:r,scale:"x",type:"band",optional:!0}},e),this.insetRight=$t(u),this.insetLeft=$t(a)}_transform(n,e,{y:r}){n.call(vt,e,{y:r},0,Zt)}_x1({x:n},{x:e},{marginLeft:r}){const{insetLeft:i}=this;return e&&n?o=>e[o]+i:r+i}_x2({x:n},{x:e},{width:r,marginRight:i}){const{insetRight:o}=this;return e&&n?u=>e[u]+n.bandwidth()-o:r-i-o}_y1(n,{y:e}){return r=>e[r]}_y2(n,{y:e}){return r=>e[r]}}function m5(t,{x:n=nt,...e}={}){return new g5(t,{...e,x:n})}function y5(t,{y:n=nt,...e}={}){return new p5(t,{...e,y:n})}function BP(t,{x:n=nt,y:e=null,r,fill:i="#ccc",fillOpacity:o,stroke:u="currentColor",strokeOpacity:a,strokeWidth:c=2,sort:f,...s}={}){const h=e!=null?Gu:qg;return Wn(Tr(t,h({x1:Rp,x2:Ip},{x:n,y:e,stroke:u,strokeOpacity:a,...s})),Gr(t,h({x1:"p25",x2:"p75"},{x:n,y:e,fill:i,fillOpacity:o,...s})),m5(t,h({x:"p50"},{x:n,y:e,stroke:u,strokeOpacity:a,strokeWidth:c,sort:f,...s})),Or(t,xe({x:b5},{x:n,y:e,z:e,r,stroke:u,strokeOpacity:a,...s})))}function qP(t,{y:n=nt,x:e=null,r,fill:i="#ccc",fillOpacity:o,stroke:u="currentColor",strokeOpacity:a,strokeWidth:c=2,sort:f,...s}={}){const h=e!=null?Hu:qg;return Wn(Sr(t,h({y1:Rp,y2:Ip},{x:e,y:n,stroke:u,strokeOpacity:a,...s})),Vr(t,h({y1:"p25",y2:"p75"},{x:e,y:n,fill:i,fillOpacity:o,...s})),y5(t,h({y:"p50"},{x:e,y:n,stroke:u,strokeOpacity:a,strokeWidth:c,sort:f,...s})),Or(t,xe({y:b5},{x:e,y:n,z:e,r,stroke:u,strokeOpacity:a,...s})))}function b5(t){const n=Rp(t),e=Ip(t);return t.map(r=>r<n||r>e?r:NaN)}function Rp(t){const n=w5(t)*2.5-v5(t)*1.5;return on(t,e=>e>=n?e:NaN)}function Ip(t){const n=v5(t)*2.5-w5(t)*1.5;return qt(t,e=>e<=n?e:NaN)}function w5(t){return pe(t,.25)}function v5(t){return pe(t,.75)}const YP={ariaLabel:"raster",stroke:null,pixelSize:1};function Di(t,n){const e=+t;if(isNaN(e))throw new Error(`invalid ${n}: ${t}`);return e}function tb(t,n){const e=Math.floor(t);if(isNaN(e))throw new Error(`invalid ${n}: ${t}`);return e}class x5 extends xt{constructor(n,e,r={},i){let{width:o,height:u,x:a,y:c,x1:f=a==null?0:void 0,y1:s=c==null?0:void 0,x2:h=a==null?o:void 0,y2:l=c==null?u:void 0,pixelSize:d=i.pixelSize,blur:p=0,interpolate:m}=r;if(o!=null&&(o=tb(o,"width")),u!=null&&(u=tb(u,"height")),f!=null&&(f=Di(f,"x1")),s!=null&&(s=Di(s,"y1")),h!=null&&(h=Di(h,"x2")),l!=null&&(l=Di(l,"y2")),a==null&&(f==null||h==null))throw new Error("missing x");if(c==null&&(s==null||l==null))throw new Error("missing y");n!=null&&o!=null&&u!=null&&(a===void 0&&f!=null&&h!=null&&(a=KP(f,h,o)),c===void 0&&s!=null&&l!=null&&(c=QP(s,l,o,u))),super(n,{x:{value:a,scale:"x",optional:!0},y:{value:c,scale:"y",optional:!0},x1:{value:f==null?null:[f],scale:"x",optional:!0,filter:null},y1:{value:s==null?null:[s],scale:"y",optional:!0,filter:null},x2:{value:h==null?null:[h],scale:"x",optional:!0,filter:null},y2:{value:l==null?null:[l],scale:"y",optional:!0,filter:null},...e},r,i),this.width=o,this.height=u,this.pixelSize=Di(d,"pixelSize"),this.blur=Di(p,"blur"),this.interpolate=a==null||c==null?null:XP(m)}}class _5 extends x5{constructor(n,e={}){const{imageRendering:r}=e;if(n==null){const{fill:i,fillOpacity:o}=e;Lt(o)[0]!==void 0&&(e=yd("fillOpacity",e)),yn(i)[0]!==void 0&&(e=yd("fill",e))}super(n,void 0,e,YP),this.imageRendering=Pt(r,"auto")}scale(n,{color:e,...r},i){return super.scale(n,r,i)}render(n,e,r,i,o){const u=e[r.channels.fill?.scale]??(N=>N),{x:a,y:c}=r,{document:f}=o,[s,h,l,d]=$5(r,i,o),p=l-s,m=d-h,{pixelSize:y,width:b=Math.round(Math.abs(p)/y),height:w=Math.round(Math.abs(m)/y)}=this,v=b*w;let{fill:g,fillOpacity:x}=r,_=0;if(this.interpolate){const N=b/p,k=w/m,I=bt(a,z=>(z-s)*N,Float64Array),L=bt(c,z=>(z-h)*k,Float64Array);g&&(g=this.interpolate(n,b,w,I,L,g)),x&&(x=this.interpolate(n,b,w,I,L,x))}else this.data==null&&n&&(_=n.fi*v);const M=f.createElement("canvas");M.width=b,M.height=w;const A=M.getContext("2d"),T=A.createImageData(b,w),E=T.data;let{r:S,g:R,b:C}=Qn(this.fill)??{r:0,g:0,b:0},$=(this.fillOpacity??1)*255;for(let N=0;N<v;++N){const k=N<<2;if(g){const I=u(g[N+_]);if(I==null){E[k+3]=0;continue}({r:S,g:R,b:C}=Qn(I))}x&&($=x[N+_]*255),E[k+0]=S,E[k+1]=R,E[k+2]=C,E[k+3]=$}return this.blur>0&&yb(T,this.blur),A.putImageData(T,0,0),ht("svg:g",o).call(At,this,i,o).call(vt,this,e).call(N=>N.append("image").attr("transform",`translate(${s},${h}) scale(${Math.sign(l-s)},${Math.sign(d-h)})`).attr("width",Math.abs(p)).attr("height",Math.abs(m)).attr("preserveAspectRatio","none").call(et,"image-rendering",this.imageRendering).call(pt,this).attr("xlink:href",M.toDataURL())).node()}}function M5(t,n,e){arguments.length<3&&(e=n,n=null);let{x:r,y:i,[t]:o,...u}=e;return r===void 0&&i===void 0&&eR(n)&&(r=Ns,i=Uu,o===void 0&&(o=QC)),[n,{...u,x:r,y:i,[t]:o}]}function UP(){const[t,n]=M5("fill",...arguments);return new _5(t,t==null||n.fill!==void 0||n.fillOpacity!==void 0?n:{...n,fill:nt})}function $5({x1:t,y1:n,x2:e,y2:r},i,{projection:o}){const{width:u,height:a,marginTop:c,marginRight:f,marginBottom:s,marginLeft:h}=i;return[t&&o==null?t[0]:h,n&&o==null?n[0]:c,e&&o==null?e[0]:u-f,r&&o==null?r[0]:a-s]}function A5({x1:t,y1:n,x2:e,y2:r},i,o,u){const a={};return t&&(a.x1=t),n&&(a.y1=n),e&&(a.x2=e),r&&(a.y2=r),$5(Ds(a,i),o,u)}function yd(t,n={}){const{[t]:e}=n;if(typeof e!="function")throw new Error(`invalid ${t}: not a function`);return un({...n,[t]:void 0},function(r,i,o,u,a,c){const{x:f,y:s}=u;if(!f)throw new Error("missing scale: x");if(!s)throw new Error("missing scale: y");const[h,l,d,p]=A5(o,u,a,c),m=d-h,y=p-l,{pixelSize:b}=this,{width:w=Math.round(Math.abs(m)/b),height:v=Math.round(Math.abs(y)/b)}=n,g=new Array(w*v*(i?i.length:1)),x=m/w,_=y/v;let M=0;for(const A of i??[void 0])for(let T=.5;T<v;++T)for(let E=.5;E<w;++E,++M)g[M]=e(f.invert(h+E*x),s.invert(l+T*_),A);return{data:g,facets:i,channels:{[t]:{value:g,scale:"auto"}}}})}function XP(t){if(typeof t=="function")return t;if(t==null)return bd;switch(`${t}`.toLowerCase()){case"none":return bd;case"nearest":return T5;case"barycentric":return S5();case"random-walk":return E5()}throw new Error(`invalid interpolate: ${t}`)}function bd(t,n,e,r,i,o){const u=new Array(n*e);for(const a of t)r[a]<0||r[a]>=n||i[a]<0||i[a]>=e||(u[Math.floor(i[a])*n+Math.floor(r[a])]=o[a]);return u}function S5({random:t=ss(42)}={}){return(n,e,r,i,o,u)=>{const{points:a,triangles:c,hull:f}=Ze.from(n,d=>i[d],d=>o[d]),s=new u.constructor(e*r).fill(NaN),h=new Uint8Array(e*r),l=ZP(u,t);for(let d=0;d<c.length;d+=3){const p=c[d],m=c[d+1],y=c[d+2],b=a[2*p],w=a[2*m],v=a[2*y],g=a[2*p+1],x=a[2*m+1],_=a[2*y+1],M=Math.min(b,w,v),A=Math.max(b,w,v),T=Math.min(g,x,_),E=Math.max(g,x,_),S=(x-_)*(b-v)+(g-_)*(v-w);if(!S)continue;const R=u[n[p]],C=u[n[m]],$=u[n[y]];for(let N=Math.floor(M);N<A;++N)for(let k=Math.floor(T);k<E;++k){if(N<0||N>=e||k<0||k>=r)continue;const I=N+.5,L=k+.5,z=Math.sign(S),P=(x-_)*(I-v)+(L-_)*(v-w);if(P*z<0)continue;const D=(_-g)*(I-v)+(L-_)*(b-v);if(D*z<0)continue;const B=S-(P+D);if(B*z<0)continue;const Y=N+e*k;s[Y]=l(R,P/S,C,D/S,$,B/S,N,k),h[Y]=1}}return WP(s,h,i,o,u,e,r,f,n,l),s}}function WP(t,n,e,r,i,o,u,a,c,f){e=Float64Array.from(a,d=>e[c[d]]),r=Float64Array.from(a,d=>r[c[d]]),i=Array.from(a,d=>i[c[d]]);const s=e.length,h=Array.from({length:s},(d,p)=>GP(p,e,r));let l=0;for(let d=0;d<u;++d){const p=d+.5;for(let m=0;m<o;++m){const y=m+o*d;if(!n[y]){const b=m+.5;for(let w=0;w<s;++w){const v=(s+l+(w%2?(w+1)/2:-w/2))%s;if(h[v](b,p)){const g=HP(e.at(v-1),r.at(v-1),e[v],r[v],b,p);t[y]=f(i.at(v-1),g,i[v],1-g,i[v],0,m,d),l=v;break}}}}}}function HP(t,n,e,r,i,o){const u=e-t,a=r-n,c=u*(e-i)+a*(r-o),f=u*(i-t)+a*(o-n);return c>0&&f>0?c/(c+f):+(c>f)}function eu(t,n,e,r){return t*r-e*n}function GP(t,n,e){const r=n.length,i=n.at(t-2),o=e.at(t-2),u=n.at(t-1),a=e.at(t-1),c=n[t],f=e[t],s=n.at(t+1-r),h=e.at(t+1-r),l=u-c,d=a-f,p=i-u,m=o-a,y=c-s,b=f-h,w=Math.hypot(l,d),v=Math.hypot(p,m),g=Math.hypot(y,b);return(x,_)=>{const M=x-u,A=_-a,T=x-c,E=_-f;return eu(M,A,T,E)>-1e-6&&eu(M,A,l,d)*v-eu(M,A,p,m)*w>-1e-6&&eu(T,E,y,b)*w-eu(T,E,l,d)*g<=0}}function T5(t,n,e,r,i,o){const u=new o.constructor(n*e),a=Ze.from(t,s=>r[s],s=>i[s]);let c,f;for(let s=.5,h=0;s<e;++s){f=c;for(let l=.5;l<n;++l,++h)f=a.find(l,s,f),l===.5&&(c=f),u[h]=o[t[f]]}return u}function E5({random:t=ss(42),minDistance:n=.5,maxSteps:e=2}={}){return(r,i,o,u,a,c)=>{const f=new c.constructor(i*o),s=Ze.from(r,p=>u[p],p=>a[p]);let h,l,d;for(let p=.5,m=0;p<o;++p){l=h;for(let y=.5;y<i;++y,++m){let b=y,w=p;d=l=s.find(b,w,l),y===.5&&(h=l);let v,g=0;for(;(v=Math.hypot(u[r[d]]-b,a[r[d]]-w))>n&&g<e;){const x=t(y,p,g)*2*Math.PI;b+=Math.cos(x)*v,w+=Math.sin(x)*v,d=s.find(b,w,d),++g}f[m]=c[r[d]]}}return f}}function VP(t,n,e,r,i,o){return n*t+r*e+o*i}function jP(t){return(n,e,r,i,o,u,a,c)=>{const f=t(a,c);return f<e?n:f<e+i?r:o}}function ZP(t,n){return Ui(t)||bn(t)?VP:jP(n)}function KP(t,n,e){return{transform(r){const i=r.length,o=new Float64Array(i),u=(n-t)/e,a=t+u/2;for(let c=0;c<i;++c)o[c]=c%e*u+a;return o}}}function QP(t,n,e,r){return{transform(i){const o=i.length,u=new Float64Array(o),a=(n-t)/r,c=t+a/2;for(let f=0;f<o;++f)u[f]=Math.floor(f/e)%r*a+c;return u}}}const nb={ariaLabel:"contour",fill:"none",stroke:"currentColor",strokeMiterlimit:1,pixelSize:2};class k5 extends x5{constructor(n,{smooth:e=!0,value:r,...i}={}){const o=D3({},i,nb);if(r===void 0){for(const a in o)if(o[a].value!=null){if(r!==void 0)throw new Error("ambiguous contour value");r=i[a],i[a]="value"}}if(r!=null){const a={transform:c=>c.map(f=>f.value),label:$e(r)};for(const c in o)i[c]==="value"&&(i[c]=a)}if(n==null){if(r==null)throw new Error("missing contour value");i=yd("value",{value:r,...i}),r=null}else{let{interpolate:a}=i;r===void 0&&(r=nt),a===void 0&&(i.interpolate="nearest")}super(n,{value:{value:r,optional:!0}},JP(i),nb);const u={geometry:{value:nt}};for(const a in this.channels){const c=this.channels[a],{scale:f}=c;f==="x"||f==="y"||a==="value"||(u[a]=c,delete this.channels[a])}this.contourChannels=u,this.smooth=!!e}filter(n,{x:e,y:r,value:i,...o},u){return super.filter(n,o,u)}render(n,e,r,i,o){const{geometry:u}=r,a=ko();return ht("svg:g",o).call(At,this,i,o).call(vt,this,e).call(c=>{c.selectAll().data(n).enter().append("path").call(pt,this).attr("d",f=>a(u[f])).call(Ct,this,r)}).node()}}function JP({thresholds:t,interval:n,...e}){return t=n5(t,n,ea),un(e,function(r,i,o,u,a,c){const[f,s,h,l]=A5(o,u,a,c),d=h-f,p=l-s,{pixelSize:m,width:y=Math.round(Math.abs(d)/m),height:b=Math.round(Math.abs(p)/m)}=this,w=y/d,v=b/p,g=o.value.value,x=[];if(this.interpolate){const{x:E,y:S}=Ra(o,u,c),R=bt(E,k=>(k-f)*w,Float64Array),C=bt(S,k=>(k-s)*v,Float64Array),$=[o.x,o.y,o.value],N=[R,C,g];for(const k of i){const I=this.filter(k,$,N);x.push(this.interpolate(I,y,b,R,C,g))}}else if(i){const E=y*b,S=i.length;for(let R=0;R<S;++R)x.push(g.slice(R*E,R*E+E))}else x.push(g);if(this.blur>0)for(const E of x)Sd({data:E,width:y,height:b},this.blur);const _=tD(t,g,...eD(x));if(_===null)throw new Error(`unsupported thresholds: ${t}`);const{contour:M}=Uc().size([y,b]).smooth(this.smooth),A=[],T=[];for(const E of x)T.push(kn(A.length,A.push(...bt(_,S=>M(E,S)))));for(const{coordinates:E}of A)for(const S of E)for(const R of S)for(const C of R)C[0]=C[0]/w+f,C[1]=C[1]/v+s;return{data:A,facets:T,channels:s3(this.contourChannels,A)}})}function tD(t,n,e,r){if(typeof t?.range=="function")return t.range(t.floor(e),r);if(typeof t=="function"&&(t=t(n,e,r)),typeof t!="number")return zr(t);const i=ge(...Uf(e,r,t),t);for(;i[i.length-1]>=r;)i.pop();for(;i[1]<e;)i.shift();return i}function nD(){return new k5(...M5("value",...arguments))}function eD(t){return[on(t,n=>on(n,eb)),qt(t,n=>qt(n,eb))]}function eb(t){return isFinite(t)?t:NaN}function rD(t,n){return zp(ip,t,n)}function iD(t,n={}){return zp(op,t,n)}function oD(t,n={}){return zp(Cf,t,n)}function zp(t,n,e={}){const{x:r,y:i,maxRadius:o}=e,u=t({px:r,py:i,maxRadius:o}),a=[];r!=null&&a.push(Sr(n,rb("x",{...u,inset:-6},e))),i!=null&&a.push(Tr(n,rb("y",{...u,inset:-6},e))),r!=null&&a.push(vo(n,ib("x",{...u,dy:9,frameAnchor:"bottom",lineAnchor:"top"},e))),i!=null&&a.push(vo(n,ib("y",{...u,dx:-9,frameAnchor:"left",textAnchor:"end"},e)));for(const c of a)c.ariaLabel=`crosshair ${c.ariaLabel}`;return Wn(...a)}function N5(t,{channels:n,...e},{facet:r,facetAnchor:i,fx:o,fy:u,[t]:a,channels:c,transform:f,initializer:s}){return{...e,facet:r,facetAnchor:i,fx:o,fy:u,[t]:a,channels:{...n,...c},transform:f,initializer:uD(t,s)}}function uD(t,n){return n==null?n:function(e,r,{x:i,y:o,px:u,py:a,...c},...f){const{channels:{x:s,y:h,...l}={},...d}=n.call(this,e,r,{...c,x:u,y:a},...f);return{channels:{...l,...s&&{px:s,...t==="x"&&{x:s}},...h&&{py:h,...t==="y"&&{y:h}}},...d}}}function rb(t,n,e){const{color:r="currentColor",opacity:i=.2,ruleStroke:o=r,ruleStrokeOpacity:u=i,ruleStrokeWidth:a}=e;return{...N5(t,n,e),stroke:o,strokeOpacity:u,strokeWidth:a}}function ib(t,n,e){const{color:r="currentColor",textFill:i=r,textFillOpacity:o,textStroke:u="var(--plot-background)",textStrokeOpacity:a,textStrokeWidth:c=5}=e;return{...N5(t,n,aD(t,e)),fill:i,fillOpacity:o,stroke:u,strokeOpacity:a,strokeWidth:c}}function aD(t,n){return un(n,(e,r,i)=>({channels:{text:{value:od(i,t)?.value}}}))}const cD={ariaLabel:"delaunay link",fill:"none",stroke:"currentColor",strokeMiterlimit:1},fD={ariaLabel:"delaunay mesh",fill:null,stroke:"currentColor",strokeOpacity:.2},sD={ariaLabel:"hull",fill:"none",stroke:"currentColor",strokeWidth:1.5,strokeMiterlimit:1},lD={ariaLabel:"voronoi",fill:"none",stroke:"currentColor",strokeMiterlimit:1},hD={ariaLabel:"voronoi mesh",fill:null,stroke:"currentColor",strokeOpacity:.2};class dD extends xt{constructor(n,e={}){const{x:r,y:i,z:o,curve:u,tension:a}=e;super(n,{x:{value:r,scale:"x",optional:!0},y:{value:i,scale:"y",optional:!0},z:{value:o,optional:!0}},e,cD),this.curve=vp(u,a),Fo(this,e)}render(n,e,r,i,o){const{x:u,y:a}=e,{x:c,y:f,z:s}=r,{curve:h}=this,[l,d]=Ae(this,i),p=c?w=>c[w]:jt(l),m=f?w=>f[w]:jt(d),y=this;function b(w){let v=-1;const g=[],x={};for(const $ in r)x[$]=[];const _=[],M=[],A=[],T=[];function E($,N){$=w[$],N=w[N],g.push(++v),_[v]=p($),A[v]=m($),M[v]=p(N),T[v]=m(N);for(const k in r)x[k].push(r[k][N])}const{halfedges:S,hull:R,triangles:C}=Ze.from(w,p,m);for(let $=0;$<S.length;++$){const N=S[$];N>$&&E(C[$],C[N])}for(let $=0;$<R.length;++$)E(R[$],R[($+1)%R.length]);_t(this).selectAll().data(g).enter().append("path").call(pt,y).attr("d",$=>{const N=se(),k=h(N);return k.lineStart(),k.point(_[$],A[$]),k.point(M[$],T[$]),k.lineEnd(),N}).call(Ct,y,x).call(Ia,y,x,o)}return ht("svg:g",o).call(At,this,i,o).call(vt,this,{x:c&&u,y:f&&a}).call(s?w=>w.selectAll().data(je(n,v=>s[v]).values()).enter().append("g").each(b):w=>w.datum(n).each(b)).node()}}class Lp extends xt{constructor(n,e={},r,i=({z:o})=>o){const{x:o,y:u}=e;super(n,{x:{value:o,scale:"x",optional:!0},y:{value:u,scale:"y",optional:!0},z:{value:i(e),optional:!0}},e,r)}render(n,e,r,i,o){const{x:u,y:a}=e,{x:c,y:f,z:s}=r,[h,l]=Ae(this,i),d=c?b=>c[b]:jt(h),p=f?b=>f[b]:jt(l),m=this;function y(b){const w=Ze.from(b,d,p);_t(this).append("path").datum(b[0]).call(pt,m).attr("d",m._render(w,i)).call(Ct,m,r)}return ht("svg:g",o).call(At,this,i,o).call(vt,this,{x:c&&u,y:f&&a}).call(s?b=>b.selectAll().data(je(n,w=>s[w]).values()).enter().append("g").each(y):b=>b.datum(n).each(y)).node()}}class gD extends Lp{constructor(n,e={}){super(n,e,fD),this.fill="none"}_render(n){return n.render()}}class pD extends Lp{constructor(n,e={}){super(n,e,sD,Lr)}_render(n){return n.renderHull()}}class mD extends xt{constructor(n,e={}){const{x:r,y:i,z:o}=e;super(n,{x:{value:r,scale:"x",optional:!0},y:{value:i,scale:"y",optional:!0},z:{value:o,optional:!0}},un(e,function(u,a,c,f,s,h){let{x:l,y:d,z:p}=c;({x:l,y:d}=Ra(c,f,h)),p=p?.value;const m=new Array((l??d).length).fill(null),[y,b]=Ae(this,s),w=l?g=>l[g]:jt(y),v=d?g=>d[g]:jt(b);for(let g of a){l&&(g=g.filter(x=>Kt(w(x)))),d&&(g=g.filter(x=>Kt(v(x))));for(const[,x]of ei(g,p)){const _=Ze.from(x,w,v),M=C5(_,s);for(let A=0,T=x.length;A<T;++A)m[x[A]]=M.renderCell(A)}}return{data:u,facets:a,channels:{cells:{value:m}}}}),lD)}render(n,e,r,i,o){const{x:u,y:a}=e,{x:c,y:f,cells:s}=r;return ht("svg:g",o).call(At,this,i,o).call(vt,this,{x:c&&u,y:f&&a}).call(h=>{h.selectAll().data(n).enter().append("path").call(pt,this).attr("d",l=>s[l]).call(Ct,this,r)}).node()}}class yD extends Lp{constructor(n,e){super(n,e,hD),this.fill="none"}_render(n,e){return C5(n,e).render()}}function C5(t,n){const{width:e,height:r,marginTop:i,marginRight:o,marginBottom:u,marginLeft:a}=n;return t.voronoi([a,i,e-o,r-u])}function Da(t,n,{x:e,y:r,...i}={}){return[e,r]=ne(e,r),new t(n,{...i,x:e,y:r})}function bD(t,n){return Da(dD,t,n)}function wD(t,n){return Da(gD,t,n)}function vD(t,n){return Da(pD,t,n)}function xD(t,{x:n,y:e,initializer:r,...i}={}){return Da(mD,t,{...ee({...i,x:n,y:e},q6),initializer:r})}function _D(t,n){return Da(yD,t,n)}const MD={ariaLabel:"density",fill:"none",stroke:"currentColor",strokeMiterlimit:1};class R5 extends xt{constructor(n,{x:e,y:r,z:i,weight:o,fill:u,stroke:a,...c}={}){const f=ob(u)&&(u="currentColor",!0),s=ob(a)&&(a="currentColor",!0);super(n,{x:{value:e,scale:"x",optional:!0},y:{value:r,scale:"y",optional:!0},z:{value:Lr({z:i,fill:u,stroke:a}),optional:!0},weight:{value:o,optional:!0}},SD({...c,fill:u,stroke:a},f,s),MD),f&&(this.fill=void 0),s&&(this.stroke=void 0),this.z=i}filter(n){return n}render(n,e,r,i,o){const{contours:u}=r,a=ko();return ht("svg:g",o).call(At,this,i,o).call(vt,this,{}).call(c=>c.selectAll().data(n).enter().append("path").call(pt,this).call(Ct,this,r).attr("d",f=>a(u[f]))).node()}}function $D(t,{x:n,y:e,...r}={}){return[n,e]=ne(n,e),new R5(t,{...r,x:n,y:e})}const AD=new Set(["x","y","z","weight"]);function SD(t,n,e){let{bandwidth:i,thresholds:o}=t;return i=i===void 0?20:+i,o=o===void 0?20:typeof o?.[Symbol.iterator]=="function"?An(o):+o,un(t,function(u,a,c,f,s,h){const l=c.weight?An(c.weight.value):null,d=c.z?.value,{z:p}=this,[m,y]=Ae(this,s),{width:b,height:w}=s,{x:v,y:g}=Ra(c,f,h),x=Object.fromEntries(Object.entries(c).filter(([C])=>!AD.has(C)).map(([C,$])=>[C,{...$,value:[]}])),_=n&&[],M=e&&[],A=qw().x(v?C=>v[C]:m).y(g?C=>g[C]:y).weight(l?C=>l[C]:1).size([b,w]).bandwidth(i),T=[];for(const C of a){const $=[];T.push($);for(const N of d?tp(C,d,p):[C]){const k=A.contours(N);$.push([N,k])}}let E=o;if(!(E instanceof ks)){let C=0;for(const $ of T)for(const[,N]of $){const k=N.max;k>C&&(C=k)}E=Float64Array.from({length:o-1},($,N)=>C*100*(N+1)/o)}const S=[],R=[];for(const C of T){const $=[];S.push($);for(const[N,k]of C)for(const I of E){$.push(R.length),R.push(k(I/100)),_&&_.push(I),M&&M.push(I);for(const L in x)x[L].value.push(c[L].value[N[0]])}}return _&&_.push(0),M&&M.push(0),{data:u,facets:S,channels:{...x,..._&&{fill:{value:_,scale:"color"}},...M&&{stroke:{value:M,scale:"color"}},contours:{value:R}}}})}function ob(t){return/^density$/i.test(t)}function TD(t,n){return I5("x",t,n)}function ED(t,n){return I5("y",t,n)}function I5(t,n,{x1:e,x2:r,y1:i,y2:o,x:u=e===void 0&&r===void 0?t==="y"?Dt:nt:void 0,y:a=i===void 0&&o===void 0?t==="x"?Dt:nt:void 0,fill:c,positiveFill:f="#3ca951",negativeFill:s="#4269d0",fillOpacity:h=1,positiveFillOpacity:l=h,negativeFillOpacity:d=h,stroke:p,strokeOpacity:m,z:y=yn(p)[0],clip:b,tip:w,render:v,...g}={}){return[e,r]=ub(u,e,r),[i,o]=ub(a,i,o),e===r&&i===o&&(t==="y"?i=Le(0):e=Le(0)),{tip:w}=xi({tip:w},t==="y"?"x":"y"),Wn(St(f)?null:Object.assign(pd(n,{x1:e,x2:r,y1:i,y2:o,z:y,fill:f,fillOpacity:l,render:Zu(v,ab(t,!0)),clip:b,...g}),{ariaLabel:"positive difference"}),St(s)?null:Object.assign(pd(n,{x1:e,x2:r,y1:i,y2:o,z:y,fill:s,fillOpacity:d,render:Zu(v,ab(t,!1)),clip:b,...g}),{ariaLabel:"negative difference"}),el(n,{x:r,y:o,z:y,stroke:p,strokeOpacity:m,tip:w,clip:!0,...g}))}function ub(t,n,e){return n===void 0&&e===void 0?n=e=Le(t):n===void 0?(e=Le(e),n=t===void 0?e:Le(t)):e===void 0?(n=Le(n),e=t===void 0?n:Le(t)):(n=Le(n),e=Le(e)),[n,e]}function Le(t){let n;const{value:e,label:r=$e(e)}=Oo(t);return{transform:i=>n||(n=ct(i,e)),label:r}}function ab(t,n){const e=t==="x"?"y":"x",r=`${e}1`,i=`${e}2`,o=`${t}1`,u=`${t}2`;return(a,c,f,s,h,l)=>{const{[r]:d,[i]:p}=f,m=new Float32Array(d.length),y=new Float32Array(p.length),b=s[t==="y"?"height":"width"];(n===A3(c[t])<0?m:y).fill(b);const w=l(a,c,{...f,[i]:d,[u]:y},s,h),v=l(a,c,{...f,[r]:p,[o]:m},s,h),g=w.querySelector("g")??w,x=v.querySelector("g")??v;for(let _=0;g.firstChild;_+=2){const M=Jg(),A=ht("svg:clipPath",h).attr("id",M).node();A.appendChild(g.firstChild),x.childNodes[_].setAttribute("clip-path",`url(#${M})`),x.insertBefore(A,x.childNodes[_])}return v}}function z5({geometry:t=nt,...n}={}){const e=bo(r=>ct(r,t));return un({...n,x:null,y:null,geometry:{transform:e}},(r,i,o,u,a,c)=>{const f=e(r),s=f.length,h=new Float64Array(s),l=new Float64Array(s),{centroid:d}=c.path();for(let p=0;p<s;++p)[h[p],l[p]]=d(f[p]);return{data:r,facets:i,channels:{x:{value:h,scale:null,source:null},y:{value:l,scale:null,source:null}}}})}function kD({geometry:t=nt,...n}={}){const e=bo(i=>ct(i,t)),r=bo(i=>ct(e(i),hv));return{...n,x:{transform:i=>Float64Array.from(r(i),([o])=>o)},y:{transform:i=>Float64Array.from(r(i),([,o])=>o)},geometry:{transform:e}}}const ND={ariaLabel:"geo",fill:"none",stroke:"currentColor",strokeWidth:1,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:1};class L5 extends xt{constructor(n,e={}){const[r,i]=Lt(e.r,3);super(n,{x:{value:e.tip?e.x:null,scale:"x",optional:!0},y:{value:e.tip?e.y:null,scale:"y",optional:!0},r:{value:r,scale:"r",filter:Ue,optional:!0},geometry:{value:e.geometry,scale:"projection"}},kp(e),ND),this.r=i}render(n,e,r,i,o){const{geometry:u,r:a}=r,c=o.path(),{r:f}=this;return Ss(f)?n=[]:f!==void 0&&c.pointRadius(f),ht("svg:g",o).call(At,this,i,o).call(vt,this,e).call(s=>{s.selectAll().data(n).enter().append("path").call(pt,this).attr("d",a?h=>c.pointRadius(a[h])(u[h]):h=>c(u[h])).call(Ct,this,r)}).node()}}function Pp(t,n={}){return n.tip&&n.x===void 0&&n.y===void 0?n=z5(n):n.geometry===void 0&&(n={...n,geometry:nt}),new L5(t,n)}function CD({strokeWidth:t=1.5,...n}={}){return Pp({type:"Sphere"},{strokeWidth:t,...n})}function RD({strokeOpacity:t=.1,...n}={}){return Pp(Mv(),{strokeOpacity:t,...n})}const _u=.5,Mu=0;function ID(t={fill:"count"},{binWidth:n,...e}={}){const{z:r}=e;return n=n===void 0?20:$t(n),t=i3(t,e),lr(t,"fill")&&(e.channels={...e.channels,fill:{value:[]}}),e.symbol===void 0&&(e.symbol="hexagon"),e.r===void 0&&!lr(t,"r")&&(e.r=n/2),un(e,(i,o,u,a,c,f)=>{let{x:s,y:h,z:l,fill:d,stroke:p,symbol:m}=u;if(s===void 0)throw new Error("missing channel: x");if(h===void 0)throw new Error("missing channel: y");({x:s,y:h}=Ra(u,a,f)),l=l?l.value:ct(i,r),d=d?.value,p=p?.value,m=m?.value;const y=Wg(t,{z:l,fill:d,stroke:p,symbol:m}),b=l&&[],w=d&&[],v=p&&[],g=m&&[],x=[],_=[],M=[];let A=-1;for(const R of t)R.initialize(i);for(const R of o){const C=[];for(const $ of t)$.scope("facet",R);for(const[$,N]of ei(R,y))for(const{index:k,extent:I}of zD(i,N,s,h,n)){C.push(++A),_.push(I.x),M.push(I.y),l&&b.push(y===l?$:l[k[0]]),d&&w.push(y===d?$:d[k[0]]),p&&v.push(y===p?$:p[k[0]]),m&&g.push(y===m?$:m[k[0]]);for(const L of t)L.reduce(k,I)}x.push(C)}const T=u.x.scale,E=u.y.scale,S={x:{value:_,source:a[T]?{value:bt(_,a[T].invert),scale:T}:null},y:{value:M,source:a[E]?{value:bt(M,a[E].invert),scale:E}:null},...l&&{z:{value:b}},...d&&{fill:{value:w,scale:"auto"}},...p&&{stroke:{value:v,scale:"auto"}},...m&&{symbol:{value:g,scale:"auto"}},...Object.fromEntries(t.map(({name:R,output:C})=>[R,{scale:"auto",label:C.label,radius:R==="r"?n/2:void 0,value:C.transform()}]))};return{data:i,facets:x,channels:S}})}function zD(t,n,e,r,i){const o=i*(1.5/J_),u=new Map;for(const a of n){let c=e[a],f=r[a];if(isNaN(c)||isNaN(f))continue;let s=Math.round(f=(f-Mu)/o),h=Math.round(c=(c-_u)/i-(s&1)/2),l=f-s;if(Math.abs(l)*3>1){let m=c-h,y=h+(c<h?-1:1)/2,b=s+(f<s?-1:1),w=c-y,v=f-b;m*m+l*l>w*w+v*v&&(h=y+(s&1?1:-1)/2,s=b)}const d=`${h},${s}`;let p=u.get(d);p===void 0&&(p={index:[],extent:{data:t,x:(h+(s&1)/2)*i+_u,y:s*o+Mu}},u.set(d,p)),p.index.push(a)}return u.values()}const LD={ariaLabel:"hexgrid",fill:"none",stroke:"currentColor",strokeOpacity:.1};function PD(t){return new P5(t)}class P5 extends xt{constructor({binWidth:n=20,clip:e=!0,...r}={}){super(xu,void 0,{clip:e,...r},LD),this.binWidth=$t(n)}render(n,e,r,i,o){const{binWidth:u}=this,{marginTop:a,marginRight:c,marginBottom:f,marginLeft:s,width:h,height:l}=i,d=s-_u,p=h-c-_u,m=a-Mu,y=l-f-Mu,b=u/2,w=b*t3,v=w/2,g=b*2,x=w*1.5,_=Math.floor(d/g),M=Math.ceil(p/g),A=Math.floor((m+v)/x),T=Math.ceil((y-v)/x)+1,E=`m0,${er(-w)}l${er(b)},${er(v)}v${er(w)}l${er(-b)},${er(v)}`;let S=E;for(let R=A;R<T;++R)for(let C=_;C<M;++C)S+=`M${er(C*g+(R&1)*b)},${er(R*x)}${E}`;return ht("svg:g",o).datum(0).call(At,this,i,o).call(vt,this,{},Zt+_u,Zt+Mu).call(R=>R.append("path").call(pt,this).call(Ct,this,r).attr("d",S)).node()}}function er(t){return Math.round(t*1e3)/1e3}const DD={ariaLabel:"image",fill:null,stroke:null};function OD(t){return/^\.*\//.test(t)}function FD(t){return/^(blob|data|file|http|https):/i.test(t)}function BD(t){return typeof t=="string"&&(OD(t)||FD(t))?[void 0,t]:[t,void 0]}let D5=class extends xt{constructor(n,e={}){let{x:r,y:i,r:o,width:u,height:a,rotate:c,src:f,preserveAspectRatio:s,crossOrigin:h,frameAnchor:l,imageRendering:d}=e;o==null&&(o=void 0),o===void 0&&u===void 0&&a===void 0?u=a=16:u===void 0&&a!==void 0?u=a:a===void 0&&u!==void 0&&(a=u);const[p,m]=BD(f),[y,b]=Lt(o),[w,v]=Lt(u,b!==void 0?b*2:void 0),[g,x]=Lt(a,b!==void 0?b*2:void 0),[_,M]=Lt(c,0);super(n,{x:{value:r,scale:"x",optional:!0},y:{value:i,scale:"y",optional:!0},r:{value:y,scale:"r",filter:Ue,optional:!0},width:{value:w,filter:Ue,optional:!0},height:{value:g,filter:Ue,optional:!0},rotate:{value:_,optional:!0},src:{value:p,optional:!0}},kp(e),DD),this.src=m,this.width=v,this.rotate=M,this.height=x,this.r=b,this.preserveAspectRatio=Pt(s,"xMidYMid"),this.crossOrigin=Vt(h),this.frameAnchor=Sa(l),this.imageRendering=Pt(d,"auto")}render(n,e,r,i,o){const{x:u,y:a}=e,{x:c,y:f,width:s,height:h,r:l,rotate:d,src:p}=r,{r:m,width:y,height:b,rotate:w}=this,[v,g]=Ae(this,i);return ht("svg:g",o).call(At,this,i,o).call(vt,this,{x:c&&u,y:f&&a}).call(x=>x.selectAll().data(n).enter().append("image").call(pt,this).attr("x",cb(c,s,l,v,y,m)).attr("y",cb(f,h,l,g,b,m)).attr("width",s?_=>s[_]:y!==void 0?y:l?_=>l[_]*2:m*2).attr("height",h?_=>h[_]:b!==void 0?b:l?_=>l[_]*2:m*2).attr("transform",d?_=>`rotate(${d[_]})`:w?`rotate(${w})`:null).attr("transform-origin",d||w?Pa`${c?_=>c[_]:v}px ${f?_=>f[_]:g}px`:null).call(et,"href",p?_=>p[_]:this.src).call(et,"preserveAspectRatio",this.preserveAspectRatio).call(et,"crossorigin",this.crossOrigin).call(et,"image-rendering",this.imageRendering).call(et,"clip-path",l?_=>`circle(${l[_]}px)`:m!==void 0?`circle(${m}px)`:null).call(Ct,this,r)).node()}};function cb(t,n,e,r,i,o){return n&&t?u=>t[u]-n[u]/2:n?u=>r-n[u]/2:t&&i!==void 0?u=>t[u]-i/2:i!==void 0?r-i/2:e&&t?u=>t[u]-e[u]:e?u=>r-e[u]:t?u=>t[u]-o:r-o}function qD(t,{x:n,y:e,...r}={}){return r.frameAnchor===void 0&&([n,e]=ne(n,e)),new D5(t,{...r,x:n,y:e})}function YD(t,n,e){var r=1e-8,i=n-1,o=e-1,u=0,a,c,f,s,h,l,d,p;if(t<=0)return 0;if(t>=1)return 1;for(a=Math.log(n/(n+e)),c=Math.log(e/(n+e)),f=Math.exp(n*a)/n,s=Math.exp(e*c)/e,d=f+s,t<f/d?l=Math.pow(n*d*t,1/n):l=1-Math.pow(e*d*(1-t),1/e),p=-Ki(n)-Ki(e)+Ki(n+e);u<10;u++){if(l===0||l===1)return l;if(h=UD(l,n,e)-t,f=Math.exp(i*Math.log(l)+o*Math.log(1-l)+p),s=h/f,l-=f=s/(1-.5*Math.min(1,s*(i/l-o/(1-l)))),l<=0&&(l=.5*(l+f)),l>=1&&(l=.5*(l+f+1)),Math.abs(f)<r*l&&u>0)break}return l}function UD(t,n,e){var r=t===0||t===1?0:Math.exp(Ki(n+e)-Ki(n)-Ki(e)+n*Math.log(t)+e*Math.log(1-t));return t<0||t>1?!1:t<(n+1)/(n+e+2)?r*fb(t,n,e)/n:1-r*fb(1-t,e,n)/e}function fb(t,n,e){var r=1e-30,i=1,o=n+e,u=n+1,a=n-1,c=1,f=1-o*t/u,s,h,l,d;for(Math.abs(f)<r&&(f=r),f=1/f,d=f;i<=100&&(s=2*i,h=i*(e-i)*t/((a+s)*(n+s)),f=1+h*f,Math.abs(f)<r&&(f=r),c=1+h/c,Math.abs(c)<r&&(c=r),f=1/f,d*=f*c,h=-(n+i)*(o+i)*t/((n+s)*(u+s)),f=1+h*f,Math.abs(f)<r&&(f=r),c=1+h/c,Math.abs(c)<r&&(c=r),f=1/f,l=f*c,d*=l,!(Math.abs(l-1)<3e-7));i++);return d}function Ki(t){var n=0,e=[76.18009172947146,-86.5053203294167,24.01409824083091,-1.231739572450155,.001208650973866179,-5395239384953e-18],r=1.000000000190015,i,o,u;for(u=(o=i=t)+5.5,u-=(i+.5)*Math.log(u);n<6;n++)r+=e[n]/++o;return Math.log(2.506628274631*r/i)-u}function XD(t,n){var e=YD(2*Math.min(t,1-t),.5*n,.5);return e=Math.sqrt(n*(1-e)/e),t>.5?e:-e}const WD={ariaLabel:"linear-regression",fill:"currentColor",fillOpacity:.1,stroke:"currentColor",strokeWidth:1.5,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:1};class O5 extends xt{constructor(n,e={}){const{x:r,y:i,z:o,ci:u=.95,precision:a=4}=e;if(super(n,{x:{value:r,scale:"x"},y:{value:i,scale:"y"},z:{value:Lr(e),optional:!0}},e,WD),this.z=o,this.ci=+u,this.precision=+a,!(0<=this.ci&&this.ci<1))throw new Error(`invalid ci; not in [0, 1): ${u}`);if(!(this.precision>0))throw new Error(`invalid precision: ${a}`)}render(n,e,r,i,o){const{x:u,y:a,z:c}=r,{ci:f}=this;return ht("svg:g",o).call(At,this,i,o).call(vt,this,e).call(s=>s.selectAll().data(c?tp(n,c,this.z):[n]).enter().call(h=>h.append("path").attr("fill","none").call(pt,this).call(Nf,this,{...r,fill:null,fillOpacity:null}).attr("d",l=>this._renderLine(l,u,a)).call(f&&!yo(this.fill)?l=>l.select(HD).attr("stroke","none").call(pt,this).call(Nf,this,{...r,stroke:null,strokeOpacity:null,strokeWidth:null}).attr("d",d=>this._renderBand(d,u,a)):()=>{}))).node()}}function HD(){return this.parentNode.insertBefore(this.ownerDocument.createElementNS(me.svg,"path"),this)}class GD extends O5{constructor(n,e){super(n,e)}_renderBand(n,e,r){const{ci:i,precision:o}=this,[u,a]=Tt(n,s=>r[s]),c=Ff(n,r,e),f=F5(n,r,e,(1-i)/2,c);return Ma().y(s=>s).x0(s=>f(s,-1)).x1(s=>f(s,1))(kn(u,a-o/2,o).concat(a))}_renderLine(n,e,r){const[i,o]=Tt(n,a=>r[a]),u=Ff(n,r,e);return`M${u(i)},${i}L${u(o)},${o}`}}class VD extends O5{constructor(n,e){super(n,e)}_renderBand(n,e,r){const{ci:i,precision:o}=this,[u,a]=Tt(n,s=>e[s]),c=Ff(n,e,r),f=F5(n,e,r,(1-i)/2,c);return Ma().x(s=>s).y0(s=>f(s,-1)).y1(s=>f(s,1))(kn(u,a-o/2,o).concat(a))}_renderLine(n,e,r){const[i,o]=Tt(n,a=>e[a]),u=Ff(n,e,r);return`M${i},${u(i)}L${o},${u(o)}`}}function jD(t,{y:n=Dt,x:e=nt,stroke:r,fill:i=St(r)?"currentColor":r,...o}={}){return new GD(t,_p({...o,x:e,y:n,fill:i,stroke:r}))}function ZD(t,{x:n=Dt,y:e=nt,stroke:r,fill:i=St(r)?"currentColor":r,...o}={}){return new VD(t,xp({...o,x:n,y:e,fill:i,stroke:r}))}function Ff(t,n,e){let r=0,i=0,o=0,u=0;for(const s of t){const h=n[s],l=e[s];r+=h,i+=l,o+=h*l,u+=h*h}const a=t.length,c=(a*o-r*i)/(a*u-r*r),f=(i-c*r)/a;return s=>c*s+f}function F5(t,n,e,r,i){const o=Kn(t,s=>n[s])/t.length;let u=0,a=0;for(const s of t)u+=(n[s]-o)**2,a+=(e[s]-i(n[s]))**2;const c=Math.sqrt(a/(t.length-2)),f=XD(r,t.length-2);return(s,h)=>{const l=i(s),d=c*Math.sqrt(1/t.length+(s-o)**2/u);return l+h*f*d}}function wd({path:t=nt,delimiter:n,frameAnchor:e,treeLayout:r=Bu,treeSort:i,treeSeparation:o,treeAnchor:u,treeFilter:a,...c}={}){u=Dp(u),i=q5(i),a!=null&&(a=xd(a)),e===void 0&&(e=u.frameAnchor);const f=Y5(n),s=j5(c,xd),[h,l]=Cn(),[d,p]=Cn();return{x:h,y:d,frameAnchor:e,...ee(c,(m,y)=>{const b=f(ct(m,t)),w=l([]),v=p([]);let g=-1;const x=[],_=[],M=M0().path(E=>b[E]),A=Qe(m)?E=>E.data=m[E.data]:E=>E.data=m.get(E.data),T=r();T.nodeSize&&T.nodeSize([1,1]),T.separation&&o!==void 0&&T.separation(o??Ng);for(const E of s)E[Bf]=E[G5]([]);for(const E of y){const S=[],R=M(E.filter(C=>b[C]!=null)).each(A);i!=null&&R.sort(i),T(R);for(const C of R.descendants())if(!(a!=null&&!a(C))){S.push(++g),x[g]=C.data,u.position(C,g,w,v);for(const $ of s)$[Bf][g]=$[V5](C)}_.push(S)}return{data:x,facets:_}}),...Object.fromEntries(s)}}function B5({path:t=nt,delimiter:n,curve:e="bump-x",stroke:r="#555",strokeWidth:i=1.5,strokeOpacity:o=.5,treeLayout:u=Bu,treeSort:a,treeSeparation:c,treeAnchor:f,treeFilter:s,...h}={}){f=Dp(f),a=q5(a),s!=null&&(s=lb(s)),h={curve:e,stroke:r,strokeWidth:i,strokeOpacity:o,...h};const l=Y5(n),d=j5(h,lb),[p,m]=Cn(),[y,b]=Cn(),[w,v]=Cn(),[g,x]=Cn();return{x1:p,x2:y,y1:w,y2:g,...ee(h,(_,M)=>{const A=l(ct(_,t)),T=m([]),E=b([]),S=v([]),R=x([]);let C=-1;const $=[],N=[],k=M0().path(L=>A[L]),I=u();I.nodeSize&&I.nodeSize([1,1]),I.separation&&c!==void 0&&I.separation(c??Ng);for(const L of d)L[Bf]=L[G5]([]);for(const L of M){const z=[],P=k(L.filter(D=>A[D]!=null)).each(D=>D.data=_[D.data]);a!=null&&P.sort(a),I(P);for(const{source:D,target:B}of P.links())if(!(s!=null&&!s(B,D))){z.push(++C),$[C]=B.data,f.position(D,C,T,S),f.position(B,C,E,R);for(const Y of d)Y[Bf][C]=Y[V5](B,D)}N.push(z)}return{data:$,facets:N}}),...Object.fromEntries(d)}}function Dp(t="left"){switch(`${t}`.trim().toLowerCase()){case"left":return KD;case"right":return QD}throw new Error(`invalid tree anchor: ${t}`)}const KD={frameAnchor:"left",dx:6,position({x:t,y:n},e,r,i){r[e]=n,i[e]=-t}},QD={frameAnchor:"right",dx:-6,position({x:t,y:n},e,r,i){r[e]=-n,i[e]=-t}};function q5(t){return t==null||typeof t=="function"?t:`${t}`.trim().toLowerCase().startsWith("node:")?sb(xd(t)):sb(JD(t))}function sb(t){return(n,e)=>Xn(t(n),t(e))}function JD(t){return n=>n.data?.[t]}function Y5(t="/"){if(t=`${t}`,t==="/")return e=>e;if(t.length!==1)throw new Error("delimiter must be exactly one character");const n=t.charCodeAt(0);return e=>e.map(r=>tO(r,n))}const vd=92,U5=47;function tO(t,n){if(n===vd)throw new Error("delimiter cannot be backslash");let e=!1;for(let r=0,i=t.length;r<i;++r){switch(t.charCodeAt(r)){case vd:if(!e){e=!0;continue}break;case n:e?(t=t.slice(0,r-1)+t.slice(r),--r,--i):t=t.slice(0,r)+"/"+t.slice(r+1);break;case U5:e?(t=t.slice(0,r)+"\\\\"+t.slice(r),r+=2,i+=2):(t=t.slice(0,r)+"\\"+t.slice(r),++r,++i);break}e=!1}return t}function nO(t){let n=!1;for(let e=0,r=t.length;e<r;++e){switch(t.charCodeAt(e)){case vd:if(!n){n=!0;continue}case U5:n&&(t=t.slice(0,e-1)+t.slice(e),--e,--r);break}n=!1}return t}function X5(t){return In(t)&&typeof t.node=="function"}function eO(t){return In(t)&&typeof t.link=="function"}function xd(t){if(X5(t))return t.node;if(t=`${t}`.trim().toLowerCase(),!!t.startsWith("node:")){switch(t){case"node:name":return Md;case"node:path":return _d;case"node:internal":return W5;case"node:external":return H5;case"node:depth":return $d;case"node:height":return Ad}throw new Error(`invalid node value: ${t}`)}}function lb(t){if(X5(t))return t.node;if(eO(t))return t.link;if(t=`${t}`.trim().toLowerCase(),!(!t.startsWith("node:")&&!t.startsWith("parent:"))){switch(t){case"parent:name":return bc(Md);case"parent:path":return bc(_d);case"parent:depth":return bc($d);case"parent:height":return bc(Ad);case"node:name":return Md;case"node:path":return _d;case"node:internal":return W5;case"node:external":return H5;case"node:depth":return $d;case"node:height":return Ad}throw new Error(`invalid link value: ${t}`)}}function _d(t){return t.id}function Md(t){return rO(t.id)}function $d(t){return t.depth}function Ad(t){return t.height}function W5(t){return!!t.children}function H5(t){return!t.children}function bc(t){return(n,e)=>e==null?void 0:t(e)}function rO(t){let n=t.length;for(;--n>0&&!iO(t,n););return nO(t.slice(n+1))}function iO(t,n){if(t[n]==="/"){let e=0;for(;n>0&&t[--n]==="\\";)++e;if((e&1)===0)return!0}return!1}const G5=2,V5=3,Bf=4;function j5(t,n){const e=[];for(const r in t){const i=t[r],o=n(i);o!==void 0&&e.push([r,...Cn(i),o])}return e}function Z5(t,{fill:n,stroke:e,strokeWidth:r,strokeOpacity:i,strokeLinejoin:o,strokeLinecap:u,strokeMiterlimit:a,strokeDasharray:c,strokeDashoffset:f,marker:s,markerStart:h=s,markerEnd:l=s,dot:d=St(h)&&St(l),text:p="node:name",textStroke:m="var(--plot-background)",title:y="node:path",dx:b,dy:w,textAnchor:v,treeLayout:g=Bu,textLayout:x=g===Bu||g===v0?"mirrored":"normal",tip:_,...M}={}){if(b===void 0&&(b=Dp(M.treeAnchor).dx),v!==void 0)throw new Error("textAnchor is not a configurable tree option");x=Rn(x,"textLayout",["mirrored","normal"]);function A(T){return vo(t,wd({treeLayout:g,text:p,fill:n===void 0?"currentColor":n,stroke:m,dx:b,dy:w,title:y,...T,...M}))}return Wn(a5(t,B5({treeLayout:g,markerStart:h,markerEnd:l,stroke:e!==void 0?e:n===void 0?"node:internal":n,strokeWidth:r,strokeOpacity:i,strokeLinejoin:o,strokeLinecap:u,strokeMiterlimit:a,strokeDasharray:c,strokeDashoffset:f,...M})),d?Or(t,wd({treeLayout:g,fill:n===void 0?"node:internal":n,title:y,tip:_,...M})):null,p!=null?x==="mirrored"?[A({textAnchor:"start",treeFilter:"node:external"}),A({textAnchor:"end",treeFilter:"node:internal",dx:-b})]:A():null)}function oO(t,n){return Z5(t,{...n,treeLayout:v0})}const K5={ariaLabel:"waffle"};class Q5 extends Tp{constructor(n,{unit:e=1,gap:r=1,round:i,multiple:o,...u}={}){super(n,t4("x",u),K5),this.unit=Math.max(0,e),this.gap=+r,this.round=n4(i),this.multiple=e4(o)}}class J5 extends Ep{constructor(n,{unit:e=1,gap:r=1,round:i,multiple:o,...u}={}){super(n,t4("y",u),K5),this.unit=Math.max(0,e),this.gap=+r,this.round=n4(i),this.multiple=e4(o)}}function t4(t,n){const e=t==="y"?"x":"y",r=`${t}1`,i=`${t}2`;return un(uO(n),function(o,u,a,c,f){const{round:s,unit:h}=this,l=a[r].value,d=a[i].value,p=Ds({...e in a&&{[e]:a[e]},[r]:a[r],[i]:a[i]},c),m=this[t==="y"?"_width":"_height"](c,p,f),y=this[t==="y"?"_x":"_y"](c,p,f),b=h*fO(c.scales[t]),{multiple:w=Math.max(1,Math.floor(Math.sqrt(m/b)))}=this,v=Math.min(m/w,b*w),g=b*w,x=(m-w*v)/2,_=typeof y=="function"?k=>y(k)+x:y+x,M=c[t](0),A=t==="y"?([k,I])=>[k*v,-I*g]:([k,I])=>[I*g,k*v],T=typeof _=="function"?k=>_(k)-m/2:()=>_,[E,S]=t==="y"?[0,1]:[1,0],R=d.length,C=new Array(R),$=new Float64Array(R),N=new Float64Array(R);for(let k=0;k<R;++k){C[k]=Op(s(l[k]/h),s(d[k]/h),w).map(A);const I=C[k].pop();$[k]=I[E]+T(k),N[k]=I[S]+M}return{channels:{polygon:{value:C,source:null,filter:null},[`c${e}`]:{value:[v,_],source:null,filter:null},[`c${t}`]:{value:[g,M],source:null,filter:null},[e]:{value:$,scale:null,source:null},[r]:{value:N,scale:null,source:a[r]},[i]:{value:N,scale:null,source:a[i]}}}})}function uO({render:t,...n}){return{...n,render:Zu(t,function(e,r,i,o,u){const{gap:a,rx:c,ry:f}=this,{channels:s,ariaLabel:h,href:l,title:d,...p}=i,{document:m}=u,y=s.polygon.value,[b,w]=s.cx.value,[v,g]=s.cy.value,x=tz(),_=m.createElementNS(me.svg,"pattern");_.setAttribute("width",b),_.setAttribute("height",v),_.setAttribute("patternUnits","userSpaceOnUse");const M=_.appendChild(m.createElementNS(me.svg,"rect"));return M.setAttribute("x",a/2),M.setAttribute("y",a/2),M.setAttribute("width",b-a),M.setAttribute("height",v-a),c!=null&&M.setAttribute("rx",c),f!=null&&M.setAttribute("ry",f),ht("svg:g",u).call(At,this,o,u).call(this._transform,this,r).call(A=>A.selectAll().data(e).enter().append(()=>_.cloneNode(!0)).attr("id",T=>`${x}-${T}`).select("rect").call(pt,this).call(Ct,this,p)).call(A=>A.selectAll().data(e).enter().append("path").attr("transform",Pa`translate(${w},${g})`).attr("d",T=>`M${y[T].join("L")}Z`).attr("fill",T=>`url(#${x}-${T})`).attr("stroke",this.stroke==null?null:"none").call(Ct,this,{ariaLabel:h,href:l,title:d})).node()})}}function Op(t,n,e){if(n<t)return Op(n,t,e);if(t<0)return aO(t,n,e,Math.ceil(-Math.min(t,n)/e));const r=Math.floor(t%e),i=Math.ceil(t%e),o=Math.floor(n%e),u=Math.ceil(n%e),a=Math.floor(t/e),c=Math.ceil(t/e),f=Math.floor(n/e),s=Math.ceil(n/e),h=[];return s>c&&h.push([0,c]),h.push([r,c],[r,a+t%1],[i,a+t%1]),t%e>e-1||(h.push([i,a]),f>a&&h.push([e,a])),f>a&&h.push([e,f]),h.push([u,f],[u,f+n%1],[o,f+n%1]),n%e<1||(h.push([o,s]),s>c&&h.push([0,s])),h.push(cO(t,n,e)),h}function aO(t,n,e,r){return Op(t+r*e,n+r*e,e).map(([i,o])=>[i,o-r])}function cO(t,n,e){const r=Math.floor(n/e)-Math.floor(t/e);return r===0?ah(t,n,e):r===1?Math.floor(n%e)>Math.ceil(t%e)?[(Math.floor(n%e)+Math.ceil(t%e))/2,Math.floor(n/e)]:n%e>e-t%e?ah(n-n%e,n,e):ah(t,e*Math.ceil(t/e),e):[e/2,(Math.round(t/e)+Math.round(n/e))/2]}function ah(t,n,e){const r=Math.floor(n)-Math.floor(t);return r===0?[Math.floor(t%e)+.5,Math.floor(t/e)+(t+n)/2%1]:r===1?n%1-t%1>.5?[Math.ceil(t%e),Math.floor(n/e)+(t%1+n%1)/2]:n%1>1-t%1?[Math.floor(n%e)+.5,Math.floor(n/e)+n%1/2]:[Math.floor(t%e)+.5,Math.floor(t/e)+(1+t%1)/2]:[Math.ceil(t%e)+Math.ceil(Math.floor(n)-Math.ceil(t))/2,Math.floor(t/e)+(n>=1+t?.5:(t+n)/2%1)]}function n4(t){if(t===void 0||t===!1)return Number;if(t===!0)return Math.round;if(typeof t!="function")throw new Error(`invalid round: ${t}`);return t}function e4(t){return t===void 0?void 0:Math.max(1,Math.floor(t))}function fO({domain:t,range:n}){return hb(n)/hb(t)}function hb(t){const[n,e]=Tt(t);return e-n}function sO(t,{tip:n,...e}={}){return Po(e)||(e={...e,y:Dt,x2:nt}),new Q5(t,{tip:r4(n),...Vs(up(Hs(e)))})}function lO(t,{tip:n,...e}={}){return Po(e)||(e={...e,x:Dt,y2:nt}),new J5(t,{tip:r4(n),...js(ap(Gs(e)))})}function r4(t){return t===!0?{maxRadius:1/0}:In(t)&&t.maxRadius===void 0?{...t,maxRadius:1/0}:void 0}var ch,db;function hO(){if(db)return ch;db=1;function t(u,a,c,f,s){for(var h=s+1;f<=s;){var l=f+s>>>1,d=u[l],p=c!==void 0?c(d,a):d-a;p>=0?(h=l,s=l-1):f=l+1}return h}function n(u,a,c,f,s){for(var h=s+1;f<=s;){var l=f+s>>>1,d=u[l],p=c!==void 0?c(d,a):d-a;p>0?(h=l,s=l-1):f=l+1}return h}function e(u,a,c,f,s){for(var h=f-1;f<=s;){var l=f+s>>>1,d=u[l],p=c!==void 0?c(d,a):d-a;p<0?(h=l,f=l+1):s=l-1}return h}function r(u,a,c,f,s){for(var h=f-1;f<=s;){var l=f+s>>>1,d=u[l],p=c!==void 0?c(d,a):d-a;p<=0?(h=l,f=l+1):s=l-1}return h}function i(u,a,c,f,s){for(;f<=s;){var h=f+s>>>1,l=u[h],d=c!==void 0?c(l,a):l-a;if(d===0)return h;d<=0?f=h+1:s=h-1}return-1}function o(u,a,c,f,s,h){return typeof c=="function"?h(u,a,c,f===void 0?0:f|0,s===void 0?u.length-1:s|0):h(u,a,void 0,c===void 0?0:c|0,f===void 0?u.length-1:f|0)}return ch={ge:function(u,a,c,f,s){return o(u,a,c,f,s,t)},gt:function(u,a,c,f,s){return o(u,a,c,f,s,n)},lt:function(u,a,c,f,s){return o(u,a,c,f,s,e)},le:function(u,a,c,f,s){return o(u,a,c,f,s,r)},eq:function(u,a,c,f,s){return o(u,a,c,f,s,i)}},ch}var fh,gb;function dO(){if(gb)return fh;gb=1;var t=hO(),n=0,e=1,r=2;fh=v;function i(g,x,_,M,A){this.mid=g,this.left=x,this.right=_,this.leftPoints=M,this.rightPoints=A,this.count=(x?x.count:0)+(_?_.count:0)+M.length}var o=i.prototype;function u(g,x){g.mid=x.mid,g.left=x.left,g.right=x.right,g.leftPoints=x.leftPoints,g.rightPoints=x.rightPoints,g.count=x.count}function a(g,x){var _=y(x);g.mid=_.mid,g.left=_.left,g.right=_.right,g.leftPoints=_.leftPoints,g.rightPoints=_.rightPoints,g.count=_.count}function c(g,x){var _=g.intervals([]);_.push(x),a(g,_)}function f(g,x){var _=g.intervals([]),M=_.indexOf(x);return M<0?n:(_.splice(M,1),a(g,_),e)}o.intervals=function(g){return g.push.apply(g,this.leftPoints),this.left&&this.left.intervals(g),this.right&&this.right.intervals(g),g},o.insert=function(g){var x=this.count-this.leftPoints.length;if(this.count+=1,g[1]<this.mid)this.left?4*(this.left.count+1)>3*(x+1)?c(this,g):this.left.insert(g):this.left=y([g]);else if(g[0]>this.mid)this.right?4*(this.right.count+1)>3*(x+1)?c(this,g):this.right.insert(g):this.right=y([g]);else{var _=t.ge(this.leftPoints,g,p),M=t.ge(this.rightPoints,g,m);this.leftPoints.splice(_,0,g),this.rightPoints.splice(M,0,g)}},o.remove=function(g){var x=this.count-this.leftPoints;if(g[1]<this.mid){if(!this.left)return n;var _=this.right?this.right.count:0;if(4*_>3*(x-1))return f(this,g);var M=this.left.remove(g);return M===r?(this.left=null,this.count-=1,e):(M===e&&(this.count-=1),M)}else if(g[0]>this.mid){if(!this.right)return n;var A=this.left?this.left.count:0;if(4*A>3*(x-1))return f(this,g);var M=this.right.remove(g);return M===r?(this.right=null,this.count-=1,e):(M===e&&(this.count-=1),M)}else{if(this.count===1)return this.leftPoints[0]===g?r:n;if(this.leftPoints.length===1&&this.leftPoints[0]===g){if(this.left&&this.right){for(var T=this,E=this.left;E.right;)T=E,E=E.right;if(T===this)E.right=this.right;else{var S=this.left,M=this.right;T.count-=E.count,T.right=E.left,E.left=S,E.right=M}u(this,E),this.count=(this.left?this.left.count:0)+(this.right?this.right.count:0)+this.leftPoints.length}else this.left?u(this,this.left):u(this,this.right);return e}for(var S=t.ge(this.leftPoints,g,p);S<this.leftPoints.length&&this.leftPoints[S][0]===g[0];++S)if(this.leftPoints[S]===g){this.count-=1,this.leftPoints.splice(S,1);for(var M=t.ge(this.rightPoints,g,m);M<this.rightPoints.length&&this.rightPoints[M][1]===g[1];++M)if(this.rightPoints[M]===g)return this.rightPoints.splice(M,1),e}return n}};function s(g,x,_){for(var M=0;M<g.length&&g[M][0]<=x;++M){var A=_(g[M]);if(A)return A}}function h(g,x,_){for(var M=g.length-1;M>=0&&g[M][1]>=x;--M){var A=_(g[M]);if(A)return A}}function l(g,x){for(var _=0;_<g.length;++_){var M=x(g[_]);if(M)return M}}o.queryPoint=function(g,x){if(g<this.mid){if(this.left){var _=this.left.queryPoint(g,x);if(_)return _}return s(this.leftPoints,g,x)}else if(g>this.mid){if(this.right){var _=this.right.queryPoint(g,x);if(_)return _}return h(this.rightPoints,g,x)}else return l(this.leftPoints,x)},o.queryInterval=function(g,x,_){if(g<this.mid&&this.left){var M=this.left.queryInterval(g,x,_);if(M)return M}if(x>this.mid&&this.right){var M=this.right.queryInterval(g,x,_);if(M)return M}return x<this.mid?s(this.leftPoints,x,_):g>this.mid?h(this.rightPoints,g,_):l(this.leftPoints,_)};function d(g,x){return g-x}function p(g,x){var _=g[0]-x[0];return _||g[1]-x[1]}function m(g,x){var _=g[1]-x[1];return _||g[0]-x[0]}function y(g){if(g.length===0)return null;for(var x=[],_=0;_<g.length;++_)x.push(g[_][0],g[_][1]);x.sort(d);for(var M=x[x.length>>1],A=[],T=[],E=[],_=0;_<g.length;++_){var S=g[_];S[1]<M?A.push(S):M<S[0]?T.push(S):E.push(S)}var R=E,C=E.slice();return R.sort(p),C.sort(m),new i(M,y(A),y(T),R,C)}function b(g){this.root=g}var w=b.prototype;w.insert=function(g){this.root?this.root.insert(g):this.root=new i(g[0],null,null,[g],[g])},w.remove=function(g){if(this.root){var x=this.root.remove(g);return x===r&&(this.root=null),x!==n}return!1},w.queryPoint=function(g,x){if(this.root)return this.root.queryPoint(g,x)},w.queryInterval=function(g,x,_){if(g<=x&&this.root)return this.root.queryInterval(g,x,_)},Object.defineProperty(w,"count",{get:function(){return this.root?this.root.count:0}}),Object.defineProperty(w,"intervals",{get:function(){return this.root?this.root.intervals([]):[]}});function v(g){return!g||g.length===0?new b(null):new b(y(g))}return fh}var gO=dO();const pO=b4(gO),mO=({marginLeft:t})=>[1,t],yO=({width:t,marginRight:n})=>[-1,t-n],bO=({width:t,marginLeft:n,marginRight:e})=>[0,(n+t-e)/2],wO=({marginTop:t})=>[1,t],vO=({height:t,marginBottom:n})=>[-1,t-n],xO=({height:t,marginTop:n,marginBottom:e})=>[0,(n+t-e)/2];function i4(t){return typeof t=="string"?{anchor:t}:t}function _O(t={},n={}){arguments.length===1&&([t,n]=o4(t));let{anchor:e="left",padding:r=1,r:i=n.r}=i4(t);switch(`${e}`.toLowerCase()){case"left":e=mO;break;case"right":e=yO;break;case"middle":e=bO;break;default:throw new Error(`unknown dodge anchor: ${e}`)}return u4("x","y",e,$t(r),i,n)}function MO(t={},n={}){arguments.length===1&&([t,n]=o4(t));let{anchor:e="bottom",padding:r=1,r:i=n.r}=i4(t);switch(`${e}`.toLowerCase()){case"top":e=wO;break;case"bottom":e=vO;break;case"middle":e=xO;break;default:throw new Error(`unknown dodge anchor: ${e}`)}return u4("y","x",e,$t(r),i,n)}function o4(t){const{anchor:n,padding:e,...r}=t,{r:i}=r;return[{anchor:n,padding:e,r:i},r]}function u4(t,n,e,r,i,o){if(i!=null&&typeof i!="number"){let{channels:u,sort:a,reverse:c}=o;u=zg(u),u?.r===void 0&&(o={...o,channels:{...u,r:{value:i,scale:"r"}}}),a===void 0&&c===void 0&&(o.sort={channel:"-r"})}return un(o,function(u,a,c,f,s,h){let{[n]:l,r:d}=c;if(!c[n])throw new Error(`missing channel: ${n}`);({[n]:l}=Ra(c,f,h));const p=d?void 0:i!==void 0?$t(i):this.r!==void 0?this.r:3;d&&(d=ct(d.value,f[d.scale]||nt,Float64Array));let[m,y]=e(s);const b=m?AO:$O,w=new Float64Array(l.length),v=d?g=>d[g]:()=>p;for(let g of a){const x=pO();g=g.filter(d?M=>Sf(l[M])&&Ue(d[M]):M=>Sf(l[M]));const _=new Float64Array(2*g.length+2);for(const M of g){const A=v(M),T=m?A+r:0,E=l[M]-A,S=l[M]+A;let R=2;x.queryInterval(E-r,S+r,([,,$])=>{const N=w[$]-T,k=l[M]-l[$],I=r+(d?d[M]+d[$]:2*p),L=Math.sqrt(I*I-k*k);_[R++]=N-L,_[R++]=N+L});let C=_.slice(0,R);m&&(C=C.filter($=>$>=0));t:for(const $ of C.sort(b)){for(let N=0;N<R;N+=2)if(_[N]+1e-6<$&&$<_[N+1]-1e-6)continue t;w[M]=$+T;break}x.insert([E,S,M])}}m||(m=1);for(const g of a)for(const x of g)w[x]=w[x]*m+y;return{data:u,facets:a,channels:{[t]:{value:w,source:null},[n]:{value:l,source:c[n]},...d&&{r:{value:d,source:c.r}}}}})}function $O(t,n){return Math.abs(t)-Math.abs(n)}function AO(t,n){return t-n}function SO(t,n){return arguments.length===1&&({basis:t,...n}=t),Np(Fp(t),n)}function TO(t,n){return arguments.length===1&&({basis:t,...n}=t),Cp(Fp(t),n)}function Fp(t){if(t===void 0)return pb;if(typeof t=="function")return il(Ig(t));if(/^p\d{2}$/i.test(t))return Xo(Cg(t));switch(`${t}`.toLowerCase()){case"deviation":return NO;case"first":return pb;case"last":return kO;case"max":return CO;case"mean":return RO;case"median":return IO;case"min":return zO;case"sum":return LO;case"extent":return EO}throw new Error(`invalid basis: ${t}`)}function il(t){return{mapIndex(n,e,r){const i=+t(n,e);for(const o of n)r[o]=e[o]===null?NaN:e[o]/i}}}function Xo(t){return il((n,e)=>t(n,r=>e[r]))}const EO={mapIndex(t,n,e){const[r,i]=Tt(t,u=>n[u]),o=i-r;for(const u of t)e[u]=n[u]===null?NaN:(n[u]-r)/o}},pb=il((t,n)=>{for(let e=0;e<t.length;++e){const r=n[t[e]];if(Kt(r))return r}}),kO=il((t,n)=>{for(let e=t.length-1;e>=0;--e){const r=n[t[e]];if(Kt(r))return r}}),NO={mapIndex(t,n,e){const r=ia(t,o=>n[o]),i=$o(t,o=>n[o]);for(const o of t)e[o]=n[o]===null?NaN:i?(n[o]-r)/i:0}},CO=Xo(qt),RO=Xo(ia),IO=Xo(to),zO=Xo(on),LO=Xo(Kn);function PO(t,n){return a4("x",t,n)}function DO(t,n){return a4("y",t,n)}function a4(t,n,e={}){let r,i=1;if(typeof n=="number")i=n,r=(f,s)=>+f+s;else{if(typeof n=="string"){const f=n.startsWith("-")?-1:1;[n,i]=Tg(n.replace(/^[+-]/,"")),i*=f}n=Rs(n),r=(f,s)=>n.offset(f,s)}const o=`${t}1`,u=`${t}2`,a=xe({[o]:f=>f.map(s=>r(s,i)),[u]:f=>f},e),c=a[u].transform;return a[u].transform=()=>{const f=c(),[s,h]=Tt(f);return f.domain=i<0?[s,r(h,i)]:[r(s,i),h],f},a}function OO(t,n={}){if(typeof t=="string")switch(t.toLowerCase()){case"first":return c4(n);case"last":return f4(n)}if(typeof t=="function")return Er(null,t,n);let e,r;for(e in t){if(r!==void 0)throw new Error("ambiguous selector; multiple inputs");r=FO(t[e])}if(r===void 0)throw new Error(`invalid selector: ${t}`);return Er(e,r,n)}function FO(t){if(typeof t=="function")return t;switch(`${t}`.toLowerCase()){case"min":return Bp;case"max":return qp}throw new Error(`unknown selector: ${t}`)}function c4(t){return Er(null,XO,t)}function f4(t){return Er(null,WO,t)}function BO(t){return Er("x",Bp,t)}function qO(t){return Er("y",Bp,t)}function YO(t){return Er("x",qp,t)}function UO(t){return Er("y",qp,t)}function*XO(t){yield t[0]}function*WO(t){yield t[t.length-1]}function*Bp(t,n){yield Rb(t,e=>n[e])}function*qp(t,n){yield ra(t,e=>n[e])}function Er(t,n,e){if(t!=null){if(e[t]==null)throw new Error(`missing channel: ${t}`);t=e[t]}const r=Lr(e);return ee(e,(i,o)=>{const u=ct(i,r),a=ct(i,t),c=[];for(const f of o){const s=[];for(const h of u?je(f,l=>u[l]).values():[f])for(const l of n(h,a))s.push(l);c.push(s)}return{data:i,facets:c}})}xt.prototype.plot=function({marks:t=[],...n}={}){return K6({...n,marks:[...t,this]})};const KO=Object.freeze(Object.defineProperty({__proto__:null,Area:Qs,Arrow:c5,BarX:Tp,BarY:Ep,Cell:Js,Contour:k5,Density:R5,Dot:tl,Frame:V6,Geo:L5,Hexgrid:P5,Image:D5,Line:nl,Link:u5,Mark:xt,Raster:_5,Rect:Ks,RuleX:J3,RuleY:t6,Text:Us,TickX:g5,TickY:p5,Tip:j6,Vector:Xs,WaffleX:Q5,WaffleY:J5,area:pd,areaX:Ku,areaY:Mo,arrow:uP,auto:bP,autoSpec:f5,axisFx:M6,axisFy:_6,axisX:lp,axisY:sp,barX:Gr,barY:Vr,bin:Xr,binX:ur,binY:ar,bollinger:ri,bollingerX:DP,bollingerY:OP,boxX:BP,boxY:qP,cell:Xi,cellX:fP,cellY:sP,centroid:z5,circle:gP,cluster:oO,column:Cn,contour:nD,crosshair:rD,crosshairX:iD,crosshairY:oD,delaunayLink:bD,delaunayMesh:wD,density:$D,differenceX:TD,differenceY:ED,dodgeX:_O,dodgeY:MO,dot:Or,dotX:hP,dotY:dP,filter:mR,find:CR,formatIsoDate:P3,formatMonth:jI,formatNumber:L3,formatWeekday:ZI,frame:bp,geo:Pp,geoCentroid:kD,graticule:RD,gridFx:k6,gridFy:T6,gridX:E6,gridY:S6,group:Yg,groupX:Hu,groupY:Gu,groupZ:qg,hexagon:pP,hexbin:ID,hexgrid:PD,hull:vD,identity:nt,image:qD,indexOf:Dt,initializer:un,interpolateNearest:T5,interpolateNone:bd,interpolatorBarycentric:S5,interpolatorRandomWalk:E5,legend:Zz,line:el,lineX:Qu,lineY:Ju,linearRegressionX:jD,linearRegressionY:ZD,link:a5,map:xe,mapX:Np,mapY:Cp,marks:Wn,normalize:Fp,normalizeX:SO,normalizeY:TO,numberInterval:G_,plot:K6,pointer:ip,pointerX:op,pointerY:Cf,raster:UP,rect:yp,rectX:zf,rectY:Lf,reverse:yR,ruleX:Sr,ruleY:Tr,scale:MI,select:OO,selectFirst:c4,selectLast:f4,selectMaxX:YO,selectMaxY:UO,selectMinX:BO,selectMinY:qO,shiftX:PO,shiftY:DO,shuffle:bR,sort:n3,sphere:CD,spike:Oz,stackX:Y6,stackX1:nL,stackX2:eL,stackY:U6,stackY1:rL,stackY2:iL,text:vo,textX:i6,textY:o6,tickX:m5,tickY:y5,timeInterval:Eg,tip:Z6,transform:ee,tree:Z5,treeLink:B5,treeNode:wd,utcInterval:Es,valueof:ct,vector:p6,vectorX:m6,vectorY:y6,voronoi:xD,voronoiMesh:_D,waffleX:sO,waffleY:lO,window:rl,windowX:AP,windowY:SP},Symbol.toStringTag,{value:"Module"}));export{KO as a,jO as i};
