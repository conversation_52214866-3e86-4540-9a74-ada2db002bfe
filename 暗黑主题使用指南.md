# 暗黑主题UI实现指南

现在你的LIV Analyzer已经完全支持暗黑主题了！以下是实现的功能和使用方法：

## 已实现的功能

### 1. 智能主题模式
- 应用程序启动时默认跟随系统主题
- 支持暗黑主题，背景色采用层级设计（#121212 到 #383838）
- 文字颜色自动适配（主要文字#FFFFFF，次要文字#B3B3B3）

### 2. 主题切换功能
- 支持浅色/深色/跟随系统三种模式
- 实时切换无需重启
- 主题设置自动保存

### 3. 自定义主题色
- 提供12种预设主题色
- 支持自定义颜色选择器
- 主题色应用到所有控件

### 4. 图表配色方案
- 默认配色：适合暗黑主题的柔和色彩
- 彩虹配色：鲜艳的多彩方案
- 单色渐变：同色系渐变
- 高对比度：黑白红绿蓝
- 色盲友好：针对色盲用户优化

### 5. UI缩放
- 支持75%到200%缩放
- 实时预览效果
- 适配高DPI显示器

## 使用方法

### 在主窗口添加主题切换按钮
```xml
<!-- 在MainWindow.xaml的标题栏添加 -->
<controls:ThemeToggleButton HorizontalAlignment="Right" 
                            VerticalAlignment="Top" 
                            Margin="0,5,50,0"/>
```

### 在菜单中添加主题设置
```xml
<MenuItem Header="视图">
    <MenuItem Header="主题设置" Click="ShowThemeSettings_Click"/>
</MenuItem>
```

### 在代码中切换主题
```csharp
// 切换到暗黑主题
App.SwitchTheme(ApplicationTheme.Dark);

// 切换到浅色主题
App.SwitchTheme(ApplicationTheme.Light);

// 跟随系统
App.SwitchTheme(null);
```

### 应用暗黑样式到控件
```xml
<!-- 使用暗黑卡片样式 -->
<Border Style="{StaticResource DarkCard}">
    <TextBlock Text="这是一个暗黑主题的卡片"/>
</Border>

<!-- 使用暗黑按钮 -->
<Button Style="{StaticResource DarkButton}" Content="暗黑按钮"/>

<!-- 使用主题按钮 -->
<Button Style="{StaticResource AccentButton}" Content="主题色按钮"/>
```

## 图表暗黑主题适配

对于OxyPlot图表，使用以下代码适配暗黑主题：

```csharp
private void ApplyDarkThemeToPlot(PlotModel plot)
{
    // 背景色
    plot.Background = OxyColor.FromRgb(30, 30, 30);
    plot.PlotAreaBackground = OxyColor.FromRgb(37, 37, 38);
    
    // 文字颜色
    plot.TextColor = OxyColor.FromRgb(228, 228, 228);
    plot.SubtitleColor = OxyColor.FromRgb(179, 179, 179);
    
    // 网格线颜色
    foreach (var axis in plot.Axes)
    {
        axis.AxislineColor = OxyColor.FromRgb(64, 64, 64);
        axis.MajorGridlineColor = OxyColor.FromRgb(64, 64, 64);
        axis.MinorGridlineColor = OxyColor.FromRgb(48, 48, 48);
        axis.TicklineColor = OxyColor.FromRgb(128, 128, 128);
        axis.TextColor = OxyColor.FromRgb(228, 228, 228);
    }
    
    // 图例
    if (plot.Legends.Any())
    {
        plot.LegendBackground = OxyColor.FromRgb(45, 45, 48);
        plot.LegendBorder = OxyColor.FromRgb(63, 63, 70);
        plot.LegendTextColor = OxyColor.FromRgb(228, 228, 228);
    }
}
```

## 最佳实践

1. **颜色对比度**：确保文字和背景有足够的对比度（至少4.5:1）
2. **避免纯黑**：使用#121212而不是#000000作为最深的背景色
3. **层级区分**：使用不同深度的灰色区分UI层级
4. **高亮显示**：使用主题色而不是白色进行高亮
5. **图标适配**：为暗黑主题准备浅色版本的图标

## 编译和运行

1. 确保已安装ModernWPF NuGet包
2. 编译项目：`dotnet build`
3. 运行应用程序，默认会显示暗黑主题

现在你的LIV Analyzer拥有了专业的暗黑主题UI！用户可以根据自己的喜好选择主题，提升使用体验。