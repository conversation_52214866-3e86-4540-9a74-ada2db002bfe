using System.Windows;
using System.Windows.Controls;
using LIVAnalyzer.UI.ViewModels;
using OxyPlot.Axes;

namespace LIVAnalyzer.UI.Views
{
    /// <summary>
    /// 坐标轴右键菜单服务
    /// </summary>
    public static class AxisContextMenuService
    {
        /// <summary>
        /// 创建坐标轴的右键菜单
        /// </summary>
        public static ContextMenu CreateAxisContextMenu(Axis axis, string chartName, MainWindowViewModel viewModel)
        {
            var contextMenu = new ContextMenu();
            
            // 设置轴范围
            var setRangeItem = new MenuItem
            {
                Header = "设置范围...",
                Icon = new TextBlock { Text = "⚙️", FontSize = 12, Margin = new Thickness(2) }
            };
            setRangeItem.Click += (s, e) => viewModel.ShowAxisSettings(axis, chartName);
            contextMenu.Items.Add(setRangeItem);
            
            // 分割线
            contextMenu.Items.Add(new Separator());
            
            // 自动范围
            var autoRangeItem = new MenuItem
            {
                Header = "自动范围",
                Icon = new TextBlock { Text = "🔄", FontSize = 12, Margin = new Thickness(2) }
            };
            autoRangeItem.Click += (s, e) => SetAutoRange(axis);
            contextMenu.Items.Add(autoRangeItem);
            
            // 重置缩放
            var resetZoomItem = new MenuItem
            {
                Header = "重置缩放",
                Icon = new TextBlock { Text = "🔍", FontSize = 12, Margin = new Thickness(2) }
            };
            resetZoomItem.Click += (s, e) => ResetAxisZoom(axis);
            contextMenu.Items.Add(resetZoomItem);
            
            // 分割线
            contextMenu.Items.Add(new Separator());
            
            // 使用当前范围
            var useCurrentRangeItem = new MenuItem
            {
                Header = "固定当前范围",
                Icon = new TextBlock { Text = "📌", FontSize = 12, Margin = new Thickness(2) }
            };
            useCurrentRangeItem.Click += (s, e) => FixCurrentRange(axis);
            contextMenu.Items.Add(useCurrentRangeItem);
            
            // 复制范围信息
            var copyRangeItem = new MenuItem
            {
                Header = "复制范围信息",
                Icon = new TextBlock { Text = "📋", FontSize = 12, Margin = new Thickness(2) }
            };
            copyRangeItem.Click += (s, e) => CopyRangeInfo(axis);
            contextMenu.Items.Add(copyRangeItem);
            
            return contextMenu;
        }
        
        /// <summary>
        /// 设置轴为自动范围
        /// </summary>
        private static void SetAutoRange(Axis axis)
        {
            axis.Minimum = double.NaN;
            axis.Maximum = double.NaN;
            axis.IsZoomEnabled = true;
            axis.IsPanEnabled = true;
            axis.PlotModel?.InvalidatePlot(true);
        }
        
        /// <summary>
        /// 重置轴的缩放
        /// </summary>
        private static void ResetAxisZoom(Axis axis)
        {
            axis.Reset();
            axis.PlotModel?.InvalidatePlot(true);
        }
        
        /// <summary>
        /// 固定当前显示范围
        /// </summary>
        private static void FixCurrentRange(Axis axis)
        {
            axis.Minimum = axis.ActualMinimum;
            axis.Maximum = axis.ActualMaximum;
            axis.IsZoomEnabled = false;
            axis.IsPanEnabled = false;
            axis.PlotModel?.InvalidatePlot(true);
            
            // 显示确认消息
            MessageBox.Show(
                $"已固定{GetAxisDescription(axis)}范围为：\n" +
                $"最小值: {axis.Minimum:F3}\n" +
                $"最大值: {axis.Maximum:F3}",
                "范围已固定",
                MessageBoxButton.OK,
                MessageBoxImage.Information);
        }
        
        /// <summary>
        /// 复制范围信息到剪贴板
        /// </summary>
        private static void CopyRangeInfo(Axis axis)
        {
            var rangeInfo = $"{GetAxisDescription(axis)}\n" +
                           $"当前范围: {axis.ActualMinimum:F6} - {axis.ActualMaximum:F6}\n" +
                           $"设定范围: {(double.IsNaN(axis.Minimum) ? "自动" : axis.Minimum.ToString("F6"))} - " +
                           $"{(double.IsNaN(axis.Maximum) ? "自动" : axis.Maximum.ToString("F6"))}\n" +
                           $"标题: {(string.IsNullOrEmpty(axis.Title) ? "无" : axis.Title)}";
            
            try
            {
                Clipboard.SetText(rangeInfo);
                MessageBox.Show("范围信息已复制到剪贴板", "复制成功", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch
            {
                MessageBox.Show("复制失败", "错误", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        /// <summary>
        /// 获取轴的描述文本
        /// </summary>
        private static string GetAxisDescription(Axis axis)
        {
            var position = axis.Position switch
            {
                AxisPosition.Bottom => "底部",
                AxisPosition.Top => "顶部",
                AxisPosition.Left => "左侧", 
                AxisPosition.Right => "右侧",
                _ => "未知位置"
            };
            
            var type = axis.Position switch
            {
                AxisPosition.Bottom or AxisPosition.Top => "X轴",
                AxisPosition.Left or AxisPosition.Right => "Y轴",
                _ => "坐标轴"
            };
            
            return $"{position}{type}";
        }
    }
}