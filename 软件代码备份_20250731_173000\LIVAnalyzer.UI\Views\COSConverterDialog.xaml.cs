using System.Windows;
using LIVAnalyzer.UI.ViewModels;

namespace LIVAnalyzer.UI.Views
{
    /// <summary>
    /// COSConverterDialog.xaml 的交互逻辑
    /// </summary>
    public partial class COSConverterDialog : Window
    {
        public COSConverterDialog()
        {
            InitializeComponent();
            DataContext = new COSConverterDialogViewModel();
        }

        public COSConverterDialog(Window owner) : this()
        {
            Owner = owner;
        }
    }
}