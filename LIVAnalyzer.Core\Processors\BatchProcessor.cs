using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using LIVAnalyzer.Core.Exporters;
using LIVAnalyzer.Data.Loaders;
using LIVAnalyzer.Models;
using LIVAnalyzer.Services.Logging;

namespace LIVAnalyzer.Core.Processors
{
    /// <summary>
    /// 批量处理器，与Python版本功能保持一致
    /// </summary>
    public class BatchProcessor
    {
        private readonly LIVDataProcessor _dataProcessor;
        private readonly ExcelDataLoader _excelLoader;
        private readonly CsvDataLoader _csvLoader;
        private readonly ExcelDataExporter _exporter;

        public BatchProcessor()
        {
            _dataProcessor = new LIVDataProcessor();
            _excelLoader = new ExcelDataLoader();
            _csvLoader = new CsvDataLoader();
            _exporter = new ExcelDataExporter();
        }

        /// <summary>
        /// 批量处理文件夹中的所有数据文件
        /// </summary>
        /// <param name="folderPath">文件夹路径</param>
        /// <param name="i1Current">I1电流值</param>
        /// <param name="i2Current">I2电流值</param>
        /// <param name="progress">进度报告</param>
        /// <returns>处理结果</returns>
        public async Task<BatchProcessResult> ProcessBatchAsync(
            string folderPath, 
            double i1Current, 
            double i2Current, 
            IProgress<BatchProgressInfo>? progress = null)
        {
            try
            {
                LoggingService.LogInformation($"开始批量处理文件夹: {folderPath}");
                
                var result = new BatchProcessResult();
                var progressInfo = new BatchProgressInfo();

                // 搜索所有支持的文件
                var supportedFiles = GetSupportedFiles(folderPath);
                progressInfo.TotalFiles = supportedFiles.Count;
                progressInfo.Status = "扫描文件完成，开始处理...";
                progress?.Report(progressInfo);

                if (!supportedFiles.Any())
                {
                    result.ErrorMessage = "未找到支持的数据文件（Excel或CSV格式）";
                    return result;
                }

                var processedFiles = new List<FileDataModel>();
                var failedFiles = new List<string>();

                // 处理每个文件
                for (int i = 0; i < supportedFiles.Count; i++)
                {
                    var filePath = supportedFiles[i];
                    progressInfo.CurrentFile = i + 1;
                    progressInfo.CurrentFileName = Path.GetFileName(filePath);
                    progressInfo.Status = $"正在处理: {progressInfo.CurrentFileName}";
                    progress?.Report(progressInfo);

                    try
                    {
                        var fileViewModel = await ProcessSingleFileAsync(filePath);
                        if (fileViewModel != null)
                        {
                            // 计算I1和I2处的功率值
                            if (fileViewModel.Data.Parameters != null)
                            {
                                var i1Power = CalculatePowerAtCurrent(fileViewModel.Data, i1Current);
                                var i2Power = CalculatePowerAtCurrent(fileViewModel.Data, i2Current);
                                
                                fileViewModel.Data.Parameters.I1Current = i1Current;
                                fileViewModel.Data.Parameters.I1Power = i1Power;
                                fileViewModel.Data.Parameters.I2Current = i2Current;
                                fileViewModel.Data.Parameters.I2Power = i2Power;
                            }

                            processedFiles.Add(fileViewModel);
                            result.ProcessedFiles++;
                        }
                        else
                        {
                            failedFiles.Add(filePath);
                            result.FailedFiles++;
                        }
                    }
                    catch (Exception ex)
                    {
                        LoggingService.LogError(ex, $"处理文件失败: {filePath}");
                        failedFiles.Add(filePath);
                        result.FailedFiles++;
                    }
                }

                // 生成输出文件路径
                var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                var outputFileName = $"批量处理结果_{timestamp}.xlsx";
                var outputPath = Path.Combine(folderPath, outputFileName);

                // 导出结果
                if (processedFiles.Any())
                {
                    progressInfo.Status = "正在生成Excel报告...";
                    progress?.Report(progressInfo);

                    await _exporter.ExportDataAsync(processedFiles, outputPath);
                    result.OutputFilePath = outputPath;
                    result.IsSuccess = true;

                    LoggingService.LogInformation($"批量处理完成，结果已保存到: {outputPath}");
                }

                // 设置结果详情
                result.ProcessedFileNames = processedFiles.Select(f => f.FileName).ToList();
                result.FailedFileNames = failedFiles.Select(Path.GetFileName).ToList();
                result.TotalFiles = supportedFiles.Count;

                progressInfo.Status = "批量处理完成";
                progress?.Report(progressInfo);

                return result;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "批量处理过程中发生错误");
                return new BatchProcessResult
                {
                    IsSuccess = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        /// <summary>
        /// 处理单个文件
        /// </summary>
        private async Task<FileDataModel?> ProcessSingleFileAsync(string filePath)
        {
            try
            {
                var extension = Path.GetExtension(filePath).ToLower();
                LIVMeasurementData? data = null;

                // 根据文件类型选择加载器
                if (extension == ".csv")
                {
                    var validationResult = _csvLoader.ValidateFile(filePath);
                    if (!validationResult.IsValid)
                    {
                        LoggingService.LogWarning($"CSV文件验证失败: {filePath} - {validationResult.ErrorMessage}");
                        return null;
                    }
                    data = await _csvLoader.LoadCsvDataAsync(filePath);
                }
                else if (extension == ".xlsx" || extension == ".xls")
                {
                    var validationResult = _excelLoader.ValidateFile(filePath);
                    if (!validationResult.IsValid)
                    {
                        LoggingService.LogWarning($"Excel文件验证失败: {filePath} - {validationResult.ErrorMessage}");
                        return null;
                    }
                    data = await _excelLoader.LoadExcelDataAsync(filePath);
                }

                if (data == null || !HasValidData(data))
                {
                    LoggingService.LogWarning($"文件无有效数据: {filePath}");
                    return null;
                }

                // 清除缓存以确保每个文件独立计算
                _dataProcessor.ClearCache();
                
                // 计算参数
                var parameters = _dataProcessor.CalculateParameters(data);
                data.Parameters = parameters;
                data.IsProcessed = true;
                
                // 计算发散角参数（如果有发散角数据）
                if ((data.HorizontalDivergenceData != null && data.HorizontalDivergenceData.Any()) ||
                    (data.VerticalDivergenceData != null && data.VerticalDivergenceData.Any()))
                {
                    var divergenceResults = _dataProcessor.CalculateDivergenceParameters(data);
                    data.DivergenceResults = divergenceResults;
                }

                return new FileDataModel(data);
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"处理单个文件失败: {filePath}");
                return null;
            }
        }

        /// <summary>
        /// 获取文件夹中所有支持的文件
        /// </summary>
        private List<string> GetSupportedFiles(string folderPath)
        {
            var supportedExtensions = new[] { ".xlsx", ".xls", ".csv" };
            var files = new List<string>();

            try
            {
                foreach (var extension in supportedExtensions)
                {
                    var pattern = $"*{extension}";
                    files.AddRange(Directory.GetFiles(folderPath, pattern, SearchOption.AllDirectories));
                }

                // 排除临时文件和隐藏文件
                files = files.Where(f => 
                    !Path.GetFileName(f).StartsWith("~") && 
                    !Path.GetFileName(f).StartsWith(".") &&
                    !Path.GetFileName(f).Contains("批量处理结果")).ToList();

                LoggingService.LogInformation($"找到 {files.Count} 个支持的文件");
                return files.OrderBy(f => f).ToList();
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"扫描文件夹失败: {folderPath}");
                return new List<string>();
            }
        }

        /// <summary>
        /// 计算指定电流下的功率值
        /// </summary>
        private double CalculatePowerAtCurrent(LIVMeasurementData data, double targetCurrent)
        {
            if (!data.CurrentPowerData.Any())
                return 0;

            // 找到最接近的电流点
            var closestPoint = data.CurrentPowerData
                .OrderBy(p => Math.Abs(p.X - targetCurrent))
                .FirstOrDefault();

            if (closestPoint != null)
            {
                // 如果电流值非常接近，直接返回功率值
                if (Math.Abs(closestPoint.X - targetCurrent) < 0.001)
                {
                    return closestPoint.Y;
                }

                // 否则进行线性插值
                var leftPoint = data.CurrentPowerData
                    .Where(p => p.X <= targetCurrent)
                    .OrderByDescending(p => p.X)
                    .FirstOrDefault();

                var rightPoint = data.CurrentPowerData
                    .Where(p => p.X >= targetCurrent)
                    .OrderBy(p => p.X)
                    .FirstOrDefault();

                if (leftPoint != null && rightPoint != null && leftPoint.X != rightPoint.X)
                {
                    // 线性插值
                    var ratio = (targetCurrent - leftPoint.X) / (rightPoint.X - leftPoint.X);
                    return leftPoint.Y + ratio * (rightPoint.Y - leftPoint.Y);
                }

                return closestPoint.Y;
            }

            return 0;
        }

        /// <summary>
        /// 检查数据是否有效
        /// </summary>
        private bool HasValidData(LIVMeasurementData data)
        {
            return data.CurrentPowerData.Any() || 
                   data.CurrentVoltageData.Any() || 
                   data.WavelengthIntensityData.Any();
        }
    }

    /// <summary>
    /// 批量处理结果
    /// </summary>
    public class BatchProcessResult
    {
        public bool IsSuccess { get; set; }
        public string? ErrorMessage { get; set; }
        public string? OutputFilePath { get; set; }
        public int TotalFiles { get; set; }
        public int ProcessedFiles { get; set; }
        public int FailedFiles { get; set; }
        public List<string> ProcessedFileNames { get; set; } = new();
        public List<string> FailedFileNames { get; set; } = new();
    }

    /// <summary>
    /// 批量处理进度信息
    /// </summary>
    public class BatchProgressInfo
    {
        public int TotalFiles { get; set; }
        public int CurrentFile { get; set; }
        public string CurrentFileName { get; set; } = "";
        public string Status { get; set; } = "";
        public double ProgressPercentage => TotalFiles > 0 ? (double)CurrentFile / TotalFiles * 100 : 0;
    }
}