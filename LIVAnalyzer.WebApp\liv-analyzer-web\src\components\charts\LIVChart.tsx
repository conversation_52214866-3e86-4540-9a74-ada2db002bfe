import { useEffect, useRef } from 'react';
// 动态导入 Plot 以减少首包
let Plot: any;
import { useAppStore } from '../../state/store';
import { movingAverage, savitzkyGolay, gaussianSmooth, butterworthLowpass, medianFilter, adaptiveSmooth } from '../../services/smoothing';
import ChartToolbar from "../ChartToolbar";

export default function LIVChart() {
  const containerRef = useRef<HTMLDivElement | null>(null);
  const { data, displayConfig } = useAppStore.getState();

  useEffect(() => {
    if (!containerRef.current) return;
    containerRef.current.innerHTML = '';
    if (!Plot) {
      import('@observablehq/plot').then(mod => { Plot = mod; render(); });
      return;
    }
    render();
  }, [data]);

  function render() {
    if (!containerRef.current || !Plot) return;

    const marks: any[] = [];
    if (useAppStore.getState().displayConfig.showGrid ?? true) {
      marks.push(Plot.gridX(), Plot.gridY());
    }

    if (data?.power && data.power.current.length && data.power.power.length) {
      const cfg = useAppStore.getState().processingConfig;
      let pY = data.power.power.slice();
      if (cfg.smoothing?.enabled) {
        if (cfg.smoothing.method === 'moving-average') {
          pY = movingAverage(pY, cfg.smoothing.windowSize ?? 5);
        } else if (cfg.smoothing.method === 'sg') {
          pY = savitzkyGolay(pY, cfg.smoothing.windowSize ?? 5, cfg.smoothing.polynomialOrder ?? 3);
        } else if (cfg.smoothing.method === 'gaussian') {
          pY = gaussianSmooth(pY, cfg.smoothing.windowSize ?? 5, cfg.smoothing.sigma);
        } else if (cfg.smoothing.method === 'butterworth') {
          pY = butterworthLowpass(pY, cfg.smoothing.cutoff ?? 0.15, cfg.smoothing.order);
        } else if (cfg.smoothing.method === 'median') {
          pY = medianFilter(pY, cfg.smoothing.windowSize ?? 5);
        } else if (cfg.smoothing.method === 'adaptive') {
          pY = adaptiveSmooth(pY, cfg.smoothing.windowSize ?? 5, cfg.smoothing.maxWindowSize ?? 15);
        }
      }
      const powerSeries = data.power.current.map((x, i) => ({ x, y: pY[i] }));
      if (displayConfig.showPI ?? true) {
        marks.push(
          Plot.line(powerSeries, { x: 'x', y: 'y', stroke: 'steelblue', title: 'P-I' }),
          ...(displayConfig.showMarkers ?? true ? [
            Plot.dot(powerSeries, { x: 'x', y: 'y', r: 2, fill: 'steelblue', title: (d: any) => `I=${d.x}\nP=${d.y}` })
          ] : [])
        );
      }
      // 拟合线与诊断
      const diag = (window as any).__liv_diag as any;
      // 优先从 store results 读取
      const storeDiag = useAppStore.getState().results?.livFitDiagnostics;
      const d = storeDiag ?? diag;
      if ((displayConfig.showDiagnosticsLIV ?? true) && d?.slope !== undefined && d?.intercept !== undefined) {
        const xMin = Math.min(...data.power.current);
        const xMax = Math.max(...data.power.current);
        const fitLine = [xMin, xMax].map(x => ({ x, y: d.slope * x + d.intercept }));
        marks.push(Plot.line(fitLine, { x: 'x', y: 'y', stroke: 'red' }));
      }
      if ((displayConfig.showDiagnosticsLIV ?? true) && d?.usedIndices && Array.isArray(d.usedIndices)) {
        const used = d.usedIndices.map((i: number) => ({ x: data.power!.current[i], y: data.power!.power[i] }));
        marks.push(Plot.dot(used, { x: 'x', y: 'y', r: 3, fill: 'green' }));
      }
      if ((displayConfig.showDiagnosticsLIV ?? true) && d?.outlierIndices && Array.isArray(d.outlierIndices) && d.outlierIndices.length) {
        const out = d.outlierIndices.map((i: number) => ({ x: data.power!.current[i], y: data.power!.power[i] }));
        marks.push(Plot.dot(out, { x: 'x', y: 'y', r: 3, fill: 'orange' }));
      }
    }

    if (data?.voltage && data.voltage.current.length && data.voltage.voltage.length) {
      const cfg = useAppStore.getState().processingConfig;
      let vY = data.voltage.voltage.slice();
      if (cfg.smoothing?.enabled) {
        if (cfg.smoothing.method === 'moving-average') vY = movingAverage(vY, cfg.smoothing.windowSize ?? 5);
        else if (cfg.smoothing.method === 'sg') vY = savitzkyGolay(vY, cfg.smoothing.windowSize ?? 5, cfg.smoothing.polynomialOrder ?? 3);
        else if (cfg.smoothing.method === 'gaussian') vY = gaussianSmooth(vY, cfg.smoothing.windowSize ?? 5);
        else if (cfg.smoothing.method === 'butterworth') vY = butterworthLowpass(vY, cfg.smoothing.cutoff ?? 0.15);
      }
      const voltageSeries = data.voltage.current.map((x, i) => ({ x, y: vY[i] }));
      if (displayConfig.showVI ?? true) {
        marks.push(
          Plot.line(voltageSeries, { x: 'x', y: 'y', stroke: 'tomato', title: 'V-I' }),
          ...(displayConfig.showMarkers ?? true ? [
            Plot.dot(voltageSeries, { x: 'x', y: 'y', r: 2, fill: 'tomato', title: (d: any) => `I=${d.x}\nV=${d.y}` })
          ] : [])
        );
      }
    }

    if (data?.voltage && data.voltage.current.length && data.voltage.voltage.length) {
      const voltageSeries = data.voltage.current.map((x, i) => ({ x, y: data.voltage!.voltage[i] }));
      marks.push(
        Plot.line(voltageSeries, { x: 'x', y: 'y', stroke: 'tomato', title: 'V-I' }),
        Plot.dot(voltageSeries, { x: 'x', y: 'y', r: 2, fill: 'tomato' })
      );
    }

    if (marks.length === 0) {
      containerRef.current.textContent = '暂无 LIV 数据';
      return;
    }

    const plot = Plot.plot({
      marginLeft: 50,
      marginBottom: 40,
      style: { background: 'transparent' },
      x: { label: 'Current (A)' },
      y: { label: 'Value' },
      marks,
    });

    containerRef.current.appendChild(plot);
    import('../../services/zoom').then(z => { (window as any).__liv_zoom = z.enableZoomPan(containerRef.current!); });
    return () => {
      plot.remove();
    };
  }

  return (
    <div className="space-y-2" data-chart="liv">
      <ChartToolbar title="LIV_chart" container={containerRef.current} onReset={() => (window as any).__liv_zoom?.then((r: any) => r.reset())} />
      <div ref={containerRef} className="w-full overflow-auto" />
    </div>
  );
}


