# LIV Analyzer .NET 9 升级指南

## 概述

本指南详细说明如何将 LIV Analyzer 从 .NET 6 升级到 .NET 9。升级后将获得性能提升、安全增强和新功能。

## 前置条件

### 1. 环境准备

**必须安装 .NET 9 SDK：**
```bash
# 检查当前安装的 SDK 版本
dotnet --list-sdks

# 如果没有 .NET 9，请访问以下链接下载：
# https://dotnet.microsoft.com/download/dotnet/9.0
```

**Visual Studio 要求：**
- Visual Studio 2022 17.12 或更高版本（完整 .NET 9 支持）
- Visual Studio 2022 17.11（部分支持，需要特殊配置）

### 2. 创建备份

在开始升级前，强烈建议创建备份分支：
```bash
git checkout -b upgrade-dotnet9
git add .
git commit -m "备份：升级到 .NET 9 前的版本"
```

## 升级步骤

### 第一阶段：项目文件升级

#### 1. 更新目标框架

**LIVAnalyzer.Models.csproj：**
```xml
<TargetFramework>net9.0</TargetFramework>
```

**LIVAnalyzer.Data.csproj：**
```xml
<TargetFramework>net9.0</TargetFramework>
```

**LIVAnalyzer.Core.csproj：**
```xml
<TargetFramework>net9.0</TargetFramework>
```

**LIVAnalyzer.Services.csproj：**
```xml
<TargetFramework>net9.0</TargetFramework>
```

**LIVAnalyzer.Tests.csproj：**
```xml
<TargetFramework>net9.0</TargetFramework>
```

**LIVAnalyzer.UI.csproj：**
```xml
<TargetFramework>net9.0-windows</TargetFramework>
```

### 第二阶段：NuGet 包升级

#### 2. 更新关键依赖包

**LIVAnalyzer.Services.csproj 中的 Microsoft.Extensions 包：**
```xml
<PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.0" />
<PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.0" />
```

**LIVAnalyzer.Tests.csproj 中的测试包：**
```xml
<PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.11.1" />
```

#### 3. 验证兼容性包

大部分现有包已兼容 .NET 9：
- ✅ EPPlus 7.0.0
- ✅ OxyPlot.Wpf 2.1.2
- ✅ CommunityToolkit.Mvvm 8.2.2
- ✅ MathNet.Numerics 5.0.0
- ✅ CsvHelper 30.0.1
- ✅ YamlDotNet 13.7.1
- ✅ Serilog 系列包

**需要注意的包：**
- ⚠️ **ModernWpfUI 0.9.6** - 理论兼容，建议测试验证
- ⚠️ **Accord.Statistics 3.8.0** - 项目处于维护模式，可能需要替换

### 第三阶段：代码适配

#### 4. 检查 BinaryFormatter 使用

.NET 9 中 BinaryFormatter 被移除。检查项目中是否有相关代码：

```bash
# 搜索 BinaryFormatter 使用
grep -r "BinaryFormatter" --include="*.cs" .
grep -r "System.Runtime.Serialization.Formatters.Binary" --include="*.cs" .
```

如果发现使用，需要替换为：
- `System.Text.Json`
- `Newtonsoft.Json`
- `MessagePack`

#### 5. WPF 新特性利用

.NET 9 WPF 新增功能：

**Fluent 主题支持：**
```xml
<!-- 在 App.xaml 中启用 Fluent 主题 -->
<Application.Resources>
    <ResourceDictionary>
        <ResourceDictionary.MergedDictionaries>
            <ResourceDictionary Source="pack://application:,,,/Microsoft.WindowsAPICodePack.Shell;component/Interop/CommonFileDialog/CommonFileDialog.xaml"/>
        </ResourceDictionary.MergedDictionaries>
    </ResourceDictionary>
</Application.Resources>
```

**主题模式 API（实验性）：**
```csharp
// 需要抑制 WPF0001 警告
#pragma warning disable WPF0001
Application.Current.ThemeMode = ThemeMode.Dark;
#pragma warning restore WPF0001
```

### 第四阶段：编译和测试

#### 6. 编译验证

```bash
# 恢复包依赖
dotnet restore

# 编译项目
dotnet build --configuration Release

# 如果出现错误，检查错误信息并相应调整
```

#### 7. 运行测试

```bash
# 运行单元测试
dotnet test

# 运行特定测试项目
dotnet test LIVAnalyzer.Tests/
```

#### 8. 功能测试

- 启动应用程序：`dotnet run --project LIVAnalyzer.UI/`
- 测试核心功能：
  - Excel 文件加载
  - 数据处理和分析
  - 图表显示
  - 导出功能

### 第五阶段：发布配置

#### 9. 更新发布脚本

`BuildAndPublish.bat` 中的发布命令无需修改，已经兼容 .NET 9：

```batch
dotnet publish LIVAnalyzer.UI\LIVAnalyzer.UI.csproj ^
    --configuration Release ^
    --runtime win-x64 ^
    --self-contained true ^
    --output "publish-release" ^
    --property:PublishSingleFile=true ^
    --property:IncludeNativeLibrariesForSelfExtract=true ^
    --property:PublishTrimmed=false ^
    --property:PublishReadyToRun=true
```

## 潜在问题和解决方案

### 1. Accord.Statistics 兼容性问题

如果 Accord.Statistics 3.8.0 出现兼容性问题，考虑以下替代方案：

**选项 1：使用 MathNet.Numerics 替代**
```xml
<!-- 移除 Accord.Statistics -->
<!-- <PackageReference Include="Accord.Statistics" Version="3.8.0" /> -->

<!-- 使用 MathNet.Numerics.Statistics -->
<PackageReference Include="MathNet.Numerics" Version="5.0.0" />
```

**选项 2：保持现状并充分测试**
- 在测试环境中验证所有统计计算功能
- 确保结果准确性不受影响

### 2. ModernWpfUI 样式问题

如果遇到 UI 样式问题：

**解决方案 1：更新到最新版本**
```xml
<PackageReference Include="ModernWpfUI" Version="0.9.6" />
<!-- 检查是否有更新版本 -->
```

**解决方案 2：利用 .NET 9 原生 Fluent 主题**
- 逐步迁移到 .NET 9 内置主题系统
- 减少对第三方 UI 库的依赖

### 3. 性能优化建议

.NET 9 性能改进：
- **GC 优化**：更好的垃圾回收性能
- **JIT 编译**：更快的启动时间
- **内存管理**：更高效的内存使用

可以在升级后进行性能基准测试：
```csharp
// 使用 BenchmarkDotNet 进行性能测试
[Benchmark]
public void ProcessLargeDataset()
{
    // 测试数据处理性能
}
```

## 验证清单

升级完成后，请验证以下功能：

- [ ] 应用程序正常启动
- [ ] Excel 文件加载功能正常
- [ ] 数据处理算法结果正确
- [ ] 图表显示无异常
- [ ] 导出功能工作正常
- [ ] UI 样式显示正确
- [ ] 性能未出现退化
- [ ] 内存使用正常

## 回退计划

如果升级过程中遇到不可解决的问题：

```bash
# 回退到原始版本
git checkout main
git branch -D upgrade-dotnet9

# 或者回退到 .NET 8（中间方案）
# 将所有 net9.0 改为 net8.0
# 这需要 .NET 8 SDK 支持
```

## 分阶段升级建议

如果直接升级到 .NET 9 遇到困难，建议分阶段升级：

1. **第一阶段**：升级到 .NET 8 LTS
2. **第二阶段**：在 .NET 8 稳定后升级到 .NET 9

这样可以降低升级风险，确保每个阶段都充分测试。

## 参考资源

- [.NET 9 官方文档](https://learn.microsoft.com/en-us/dotnet/core/whats-new/dotnet-9/)
- [.NET 9 突破性变更](https://learn.microsoft.com/en-us/dotnet/core/compatibility/9.0)
- [WPF .NET 9 新功能](https://learn.microsoft.com/en-us/dotnet/desktop/wpf/whats-new/net90)
- [迁移指南](https://learn.microsoft.com/en-us/dotnet/core/porting/)

## 总结

.NET 9 升级将为 LIV Analyzer 带来：
- **性能提升**：更快的启动时间和运行速度
- **安全增强**：最新的安全补丁和修复
- **新功能**：WPF Fluent 主题等现代化特性
- **长期支持**：保持技术栈的现代性

建议在非生产环境中充分测试后再部署到生产环境。