using LIVAnalyzer.Models;

namespace LIVAnalyzer.Data.Interfaces
{
    /// <summary>
    /// 文件数据加载接口
    /// </summary>
    public interface IDataLoader
    {
        /// <summary>
        /// 加载Excel文件数据
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>测量数据</returns>
        Task<LIVMeasurementData?> LoadExcelDataAsync(string filePath);
        
        /// <summary>
        /// 加载CSV文件数据
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>测量数据</returns>
        Task<LIVMeasurementData?> LoadCsvDataAsync(string filePath);
        
        /// <summary>
        /// 验证文件格式
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>验证结果</returns>
        (bool IsValid, string ErrorMessage) ValidateFile(string filePath);
    }
}