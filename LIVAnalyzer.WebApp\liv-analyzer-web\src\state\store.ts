import { create } from 'zustand';
import { loadDisplayConfig, loadProcessingConfig, saveDisplayConfig, saveProcessingConfig } from '../services/persist';
import type {
  LIVData,
  ProcessingResult,
  ProcessingConfig,
  DisplayConfig,
} from '../types/data';

export interface AppState {
  data: LIVData | null;
  results: ProcessingResult | null;
  currentTab: 'LIV' | 'Spectrum' | 'Divergence' | 'Efficiency';
  theme: 'light' | 'dark';
  sidebarWidth: number;
  isProcessing: boolean;
  progress: number;
  error: string | null;
  processingConfig: ProcessingConfig;
  displayConfig: DisplayConfig;

  setData: (data: LIVData | null) => void;
  setResults: (r: ProcessingResult | null) => void;
  setCurrentTab: (tab: AppState['currentTab']) => void;
  setTheme: (t: AppState['theme']) => void;
  setSidebarWidth: (w: number) => void;
  setProcessing: (v: boolean) => void;
  setProgress: (p: number) => void;
  setError: (e: string | null) => void;
  updateProcessingConfig: (cfg: Partial<ProcessingConfig>) => void;
  updateDisplayConfig: (cfg: Partial<DisplayConfig>) => void;
  reset: () => void;
}

const initialProcessing = loadProcessingConfig() ?? { thresholdDetection: 'linear', fittingPoints: 150, smoothing: { enabled: false, method: 'moving-average', windowSize: 5 } };
const initialDisplay = loadDisplayConfig() ?? { showGrid: true, showLegend: true, showMarkers: true, showCoords: true, theme: 'light' };
const initialSidebarWidth = (() => { try { const s = localStorage.getItem('liv_sidebar_width_px'); return s ? Number(s) : 350; } catch { return 350; } })();

export const useAppStore = create<AppState>((set) => ({
  data: null,
  results: null,
  currentTab: 'LIV',
  theme: 'light',
  sidebarWidth: initialSidebarWidth,
  isProcessing: false,
  progress: 0,
  error: null,
  processingConfig: initialProcessing,
  displayConfig: initialDisplay,

  setData: (data) => set({ data }),
  setResults: (results) => set({ results }),
  setCurrentTab: (currentTab) => set({ currentTab }),
  setTheme: (theme) => set({ theme, displayConfig: { ...useAppStore.getState().displayConfig, theme } }),
  setSidebarWidth: (sidebarWidth) => {
    try { localStorage.setItem('liv_sidebar_width_px', String(sidebarWidth)); } catch {}
    set({ sidebarWidth });
  },
  setProcessing: (isProcessing) => set({ isProcessing }),
  setProgress: (progress) => set({ progress }),
  setError: (error) => set({ error }),
  updateProcessingConfig: (cfg) => set((s) => { const next = { ...s.processingConfig, ...cfg }; saveProcessingConfig(next); return { processingConfig: next }; }),
  updateDisplayConfig: (cfg) => set((s) => { const next = { ...s.displayConfig, ...cfg }; saveDisplayConfig(next); return { displayConfig: next }; }),
  reset: () => set({
    data: null,
    results: null,
    currentTab: 'LIV',
    isProcessing: false,
    progress: 0,
    error: null,
  }),
}));


