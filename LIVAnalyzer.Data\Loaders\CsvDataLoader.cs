using LIVAnalyzer.Data.Interfaces;
using LIVAnalyzer.Models;
using CsvHelper;
using System.Globalization;

namespace LIVAnalyzer.Data.Loaders
{
    /// <summary>
    /// CSV文件数据加载器
    /// </summary>
    public class CsvDataLoader : IDataLoader
    {
        public async Task<LIVMeasurementData?> LoadCsvDataAsync(string filePath)
        {
            try
            {
                var fileName = Path.GetFileName(filePath);
                var data = new LIVMeasurementData { FileName = fileName };
                
                // 读取文件内容
                var lines = await File.ReadAllLinesAsync(filePath);
                
                // 查找数据开始行（必须以"Sample No;"开头）
                int dataStart = -1;
                for (int i = 0; i < lines.Length; i++)
                {
                    if (lines[i].StartsWith("Sample No;"))
                    {
                        dataStart = i;
                        break;
                    }
                }
                
                if (dataStart == -1)
                {
                    throw new InvalidOperationException("CSV文件格式错误：找不到'Sample No;'标题行");
                }
                
                // 解析标题行
                var headers = lines[dataStart].Split(';');
                var expectedColumns = new[]
                {
                    "Sample No", "Voltage [V]", "Set current [A]", "Measured current [A]",
                    "Optical Power [W]", "Efficiency [%]", "Derivative [W/A]", 
                    "Power loss [W]", "Spectrum Wavelength [nm]", "Intensity [cts]"
                };
                
                // 验证列是否存在
                foreach (var expectedCol in expectedColumns)
                {
                    if (!headers.Contains(expectedCol))
                    {
                        throw new InvalidOperationException($"CSV文件缺少必需的列：{expectedCol}");
                    }
                }
                
                // 解析数据行
                for (int i = dataStart + 1; i < lines.Length; i++)
                {
                    var line = lines[i].Trim();
                    if (string.IsNullOrEmpty(line)) continue;
                    
                    var values = line.Split(';');
                    if (values.Length < headers.Length) continue;
                    
                    try
                    {
                        // 解析各个数值
                        var setCurrent = ParseDouble(values, headers, "Set current [A]");
                        var voltage = ParseDouble(values, headers, "Voltage [V]");
                        var opticalPower = ParseDouble(values, headers, "Optical Power [W]");
                        var wavelength = ParseDouble(values, headers, "Spectrum Wavelength [nm]");
                        var intensity = ParseDouble(values, headers, "Intensity [cts]");
                        
                        // 添加电流-功率数据（使用设置电流值）
                        if (setCurrent.HasValue && opticalPower.HasValue)
                        {
                            data.CurrentPowerData.Add(new DataPoint(setCurrent.Value, opticalPower.Value));
                        }
                        
                        // 添加电流-电压数据（使用设置电流值）
                        if (setCurrent.HasValue && voltage.HasValue)
                        {
                            data.CurrentVoltageData.Add(new DataPoint(setCurrent.Value, voltage.Value));
                        }
                        
                        // 添加波长-强度数据
                        if (wavelength.HasValue && intensity.HasValue)
                        {
                            data.WavelengthIntensityData.Add(new DataPoint(wavelength.Value, intensity.Value));
                        }
                    }
                    catch (Exception ex)
                    {
                        // 跳过解析失败的行，但记录错误
                        Console.WriteLine($"跳过CSV第{i+1}行，解析失败: {ex.Message}");
                    }
                }
                
                return data;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"加载CSV文件失败: {ex.Message}", ex);
            }
        }
        
        private double? ParseDouble(string[] values, string[] headers, string columnName)
        {
            var index = Array.IndexOf(headers, columnName);
            if (index == -1 || index >= values.Length) return null;
            
            if (double.TryParse(values[index], out var result))
            {
                // 应用Python版本的数据清理规则：负值清零
                if (columnName.Contains("current") || columnName.Contains("Power") || columnName.Contains("Voltage"))
                {
                    return Math.Max(0, result);
                }
                return result;
            }
            return null;
        }
        
        public Task<LIVMeasurementData?> LoadExcelDataAsync(string filePath)
        {
            throw new NotImplementedException("CSV加载器不支持Excel文件");
        }
        
        public (bool IsValid, string ErrorMessage) ValidateFile(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                    return (false, "文件不存在");
                    
                var extension = Path.GetExtension(filePath).ToLower();
                if (extension != ".csv")
                    return (false, "不支持的文件格式，仅支持CSV文件");
                    
                var fileInfo = new FileInfo(filePath);
                if (fileInfo.Length > 500 * 1024 * 1024) // 500MB限制
                    return (false, "文件过大，超过500MB限制");
                    
                // 检查文件是否可读
                using var reader = new StreamReader(filePath);
                var firstLine = reader.ReadLine();
                if (string.IsNullOrEmpty(firstLine))
                    return (false, "CSV文件为空");
                    
                return (true, string.Empty);
            }
            catch (Exception ex)
            {
                return (false, $"文件验证失败: {ex.Message}");
            }
        }
        
        private void ParseCsvData(List<dynamic> records, LIVMeasurementData data)
        {
            foreach (var record in records)
            {
                var recordDict = record as IDictionary<string, object>;
                if (recordDict == null) continue;
                
                // 尝试解析每一行的所有数据
                double? current = null, power = null, voltage = null, wavelength = null, intensity = null;
                
                // 解析电流值
                if (recordDict.ContainsKey("Current") && 
                    double.TryParse(recordDict["Current"]?.ToString(), out var currentValue))
                {
                    current = currentValue;
                }
                
                // 解析功率值
                if (recordDict.ContainsKey("Power") && 
                    double.TryParse(recordDict["Power"]?.ToString(), out var powerValue))
                {
                    power = powerValue;
                }
                
                // 解析电压值
                if (recordDict.ContainsKey("Voltage") && 
                    double.TryParse(recordDict["Voltage"]?.ToString(), out var voltageValue))
                {
                    voltage = voltageValue;
                }
                
                // 解析波长值
                if (recordDict.ContainsKey("Wavelength") && 
                    double.TryParse(recordDict["Wavelength"]?.ToString(), out var wavelengthValue))
                {
                    wavelength = wavelengthValue;
                }
                
                // 解析强度值
                if (recordDict.ContainsKey("Intensity") && 
                    double.TryParse(recordDict["Intensity"]?.ToString(), out var intensityValue))
                {
                    intensity = intensityValue;
                }
                
                // 添加电流-功率数据
                if (current.HasValue && power.HasValue)
                {
                    data.CurrentPowerData.Add(new DataPoint(current.Value, power.Value));
                }
                
                // 添加电流-电压数据
                if (current.HasValue && voltage.HasValue)
                {
                    data.CurrentVoltageData.Add(new DataPoint(current.Value, voltage.Value));
                }
                
                // 添加波长-强度数据
                if (wavelength.HasValue && intensity.HasValue)
                {
                    data.WavelengthIntensityData.Add(new DataPoint(wavelength.Value, intensity.Value));
                }
            }
        }
    }
}