namespace LIVAnalyzer.Models
{
    /// <summary>
    /// 测量数据点模型
    /// </summary>
    public class DataPoint
    {
        public double X { get; set; }
        public double Y { get; set; }
        
        public DataPoint(double x, double y)
        {
            X = x;
            Y = y;
        }
    }
    
    /// <summary>
    /// LIV测量数据模型
    /// </summary>
    public class LIVMeasurementData
    {
        /// <summary>
        /// 文件名
        /// </summary>
        public string FileName { get; set; } = string.Empty;
        
        /// <summary>
        /// 电流-功率数据
        /// </summary>
        public List<DataPoint> CurrentPowerData { get; set; } = new();
        
        /// <summary>
        /// 电流-电压数据
        /// </summary>
        public List<DataPoint> CurrentVoltageData { get; set; } = new();
        
        /// <summary>
        /// 波长-强度数据
        /// </summary>
        public List<DataPoint> WavelengthIntensityData { get; set; } = new();
        
        /// <summary>
        /// 水平发散角数据 (角度-强度)
        /// </summary>
        public List<DataPoint>? HorizontalDivergenceData { get; set; }
        
        /// <summary>
        /// 垂直发散角数据 (角度-强度)
        /// </summary>
        public List<DataPoint>? VerticalDivergenceData { get; set; }
        
        /// <summary>
        /// 是否已计算参数
        /// </summary>
        public bool IsProcessed { get; set; }
        
        /// <summary>
        /// LIV计算参数
        /// </summary>
        public LIVParameters? Parameters { get; set; }
        
        /// <summary>
        /// 发散角计算结果
        /// </summary>
        public DivergenceResults? DivergenceResults { get; set; }
    }
}