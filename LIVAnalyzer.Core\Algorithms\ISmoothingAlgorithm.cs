using System.Collections.Generic;
using LIVAnalyzer.Models;

namespace LIVAnalyzer.Core.Algorithms
{
    /// <summary>
    /// 平滑算法接口
    /// </summary>
    public interface ISmoothingAlgorithm
    {
        /// <summary>
        /// 算法名称
        /// </summary>
        string Name { get; }
        
        /// <summary>
        /// 算法描述
        /// </summary>
        string Description { get; }
        
        /// <summary>
        /// 算法类型
        /// </summary>
        SmoothingAlgorithmType AlgorithmType { get; }
        
        /// <summary>
        /// 是否支持实时预览
        /// </summary>
        bool SupportsRealTimePreview { get; }
        
        /// <summary>
        /// 对数据进行平滑处理
        /// </summary>
        /// <param name="xData">X轴数据</param>
        /// <param name="yData">Y轴数据</param>
        /// <param name="config">平滑配置</param>
        /// <returns>平滑后的Y轴数据</returns>
        double[] Smooth(double[] xData, double[] yData, SmoothingConfig config);
        
        /// <summary>
        /// 验证参数有效性
        /// </summary>
        /// <param name="config">平滑配置</param>
        /// <param name="dataLength">数据长度</param>
        /// <returns>验证结果，null表示通过，字符串表示错误信息</returns>
        string? ValidateParameters(SmoothingConfig config, int dataLength);
        
        /// <summary>
        /// 获取推荐参数
        /// </summary>
        /// <param name="dataLength">数据长度</param>
        /// <param name="noiseLevel">噪声水平估计（0-1）</param>
        /// <returns>推荐的配置参数</returns>
        SmoothingConfig GetRecommendedParameters(int dataLength, double noiseLevel = 0.1);
    }
}