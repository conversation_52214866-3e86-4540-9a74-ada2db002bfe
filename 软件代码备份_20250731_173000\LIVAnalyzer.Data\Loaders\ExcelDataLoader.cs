using LIVAnalyzer.Data.Interfaces;
using LIVAnalyzer.Models;
using OfficeOpenXml;
using System.Data;

namespace LIVAnalyzer.Data.Loaders
{
    /// <summary>
    /// Excel文件数据加载器
    /// </summary>
    public class ExcelDataLoader : IDataLoader
    {
        public ExcelDataLoader()
        {
            // 设置EPPlus许可证模式
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
        }
        
        public async Task<LIVMeasurementData?> LoadExcelDataAsync(string filePath)
        {
            try
            {
                var fileName = Path.GetFileName(filePath);
                var data = new LIVMeasurementData { FileName = fileName };
                
                using var package = new ExcelPackage(new FileInfo(filePath));
                
                // 加载电流-功率数据
                LoadPowerData(package, data);
                
                // 加载电流-电压数据
                LoadVoltageData(package, data);
                
                // 加载波长数据
                LoadWavelengthData(package, data);
                
                // 加载发散角数据（可选）
                LoadDivergenceData(package, data);
                
                return data;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"加载Excel文件失败: {ex.Message}", ex);
            }
        }
        
        public Task<LIVMeasurementData?> LoadCsvDataAsync(string filePath)
        {
            throw new NotImplementedException("Excel加载器不支持CSV文件");
        }
        
        public (bool IsValid, string ErrorMessage) ValidateFile(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                    return (false, "文件不存在");
                    
                var extension = Path.GetExtension(filePath).ToLower();
                if (extension != ".xlsx" && extension != ".xls")
                    return (false, "不支持的文件格式，仅支持Excel文件");
                    
                var fileInfo = new FileInfo(filePath);
                if (fileInfo.Length > 500 * 1024 * 1024) // 500MB限制
                    return (false, "文件过大，超过500MB限制");
                    
                // 尝试打开文件验证格式
                using var package = new ExcelPackage(fileInfo);
                if (package.Workbook.Worksheets.Count == 0)
                    return (false, "Excel文件没有工作表");
                    
                return (true, string.Empty);
            }
            catch (Exception ex)
            {
                return (false, $"文件验证失败: {ex.Message}");
            }
        }
        
        private void LoadPowerData(ExcelPackage package, LIVMeasurementData data)
        {
            var worksheet = package.Workbook.Worksheets["power"];
            if (worksheet == null) 
            {
                throw new InvalidOperationException("Excel文件缺少必需的'power'工作表");
            }
            
            var rowCount = worksheet.Dimension?.Rows ?? 0;
            if (rowCount < 2) return; // 至少需要标题行和一行数据
            
            // 查找列索引
            int currentCol = -1, powerCol = -1;
            for (int col = 1; col <= worksheet.Dimension?.Columns; col++)
            {
                var header = worksheet.Cells[1, col].Text?.Trim();
                if (string.Equals(header, "Current", StringComparison.OrdinalIgnoreCase))
                    currentCol = col;
                else if (string.Equals(header, "Power", StringComparison.OrdinalIgnoreCase))
                    powerCol = col;
            }
            
            if (currentCol == -1 || powerCol == -1)
            {
                throw new InvalidOperationException("power工作表缺少必需的列：Current, Power");
            }
            
            for (int row = 2; row <= rowCount; row++)
            {
                try
                {
                    var current = worksheet.Cells[row, currentCol].GetValue<double>();
                    var power = worksheet.Cells[row, powerCol].GetValue<double>();
                    
                    // 应用Python版本的数据清理规则：负值清零
                    current = Math.Max(0, current);
                    power = Math.Max(0, power);
                    
                    data.CurrentPowerData.Add(new DataPoint(current, power));
                }
                catch
                {
                    // 跳过无法解析的行
                    continue;
                }
            }
        }
        
        private void LoadVoltageData(ExcelPackage package, LIVMeasurementData data)
        {
            var worksheet = package.Workbook.Worksheets["voltage"];
            if (worksheet == null) 
            {
                throw new InvalidOperationException("Excel文件缺少必需的'voltage'工作表");
            }
            
            var rowCount = worksheet.Dimension?.Rows ?? 0;
            if (rowCount < 2) return; // 至少需要标题行和一行数据
            
            // 查找列索引
            int currentCol = -1, voltageCol = -1;
            for (int col = 1; col <= worksheet.Dimension?.Columns; col++)
            {
                var header = worksheet.Cells[1, col].Text?.Trim();
                if (string.Equals(header, "Current", StringComparison.OrdinalIgnoreCase))
                    currentCol = col;
                else if (string.Equals(header, "Voltage", StringComparison.OrdinalIgnoreCase))
                    voltageCol = col;
            }
            
            if (currentCol == -1 || voltageCol == -1)
            {
                throw new InvalidOperationException("voltage工作表缺少必需的列：Current, Voltage");
            }
            
            for (int row = 2; row <= rowCount; row++)
            {
                try
                {
                    var current = worksheet.Cells[row, currentCol].GetValue<double>();
                    var voltage = worksheet.Cells[row, voltageCol].GetValue<double>();
                    
                    // 应用Python版本的数据清理规则：负值清零
                    current = Math.Max(0, current);
                    voltage = Math.Max(0, voltage);
                    
                    data.CurrentVoltageData.Add(new DataPoint(current, voltage));
                }
                catch
                {
                    // 跳过无法解析的行
                    continue;
                }
            }
        }
        
        private void LoadWavelengthData(ExcelPackage package, LIVMeasurementData data)
        {
            var worksheet = package.Workbook.Worksheets["wavelength"];
            if (worksheet == null) 
            {
                // 波长数据是可选的，不抛出异常
                return;
            }
            
            var rowCount = worksheet.Dimension?.Rows ?? 0;
            if (rowCount < 2) return; // 至少需要标题行和一行数据
            
            // 查找列索引
            int wavelengthCol = -1, intensityCol = -1;
            for (int col = 1; col <= worksheet.Dimension?.Columns; col++)
            {
                var header = worksheet.Cells[1, col].Text?.Trim();
                if (string.Equals(header, "Wavelength", StringComparison.OrdinalIgnoreCase))
                    wavelengthCol = col;
                else if (string.Equals(header, "Intensity", StringComparison.OrdinalIgnoreCase))
                    intensityCol = col;
            }
            
            if (wavelengthCol == -1 || intensityCol == -1)
            {
                // 波长数据可选，记录警告但不抛出异常
                Console.WriteLine("警告：wavelength工作表缺少必需的列：Wavelength, Intensity");
                return;
            }
            
            for (int row = 2; row <= rowCount; row++)
            {
                try
                {
                    var wavelength = worksheet.Cells[row, wavelengthCol].GetValue<double>();
                    var intensity = worksheet.Cells[row, intensityCol].GetValue<double>();
                    
                    data.WavelengthIntensityData.Add(new DataPoint(wavelength, intensity));
                }
                catch
                {
                    // 跳过无法解析的行
                    continue;
                }
            }
        }
        
        private void LoadDivergenceData(ExcelPackage package, LIVMeasurementData data)
        {
            // 加载水平发散角数据
            var hffWorksheet = package.Workbook.Worksheets["HFF"];
            if (hffWorksheet != null)
            {
                data.HorizontalDivergenceData = new List<DataPoint>();
                var rowCount = hffWorksheet.Dimension?.Rows ?? 0;
                
                // 查找列索引
                int angleCol = -1, photocurrentCol = -1;
                for (int col = 1; col <= hffWorksheet.Dimension?.Columns; col++)
                {
                    var header = hffWorksheet.Cells[1, col].Text?.Trim();
                    if (string.Equals(header, "Angle", StringComparison.OrdinalIgnoreCase))
                        angleCol = col;
                    else if (string.Equals(header, "Photocurrent", StringComparison.OrdinalIgnoreCase))
                        photocurrentCol = col;
                }
                
                if (angleCol != -1 && photocurrentCol != -1)
                {
                    for (int row = 2; row <= rowCount; row++)
                    {
                        try
                        {
                            var angle = hffWorksheet.Cells[row, angleCol].GetValue<double>();
                            var photocurrent = hffWorksheet.Cells[row, photocurrentCol].GetValue<double>();
                            data.HorizontalDivergenceData.Add(new DataPoint(angle, photocurrent));
                        }
                        catch
                        {
                            // 跳过无法解析的行
                            continue;
                        }
                    }
                }
            }
            
            // 加载垂直发散角数据
            var vffWorksheet = package.Workbook.Worksheets["VFF"];
            if (vffWorksheet != null)
            {
                data.VerticalDivergenceData = new List<DataPoint>();
                var rowCount = vffWorksheet.Dimension?.Rows ?? 0;
                
                // 查找列索引
                int angleCol = -1, photocurrentCol = -1;
                for (int col = 1; col <= vffWorksheet.Dimension?.Columns; col++)
                {
                    var header = vffWorksheet.Cells[1, col].Text?.Trim();
                    if (string.Equals(header, "Angle", StringComparison.OrdinalIgnoreCase))
                        angleCol = col;
                    else if (string.Equals(header, "Photocurrent", StringComparison.OrdinalIgnoreCase))
                        photocurrentCol = col;
                }
                
                if (angleCol != -1 && photocurrentCol != -1)
                {
                    for (int row = 2; row <= rowCount; row++)
                    {
                        try
                        {
                            var angle = vffWorksheet.Cells[row, angleCol].GetValue<double>();
                            var photocurrent = vffWorksheet.Cells[row, photocurrentCol].GetValue<double>();
                            data.VerticalDivergenceData.Add(new DataPoint(angle, photocurrent));
                        }
                        catch
                        {
                            // 跳过无法解析的行
                            continue;
                        }
                    }
                }
            }
        }
    }
}