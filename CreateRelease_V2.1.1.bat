@echo off
chcp 65001 >nul
echo ========================================
echo    创建LIV分析工具 v2.1.1 发布包
echo    电压数据导出增强版本
echo ========================================
echo.

cd /d "%~dp0"

echo [1/6] 清理旧的构建文件...
if exist "publish-v2.1.1" rd /s /q "publish-v2.1.1"
if exist "LIVAnalyzer_V2.1.1_Release" rd /s /q "LIVAnalyzer_V2.1.1_Release"

echo [2/6] 构建项目...
dotnet clean
dotnet restore
dotnet build --configuration Release

if %ERRORLEVEL% neq 0 (
    echo 错误：项目构建失败！
    pause
    exit /b 1
)

echo [3/6] 发布自包含应用程序...
dotnet publish LIVAnalyzer.UI -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true -p:IncludeNativeLibrariesForSelfExtract=true -o publish-v2.1.1

if %ERRORLEVEL% neq 0 (
    echo 错误：应用程序发布失败！
    pause
    exit /b 1
)

echo [4/6] 创建发布包目录...
set RELEASE_DIR=LIVAnalyzer_V2.1.1_Release
mkdir "%RELEASE_DIR%"

echo [5/6] 复制文件到发布包...
REM 复制主程序
copy "publish-v2.1.1\LIVAnalyzer.exe" "%RELEASE_DIR%\"

REM 复制配置文件（如果存在）
if exist "publish-v2.1.1\*.config" copy "publish-v2.1.1\*.config" "%RELEASE_DIR%\"

REM 复制文档文件
copy "使用指南.md" "%RELEASE_DIR%\"
if exist "技术文档.md" copy "技术文档.md" "%RELEASE_DIR%\"
if exist "发布说明.md" copy "发布说明.md" "%RELEASE_DIR%\"

REM 创建启动脚本
echo @echo off > "%RELEASE_DIR%\启动LIV分析工具.bat"
echo chcp 65001 ^>nul >> "%RELEASE_DIR%\启动LIV分析工具.bat"
echo echo 正在启动LIV分析工具 v2.1.1... >> "%RELEASE_DIR%\启动LIV分析工具.bat"
echo start "" "LIVAnalyzer.exe" >> "%RELEASE_DIR%\启动LIV分析工具.bat"

REM 创建README文件
echo LIV分析工具 v2.1.1 (电压数据导出增强版本) > "%RELEASE_DIR%\README.txt"
echo. >> "%RELEASE_DIR%\README.txt"
echo v2.1.1 更新内容： >> "%RELEASE_DIR%\README.txt"
echo - 修复Excel导出功能，汇总表中新增I1电压和I2电压列 >> "%RELEASE_DIR%\README.txt"
echo - 完善电压数据导出，确保数据完整性 >> "%RELEASE_DIR%\README.txt"
echo - 提升数据分析的准确性和可用性 >> "%RELEASE_DIR%\README.txt"
echo. >> "%RELEASE_DIR%\README.txt"
echo 主要特性： >> "%RELEASE_DIR%\README.txt"
echo - .NET 9.0框架，性能优异 >> "%RELEASE_DIR%\README.txt"
echo - 原生Fluent Design界面，现代化体验 >> "%RELEASE_DIR%\README.txt"
echo - 完整的LIV参数分析和导出功能 >> "%RELEASE_DIR%\README.txt"
echo - 支持批量处理和数据可视化 >> "%RELEASE_DIR%\README.txt"
echo. >> "%RELEASE_DIR%\README.txt"
echo 系统要求： >> "%RELEASE_DIR%\README.txt"
echo - Windows 10/11 (x64) >> "%RELEASE_DIR%\README.txt"
echo - 8GB+ 内存推荐 >> "%RELEASE_DIR%\README.txt"
echo - DirectX 11+ 显卡（用于Fluent Design效果） >> "%RELEASE_DIR%\README.txt"
echo. >> "%RELEASE_DIR%\README.txt"
echo 使用方法： >> "%RELEASE_DIR%\README.txt"
echo 1. 双击"启动LIV分析工具.bat"运行程序 >> "%RELEASE_DIR%\README.txt"
echo 2. 或直接运行"LIVAnalyzer.exe" >> "%RELEASE_DIR%\README.txt"
echo 3. 查看"使用指南.md"了解详细使用方法 >> "%RELEASE_DIR%\README.txt"
echo. >> "%RELEASE_DIR%\README.txt"
echo 开发者：00106 >> "%RELEASE_DIR%\README.txt"
echo 发布日期：2025年8月5日 >> "%RELEASE_DIR%\README.txt"

echo [6/6] 创建版本信息...
echo LIV分析工具 v2.1.1 (电压数据导出增强版本) > "%RELEASE_DIR%\版本信息.txt"
echo 构建时间：%date% %time% >> "%RELEASE_DIR%\版本信息.txt"
echo 开发者：00106 >> "%RELEASE_DIR%\版本信息.txt"
echo 发布日期：2025年8月5日 >> "%RELEASE_DIR%\版本信息.txt"
echo. >> "%RELEASE_DIR%\版本信息.txt"
echo v2.1.1 更新内容： >> "%RELEASE_DIR%\版本信息.txt"
echo - 修复Excel导出功能中缺少电压数据的问题 >> "%RELEASE_DIR%\版本信息.txt"
echo - 在汇总表中新增I1电压(V)和I2电压(V)列 >> "%RELEASE_DIR%\版本信息.txt"
echo - 确保导出数据的完整性和准确性 >> "%RELEASE_DIR%\版本信息.txt"
echo - 提升用户数据分析体验 >> "%RELEASE_DIR%\版本信息.txt"
echo. >> "%RELEASE_DIR%\版本信息.txt"
echo 主要特性： >> "%RELEASE_DIR%\版本信息.txt"
echo - .NET 9.0框架 >> "%RELEASE_DIR%\版本信息.txt"
echo - 原生Fluent Design界面 >> "%RELEASE_DIR%\版本信息.txt"
echo - 自包含部署，无需安装.NET运行时 >> "%RELEASE_DIR%\版本信息.txt"
echo - 单文件发布，便于分发 >> "%RELEASE_DIR%\版本信息.txt"
echo - 支持Windows 10/11 x64 >> "%RELEASE_DIR%\版本信息.txt"

REM 显示文件信息
echo.
echo ========================================
echo         v2.1.1 发布包创建完成！
echo ========================================
echo.
echo 发布目录：%RELEASE_DIR%
echo.
echo 包含文件：
dir "%RELEASE_DIR%" /b
echo.

REM 显示主程序大小
if exist "%RELEASE_DIR%\LIVAnalyzer.exe" (
    for %%f in ("%RELEASE_DIR%\LIVAnalyzer.exe") do (
        set /a size_mb=%%~zf/1024/1024
        echo 主程序大小：%%~zf 字节 (约 !size_mb! MB)
    )
) else (
    echo 警告：未找到主程序文件！
)

echo.
echo Git标签：v2.1.1 已创建
echo 分支：voltage-export-enhancement-v2.1.1
echo.

REM 询问是否打开发布目录
set /p choice="是否打开发布目录？(Y/N): "
if /i "%choice%"=="Y" (
    explorer "%RELEASE_DIR%"
)

REM 询问是否创建压缩包
set /p choice2="是否创建ZIP压缩包？(Y/N): "
if /i "%choice2%"=="Y" (
    echo 正在创建压缩包...
    powershell -command "Compress-Archive -Path '%RELEASE_DIR%\*' -DestinationPath '%RELEASE_DIR%.zip' -Force"
    if exist "%RELEASE_DIR%.zip" (
        echo 压缩包创建成功：%RELEASE_DIR%.zip
        for %%f in ("%RELEASE_DIR%.zip") do (
            set /a zip_size_mb=%%~zf/1024/1024
            echo 压缩包大小：%%~zf 字节 (约 !zip_size_mb! MB)
        )
    ) else (
        echo 压缩包创建失败！
    )
)

echo.
echo ========================================
echo    LIV分析工具 v2.1.1 发布完成！
echo    电压数据导出增强版本
echo ========================================
echo.
echo 发布包可以分发给用户使用，无需安装.NET运行时。
echo 用户只需解压后运行"启动LIV分析工具.bat"即可。
echo.
echo 本版本主要修复了Excel导出中缺少电压数据的问题，
echo 现在汇总表将包含完整的I1和I2电压信息。
echo.
pause
