{"permissions": {"allow": ["<PERSON><PERSON>(powershell:*)", "Bash(cmd /c:*)", "Bash(find:*)", "Bash(dotnet build:*)", "Bash(dotnet run:*)", "<PERSON><PERSON>(dir:*)", "Bash(claude mcp add:*)", "Bash(dotnet clean:*)", "<PERSON><PERSON>(sed:*)", "Bash(copy:*)", "<PERSON><PERSON>(python:*)", "<PERSON><PERSON>(taskkill:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(dotnet test:*)", "Bash(cmd.exe:*)", "Bash(dotnet restore:*)", "Bash(dotnet publish:*)", "Bash(xcopy:*)", "Bash(cp:*)", "Bash(ls:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(del:*)", "Bash(rm:*)", "Bash(publish-finalLIVAnalyzer.exe)", "Bash(.publish-finalLIVAnalyzer.exe)", "Bash(start publish-finalLIVAnalyzer.exe)", "<PERSON><PERSON>(msbuild:*)", "<PERSON><PERSON>(BuildAndPublish.bat)", "<PERSON><PERSON>(wmic:*)", "Bash(dotnet --list-sdks)", "Bash(wc:*)", "WebFetch(domain:learn.microsoft.com)", "Bash(git checkout:*)", "<PERSON><PERSON>(test:*)", "WebFetch(domain:www.thomasclaudiushuber.com)", "WebFetch(domain:smsagent.blog)", "Bash(grep:*)", "Bash(cd \"E:\\01LaserPackage\\software\\LIV_Analyzer\\CSharp_Version\")", "Bash(git tag:*)"], "deny": []}}