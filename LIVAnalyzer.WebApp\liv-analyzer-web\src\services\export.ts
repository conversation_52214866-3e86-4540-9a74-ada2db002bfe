export function exportSvgFromContainer(container: HTMLElement | null, filename: string) {
  if (!container) return;
  const svg = container.querySelector('svg');
  if (!svg) return;
  const serializer = new XMLSerializer();
  const source = serializer.serializeToString(svg);
  const blob = new Blob([source], { type: 'image/svg+xml;charset=utf-8' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = filename.endsWith('.svg') ? filename : `${filename}.svg`;
  document.body.appendChild(a);
  a.click();
  a.remove();
  URL.revokeObjectURL(url);
}

export async function exportPngFromContainer(container: HTMLElement | null, filename: string, scale = 1) {
  if (!container) return;
  const svg = container.querySelector('svg');
  if (!svg) return;
  const serializer = new XMLSerializer();
  const source = serializer.serializeToString(svg);
  const svgBlob = new Blob([source], { type: 'image/svg+xml;charset=utf-8' });
  const url = URL.createObjectURL(svgBlob);
  const img = new Image();
  const canvas = document.createElement('canvas');
  const rect = svg.getBoundingClientRect();
  canvas.width = Math.max(1, Math.floor(rect.width * scale));
  canvas.height = Math.max(1, Math.floor(rect.height * scale));
  const ctx = canvas.getContext('2d');
  if (!ctx) { URL.revokeObjectURL(url); return; }
  await new Promise<void>((resolve) => {
    img.onload = () => { resolve(); };
    img.src = url;
  });
  ctx.fillStyle = '#ffffff';
  ctx.fillRect(0, 0, canvas.width, canvas.height);
  ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
  URL.revokeObjectURL(url);
  canvas.toBlob((blob) => {
    if (!blob) return;
    const a = document.createElement('a');
    const href = URL.createObjectURL(blob);
    a.href = href;
    a.download = filename.endsWith('.png') ? filename : `${filename}.png`;
    document.body.appendChild(a);
    a.click();
    a.remove();
    URL.revokeObjectURL(href);
  }, 'image/png');
}

export function exportCSV(filename: string, headers: string[], rows: Array<(string | number | null | undefined)[]>) {
  const lines = [headers.join(',')].concat(
    rows.map(r => r.map(v => {
      if (v === null || v === undefined) return '';
      const s = String(v);
      if (s.includes(',') || s.includes('"') || s.includes('\n')) {
        return '"' + s.replace(/"/g, '""') + '"';
      }
      return s;
    }).join(','))
  );
  const blob = new Blob([lines.join('\n')], { type: 'text/csv;charset=utf-8' });
  downloadBlob(blob, filename.endsWith('.csv') ? filename : `${filename}.csv`);
}

/**
 * 导出Excel文件
 */
export async function exportExcel(filename: string, sheets: Array<{
  name: string;
  headers: string[];
  rows: Array<(string | number | null | undefined)[]>;
}>) {
  try {
    // 动态导入xlsx库
    const XLSX = await import('xlsx');

    const workbook = XLSX.utils.book_new();

    sheets.forEach(sheet => {
      // 创建工作表数据
      const wsData = [sheet.headers, ...sheet.rows];
      const worksheet = XLSX.utils.aoa_to_sheet(wsData);

      // 设置列宽
      const colWidths = sheet.headers.map(() => ({ wch: 15 }));
      worksheet['!cols'] = colWidths;

      // 添加工作表到工作簿
      XLSX.utils.book_append_sheet(workbook, worksheet, sheet.name);
    });

    // 生成Excel文件
    const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
    const blob = new Blob([excelBuffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    });

    downloadBlob(blob, filename.endsWith('.xlsx') ? filename : `${filename}.xlsx`);
  } catch (error) {
    console.error('Excel导出失败:', error);
    // 降级到CSV导出
    if (sheets.length > 0) {
      exportCSV(filename.replace('.xlsx', '.csv'), sheets[0].headers, sheets[0].rows);
    }
  }
}

/**
 * 生成PDF报告
 */
export async function exportPDFReport(filename: string, reportData: {
  title: string;
  subtitle?: string;
  metadata?: Record<string, string>;
  charts?: Array<{ title: string; container: HTMLElement }>;
  tables?: Array<{ title: string; headers: string[]; rows: Array<(string | number)[]> }>;
  analysis?: {
    thresholdCurrent?: number;
    slopeEfficiency?: number;
    seriesResistance?: number;
    maxEfficiency?: number;
  };
}) {
  try {
    // 动态导入jsPDF库
    const { jsPDF } = await import('jspdf');
    const doc = new jsPDF();

    let yPosition = 20;
    const pageHeight = doc.internal.pageSize.height;
    const margin = 20;

    // 添加标题
    doc.setFontSize(20);
    doc.text(reportData.title, margin, yPosition);
    yPosition += 15;

    if (reportData.subtitle) {
      doc.setFontSize(14);
      doc.text(reportData.subtitle, margin, yPosition);
      yPosition += 10;
    }

    // 添加元数据
    if (reportData.metadata) {
      doc.setFontSize(10);
      Object.entries(reportData.metadata).forEach(([key, value]) => {
        doc.text(`${key}: ${value}`, margin, yPosition);
        yPosition += 6;
      });
      yPosition += 10;
    }

    // 添加分析结果
    if (reportData.analysis) {
      doc.setFontSize(16);
      doc.text('分析结果', margin, yPosition);
      yPosition += 10;

      doc.setFontSize(12);
      const analysis = reportData.analysis;
      if (analysis.thresholdCurrent !== undefined) {
        doc.text(`阈值电流: ${analysis.thresholdCurrent.toFixed(3)} mA`, margin, yPosition);
        yPosition += 8;
      }
      if (analysis.slopeEfficiency !== undefined) {
        doc.text(`斜率效率: ${analysis.slopeEfficiency.toFixed(3)} W/A`, margin, yPosition);
        yPosition += 8;
      }
      if (analysis.seriesResistance !== undefined) {
        doc.text(`串联电阻: ${analysis.seriesResistance.toFixed(3)} Ω`, margin, yPosition);
        yPosition += 8;
      }
      if (analysis.maxEfficiency !== undefined) {
        doc.text(`最大效率: ${(analysis.maxEfficiency * 100).toFixed(2)}%`, margin, yPosition);
        yPosition += 8;
      }
      yPosition += 10;
    }

    // 添加图表
    if (reportData.charts) {
      for (const chart of reportData.charts) {
        if (yPosition > pageHeight - 100) {
          doc.addPage();
          yPosition = 20;
        }

        doc.setFontSize(14);
        doc.text(chart.title, margin, yPosition);
        yPosition += 10;

        try {
          const canvas = await htmlToCanvas(chart.container);
          const imgData = canvas.toDataURL('image/png');
          const imgWidth = 160;
          const imgHeight = (canvas.height * imgWidth) / canvas.width;

          if (yPosition + imgHeight > pageHeight - margin) {
            doc.addPage();
            yPosition = 20;
          }

          doc.addImage(imgData, 'PNG', margin, yPosition, imgWidth, imgHeight);
          yPosition += imgHeight + 10;
        } catch (error) {
          console.warn('图表添加失败:', error);
          doc.text('图表生成失败', margin, yPosition);
          yPosition += 10;
        }
      }
    }

    // 添加数据表格
    if (reportData.tables) {
      for (const table of reportData.tables) {
        if (yPosition > pageHeight - 50) {
          doc.addPage();
          yPosition = 20;
        }

        doc.setFontSize(14);
        doc.text(table.title, margin, yPosition);
        yPosition += 10;

        // 简单的表格实现
        doc.setFontSize(10);
        const cellHeight = 6;
        const cellWidth = (doc.internal.pageSize.width - 2 * margin) / table.headers.length;

        // 表头
        table.headers.forEach((header, i) => {
          doc.text(header, margin + i * cellWidth, yPosition);
        });
        yPosition += cellHeight;

        // 数据行（限制行数以避免页面溢出）
        const maxRows = Math.min(table.rows.length, Math.floor((pageHeight - yPosition - margin) / cellHeight));
        for (let i = 0; i < maxRows; i++) {
          table.rows[i].forEach((cell, j) => {
            doc.text(String(cell), margin + j * cellWidth, yPosition);
          });
          yPosition += cellHeight;
        }

        if (table.rows.length > maxRows) {
          doc.text(`... 还有 ${table.rows.length - maxRows} 行数据`, margin, yPosition);
          yPosition += cellHeight;
        }

        yPosition += 10;
      }
    }

    // 保存PDF
    doc.save(filename.endsWith('.pdf') ? filename : `${filename}.pdf`);
  } catch (error) {
    console.error('PDF生成失败:', error);
    throw new Error('PDF生成失败，请检查数据格式');
  }
}

/**
 * 批量导出数据
 */
export async function exportBatch(filename: string, data: {
  livData?: { current: number[]; power: number[]; voltage?: number[] };
  spectralData?: { wavelength: number[]; intensity: number[] };
  divergenceData?: {
    hff?: { angle: number[]; intensity: number[] };
    vff?: { angle: number[]; intensity: number[] };
  };
  analysisResults?: {
    thresholdCurrent?: number;
    slopeEfficiency?: number;
    seriesResistance?: number;
    maxEfficiency?: number;
  };
  charts?: Array<{ title: string; container: HTMLElement }>;
}) {
  const sheets: Array<{
    name: string;
    headers: string[];
    rows: Array<(string | number | null | undefined)[]>;
  }> = [];

  // LIV数据表
  if (data.livData) {
    const headers = ['电流 (A)', '功率 (W)'];
    if (data.livData.voltage) {
      headers.push('电压 (V)');
    }

    const rows = data.livData.current.map((current, i) => {
      const row: (string | number)[] = [current, data.livData!.power[i]];
      if (data.livData!.voltage) {
        row.push(data.livData!.voltage[i]);
      }
      return row;
    });

    sheets.push({ name: 'LIV数据', headers, rows });
  }

  // 光谱数据表
  if (data.spectralData) {
    const headers = ['波长 (nm)', '强度'];
    const rows = data.spectralData.wavelength.map((wavelength, i) => [
      wavelength,
      data.spectralData!.intensity[i]
    ]);

    sheets.push({ name: '光谱数据', headers, rows });
  }

  // 发散角数据表
  if (data.divergenceData) {
    if (data.divergenceData.hff) {
      const headers = ['角度 (°)', '强度'];
      const rows = data.divergenceData.hff.angle.map((angle, i) => [
        angle,
        data.divergenceData!.hff!.intensity[i]
      ]);

      sheets.push({ name: '水平发散角', headers, rows });
    }

    if (data.divergenceData.vff) {
      const headers = ['角度 (°)', '强度'];
      const rows = data.divergenceData.vff.angle.map((angle, i) => [
        angle,
        data.divergenceData!.vff!.intensity[i]
      ]);

      sheets.push({ name: '垂直发散角', headers, rows });
    }
  }

  // 分析结果表
  if (data.analysisResults) {
    const headers = ['参数', '数值', '单位'];
    const rows: Array<(string | number)[]> = [];

    if (data.analysisResults.thresholdCurrent !== undefined) {
      rows.push(['阈值电流', data.analysisResults.thresholdCurrent, 'mA']);
    }
    if (data.analysisResults.slopeEfficiency !== undefined) {
      rows.push(['斜率效率', data.analysisResults.slopeEfficiency, 'W/A']);
    }
    if (data.analysisResults.seriesResistance !== undefined) {
      rows.push(['串联电阻', data.analysisResults.seriesResistance, 'Ω']);
    }
    if (data.analysisResults.maxEfficiency !== undefined) {
      rows.push(['最大效率', (data.analysisResults.maxEfficiency * 100).toFixed(2), '%']);
    }

    if (rows.length > 0) {
      sheets.push({ name: '分析结果', headers, rows });
    }
  }

  // 导出Excel文件
  await exportExcel(filename, sheets);
}

/**
 * 导出完整报告（包含图表和数据）
 */
export async function exportCompleteReport(filename: string, reportData: {
  title: string;
  subtitle?: string;
  metadata?: Record<string, string>;
  data: {
    livData?: { current: number[]; power: number[]; voltage?: number[] };
    spectralData?: { wavelength: number[]; intensity: number[] };
    divergenceData?: {
      hff?: { angle: number[]; intensity: number[] };
      vff?: { angle: number[]; intensity: number[] };
    };
    analysisResults?: {
      thresholdCurrent?: number;
      slopeEfficiency?: number;
      seriesResistance?: number;
      maxEfficiency?: number;
    };
  };
  charts?: Array<{ title: string; container: HTMLElement }>;
}) {
  try {
    // 生成PDF报告
    await exportPDFReport(`${filename}_report.pdf`, {
      title: reportData.title,
      subtitle: reportData.subtitle,
      metadata: reportData.metadata,
      charts: reportData.charts,
      analysis: reportData.data.analysisResults,
      tables: generateDataTables(reportData.data)
    });

    // 导出Excel数据
    await exportBatch(`${filename}_data.xlsx`, reportData.data);

    console.log('完整报告导出成功');
  } catch (error) {
    console.error('完整报告导出失败:', error);
    throw error;
  }
}

/**
 * 辅助函数：下载Blob
 */
function downloadBlob(blob: Blob, filename: string) {
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = filename;
  document.body.appendChild(a);
  a.click();
  a.remove();
  URL.revokeObjectURL(url);
}

/**
 * 辅助函数：HTML转Canvas
 */
async function htmlToCanvas(element: HTMLElement): Promise<HTMLCanvasElement> {
  try {
    // 尝试使用html2canvas库
    const html2canvas = await import('html2canvas');
    return await html2canvas.default(element, {
      backgroundColor: '#ffffff',
      scale: 2,
      useCORS: true,
      allowTaint: true
    });
  } catch (error) {
    // 降级方案：创建简单的canvas
    const canvas = document.createElement('canvas');
    canvas.width = element.offsetWidth || 800;
    canvas.height = element.offsetHeight || 600;
    const ctx = canvas.getContext('2d');
    if (ctx) {
      ctx.fillStyle = '#ffffff';
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      ctx.fillStyle = '#000000';
      ctx.font = '16px Arial';
      ctx.fillText('图表生成失败', 50, 50);
    }
    return canvas;
  }
}

/**
 * 辅助函数：生成数据表格
 */
function generateDataTables(data: {
  livData?: { current: number[]; power: number[]; voltage?: number[] };
  spectralData?: { wavelength: number[]; intensity: number[] };
  divergenceData?: {
    hff?: { angle: number[]; intensity: number[] };
    vff?: { angle: number[]; intensity: number[] };
  };
  analysisResults?: {
    thresholdCurrent?: number;
    slopeEfficiency?: number;
    seriesResistance?: number;
    maxEfficiency?: number;
  };
}): Array<{ title: string; headers: string[]; rows: Array<(string | number)[]> }> {
  const tables: Array<{ title: string; headers: string[]; rows: Array<(string | number)[]> }> = [];

  // LIV数据表（仅显示前20行）
  if (data.livData) {
    const headers = ['电流 (A)', '功率 (W)'];
    if (data.livData.voltage) {
      headers.push('电压 (V)');
    }

    const maxRows = Math.min(20, data.livData.current.length);
    const rows = data.livData.current.slice(0, maxRows).map((current, i) => {
      const row: (string | number)[] = [
        Number(current.toFixed(6)),
        Number(data.livData!.power[i].toFixed(6))
      ];
      if (data.livData!.voltage) {
        row.push(Number(data.livData!.voltage[i].toFixed(6)));
      }
      return row;
    });

    tables.push({ title: 'LIV数据 (前20行)', headers, rows });
  }

  // 分析结果表
  if (data.analysisResults) {
    const headers = ['参数', '数值', '单位'];
    const rows: Array<(string | number)[]> = [];

    if (data.analysisResults.thresholdCurrent !== undefined) {
      rows.push(['阈值电流', Number(data.analysisResults.thresholdCurrent.toFixed(3)), 'mA']);
    }
    if (data.analysisResults.slopeEfficiency !== undefined) {
      rows.push(['斜率效率', Number(data.analysisResults.slopeEfficiency.toFixed(3)), 'W/A']);
    }
    if (data.analysisResults.seriesResistance !== undefined) {
      rows.push(['串联电阻', Number(data.analysisResults.seriesResistance.toFixed(3)), 'Ω']);
    }
    if (data.analysisResults.maxEfficiency !== undefined) {
      rows.push(['最大效率', Number((data.analysisResults.maxEfficiency * 100).toFixed(2)), '%']);
    }

    if (rows.length > 0) {
      tables.push({ title: '分析结果', headers, rows });
    }
  }

  return tables;
}


