using System;
using System.Windows;
using LIVAnalyzer.Services.Configuration;

namespace LIVAnalyzer.UI.Services
{
    /// <summary>
    /// 原生Fluent主题变更事件参数
    /// </summary>
    public class NativeThemeChangedEventArgs : EventArgs
    {
        public ThemeMode ThemeMode { get; }

        public NativeThemeChangedEventArgs(ThemeMode themeMode)
        {
            ThemeMode = themeMode;
        }
    }

    /// <summary>
    /// .NET 9 原生 Fluent Design 主题服务
    /// 基于官方 PresentationFramework.Fluent 实现
    /// </summary>
    public class NativeFluentThemeService
    {
        private static NativeFluentThemeService? _instance;
        public static NativeFluentThemeService Instance => _instance ??= new NativeFluentThemeService();

        /// <summary>
        /// 主题变更事件
        /// </summary>
        public event EventHandler<NativeThemeChangedEventArgs>? ThemeChanged;

        private NativeFluentThemeService()
        {
            // 加载保存的主题设置
            LoadThemeSettings();
        }

        /// <summary>
        /// 设置应用主题
        /// </summary>
        /// <param name="themeMode">主题模式</param>
        public void SetTheme(ThemeMode themeMode)
        {
            try
            {
                var oldTheme = Application.Current.ThemeMode;
                Application.Current.ThemeMode = themeMode;

                // 更新应用程序资源
                UpdateApplicationResources(themeMode);

                // 保存设置
                SaveThemeSettings(themeMode);

                // 触发主题变更事件
                if (oldTheme != themeMode)
                {
                    ThemeChanged?.Invoke(this, new NativeThemeChangedEventArgs(themeMode));
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"主题切换失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 设置应用主题（字符串版本，兼容旧代码）
        /// </summary>
        /// <param name="theme">主题名称</param>
        public void SetTheme(string theme)
        {
            var themeMode = theme.ToLower() switch
            {
                "light" => ThemeMode.Light,
                "dark" => ThemeMode.Dark,
                "system" => ThemeMode.System,
                _ => ThemeMode.System
            };

            SetTheme(themeMode);
        }

        /// <summary>
        /// 获取当前主题模式
        /// </summary>
        public ThemeMode GetCurrentThemeMode()
        {
            try
            {
                return Application.Current.ThemeMode;
            }
            catch
            {
                return ThemeMode.System;
            }
        }

        /// <summary>
        /// 获取当前主题（字符串版本，兼容旧代码）
        /// </summary>
        public string GetCurrentTheme()
        {
            var currentMode = GetCurrentThemeMode();
            if (currentMode == ThemeMode.Light)
                return "Light";
            else if (currentMode == ThemeMode.Dark)
                return "Dark";
            else
                return "System";
        }

        /// <summary>
        /// 检测系统是否为深色主题
        /// </summary>
        public bool IsSystemDarkTheme()
        {
            try
            {
                using (var key = Microsoft.Win32.Registry.CurrentUser.OpenSubKey(
                    @"Software\Microsoft\Windows\CurrentVersion\Themes\Personalize"))
                {
                    var value = key?.GetValue("AppsUseLightTheme");
                    return value is int intValue && intValue == 0;
                }
            }
            catch
            {
                return true; // 默认深色主题
            }
        }

        /// <summary>
        /// 获取当前实际显示的主题（考虑System模式）
        /// </summary>
        public bool IsCurrentlyDarkMode()
        {
            var currentTheme = GetCurrentTheme();
            if (currentTheme == "Dark")
                return true;
            else if (currentTheme == "Light")
                return false;
            else
                return IsSystemDarkTheme();
        }

        /// <summary>
        /// 更新应用程序资源
        /// </summary>
        private void UpdateApplicationResources(ThemeMode themeMode)
        {
            var isDark = (themeMode == ThemeMode.Dark) ||
                        (themeMode == ThemeMode.System && IsSystemDarkTheme());

            try
            {
                var resources = Application.Current.Resources;

                if (isDark)
                {
                    // 深色主题颜色 - 更深的背景，更好的对比度
                    UpdateBrushResource(resources, "AppBackgroundBrush", System.Windows.Media.Color.FromRgb(24, 24, 24));
                    UpdateBrushResource(resources, "AppForegroundBrush", System.Windows.Media.Color.FromRgb(255, 255, 255));
                    UpdateBrushResource(resources, "AppControlBackgroundBrush", System.Windows.Media.Color.FromRgb(32, 32, 32));
                    UpdateBrushResource(resources, "AppBorderBrush", System.Windows.Media.Color.FromRgb(80, 80, 80));

                    // 图表颜色 - 深色主题优化
                    UpdateBrushResource(resources, "ChartBackgroundBrush", System.Windows.Media.Color.FromRgb(24, 24, 24));
                    UpdateBrushResource(resources, "ChartPlotAreaBrush", System.Windows.Media.Color.FromRgb(32, 32, 32));
                    UpdateBrushResource(resources, "ChartTextBrush", System.Windows.Media.Color.FromRgb(255, 255, 255));
                    UpdateBrushResource(resources, "ChartAxisBrush", System.Windows.Media.Color.FromRgb(255, 255, 255));
                    UpdateBrushResource(resources, "FluentChartTextBrush", System.Windows.Media.Color.FromRgb(255, 255, 255));
                    UpdateBrushResource(resources, "FluentChartAxisBrush", System.Windows.Media.Color.FromRgb(255, 255, 255));
                    UpdateBrushResource(resources, "FluentChartGridBrush", System.Windows.Media.Color.FromRgb(64, 64, 64));
                    UpdateBrushResource(resources, "ChartGridBrush", System.Windows.Media.Color.FromRgb(64, 64, 64));
                    UpdateBrushResource(resources, "ChartMinorGridBrush", System.Windows.Media.Color.FromRgb(48, 48, 48));

                    // 轴颜色定义
                    UpdateBrushResource(resources, "ChartPowerAxisBrush", System.Windows.Media.Color.FromRgb(100, 149, 237)); // 功率轴颜色（蓝色）
                    UpdateBrushResource(resources, "ChartVoltageAxisBrush", System.Windows.Media.Color.FromRgb(220, 20, 60)); // 电压轴颜色（红色）
                    UpdateBrushResource(resources, "ChartEfficiencyAxisBrush", System.Windows.Media.Color.FromRgb(34, 197, 94)); // 效率轴颜色（绿色）
                }
                else
                {
                    // 浅色主题颜色
                    UpdateBrushResource(resources, "AppBackgroundBrush", System.Windows.Media.Color.FromRgb(255, 255, 255));
                    UpdateBrushResource(resources, "AppForegroundBrush", System.Windows.Media.Color.FromRgb(0, 0, 0));
                    UpdateBrushResource(resources, "AppControlBackgroundBrush", System.Windows.Media.Color.FromRgb(243, 243, 243));
                    UpdateBrushResource(resources, "AppBorderBrush", System.Windows.Media.Color.FromRgb(225, 225, 225));

                    // 图表颜色
                    UpdateBrushResource(resources, "ChartBackgroundBrush", System.Windows.Media.Color.FromRgb(255, 255, 255));
                    UpdateBrushResource(resources, "ChartPlotAreaBrush", System.Windows.Media.Color.FromRgb(243, 243, 243));
                    UpdateBrushResource(resources, "ChartTextBrush", System.Windows.Media.Color.FromRgb(0, 0, 0));
                    UpdateBrushResource(resources, "ChartAxisBrush", System.Windows.Media.Color.FromRgb(0, 0, 0));
                    UpdateBrushResource(resources, "FluentChartTextBrush", System.Windows.Media.Color.FromRgb(0, 0, 0));
                    UpdateBrushResource(resources, "FluentChartAxisBrush", System.Windows.Media.Color.FromRgb(0, 0, 0));
                    UpdateBrushResource(resources, "FluentChartGridBrush", System.Windows.Media.Color.FromRgb(225, 225, 225));
                    UpdateBrushResource(resources, "ChartGridBrush", System.Windows.Media.Color.FromRgb(225, 225, 225));
                    UpdateBrushResource(resources, "ChartMinorGridBrush", System.Windows.Media.Color.FromRgb(240, 240, 240));

                    // 轴颜色定义
                    UpdateBrushResource(resources, "ChartPowerAxisBrush", System.Windows.Media.Color.FromRgb(0, 100, 200)); // 功率轴颜色（蓝色）
                    UpdateBrushResource(resources, "ChartVoltageAxisBrush", System.Windows.Media.Color.FromRgb(200, 0, 0)); // 电压轴颜色（红色）
                    UpdateBrushResource(resources, "ChartEfficiencyAxisBrush", System.Windows.Media.Color.FromRgb(0, 150, 0)); // 效率轴颜色（绿色）
                }

                // 强制UI刷新
                Application.Current.Dispatcher.BeginInvoke(new Action(() =>
                {
                    foreach (Window window in Application.Current.Windows)
                    {
                        window.InvalidateVisual();
                        window.UpdateLayout();
                    }
                }));
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"更新应用程序资源失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 安全更新画刷资源，保持DynamicResource绑定
        /// </summary>
        private void UpdateBrushResource(System.Windows.ResourceDictionary resources, string key, System.Windows.Media.Color color)
        {
            try
            {
                if (resources[key] is System.Windows.Media.SolidColorBrush brush && !brush.IsFrozen)
                {
                    // 更新现有资源的颜色，保持绑定
                    brush.Color = color;
                }
                else
                {
                    // 创建新的资源
                    resources[key] = new System.Windows.Media.SolidColorBrush(color);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"更新资源 {key} 失败: {ex.Message}");
                // 出错时直接替换资源
                resources[key] = new System.Windows.Media.SolidColorBrush(color);
            }
        }

        /// <summary>
        /// 应用主题到窗口
        /// </summary>
        public void ApplyThemeToWindow(Window window)
        {
            // .NET 9 原生 Fluent 主题会自动应用到所有窗口
            // 这里可以添加额外的窗口特定样式
        }

        /// <summary>
        /// 加载主题设置
        /// </summary>
        private void LoadThemeSettings()
        {
            try
            {
                var config = ConfigurationManager.Instance.GetConfig();

                if (Enum.TryParse<ThemeMode>(config.UI.Theme, out var savedMode))
                {
                    Application.Current.ThemeMode = savedMode;
                    UpdateApplicationResources(savedMode);
                }
                else
                {
                    // 默认跟随系统
                    Application.Current.ThemeMode = ThemeMode.System;
                    UpdateApplicationResources(ThemeMode.System);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载主题设置时发生错误: {ex.Message}");
                Application.Current.ThemeMode = ThemeMode.System;
                UpdateApplicationResources(ThemeMode.System);
            }
        }

        /// <summary>
        /// 保存主题设置
        /// </summary>
        private void SaveThemeSettings(ThemeMode themeMode)
        {
            try
            {
                var config = ConfigurationManager.Instance.GetConfig();
                config.UI.Theme = themeMode.ToString();
                ConfigurationManager.Instance.SaveConfiguration();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"保存主题设置时发生错误: {ex.Message}");
            }
        }
    }
}