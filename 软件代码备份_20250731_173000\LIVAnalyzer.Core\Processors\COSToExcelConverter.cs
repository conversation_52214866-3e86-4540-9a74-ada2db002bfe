using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using LIVAnalyzer.Models;
using LIVAnalyzer.Services.Logging;
using OfficeOpenXml;

namespace LIVAnalyzer.Core.Processors
{
    /// <summary>
    /// COS文件到Excel格式转换器
    /// </summary>
    public class COSToExcelConverter
    {
        public COSToExcelConverter()
        {
            // 设置EPPlus许可证上下文为非商业用途
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
        }

        /// <summary>
        /// 加载光谱数据文件 (.cwv1)
        /// </summary>
        /// <param name="spectrumPath">光谱文件路径</param>
        /// <returns>光谱数据或null</returns>
        public List<DataPoint>? LoadSpectrumData(string spectrumPath)
        {
            try
            {
                var lines = File.ReadAllLines(spectrumPath, Encoding.UTF8);
                var wavelengthData = new List<DataPoint>();

                // 跳过第一行（表头），解析数据行
                for (int i = 1; i < lines.Length; i++)
                {
                    var line = lines[i].Trim();
                    if (string.IsNullOrEmpty(line)) continue;

                    var values = line.Split('\t');
                    if (values.Length >= 2)
                    {
                        if (double.TryParse(values[0], out double wavelength) &&
                            double.TryParse(values[1], out double intensity))
                        {
                            wavelengthData.Add(new DataPoint(wavelength, intensity));
                        }
                    }
                }

                if (wavelengthData.Count == 0)
                {
                    LoggingService.LogWarning($"光谱文件 {spectrumPath} 没有找到有效数据");
                    return null;
                }

                LoggingService.LogInformation($"成功加载光谱文件：{spectrumPath}，数据点数：{wavelengthData.Count}");
                return wavelengthData;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"加载光谱文件失败：{spectrumPath}");
                return null;
            }
        }

        /// <summary>
        /// 加载发散角数据文件 (.cxf 或 .cyf)
        /// </summary>
        /// <param name="divergencePath">发散角文件路径</param>
        /// <param name="dataType">数据类型 HFF 或 VFF</param>
        /// <returns>发散角数据或null</returns>
        public List<DataPoint>? LoadDivergenceData(string divergencePath, string dataType)
        {
            try
            {
                var lines = File.ReadAllLines(divergencePath, Encoding.UTF8);
                var divergenceData = new List<DataPoint>();

                // 跳过第一行（表头），解析数据行
                for (int i = 1; i < lines.Length; i++)
                {
                    var line = lines[i].Trim();
                    if (string.IsNullOrEmpty(line)) continue;

                    var values = line.Split('\t');
                    if (values.Length >= 2)
                    {
                        if (double.TryParse(values[0], out double angle) &&
                            double.TryParse(values[1], out double photocurrent))
                        {
                            divergenceData.Add(new DataPoint(angle, photocurrent));
                        }
                    }
                }

                if (divergenceData.Count == 0)
                {
                    LoggingService.LogWarning($"{dataType}发散角文件 {divergencePath} 没有找到有效数据");
                    return null;
                }

                LoggingService.LogInformation($"成功加载{dataType}发散角文件：{divergencePath}，数据点数：{divergenceData.Count}");
                return divergenceData;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"加载{dataType}发散角文件失败：{divergencePath}");
                return null;
            }
        }

        /// <summary>
        /// 转换单个COS文件
        /// </summary>
        /// <param name="txtPath">COS_Test_LIV_Data.txt文件路径</param>
        /// <param name="excelPath">输出Excel文件路径</param>
        /// <returns>转换是否成功</returns>
        public async Task<bool> ConvertSingleFileAsync(string txtPath, string? excelPath = null)
        {
            try
            {
                // 读取COS_Test_LIV_Data.txt文件
                var lines = await File.ReadAllLinesAsync(txtPath, Encoding.UTF8);
                
                if (lines.Length < 2)
                {
                    LoggingService.LogWarning($"文件 {txtPath} 数据不足");
                    return false;
                }

                // 解析数据行
                var livData = new List<(double Current, double Voltage, double Power)>();
                
                for (int i = 1; i < lines.Length; i++)
                {
                    var line = lines[i].Trim();
                    if (string.IsNullOrEmpty(line)) continue;

                    var values = line.Split('\t');
                    if (values.Length >= 3)
                    {
                        if (double.TryParse(values[0], out double current) &&
                            double.TryParse(values[1], out double voltage) &&
                            double.TryParse(values[2], out double power))
                        {
                            // 将负值置为0
                            current = Math.Max(0, current);
                            voltage = Math.Max(0, voltage);
                            power = Math.Max(0, power);
                            
                            livData.Add((current, voltage, power));
                        }
                    }
                }

                if (livData.Count == 0)
                {
                    LoggingService.LogWarning($"文件 {txtPath} 没有找到有效的LIV数据");
                    return false;
                }

                // 查找相关文件
                var dirPath = Path.GetDirectoryName(txtPath)!;
                var spectrumData = FindAndLoadSpectrumData(dirPath);
                var hffData = FindAndLoadHFFData(dirPath);
                var vffData = FindAndLoadVFFData(dirPath);

                // 生成输出文件路径
                if (string.IsNullOrEmpty(excelPath))
                {
                    var sampleId = Path.GetFileName(dirPath);
                    excelPath = Path.Combine(dirPath, $"{sampleId}_converted.xlsx");
                }

                // 创建Excel文件
                using var package = new ExcelPackage();

                // 创建wavelength工作表
                var wavelengthSheet = package.Workbook.Worksheets.Add("wavelength");
                wavelengthSheet.Cells[1, 1].Value = "Wavelength";
                wavelengthSheet.Cells[1, 2].Value = "Intensity";
                
                if (spectrumData != null)
                {
                    for (int i = 0; i < spectrumData.Count; i++)
                    {
                        wavelengthSheet.Cells[i + 2, 1].Value = spectrumData[i].X;
                        wavelengthSheet.Cells[i + 2, 2].Value = spectrumData[i].Y;
                    }
                }

                // 创建power工作表
                var powerSheet = package.Workbook.Worksheets.Add("power");
                powerSheet.Cells[1, 1].Value = "Current";
                powerSheet.Cells[1, 2].Value = "Power";
                
                for (int i = 0; i < livData.Count; i++)
                {
                    powerSheet.Cells[i + 2, 1].Value = livData[i].Current;
                    powerSheet.Cells[i + 2, 2].Value = livData[i].Power;
                }

                // 创建voltage工作表
                var voltageSheet = package.Workbook.Worksheets.Add("voltage");
                voltageSheet.Cells[1, 1].Value = "Current";
                voltageSheet.Cells[1, 2].Value = "Voltage";
                
                for (int i = 0; i < livData.Count; i++)
                {
                    voltageSheet.Cells[i + 2, 1].Value = livData[i].Current;
                    voltageSheet.Cells[i + 2, 2].Value = livData[i].Voltage;
                }

                // 创建HFF工作表（如果有数据）
                if (hffData != null)
                {
                    var hffSheet = package.Workbook.Worksheets.Add("HFF");
                    hffSheet.Cells[1, 1].Value = "Angle";
                    hffSheet.Cells[1, 2].Value = "Photocurrent";
                    
                    for (int i = 0; i < hffData.Count; i++)
                    {
                        hffSheet.Cells[i + 2, 1].Value = hffData[i].X;
                        hffSheet.Cells[i + 2, 2].Value = hffData[i].Y;
                    }
                }

                // 创建VFF工作表（如果有数据）
                if (vffData != null)
                {
                    var vffSheet = package.Workbook.Worksheets.Add("VFF");
                    vffSheet.Cells[1, 1].Value = "Angle";
                    vffSheet.Cells[1, 2].Value = "Photocurrent";
                    
                    for (int i = 0; i < vffData.Count; i++)
                    {
                        vffSheet.Cells[i + 2, 1].Value = vffData[i].X;
                        vffSheet.Cells[i + 2, 2].Value = vffData[i].Y;
                    }
                }

                // 保存Excel文件
                await package.SaveAsAsync(new FileInfo(excelPath));

                LoggingService.LogInformation($"✓ 成功转换: {txtPath} -> {excelPath}");
                if (spectrumData != null)
                    LoggingService.LogInformation($"  包含光谱数据: {spectrumData.Count} 个数据点");
                if (hffData != null)
                    LoggingService.LogInformation($"  包含HFF水平发散角数据: {hffData.Count} 个数据点");
                if (vffData != null)
                    LoggingService.LogInformation($"  包含VFF垂直发散角数据: {vffData.Count} 个数据点");

                return true;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"转换文件失败：{txtPath}");
                return false;
            }
        }

        /// <summary>
        /// 查找并加载光谱数据
        /// </summary>
        private List<DataPoint>? FindAndLoadSpectrumData(string dirPath)
        {
            try
            {
                var cwv1Files = Directory.GetFiles(dirPath, "*.cwv1");
                return cwv1Files.Length > 0 ? LoadSpectrumData(cwv1Files[0]) : null;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 查找并加载HFF数据
        /// </summary>
        private List<DataPoint>? FindAndLoadHFFData(string dirPath)
        {
            try
            {
                var cxfFiles = Directory.GetFiles(dirPath, "*HFF*.cxf");
                return cxfFiles.Length > 0 ? LoadDivergenceData(cxfFiles[0], "HFF") : null;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 查找并加载VFF数据
        /// </summary>
        private List<DataPoint>? FindAndLoadVFFData(string dirPath)
        {
            try
            {
                var cyfFiles = Directory.GetFiles(dirPath, "*VFF*.cyf");
                return cyfFiles.Length > 0 ? LoadDivergenceData(cyfFiles[0], "VFF") : null;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 查找COS文件
        /// </summary>
        /// <param name="rootDir">根目录</param>
        /// <param name="recursive">是否递归搜索</param>
        /// <returns>COS文件列表 (SampleId, FilePath)</returns>
        public List<(string SampleId, string FilePath)> FindCOSFiles(string rootDir, bool recursive = true)
        {
            var cosFiles = new List<(string, string)>();

            try
            {
                if (recursive)
                {
                    var directories = Directory.GetDirectories(rootDir, "*", SearchOption.AllDirectories);
                    foreach (var dir in directories)
                    {
                        var livFile = Path.Combine(dir, "COS_Test_LIV_Data.txt");
                        if (File.Exists(livFile))
                        {
                            var sampleId = Path.GetFileName(dir);
                            cosFiles.Add((sampleId, livFile));
                        }
                    }
                }

                // 也检查根目录
                var rootLivFile = Path.Combine(rootDir, "COS_Test_LIV_Data.txt");
                if (File.Exists(rootLivFile))
                {
                    var sampleId = Path.GetFileName(rootDir);
                    cosFiles.Add((sampleId, rootLivFile));
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"查找COS文件失败：{rootDir}");
            }

            return cosFiles;
        }

        /// <summary>
        /// 批量转换COS文件
        /// </summary>
        /// <param name="sourceDir">源目录</param>
        /// <param name="outputDir">输出目录</param>
        /// <param name="recursive">是否递归搜索</param>
        /// <param name="progress">进度回调</param>
        /// <returns>转换结果</returns>
        public async Task<(int Success, int Failed, List<string> ConvertedFiles)> ConvertBatchFilesAsync(
            string sourceDir, 
            string outputDir, 
            bool recursive = true, 
            IProgress<(int Current, string Message)>? progress = null)
        {
            var cosFiles = FindCOSFiles(sourceDir, recursive);
            var convertedFiles = new List<string>();
            int successCount = 0;
            int failedCount = 0;

            if (cosFiles.Count == 0)
            {
                LoggingService.LogWarning($"在目录 {sourceDir} 中未找到任何COS_Test_LIV_Data.txt文件");
                return (0, 0, convertedFiles);
            }

            // 确保输出目录存在
            Directory.CreateDirectory(outputDir);

            LoggingService.LogInformation($"找到 {cosFiles.Count} 个COS文件，开始批量转换");

            for (int i = 0; i < cosFiles.Count; i++)
            {
                var (sampleId, filePath) = cosFiles[i];
                progress?.Report((i, $"正在转换: {sampleId}"));

                try
                {
                    var outputPath = Path.Combine(outputDir, $"{sampleId}_converted.xlsx");
                    if (await ConvertSingleFileAsync(filePath, outputPath))
                    {
                        successCount++;
                        convertedFiles.Add(outputPath);
                    }
                    else
                    {
                        failedCount++;
                    }
                }
                catch (Exception ex)
                {
                    LoggingService.LogError(ex, $"转换文件失败：{sampleId}");
                    failedCount++;
                }
            }

            progress?.Report((cosFiles.Count, "转换完成"));

            LoggingService.LogInformation($"批量转换完成: 成功 {successCount} 个，失败 {failedCount} 个");
            return (successCount, failedCount, convertedFiles);
        }
    }
}