using Serilog;
using Serilog.Events;

namespace LIVAnalyzer.Services.Logging
{
    /// <summary>
    /// 日志服务
    /// </summary>
    public static class LoggingService
    {
        private static ILogger? _logger;
        
        /// <summary>
        /// 初始化日志系统
        /// </summary>
        public static void Initialize()
        {
            var logDirectory = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                "LIVAnalyzer", "Logs");
            Directory.CreateDirectory(logDirectory);
            
            var logFilePath = Path.Combine(logDirectory, "liv_analyzer.log");
            var errorLogFilePath = Path.Combine(logDirectory, "liv_analyzer_error.log");
            
            _logger = new LoggerConfiguration()
                .MinimumLevel.Debug()
                .MinimumLevel.Override("Microsoft", LogEventLevel.Warning)
                .WriteTo.Console()
                .WriteTo.File(logFilePath,
                    rollingInterval: RollingInterval.Day,
                    retainedFileCountLimit: 7,
                    fileSizeLimitBytes: 10 * 1024 * 1024, // 10MB
                    rollOnFileSizeLimit: true)
                .WriteTo.File(errorLogFilePath,
                    restrictedToMinimumLevel: LogEventLevel.Error,
                    rollingInterval: RollingInterval.Day,
                    retainedFileCountLimit: 30)
                .CreateLogger();
            
            Log.Logger = _logger;
        }
        
        /// <summary>
        /// 记录信息日志
        /// </summary>
        public static void LogInformation(string message, params object[] args)
        {
            _logger?.Information(message, args);
        }
        
        /// <summary>
        /// 记录警告日志
        /// </summary>
        public static void LogWarning(string message, params object[] args)
        {
            _logger?.Warning(message, args);
        }
        
        /// <summary>
        /// 记录错误日志
        /// </summary>
        public static void LogError(string message, params object[] args)
        {
            _logger?.Error(message, args);
        }
        
        /// <summary>
        /// 记录异常日志
        /// </summary>
        public static void LogError(Exception exception, string message, params object[] args)
        {
            _logger?.Error(exception, message, args);
        }
        
        /// <summary>
        /// 记录用户操作日志
        /// </summary>
        public static void LogUserAction(string action, string? details = null)
        {
            var message = string.IsNullOrEmpty(details) ? 
                "用户操作: {Action}" : 
                "用户操作: {Action} - {Details}";
            _logger?.Information(message, action, details);
        }
        
        /// <summary>
        /// 记录数据处理日志
        /// </summary>
        public static void LogDataProcessing(string operation, string fileName, string status)
        {
            _logger?.Information("数据处理: {Operation} - 文件: {FileName} - 状态: {Status}", 
                operation, fileName, status);
        }
        
        /// <summary>
        /// 关闭日志系统
        /// </summary>
        public static void Close()
        {
            Log.CloseAndFlush();
        }
    }
}