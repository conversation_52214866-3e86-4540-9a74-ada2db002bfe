using System;
using System.Collections.Generic;
using System.Globalization;
using System.Text.RegularExpressions;
using System.Windows;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Media;

namespace LIVAnalyzer.UI.Converters
{
    /// <summary>
    /// 简单的Markdown到FlowDocument转换器
    /// </summary>
    public class SimpleMarkdownConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is not string markdown || string.IsNullOrEmpty(markdown))
                return CreateEmptyDocument();

            try
            {
                return ConvertMarkdownToFlowDocument(markdown);
            }
            catch (Exception)
            {
                // 如果转换失败，返回纯文本
                return CreateFallbackDocument(markdown);
            }
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }

        private FlowDocument CreateEmptyDocument()
        {
            return new FlowDocument
            {
                FontFamily = new FontFamily("Microsoft YaHei, 微软雅黑"),
                FontSize = 14,
                PagePadding = new Thickness(20),
                LineHeight = 22
            };
        }

        private FlowDocument CreateFallbackDocument(string text)
        {
            var doc = CreateEmptyDocument();
            doc.Blocks.Add(new Paragraph(new Run(text)));
            return doc;
        }

        private FlowDocument ConvertMarkdownToFlowDocument(string markdown)
        {
            var document = CreateEmptyDocument();
            var lines = markdown.Split('\n');
            Paragraph currentParagraph = null;

            foreach (var line in lines)
            {
                var trimmedLine = line.Trim();
                
                if (string.IsNullOrEmpty(trimmedLine))
                {
                    // 空行，结束当前段落
                    if (currentParagraph != null)
                    {
                        document.Blocks.Add(currentParagraph);
                        currentParagraph = null;
                    }
                    continue;
                }

                // 处理标题
                if (trimmedLine.StartsWith("#"))
                {
                    if (currentParagraph != null)
                    {
                        document.Blocks.Add(currentParagraph);
                        currentParagraph = null;
                    }
                    
                    var heading = CreateHeading(trimmedLine);
                    document.Blocks.Add(heading);
                    continue;
                }

                // 处理列表项
                if (trimmedLine.StartsWith("- ") || trimmedLine.StartsWith("* "))
                {
                    if (currentParagraph != null)
                    {
                        document.Blocks.Add(currentParagraph);
                        currentParagraph = null;
                    }
                    
                    var listItem = CreateListItem(trimmedLine.Substring(2));
                    document.Blocks.Add(listItem);
                    continue;
                }

                // 普通段落
                if (currentParagraph == null)
                {
                    currentParagraph = new Paragraph
                    {
                        Margin = new Thickness(0, 6, 0, 6)
                    };
                }

                // 处理行内格式
                var inlines = ProcessInlineFormatting(trimmedLine);
                foreach (var inline in inlines)
                {
                    currentParagraph.Inlines.Add(inline);
                }
                
                currentParagraph.Inlines.Add(new Run(" "));
            }

            // 添加最后一个段落
            if (currentParagraph != null)
            {
                document.Blocks.Add(currentParagraph);
            }

            return document;
        }

        private Paragraph CreateHeading(string line)
        {
            var level = 0;
            while (level < line.Length && line[level] == '#')
                level++;

            var text = line.Substring(level).Trim();
            var paragraph = new Paragraph(new Run(text));

            // 设置标题样式
            switch (level)
            {
                case 1:
                    paragraph.FontSize = 24;
                    paragraph.FontWeight = FontWeights.Bold;
                    paragraph.Foreground = new SolidColorBrush(Color.FromRgb(0x2C, 0x3E, 0x50));
                    paragraph.Margin = new Thickness(0, 20, 0, 12);
                    break;
                case 2:
                    paragraph.FontSize = 20;
                    paragraph.FontWeight = FontWeights.Bold;
                    paragraph.Foreground = new SolidColorBrush(Color.FromRgb(0x34, 0x49, 0x5E));
                    paragraph.Margin = new Thickness(0, 16, 0, 10);
                    break;
                case 3:
                    paragraph.FontSize = 18;
                    paragraph.FontWeight = FontWeights.SemiBold;
                    paragraph.Foreground = new SolidColorBrush(Color.FromRgb(0x5D, 0x6D, 0x7E));
                    paragraph.Margin = new Thickness(0, 14, 0, 8);
                    break;
                default:
                    paragraph.FontSize = 16;
                    paragraph.FontWeight = FontWeights.SemiBold;
                    paragraph.Foreground = new SolidColorBrush(Color.FromRgb(0x7F, 0x8C, 0x8D));
                    paragraph.Margin = new Thickness(0, 12, 0, 6);
                    break;
            }

            return paragraph;
        }

        private Paragraph CreateListItem(string text)
        {
            var paragraph = new Paragraph
            {
                Margin = new Thickness(20, 4, 0, 4)
            };
            
            paragraph.Inlines.Add(new Run("• ") { FontWeight = FontWeights.Bold });
            
            var inlines = ProcessInlineFormatting(text);
            foreach (var inline in inlines)
            {
                paragraph.Inlines.Add(inline);
            }
            
            return paragraph;
        }

        private List<Inline> ProcessInlineFormatting(string text)
        {
            var inlines = new List<Inline>();
            
            // 简单处理粗体 **text**
            var boldPattern = @"\*\*(.*?)\*\*";
            var parts = Regex.Split(text, boldPattern);
            
            for (int i = 0; i < parts.Length; i++)
            {
                if (i % 2 == 0)
                {
                    // 普通文本
                    if (!string.IsNullOrEmpty(parts[i]))
                    {
                        inlines.Add(new Run(parts[i]));
                    }
                }
                else
                {
                    // 粗体文本
                    inlines.Add(new Run(parts[i]) { FontWeight = FontWeights.Bold });
                }
            }
            
            // 如果没有找到格式，返回原始文本
            if (inlines.Count == 0)
            {
                inlines.Add(new Run(text));
            }
            
            return inlines;
        }
    }
}
