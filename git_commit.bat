@echo off
echo 正在执行Git操作...

echo.
echo 1. 检查Git状态:
git status

echo.
echo 2. 添加修改的文件:
git add LIVAnalyzer.UI/ViewModels/MainWindowViewModel.cs
git add LIVAnalyzer.UI/Views/MainWindow.xaml

echo.
echo 3. 提交修改:
git commit -m "优化显示选项布局和移除十字光标功能

- 移除十字光标功能：删除ShowCrosshair属性和相关方法
- 优化界面布局：线型选择和显示选项改为横向排列  
- 简化文字：显示图例改为图例，显示网格改为网格
- 节省竖向空间，提升界面紧凑性"

echo.
echo 4. 查看最近的提交:
git log --oneline -3

echo.
echo Git操作完成！
pause
