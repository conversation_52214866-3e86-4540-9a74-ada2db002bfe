LIV分析工具 v2.1.1 (电压数据导出增强版本)
构建时间：2025年8月5日
开发者：00106
发布日期：2025年8月5日

v2.1.1 更新内容：
- 修复Excel导出功能中缺少电压数据的问题
- 在汇总表中新增I1电压(V)和I2电压(V)列
- 修复批量处理中I1/I2电压和效率计算缺失的问题
- 修复批量处理中发散角能量占比计算缺失的问题
- 确保批量处理与单文件处理结果完全一致
- 更新帮助菜单中的四个文档内容
- 提升数据导出的完整性和准确性

主要特性：
- .NET 9.0框架
- 现代化设计界面
- 自包含部署，无需安装.NET运行时
- 单文件发布，便于分发
- 支持Windows 10/11 x64

技术改进：
- 新增CalculateVoltageAtCurrentOptimized方法
- 改进发散角能量占比计算算法
- 优化批量处理性能
- 增强数据验证功能

Git信息：
- 提交哈希：ebc2d34
- 标签：v2.1.1
- 分支：master
