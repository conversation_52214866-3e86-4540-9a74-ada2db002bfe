using System;
using Xunit;
using LIVAnalyzer.Core.Configuration;

namespace LIVAnalyzer.Tests.Core
{
    /// <summary>
    /// 阈值计算配置的单元测试
    /// </summary>
    public class ThresholdCalculationConfigTests
    {
        [Fact]
        public void DefaultConfig_ShouldHaveValidValues()
        {
            // Arrange & Act
            var config = ThresholdCalculationConfig.Default;
            
            // Assert
            Assert.Equal(0.5, config.SearchRangeRatio);
            Assert.Equal(0.3, config.MaxThresholdRatio);
            Assert.Equal(0.5, config.SecondaryMaxThresholdRatio);
            Assert.Equal(0.05, config.PrimaryFallbackPowerRatio);
            Assert.Equal(0.01, config.SecondaryFallbackPowerRatio);
            Assert.Equal(0.02, config.TertiaryFallbackPowerRatio);
            Assert.Equal(31, config.MaxSmoothingWindow);
            Assert.Equal(0.2, config.DataWindowRatio);
            Assert.Equal(5, config.MaxDerivativeSmoothingWindow);
            Assert.Equal(0.33, config.DerivativeWindowRatio, 2);
            Assert.Equal(0.5, config.DerivativeRatio);
            Assert.Equal(10, config.MinDataPoints);
            Assert.Equal(1e-10, config.NumericalPrecision);
        }
        
        [Fact]
        public void Validate_WithValidConfig_ShouldNotThrow()
        {
            // Arrange
            var config = new ThresholdCalculationConfig();
            
            // Act & Assert
            config.Validate(); // Should not throw
        }
        
        [Theory]
        [InlineData(-0.1)] // 负值
        [InlineData(0)]    // 零值
        [InlineData(1.1)]  // 超过1
        public void Validate_WithInvalidSearchRangeRatio_ShouldThrow(double invalidValue)
        {
            // Arrange
            var config = new ThresholdCalculationConfig
            {
                SearchRangeRatio = invalidValue
            };
            
            // Act & Assert
            Assert.Throws<ArgumentOutOfRangeException>(() => config.Validate());
        }
        
        [Theory]
        [InlineData(-0.1)]
        [InlineData(0)]
        [InlineData(1.1)]
        public void Validate_WithInvalidMaxThresholdRatio_ShouldThrow(double invalidValue)
        {
            // Arrange
            var config = new ThresholdCalculationConfig
            {
                MaxThresholdRatio = invalidValue
            };
            
            // Act & Assert
            Assert.Throws<ArgumentOutOfRangeException>(() => config.Validate());
        }
        
        [Theory]
        [InlineData(2)]   // 小于3
        [InlineData(4)]   // 偶数
        [InlineData(30)]  // 偶数
        public void Validate_WithInvalidMaxSmoothingWindow_ShouldThrow(int invalidValue)
        {
            // Arrange
            var config = new ThresholdCalculationConfig
            {
                MaxSmoothingWindow = invalidValue
            };
            
            // Act & Assert
            Assert.Throws<ArgumentOutOfRangeException>(() => config.Validate());
        }
        
        [Theory]
        [InlineData(3)]   // 最小有效值
        [InlineData(5)]   // 正常奇数
        [InlineData(31)]  // 默认值
        [InlineData(51)]  // 大奇数
        public void Validate_WithValidMaxSmoothingWindow_ShouldNotThrow(int validValue)
        {
            // Arrange
            var config = new ThresholdCalculationConfig
            {
                MaxSmoothingWindow = validValue
            };
            
            // Act & Assert
            config.Validate(); // Should not throw
        }
        
        [Fact]
        public void Clone_ShouldCreateIdenticalCopy()
        {
            // Arrange
            var original = new ThresholdCalculationConfig
            {
                SearchRangeRatio = 0.6,
                MaxThresholdRatio = 0.4,
                PrimaryFallbackPowerRatio = 0.08,
                MaxSmoothingWindow = 25,
                DerivativeRatio = 0.45
            };
            
            // Act
            var clone = original.Clone();
            
            // Assert
            Assert.NotSame(original, clone);
            Assert.Equal(original.SearchRangeRatio, clone.SearchRangeRatio);
            Assert.Equal(original.MaxThresholdRatio, clone.MaxThresholdRatio);
            Assert.Equal(original.PrimaryFallbackPowerRatio, clone.PrimaryFallbackPowerRatio);
            Assert.Equal(original.MaxSmoothingWindow, clone.MaxSmoothingWindow);
            Assert.Equal(original.DerivativeRatio, clone.DerivativeRatio);
        }
    }
}
