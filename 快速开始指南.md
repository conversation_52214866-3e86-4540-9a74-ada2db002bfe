# 🚀 LIV Analyzer C# 版本 - 快速开始指南

## 📋 前置要求

### 必需软件
1. **Windows 10/11** (x64)
2. **.NET 6 SDK** 
   - 下载地址: https://dotnet.microsoft.com/download/dotnet/6.0
   - ⚠️ 注意：下载"SDK"不是"Runtime"

### 推荐开发工具
- **Visual Studio 2022** (Community/Professional/Enterprise)
- **Visual Studio Code** + C# 扩展
- **JetBrains Rider**

## 🎯 快速启动

### 方法一：使用运行向导（推荐）
1. 双击 `运行向导.bat`
2. 按提示安装.NET SDK（如果需要）
3. 选择 `R` 运行应用程序

### 方法二：命令行操作
```bash
# 1. 打开PowerShell或命令提示符
# 2. 进入项目目录
cd "E:\01LaserPackage\software\LIV_Analyzer\CSharp_Version"

# 3. 还原包
dotnet restore

# 4. 编译
dotnet build

# 5. 运行
dotnet run --project LIVAnalyzer.UI
```

### 方法三：使用Visual Studio
1. 双击 `LIVAnalyzer.sln` 打开解决方案
2. 右键点击 `LIVAnalyzer.UI` 项目
3. 选择"设为启动项目"
4. 按 `F5` 或点击"开始调试"

## 🧪 测试和验证

### 运行单元测试
```bash
dotnet test --verbosity normal
```

### 测试数据文件
您可以使用现有Python版本的测试数据：
- Excel文件：`sample_data/demo_sample.xlsx`
- CSV文件：`GFLLL/` 文件夹中的CSV文件

## 📦 打包发布

### 单文件可执行程序
```bash
dotnet publish LIVAnalyzer.UI -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true
```

### 框架依赖版本（需要.NET Runtime）
```bash
dotnet publish LIVAnalyzer.UI -c Release -r win-x64 --self-contained false
```

## 🔧 开发说明

### 项目架构
```
LIVAnalyzer.Models      # 数据模型
LIVAnalyzer.Data        # 数据访问（Excel/CSV）
LIVAnalyzer.Core        # 业务逻辑（算法）
LIVAnalyzer.Services    # 服务（配置/日志）
LIVAnalyzer.UI          # WPF界面
LIVAnalyzer.Tests       # 单元测试
```

### 主要技术栈
- **.NET 6** - 核心框架
- **WPF + MVVM** - 界面架构
- **OxyPlot** - 图表库
- **EPPlus** - Excel处理
- **Serilog** - 日志系统
- **YamlDotNet** - 配置文件

### 关键文件说明
- `MainWindowViewModel.cs` - 主界面逻辑
- `LIVDataProcessor.cs` - 核心算法
- `ExcelDataLoader.cs` - Excel文件读取
- `ConfigurationManager.cs` - 配置管理

## 🐛 故障排除

### 常见问题

#### 1. "找不到.NET SDK"
```bash
# 解决方案：
# 1. 下载安装.NET 6 SDK
# 2. 重启命令提示符
# 3. 验证安装：dotnet --version
```

#### 2. "包还原失败"
```bash
# 解决方案：
dotnet nuget locals all --clear  # 清除缓存
dotnet restore --force           # 强制还原
```

#### 3. "编译错误"
```bash
# 解决方案：
dotnet clean          # 清理项目
dotnet restore        # 还原包
dotnet build          # 重新编译
```

#### 4. "运行时找不到文件"
- 确保在正确的目录运行
- 检查项目引用是否正确
- 重新编译项目

### 性能优化建议
1. **发布时使用Release配置**
2. **启用ReadyToRun优化**
3. **使用单文件发布减少启动时间**

## 📈 与Python版本对比

| 特性 | Python版本 | C#版本 |
|------|------------|--------|
| 启动速度 | ~3-5秒 | ~1-2秒 |
| 内存使用 | ~200MB | ~100MB |
| 安装包大小 | ~500MB | ~150MB |
| 部署复杂度 | 高（需Python环境） | 低（单文件） |
| 界面响应 | 一般 | 优秀 |
| 扩展性 | 好 | 优秀 |

## 🔮 后续开发计划

### 已实现功能 ✅
- [x] 核心LIV算法
- [x] Excel/CSV文件读取
- [x] WPF界面框架
- [x] 图表显示
- [x] 配置管理
- [x] 日志系统

### 待完善功能 🚧
- [ ] COS文件转换
- [ ] 批量处理完整实现
- [ ] 图表导出功能
- [ ] 更多图表样式
- [ ] 插件系统

### 计划新功能 📝
- [ ] 数据库支持
- [ ] 云端同步
- [ ] 自动更新
- [ ] 多语言支持

## 📞 技术支持

如遇到问题，请检查：
1. 是否安装了正确的.NET SDK版本
2. 项目文件是否完整
3. 网络连接是否正常（包下载需要）

---

**祝您使用愉快！** 🎉