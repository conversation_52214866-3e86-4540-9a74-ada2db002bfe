import { useEffect, useRef } from 'react';
import ChartToolbar from "../ChartToolbar";
let Plot: any;
import { useAppStore } from '../../state/store';

export default function EfficiencyChart() {
  const ref = useRef<HTMLDivElement | null>(null);
  const { data, displayConfig } = useAppStore.getState();

  useEffect(() => {
    if (!ref.current) return;
    ref.current.innerHTML = '';

    const p = data?.power;
    const v = data?.voltage;
    if (!p || !v) {
      ref.current.textContent = '暂无效率数据（需要 P-I 与 V-I）';
      return;
    }

    const n = Math.min(p.current.length, p.power.length, v.current.length, v.voltage.length);
    if (n === 0) {
      ref.current.textContent = '暂无效率数据';
      return;
    }

    const series: { x: number; y: number }[] = [];
    for (let i = 0; i < n; i++) {
      const I = p.current[i];
      const Popt = p.power[i];
      const Vval = v.voltage[i];
      const denom = I * Vval;
      if (Number.isFinite(I) && Number.isFinite(Popt) && Number.isFinite(Vval) && Math.abs(denom) > 1e-12) {
        series.push({ x: I, y: Popt / denom });
      }
    }

    if (series.length === 0) {
      ref.current.textContent = '暂无有效效率数据';
      return;
    }

    if (!Plot) {
      import('@observablehq/plot').then(mod => { Plot = mod; render(); });
      return;
    }
    render();
  }, [data]);

  function render() {
    if (!ref.current || !Plot) return;
    if (!(displayConfig.showEta ?? true)) {
      ref.current.textContent = 'η-I 已隐藏';
      return;
    }

    const p = data?.power;
    const v = data?.voltage;
    if (!p || !v) return;
    const n = Math.min(p.current.length, p.power.length, v.current.length, v.voltage.length);
    const series: { x: number; y: number }[] = [];
    for (let i = 0; i < n; i++) {
      const I = p.current[i];
      const Popt = p.power[i];
      const Vval = v.voltage[i];
      const denom = I * Vval;
      if (Number.isFinite(I) && Number.isFinite(Popt) && Number.isFinite(Vval) && Math.abs(denom) > 1e-12) {
        series.push({ x: I, y: Popt / denom });
      }
    }

    const plot = Plot.plot({
      marginLeft: 50,
      marginBottom: 40,
      style: { background: 'transparent' },
      x: { label: 'Current (A)' },
      y: { label: 'Efficiency η' },
      marks: [
        Plot.line(series, { x: 'x', y: 'y', stroke: 'purple', title: 'η-I' }),
        Plot.dot(series, { x: 'x', y: 'y', r: 2, fill: 'purple', title: (d: any) => `I=${d.x}\nη=${d.y.toFixed(4)}` }),
        Plot.ruleY([0])
      ],
    });

    ref.current.appendChild(plot);
    import('../../services/zoom').then(z => { (window as any).__eta_zoom = z.enableZoomPan(ref.current!); });
    return () => plot.remove();
  }

  return (
    <div className="space-y-2">
      <ChartToolbar title="Efficiency_chart" container={ref.current} onReset={() => (window as any).__eta_zoom?.then((r: any) => r.reset())} />
      <div ref={ref} className="w-full overflow-auto" />
    </div>
  );
}


