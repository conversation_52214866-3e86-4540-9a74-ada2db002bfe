import { useMemo, useState } from 'react';
import { useAppStore } from '../state/store';
import { Button } from './ui/button';

type SeriesKey = 'power' | 'voltage' | 'wavelength' | 'hff' | 'vff';

const SERIES_OPTIONS: Array<{ key: SeriesKey; label: string; x: string; y: string }> = [
  { key: 'power', label: 'P-I', x: 'Current (A)', y: 'Power (W)' },
  { key: 'voltage', label: 'V-I', x: 'Current (A)', y: 'Voltage (V)' },
  { key: 'wavelength', label: '光谱', x: 'Wavelength (nm)', y: 'Intensity' },
  { key: 'hff', label: 'HFF 远场', x: 'Angle (deg)', y: 'Intensity' },
  { key: 'vff', label: 'VFF 远场', x: 'Angle (deg)', y: 'Intensity' },
];

export default function DataTable() {
  const { data } = useAppStore();
  const [series, setSeries] = useState<SeriesKey>('power');
  const rows = useMemo(() => {
    if (!data) return [] as Array<[number, number]>;
    const pick = (a?: number[], b?: number[]) =>
      !a || !b ? [] : a.slice(0, 50).map((x, i) => [x, b[i]] as [number, number]);
    switch (series) {
      case 'power':
        return pick(data.power?.current, data.power?.power);
      case 'voltage':
        return pick(data.voltage?.current, data.voltage?.voltage);
      case 'wavelength':
        return pick(data.wavelength?.wavelength, data.wavelength?.intensity);
      case 'hff':
        return pick(data.hff?.angle, data.hff?.intensity);
      case 'vff':
        return pick(data.vff?.angle, data.vff?.intensity);
      default:
        return [];
    }
  }, [data, series]);

  const currentOpt = SERIES_OPTIONS.find((o) => o.key === series)!;
  const hasData = rows.length > 0;

  return (
    <div className="space-y-3">
      <div className="flex gap-2 flex-wrap">
        {SERIES_OPTIONS.map((o) => (
          <Button key={o.key} variant={series === o.key ? 'default' : 'outline'} size="sm" onClick={() => setSeries(o.key)}>
            {o.label}
          </Button>
        ))}
      </div>
      {!hasData ? (
        <div className="text-sm text-muted-foreground">当前系列暂无数据</div>
      ) : (
        <div className="overflow-auto border rounded">
          <table className="w-full text-sm">
            <thead>
              <tr className="bg-muted/30">
                <th className="px-3 py-2 text-left font-medium text-muted-foreground">{currentOpt.x}</th>
                <th className="px-3 py-2 text-left font-medium text-muted-foreground">{currentOpt.y}</th>
              </tr>
            </thead>
            <tbody>
              {rows.map(([x, y], idx) => (
                <tr key={idx} className="even:bg-muted/10">
                  <td className="px-3 py-1">{x}</td>
                  <td className="px-3 py-1">{y}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
}


