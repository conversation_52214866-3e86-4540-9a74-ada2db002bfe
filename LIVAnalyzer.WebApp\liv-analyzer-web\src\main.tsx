import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import { createBrowserRouter, RouterProvider } from 'react-router-dom'
import './index.css'
import App from './App.tsx'
import RegressPage from './pages/RegressPage'
import BatchPage from './pages/BatchPage'

function Settings() {
  return <div className="p-6 text-sm text-muted-foreground">设置页（待完善）</div>
}

function Docs() {
  return <div className="p-6 text-sm text-muted-foreground">文档（待完善）</div>
}

function About() {
  return <div className="p-6 text-sm text-muted-foreground">关于 LIV Analyzer Web（待完善）</div>
}

const router = createBrowserRouter([
  { path: '/', element: <App /> },
  { path: '/settings', element: <Settings /> },
  { path: '/docs', element: <Docs /> },
  { path: '/about', element: <About /> },
  { path: '/regress', element: <RegressPage /> },
  { path: '/batch', element: <BatchPage /> },
])

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <RouterProvider router={router} />
  </StrictMode>,
)
