using CommunityToolkit.Mvvm.ComponentModel;
using LIVAnalyzer.Models;

namespace LIVAnalyzer.UI.ViewModels
{
    public partial class FileViewModel : ObservableObject
    {
        public FileViewModel(LIVMeasurementData data)
        {
            Data = data;
            FileName = data.FileName;
            IsSelected = true; // 默认选中
        }

        public LIVMeasurementData Data { get; private set; }

        public string FileName { get; }

        [ObservableProperty]
        private bool isSelected;

        /// <summary>
        /// 更新数据（用于渐进式加载）
        /// </summary>
        public void UpdateData(LIVMeasurementData newData)
        {
            // 更新数据引用
            Data = newData;

            // 通知属性变更（如果需要UI更新其他绑定的属性）
            OnPropertyChanged(nameof(Data));
        }
    }
}