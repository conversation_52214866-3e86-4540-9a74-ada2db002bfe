using System;
using System.Windows;
using OxyPlot;
using OxyPlot.Axes;
using OxyPlot.Series;
using LIVAnalyzer.UI.Services;

namespace LIVAnalyzer.UI.Views
{
    public partial class AxisColorTestWindow : Window
    {
        private int _debugCounter = 0;
        
        public AxisColorTestWindow()
        {
            InitializeComponent();
            InitializePlots();
            UpdateThemeStatus();
        }
        
        private void InitializePlots()
        {
            // 创建测试图表1
            var model1 = new PlotModel { Title = "LIV曲线测试" };
            
            // 添加坐标轴
            var xAxis1 = new LinearAxis
            {
                Position = AxisPosition.Bottom,
                Title = "电流 (A)",
                MajorGridlineStyle = LineStyle.Solid,
                MinorGridlineStyle = LineStyle.Dot
            };
            model1.Axes.Add(xAxis1);
            
            var yAxis1 = new LinearAxis
            {
                Position = AxisPosition.Left,
                Title = "功率 (W)",
                MajorGridlineStyle = LineStyle.Solid,
                MinorGridlineStyle = LineStyle.Dot
            };
            model1.Axes.Add(yAxis1);
            
            // 添加测试数据
            var series1 = new LineSeries { Title = "功率曲线" };
            for (int i = 0; i <= 100; i++)
            {
                double x = i / 10.0;
                double y = Math.Pow(x, 2) * 0.8 + Math.Sin(x) * 2;
                series1.Points.Add(new DataPoint(x, y));
            }
            model1.Series.Add(series1);
            
            TestPlot1.Model = model1;
            
            // 创建测试图表2
            var model2 = new PlotModel { Title = "光谱测试" };
            
            var xAxis2 = new LinearAxis
            {
                Position = AxisPosition.Bottom,
                Title = "波长 (nm)",
                MajorGridlineStyle = LineStyle.Solid,
                MinorGridlineStyle = LineStyle.Dot
            };
            model2.Axes.Add(xAxis2);
            
            var yAxis2 = new LinearAxis
            {
                Position = AxisPosition.Left,
                Title = "强度",
                MajorGridlineStyle = LineStyle.Solid,
                MinorGridlineStyle = LineStyle.Dot
            };
            model2.Axes.Add(yAxis2);
            
            // 添加测试数据
            var series2 = new LineSeries { Title = "光谱曲线" };
            for (int i = 0; i <= 200; i++)
            {
                double x = 400 + i * 2; // 400-800nm
                double y = Math.Exp(-Math.Pow((x - 650) / 50, 2)) * 100; // 高斯峰
                series2.Points.Add(new DataPoint(x, y));
            }
            model2.Series.Add(series2);
            
            TestPlot2.Model = model2;
            
            // 应用主题
            ApplyThemeToPlots();
            
            AddDebugInfo("图表初始化完成");
        }
        
        private void ApplyThemeToPlots()
        {
            ApplyThemeToPlot(TestPlot1.Model);
            ApplyThemeToPlot(TestPlot2.Model);
        }
        
        private void ApplyThemeToPlot(PlotModel? plot)
        {
            if (plot == null) return;
            
            try
            {
                var isDark = NativeFluentThemeService.Instance.IsCurrentlyDarkMode();
                
                // 获取主题颜色
                var chartTextColor = GetThemeColor("FluentChartTextBrush");
                var chartAxisColor = GetThemeColor("FluentChartAxisBrush");
                var chartGridColor = GetThemeColor("FluentChartGridBrush");
                
                AddDebugInfo($"主题: {(isDark ? "深色" : "浅色")}, 轴线颜色: {chartAxisColor}");
                
                // 设置图表颜色
                plot.TextColor = chartTextColor;
                
                // 更新轴的颜色
                foreach (var axis in plot.Axes)
                {
                    axis.AxislineColor = chartAxisColor;
                    axis.TicklineColor = chartAxisColor;
                    axis.TextColor = chartTextColor;
                    axis.MajorGridlineColor = chartGridColor;
                    axis.MinorGridlineColor = OxyColor.FromAColor(128, chartGridColor);
                    
                    // 确保轴线可见
                    axis.AxislineStyle = LineStyle.Solid;
                    
                    AddDebugInfo($"轴 {axis.Position}: 轴线={axis.AxislineColor}, 刻度={axis.TicklineColor}");
                }
                
                plot.InvalidatePlot(true);
            }
            catch (Exception ex)
            {
                AddDebugInfo($"应用主题失败: {ex.Message}");
            }
        }
        
        private OxyColor GetThemeColor(string resourceKey)
        {
            try
            {
                if (Application.Current?.Resources[resourceKey] is System.Windows.Media.SolidColorBrush brush)
                {
                    var color = brush.Color;
                    return OxyColor.FromArgb(color.A, color.R, color.G, color.B);
                }
                
                // 默认颜色
                var isDark = NativeFluentThemeService.Instance.IsCurrentlyDarkMode();
                return resourceKey switch
                {
                    "FluentChartAxisBrush" => isDark ? OxyColor.FromRgb(156, 163, 175) : OxyColor.FromRgb(107, 114, 128),
                    "FluentChartTextBrush" => isDark ? OxyColor.FromRgb(243, 244, 246) : OxyColor.FromRgb(31, 41, 55),
                    "FluentChartGridBrush" => isDark ? OxyColor.FromRgb(55, 65, 81) : OxyColor.FromRgb(229, 231, 235),
                    _ => isDark ? OxyColor.FromRgb(240, 240, 240) : OxyColor.FromRgb(31, 41, 55)
                };
            }
            catch (Exception ex)
            {
                AddDebugInfo($"获取颜色失败 {resourceKey}: {ex.Message}");
                var isDark = NativeFluentThemeService.Instance.IsCurrentlyDarkMode();
                return isDark ? OxyColor.FromRgb(200, 200, 200) : OxyColor.FromRgb(50, 50, 50);
            }
        }
        
        private void ToggleThemeBtn_Click(object sender, RoutedEventArgs e)
        {
            var currentIsDark = NativeFluentThemeService.Instance.IsCurrentlyDarkMode();
            var newTheme = currentIsDark ? "Light" : "Dark";
            App.SwitchTheme(newTheme);
            
            UpdateThemeStatus();
            ApplyThemeToPlots();
            
            AddDebugInfo($"主题已切换到: {newTheme}");
        }
        
        private void RefreshPlotBtn_Click(object sender, RoutedEventArgs e)
        {
            ApplyThemeToPlots();
            AddDebugInfo("图表已刷新");
        }
        
        private void UpdateThemeStatus()
        {
            var isDark = NativeFluentThemeService.Instance.IsCurrentlyDarkMode();
            ThemeStatusText.Text = $"当前主题: {(isDark ? "深色" : "浅色")}";
        }
        
        private void AddDebugInfo(string message)
        {
            _debugCounter++;
            var timestamp = DateTime.Now.ToString("HH:mm:ss.fff");
            DebugInfo.Text += $"[{_debugCounter:D3}] {timestamp}: {message}\n";
            
            // 自动滚动到底部
            if (DebugInfo.Parent is System.Windows.Controls.ScrollViewer scrollViewer)
            {
                scrollViewer.ScrollToEnd();
            }
        }
    }
}
