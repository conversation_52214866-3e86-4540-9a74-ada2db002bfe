using LIVAnalyzer.Models;

namespace LIVAnalyzer.Models
{
    /// <summary>
    /// 文件数据传输对象，用于在不同层之间传递文件信息
    /// </summary>
    public class FileDataModel
    {
        public FileDataModel(LIVMeasurementData data)
        {
            Data = data;
            FileName = data.FileName;
            IsSelected = true; // 默认选中
        }
        
        public LIVMeasurementData Data { get; }
        
        public string FileName { get; }
        
        public bool IsSelected { get; set; }
    }
}