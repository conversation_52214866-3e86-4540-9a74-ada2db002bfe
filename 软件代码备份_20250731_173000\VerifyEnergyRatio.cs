using System;
using System.IO;
using System.Linq;
using System.Collections.Generic;
using LIVAnalyzer.Data.Loaders;
using LIVAnalyzer.Core.Processors;
using LIVAnalyzer.Models;

class VerifyEnergyRatio
{
    static void Main()
    {
        string testDataPath = @"E:\01LaserPackage\software\LIV_Analyzer\光峰芯片测试数据\640nm_QCW2P5A_30duty_T45C_LIV+Spectrum0716\芯瑞光红光芯片测试数据\R24+COS01+201_converted.xlsx";
        
        if (!File.Exists(testDataPath))
        {
            Console.WriteLine($"测试数据文件不存在: {testDataPath}");
            return;
        }

        try
        {
            var loader = new ExcelDataLoader();
            var data = loader.LoadExcelDataAsync(testDataPath).Result;
            
            if (data == null)
            {
                Console.WriteLine("无法加载数据文件");
                return;
            }
            
            Console.WriteLine($"成功加载数据文件: {Path.GetFileName(testDataPath)}");
            
            if (data.HorizontalDivergenceData?.Any() == true)
            {
                Console.WriteLine("\n=== 水平发散角分析 ===");
                AnalyzeDivergence(data.HorizontalDivergenceData, "水平");
            }
            
            if (data.VerticalDivergenceData?.Any() == true)
            {
                Console.WriteLine("\n=== 垂直发散角分析 ===");
                AnalyzeDivergence(data.VerticalDivergenceData, "垂直");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"错误: {ex.Message}");
        }
    }
    
    static void AnalyzeDivergence(List<DataPoint> divergenceData, string direction)
    {
        var processor = new DivergenceProcessor();
        var angleDataPoints = divergenceData.Select(p => new DataPoint(p.X, p.Y)).ToList();
        var intensityDataPoints = divergenceData.Select(p => new DataPoint(p.X, p.Y)).ToList();
        
        var result = processor.CalculateDivergence(angleDataPoints, intensityDataPoints);
        
        if (result.IsValid)
        {
            Console.WriteLine($"FWHM: {result.FWHM:F2}°");
            Console.WriteLine($"FW(1/e²): {result.FW1e2:F2}°");
            Console.WriteLine($"FW(1/e²)能量占比: {result.FW1e2PowerContainment:P3}");
            Console.WriteLine($"FW95%: {result.FW95:F2}°");
            
            // 高亮显示能量占比结果
            Console.ForegroundColor = ConsoleColor.Yellow;
            Console.WriteLine($"\n*** {direction}方向 FW(1/e²) 能量占比 = {result.FW1e2PowerContainment:P3} ***");
            Console.ResetColor();
        }
        else
        {
            Console.WriteLine($"计算失败: {result.ErrorMessage}");
        }
    }
}