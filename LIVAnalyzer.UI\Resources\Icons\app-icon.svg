<?xml version="1.0" encoding="UTF-8"?>
<svg width="256" height="256" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 背景渐变 -->
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2196F3;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1976D2;stop-opacity:1" />
    </linearGradient>
    
    <!-- 激光束渐变 -->
    <linearGradient id="laserGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#FF9800;stop-opacity:0.8" />
      <stop offset="50%" style="stop-color:#FFC107;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FF5722;stop-opacity:0.6" />
    </linearGradient>
    
    <!-- 曲线渐变 -->
    <linearGradient id="curveGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#4CAF50;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#8BC34A;stop-opacity:1" />
    </linearGradient>
    
    <!-- 阴影滤镜 -->
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- 背景圆角矩形 -->
  <rect x="16" y="16" width="224" height="224" rx="32" ry="32" fill="url(#bgGradient)" filter="url(#shadow)"/>
  
  <!-- 坐标系网格 -->
  <g stroke="#ffffff" stroke-width="1" opacity="0.3">
    <!-- 垂直网格线 -->
    <line x1="64" y1="64" x2="64" y2="192"/>
    <line x1="96" y1="64" x2="96" y2="192"/>
    <line x1="128" y1="64" x2="128" y2="192"/>
    <line x1="160" y1="64" x2="160" y2="192"/>
    <line x1="192" y1="64" x2="192" y2="192"/>
    
    <!-- 水平网格线 -->
    <line x1="48" y1="80" x2="208" y2="80"/>
    <line x1="48" y1="112" x2="208" y2="112"/>
    <line x1="48" y1="144" x2="208" y2="144"/>
    <line x1="48" y1="176" x2="208" y2="176"/>
  </g>
  
  <!-- 坐标轴 -->
  <g stroke="#ffffff" stroke-width="2" fill="none">
    <!-- X轴 -->
    <line x1="48" y1="192" x2="208" y2="192"/>
    <!-- Y轴 -->
    <line x1="48" y1="64" x2="48" y2="192"/>
    
    <!-- 箭头 -->
    <polygon points="208,192 200,188 200,196" fill="#ffffff"/>
    <polygon points="48,64 44,72 52,72" fill="#ffffff"/>
  </g>
  
  <!-- LIV曲线 -->
  <path d="M 56 180 Q 80 170 104 150 Q 128 120 152 90 Q 176 70 200 75" 
        stroke="url(#curveGradient)" stroke-width="3" fill="none" opacity="0.9"/>
  
  <!-- 数据点 -->
  <g fill="#4CAF50">
    <circle cx="56" cy="180" r="3"/>
    <circle cx="80" cy="165" r="3"/>
    <circle cx="104" cy="140" r="3"/>
    <circle cx="128" cy="110" r="3"/>
    <circle cx="152" cy="85" r="3"/>
    <circle cx="176" cy="72" r="3"/>
    <circle cx="200" cy="75" r="3"/>
  </g>
  
  <!-- 激光器符号 -->
  <g transform="translate(180, 40)">
    <!-- 激光器外壳 -->
    <rect x="0" y="0" width="32" height="12" rx="2" fill="#ffffff" opacity="0.9"/>
    <rect x="2" y="2" width="28" height="8" rx="1" fill="#1976D2"/>
    
    <!-- 激光束 -->
    <g opacity="0.8">
      <line x1="32" y1="6" x2="48" y2="6" stroke="url(#laserGradient)" stroke-width="2"/>
      <line x1="32" y1="6" x2="44" y2="2" stroke="url(#laserGradient)" stroke-width="1" opacity="0.6"/>
      <line x1="32" y1="6" x2="44" y2="10" stroke="url(#laserGradient)" stroke-width="1" opacity="0.6"/>
    </g>
  </g>
  
  <!-- 标题文字 -->
  <text x="128" y="230" text-anchor="middle" fill="#ffffff" font-family="Arial, sans-serif" font-size="14" font-weight="bold" opacity="0.8">LIV</text>
</svg>
