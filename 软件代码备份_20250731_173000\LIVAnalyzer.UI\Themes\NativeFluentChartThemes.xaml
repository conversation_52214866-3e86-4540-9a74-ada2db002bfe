<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:oxy="http://oxyplot.org/wpf"
                    xmlns:local="clr-namespace:LIVAnalyzer.UI.Services">

    <!-- .NET 9 原生 Fluent Design 图表主题 -->
    <!-- 基于系统主题自动适配的图表样式 -->
    <!-- 注意：主题颜色资源由主题服务动态管理，此处不重复定义 -->

    <!-- 图表系列颜色 - 固定高对比度配色 -->
    <SolidColorBrush x:Key="ChartSeries1Brush" Color="#FF0078D4"/>
    <SolidColorBrush x:Key="ChartSeries2Brush" Color="#FF6B46C1"/>
    <SolidColorBrush x:Key="ChartSeries3Brush" Color="#FF10B981"/>
    <SolidColorBrush x:Key="ChartSeries4Brush" Color="#FFF59E0B"/>
    
    <!-- 图表强调色 - 使用系统强调色 -->
    <SolidColorBrush x:Key="ChartAccentBrush" Color="{DynamicResource SystemAccentColor}"/>
    <SolidColorBrush x:Key="ChartAccentLightBrush" Color="{DynamicResource SystemAccentColorLight1}"/>
    <SolidColorBrush x:Key="ChartAccentDarkBrush" Color="{DynamicResource SystemAccentColorDark1}"/>
    
    <!-- 数据系列颜色 - 基于Fluent Design色彩系统 -->
    <SolidColorBrush x:Key="Series1Brush" Color="{DynamicResource SystemAccentColor}"/>
    <SolidColorBrush x:Key="Series2Brush" Color="#FF6B73"/>
    <SolidColorBrush x:Key="Series3Brush" Color="#4ECDC4"/>
    <SolidColorBrush x:Key="Series4Brush" Color="#45B7D1"/>
    <SolidColorBrush x:Key="Series5Brush" Color="#96CEB4"/>
    <SolidColorBrush x:Key="Series6Brush" Color="#FFEAA7"/>
    <SolidColorBrush x:Key="Series7Brush" Color="#DDA0DD"/>
    <SolidColorBrush x:Key="Series8Brush" Color="#98D8C8"/>
    
    <!-- OxyPlot 图表样式 -->
    <Style x:Key="NativeFluentPlotViewStyle" TargetType="{x:Type oxy:PlotView}">
        <Setter Property="Background" Value="{DynamicResource ChartBackgroundBrush}"/>
        <Setter Property="Foreground" Value="{DynamicResource ChartTextBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource ChartAxisBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Margin" Value="5"/>
        <Setter Property="Padding" Value="10"/>
        <!-- Fluent Design 圆角 -->
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type oxy:PlotView}">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="8"
                            Padding="{TemplateBinding Padding}">
                        <!-- Fluent Design 阴影效果 -->
                        <Border.Effect>
                            <DropShadowEffect Color="{DynamicResource SystemBaseLowColor}" 
                                            BlurRadius="8" 
                                            ShadowDepth="2" 
                                            Opacity="0.3"/>
                        </Border.Effect>
                        <ContentPresenter/>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    
    <!-- 图表容器样式 -->
    <Style x:Key="ChartContainerStyle" TargetType="{x:Type Border}">
        <Setter Property="Background" Value="{DynamicResource ChartBackgroundBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource ChartAxisBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="CornerRadius" Value="8"/>
        <Setter Property="Margin" Value="5"/>
        <Setter Property="Padding" Value="10"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="{DynamicResource SystemBaseLowColor}" 
                                BlurRadius="8" 
                                ShadowDepth="2" 
                                Opacity="0.3"/>
            </Setter.Value>
        </Setter>
    </Style>
    
    <!-- 图表标题样式 -->
    <Style x:Key="ChartTitleStyle" TargetType="{x:Type TextBlock}">
        <Setter Property="Foreground" Value="{DynamicResource ChartTextBrush}"/>
        <Setter Property="FontSize" Value="16"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Margin" Value="0,0,0,10"/>
        <Setter Property="HorizontalAlignment" Value="Center"/>
    </Style>
    
    <!-- 图表图例样式 -->
    <Style x:Key="ChartLegendStyle" TargetType="{x:Type StackPanel}">
        <Setter Property="Orientation" Value="Horizontal"/>
        <Setter Property="HorizontalAlignment" Value="Center"/>
        <Setter Property="Margin" Value="0,10,0,0"/>
    </Style>
    
    <!-- 图例项样式 -->
    <Style x:Key="LegendItemStyle" TargetType="{x:Type StackPanel}">
        <Setter Property="Orientation" Value="Horizontal"/>
        <Setter Property="Margin" Value="10,0"/>
    </Style>
    
    <!-- 图例颜色块样式 -->
    <Style x:Key="LegendColorStyle" TargetType="{x:Type Rectangle}">
        <Setter Property="Width" Value="12"/>
        <Setter Property="Height" Value="12"/>
        <Setter Property="Margin" Value="0,0,5,0"/>
        <Setter Property="RadiusX" Value="2"/>
        <Setter Property="RadiusY" Value="2"/>
    </Style>
    
    <!-- 图例文字样式 -->
    <Style x:Key="LegendTextStyle" TargetType="{x:Type TextBlock}">
        <Setter Property="Foreground" Value="{DynamicResource ChartTextBrush}"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
    </Style>

</ResourceDictionary>
