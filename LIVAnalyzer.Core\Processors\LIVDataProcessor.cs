using System;
using System.Linq;
using LIVAnalyzer.Models;
using LIVAnalyzer.Core.Services;
using LIVAnalyzer.Services.Logging;
using LIVAnalyzer.Core.Algorithms;
using LIVAnalyzer.Core.Configuration;
using MathNet.Numerics;
using MathNet.Numerics.LinearAlgebra;
using MathNet.Numerics.Statistics;

namespace LIVAnalyzer.Core.Processors
{
    /// <summary>
    /// LIV数据处理器
    /// </summary>
    public class LIVDataProcessor
    {
        /// <summary>
        /// 计算LIV参数
        /// </summary>
        /// <param name="data">测量数据</param>
        /// <returns>计算结果</returns>
        public LIVParameters CalculateParameters(LIVMeasurementData data)
        {
            var parameters = new LIVParameters();
            
            // 计算光谱参数
            if (data.WavelengthIntensityData.Any())
            {
                CalculateSpectralParameters(data.WavelengthIntensityData, parameters);
            }
            
            // 计算LIV参数
            if (data.CurrentPowerData.Any())
            {
                CalculateLIVParameters(data.CurrentPowerData, parameters);
            }
            
            // 计算电阻参数
            if (data.CurrentVoltageData.Any())
            {
                CalculateResistanceParameters(data.CurrentVoltageData, parameters);
            }
            
            // 计算最大效率 (需要电流、功率和电压数据)
            if (data.CurrentPowerData.Any() && data.CurrentVoltageData.Any())
            {
                parameters.MaxEfficiency = CalculateMaxEfficiency(data.CurrentPowerData, data.CurrentVoltageData);
            }
            
            return parameters;
        }
        
        /// <summary>
        /// 计算基本LIV参数（用于渐进式加载）
        /// </summary>
        public LIVParameters CalculateBasicLIVParameters(LIVMeasurementData data)
        {
            var parameters = new LIVParameters();

            try
            {
                if (data.CurrentPowerData.Any())
                {
                    CalculateLIVParameters(data.CurrentPowerData, parameters);
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "计算基本LIV参数时发生错误");
                // 不抛出异常，返回部分结果
            }

            return parameters;
        }

        /// <summary>
        /// 计算光谱参数（用于渐进式加载）
        /// </summary>
        public (double PeakWavelength, double FWHM) CalculateSpectralParameters(List<DataPoint> wavelengthData)
        {
            var parameters = new LIVParameters();
            CalculateSpectralParameters(wavelengthData, parameters);
            return (parameters.PeakWavelength, parameters.FWHM);
        }

        // TODO: 修复编译错误后重新启用
        /*
        /// <summary>
        /// 计算电阻参数并返回元组（用于渐进式加载）
        /// </summary>
        public (double? SeriesResistance, double? DifferentialResistance) GetResistanceParametersTuple(List<DataPoint> voltageData)
        {
            try
            {
                var parameters = new LIVParameters();
                CalculateResistanceParameters(voltageData, parameters);

                return (parameters.SeriesResistance, parameters.DifferentialResistance);
            }
            catch
            {
                return (null, null);
            }
        }
        */

        /// <summary>
        /// 计算发散角参数
        /// </summary>
        /// <param name="data">测量数据</param>
        /// <returns>发散角结果</returns>
        public DivergenceResults? CalculateDivergenceParameters(LIVMeasurementData data)
        {
            var results = new DivergenceResults();
            bool hasResults = false;

            // 计算水平发散角
            if (data.HorizontalDivergenceData != null && data.HorizontalDivergenceData.Any())
            {
                var horizontalResults = CalculateDivergenceAngles(data.HorizontalDivergenceData);
                results.HorizontalFWHM = horizontalResults.FWHM;
                results.HorizontalFW1e2 = horizontalResults.FW1e2;
                results.HorizontalFW1e2PowerContainment = horizontalResults.FW1e2PowerContainment;
                results.HorizontalFW95 = horizontalResults.FW95;
                hasResults = true;
            }

            // 计算垂直发散角
            if (data.VerticalDivergenceData != null && data.VerticalDivergenceData.Any())
            {
                var verticalResults = CalculateDivergenceAngles(data.VerticalDivergenceData);
                results.VerticalFWHM = verticalResults.FWHM;
                results.VerticalFW1e2 = verticalResults.FW1e2;
                results.VerticalFW1e2PowerContainment = verticalResults.FW1e2PowerContainment;
                results.VerticalFW95 = verticalResults.FW95;
                hasResults = true;
            }

            return hasResults ? results : null;
        }
        

        
        private void CalculateSpectralParameters(List<DataPoint> wavelengthData, LIVParameters parameters)
        {
            if (!wavelengthData.Any()) return;
            
            try
            {
                // 按波长排序
                var sortedData = wavelengthData.OrderBy(p => p.X).ToList();
                
                // 找到峰值
                var maxIntensity = sortedData.Max(p => p.Y);
                var peakIndex = sortedData.FindIndex(p => Math.Abs(p.Y - maxIntensity) < 1e-10);
                parameters.PeakWavelength = sortedData[peakIndex].X;
                
                // 计算FWHM - 使用插值方法找到精确的半高位置
                var halfMax = maxIntensity / 2.0;
                
                // 寻找左侧半高点
                double? leftWavelength = null;
                for (int i = 0; i < peakIndex; i++)
                {
                    if (sortedData[i].Y <= halfMax && sortedData[i + 1].Y >= halfMax)
                    {
                        // 线性插值找到精确交点
                        var x1 = sortedData[i].X;
                        var y1 = sortedData[i].Y;
                        var x2 = sortedData[i + 1].X;
                        var y2 = sortedData[i + 1].Y;
                        
                        if (Math.Abs(y2 - y1) > 1e-10)
                        {
                            leftWavelength = x1 + (halfMax - y1) * (x2 - x1) / (y2 - y1);
                        }
                        break;
                    }
                }
                
                // 寻找右侧半高点
                double? rightWavelength = null;
                for (int i = peakIndex; i < sortedData.Count - 1; i++)
                {
                    if (sortedData[i].Y >= halfMax && sortedData[i + 1].Y <= halfMax)
                    {
                        // 线性插值找到精确交点
                        var x1 = sortedData[i].X;
                        var y1 = sortedData[i].Y;
                        var x2 = sortedData[i + 1].X;
                        var y2 = sortedData[i + 1].Y;
                        
                        if (Math.Abs(y2 - y1) > 1e-10)
                        {
                            rightWavelength = x1 + (halfMax - y1) * (x2 - x1) / (y2 - y1);
                        }
                        break;
                    }
                }
                
                // 如果没找到精确交点，使用最接近的点（兼容旧方法）
                if (!leftWavelength.HasValue)
                {
                    var leftPoints = sortedData.Take(peakIndex).Where(p => p.Y >= halfMax).ToList();
                    if (leftPoints.Any())
                    {
                        leftWavelength = leftPoints.First().X;
                    }
                }
                
                if (!rightWavelength.HasValue)
                {
                    var rightPoints = sortedData.Skip(peakIndex).Where(p => p.Y >= halfMax).ToList();
                    if (rightPoints.Any())
                    {
                        rightWavelength = rightPoints.Last().X;
                    }
                }
                
                // 计算FWHM
                if (leftWavelength.HasValue && rightWavelength.HasValue)
                {
                    parameters.FWHM = rightWavelength.Value - leftWavelength.Value;
                }
                else
                {
                    parameters.FWHM = 0; // 无法计算FWHM
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"光谱参数计算错误: {ex.Message}");
                parameters.FWHM = 0;
            }
        }
        
        private void CalculateLIVParameters(List<DataPoint> currentPowerData, LIVParameters parameters)
        {
            if (!currentPowerData.Any()) return;
            
            // 计算最大功率
            parameters.MaxPower = currentPowerData.Max(p => p.Y);
            
            // 计算阈值电流（功率开始显著增长的点）
            parameters.ThresholdCurrent = CalculateThresholdCurrent(currentPowerData);
            
            // 计算斜率效率
            parameters.SlopeEfficiency = CalculateSlopeEfficiency(currentPowerData, parameters.ThresholdCurrent);
        }
        
        private void CalculateResistanceParameters(List<DataPoint> currentVoltageData, LIVParameters parameters)
        {
            if (currentVoltageData.Count < 2) return;
            
            try
            {
                // 计算串联电阻（线性拟合斜率）
                var xValues = currentVoltageData.Select(p => p.X).ToArray();
                var yValues = currentVoltageData.Select(p => p.Y).ToArray();
                
                var fit = Fit.Line(xValues, yValues);
                parameters.SeriesResistance = fit.Item2; // 斜率即为电阻
                
                // 计算拟合度R²
                var predictions = xValues.Select(x => fit.Item1 + fit.Item2 * x);
                var rSquared = GoodnessOfFit.RSquared(predictions, yValues);
                parameters.SeriesResistanceR2 = rSquared;
                
                // 计算微分电阻（相邻点的斜率）
                var differentialResistances = new List<double>();
                for (int i = 1; i < currentVoltageData.Count; i++)
                {
                    var dV = currentVoltageData[i].Y - currentVoltageData[i - 1].Y;
                    var dI = currentVoltageData[i].X - currentVoltageData[i - 1].X;
                    if (Math.Abs(dI) > 1e-10)
                    {
                        differentialResistances.Add(dV / dI);
                    }
                }
                
                if (differentialResistances.Any())
                {
                    parameters.DifferentialResistance = differentialResistances.Mean();
                }
            }
            catch
            {
                // 如果计算失败，设置为null
                parameters.SeriesResistance = null;
                parameters.SeriesResistanceR2 = null;
                parameters.DifferentialResistance = null;
            }
        }
        
        /// <summary>
        /// 计算阈值电流 - 使用默认配置的向后兼容方法
        /// </summary>
        protected virtual double CalculateThresholdCurrent(List<DataPoint> currentPowerData)
        {
            return CalculateThresholdCurrent(currentPowerData, ThresholdCalculationConfig.Default);
        }

        /// <summary>
        /// 计算阈值电流 - 支持自定义配置的新方法
        /// 使用改进的一阶导数法计算阈值电流，增强了噪声过滤和合理性验证
        /// </summary>
        /// <param name="currentPowerData">电流-功率数据点</param>
        /// <param name="config">阈值计算配置参数</param>
        /// <returns>阈值电流值</returns>
        protected virtual double CalculateThresholdCurrent(List<DataPoint> currentPowerData, ThresholdCalculationConfig config)
        {
            // 验证配置参数
            config?.Validate();
            config ??= ThresholdCalculationConfig.Default;

            // 检查最小数据点数
            if (currentPowerData.Count < config.MinDataPoints) return 0;

            try
            {
                // 1. 严格的数据预处理：移除零值、负值、重复值和明显的噪声点
                var sortedData = currentPowerData
                    .Where(p => p.X >= 0 && p.Y >= 0)
                    .OrderBy(p => p.X)
                    .ToList();
                    
                if (sortedData.Count < config.MinDataPoints) return 0;
                
                // 2. 噪声过滤：移除低电流区域的异常高功率点
                var maxPower = sortedData.Max(p => p.Y);
                var filteredData = new List<DataPoint>();
                
                for (int i = 0; i < sortedData.Count; i++)
                {
                    var point = sortedData[i];
                    
                    // 对于低电流区域（< 20% 最大电流），过滤掉异常高功率点
                    var maxCurrent = sortedData.Max(p => p.X);
                    if (point.X < maxCurrent * 0.2)
                    {
                        // 检查是否为噪声点：功率远高于相邻点的平均值
                        var nearbyPoints = sortedData
                            .Where(p => Math.Abs(p.X - point.X) <= maxCurrent * 0.05)
                            .Where(p => p != point)
                            .ToList();
                            
                        if (nearbyPoints.Any())
                        {
                            var avgNearbyPower = nearbyPoints.Average(p => p.Y);
                            var powerThreshold = Math.Max(avgNearbyPower * 3, maxPower * 0.01);
                            
                            // 如果功率超过阈值，认为是噪声点，跳过
                            if (point.Y > powerThreshold && avgNearbyPower < maxPower * 0.02)
                            {
                                continue;
                            }
                        }
                    }
                    
                    filteredData.Add(point);
                }
                
                // 去重并排序
                var validData = filteredData
                    .GroupBy(p => p.X)
                    .Select(g => new DataPoint(g.Key, g.Average(p => p.Y)))
                    .OrderBy(p => p.X)
                    .ToList();
                    
                if (validData.Count < config.MinDataPoints) return 0;

                // 3. 计算自适应窗口大小（确保为奇数）
                int maxWindow = Math.Max(3, (int)(validData.Count * config.DataWindowRatio));
                if (maxWindow % 2 == 0) maxWindow--;
                int windowSize = Math.Min(config.MaxSmoothingWindow, maxWindow);
                
                // 4. 对功率数据进行移动平均平滑
                var smoothedPower = MovingAverage(validData.Select(p => p.Y).ToArray(), windowSize);
                var validIndices = Enumerable.Range(windowSize / 2, validData.Count - windowSize + 1).ToArray();
                var validCurrent = validIndices.Select(i => validData[i].X).ToArray();
                
                // 5. 计算一阶导数 (dP/dI)
                var derivatives = new double[smoothedPower.Length - 1];
                for (int i = 0; i < derivatives.Length; i++)
                {
                    var dP = smoothedPower[i + 1] - smoothedPower[i];
                    var dI = validCurrent[i + 1] - validCurrent[i];
                    derivatives[i] = Math.Abs(dI) > config.NumericalPrecision ? dP / dI : 0;
                }
                
                // 6. 对一阶导数进行平滑
                int derivWindowSize = Math.Min(config.MaxDerivativeSmoothingWindow,
                                              Math.Max(3, (int)(derivatives.Length * config.DerivativeWindowRatio)));
                if (derivWindowSize % 2 == 0) derivWindowSize--;
                if (derivWindowSize < 3) derivWindowSize = 3;
                
                var smoothedDerivatives = MovingAverage(derivatives, derivWindowSize);
                var finalIndices = Enumerable.Range(derivWindowSize / 2, derivatives.Length - derivWindowSize + 1).ToArray();
                var finalCurrent = finalIndices.Select(i => validCurrent[i]).ToArray();
                
                // 7. 找到最大导数值，限制搜索范围
                var searchLimit = (int)(finalCurrent.Length * config.SearchRangeRatio);
                var searchDerivatives = smoothedDerivatives.Take(searchLimit).ToArray();
                var searchCurrent = finalCurrent.Take(searchLimit).ToArray();
                
                if (searchDerivatives.Length == 0) return 0;
                
                double maxDerivative = searchDerivatives.Max();
                double halfMaxDerivative = maxDerivative * config.DerivativeRatio;
                
                // 8. 使用插值找到导数等于半最大值的精确位置
                double thresholdCurrent = 0;
                
                // 寻找跨越半最大值的点
                for (int i = 0; i < searchDerivatives.Length - 1; i++)
                {
                    if ((searchDerivatives[i] - halfMaxDerivative) * (searchDerivatives[i + 1] - halfMaxDerivative) <= 0)
                    {
                        // 线性插值
                        if (Math.Abs(searchDerivatives[i + 1] - searchDerivatives[i]) > config.NumericalPrecision)
                        {
                            double t = (halfMaxDerivative - searchDerivatives[i]) / 
                                      (searchDerivatives[i + 1] - searchDerivatives[i]);
                            thresholdCurrent = searchCurrent[i] + t * (searchCurrent[i + 1] - searchCurrent[i]);
                            break;
                        }
                    }
                }
                
                // 9. 如果没找到交点，使用最接近的点
                if (thresholdCurrent == 0)
                {
                    int closestIdx = 0;
                    double minDiff = Math.Abs(searchDerivatives[0] - halfMaxDerivative);
                    for (int i = 1; i < searchDerivatives.Length; i++)
                    {
                        double diff = Math.Abs(searchDerivatives[i] - halfMaxDerivative);
                        if (diff < minDiff)
                        {
                            minDiff = diff;
                            closestIdx = i;
                        }
                    }
                    thresholdCurrent = searchCurrent[closestIdx];
                }
                
                // 10. 严格的合理性验证
                var maxCurrentValue = validData.Max(p => p.X);
                var maxPowerValue = validData.Max(p => p.Y);
                
                // 阈值电流合理性验证
                if (thresholdCurrent < 0 || thresholdCurrent > maxCurrentValue * config.MaxThresholdRatio)
                {
                    // 使用备选方法：功率达到配置比例的点
                    var fallbackThreshold = maxPowerValue * config.PrimaryFallbackPowerRatio;
                    var fallbackPoint = validData.FirstOrDefault(p => p.Y > fallbackThreshold);
                    if (fallbackPoint != null)
                    {
                        thresholdCurrent = fallbackPoint.X;
                    }
                    else
                    {
                        // 最后的备选：使用次级功率比例
                        thresholdCurrent = validData.FirstOrDefault(p => p.Y > maxPowerValue * config.SecondaryFallbackPowerRatio)?.X ?? 0;
                    }
                }
                
                // 11. 最终验证：阈值电流应该合理
                if (thresholdCurrent > maxCurrentValue * config.SecondaryMaxThresholdRatio)
                {
                    // 如果计算出的阈值超过次级限制，使用第三级功率比例回退
                    var reasonableThreshold = validData.FirstOrDefault(p => p.Y > maxPowerValue * config.TertiaryFallbackPowerRatio)?.X ?? 0;
                    return reasonableThreshold;
                }

                return thresholdCurrent;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"阈值电流计算失败: {ex.Message}");
                return 0;
            }
        }
        
        /// <summary>
        /// 移动平均算法
        /// </summary>
        private double[] MovingAverage(double[] data, int windowSize)
        {
            if (windowSize > data.Length) windowSize = data.Length;
            if (windowSize % 2 == 0) windowSize--; // 确保窗口为奇数
            if (windowSize < 1) windowSize = 1;
            
            int halfWindow = windowSize / 2;
            var result = new double[data.Length - windowSize + 1];
            
            for (int i = 0; i < result.Length; i++)
            {
                double sum = 0;
                for (int j = 0; j < windowSize; j++)
                {
                    sum += data[i + j];
                }
                result[i] = sum / windowSize;
            }
            
            return result;
        }
        
        private double CalculateSlopeEfficiency(List<DataPoint> currentPowerData, double thresholdCurrent)
        {
            var linearRegion = currentPowerData.Where(p => p.X > thresholdCurrent).ToList();
            if (linearRegion.Count < 2) return 0;
            
            var xValues = linearRegion.Select(p => p.X).ToArray();
            var yValues = linearRegion.Select(p => p.Y).ToArray();
            
            var fit = Fit.Line(xValues, yValues);
            return fit.Item2; // 斜率
        }
        
        private double CalculateMaxEfficiency(List<DataPoint> currentPowerData, List<DataPoint> currentVoltageData)
        {
            // 创建电流到电压的映射
            var voltageMap = currentVoltageData.ToDictionary(p => p.X, p => p.Y);
            
            // 计算所有有效的效率值
            var efficiencyData = new List<(double Current, double Efficiency)>();
            
            foreach (var point in currentPowerData)
            {
                if (point.X > 0 && point.Y > 0 && voltageMap.ContainsKey(point.X))
                {
                    var voltage = voltageMap[point.X];
                    if (voltage > 0)
                    {
                        var efficiency = (point.Y / (point.X * voltage)) * 100;
                        efficiencyData.Add((point.X, efficiency));
                    }
                }
            }
            
            if (!efficiencyData.Any()) return 0;
            
            // 使用统计方法检测和过滤异常值
            var cleanedEfficiencies = FilterEfficiencyOutliers(efficiencyData);
            
            return cleanedEfficiencies.Any() ? cleanedEfficiencies.Max() : 0;
        }
        
        /// <summary>
        /// 使用统计方法过滤效率异常值
        /// </summary>
        private List<double> FilterEfficiencyOutliers(List<(double Current, double Efficiency)> efficiencyData)
        {
            if (efficiencyData.Count < 5) return efficiencyData.Select(e => e.Efficiency).ToList();
            
            // 将数据按电流分为低、中、高三个区间分别分析
            var maxCurrent = efficiencyData.Max(e => e.Current);
            var lowCurrentData = efficiencyData.Where(e => e.Current <= maxCurrent * 0.2).ToList();
            var midCurrentData = efficiencyData.Where(e => e.Current > maxCurrent * 0.2 && e.Current <= maxCurrent * 0.8).ToList();
            var highCurrentData = efficiencyData.Where(e => e.Current > maxCurrent * 0.8).ToList();
            
            var cleanedEfficiencies = new List<double>();
            
            // 对每个区间分别应用异常值检测
            cleanedEfficiencies.AddRange(FilterRegionOutliers(lowCurrentData, "低电流"));
            cleanedEfficiencies.AddRange(FilterRegionOutliers(midCurrentData, "中电流"));
            cleanedEfficiencies.AddRange(FilterRegionOutliers(highCurrentData, "高电流"));
            
            return cleanedEfficiencies;
        }
        
        /// <summary>
        /// 对特定电流区间应用异常值检测
        /// </summary>
        private List<double> FilterRegionOutliers(List<(double Current, double Efficiency)> regionData, string regionName)
        {
            if (regionData.Count < 3) return regionData.Select(e => e.Efficiency).ToList();
            
            var efficiencies = regionData.Select(e => e.Efficiency).ToList();
            efficiencies.Sort();
            
            // 使用四分位数方法检测异常值 (IQR method)
            var q1Index = (int)(efficiencies.Count * 0.25);
            var q3Index = (int)(efficiencies.Count * 0.75);
            var q1 = efficiencies[q1Index];
            var q3 = efficiencies[q3Index];
            var iqr = q3 - q1;
            
            // 异常值定义：超过 Q3 + 1.5*IQR 或低于 Q1 - 1.5*IQR
            var lowerBound = q1 - 1.5 * iqr;
            var upperBound = q3 + 1.5 * iqr;
            
            // 对于低电流区域，更严格地过滤上界异常值
            if (regionName == "低电流" && iqr > 0)
            {
                // 如果存在明显的异常高值，使用更保守的上界
                var median = efficiencies[efficiencies.Count / 2];
                var conservativeUpperBound = median + 2 * iqr;
                upperBound = Math.Min(upperBound, conservativeUpperBound);
                
                // 额外的物理约束：低电流区域效率通常不会超过中高电流区域
                upperBound = Math.Min(upperBound, 50.0); // 50%作为低电流区域的合理上限
            }
            
            return efficiencies.Where(e => e >= lowerBound && e <= upperBound).ToList();
        }
        
        private (double? FWHM, double? FW1e2, double? FW1e2PowerContainment, double? FW95) CalculateDivergenceAngles(List<DataPoint> divergenceData)
        {
            if (!divergenceData.Any()) return (null, null, null, null);

            try
            {
                // 使用DivergenceProcessor进行完整的发散角计算
                var divergenceProcessor = new DivergenceProcessor();

                // 分离角度和强度数据
                var angleData = divergenceData.Select(p => new DataPoint(p.X, p.X)).ToList();
                var intensityData = divergenceData.Select(p => new DataPoint(p.X, p.Y)).ToList();

                // 计算发散角参数
                var result = divergenceProcessor.CalculateDivergence(angleData, intensityData);

                if (result.IsValid)
                {
                    return (result.FWHM, result.FW1e2, result.FW1e2PowerContainment, result.FW95);
                }
                else
                {
                    // 如果DivergenceProcessor失败，回退到简单计算
                    LoggingService.LogWarning($"DivergenceProcessor计算失败，使用简单方法: {result.ErrorMessage}");
                    return CalculateDivergenceAnglesSimple(divergenceData);
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"发散角计算失败: {ex.Message}");
                // 回退到简单计算
                return CalculateDivergenceAnglesSimple(divergenceData);
            }
        }

        private (double? FWHM, double? FW1e2, double? FW1e2PowerContainment, double? FW95) CalculateDivergenceAnglesSimple(List<DataPoint> divergenceData)
        {
            if (!divergenceData.Any()) return (null, null, null, null);

            var maxIntensity = divergenceData.Max(p => p.Y);

            // 计算FWHM (50%)
            var fwhm = CalculateAngleWidth(divergenceData, maxIntensity * 0.5);

            // 计算FW(1/e²)
            var fw1e2 = CalculateAngleWidth(divergenceData, maxIntensity * 0.135); // 1/e² = 0.135

            // 计算FW95%
            var fw95 = CalculateAngleWidth(divergenceData, maxIntensity * 0.05); // 1-0.95

            // 简单计算能量占比（估算值）
            double? fw1e2PowerContainment = null;
            if (fw1e2.HasValue && fw1e2.Value > 0)
            {
                // 对于高斯分布，1/e²宽度内的能量占比约为86.5%
                fw1e2PowerContainment = 0.865;
            }

            return (fwhm, fw1e2, fw1e2PowerContainment, fw95);
        }
        
        private double? CalculateAngleWidth(List<DataPoint> data, double threshold)
        {
            var aboveThreshold = data.Where(p => p.Y >= threshold).ToList();
            if (!aboveThreshold.Any()) return null;
            
            var minAngle = aboveThreshold.Min(p => p.X);
            var maxAngle = aboveThreshold.Max(p => p.X);
            
            return maxAngle - minAngle;
        }
        
        private double? InterpolateValue(List<DataPoint> data, double targetX)
        {
            if (!data.Any()) return null;
            
            // 找到目标值前后的点
            var sortedData = data.OrderBy(p => p.X).ToList();
            
            if (targetX <= sortedData.First().X) return sortedData.First().Y;
            if (targetX >= sortedData.Last().X) return sortedData.Last().Y;
            
            for (int i = 0; i < sortedData.Count - 1; i++)
            {
                if (sortedData[i].X <= targetX && sortedData[i + 1].X >= targetX)
                {
                    // 线性插值
                    var x1 = sortedData[i].X;
                    var y1 = sortedData[i].Y;
                    var x2 = sortedData[i + 1].X;
                    var y2 = sortedData[i + 1].Y;
                    
                    return y1 + (y2 - y1) * (targetX - x1) / (x2 - x1);
                }
            }
            
            return null;
        }
        
        /// <summary>
        /// 使用指定算法平滑数据
        /// </summary>
        /// <param name="data">原始数据</param>
        /// <param name="config">平滑配置</param>
        /// <returns>平滑后的数据</returns>
        public List<LIVAnalyzer.Models.DataPoint> SmoothData(List<LIVAnalyzer.Models.DataPoint> data, SmoothingConfig config)
        {
            if (data == null || data.Count == 0)
                return data;
            
            try
            {
                // 获取平滑算法
                var algorithm = SmoothingAlgorithmFactory.GetAlgorithm(config.AlgorithmType);
                
                // 验证参数
                var validationError = algorithm.ValidateParameters(config, data.Count);
                if (validationError != null)
                {
                    LoggingService.LogWarning($"平滑参数验证失败: {validationError}，使用默认参数");
                    config = algorithm.GetRecommendedParameters(data.Count);
                }
                
                // 分离X和Y数据
                var xData = data.Select(p => p.X).ToArray();
                var yData = data.Select(p => p.Y).ToArray();
                
                // 应用平滑算法
                var smoothedYData = algorithm.Smooth(xData, yData, config);
                
                // 重构为DataPoint列表
                var smoothedData = new List<LIVAnalyzer.Models.DataPoint>();
                for (int i = 0; i < Math.Min(data.Count, smoothedYData.Length); i++)
                {
                    smoothedData.Add(new LIVAnalyzer.Models.DataPoint(data[i].X, smoothedYData[i]));
                }
                
                LoggingService.LogInformation($"数据平滑完成，使用算法: {algorithm.Name}，数据点数: {smoothedData.Count}");
                return smoothedData;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"数据平滑失败: {ex.Message}");
                return data; // 返回原始数据
            }
        }
        
        /// <summary>
        /// 使用移动平均法平滑数据（保持向后兼容）
        /// </summary>
        /// <param name="data">原始数据</param>
        /// <param name="windowSize">平滑窗口大小（必须为奇数）</param>
        /// <returns>平滑后的数据</returns>
        [Obsolete("请使用 SmoothData(List<DataPoint> data, SmoothingConfig config) 方法")]
        public List<LIVAnalyzer.Models.DataPoint> SmoothData(List<LIVAnalyzer.Models.DataPoint> data, int windowSize)
        {
            var config = new SmoothingConfig
            {
                AlgorithmType = SmoothingAlgorithmType.MovingAverage,
                DefaultWindowSize = windowSize
            };
            
            return SmoothData(data, config);
        }
        
        /// <summary>
        /// 清除缓存 - 虚方法，供派生类重写
        /// </summary>
        public virtual void ClearCache()
        {
            // 基类没有缓存，什么都不做
        }
    }
}