using LIVAnalyzer.Core.Processors;
using LIVAnalyzer.Models;
using Xunit;
using System.Collections.Generic;

namespace LIVAnalyzer.Tests.Core
{
    public class FWHMCalculationTests
    {
        private readonly LIVDataProcessor _processor;
        private readonly OptimizedLIVDataProcessor _optimizedProcessor;
        
        public FWHMCalculationTests()
        {
            _processor = new LIVDataProcessor();
            _optimizedProcessor = new OptimizedLIVDataProcessor();
        }
        
        [Fact]
        public void CalculateFWHM_WithGaussianSpectrum_ReturnsCorrectValue()
        {
            // Arrange - 创建一个高斯形状的光谱
            var wavelengthData = new List<DataPoint>();
            double centerWavelength = 650.0; // nm
            double actualFWHM = 10.0; // nm
            double sigma = actualFWHM / (2.0 * Math.Sqrt(2.0 * Math.Log(2.0))); // 高斯分布的标准差
            
            // 生成高斯分布数据
            for (double wavelength = 630.0; wavelength <= 670.0; wavelength += 0.5)
            {
                double intensity = Math.Exp(-Math.Pow(wavelength - centerWavelength, 2) / (2 * sigma * sigma));
                wavelengthData.Add(new DataPoint(wavelength, intensity));
            }
            
            var data = new LIVMeasurementData
            {
                FileName = "test_gaussian.xlsx",
                WavelengthIntensityData = wavelengthData,
                CurrentPowerData = new List<DataPoint> { new(0.1, 0.1) }, // 最小数据
                CurrentVoltageData = new List<DataPoint> { new(0.1, 0.1) }
            };
            
            // Act - 计算参数
            var result = _processor.CalculateParameters(data);
            
            // Assert
            Assert.NotNull(result);
            Assert.Equal(centerWavelength, result.PeakWavelength, 1); // 峰值波长应该接近中心波长
            Assert.Equal(actualFWHM, result.FWHM, 1); // FWHM应该接近10nm，允许1nm误差
        }
        
        [Fact]
        public void CalculateFWHM_WithAsymmetricSpectrum_ReturnsCorrectValue()
        {
            // Arrange - 创建一个非对称的光谱
            var wavelengthData = new List<DataPoint>
            {
                new(640, 0.1),
                new(642, 0.3),
                new(644, 0.48), // 接近半高
                new(646, 0.7),
                new(648, 0.9),
                new(650, 1.0), // 峰值
                new(652, 0.8),
                new(654, 0.52), // 接近半高
                new(656, 0.3),
                new(658, 0.1)
            };
            
            var data = new LIVMeasurementData
            {
                FileName = "test_asymmetric.xlsx",
                WavelengthIntensityData = wavelengthData,
                CurrentPowerData = new List<DataPoint> { new(0.1, 0.1) },
                CurrentVoltageData = new List<DataPoint> { new(0.1, 0.1) }
            };
            
            // Act
            var result = _processor.CalculateParameters(data);
            
            // Assert
            Assert.NotNull(result);
            Assert.Equal(650, result.PeakWavelength, 0.1);
            // FWHM应该大约是从644nm到654nm，约10nm
            Assert.True(result.FWHM > 9 && result.FWHM < 11, $"FWHM {result.FWHM} should be between 9 and 11");
        }
        
        [Fact]
        public void CalculateFWHM_DifferentSpectrums_ReturnsDifferentValues()
        {
            // Arrange - 创建两个不同宽度的光谱
            var narrowSpectrum = new List<DataPoint>();
            var wideSpectrum = new List<DataPoint>();
            
            // 窄光谱 - FWHM约5nm
            for (double w = 645; w <= 655; w += 0.5)
            {
                double intensity = Math.Exp(-Math.Pow(w - 650, 2) / (2 * 2.1 * 2.1));
                narrowSpectrum.Add(new DataPoint(w, intensity));
            }
            
            // 宽光谱 - FWHM约20nm  
            for (double w = 630; w <= 670; w += 0.5)
            {
                double intensity = Math.Exp(-Math.Pow(w - 650, 2) / (2 * 8.5 * 8.5));
                wideSpectrum.Add(new DataPoint(w, intensity));
            }
            
            var narrowData = new LIVMeasurementData
            {
                FileName = "narrow.xlsx",
                WavelengthIntensityData = narrowSpectrum,
                CurrentPowerData = new List<DataPoint> { new(0.1, 0.1) },
                CurrentVoltageData = new List<DataPoint> { new(0.1, 0.1) }
            };
            
            var wideData = new LIVMeasurementData
            {
                FileName = "wide.xlsx",
                WavelengthIntensityData = wideSpectrum,
                CurrentPowerData = new List<DataPoint> { new(0.1, 0.1) },
                CurrentVoltageData = new List<DataPoint> { new(0.1, 0.1) }
            };
            
            // Act
            var narrowResult = _processor.CalculateParameters(narrowData);
            var wideResult = _processor.CalculateParameters(wideData);
            
            // Assert - 确保不同光谱返回不同的FWHM值
            Assert.NotNull(narrowResult);
            Assert.NotNull(wideResult);
            Assert.True(narrowResult.FWHM < wideResult.FWHM, 
                $"Narrow spectrum FWHM ({narrowResult.FWHM}) should be less than wide spectrum FWHM ({wideResult.FWHM})");
            Assert.True(narrowResult.FWHM > 3 && narrowResult.FWHM < 7, 
                $"Narrow FWHM {narrowResult.FWHM} should be between 3 and 7");
            Assert.True(wideResult.FWHM > 15 && wideResult.FWHM < 25, 
                $"Wide FWHM {wideResult.FWHM} should be between 15 and 25");
        }
        
        [Fact]
        public void OptimizedProcessor_CalculatesFWHM_ConsistentWithBasicProcessor()
        {
            // Arrange
            var wavelengthData = new List<DataPoint>();
            for (double w = 640; w <= 660; w += 0.2)
            {
                double intensity = Math.Exp(-Math.Pow(w - 650, 2) / (2 * 4.2 * 4.2));
                wavelengthData.Add(new DataPoint(w, intensity));
            }
            
            var data = new LIVMeasurementData
            {
                FileName = "test_consistency.xlsx",
                WavelengthIntensityData = wavelengthData,
                CurrentPowerData = new List<DataPoint> { new(0.1, 0.1) },
                CurrentVoltageData = new List<DataPoint> { new(0.1, 0.1) }
            };
            
            // Act
            _optimizedProcessor.ClearCache();
            var basicResult = _processor.CalculateParameters(data);
            var optimizedResult = _optimizedProcessor.CalculateParameters(data);
            
            // Assert - 两个处理器应该返回相同的FWHM值
            Assert.NotNull(basicResult);
            Assert.NotNull(optimizedResult);
            Assert.Equal(basicResult.FWHM, optimizedResult.FWHM, 0.1);
            Assert.Equal(basicResult.PeakWavelength, optimizedResult.PeakWavelength, 0.1);
        }
    }
}