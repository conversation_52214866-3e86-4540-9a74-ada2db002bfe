import sys

with open(r'E:\01LaserPackage\software\LIV_Analyzer\CSharp_Version\LIVAnalyzer.UI\ViewModels\MainWindowViewModel.cs', 'rb') as f:
    content = f.read()
    
# 查找所有#字符的位置
positions = []
for i, byte in enumerate(content):
    if byte == ord('#'):
        # 获取前后的内容
        start = max(0, i-20)
        end = min(len(content), i+20)
        context = content[start:end]
        positions.append((i, context))

# 显示最后几个#的位置
print("Last few # positions:")
for pos, context in positions[-10:]:
    print(f"Position {pos}: {context}")
    
# 检查1922行附近的内容
lines = content.decode('utf-8', errors='replace').split('\n')
print(f"\nLine 1920: {repr(lines[1920])}")
print(f"Line 1921: {repr(lines[1921])}")
print(f"Line 1922: {repr(lines[1922])}")
print(f"Line 1923: {repr(lines[1923])}")