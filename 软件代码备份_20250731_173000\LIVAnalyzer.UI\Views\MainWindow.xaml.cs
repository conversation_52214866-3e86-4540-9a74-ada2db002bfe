using LIVAnalyzer.UI.ViewModels;
using LIVAnalyzer.UI.Services;
using LIVAnalyzer.UI.Views;
using System.Windows;
using System.Windows.Input;
using OxyPlot.Wpf;
using OxyPlot;
using OxyPlot.Axes;
using System.Linq;
using System;
using System.Windows.Controls;

namespace LIVAnalyzer.UI.Views
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window
    {
        public MainWindow()
        {
            InitializeComponent();
            DataContext = new MainWindowViewModel();

            // 确保窗口加载后应用主题
            this.Loaded += (sender, args) =>
            {
                NativeFluentThemeService.Instance.ApplyThemeToWindow(this);

                // 初始化主题按钮图标
                UpdateThemeButtonIcon();

                // 触发图表主题更新
                if (DataContext is MainWindowViewModel viewModel)
                {
                    viewModel.UpdateAllPlotsTheme();
                }
            };
        }

        private void PlotView_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            // 双击功能改为显示提示，主要交互改为右键菜单
            if (sender is PlotView plotView)
            {
                var position = e.GetPosition(plotView);
                var clickedAxis = DetectAxisFromPosition(plotView, position);
                
                if (clickedAxis != null)
                {
                    MessageBox.Show(
                        $"检测到{GetAxisDescription(clickedAxis)}\n\n" +
                        "提示：使用右键菜单可以更方便地设置坐标轴！\n" +
                        "• 右键点击坐标轴区域\n" +
                        "• 选择相应的设置选项",
                        "坐标轴设置提示",
                        MessageBoxButton.OK,
                        MessageBoxImage.Information);
                }
            }
        }
        
        private void PlotView_PreviewMouseRightButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (sender is PlotView plotView && plotView.Model != null)
            {
                var position = e.GetPosition(plotView);
                var chartName = plotView.Tag as string ?? "未知图表";
                
                // 转换为OxyPlot坐标
                var screenPoint = new ScreenPoint(position.X, position.Y);
                
                // 检测点击的轴
                var hitTestResults = plotView.Model.HitTest(new HitTestArguments(screenPoint, 10));
                var clickedAxis = hitTestResults.FirstOrDefault()?.Element as OxyPlot.Axes.Axis;
                
                if (clickedAxis != null)
                {
                    // 直接点击到了轴
                    ShowAxisContextMenu(plotView, clickedAxis, chartName, position);
                }
                else
                {
                    // 没有直接点击轴，尝试根据位置推断
                    var detectedAxis = DetectAxisFromPosition(plotView, position);
                    if (detectedAxis != null)
                    {
                        ShowAxisContextMenu(plotView, detectedAxis, chartName, position);
                    }
                    else
                    {
                        // 显示通用图表菜单
                        ShowGeneralChartContextMenu(plotView, chartName, position);
                    }
                }
            }
            e.Handled = true;
        }
        
        private void ShowAxisContextMenu(PlotView plotView, OxyPlot.Axes.Axis axis, string chartName, Point position)
        {
            if (DataContext is MainWindowViewModel viewModel)
            {
                var contextMenu = AxisContextMenuService.CreateAxisContextMenu(axis, chartName, viewModel);
                contextMenu.PlacementTarget = plotView;
                contextMenu.Placement = System.Windows.Controls.Primitives.PlacementMode.MousePoint;
                contextMenu.IsOpen = true;
            }
        }
        
        private void ShowGeneralChartContextMenu(PlotView plotView, string chartName, Point position)
        {
            var contextMenu = new ContextMenu();
            
            var resetZoomItem = new MenuItem
            {
                Header = "重置缩放",
                Icon = new TextBlock { Text = "🔍", FontSize = 12, Margin = new Thickness(2) }
            };
            resetZoomItem.Click += (s, e) => 
            {
                if (DataContext is MainWindowViewModel vm)
                    vm.ResetZoomCommand.Execute(null);
            };
            contextMenu.Items.Add(resetZoomItem);
            
            contextMenu.PlacementTarget = plotView;
            contextMenu.Placement = System.Windows.Controls.Primitives.PlacementMode.MousePoint;
            contextMenu.IsOpen = true;
        }
        
        private OxyPlot.Axes.Axis? DetectAxisFromPosition(PlotView plotView, System.Windows.Point position)
        {
            if (plotView.Model == null || plotView.Model.Axes == null)
                return null;
                
            var plotArea = plotView.Model.PlotArea;
            const double axisDetectionMargin = 60; // 轴检测边距
            
            // 检查底部X轴区域（图表下方）
            if (position.Y > plotArea.Bottom && position.Y <= plotArea.Bottom + axisDetectionMargin)
            {
                var axis = plotView.Model.Axes.FirstOrDefault(a => a.Position == AxisPosition.Bottom);
                if (axis != null)
                {
                    System.Diagnostics.Debug.WriteLine($"Detected bottom X-axis at position ({position.X:F0}, {position.Y:F0})");
                    return axis;
                }
            }
            
            // 检查顶部X轴区域（图表上方）
            if (position.Y < plotArea.Top && position.Y >= plotArea.Top - axisDetectionMargin)
            {
                var axis = plotView.Model.Axes.FirstOrDefault(a => a.Position == AxisPosition.Top);
                if (axis != null)
                {
                    System.Diagnostics.Debug.WriteLine($"Detected top X-axis at position ({position.X:F0}, {position.Y:F0})");
                    return axis;
                }
            }
            
            // 检查左侧Y轴区域（图表左边）
            if (position.X < plotArea.Left && position.X >= plotArea.Left - axisDetectionMargin)
            {
                var axis = plotView.Model.Axes.FirstOrDefault(a => a.Position == AxisPosition.Left);
                if (axis != null)
                {
                    System.Diagnostics.Debug.WriteLine($"Detected left Y-axis at position ({position.X:F0}, {position.Y:F0})");
                    return axis;
                }
            }
            
            // 检查右侧Y轴区域（图表右边）
            if (position.X > plotArea.Right && position.X <= plotArea.Right + axisDetectionMargin)
            {
                var axis = plotView.Model.Axes.FirstOrDefault(a => a.Position == AxisPosition.Right);
                if (axis != null)
                {
                    System.Diagnostics.Debug.WriteLine($"Detected right Y-axis at position ({position.X:F0}, {position.Y:F0})");
                    return axis;
                }
            }
            
            // 如果在图表内部，尝试更精确的检测
            // 检查是否在轴线附近（图表边缘内部小范围）
            const double edgeDetectionMargin = 20;
            
            // 靠近底边 - 底部X轴
            if (position.Y > plotArea.Bottom - edgeDetectionMargin && position.Y <= plotArea.Bottom)
            {
                var axis = plotView.Model.Axes.FirstOrDefault(a => a.Position == AxisPosition.Bottom);
                if (axis != null)
                {
                    System.Diagnostics.Debug.WriteLine($"Detected bottom X-axis (edge) at position ({position.X:F0}, {position.Y:F0})");
                    return axis;
                }
            }
            
            // 靠近顶边 - 顶部X轴
            if (position.Y < plotArea.Top + edgeDetectionMargin && position.Y >= plotArea.Top)
            {
                var axis = plotView.Model.Axes.FirstOrDefault(a => a.Position == AxisPosition.Top);
                if (axis != null)
                {
                    System.Diagnostics.Debug.WriteLine($"Detected top X-axis (edge) at position ({position.X:F0}, {position.Y:F0})");
                    return axis;
                }
            }
            
            // 靠近左边 - 左侧Y轴
            if (position.X < plotArea.Left + edgeDetectionMargin && position.X >= plotArea.Left)
            {
                var axis = plotView.Model.Axes.FirstOrDefault(a => a.Position == AxisPosition.Left);
                if (axis != null)
                {
                    System.Diagnostics.Debug.WriteLine($"Detected left Y-axis (edge) at position ({position.X:F0}, {position.Y:F0})");
                    return axis;
                }
            }
            
            // 靠近右边 - 右侧Y轴
            if (position.X > plotArea.Right - edgeDetectionMargin && position.X <= plotArea.Right)
            {
                var axis = plotView.Model.Axes.FirstOrDefault(a => a.Position == AxisPosition.Right);
                if (axis != null)
                {
                    System.Diagnostics.Debug.WriteLine($"Detected right Y-axis (edge) at position ({position.X:F0}, {position.Y:F0})");
                    return axis;
                }
            }
            
            System.Diagnostics.Debug.WriteLine($"No axis detected at position ({position.X:F0}, {position.Y:F0}), PlotArea: {plotArea}");
            return null;
        }
        
        private string GetAxisDescription(OxyPlot.Axes.Axis axis)
        {
            var position = axis.Position switch
            {
                AxisPosition.Bottom => "底部",
                AxisPosition.Top => "顶部", 
                AxisPosition.Left => "左侧",
                AxisPosition.Right => "右侧",
                _ => "未知"
            };
            
            var type = axis.Position switch
            {
                AxisPosition.Bottom or AxisPosition.Top => "X轴",
                AxisPosition.Left or AxisPosition.Right => "Y轴",
                _ => "轴"
            };
            
            var title = !string.IsNullOrEmpty(axis.Title) ? $" ({axis.Title})" : "";
            var key = !string.IsNullOrEmpty(axis.Key) ? $" [Key: {axis.Key}]" : "";
            
            return $"{position}{type}{title}{key}";
        }

        private void ThemeToggleButton_Click(object sender, RoutedEventArgs e)
        {
            // 智能主题切换逻辑
            var currentIsDark = NativeFluentThemeService.Instance.IsCurrentlyDarkMode();
            var newTheme = currentIsDark ? "Light" : "Dark";
            App.SwitchTheme(newTheme);

            // 更新按钮图标
            UpdateThemeButtonIcon();

            // 更新图表主题
            if (DataContext is MainWindowViewModel viewModel)
            {
                viewModel.UpdateAllPlotsTheme();
            }
        }

        private void SetLightTheme_Click(object sender, RoutedEventArgs e)
        {
            App.SwitchTheme("Light");
            UpdateThemeButtonIcon();
            if (DataContext is MainWindowViewModel viewModel)
            {
                viewModel.UpdateAllPlotsTheme();
            }
        }

        private void SetDarkTheme_Click(object sender, RoutedEventArgs e)
        {
            App.SwitchTheme("Dark");
            UpdateThemeButtonIcon();
            if (DataContext is MainWindowViewModel viewModel)
            {
                viewModel.UpdateAllPlotsTheme();
            }
        }

        private void SetSystemTheme_Click(object sender, RoutedEventArgs e)
        {
            App.SwitchTheme("System");
            UpdateThemeButtonIcon();
            if (DataContext is MainWindowViewModel viewModel)
            {
                viewModel.UpdateAllPlotsTheme();
            }
        }

        private void UpdateThemeButtonIcon()
        {
            if (ThemeToggleButton != null)
            {
                var isDark = NativeFluentThemeService.Instance.IsCurrentlyDarkMode();
                ThemeToggleButton.Content = isDark ? "☀️" : "🌙";
                ThemeToggleButton.ToolTip = isDark ? "切换到浅色主题" : "切换到深色主题";

                // 强制刷新按钮样式
                ThemeToggleButton.InvalidateVisual();
            }
        }
    }
}