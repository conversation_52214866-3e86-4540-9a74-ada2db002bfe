@echo off
echo Checking .NET SDK...
dotnet --version
if %errorlevel% neq 0 (
    echo.
    echo ERROR: .NET SDK not installed!
    echo Please download from: https://dotnet.microsoft.com/download/dotnet/6.0
    echo Install ".NET 6.0 SDK" and try again.
    pause
    exit /b 1
)

echo.
echo Restoring packages...
dotnet restore

echo.
echo Building project...
dotnet build

echo.
echo Starting LIV Analyzer...
dotnet run --project LIVAnalyzer.UI

pause