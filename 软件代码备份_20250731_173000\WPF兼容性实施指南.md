# WPF 全面兼容性实施指南

## 1. WPF 特性兼容性检查

### 当前项目状态
- 使用 ModernWPF UI 库
- 基于 .NET 6.0
- 使用 MVVM 模式（CommunityToolkit.Mvvm）

### 需要调整的关键领域

## 2. UI/UX 兼容性

### 2.1 样式和主题
```xml
<!-- App.xaml - 确保完整的WPF样式支持 -->
<Application x:Class="LIVAnalyzer.UI.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:ui="http://schemas.modernwpf.com/2019">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- ModernWPF 资源 -->
                <ui:ThemeResources />
                <ui:XamlControlsResources />
                
                <!-- 自定义WPF资源 -->
                <ResourceDictionary Source="Themes/CustomTheme.xaml"/>
                
                <!-- WPF 原生样式覆盖 -->
                <ResourceDictionary Source="Themes/WpfCompatibility.xaml"/>
            </ResourceDictionary.MergedDictionaries>
            
            <!-- 确保WPF控件样式 -->
            <Style TargetType="{x:Type Window}">
                <Setter Property="Background" Value="{DynamicResource SystemControlBackgroundAltHighBrush}"/>
                <Setter Property="Foreground" Value="{DynamicResource SystemControlForegroundBaseHighBrush}"/>
            </Style>
        </ResourceDictionary>
    </Application.Resources>
</Application>
```

### 2.2 创建WPF兼容性资源字典
```xml
<!-- Themes/WpfCompatibility.xaml -->
<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    
    <!-- 确保所有WPF原生控件都有合适的样式 -->
    
    <!-- DataGrid 兼容性 -->
    <Style TargetType="{x:Type DataGrid}" BasedOn="{StaticResource DefaultDataGridStyle}">
        <Setter Property="AutoGenerateColumns" Value="False"/>
        <Setter Property="CanUserAddRows" Value="False"/>
        <Setter Property="GridLinesVisibility" Value="Horizontal"/>
    </Style>
    
    <!-- TreeView 兼容性 -->
    <Style TargetType="{x:Type TreeView}" BasedOn="{StaticResource DefaultTreeViewStyle}">
        <Setter Property="ScrollViewer.HorizontalScrollBarVisibility" Value="Auto"/>
        <Setter Property="ScrollViewer.VerticalScrollBarVisibility" Value="Auto"/>
    </Style>
    
    <!-- ContextMenu 兼容性 -->
    <Style TargetType="{x:Type ContextMenu}" BasedOn="{StaticResource DefaultContextMenuStyle}">
        <Setter Property="OverridesDefaultStyle" Value="True"/>
    </Style>
    
    <!-- ToolTip 兼容性 -->
    <Style TargetType="{x:Type ToolTip}" BasedOn="{StaticResource DefaultToolTipStyle}">
        <Setter Property="Placement" Value="Bottom"/>
    </Style>
</ResourceDictionary>
```

## 3. 控件兼容性

### 3.1 创建WPF控件包装器
```csharp
using System.Windows;
using System.Windows.Controls;
using ModernWpf.Controls;

namespace LIVAnalyzer.UI.Controls
{
    /// <summary>
    /// WPF兼容的NavigationView包装器
    /// </summary>
    public class WpfNavigationView : NavigationView
    {
        static WpfNavigationView()
        {
            DefaultStyleKeyProperty.OverrideMetadata(
                typeof(WpfNavigationView),
                new FrameworkPropertyMetadata(typeof(WpfNavigationView)));
        }

        protected override void OnApplyTemplate()
        {
            base.OnApplyTemplate();
            
            // 确保WPF特定功能
            this.Focusable = true;
            this.IsTabStop = true;
            
            // 处理WPF特定的输入
            this.PreviewKeyDown += OnPreviewKeyDown;
        }

        private void OnPreviewKeyDown(object sender, System.Windows.Input.KeyEventArgs e)
        {
            // 处理WPF键盘导航
            if (e.Key == System.Windows.Input.Key.Tab)
            {
                // 自定义Tab导航逻辑
            }
        }
    }
}
```

### 3.2 数据绑定兼容性
```csharp
using System.Windows;
using System.Windows.Data;

namespace LIVAnalyzer.UI.Converters
{
    /// <summary>
    /// WPF兼容的值转换器基类
    /// </summary>
    public abstract class WpfValueConverter : IValueConverter
    {
        public abstract object Convert(object value, Type targetType, 
            object parameter, CultureInfo culture);
            
        public abstract object ConvertBack(object value, Type targetType, 
            object parameter, CultureInfo culture);
            
        // 提供WPF特定的错误处理
        protected object HandleConversionError(Exception ex)
        {
            // WPF的DependencyProperty.UnsetValue
            return DependencyProperty.UnsetValue;
        }
    }
}
```

## 4. 输入处理兼容性

### 4.1 命令处理
```csharp
using System.Windows.Input;
using CommunityToolkit.Mvvm.Input;

namespace LIVAnalyzer.UI.Commands
{
    /// <summary>
    /// WPF兼容的命令实现
    /// </summary>
    public class WpfRelayCommand : ICommand
    {
        private readonly RelayCommand _relayCommand;
        
        public WpfRelayCommand(Action execute, Func<bool>? canExecute = null)
        {
            _relayCommand = new RelayCommand(execute, canExecute);
        }
        
        public event EventHandler? CanExecuteChanged
        {
            add => CommandManager.RequerySuggested += value;
            remove => CommandManager.RequerySuggested -= value;
        }
        
        public bool CanExecute(object? parameter)
        {
            return _relayCommand.CanExecute(parameter);
        }
        
        public void Execute(object? parameter)
        {
            _relayCommand.Execute(parameter);
        }
        
        // WPF特定：触发CanExecute重新评估
        public static void InvalidateRequerySuggested()
        {
            CommandManager.InvalidateRequerySuggested();
        }
    }
}
```

### 4.2 拖放支持
```csharp
namespace LIVAnalyzer.UI.Behaviors
{
    /// <summary>
    /// WPF拖放行为
    /// </summary>
    public static class DragDropBehavior
    {
        public static readonly DependencyProperty EnableDragDropProperty =
            DependencyProperty.RegisterAttached(
                "EnableDragDrop",
                typeof(bool),
                typeof(DragDropBehavior),
                new PropertyMetadata(false, OnEnableDragDropChanged));
                
        public static bool GetEnableDragDrop(DependencyObject obj)
        {
            return (bool)obj.GetValue(EnableDragDropProperty);
        }
        
        public static void SetEnableDragDrop(DependencyObject obj, bool value)
        {
            obj.SetValue(EnableDragDropProperty, value);
        }
        
        private static void OnEnableDragDropChanged(
            DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is UIElement element)
            {
                if ((bool)e.NewValue)
                {
                    element.AllowDrop = true;
                    element.DragEnter += OnDragEnter;
                    element.DragOver += OnDragOver;
                    element.Drop += OnDrop;
                }
                else
                {
                    element.AllowDrop = false;
                    element.DragEnter -= OnDragEnter;
                    element.DragOver -= OnDragOver;
                    element.Drop -= OnDrop;
                }
            }
        }
        
        private static void OnDragEnter(object sender, DragEventArgs e)
        {
            if (e.Data.GetDataPresent(DataFormats.FileDrop))
            {
                e.Effects = DragDropEffects.Copy;
            }
            else
            {
                e.Effects = DragDropEffects.None;
            }
        }
        
        private static void OnDragOver(object sender, DragEventArgs e)
        {
            OnDragEnter(sender, e);
        }
        
        private static void OnDrop(object sender, DragEventArgs e)
        {
            if (e.Data.GetDataPresent(DataFormats.FileDrop))
            {
                string[] files = (string[])e.Data.GetData(DataFormats.FileDrop);
                // 处理拖放的文件
                if (sender is FrameworkElement element && 
                    element.DataContext is MainWindowViewModel vm)
                {
                    vm.LoadFilesCommand.Execute(files);
                }
            }
        }
    }
}
```

## 5. 性能优化

### 5.1 虚拟化支持
```xml
<!-- 确保列表控件使用虚拟化 -->
<ListBox VirtualizingPanel.IsVirtualizing="True"
         VirtualizingPanel.VirtualizationMode="Recycling"
         ScrollViewer.IsDeferredScrollingEnabled="True">
    <ListBox.ItemsPanel>
        <ItemsPanelTemplate>
            <VirtualizingStackPanel />
        </ItemsPanelTemplate>
    </ListBox.ItemsPanel>
</ListBox>
```

### 5.2 异步加载
```csharp
public class WpfAsyncDataLoader
{
    private readonly Dispatcher _dispatcher;
    
    public WpfAsyncDataLoader()
    {
        _dispatcher = Application.Current.Dispatcher;
    }
    
    public async Task LoadDataAsync<T>(
        Func<Task<T>> loadFunc, 
        Action<T> updateUI)
    {
        var data = await Task.Run(loadFunc);
        
        // 确保UI更新在主线程
        await _dispatcher.InvokeAsync(() => updateUI(data));
    }
}
```

## 6. 打印支持

### 6.1 WPF打印功能
```csharp
using System.Windows.Documents;
using System.Windows.Xps;
using System.Printing;

namespace LIVAnalyzer.UI.Services
{
    public class WpfPrintService
    {
        public void PrintDocument(FlowDocument document)
        {
            PrintDialog printDialog = new PrintDialog();
            if (printDialog.ShowDialog() == true)
            {
                // 创建分页器
                IDocumentPaginatorSource idpSource = document;
                printDialog.PrintDocument(
                    idpSource.DocumentPaginator, 
                    "LIV Analysis Report");
            }
        }
        
        public void PrintVisual(Visual visual, string description)
        {
            PrintDialog printDialog = new PrintDialog();
            if (printDialog.ShowDialog() == true)
            {
                printDialog.PrintVisual(visual, description);
            }
        }
    }
}
```

## 7. 动画和过渡

### 7.1 WPF动画支持
```csharp
using System.Windows.Media.Animation;

namespace LIVAnalyzer.UI.Animations
{
    public static class WpfAnimations
    {
        public static Storyboard CreateFadeIn(DependencyObject target)
        {
            var storyboard = new Storyboard();
            var animation = new DoubleAnimation
            {
                From = 0.0,
                To = 1.0,
                Duration = new Duration(TimeSpan.FromMilliseconds(300)),
                EasingFunction = new CubicEase { EasingMode = EasingMode.EaseOut }
            };
            
            Storyboard.SetTarget(animation, target);
            Storyboard.SetTargetProperty(animation, 
                new PropertyPath(UIElement.OpacityProperty));
            storyboard.Children.Add(animation);
            
            return storyboard;
        }
        
        public static Storyboard CreateSlideIn(DependencyObject target)
        {
            var storyboard = new Storyboard();
            var transform = new TranslateTransform();
            
            if (target is FrameworkElement element)
            {
                element.RenderTransform = transform;
            }
            
            var animation = new DoubleAnimation
            {
                From = 100,
                To = 0,
                Duration = new Duration(TimeSpan.FromMilliseconds(400)),
                EasingFunction = new ExponentialEase { EasingMode = EasingMode.EaseOut }
            };
            
            Storyboard.SetTarget(animation, transform);
            Storyboard.SetTargetProperty(animation, 
                new PropertyPath(TranslateTransform.XProperty));
            storyboard.Children.Add(animation);
            
            return storyboard;
        }
    }
}
```

## 8. 高DPI支持

### 8.1 应用程序清单
```xml
<!-- app.manifest -->
<application xmlns="urn:schemas-microsoft-com:asm.v3">
  <windowsSettings>
    <dpiAware xmlns="http://schemas.microsoft.com/SMI/2005/WindowsSettings">true/PM</dpiAware>
    <dpiAwareness xmlns="http://schemas.microsoft.com/SMI/2016/WindowsSettings">PerMonitorV2</dpiAwareness>
  </windowsSettings>
</application>
```

### 8.2 DPI感知处理
```csharp
public class DpiHelper
{
    public static double GetDpiScale()
    {
        var source = PresentationSource.FromVisual(Application.Current.MainWindow);
        return source?.CompositionTarget?.TransformToDevice.M11 ?? 1.0;
    }
    
    public static Size GetScaledSize(Size size)
    {
        var scale = GetDpiScale();
        return new Size(size.Width * scale, size.Height * scale);
    }
}
```

## 9. 辅助功能支持

### 9.1 自动化支持
```csharp
using System.Windows.Automation;

public class AccessibilityHelper
{
    public static void SetAccessibilityProperties(UIElement element, 
        string name, string helpText)
    {
        AutomationProperties.SetName(element, name);
        AutomationProperties.SetHelpText(element, helpText);
        AutomationProperties.SetLiveSetting(element, 
            AutomationLiveSetting.Polite);
    }
}
```

## 10. 部署考虑

### 10.1 项目文件更新
```xml
<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <UseWPF>true</UseWPF>
    <UseWindowsForms>false</UseWindowsForms>
    
    <!-- WPF特定设置 -->
    <ApplicationManifest>app.manifest</ApplicationManifest>
    <ApplicationIcon>app.ico</ApplicationIcon>
    
    <!-- 启用WPF设计器 -->
    <DesignerSupport>true</DesignerSupport>
    
    <!-- 单文件发布支持 -->
    <PublishSingleFile>true</PublishSingleFile>
    <SelfContained>true</SelfContained>
    <RuntimeIdentifier>win-x64</RuntimeIdentifier>
  </PropertyGroup>
</Project>
```

## 实施步骤

1. **评估现有代码**：检查所有UI组件的WPF兼容性
2. **创建兼容层**：实现上述包装器和辅助类
3. **更新资源文件**：确保所有样式都兼容WPF
4. **测试各项功能**：特别是拖放、打印、动画等WPF特性
5. **性能调优**：使用WPF性能分析工具
6. **辅助功能测试**：确保符合无障碍标准

这样就能确保应用程序完全兼容WPF的所有特性和功能。