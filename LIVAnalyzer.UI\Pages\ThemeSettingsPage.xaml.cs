using System;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using LIVAnalyzer.UI.Services;

namespace LIVAnalyzer.UI.Pages
{
    /// <summary>
    /// ThemeSettingsPage.xaml 的交互逻辑
    /// </summary>
    public partial class ThemeSettingsPage : Page
    {
        public ThemeSettingsPage()
        {
            InitializeComponent();
            InitializeControls();
        }

        private void InitializeControls()
        {
            // 设置当前主题 - 默认深色主题
            foreach (RadioButton rb in ThemeRadioButtons.Children.OfType<RadioButton>())
            {
                if (rb.Tag?.ToString() == "Dark")
                {
                    rb.IsChecked = true;
                    break;
                }
            }

            // 设置缩放
            var scale = GetCurrentScale();
            ScaleSlider.Value = scale;
            UpdateScaleText(scale);
        }

        private void ThemeRadioButton_Checked(object sender, RoutedEventArgs e)
        {
            if (sender is RadioButton radioButton)
            {
                var tag = radioButton.Tag?.ToString();
                App.SwitchTheme(tag ?? "System");
                SaveSettings();
            }
        }

        private void AccentColor_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is string colorString)
            {
                var color = (Color)ColorConverter.ConvertFromString(colorString);
                CustomColorButton.Background = new SolidColorBrush(color);
                SaveSettings();
            }
        }

        private void CustomColorButton_Click(object sender, RoutedEventArgs e)
        {
            // 简化的颜色选择 - 循环几种预设颜色
            var colors = new[]
            {
                Color.FromRgb(0x00, 0x84, 0xFF), // 蓝色
                Color.FromRgb(0x00, 0x7A, 0xCC), // 深蓝
                Color.FromRgb(0x10, 0x7C, 0x10), // 绿色
                Color.FromRgb(0xFF, 0x8C, 0x00), // 橙色
                Color.FromRgb(0xE7, 0x4C, 0x3C), // 红色
                Color.FromRgb(0x9B, 0x59, 0xB6)  // 紫色
            };

            var currentColor = ((SolidColorBrush)CustomColorButton.Background).Color;
            var currentIndex = Array.FindIndex(colors, c => c.R == currentColor.R && c.G == currentColor.G && c.B == currentColor.B);
            var nextIndex = (currentIndex + 1) % colors.Length;
            var newColor = colors[nextIndex];

            CustomColorButton.Background = new SolidColorBrush(newColor);
            SaveSettings();
        }

        private void ScaleSlider_ValueChanged(object sender, RoutedPropertyChangedEventArgs<double> e)
        {
            UpdateScaleText(e.NewValue);
            ApplyScale(e.NewValue);
            SaveSettings();
        }

        private void UpdateScaleText(double scale)
        {
            if (ScaleText != null)
            {
                ScaleText.Text = $"{(int)(scale * 100)}%";
            }
        }

        private double GetCurrentScale()
        {
            // 获取当前应用的缩放比例
            if (Application.Current.MainWindow != null)
            {
                var transform = Application.Current.MainWindow.LayoutTransform as ScaleTransform;
                return transform?.ScaleX ?? 1.0;
            }
            return 1.0;
        }

        private void ApplyScale(double scale)
        {
            // 应用缩放到所有窗口
            foreach (Window window in Application.Current.Windows)
            {
                window.LayoutTransform = new ScaleTransform(scale, scale);
            }
        }

        private void ChartThemeCombo_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // 这里可以切换图表配色方案
            if (ChartThemeCombo.SelectedIndex >= 0)
            {
                // 应用新的图表配色
                ApplyChartColorScheme(ChartThemeCombo.SelectedIndex);
                SaveSettings();
            }
        }

        private void ApplyChartColorScheme(int schemeIndex)
        {
            // 根据选择应用不同的配色方案
            var colors = schemeIndex switch
            {
                0 => new[] { "#4FC3F7", "#81C784", "#FFB74D", "#E57373", "#BA68C8" }, // 默认
                1 => new[] { "#F44336", "#FF9800", "#FFEB3B", "#4CAF50", "#2196F3" }, // 彩虹
                2 => new[] { "#1976D2", "#1E88E5", "#2196F3", "#42A5F5", "#64B5F6" }, // 单色渐变
                3 => new[] { "#000000", "#FFFFFF", "#FF0000", "#00FF00", "#0000FF" }, // 高对比度
                4 => new[] { "#648FFF", "#785EF0", "#DC267F", "#FE6100", "#FFB000" }, // 色盲友好
                _ => new[] { "#4FC3F7", "#81C784", "#FFB74D", "#E57373", "#BA68C8" }
            };

            // 更新预览
            var preview = this.FindName("ColorPreview") as WrapPanel;
            if (preview != null)
            {
                preview.Children.Clear();
                foreach (var color in colors)
                {
                    preview.Children.Add(new System.Windows.Shapes.Rectangle
                    {
                        Width = 40,
                        Height = 20,
                        Fill = new SolidColorBrush((Color)ColorConverter.ConvertFromString(color)),
                        Margin = new Thickness(2)
                    });
                }
            }

            // 这里可以将配色方案应用到实际的图表中
        }

        private void ResetButton_Click(object sender, RoutedEventArgs e)
        {
            // 恢复默认设置
            App.SwitchTheme("System");
            ScaleSlider.Value = 1.0;
            ChartThemeCombo.SelectedIndex = 0;

            InitializeControls();
            SaveSettings();
        }

        private void SaveSettings()
        {
            // 保存用户偏好设置
            try
            {
                var checkedTheme = "System";
                foreach (RadioButton rb in ThemeRadioButtons.Children.OfType<RadioButton>())
                {
                    if (rb.IsChecked == true)
                    {
                        checkedTheme = rb.Tag?.ToString() ?? "System";
                        break;
                    }
                }
                
                var settings = new
                {
                    Theme = checkedTheme,
                    AccentColor = "#0084FF",
                    UIScale = ScaleSlider.Value,
                    ChartColorScheme = ChartThemeCombo.SelectedIndex
                };

                // 这里可以将设置保存到配置文件
                System.IO.File.WriteAllText("theme-settings.json", 
                    System.Text.Json.JsonSerializer.Serialize(settings));
            }
            catch
            {
                // 忽略保存错误
            }
        }
    }
}