@echo off
echo Cleaning and rebuilding LIV Analyzer...
cd /d "E:\01LaserPackage\software\LIV_Analyzer\CSharp_Version"

echo Cleaning solution...
dotnet clean

echo Restoring packages...
dotnet restore

echo Building solution...
dotnet build --configuration Release

echo.
echo Build complete! Starting application...
cd LIVAnalyzer.UI\bin\Release\net6.0-windows

if exist LIVAnalyzer.exe (
    echo Starting LIVAnalyzer.exe...
    LIVAnalyzer.exe
) else (
    echo ERROR: LIVAnalyzer.exe not found!
    echo Trying Debug build...
    cd ..\..\Debug\net6.0-windows
    if exist LIVAnalyzer.exe (
        echo Starting Debug version...
        LIVAnalyzer.exe
    ) else (
        echo ERROR: No executable found!
    )
)

echo.
echo Application closed.
pause