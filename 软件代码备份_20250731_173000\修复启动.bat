@echo off
echo ====================================
echo   LIV Analyzer C# Version - Fixed
echo ====================================
echo.

echo Checking .NET SDK...
dotnet --version
if %errorlevel% neq 0 (
    echo ERROR: .NET SDK not installed!
    pause
    exit /b 1
)

echo.
echo Cleaning previous build...
dotnet clean

echo.
echo Clearing NuGet cache...
dotnet nuget locals all --clear

echo.
echo Restoring packages...
dotnet restore --force

echo.
echo Building project...
dotnet build --configuration Release

if %errorlevel% neq 0 (
    echo.
    echo BUILD FAILED! Please check the errors above.
    pause
    exit /b 1
)

echo.
echo Starting LIV Analyzer...
dotnet run --project LIVAnalyzer.UI --configuration Release

pause