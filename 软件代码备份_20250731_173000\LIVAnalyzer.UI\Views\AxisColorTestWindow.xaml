<Window x:Class="LIVAnalyzer.UI.Views.AxisColorTestWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:oxy="http://oxyplot.org/wpf"
        Title="坐标轴颜色测试" Height="600" Width="800"
        WindowStartupLocation="CenterOwner">
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- 标题 -->
        <TextBlock Grid.Row="0" Text="坐标轴颜色测试 - 深色模式轴线可见性" 
                   FontSize="18" FontWeight="Bold" Margin="0,0,0,20"/>
        
        <!-- 控制面板 -->
        <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="0,0,0,20">
            <Button x:Name="ToggleThemeBtn" Content="切换主题" Click="ToggleThemeBtn_Click" 
                    Width="100" Margin="0,0,10,0"/>
            <Button x:Name="RefreshPlotBtn" Content="刷新图表" Click="RefreshPlotBtn_Click" 
                    Width="100" Margin="0,0,10,0"/>
            <TextBlock x:Name="ThemeStatusText" Text="当前主题: 浅色" 
                       VerticalAlignment="Center" Margin="10,0,0,0"/>
        </StackPanel>
        
        <!-- 图表区域 -->
        <Grid Grid.Row="2">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            
            <!-- 左侧图表 -->
            <GroupBox Grid.Column="0" Header="测试图表 1" Margin="0,0,10,0">
                <oxy:PlotView x:Name="TestPlot1" Background="Transparent"/>
            </GroupBox>
            
            <!-- 右侧图表 -->
            <GroupBox Grid.Column="1" Header="测试图表 2" Margin="10,0,0,0">
                <oxy:PlotView x:Name="TestPlot2" Background="Transparent"/>
            </GroupBox>
        </Grid>
        
        <!-- 信息面板 -->
        <GroupBox Grid.Row="3" Header="调试信息" Margin="0,20,0,0" Height="150">
            <ScrollViewer VerticalScrollBarVisibility="Auto">
                <TextBlock x:Name="DebugInfo" FontFamily="Consolas" FontSize="10" 
                           Background="LightGray" Padding="5" TextWrapping="Wrap"/>
            </ScrollViewer>
        </GroupBox>
    </Grid>
</Window>
