<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- 参数组样式 - 使用应用主题资源 -->
    <Style x:Key="ParameterGroupBox" TargetType="GroupBox">
        <Setter Property="Margin" Value="5"/>
        <Setter Property="Padding" Value="8"/>
        <Setter Property="BorderBrush" Value="{DynamicResource AppBorderBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Background" Value="{DynamicResource AppControlBackgroundBrush}"/>
        <Setter Property="Foreground" Value="{DynamicResource AppForegroundBrush}"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="GroupBox">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="8"
                            Margin="{TemplateBinding Margin}">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>
                            <ContentPresenter Grid.Row="0"
                                            ContentSource="Header"
                                            Margin="8,4"/>
                            <ContentPresenter Grid.Row="1"
                                            Margin="{TemplateBinding Padding}"/>
                        </Grid>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 工具栏按钮样式 - 使用系统兼容资源 -->
    <Style x:Key="ToolbarButton" TargetType="Button">
        <Setter Property="Margin" Value="5,2"/>
        <Setter Property="Padding" Value="12,6"/>
        <Setter Property="MinHeight" Value="32"/>
        <Setter Property="MinWidth" Value="80"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Background" Value="{DynamicResource {x:Static SystemColors.HighlightBrushKey}}"/>
        <Setter Property="Foreground" Value="{DynamicResource {x:Static SystemColors.HighlightTextBrushKey}}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource {x:Static SystemColors.HighlightBrushKey}}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="4"
                            Padding="{TemplateBinding Padding}">
                        <ContentPresenter HorizontalAlignment="Center"
                                        VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="{DynamicResource {x:Static SystemColors.HotTrackBrushKey}}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" Value="{DynamicResource {x:Static SystemColors.ActiveCaptionBrushKey}}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 状态文本样式 - 使用应用主题资源 -->
    <Style x:Key="StatusText" TargetType="TextBlock">
        <Setter Property="Foreground" Value="{DynamicResource AppForegroundBrush}"/>
        <Setter Property="Margin" Value="5,2"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="FontWeight" Value="Normal"/>
    </Style>

    <!-- 主题响应式按钮样式 -->
    <Style x:Key="WhiteButton" TargetType="Button">
        <Setter Property="Background" Value="{DynamicResource AppControlBackgroundBrush}"/>
        <Setter Property="Foreground" Value="{DynamicResource AppForegroundBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource AppBorderBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Padding" Value="12,0"/>
        <Setter Property="Height" Value="32"/>
        <Setter Property="FontWeight" Value="Normal"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="8">
                        <ContentPresenter HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Margin="{TemplateBinding Padding}"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="{DynamicResource AppBorderBrush}"/>
                            <Setter Property="BorderBrush" Value="{DynamicResource AppForegroundBrush}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" Value="{DynamicResource AppBorderBrush}"/>
                            <Setter Property="BorderBrush" Value="{DynamicResource AppForegroundBrush}"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Background" Value="{DynamicResource AppControlBackgroundBrush}"/>
                            <Setter Property="Foreground" Value="{DynamicResource AppBorderBrush}"/>
                            <Setter Property="BorderBrush" Value="{DynamicResource AppBorderBrush}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 主题响应式TabControl样式 -->
    <Style x:Key="WhiteTabControl" TargetType="TabControl">
        <Setter Property="Background" Value="{DynamicResource AppBackgroundBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource AppBorderBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
    </Style>

    <!-- 主题响应式TabItem样式 -->
    <Style x:Key="WhiteTabItem" TargetType="TabItem">
        <Setter Property="Background" Value="{DynamicResource AppControlBackgroundBrush}"/>
        <Setter Property="Foreground" Value="{DynamicResource AppForegroundBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource AppBorderBrush}"/>
        <Setter Property="BorderThickness" Value="1,1,1,0"/>
        <Setter Property="Padding" Value="12,8"/>
        <Setter Property="Margin" Value="0,0,2,0"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="TabItem">
                    <Border x:Name="Border"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="4,4,0,0"
                            Margin="{TemplateBinding Margin}">
                        <ContentPresenter x:Name="ContentSite"
                                        VerticalAlignment="Center"
                                        HorizontalAlignment="Center"
                                        ContentSource="Header"
                                        Margin="{TemplateBinding Padding}"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <!-- 鼠标悬停状态 -->
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="Border" Property="Background" Value="{DynamicResource AppBorderBrush}"/>
                        </Trigger>
                        <!-- 选中状态 - 关键修复 -->
                        <Trigger Property="IsSelected" Value="True">
                            <Setter TargetName="Border" Property="Background" Value="{DynamicResource AppBackgroundBrush}"/>
                            <Setter TargetName="Border" Property="BorderBrush" Value="{DynamicResource AppBorderBrush}"/>
                            <Setter TargetName="Border" Property="BorderThickness" Value="1,1,1,0"/>
                            <Setter Property="Panel.ZIndex" Value="100"/>
                        </Trigger>
                        <!-- 选中且鼠标悬停状态 -->
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsSelected" Value="True"/>
                                <Condition Property="IsMouseOver" Value="True"/>
                            </MultiTrigger.Conditions>
                            <Setter TargetName="Border" Property="Background" Value="{DynamicResource AppBackgroundBrush}"/>
                            <Setter TargetName="Border" Property="Opacity" Value="0.9"/>
                        </MultiTrigger>
                        <!-- 禁用状态 -->
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Opacity" Value="0.5"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 简化的TextBox样式 - 与按钮高度完全一致 -->
    <Style x:Key="RoundedTextBox" TargetType="TextBox">
        <Setter Property="Background" Value="{DynamicResource AppControlBackgroundBrush}"/>
        <Setter Property="Foreground" Value="{DynamicResource AppForegroundBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource AppBorderBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Padding" Value="8,0"/>
        <Setter Property="Height" Value="32"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="TextBox">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="8"
                            Height="{TemplateBinding Height}">
                        <ScrollViewer x:Name="PART_ContentHost"
                                    Margin="{TemplateBinding Padding}"
                                    VerticalAlignment="Center"/>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 统一高度的Slider样式 -->
    <Style x:Key="UniformSlider" TargetType="Slider">
        <Setter Property="Height" Value="32"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
    </Style>

    <!-- 主题响应式CheckBox样式 -->
    <Style x:Key="UniformCheckBox" TargetType="CheckBox" BasedOn="{StaticResource {x:Type CheckBox}}">
        <Setter Property="Height" Value="32"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="Foreground" Value="{DynamicResource AppForegroundBrush}"/>
    </Style>

    <!-- 统一高度的RadioButton样式 -->
    <Style x:Key="UniformRadioButton" TargetType="RadioButton">
        <Setter Property="Height" Value="32"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="Foreground" Value="{DynamicResource AppForegroundBrush}"/>
    </Style>

    <!-- 简化的Expander样式 -->
    <Style TargetType="Expander">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="Foreground" Value="{DynamicResource AppForegroundBrush}"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Margin" Value="0,4"/>
        <Setter Property="Padding" Value="0"/>

    </Style>

    <!-- 主题响应式参数面板TextBlock样式 -->
    <Style x:Key="ParameterTextBlock" TargetType="TextBlock">
        <Setter Property="Foreground" Value="{DynamicResource AppForegroundBrush}"/>
        <Setter Property="FontSize" Value="15"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
    </Style>

    <!-- 主题响应式参数标签样式 -->
    <Style x:Key="ParameterLabel" TargetType="TextBlock">
        <Setter Property="Foreground" Value="{DynamicResource AppForegroundBrush}"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
        <Setter Property="Margin" Value="0,0,8,0"/>
    </Style>

    <!-- 主题响应式参数值样式 -->
    <Style x:Key="ParameterValue" TargetType="TextBlock">
        <Setter Property="Foreground" Value="{DynamicResource AppForegroundBrush}"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="FontWeight" Value="Normal"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
    </Style>

    <!-- 主题响应式菜单样式 -->
    <Style TargetType="Menu">
        <Setter Property="Background" Value="{DynamicResource AppControlBackgroundBrush}"/>
        <Setter Property="Foreground" Value="{DynamicResource AppForegroundBrush}"/>
    </Style>

    <!-- 主题响应式菜单项样式 -->
    <Style TargetType="MenuItem">
        <Setter Property="Background" Value="{DynamicResource AppControlBackgroundBrush}"/>
        <Setter Property="Foreground" Value="{DynamicResource AppForegroundBrush}"/>
    </Style>

</ResourceDictionary>