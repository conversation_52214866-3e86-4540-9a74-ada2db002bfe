namespace LIVAnalyzer.Models
{
    /// <summary>
    /// 发散角分析结果模型
    /// </summary>
    public class DivergenceResults
    {
        /// <summary>
        /// 水平方向发散角 FWHM (度)
        /// </summary>
        public double? HorizontalFWHM { get; set; }
        
        /// <summary>
        /// 水平方向发散角 FW(1/e²) (度)
        /// </summary>
        public double? HorizontalFW1e2 { get; set; }
        
        /// <summary>
        /// 水平方向发散角 FW(1/e²) 能量占比
        /// </summary>
        public double? HorizontalFW1e2PowerContainment { get; set; }
        
        /// <summary>
        /// 水平方向发散角 FW95% (度)
        /// </summary>
        public double? HorizontalFW95 { get; set; }
        
        /// <summary>
        /// 垂直方向发散角 FWHM (度)
        /// </summary>
        public double? VerticalFWHM { get; set; }
        
        /// <summary>
        /// 垂直方向发散角 FW(1/e²) (度)
        /// </summary>
        public double? VerticalFW1e2 { get; set; }
        
        /// <summary>
        /// 垂直方向发散角 FW(1/e²) 能量占比
        /// </summary>
        public double? VerticalFW1e2PowerContainment { get; set; }
        
        /// <summary>
        /// 垂直方向发散角 FW95% (度)
        /// </summary>
        public double? VerticalFW95 { get; set; }
    }
}