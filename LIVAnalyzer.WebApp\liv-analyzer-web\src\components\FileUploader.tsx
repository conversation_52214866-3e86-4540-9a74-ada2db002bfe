import { useRef, useState } from 'react';
import { Button } from './ui/button';
import { Progress } from './ui/progress';
import { useAppStore } from '../state/store';
import { parseCSV, parseExcel, toLIVDataFromSheets } from '../services/fileService';
import type { LIVData } from '../types/data';
import { ParserWorkerClient } from '../services/workerClient';
import { preprocessLIVData } from '../services/preprocess';
import { summarizeValidation, validateXY } from '../services/validation';
import { isFSAccessSupported, pickFileWithStartIn } from '../services/fsAccess';
import { ComputeWorkerClient } from '../services/computeClient';

function inferCsvTarget(filename: string): 'power' | 'voltage' | 'wavelength' {
  const lower = filename.toLowerCase();
  if (lower.includes('volt')) return 'voltage';
  if (lower.includes('wave') || lower.includes('spectrum')) return 'wavelength';
  return 'power';
}

function toLIVDataFromCsv(rows: number[][], target: 'power' | 'voltage' | 'wavelength'): LIVData {
  const x = rows.map((r) => r[0]).filter((v) => Number.isFinite(v));
  const y = rows.map((r) => r[1]).filter((v) => Number.isFinite(v));
  if (target === 'power') return { power: { current: x, power: y } };
  if (target === 'voltage') return { voltage: { current: x, voltage: y } };
  return { wavelength: { wavelength: x, intensity: y } };
}

export default function FileUploader() {
  const { setData, setError, setProcessing, setProgress, isProcessing, progress } = useAppStore();
  const [filename, setFilename] = useState<string>('');
  const workerRef = useState(() => new ParserWorkerClient())[0];
  const computeRef = useState(() => new ComputeWorkerClient())[0];

  const fileInputRef = useRef<HTMLInputElement | null>(null);

  async function handleFiles(files: FileList | null) {
    if (!files || files.length === 0) return;
    const file = files[0];
    setFilename(file.name);
    setProcessing(true);
    setProgress(5);
    setError(null);
    try {
      const ext = file.name.split('.').pop()?.toLowerCase();
      let data: LIVData | null = null;
      const useWorker = (file.size ?? 0) > 10 * 1024 * 1024; // >10MB 走 Worker
      if (ext === 'csv') {
        const res = useWorker ? { data: await workerRef.parseCSV(file) } : await parseCSV(file);
        if (res.error) throw new Error(res.error);
        setProgress(40);
        const target = inferCsvTarget(file.name);
        data = toLIVDataFromCsv(res.data ?? [], target);
      } else if (ext === 'xlsx' || ext === 'xls') {
        const res = useWorker ? { data: await workerRef.parseExcel(file) } : await parseExcel(file);
        if (res.error) throw new Error(res.error);
        setProgress(40);
        data = toLIVDataFromSheets(res.data ?? {});
      } else {
        throw new Error('不支持的文件类型，请选择 CSV 或 Excel 文件');
      }
      setProgress(70);
      const cleaned = preprocessLIVData(data);
      const errs: string[] = [];
      if (cleaned.power) errs.push(...validateXY('P-I', cleaned.power.current, cleaned.power.power));
      if (cleaned.voltage) errs.push(...validateXY('V-I', cleaned.voltage.current, cleaned.voltage.voltage));
      if (cleaned.wavelength) errs.push(...validateXY('光谱', cleaned.wavelength.wavelength, cleaned.wavelength.intensity));
      if (cleaned.hff) errs.push(...validateXY('HFF', cleaned.hff.angle, cleaned.hff.intensity));
      if (cleaned.vff) errs.push(...validateXY('VFF', cleaned.vff.angle, cleaned.vff.intensity));
      const summary = summarizeValidation(errs);
      if (summary) setError(summary);
      setProgress(90);
      setData(cleaned);
      setProgress(95);
      try {
        const result = await computeRef.compute(cleaned, useAppStore.getState().processingConfig);
        useAppStore.getState().setResults(result);
      } catch (e) {
        // 计算失败不阻断加载，仅提示
        setError((e as Error).message);
      }
      setProgress(100);
    } catch (err) {
      setError((err as Error).message);
    } finally {
      setProcessing(false);
      setTimeout(() => setProgress(0), 400);
    }
  }

  return (
    <div className="space-y-3">
      <div className="text-sm text-muted-foreground">选择 CSV 或 Excel 文件(.xlsx/.xls)</div>
      <label className="inline-block">
        <input
          type="file"
          accept=".csv,.xlsx,.xls"
          className="hidden"
          ref={fileInputRef}
          onChange={(e) => handleFiles(e.target.files)}
        />
        <Button disabled={isProcessing}>
          {isProcessing ? '处理中...' : '选择文件'}
        </Button>
      </label>
      {/* 桥接菜单/工具栏触发文件对话框 */}
      <button
        id="__open_files_btn"
        type="button"
        className="hidden"
        onClick={() => fileInputRef.current?.click()}
        aria-hidden
        tabIndex={-1}
      />
      {isFSAccessSupported() && (
        <Button variant="outline" size="sm" disabled={isProcessing} onClick={async () => {
          const file = await pickFileWithStartIn();
          if (file) await handleFiles({ 0: file, length: 1, item: (i: number) => (i === 0 ? file : null) } as unknown as FileList);
        }}>从最近目录选择</Button>
      )}
      {isProcessing && <Progress value={progress} />}
      {filename && (
        <div className="text-xs text-muted-foreground">当前文件: {filename}</div>
      )}
    </div>
  );
}


