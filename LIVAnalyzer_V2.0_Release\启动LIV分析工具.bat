@echo off
chcp 65001 >nul
echo ========================================
echo        LIV曲线分析工具 V2.0
echo ========================================
echo.
echo 正在启动程序...
echo.

REM 检查文件是否存在
if not exist "LIVAnalyzer.exe" (
    echo 错误：找不到 LIVAnalyzer.exe 文件！
    echo 请确保此批处理文件与 LIVAnalyzer.exe 在同一目录下。
    pause
    exit /b 1
)

REM 启动程序
start "" "LIVAnalyzer.exe"

REM 等待一下确保程序启动
timeout /t 2 /nobreak >nul

echo 程序已启动！
echo.
echo 如果程序没有正常启动，请：
echo 1. 检查是否安装了 .NET 6.0 运行时
echo 2. 检查防病毒软件是否阻止了程序运行
echo 3. 以管理员身份运行此批处理文件
echo.
echo 按任意键退出...
pause >nul
