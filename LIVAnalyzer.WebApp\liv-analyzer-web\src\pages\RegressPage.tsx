import { useState } from 'react';
import { ComputeWorkerClient } from '../services/computeClient';
import type { LIVData } from '../types/data';
import { exportCSV } from '../services/export';
import { parseCSV, parseExcel, toLIVDataFromSheets } from '../services/fileService';

const GOLDEN: LIVData = {
  power: { current: [0,0.01,0.02,0.03,0.04,0.05], power: [0,0.001,0.004,0.010,0.017,0.026] },
  voltage: { current: [0,0.01,0.02,0.03,0.04,0.05], voltage: [0,0.6,0.7,0.8,0.9,1.0] },
  wavelength: { wavelength: [979.5,979.8,980.2,980.0], intensity: [10,12,20,15] },
};

export default function RegressPage() {
  const [result, setResult] = useState<any | null>(null);
  const [status, setStatus] = useState<string>('');
  const [data, setData] = useState<LIVData>(GOLDEN);
  const [expected, setExpected] = useState({
    ith_mA: '',
    slope_WA: '',
    rs_ohm: '',
    lambda_nm: '',
    fwhm_nm: '',
  });
  const [tolerance, setTolerance] = useState({
    ith_pct: 5,
    slope_pct: 3,
    rs_pct: 5,
    lambda_abs: 0.1,
    fwhm_abs: 0.05,
  });

  async function run() {
    setStatus('运行中...');
    try {
      const client = new ComputeWorkerClient();
      const r = await client.compute(data, { thresholdDetection: 'segmented', fittingPoints: 3 });
      setResult(r);
      const ok = evaluate(r);
      setStatus(ok ? '✅ 通过（原型对齐）' : '⚠️ 部分指标未计算');
    } catch (e) {
      setStatus('失败：' + (e as Error).message);
    }
  }

  function download() {
    if (!result) return;
    exportCSV('regression_report', ['Metric','Measured','Expected','Error','Pass'], [
      row('Ith_mA', result.livParameters?.thresholdCurrent_mA, expected.ith_mA, pct(tolerance.ith_pct)),
      row('Slope_W_A', result.livParameters?.slopeEfficiency_W_per_A, expected.slope_WA, pct(tolerance.slope_pct)),
      row('Rs_Ohm', result.livParameters?.seriesResistance_Ohm, expected.rs_ohm, pct(tolerance.rs_pct)),
      row('LambdaPeak_nm', result.spectralParameters?.peakWavelength_nm, expected.lambda_nm, abs(tolerance.lambda_abs)),
      row('FWHM_nm', result.spectralParameters?.fwhm_nm, expected.fwhm_nm, abs(tolerance.fwhm_abs)),
    ]);
  }

  function pct(p: number) { return { kind: 'pct', v: p } as const; }
  function abs(a: number) { return { kind: 'abs', v: a } as const; }
  function parseNum(v: any): number | undefined { const n = Number(v); return Number.isFinite(n) ? n : undefined; }
  function row(name: string, measured: any, expectedStr: string, tol: ReturnType<typeof pct> | ReturnType<typeof abs>) {
    const m = parseNum(measured);
    const e = parseNum(expectedStr);
    if (m === undefined || e === undefined) return [name, measured ?? '', expectedStr ?? '', '', ''];
    const err = tol.kind === 'pct' ? (Math.abs(m - e) / (Math.abs(e) > 1e-12 ? Math.abs(e) : 1) * 100) : Math.abs(m - e);
    const pass = err <= tol.v;
    return [name, m, e, err, pass ? 'PASS' : 'FAIL'];
  }

  function evaluate(r: any): boolean {
    const rows = [
      row('Ith_mA', r.livParameters?.thresholdCurrent_mA, expected.ith_mA, pct(tolerance.ith_pct)),
      row('Slope_W_A', r.livParameters?.slopeEfficiency_W_per_A, expected.slope_WA, pct(tolerance.slope_pct)),
      row('Rs_Ohm', r.livParameters?.seriesResistance_Ohm, expected.rs_ohm, pct(tolerance.rs_pct)),
      row('LambdaPeak_nm', r.spectralParameters?.peakWavelength_nm, expected.lambda_nm, abs(tolerance.lambda_abs)),
      row('FWHM_nm', r.spectralParameters?.fwhm_nm, expected.fwhm_nm, abs(tolerance.fwhm_abs)),
    ];
    // If expected is blank for all, treat as pass
    const hasExpect = [expected.ith_mA, expected.slope_WA, expected.rs_ohm, expected.lambda_nm, expected.fwhm_nm].some(v => String(v || '').trim() !== '');
    if (!hasExpect) return true;
    return rows.every((arr) => arr[4] === 'PASS' || arr[4] === '');
  }

  async function onUpload(files: FileList | null) {
    if (!files || files.length === 0) return;
    const f = files[0];
    const ext = f.name.split('.').pop()?.toLowerCase();
    if (ext === 'csv') {
      const res = await parseCSV(f);
      if (res.data) setData(csvToLIVData(res.data));
    } else if (ext === 'xlsx' || ext === 'xls') {
      const res = await parseExcel(f);
      if (res.data) setData(toLIVDataFromSheets(res.data));
    }
  }

  function csvToLIVData(rows: number[][]): LIVData {
    // 采用与 FileUploader 推断一致的简化逻辑：默认当作 P-I
    const x = rows.map(r => r[0]).filter(Number.isFinite);
    const y = rows.map(r => r[1]).filter(Number.isFinite);
    return { power: { current: x, power: y } } as LIVData;
  }

  return (
    <div className="max-w-3xl mx-auto p-6 space-y-4 text-sm">
      <div className="text-lg font-semibold">回归对齐（原型）</div>
      <div className="text-muted-foreground">加载内置黄金数据，调用计算引擎并输出关键指标，作为 Phase 2 精度对齐的初步原型。</div>
      <div className="space-y-2">
        <div className="flex items-center gap-3">
          <button className="underline" onClick={() => setData(GOLDEN)}>使用内置数据</button>
          <label className="underline cursor-pointer">
            <input type="file" className="hidden" accept=".csv,.xlsx,.xls" onChange={(e) => onUpload(e.target.files)} />
            上传数据文件
          </label>
          <button className="underline" onClick={run}>运行回归</button>
          <button className="underline" onClick={download} disabled={!result}>下载报告CSV</button>
        </div>

        <div className="grid grid-cols-2 gap-3">
          <div>
            <div className="font-medium mb-1">期望值（可选）</div>
            <div className="grid grid-cols-2 gap-2 items-center">
              <label>Ith (mA)</label>
              <input className="border rounded px-2 py-1" value={expected.ith_mA} onChange={e => setExpected({ ...expected, ith_mA: e.target.value })} />
              <label>Slope (W/A)</label>
              <input className="border rounded px-2 py-1" value={expected.slope_WA} onChange={e => setExpected({ ...expected, slope_WA: e.target.value })} />
              <label>Rs (Ω)</label>
              <input className="border rounded px-2 py-1" value={expected.rs_ohm} onChange={e => setExpected({ ...expected, rs_ohm: e.target.value })} />
              <label>λpeak (nm)</label>
              <input className="border rounded px-2 py-1" value={expected.lambda_nm} onChange={e => setExpected({ ...expected, lambda_nm: e.target.value })} />
              <label>FWHM (nm)</label>
              <input className="border rounded px-2 py-1" value={expected.fwhm_nm} onChange={e => setExpected({ ...expected, fwhm_nm: e.target.value })} />
            </div>
          </div>
          <div>
            <div className="font-medium mb-1">阈值（PRD 默认）</div>
            <div className="grid grid-cols-2 gap-2 items-center">
              <label>Ith 误差(%)</label>
              <input className="border rounded px-2 py-1" type="number" value={tolerance.ith_pct} onChange={e => setTolerance({ ...tolerance, ith_pct: Number(e.target.value) })} />
              <label>斜率误差(%)</label>
              <input className="border rounded px-2 py-1" type="number" value={tolerance.slope_pct} onChange={e => setTolerance({ ...tolerance, slope_pct: Number(e.target.value) })} />
              <label>Rs 误差(%)</label>
              <input className="border rounded px-2 py-1" type="number" value={tolerance.rs_pct} onChange={e => setTolerance({ ...tolerance, rs_pct: Number(e.target.value) })} />
              <label>λ误差(nm)</label>
              <input className="border rounded px-2 py-1" type="number" value={tolerance.lambda_abs} onChange={e => setTolerance({ ...tolerance, lambda_abs: Number(e.target.value) })} />
              <label>FWHM误差(nm)</label>
              <input className="border rounded px-2 py-1" type="number" value={tolerance.fwhm_abs} onChange={e => setTolerance({ ...tolerance, fwhm_abs: Number(e.target.value) })} />
            </div>
          </div>
        </div>
      </div>
      <div>{status}</div>
      {result && (
        <pre className="bg-muted/30 p-3 rounded overflow-auto text-xs">{JSON.stringify(result, null, 2)}</pre>
      )}
    </div>
  );
}


