using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using OxyPlot;
using OxyPlot.Axes;
using OxyPlot.Wpf;

namespace LIVAnalyzer.UI.Views
{
    /// <summary>
    /// 图表信息显示类
    /// </summary>
    public class ChartDisplayInfo
    {
        public string ChartName { get; set; }
        public string ChartIcon { get; set; }
        public List<AxisDisplayInfo> Axes { get; set; } = new List<AxisDisplayInfo>();
    }

    /// <summary>
    /// 多图表轴选择对话框
    /// </summary>
    public partial class MultiChartAxisSelectionDialog : Window
    {
        public Axis SelectedAxis { get; private set; }
        public string SelectedChartName { get; private set; }

        public MultiChartAxisSelectionDialog(List<PlotView> plotViews)
        {
            InitializeComponent();
            
            Title = "设置坐标轴范围";
            LoadChartsAndAxes(plotViews);
        }

        private void LoadChartsAndAxes(List<PlotView> plotViews)
        {
            var chartInfos = new List<ChartDisplayInfo>();

            foreach (var plotView in plotViews)
            {
                if (plotView?.Model == null) continue;

                var chartInfo = new ChartDisplayInfo
                {
                    ChartName = plotView.Tag as string ?? "未知图表",
                    ChartIcon = GetChartIcon(plotView.Tag as string ?? "")
                };

                foreach (var axis in plotView.Model.Axes.Where(a => a.IsAxisVisible))
                {
                    var axisInfo = new AxisDisplayInfo
                    {
                        Axis = axis,
                        DisplayName = GetAxisDisplayName(axis),
                        RangeInfo = $"范围: {axis.ActualMinimum:F2} ~ {axis.ActualMaximum:F2}",
                        PositionText = GetPositionText(axis),
                        Icon = GetAxisIcon(axis)
                    };
                    
                    chartInfo.Axes.Add(axisInfo);
                }

                if (chartInfo.Axes.Count > 0)
                {
                    chartInfos.Add(chartInfo);
                }
            }

            ChartAxisTreeView.ItemsSource = chartInfos;
            
            // 展开所有节点
            foreach (var item in chartInfos)
            {
                var container = ChartAxisTreeView.ItemContainerGenerator.ContainerFromItem(item) as TreeViewItem;
                if (container != null)
                {
                    container.IsExpanded = true;
                }
            }
        }

        private string GetChartIcon(string chartName)
        {
            return chartName switch
            {
                "LIV曲线" => "📈",
                "光谱图" => "🌈",
                "效率曲线" => "⚡",
                "HFF发散角" => "📐",
                "VFF发散角" => "📏",
                _ => "📊"
            };
        }

        private string GetAxisDisplayName(Axis axis)
        {
            if (!string.IsNullOrEmpty(axis.Title))
            {
                return axis.Title;
            }

            return axis.Position switch
            {
                AxisPosition.Bottom => "底部X轴",
                AxisPosition.Top => "顶部X轴",
                AxisPosition.Left => "左侧Y轴",
                AxisPosition.Right when axis.PositionTier == 0 => "右侧Y轴",
                AxisPosition.Right when axis.PositionTier == 1 => "右侧Y轴 (第2层)",
                AxisPosition.Right when axis.PositionTier == 2 => "右侧Y轴 (第3层)",
                AxisPosition.Right => $"右侧Y轴 (第{axis.PositionTier + 1}层)",
                _ => "未知轴"
            };
        }

        private string GetPositionText(Axis axis)
        {
            var position = axis.Position switch
            {
                AxisPosition.Bottom => "底部",
                AxisPosition.Top => "顶部",
                AxisPosition.Left => "左侧",
                AxisPosition.Right => "右侧",
                _ => "未知"
            };

            if (axis.Position == AxisPosition.Right && axis.PositionTier > 0)
            {
                position += $" T{axis.PositionTier}";
            }

            return position;
        }

        private string GetAxisIcon(Axis axis)
        {
            return axis.Position switch
            {
                AxisPosition.Bottom => "📐", // 底部X轴
                AxisPosition.Top => "📏", // 顶部X轴
                AxisPosition.Left => "📊", // 左侧Y轴
                AxisPosition.Right => "📈", // 右侧Y轴
                _ => "📋"
            };
        }

        private void OkButton_Click(object sender, RoutedEventArgs e)
        {
            var selectedItem = ChartAxisTreeView.SelectedItem;
            
            if (selectedItem is AxisDisplayInfo axisInfo)
            {
                SelectedAxis = axisInfo.Axis;
                
                // 找到对应的图表名称
                foreach (ChartDisplayInfo chartInfo in ChartAxisTreeView.ItemsSource)
                {
                    if (chartInfo.Axes.Contains(axisInfo))
                    {
                        SelectedChartName = chartInfo.ChartName;
                        break;
                    }
                }
                
                DialogResult = true;
            }
            else
            {
                MessageBox.Show("请选择一个坐标轴。", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
        }
    }
}
