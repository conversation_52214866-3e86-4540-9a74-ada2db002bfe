using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace LIVAnalyzer.UI.Services
{
    /// <summary>
    /// UI交互服务接口
    /// </summary>
    public interface IUIInteractionService
    {
        /// <summary>
        /// 展开/收起所有Expander
        /// </summary>
        /// <param name="window">目标窗口</param>
        /// <param name="isExpanded">是否展开</param>
        void SetAllExpandersState(Window window, bool isExpanded);

        /// <summary>
        /// 显示消息框
        /// </summary>
        /// <param name="message">消息内容</param>
        /// <param name="title">标题</param>
        /// <param name="button">按钮类型</param>
        /// <param name="icon">图标类型</param>
        /// <returns>用户选择结果</returns>
        MessageBoxResult ShowMessageBox(string message, string title = "", 
            MessageBoxButton button = MessageBoxButton.OK, 
            MessageBoxImage icon = MessageBoxImage.Information);

        /// <summary>
        /// 更新主题按钮图标
        /// </summary>
        /// <param name="toggleButton">主题切换按钮</param>
        /// <param name="isDarkMode">是否为深色模式</param>
        void UpdateThemeButtonIcon(System.Windows.Controls.Primitives.ToggleButton toggleButton, bool isDarkMode);

        /// <summary>
        /// 查找可视化树中的子控件
        /// </summary>
        /// <typeparam name="T">控件类型</typeparam>
        /// <param name="parent">父级控件</param>
        /// <returns>找到的子控件集合</returns>
        IEnumerable<T> FindVisualChildren<T>(DependencyObject parent) where T : DependencyObject;
    }

    /// <summary>
    /// UI交互服务实现
    /// </summary>
    public class UIInteractionService : IUIInteractionService
    {
        private static readonly Lazy<UIInteractionService> _instance = new(() => new UIInteractionService());
        public static UIInteractionService Instance => _instance.Value;

        private UIInteractionService() { }

        public void SetAllExpandersState(Window window, bool isExpanded)
        {
            var expanders = FindVisualChildren<Expander>(window);
            foreach (var expander in expanders)
            {
                expander.IsExpanded = isExpanded;
            }
        }

        public MessageBoxResult ShowMessageBox(string message, string title = "", 
            MessageBoxButton button = MessageBoxButton.OK, 
            MessageBoxImage icon = MessageBoxImage.Information)
        {
            return MessageBox.Show(message, title, button, icon);
        }

        public void UpdateThemeButtonIcon(System.Windows.Controls.Primitives.ToggleButton toggleButton, bool isDarkMode)
        {
            if (toggleButton?.Template == null) return;

            try
            {
                // 获取模板中的图标元素
                var lightIcon = toggleButton.Template.FindName("LightIcon", toggleButton) as System.Windows.Shapes.Path;
                var darkIcon = toggleButton.Template.FindName("DarkIcon", toggleButton) as System.Windows.Shapes.Path;

                if (lightIcon != null && darkIcon != null)
                {
                    if (isDarkMode)
                    {
                        // 深色主题 - 显示太阳图标（切换到浅色）
                        lightIcon.Visibility = Visibility.Visible;
                        darkIcon.Visibility = Visibility.Collapsed;
                        toggleButton.ToolTip = "切换到浅色主题 (当前: 深色)";
                    }
                    else
                    {
                        // 浅色主题 - 显示月亮图标（切换到深色）
                        lightIcon.Visibility = Visibility.Collapsed;
                        darkIcon.Visibility = Visibility.Visible;
                        toggleButton.ToolTip = "切换到深色主题 (当前: 浅色)";
                    }
                }

                // 更新按钮状态
                toggleButton.IsChecked = isDarkMode;

                // 强制刷新按钮样式
                toggleButton.InvalidateVisual();
            }
            catch (Exception ex)
            {
                // 记录异常但不影响程序运行
                System.Diagnostics.Debug.WriteLine($"更新主题按钮图标失败: {ex.Message}");
            }
        }

        public IEnumerable<T> FindVisualChildren<T>(DependencyObject parent) where T : DependencyObject
        {
            if (parent != null)
            {
                for (int i = 0; i < VisualTreeHelper.GetChildrenCount(parent); i++)
                {
                    DependencyObject child = VisualTreeHelper.GetChild(parent, i);
                    if (child != null && child is T)
                    {
                        yield return (T)child;
                    }

                    foreach (T childOfChild in FindVisualChildren<T>(child))
                    {
                        yield return childOfChild;
                    }
                }
            }
        }
    }
}