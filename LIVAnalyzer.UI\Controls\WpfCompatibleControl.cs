using System;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Automation;
using System.Windows.Automation.Peers;

namespace LIVAnalyzer.UI.Controls
{
    /// <summary>
    /// WPF兼容的自定义控件基类
    /// </summary>
    public abstract class WpfCompatibleControl : UserControl
    {
        static WpfCompatibleControl()
        {
            // 确保WPF样式键正确设置
            DefaultStyleKeyProperty.OverrideMetadata(
                typeof(WpfCompatibleControl),
                new FrameworkPropertyMetadata(typeof(WpfCompatibleControl)));
        }

        protected WpfCompatibleControl()
        {
            // 启用WPF特性
            InitializeWpfFeatures();
        }

        private void InitializeWpfFeatures()
        {
            // 键盘导航支持
            this.Focusable = true;
            this.IsTabStop = true;
            
            // 启用手势支持
            this.IsManipulationEnabled = true;
            
            // 添加默认的输入绑定
            InitializeInputBindings();
            
            // 启用动画
            this.RenderTransformOrigin = new Point(0.5, 0.5);
            this.RenderTransform = new TransformGroup
            {
                Children = new TransformCollection
                {
                    new ScaleTransform(),
                    new RotateTransform(),
                    new TranslateTransform()
                }
            };
        }

        protected virtual void InitializeInputBindings()
        {
            // 添加常用的键盘快捷键
            this.InputBindings.Add(new KeyBinding(
                ApplicationCommands.Copy,
                new KeyGesture(Key.C, ModifierKeys.Control)));
                
            this.InputBindings.Add(new KeyBinding(
                ApplicationCommands.Paste,
                new KeyGesture(Key.V, ModifierKeys.Control)));
        }

        #region 动画支持

        public void FadeIn(double duration = 300)
        {
            var animation = new DoubleAnimation
            {
                From = 0,
                To = 1,
                Duration = TimeSpan.FromMilliseconds(duration),
                EasingFunction = new CubicEase { EasingMode = EasingMode.EaseOut }
            };
            
            this.BeginAnimation(OpacityProperty, animation);
        }

        public void FadeOut(double duration = 300)
        {
            var animation = new DoubleAnimation
            {
                From = 1,
                To = 0,
                Duration = TimeSpan.FromMilliseconds(duration),
                EasingFunction = new CubicEase { EasingMode = EasingMode.EaseIn }
            };
            
            this.BeginAnimation(OpacityProperty, animation);
        }

        public void SlideIn(SlideDirection direction, double duration = 400)
        {
            var transform = this.RenderTransform as TransformGroup;
            var translateTransform = transform?.Children.OfType<TranslateTransform>().FirstOrDefault();
            
            if (translateTransform == null) return;

            double fromX = 0, fromY = 0, toX = 0, toY = 0;
            
            switch (direction)
            {
                case SlideDirection.Left:
                    fromX = -this.ActualWidth;
                    break;
                case SlideDirection.Right:
                    fromX = this.ActualWidth;
                    break;
                case SlideDirection.Top:
                    fromY = -this.ActualHeight;
                    break;
                case SlideDirection.Bottom:
                    fromY = this.ActualHeight;
                    break;
            }

            var storyboard = new Storyboard();
            
            var xAnimation = new DoubleAnimation
            {
                From = fromX,
                To = toX,
                Duration = TimeSpan.FromMilliseconds(duration),
                EasingFunction = new ExponentialEase { EasingMode = EasingMode.EaseOut }
            };
            
            var yAnimation = new DoubleAnimation
            {
                From = fromY,
                To = toY,
                Duration = TimeSpan.FromMilliseconds(duration),
                EasingFunction = new ExponentialEase { EasingMode = EasingMode.EaseOut }
            };
            
            Storyboard.SetTarget(xAnimation, translateTransform);
            Storyboard.SetTargetProperty(xAnimation, new PropertyPath(TranslateTransform.XProperty));
            
            Storyboard.SetTarget(yAnimation, translateTransform);
            Storyboard.SetTargetProperty(yAnimation, new PropertyPath(TranslateTransform.YProperty));
            
            storyboard.Children.Add(xAnimation);
            storyboard.Children.Add(yAnimation);
            
            storyboard.Begin();
        }

        #endregion

        #region 拖放支持

        protected override void OnDragEnter(DragEventArgs e)
        {
            base.OnDragEnter(e);
            HandleDragEvent(e);
        }

        protected override void OnDragOver(DragEventArgs e)
        {
            base.OnDragOver(e);
            HandleDragEvent(e);
        }

        protected override void OnDrop(DragEventArgs e)
        {
            base.OnDrop(e);
            
            if (e.Data.GetDataPresent(DataFormats.FileDrop))
            {
                var files = (string[])e.Data.GetData(DataFormats.FileDrop);
                OnFilesDropped(files);
            }
        }

        protected virtual void OnFilesDropped(string[] files)
        {
            // 子类重写以处理文件拖放
        }

        private void HandleDragEvent(DragEventArgs e)
        {
            e.Effects = e.Data.GetDataPresent(DataFormats.FileDrop) 
                ? DragDropEffects.Copy 
                : DragDropEffects.None;
            e.Handled = true;
        }

        #endregion

        #region 高DPI支持

        protected override void OnDpiChanged(DpiScale oldDpi, DpiScale newDpi)
        {
            base.OnDpiChanged(oldDpi, newDpi);
            
            // 通知子类DPI变化
            OnDpiScaleChanged(oldDpi, newDpi);
        }

        protected virtual void OnDpiScaleChanged(DpiScale oldDpi, DpiScale newDpi)
        {
            // 子类可以重写以处理DPI变化
        }

        protected Size GetDpiAwareSize(Size baseSize)
        {
            var dpi = VisualTreeHelper.GetDpi(this);
            return new Size(
                baseSize.Width * dpi.DpiScaleX,
                baseSize.Height * dpi.DpiScaleY);
        }

        #endregion

        #region 辅助功能支持

        protected void SetAccessibility(string name, string helpText)
        {
            AutomationProperties.SetName(this, name);
            AutomationProperties.SetHelpText(this, helpText);
        }

        protected override AutomationPeer OnCreateAutomationPeer()
        {
            return new WpfCompatibleControlAutomationPeer(this);
        }

        #endregion
    }

    public enum SlideDirection
    {
        Left,
        Right,
        Top,
        Bottom
    }

    /// <summary>
    /// 自定义控件的自动化对等类
    /// </summary>
    public class WpfCompatibleControlAutomationPeer : UserControlAutomationPeer
    {
        public WpfCompatibleControlAutomationPeer(UserControl owner) : base(owner)
        {
        }

        protected override string GetClassNameCore()
        {
            return "WpfCompatibleControl";
        }

        protected override AutomationControlType GetAutomationControlTypeCore()
        {
            return AutomationControlType.Custom;
        }
    }
}