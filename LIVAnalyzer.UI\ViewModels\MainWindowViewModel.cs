using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using LIVAnalyzer.Core.Processors;
using LIVAnalyzer.Core.Exporters;
using LIVAnalyzer.Core.Algorithms;
using LIVAnalyzer.Data.Loaders;
using LIVAnalyzer.Models;
using LIVAnalyzer.Services.Configuration;
using LIVAnalyzer.Services.Logging;
using LIVAnalyzer.UI.Services;
using Microsoft.Win32;
using OxyPlot;
using OxyPlot.Axes;
using OxyPlot.Legends;
using OxyPlot.Series;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Media;

namespace LIVAnalyzer.UI.ViewModels
{
    public partial class MainWindowViewModel : ObservableObject
    {
        private readonly OptimizedLIVDataProcessor _dataProcessor;
        private readonly ExcelDataLoader _excelLoader;
        private readonly CsvDataLoader _csvLoader;
        private readonly ConfigurationManager _config;
        private readonly SemaphoreSlim _plotUpdateSemaphore = new(1, 1);
        private CancellationTokenSource? _plotUpdateCancellation;
        private bool _needsSettingsUpdate = true; // 标记是否需要更新图表设置
        private readonly OptimizedPlotManager _plotManager;
        private FileViewModel? _lastSelectedFile;
        
        public MainWindowViewModel()
        {
            _dataProcessor = new OptimizedLIVDataProcessor();
            _excelLoader = new ExcelDataLoader();
            _csvLoader = new CsvDataLoader();
            _config = ConfigurationManager.Instance;
            _plotManager = new OptimizedPlotManager();

            LoadedFiles = new ObservableCollection<FileViewModel>();
            InitializeAvailableAlgorithms();

            InitializePlotModels();
            InitializeCommands();
            InitializeFromConfig();

            // 加载主题设置 - 使用原生Fluent主题
            // 主题已在App.xaml中配置，此处不需要额外加载

            // 监听主题变化 - 使用原生Fluent主题服务
            try
            {
                // 注册主题变化事件 - 将来可以通过事件系统实现
                // 目前直接初始化主题
                UpdateAllPlotsTheme();
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "初始化主题失败");
            }
        }
        
        #region Properties
        
        // Chart settings
        private ChartSettings _currentChartSettings = new ChartSettings
        {
            LineThickness = 2.0,
            MarkerSize = 4,
            MarkerType = "圆形",
            MajorGridOpacity = 50,
            MinorGridOpacity = 25,
            MajorGridLineStyle = "实线",
            MinorGridLineStyle = "点线",
            MajorGridColor = "灰色",
            MinorGridColor = "灰色",
            ColorScheme = "默认",
            LegendPosition = "右上",
            LegendFontSize = 12,
            AxisTitleFontSize = 14,
            AxisLabelFontSize = 11
        };
        
        [ObservableProperty]
        private ObservableCollection<FileViewModel> loadedFiles;
        
        [ObservableProperty]
        private bool hasData;
        
        [ObservableProperty]
        private bool isLoading;
        
        [ObservableProperty]
        private string loadingText = "";
        
        [ObservableProperty]
        private string statusText = "请选择数据文件";
        
        [ObservableProperty]
        private string parametersText = "等待数据...";
        
        [ObservableProperty]
        private double i1Current;

        [ObservableProperty]
        private double i2Current;
        
        [ObservableProperty]
        private bool isSmoothingEnabled;
        
        [ObservableProperty]
        private int smoothingWindowSize;
        
        // 平滑算法相关属性
        [ObservableProperty]
        private SmoothingAlgorithmType selectedSmoothingAlgorithm = SmoothingAlgorithmType.MovingAverage;
        
        [ObservableProperty]
        private ObservableCollection<SmoothingAlgorithmViewModel> availableAlgorithms;
        
        // Savitzky-Golay参数
        [ObservableProperty]
        private int savitzkyGolayWindowSize = 5;
        
        [ObservableProperty]
        private int savitzkyGolayPolynomialOrder = 2;
        
        // 高斯滤波参数
        [ObservableProperty]
        private double gaussianSigma = 1.0;
        
        // 当前平滑配置
        private SmoothingConfig _currentSmoothingConfig = new();
        
        // 属性变化处理方法
        partial void OnSelectedSmoothingAlgorithmChanged(SmoothingAlgorithmType value)
        {
            UpdateCurrentSmoothingConfig();
            UpdatePlots();
        }
        
        partial void OnIsSmoothingEnabledChanged(bool value)
        {
            UpdateCurrentSmoothingConfig();
            UpdatePlots();
        }
        
        partial void OnSmoothingWindowSizeChanged(int value)
        {
            UpdateCurrentSmoothingConfig();
            UpdatePlots();
        }
        
        partial void OnSavitzkyGolayWindowSizeChanged(int value)
        {
            UpdateCurrentSmoothingConfig();
            if (SelectedSmoothingAlgorithm == SmoothingAlgorithmType.SavitzkyGolay)
                UpdatePlots();
        }
        
        partial void OnSavitzkyGolayPolynomialOrderChanged(int value)
        {
            UpdateCurrentSmoothingConfig();
            if (SelectedSmoothingAlgorithm == SmoothingAlgorithmType.SavitzkyGolay)
                UpdatePlots();
        }
        
        partial void OnGaussianSigmaChanged(double value)
        {
            UpdateCurrentSmoothingConfig();
            if (SelectedSmoothingAlgorithm == SmoothingAlgorithmType.Gaussian)
                UpdatePlots();
        }
        
        [ObservableProperty]
        private bool isLineStyle = true;
        
        [ObservableProperty]
        private bool isPointLineStyle;
        
        [ObservableProperty]
        private bool isPointStyle;
        
        private int _lineStyleIndex = 0;
        public int LineStyleIndex
        {
            get => _lineStyleIndex;
            set
            {
                if (SetProperty(ref _lineStyleIndex, value))
                {
                    IsLineStyle = value == 0;
                    IsPointLineStyle = value == 1;
                    IsPointStyle = value == 2;
                }
            }
        }
        
        [ObservableProperty]
        private bool showLegend;

        [ObservableProperty]
        private bool showGrid = true;

        [ObservableProperty]
        private bool showEfficiencyOverlay = false;

        partial void OnShowGridChanged(bool value)
        {
            UpdateGridSettings();
        }

        partial void OnShowLegendChanged(bool value)
        {
            UpdateLegendSettings();
        }

        partial void OnShowEfficiencyOverlayChanged(bool value)
        {
            // 更新图表设置并重新绘制图表
            _currentChartSettings.ShowEfficiencyOverlay = value;
            UpdatePlots();
        }

        [ObservableProperty]
        private PlotModel? lIVPlotModel;
        
        [ObservableProperty]
        private PlotModel? spectrumPlotModel;
        
        [ObservableProperty]
        private PlotModel? efficiencyPlotModel;
        
        [ObservableProperty]
        private PlotModel? divergencePlotModel;
        
        [ObservableProperty]
        private PlotModel? hffDivergencePlotModel;
        
        [ObservableProperty]
        private PlotModel? vffDivergencePlotModel;
        
        [ObservableProperty]
        private string divergenceResultText = "请选择包含发散角数据的文件...";
        
        // 手动实现命令属性
        public IAsyncRelayCommand LoadFileAsyncCommand { get; private set; }
        public IAsyncRelayCommand ExportDataAsyncCommand { get; private set; }
        public IAsyncRelayCommand BatchProcessAsyncCommand { get; private set; }
        public IRelayCommand SavePlotCommand { get; private set; }
        public IRelayCommand SelectAllCommand { get; private set; }
        public IRelayCommand DeselectAllCommand { get; private set; }
        public IRelayCommand ClearAllCommand { get; private set; }
        public IRelayCommand ShowCOSConverterCommand { get; private set; }
        public IRelayCommand ShowChartSettingsCommand { get; private set; }
        public IRelayCommand ShowGuideCommand { get; private set; }
        public IRelayCommand ShowTechDocCommand { get; private set; }
        public IRelayCommand ShowReleaseNotesCommand { get; private set; }
        public IRelayCommand ShowAboutCommand { get; private set; }
        public IRelayCommand TestCommand { get; private set; }
        public IRelayCommand ResetZoomCommand { get; private set; }
        
        #endregion
        
        #region Commands
        
        private void TestCommand_Execute()
        {
            MessageBox.Show("测试命令被触发了！", "测试", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void ResetZoom()
        {
            // 重置所有图表的缩放
            LIVPlotModel?.ResetAllAxes();
            LIVPlotModel?.InvalidatePlot(false);

            SpectrumPlotModel?.ResetAllAxes();
            SpectrumPlotModel?.InvalidatePlot(false);

            EfficiencyPlotModel?.ResetAllAxes();
            EfficiencyPlotModel?.InvalidatePlot(false);

            DivergencePlotModel?.ResetAllAxes();
            DivergencePlotModel?.InvalidatePlot(false);
            
            HffDivergencePlotModel?.ResetAllAxes();
            HffDivergencePlotModel?.InvalidatePlot(false);
            
            VffDivergencePlotModel?.ResetAllAxes();
            VffDivergencePlotModel?.InvalidatePlot(false);
        }
        
        private async Task LoadFileAsync()
        {
            try
            {
                LoggingService.LogUserAction("开始加载文件");
                
                var openFileDialog = new OpenFileDialog
                {
                    Title = "选择数据文件",
                    Filter = "All Files (*.*)|*.*|CSV Files (*.csv)|*.csv|Excel Files (*.xlsx)|*.xlsx",
                    Multiselect = true,
                    InitialDirectory = GetDefaultDirectory(false)
                };
                
                if (openFileDialog.ShowDialog() == true)
                {
                    await LoadFilesAsync(openFileDialog.FileNames);
                    
                    if (openFileDialog.FileNames.Length > 0)
                    {
                        _config.SetLastFilePath(openFileDialog.FileNames[0]);
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "加载文件失败");
                MessageBox.Show($"加载文件失败: {ex.Message}\n\n堆栈跟踪:\n{ex.StackTrace}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private async Task ExportDataAsync()
        {
            try
            {
                var selectedFiles = LoadedFiles.Where(f => f.IsSelected).ToList();
                if (!selectedFiles.Any())
                {
                    MessageBox.Show("请先选择要导出的曲线！", "警告", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }
                
                var saveFileDialog = new SaveFileDialog
                {
                    Title = "选择保存位置",
                    Filter = "Excel Files (*.xlsx)|*.xlsx",
                    FileName = $"export_results_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx",
                    InitialDirectory = GetDefaultDirectory(false)
                };
                
                if (saveFileDialog.ShowDialog() == true)
                {
                    await ExportDataAsync(selectedFiles, saveFileDialog.FileName);
                    _config.SetLastFilePath(saveFileDialog.FileName);
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "导出数据失败");
                MessageBox.Show($"导出数据失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void SavePlot()
        {
            try
            {
                var saveFileDialog = new SaveFileDialog
                {
                    Title = "保存图表",
                    Filter = "PNG Files (*.png)|*.png|SVG Files (*.svg)|*.svg",
                    FileName = $"LIV分析结果_{DateTime.Now:yyyyMMdd_HHmmss}.png",
                    InitialDirectory = GetDefaultDirectory(false)
                };
                
                if (saveFileDialog.ShowDialog() == true)
                {
                    var fileName = saveFileDialog.FileName;
                    var extension = Path.GetExtension(fileName).ToLower();
                    var baseFileName = Path.GetFileNameWithoutExtension(fileName);
                    var directory = Path.GetDirectoryName(fileName) ?? "";
                    
                    // 保存所有图表
                    var plotModels = new Dictionary<string, PlotModel?>
                    {
                        { "LIV曲线", LIVPlotModel },
                        { "光谱图", SpectrumPlotModel },
                        { "效率曲线", EfficiencyPlotModel },
                        { "水平发散角", HffDivergencePlotModel },
                        { "垂直发散角", VffDivergencePlotModel }
                    };
                    
                    int savedCount = 0;
                    foreach (var kvp in plotModels)
                    {
                        var plotType = kvp.Key;
                        var plotModel = kvp.Value;
                        
                        if (plotModel != null && plotModel.Series.Any())
                        {
                            // 为每个图表生成文件名
                            var plotFileName = Path.Combine(directory, $"{baseFileName}_{plotType}{extension}");
                            
                            switch (extension)
                            {
                                case ".png":
                                    var pngExporter = new OxyPlot.Wpf.PngExporter { Width = 1200, Height = 800 };
                                    using (var stream = File.Create(plotFileName))
                                    {
                                        pngExporter.Export(plotModel, stream);
                                    }
                                    savedCount++;
                                    break;
                                    
                                case ".svg":
                                    var svgExporter = new OxyPlot.SvgExporter { Width = 1200, Height = 800 };
                                    using (var stream = File.Create(plotFileName))
                                    {
                                        svgExporter.Export(plotModel, stream);
                                    }
                                    savedCount++;
                                    break;
                            }
                            
                            LoggingService.LogUserAction($"保存{plotType}图表", plotFileName);
                        }
                    }
                    
                    if (savedCount > 0)
                    {
                        MessageBox.Show($"成功保存 {savedCount} 个图表至:\n{directory}", "保存成功", MessageBoxButton.OK, MessageBoxImage.Information);
                        _config.SetLastFilePath(fileName);
                    }
                    else
                    {
                        MessageBox.Show("没有可保存的图表数据！请先加载数据文件。", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "保存图表失败");
                MessageBox.Show($"保存图表失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private async Task BatchProcessAsync()
        {
            try
            {
                LoggingService.LogUserAction("开始批量处理");
                
                // 使用.NET 6兼容的文件夹选择对话框
                using var folderDialog = new System.Windows.Forms.FolderBrowserDialog
                {
                    Description = "选择Excel文件所在文件夹",
                    UseDescriptionForTitle = true,
                    SelectedPath = GetDefaultDirectory(true)
                };
                
                if (folderDialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
                {
                    await BatchProcessAsync(folderDialog.SelectedPath);
                    _config.SetLastFolderPath(folderDialog.SelectedPath);
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "批量处理失败");
                MessageBox.Show($"批量处理失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void SelectAll()
        {
            foreach (var file in LoadedFiles)
            {
                file.IsSelected = true;
            }
            UpdatePlots();
            UpdateParameters();
        }
        
        private void DeselectAll()
        {
            foreach (var file in LoadedFiles)
            {
                file.IsSelected = false;
            }
            UpdatePlots();
            UpdateParameters();
        }
        
        private void ClearAll()
        {
            LoadedFiles.Clear();
            HasData = false;
            StatusText = "请选择数据文件";
            ParametersText = "请选择要分析的曲线...";
            _lastSelectedFile = null;
            ClearPlots();
        }
        
        private void ShowCOSConverter()
        {
            try
            {
                var cosConverterDialog = new Views.COSConverterDialog();
                if (Application.Current.MainWindow != null)
                {
                    cosConverterDialog.Owner = Application.Current.MainWindow;
                }
                cosConverterDialog.ShowDialog();
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "打开COS转换器失败");
                MessageBox.Show($"打开COS转换器失败：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void ShowChartSettings()
        {
            try
            {
                var chartSettingsDialog = new Views.ChartSettingsDialog();
                var viewModel = new ChartSettingsViewModel(this);
                chartSettingsDialog.DataContext = viewModel;
                
                viewModel.OnRequestClose = (isApplied) =>
                {
                    chartSettingsDialog.DialogResult = isApplied;
                    chartSettingsDialog.Close();
                };
                
                if (Application.Current.MainWindow != null)
                {
                    chartSettingsDialog.Owner = Application.Current.MainWindow;
                }
                chartSettingsDialog.ShowDialog();
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "打开图表设置失败");
                MessageBox.Show($"打开图表设置失败：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void ShowGuide()
        {
            try
            {
                var content = LIVAnalyzer.Services.Documents.DocumentService.GetUserGuide();
                var dialog = new Views.DocumentViewerDialog(
                    "使用指南",
                    content,
                    "LIV分析工具 - 使用指南"
                );

                // 设置正确的所有者窗口
                var mainWindow = Application.Current.Windows.OfType<Views.MainWindow>().FirstOrDefault();
                if (mainWindow != null)
                {
                    dialog.Owner = mainWindow;
                }

                dialog.ShowDialog();
                LoggingService.LogUserAction("查看使用指南");
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "打开使用指南失败");
                MessageBox.Show($"打开使用指南失败：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void ShowTechDoc()
        {
            try
            {
                var content = LIVAnalyzer.Services.Documents.DocumentService.GetTechnicalDocumentation();
                var dialog = new Views.DocumentViewerDialog(
                    "技术文档",
                    content,
                    "LIV分析工具 - 技术文档"
                );

                // 设置正确的所有者窗口
                var mainWindow = Application.Current.Windows.OfType<Views.MainWindow>().FirstOrDefault();
                if (mainWindow != null)
                {
                    dialog.Owner = mainWindow;
                }

                dialog.ShowDialog();
                LoggingService.LogUserAction("查看技术文档");
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "打开技术文档失败");
                MessageBox.Show($"打开技术文档失败：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void ShowReleaseNotes()
        {
            try
            {
                var content = LIVAnalyzer.Services.Documents.DocumentService.GetReleaseNotes();
                var dialog = new Views.DocumentViewerDialog(
                    "发布说明",
                    content,
                    "LIV分析工具 - 发布说明"
                );

                // 设置正确的所有者窗口
                var mainWindow = Application.Current.Windows.OfType<Views.MainWindow>().FirstOrDefault();
                if (mainWindow != null)
                {
                    dialog.Owner = mainWindow;
                }

                dialog.ShowDialog();
                LoggingService.LogUserAction("查看发布说明");
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "打开发布说明失败");
                MessageBox.Show($"打开发布说明失败：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void ShowAbout()
        {
            try
            {
                var content = LIVAnalyzer.Services.Documents.DocumentService.GetAboutInfo();
                var dialog = new Views.DocumentViewerDialog(
                    Application.Current.MainWindow,
                    "关于",
                    content,
                    "关于 LIV分析工具"
                );
                dialog.ShowDialog();
                LoggingService.LogUserAction("查看关于信息");
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "打开关于信息失败");
                MessageBox.Show($"打开关于信息失败：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        #endregion
        
        #region Private Methods
        
        private void InitializePlotModels()
        {
            LIVPlotModel = CreatePlotModel("LIV曲线", "电流 (A)", "功率 (W) / 电压 (V)");
            SpectrumPlotModel = CreatePlotModel("光谱图", "波长 (nm)", "强度");
            EfficiencyPlotModel = CreatePlotModel("效率曲线", "电流 (A)", "效率 (%)");
            DivergencePlotModel = CreatePlotModel("发散角", "角度 (°)", "强度");
            HffDivergencePlotModel = CreatePlotModel("水平发散角 (HFF)", "角度 (°)", "强度");
            VffDivergencePlotModel = CreatePlotModel("垂直发散角 (VFF)", "角度 (°)", "强度");
        }
        
        private PlotModel CreatePlotModel(string title, string xAxisTitle, string yAxisTitle)
        {
            var model = new PlotModel { Title = title };

            // 先创建轴，使用默认的网格样式
            // X轴
            var xAxis = new LinearAxis
            {
                Position = AxisPosition.Bottom,
                Title = xAxisTitle,
                MajorGridlineStyle = LineStyle.Solid,
                MinorGridlineStyle = LineStyle.Dot,
                LabelFormatter = value => value.ToString("F2") // 保留两位小数
            };
            model.Axes.Add(xAxis);

            if (title == "LIV曲线")
            {
                // LIV图表需要双Y轴
                // 左轴 - 功率
                model.Axes.Add(new LinearAxis
                {
                    Position = AxisPosition.Left,
                    Title = "功率 (W)",
                    Key = "PowerAxis",
                    TitleColor = GetThemeColor("ChartPowerAxisBrush"),
                    TicklineColor = GetThemeColor("ChartPowerAxisBrush"),
                    // TextColor将由ApplyThemeToPlot统一设置
                    Minimum = 0,
                    MajorGridlineStyle = LineStyle.Solid,
                    MinorGridlineStyle = LineStyle.Dot,
                    LabelFormatter = value => value.ToString("F2") // 保留两位小数
                });

                // 右轴 - 电压
                model.Axes.Add(new LinearAxis
                {
                    Position = AxisPosition.Right,
                    Title = "电压 (V)",
                    Key = "VoltageAxis",
                    PositionTier = 0, // 第一层右轴
                    TitleColor = GetThemeColor("ChartVoltageAxisBrush"),
                    TicklineColor = GetThemeColor("ChartVoltageAxisBrush"),
                    // TextColor将由ApplyThemeToPlot统一设置
                    Minimum = 0,
                    MajorGridlineStyle = LineStyle.Solid,
                    MinorGridlineStyle = LineStyle.Dot,
                    LabelFormatter = value => value.ToString("F2") // 保留两位小数
                });

                // 右轴第二层 - 效率（默认隐藏，根据设置显示）
                model.Axes.Add(new LinearAxis
                {
                    Position = AxisPosition.Right,
                    Title = "效率 (%)",
                    Key = "EfficiencyAxis",
                    PositionTier = 2, // 第三层右轴，提供更多空间
                    TitleColor = GetThemeColor("ChartEfficiencyAxisBrush"),
                    TicklineColor = GetThemeColor("ChartEfficiencyAxisBrush"),
                    // TextColor将由ApplyThemeToPlot统一设置
                    Minimum = 0,
                    Maximum = 100, // 效率最大值为100%
                    MajorGridlineStyle = LineStyle.None, // 不显示网格线，避免与主网格冲突
                    MinorGridlineStyle = LineStyle.None,
                    LabelFormatter = value => value.ToString("F1") + "%", // 显示百分号
                    IsAxisVisible = false, // 默认隐藏，根据设置显示
                    TitleFontSize = 12, // 设置合适的标题字体大小
                    FontSize = 10 // 设置合适的刻度标签字体大小
                });
            }
            else
            {
                // 其他图表使用单Y轴
                var yAxis = new LinearAxis
                {
                    Position = AxisPosition.Left,
                    Title = yAxisTitle,
                    MajorGridlineStyle = LineStyle.Solid,
                    MinorGridlineStyle = LineStyle.Dot,
                    LabelFormatter = value => value.ToString("F2") // 保留两位小数
                };
                model.Axes.Add(yAxis);
            }

            // 配置图例 - 使用新版OxyPlot方式
            ConfigureLegend(model);

            // 配置图表交互功能
            ConfigureInteractions(model);

            // 应用主题 - 在轴创建后应用，以确保主题颜色生效
            ApplyThemeToPlot(model);

            // 应用用户设置的网格颜色（如果有的话）
            ApplySettingsToPlot(model);

            return model;
        }

        private void OnThemeChanged(object sender, object args)
        {
            // 主题变化时更新所有图表
            try
            {
                Application.Current?.Dispatcher?.Invoke(() =>
                {
                    UpdateAllPlotsTheme();
                });
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "主题切换时更新图表失败");
            }
        }

        public void UpdateAllPlotsTheme()
        {
            // 更新所有图表的主题和图例
            UpdateSinglePlotTheme(LIVPlotModel);
            UpdateSinglePlotTheme(SpectrumPlotModel);
            UpdateSinglePlotTheme(EfficiencyPlotModel);
            UpdateSinglePlotTheme(DivergencePlotModel);
            UpdateSinglePlotTheme(HffDivergencePlotModel);
            UpdateSinglePlotTheme(VffDivergencePlotModel);
        }

        /// <summary>
        /// 更新单个图表的主题（包括图例）
        /// </summary>
        private void UpdateSinglePlotTheme(PlotModel? plotModel)
        {
            if (plotModel == null) return;
            
            // 重新配置图例（主题感知）
            ConfigureLegend(plotModel);
            
            // 应用主题到图表
            ApplyThemeToPlot(plotModel);
            
            // 强制刷新
            plotModel.InvalidatePlot(true);
        }

        /// <summary>
        /// 从当前主题资源中获取颜色值
        /// </summary>
        private OxyColor GetThemeColor(string resourceKey)
        {
            try
            {
                if (Application.Current?.Resources[resourceKey] is SolidColorBrush brush)
                {
                    var color = brush.Color;
                    return OxyColor.FromArgb(color.A, color.R, color.G, color.B);
                }

                // 如果找不到资源，返回默认颜色
                var isDark = NativeFluentThemeService.Instance.IsCurrentlyDarkMode();

                // 使用通用的默认颜色获取方法
                return GetDefaultThemeColor(resourceKey, isDark);
            }
            catch (Exception ex)
            {
                LoggingService.LogWarning($"获取主题颜色失败: {resourceKey}, 错误: {ex.Message}");

                // 异常时返回安全的默认颜色
                var isDark = NativeFluentThemeService.Instance.IsCurrentlyDarkMode();
                return GetDefaultThemeColor(resourceKey, isDark);
            }
        }

        /// <summary>
        /// 获取资源键对应的默认主题颜色
        /// </summary>
        private OxyColor GetDefaultThemeColor(string resourceKey, bool isDark)
        {
            return resourceKey switch
            {
                // 图表轴线和边框颜色
                "ChartAxisBrush" or "ChartBorderBrush" or "FluentChartAxisBrush" => 
                    isDark ? OxyColor.FromRgb(255, 255, 255) : OxyColor.FromRgb(0, 0, 0),
                
                // 图表文字颜色
                "ChartTextBrush" or "FluentChartTextBrush" => 
                    isDark ? OxyColor.FromRgb(255, 255, 255) : OxyColor.FromRgb(0, 0, 0),
                
                // 图表网格颜色
                "ChartGridBrush" or "FluentChartGridBrush" => 
                    isDark ? OxyColor.FromRgb(64, 64, 64) : OxyColor.FromRgb(225, 225, 225),
                
                // 图表背景颜色
                "ChartBackgroundBrush" => 
                    isDark ? OxyColor.FromRgb(24, 24, 24) : OxyColor.FromRgb(255, 255, 255),
                
                // 图表绘图区背景颜色
                "ChartPlotAreaBrush" => 
                    isDark ? OxyColor.FromRgb(32, 32, 32) : OxyColor.FromRgb(255, 255, 255),
                
                // 功率轴颜色（蓝色）
                "ChartPowerAxisBrush" => 
                    isDark ? OxyColor.FromRgb(100, 149, 237) : OxyColor.FromRgb(0, 100, 200),
                
                // 电压轴颜色（红色）
                "ChartVoltageAxisBrush" =>
                    isDark ? OxyColor.FromRgb(220, 20, 60) : OxyColor.FromRgb(200, 0, 0),

                // 效率轴颜色（绿色）
                "ChartEfficiencyAxisBrush" =>
                    isDark ? OxyColor.FromRgb(34, 197, 94) : OxyColor.FromRgb(0, 150, 0),
                
                // 图例相关颜色
                "ChartLegendBackgroundBrush" => 
                    isDark ? OxyColor.FromAColor(220, OxyColor.FromRgb(32, 32, 32)) : OxyColor.FromAColor(240, OxyColor.FromRgb(255, 255, 255)),
                    
                "ChartLegendBorderBrush" => 
                    isDark ? OxyColor.FromRgb(255, 255, 255) : OxyColor.FromRgb(128, 128, 128),
                    
                "ChartLegendTextBrush" => 
                    isDark ? OxyColor.FromRgb(255, 255, 255) : OxyColor.FromRgb(0, 0, 0),
                
                // 系列颜色（支持动态系列）
                var key when key.StartsWith("ChartSeries") && key.EndsWith("Brush") => GetSeriesColor(key, isDark),
                
                // 次要网格颜色
                "ChartMinorGridBrush" => 
                    isDark ? OxyColor.FromRgb(48, 48, 48) : OxyColor.FromRgb(240, 240, 240),
                
                // 默认颜色
                _ => isDark ? OxyColor.FromRgb(200, 200, 200) : OxyColor.FromRgb(50, 50, 50)
            };
        }

        /// <summary>
        /// 获取系列颜色
        /// </summary>
        private OxyColor GetSeriesColor(string seriesKey, bool isDark)
        {
            // 提取系列编号（如 ChartSeries1Brush -> 1）
            var match = System.Text.RegularExpressions.Regex.Match(seriesKey, @"ChartSeries(\d+)Brush");
            if (!match.Success) return isDark ? OxyColor.FromRgb(100, 149, 237) : OxyColor.FromRgb(0, 100, 200);
            
            var seriesIndex = int.Parse(match.Groups[1].Value);
            
            // 定义深色和浅色主题的系列颜色调色板
            var darkColors = new[]
            {
                OxyColor.FromRgb(100, 149, 237), // 浅蓝色
                OxyColor.FromRgb(220, 20, 60),   // 浅红色
                OxyColor.FromRgb(50, 205, 50),   // 浅绿色
                OxyColor.FromRgb(255, 165, 0),   // 橙色
                OxyColor.FromRgb(138, 43, 226),  // 紫色
                OxyColor.FromRgb(255, 192, 203), // 粉色
                OxyColor.FromRgb(255, 255, 0),   // 黄色
                OxyColor.FromRgb(0, 255, 255)    // 青色
            };
            
            var lightColors = new[]
            {
                OxyColor.FromRgb(0, 100, 200),   // 深蓝色
                OxyColor.FromRgb(200, 0, 0),     // 深红色
                OxyColor.FromRgb(0, 150, 0),     // 深绿色
                OxyColor.FromRgb(255, 140, 0),   // 深橙色
                OxyColor.FromRgb(128, 0, 128),   // 深紫色
                OxyColor.FromRgb(255, 20, 147),  // 深粉色
                OxyColor.FromRgb(184, 134, 11),  // 深黄色
                OxyColor.FromRgb(0, 139, 139)    // 深青色
            };
            
            var colors = isDark ? darkColors : lightColors;
            var colorIndex = (seriesIndex - 1) % colors.Length; // 从1开始編號，转换为0开始的索引
            
            return colors[colorIndex];
        }

        private void ApplyThemeToPlot(PlotModel? plot)
        {
            if (plot == null) return;

            try
            {
                // 使用主题资源获取颜色，而不是硬编码
                var chartTextColor = GetThemeColor("ChartTextBrush");
                var chartAxisColor = GetThemeColor("ChartAxisBrush"); 
                var chartBorderColor = GetThemeColor("ChartBorderBrush");
                var chartGridColor = GetThemeColor("ChartGridBrush");
                var chartBackgroundColor = GetThemeColor("ChartBackgroundBrush");
                var chartPlotAreaColor = GetThemeColor("ChartPlotAreaBrush");

                var isDark = NativeFluentThemeService.Instance.IsCurrentlyDarkMode();

                // 调试信息
                LoggingService.LogInformation($"应用主题到图表: 深色模式={isDark}, 轴线颜色={chartAxisColor}, 边框颜色={chartBorderColor}");

                // 设置图表背景和文字颜色
                plot.TextColor = chartTextColor;

                // 设置图表背景色 - 使用主题资源
                plot.Background = chartBackgroundColor;
                plot.PlotAreaBackground = chartPlotAreaColor;

                // 设置图表外边框 - 使用主题资源颜色
                plot.PlotAreaBorderColor = chartBorderColor;
                plot.PlotAreaBorderThickness = new OxyThickness(1);

                // 更新轴的颜色
                foreach (var axis in plot.Axes)
                {
                    // 所有轴都使用相同的文本颜色，确保标签颜色一致
                    axis.TextColor = chartTextColor;
                    
                    // 检查轴是否有特定的键，如果有则保留其特定颜色设置
                    if (string.IsNullOrEmpty(axis.Key))
                    {
                        // 对于没有特定键的轴（如X轴），使用通用主题颜色
                        axis.AxislineColor = chartAxisColor;
                        axis.TicklineColor = chartAxisColor;
                    }
                    else
                    {
                        // 对于有特定键的轴（如PowerAxis、VoltageAxis），只设置轴线颜色，保留刻度线的特定颜色
                        axis.AxislineColor = chartAxisColor;
                        // 不覆盖TicklineColor，保留轴创建时设置的特定颜色
                    }

                    // 确保轴线样式为实线且可见
                    axis.AxislineStyle = LineStyle.Solid;
                    axis.AxislineThickness = 1.0; // 确保轴线有厚度

                    // 只有在用户没有自定义网格颜色时才应用主题网格颜色
                    if (_currentChartSettings.MajorGridColor == "灰色" &&
                        _currentChartSettings.MinorGridColor == "灰色")
                    {
                        axis.MajorGridlineColor = chartGridColor;
                        axis.MinorGridlineColor = OxyColor.FromAColor(128, chartGridColor);
                    }

                    LoggingService.LogInformation($"轴 {axis.Position}: 键={axis.Key}, 文本颜色={axis.TextColor}, 刻度线颜色={axis.TicklineColor}");
                }

                plot.InvalidatePlot(true);
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "应用主题到图表失败");
            }
        }

        private OxyColor GetGridLineColor(byte alpha)
        {
            try
            {
                // 使用主题资源获取网格颜色
                var gridColor = GetThemeColor("ChartGridBrush");
                return OxyColor.FromAColor(alpha, gridColor);
            }
            catch
            {
                // 异常时使用默认颜色
                var isDark = NativeFluentThemeService.Instance.IsCurrentlyDarkMode();
                var defaultGridColor = GetDefaultThemeColor("ChartGridBrush", isDark);
                return OxyColor.FromAColor(alpha, defaultGridColor);
            }
        }

        private OxyColor GetAxisTextColor()
        {
            try
            {
                // 使用主题资源获取文字颜色
                return GetThemeColor("ChartTextBrush");
            }
            catch
            {
                // 异常时使用默认颜色
                var isDark = NativeFluentThemeService.Instance.IsCurrentlyDarkMode();
                return GetDefaultThemeColor("ChartTextBrush", isDark);
            }
        }

        private OxyColor GetAxisLineColor()
        {
            try
            {
                // 使用主题资源获取轴线颜色
                return GetThemeColor("ChartAxisBrush");
            }
            catch
            {
                // 异常时使用默认颜色
                var isDark = NativeFluentThemeService.Instance.IsCurrentlyDarkMode();
                return GetDefaultThemeColor("ChartAxisBrush", isDark);
            }
        }

        private void ConfigureInteractions(PlotModel model)
        {
            // 设置图表边距 - 为多层右轴预留更多空间
            // 左边距60, 上边距10, 右边距130(为双右轴和效率轴标题预留足够空间), 下边距40
            model.PlotMargins = new OxyThickness(60, 10, 130, 40);

            // 注意：不在这里设置网格颜色，因为会覆盖主题和用户设置
            // 网格颜色应该由 ApplyThemeToPlot 和 ApplySettingsToPlot 处理
        }
        
        private void ConfigureLegend(PlotModel model)
        {
            // 清除现有图例
            model.Legends.Clear();

            if (ShowLegend)
            {
                // 使用主题资源获取图例颜色
                var legendBackgroundColor = GetThemeColor("ChartLegendBackgroundBrush");
                var legendBorderColor = GetThemeColor("ChartLegendBorderBrush");
                var legendTextColor = GetThemeColor("ChartLegendTextBrush");

                // 添加新的图例配置 - 兼容新版OxyPlot
                var legend = new OxyPlot.Legends.Legend
                {
                    LegendPosition = ConvertToLegendPosition(_currentChartSettings.LegendPosition),
                    LegendPlacement = OxyPlot.Legends.LegendPlacement.Outside,
                    LegendOrientation = OxyPlot.Legends.LegendOrientation.Vertical,
                    LegendBackground = legendBackgroundColor,
                    LegendBorder = legendBorderColor,
                    LegendTextColor = legendTextColor,
                    LegendBorderThickness = 1.0,
                    LegendMargin = 10,
                    LegendItemSpacing = 5,
                    LegendFontSize = _currentChartSettings.LegendFontSize
                };

                model.Legends.Add(legend);
            }
        }
        
        private void SetLineStyle(LineSeries series)
        {
            series.StrokeThickness = _currentChartSettings.LineThickness;
            
            if (IsLineStyle)
            {
                series.LineStyle = LineStyle.Solid;
                series.MarkerType = ConvertToMarkerType(_currentChartSettings.MarkerType);
                if (series.MarkerType != MarkerType.None)
                {
                    series.MarkerSize = _currentChartSettings.MarkerSize;
                }
            }
            else if (IsPointLineStyle)
            {
                series.LineStyle = LineStyle.Solid;
                series.MarkerType = ConvertToMarkerType(_currentChartSettings.MarkerType);
                series.MarkerSize = _currentChartSettings.MarkerSize;
            }
            else if (IsPointStyle)
            {
                series.LineStyle = LineStyle.None;
                series.MarkerType = ConvertToMarkerType(_currentChartSettings.MarkerType);
                series.MarkerSize = _currentChartSettings.MarkerSize;
            }
        }
        
        private MarkerType ConvertToMarkerType(string markerType)
        {
            return markerType switch
            {
                "无" => MarkerType.None,
                "圆形" => MarkerType.Circle,
                "方形" => MarkerType.Square,
                "菱形" => MarkerType.Diamond,
                "三角形" => MarkerType.Triangle,
                "十字" => MarkerType.Cross,
                _ => MarkerType.Circle
            };
        }

        private OxyColor GetSeriesColor(int index)
        {
            // 根据颜色方案获取颜色
            var colors = GetColorSchemeColors(_currentChartSettings.ColorScheme);
            return colors[index % colors.Count];
        }
        
        private List<OxyColor> GetColorSchemeColors(string scheme)
        {
            var isDark = NativeFluentThemeService.Instance.IsCurrentlyDarkMode();
            
            return scheme switch
            {
                "彩虹" => isDark ? new List<OxyColor>
                {
                    // 深色主题彩虹色 - 更亮的颜色
                    OxyColor.FromRgb(255, 102, 102), OxyColor.FromRgb(255, 178, 102), OxyColor.FromRgb(255, 255, 102),
                    OxyColor.FromRgb(102, 255, 102), OxyColor.FromRgb(102, 178, 255), OxyColor.FromRgb(178, 102, 255),
                    OxyColor.FromRgb(255, 102, 255), OxyColor.FromRgb(255, 102, 178)
                } : new List<OxyColor>
                {
                    // 浅色主题彩虹色 - 标准颜色
                    OxyColor.FromRgb(220, 20, 60), OxyColor.FromRgb(255, 140, 0), OxyColor.FromRgb(255, 215, 0),
                    OxyColor.FromRgb(50, 205, 50), OxyColor.FromRgb(30, 144, 255), OxyColor.FromRgb(75, 0, 130),
                    OxyColor.FromRgb(238, 130, 238), OxyColor.FromRgb(255, 20, 147)
                },
                
                "暖色" => isDark ? new List<OxyColor>
                {
                    // 深色主题暖色 - 更亮的暖色调
                    OxyColor.FromRgb(255, 102, 102), OxyColor.FromRgb(255, 128, 128), OxyColor.FromRgb(255, 160, 102),
                    OxyColor.FromRgb(255, 178, 102), OxyColor.FromRgb(255, 215, 102), OxyColor.FromRgb(255, 255, 102),
                    OxyColor.FromRgb(255, 255, 153), OxyColor.FromRgb(255, 218, 185)
                } : new List<OxyColor>
                {
                    // 浅色主题暖色 - 标准暖色调
                    OxyColor.FromRgb(139, 0, 0), OxyColor.FromRgb(220, 20, 60), OxyColor.FromRgb(255, 69, 0),
                    OxyColor.FromRgb(255, 140, 0), OxyColor.FromRgb(255, 215, 0), OxyColor.FromRgb(255, 255, 0),
                    OxyColor.FromRgb(255, 255, 224), OxyColor.FromRgb(255, 218, 185)
                },
                
                "冷色" => isDark ? new List<OxyColor>
                {
                    // 深色主题冷色 - 更亮的冷色调
                    OxyColor.FromRgb(102, 178, 255), OxyColor.FromRgb(135, 206, 250), OxyColor.FromRgb(176, 196, 222),
                    OxyColor.FromRgb(173, 216, 230), OxyColor.FromRgb(102, 255, 255), OxyColor.FromRgb(102, 205, 170),
                    OxyColor.FromRgb(102, 255, 178), OxyColor.FromRgb(127, 255, 212)
                } : new List<OxyColor>
                {
                    // 浅色主题冷色 - 标准冷色调
                    OxyColor.FromRgb(0, 0, 139), OxyColor.FromRgb(0, 0, 255), OxyColor.FromRgb(100, 149, 237),
                    OxyColor.FromRgb(173, 216, 230), OxyColor.FromRgb(0, 255, 255), OxyColor.FromRgb(0, 128, 128),
                    OxyColor.FromRgb(46, 139, 87), OxyColor.FromRgb(32, 178, 170)
                },
                
                "单色渐变" => isDark ? new List<OxyColor>
                {
                    // 深色主题蓝色渐变 - 更亮
                    OxyColor.FromRgb(102, 102, 255), OxyColor.FromRgb(128, 128, 255),
                    OxyColor.FromRgb(153, 153, 255), OxyColor.FromRgb(178, 178, 255),
                    OxyColor.FromRgb(192, 192, 255), OxyColor.FromRgb(206, 206, 255),
                    OxyColor.FromRgb(220, 220, 255), OxyColor.FromRgb(234, 234, 255)
                } : new List<OxyColor>
                {
                    // 浅色主题蓝色渐变 - 标准深浅
                    OxyColor.FromRgb(0, 0, 128), OxyColor.FromRgb(0, 0, 160),
                    OxyColor.FromRgb(0, 0, 192), OxyColor.FromRgb(0, 0, 224),
                    OxyColor.FromRgb(64, 64, 255), OxyColor.FromRgb(128, 128, 255),
                    OxyColor.FromRgb(192, 192, 255), OxyColor.FromRgb(224, 224, 255)
                },
                
                "高对比度" => GetHighContrastColors(),
                
                _ => GetDefaultColors() // 默认方案
            };
        }

        /// <summary>
        /// 获取高对比度配色方案（考虑主题）
        /// </summary>
        private List<OxyColor> GetHighContrastColors()
        {
            var isDark = NativeFluentThemeService.Instance.IsCurrentlyDarkMode();
            
            return isDark ? new List<OxyColor>
            {
                // 深色主题高对比度 - 亮色系
                OxyColor.FromRgb(255, 255, 255), OxyColor.FromRgb(255, 102, 102), OxyColor.FromRgb(102, 178, 255),
                OxyColor.FromRgb(102, 255, 102), OxyColor.FromRgb(255, 178, 102), OxyColor.FromRgb(255, 102, 255),
                OxyColor.FromRgb(160, 82, 45), OxyColor.FromRgb(192, 192, 192)
            } : new List<OxyColor>
            {
                // 浅色主题高对比度 - 深色系
                OxyColor.FromRgb(0, 0, 0), OxyColor.FromRgb(220, 20, 60), OxyColor.FromRgb(0, 0, 255),
                OxyColor.FromRgb(0, 128, 0), OxyColor.FromRgb(255, 140, 0), OxyColor.FromRgb(128, 0, 128),
                OxyColor.FromRgb(165, 42, 42), OxyColor.FromRgb(128, 128, 128)
            };
        }

        /// <summary>
        /// 获取默认配色方案（考虑主题）
        /// </summary>
        private List<OxyColor> GetDefaultColors()
        {
            // 使用我们定义的系列颜色
            var colors = new List<OxyColor>();
            var isDark = NativeFluentThemeService.Instance.IsCurrentlyDarkMode();
            
            for (int i = 1; i <= 8; i++)
            {
                colors.Add(GetSeriesColor($"ChartSeries{i}Brush", isDark));
            }
            
            return colors;
        }
        
        #endregion
        
        private void InitializeCommands()
        {
            // 手动创建所有命令
            TestCommand = new RelayCommand(TestCommand_Execute);
            ResetZoomCommand = new RelayCommand(ResetZoom);
            LoadFileAsyncCommand = new AsyncRelayCommand(LoadFileAsync);
            ExportDataAsyncCommand = new AsyncRelayCommand(ExportDataAsync);
            SavePlotCommand = new RelayCommand(SavePlot);
            BatchProcessAsyncCommand = new AsyncRelayCommand(BatchProcessAsync);
            SelectAllCommand = new RelayCommand(SelectAll);
            DeselectAllCommand = new RelayCommand(DeselectAll);
            ClearAllCommand = new RelayCommand(ClearAll);
            ShowCOSConverterCommand = new RelayCommand(ShowCOSConverter);
            ShowChartSettingsCommand = new RelayCommand(ShowChartSettings);
            ShowGuideCommand = new RelayCommand(ShowGuide);
            ShowTechDocCommand = new RelayCommand(ShowTechDoc);
            ShowReleaseNotesCommand = new RelayCommand(ShowReleaseNotes);
            ShowAboutCommand = new RelayCommand(ShowAbout);
            
            // 初始化主题相关命令
            SetThemeModeCommand = new RelayCommand<string>(mode => 
            {
                if (!string.IsNullOrEmpty(mode))
                {
                    // 主题模式设置 - 使用原生Fluent主题
                    App.SwitchTheme(mode);
                    OnPropertyChanged(nameof(IsSystemTheme));
                    OnPropertyChanged(nameof(IsLightTheme));
                    OnPropertyChanged(nameof(IsDarkTheme));
                }
            });
            
            SetAccentColorCommand = new RelayCommand<string>(colorName =>
            {
                if (!string.IsNullOrEmpty(colorName))
                {
                    // 主题色设置 - 使用原生Fluent主题
                    // 原生主题会自动使用系统强调色
                }
            });
            
            // 属性变化时的处理
            PropertyChanged += (s, e) =>
            {
                switch (e.PropertyName)
                {
                    case nameof(IsSmoothingEnabled):
                    case nameof(SmoothingWindowSize):
                        // 数据处理相关变化，需要完全重建
                        _needsSettingsUpdate = true;
                        UpdatePlots();
                        break;
                    case nameof(IsLineStyle):
                    case nameof(IsPointLineStyle):
                    case nameof(IsPointStyle):
                        // 样式变化，需要更新设置
                        _needsSettingsUpdate = true;
                        UpdatePlots();
                        break;
                    case nameof(ShowLegend):
                    case nameof(ShowGrid):
                    case nameof(ShowEfficiencyOverlay):
                        // 显示设置变化，需要更新设置
                        _needsSettingsUpdate = true;
                        UpdatePlots();
                        break;
                }
            };
        }
        
        private void InitializeFromConfig()
        {
            var config = _config.GetConfig();
            I1Current = config.DataProcessing.BatchProcessing.DefaultI1;
            I2Current = config.DataProcessing.BatchProcessing.DefaultI2;
            IsSmoothingEnabled = config.DataProcessing.Smoothing.EnableByDefault;
            SmoothingWindowSize = config.DataProcessing.Smoothing.DefaultWindowSize;
            
            // 初始化平滑算法相关参数
            SelectedSmoothingAlgorithm = config.DataProcessing.Smoothing.AlgorithmType;
            SavitzkyGolayWindowSize = config.DataProcessing.Smoothing.SavitzkyGolay.WindowSize;
            SavitzkyGolayPolynomialOrder = config.DataProcessing.Smoothing.SavitzkyGolay.PolynomialOrder;
            GaussianSigma = config.DataProcessing.Smoothing.Gaussian.Sigma;
            
            ShowLegend = config.Display.ShowLegend;
            ShowEfficiencyOverlay = _currentChartSettings.ShowEfficiencyOverlay;

            // Load chart settings from config
            LoadChartSettingsFromConfig();
            
            // 更新当前平滑配置
            UpdateCurrentSmoothingConfig();
        }
        
        /// <summary>
        /// 初始化可用的平滑算法
        /// </summary>
        private void InitializeAvailableAlgorithms()
        {
            AvailableAlgorithms = new ObservableCollection<SmoothingAlgorithmViewModel>();
            
            var algorithms = SmoothingAlgorithmFactory.GetAllAlgorithms();
            foreach (var algorithm in algorithms)
            {
                AvailableAlgorithms.Add(new SmoothingAlgorithmViewModel(
                    algorithm.AlgorithmType,
                    algorithm.Name,
                    algorithm.Description,
                    algorithm.SupportsRealTimePreview
                ));
            }
        }
        
        /// <summary>
        /// 更新当前平滑配置
        /// </summary>
        private void UpdateCurrentSmoothingConfig()
        {
            _currentSmoothingConfig.AlgorithmType = SelectedSmoothingAlgorithm;
            _currentSmoothingConfig.EnableByDefault = IsSmoothingEnabled;
            _currentSmoothingConfig.DefaultWindowSize = SmoothingWindowSize;
            
            // 更新各算法的参数
            _currentSmoothingConfig.SavitzkyGolay.WindowSize = SavitzkyGolayWindowSize;
            _currentSmoothingConfig.SavitzkyGolay.PolynomialOrder = SavitzkyGolayPolynomialOrder;
            _currentSmoothingConfig.Gaussian.Sigma = GaussianSigma;
            
            // 重新计算所有文件的参数（使用新的平滑设置）
            RecalculateAllParametersWithSmoothing();
        }
        
        private string GetDefaultDirectory(bool isFolder)
        {
            try
            {
                var myDocs = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);

                if (isFolder)
                {
                    var lastFolder = _config.GetLastFolderPath();
                    if (!string.IsNullOrWhiteSpace(lastFolder) && Directory.Exists(lastFolder))
                    {
                        return lastFolder;
                    }

                    // 若无有效记录，回退到“我的文档”
                    return string.IsNullOrWhiteSpace(myDocs) ? "C:\\" : myDocs;
                }
                else
                {
                    var lastFile = _config.GetLastFilePath();
                    var lastDir = string.IsNullOrWhiteSpace(lastFile) ? string.Empty : Path.GetDirectoryName(lastFile) ?? string.Empty;

                    if (!string.IsNullOrWhiteSpace(lastDir) && Directory.Exists(lastDir))
                    {
                        return lastDir;
                    }

                    // 若无有效记录，回退到“我的文档”
                    return string.IsNullOrWhiteSpace(myDocs) ? "C:\\" : myDocs;
                }
            }
            catch
            {
                // 任意异常统一回退
                return "C:\\";
            }
        }
        
        private async Task LoadFilesAsync(string[] filePaths)
        {
            try
            {
                // 使用真正的渐进式加载（数据点渐进式读取）
                await LoadFilesWithTrueProgressiveAsync(filePaths);
            }
            finally
            {
                // 不显示任何加载状态
            }
        }

        // TODO: 渐进式文件加载功能暂时禁用，等修复编译错误后重新实现
        /*
        /// <summary>
        /// 渐进式文件加载 - 提供更好的用户体验
        /// </summary>
        private async Task LoadFilesProgressivelyAsync(string[] filePaths)
        {
            var progressiveLoader = new LIVAnalyzer.Core.Services.ProgressiveDataLoader(_csvLoader, _excelLoader);
            var loadedFileViewModels = new ConcurrentBag<FileViewModel>();
            var errorMessages = new List<string>();

            // 订阅进度更新事件
            progressiveLoader.ProgressUpdated += (sender, e) =>
            {
                Application.Current.Dispatcher.Invoke(() =>
                {
                    LoadingText = $"{e.Message} ({e.Percentage:F0}%)";
                });
            };

            // 订阅文件加载完成事件
            progressiveLoader.FileLoadCompleted += (sender, e) =>
            {
                if (e.Data != null && e.Error == null)
                {
                    Application.Current.Dispatcher.Invoke(() =>
                    {
                        HandleProgressiveFileLoad(e, loadedFileViewModels);
                    });
                }
                else if (e.Error != null)
                {
                    errorMessages.Add($"{Path.GetFileName(e.FilePath)}: {e.Error}");
                }
            };

            try
            {
                // 禁用图表更新以提升性能
                var originalUpdateEnabled = _plotUpdateEnabled;
                _plotUpdateEnabled = false;

                try
                {
                    // 开始渐进式加载
                    await progressiveLoader.LoadFilesProgressivelyAsync(filePaths);

                    // 显示加载结果
                    var successCount = loadedFileViewModels.Count;
                    var failCount = errorMessages.Count;

                    if (successCount > 0)
                    {
                        LoadingText = "正在更新界面...";

                        // 批量添加到UI（已经在事件处理中添加了）
                        if (LoadedFiles.Any())
                        {
                            // 选择第一个文件
                            LoadedFiles.First().IsSelected = true;
                        }

                        LoggingService.LogUserAction($"成功加载 {successCount} 个文件");
                    }

                    // 显示结果摘要
                    if (failCount > 0)
                    {
                        var errorSummary = string.Join("\n", errorMessages.Take(5));
                        if (errorMessages.Count > 5)
                        {
                            errorSummary += $"\n... 还有 {errorMessages.Count - 5} 个错误";
                        }

                        MessageBox.Show($"加载完成！\n成功: {successCount} 个文件\n失败: {failCount} 个文件\n\n错误详情:\n{errorSummary}",
                            "加载结果", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else if (successCount > 0)
                    {
                        // 只在全部成功时显示简单提示
                        LoadingText = $"成功加载 {successCount} 个文件";
                        await Task.Delay(1000); // 显示1秒后清除
                    }
                }
                finally
                {
                    // 恢复图表更新
                    _plotUpdateEnabled = originalUpdateEnabled;

                    // 触发一次完整的图表更新
                    if (_plotUpdateEnabled && LoadedFiles.Any(f => f.IsSelected))
                    {
                        UpdatePlots();
                        UpdateParameters();
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "渐进式加载失败");
                MessageBox.Show($"加载文件时发生错误: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 处理渐进式文件加载的各个阶段
        /// </summary>
        private void HandleProgressiveFileLoad(LIVAnalyzer.Core.Services.FileLoadCompletedEventArgs e, ConcurrentBag<FileViewModel> loadedFileViewModels)
        {
            switch (e.Stage)
            {
                case LIVAnalyzer.Core.Services.LoadingStage.BasicDataLoaded:
                    // 第一阶段：基本数据加载完成，立即显示文件
                    if (e.Data != null)
                    {
                        var fileViewModel = new FileViewModel(e.Data);
                        fileViewModel.PropertyChanged += (s, args) =>
                        {
                            if (args.PropertyName == nameof(FileViewModel.IsSelected))
                            {
                                if (fileViewModel.IsSelected)
                                {
                                    _lastSelectedFile = fileViewModel;
                                }
                                if (_plotUpdateEnabled)
                                {
                                    UpdatePlots();
                                    UpdateParameters();
                                }
                            }
                        };

                        LoadedFiles.Add(fileViewModel);
                        loadedFileViewModels.Add(fileViewModel);

                        // 显示进度
                        LoadingText = $"已显示文件: {Path.GetFileName(e.FilePath)} (数据加载完成)";
                    }
                    break;

                case LIVAnalyzer.Core.Services.LoadingStage.BasicParametersCalculated:
                    // 第二阶段：基本参数计算完成，更新参数显示
                    LoadingText = $"已计算基本参数: {Path.GetFileName(e.FilePath)}";
                    if (_plotUpdateEnabled)
                    {
                        UpdateParameters(); // 更新参数显示
                    }
                    break;

                case LIVAnalyzer.Core.Services.LoadingStage.FullyProcessed:
                    // 最终阶段：完全处理完成
                    LoadingText = $"完全处理完成: {Path.GetFileName(e.FilePath)}";
                    if (_plotUpdateEnabled)
                    {
                        UpdatePlots(); // 最终图表更新
                        UpdateParameters();
                    }
                    break;

                case LIVAnalyzer.Core.Services.LoadingStage.ProcessingFailed:
                    // 处理失败
                    LoadingText = $"处理失败: {Path.GetFileName(e.FilePath)}";
                    break;
            }
        }
        */

        /// <summary>
        /// 简化版渐进式文件加载
        /// </summary>
        private async Task LoadFilesWithSimpleProgressiveAsync(string[] filePaths)
        {
            var progressiveLoader = new LIVAnalyzer.Core.Services.SimpleProgressiveLoader(_csvLoader, _excelLoader);
            var loadedFileViewModels = new List<FileViewModel>();

            // 订阅进度更新事件
            progressiveLoader.ProgressUpdated += (sender, e) =>
            {
                Application.Current.Dispatcher.Invoke(() =>
                {
                    LoadingText = $"{e.Message} ({e.Percentage:F0}%)";
                });
            };

            // 订阅文件完成事件
            progressiveLoader.FileCompleted += (sender, e) =>
            {
                if (e.Data != null)
                {
                    Application.Current.Dispatcher.Invoke(() =>
                    {
                        if (e.Stage == "DataLoaded")
                        {
                            // 数据加载完成，立即显示文件
                            var fileViewModel = new FileViewModel(e.Data);
                            fileViewModel.PropertyChanged += (s, args) =>
                            {
                                if (args.PropertyName == nameof(FileViewModel.IsSelected))
                                {
                                    if (fileViewModel.IsSelected)
                                    {
                                        _lastSelectedFile = fileViewModel;
                                    }
                                    if (_plotUpdateEnabled)
                                    {
                                        UpdatePlots();
                                        UpdateParameters();
                                    }
                                }
                            };

                            LoadedFiles.Add(fileViewModel);
                            loadedFileViewModels.Add(fileViewModel);
                        }
                        else if (e.Stage == "ParametersCalculated")
                        {
                            // 参数计算完成，更新显示
                            if (_plotUpdateEnabled)
                            {
                                UpdateParameters();
                            }
                        }
                    });
                }
            };

            try
            {
                // 禁用图表更新以提升性能
                var originalUpdateEnabled = _plotUpdateEnabled;
                _plotUpdateEnabled = false;

                try
                {
                    // 开始渐进式加载
                    var loadedData = await progressiveLoader.LoadFilesAsync(filePaths);

                    // 显示加载结果
                    var successCount = loadedData.Count;
                    var failCount = filePaths.Length - successCount;

                    if (successCount > 0)
                    {
                        LoadingText = "正在更新界面...";

                        // 选择第一个文件
                        if (LoadedFiles.Any())
                        {
                            LoadedFiles.First().IsSelected = true;
                        }

                        LoggingService.LogUserAction($"成功加载 {successCount} 个文件");

                        if (failCount == 0)
                        {
                            LoadingText = $"成功加载 {successCount} 个文件";
                            await Task.Delay(1000); // 显示1秒后清除
                        }
                    }

                    if (failCount > 0)
                    {
                        MessageBox.Show($"加载完成！\n成功: {successCount} 个文件\n失败: {failCount} 个文件",
                            "加载结果", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
                finally
                {
                    // 恢复图表更新
                    _plotUpdateEnabled = originalUpdateEnabled;

                    // 触发一次完整的图表更新
                    if (_plotUpdateEnabled && LoadedFiles.Any(f => f.IsSelected))
                    {
                        UpdatePlots();
                        UpdateParameters();
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "渐进式加载失败");
                MessageBox.Show($"加载文件时发生错误: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 真正的渐进式文件加载 - 数据点渐进式读取
        /// </summary>
        private async Task LoadFilesWithTrueProgressiveAsync(string[] filePaths)
        {
            var progressiveLoader = new LIVAnalyzer.Data.Loaders.TrueProgressiveLoader(_csvLoader, _excelLoader);
            var fileViewModels = new Dictionary<string, FileViewModel>();

            // 订阅数据更新事件
            progressiveLoader.DataUpdated += (sender, e) =>
            {
                Application.Current.Dispatcher.Invoke(() =>
                {
                    // 不显示进度信息，保持界面简洁

                    // 更新或创建FileViewModel
                    if (e.CurrentFile != null)
                    {
                        var fileName = e.CurrentFile.FileName ?? "";

                        if (!fileViewModels.ContainsKey(fileName))
                        {
                            // 第一次创建这个文件的ViewModel
                            var fileViewModel = new FileViewModel(e.CurrentFile);
                            fileViewModel.PropertyChanged += (s, args) =>
                            {
                                if (args.PropertyName == nameof(FileViewModel.IsSelected))
                                {
                                    if (fileViewModel.IsSelected)
                                    {
                                        _lastSelectedFile = fileViewModel;
                                    }
                                    // 立即更新图表和参数
                                    UpdatePlots();
                                    UpdateParameters();
                                }
                            };

                            fileViewModels[fileName] = fileViewModel;
                            LoadedFiles.Add(fileViewModel);

                            // 确保第一个文件被选中
                            if (LoadedFiles.Count == 1)
                            {
                                fileViewModel.IsSelected = true;
                                _lastSelectedFile = fileViewModel;
                            }
                        }
                        else
                        {
                            // 更新现有的ViewModel数据
                            fileViewModels[fileName].UpdateData(e.CurrentFile);
                        }

                        // 调试信息：检查数据是否正确加载
                        var dataPointCount = e.CurrentFile?.CurrentPowerData?.Count ?? 0;
                        System.Diagnostics.Debug.WriteLine($"文件 {fileName} 加载了 {dataPointCount} 个数据点，间隔={e.Interval}");

                        // 强制更新图表（忽略_plotUpdateEnabled状态）
                        ForceUpdatePlots();

                        // 每次数据更新都计算参数
                        UpdateParameters();
                    }
                });
            };

            try
            {
                // 开始渐进式加载
                var loadedData = await progressiveLoader.LoadFilesProgressivelyAsync(filePaths);

                // 加载完成
                var successCount = loadedData.Count;
                var failCount = filePaths.Length - successCount;

                if (successCount > 0)
                {
                    // 选择第一个文件
                    if (LoadedFiles.Any())
                    {
                        LoadedFiles.First().IsSelected = true;
                    }

                    LoggingService.LogUserAction($"加载完成: {successCount} 个文件");

                    // 确保所有文件都计算了参数
                    foreach (var fileViewModel in LoadedFiles)
                    {
                        if (fileViewModel.Data.Parameters == null || !fileViewModel.Data.IsProcessed)
                        {
                            try
                            {
                                var processor = new LIVAnalyzer.Core.Processors.LIVDataProcessor();
                                var parameters = processor.CalculateParameters(fileViewModel.Data);
                                fileViewModel.Data.Parameters = parameters;
                                fileViewModel.Data.IsProcessed = true;
                            }
                            catch (Exception ex)
                            {
                                LoggingService.LogError(ex, $"计算参数失败: {fileViewModel.FileName}");
                            }
                        }
                    }

                    // 最终更新图表和参数
                    UpdatePlots();
                    UpdateParameters();
                }

                if (failCount > 0)
                {
                    MessageBox.Show($"加载完成！\n成功: {successCount} 个文件\n失败: {failCount} 个文件",
                        "加载结果", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "渐进式加载失败");
                MessageBox.Show($"渐进式加载失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 优化的批量文件加载
        /// </summary>
        private async Task LoadFilesOptimizedAsync(string[] filePaths)
        {
            var successCount = 0;
            var failCount = 0;
            var errorMessages = new List<string>();

            // 1. 预处理：过滤和验证文件
            var validFiles = new List<string>();
            foreach (var filePath in filePaths)
            {
                var fileName = Path.GetFileName(filePath);

                // 检查是否已加载
                if (LoadedFiles.Any(f => f.FileName == fileName))
                {
                    errorMessages.Add($"{fileName}: 文件已经加载");
                    failCount++;
                    continue;
                }

                // 检查文件格式
                var extension = Path.GetExtension(filePath).ToLower();
                if (extension != ".csv" && extension != ".xlsx" && extension != ".xls")
                {
                    errorMessages.Add($"{fileName}: 不支持的文件格式");
                    failCount++;
                    continue;
                }

                // 验证文件
                (bool isValid, string errorMessage) validationResult;
                if (extension == ".csv")
                {
                    validationResult = _csvLoader.ValidateFile(filePath);
                }
                else
                {
                    validationResult = _excelLoader.ValidateFile(filePath);
                }

                if (!validationResult.isValid)
                {
                    errorMessages.Add($"{fileName}: {validationResult.errorMessage}");
                    failCount++;
                    continue;
                }

                validFiles.Add(filePath);
            }

            if (!validFiles.Any())
            {
                ShowErrorMessages(errorMessages, failCount);
                return;
            }

            // 2. 并行加载文件数据
            LoadingText = "正在并行加载文件数据...";
            var loadTasks = validFiles.Select(async filePath =>
            {
                try
                {
                    var extension = Path.GetExtension(filePath).ToLower();
                    LIVMeasurementData? data = null;

                    if (extension == ".csv")
                    {
                        data = await _csvLoader.LoadCsvDataAsync(filePath);
                    }
                    else if (extension == ".xlsx" || extension == ".xls")
                    {
                        data = await _excelLoader.LoadExcelDataAsync(filePath);
                    }

                    return new { FilePath = filePath, Data = data, Success = true, Error = (string?)null };
                }
                catch (Exception ex)
                {
                    LoggingService.LogError(ex, $"加载文件失败: {filePath}");
                    return new { FilePath = filePath, Data = (LIVMeasurementData?)null, Success = false, Error = ex.Message };
                }
            }).ToArray();

            var loadResults = await Task.WhenAll(loadTasks);

            // 3. 批量处理数据
            LoadingText = "正在处理数据...";
            var newFileViewModels = new List<FileViewModel>();

            // 暂时禁用图表更新以提高性能
            var originalUpdateEnabled = _plotUpdateEnabled;
            _plotUpdateEnabled = false;

            try
            {
                foreach (var result in loadResults)
                {
                    if (!result.Success)
                    {
                        errorMessages.Add($"{Path.GetFileName(result.FilePath)}: {result.Error}");
                        failCount++;
                        continue;
                    }

                    if (result.Data != null && HasValidData(result.Data))
                    {
                        // 批量处理数据，减少缓存清理次数
                        var parameters = CalculateParametersWithSmoothing(result.Data);
                        result.Data.Parameters = parameters;
                        result.Data.IsProcessed = true;

                        // 计算发散角参数
                        if ((result.Data.HorizontalDivergenceData?.Any() == true) ||
                            (result.Data.VerticalDivergenceData?.Any() == true))
                        {
                            var divergenceResults = _dataProcessor.CalculateDivergenceParameters(result.Data);
                            result.Data.DivergenceResults = divergenceResults;
                        }

                        var fileViewModel = new FileViewModel(result.Data);
                        fileViewModel.PropertyChanged += (s, e) =>
                        {
                            if (e.PropertyName == nameof(FileViewModel.IsSelected))
                            {
                                if (fileViewModel.IsSelected)
                                {
                                    _lastSelectedFile = fileViewModel;
                                }
                                if (_plotUpdateEnabled) // 只在启用时更新
                                {
                                    UpdatePlots();
                                    UpdateParameters();
                                }
                            }
                        };

                        newFileViewModels.Add(fileViewModel);
                        successCount++;
                    }
                    else
                    {
                        errorMessages.Add($"{Path.GetFileName(result.FilePath)}: 文件中没有有效的数据");
                        failCount++;
                    }
                }

                // 4. 批量添加到UI
                LoadingText = "正在更新界面...";
                foreach (var fileViewModel in newFileViewModels)
                {
                    LoadedFiles.Add(fileViewModel);
                }
            }
            finally
            {
                // 恢复图表更新
                _plotUpdateEnabled = originalUpdateEnabled;
            }

            // 5. 最后统一更新图表和参数
            HasData = LoadedFiles.Any();
            StatusText = $"已加载 {successCount} 个数据文件" +
                       (failCount > 0 ? $"（失败 {failCount} 个）" : "");

            if (HasData && newFileViewModels.Any())
            {
                LoadingText = "正在生成图表...";
                UpdatePlots();
                UpdateParameters();
            }

            // 显示错误信息
            ShowErrorMessages(errorMessages, failCount);

            LoggingService.LogUserAction("文件加载完成", $"成功: {successCount}, 失败: {failCount}");
        }

        private bool _plotUpdateEnabled = true; // 控制图表更新的标志

        private void ShowErrorMessages(List<string> errorMessages, int failCount)
        {
            if (errorMessages.Any())
            {
                var errorSummary = string.Join("\n", errorMessages.Take(5));
                if (errorMessages.Count > 5)
                {
                    errorSummary += $"\n... 还有 {errorMessages.Count - 5} 个错误";
                }
                MessageBox.Show($"部分文件加载失败:\n\n{errorSummary}", "文件加载警告",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }
        
        private async Task ExportDataAsync(List<FileViewModel> selectedFiles, string filePath)
        {
            IsLoading = true;
            LoadingText = "正在导出数据...";
            
            try
            {
                // 准备FileDataModel列表供导出
                var fileDataModels = selectedFiles.Select(f => new FileDataModel(f.Data)).ToList();
                
                // 为每个文件设置I1和I2参数以及重新计算发散角
                foreach (var model in fileDataModels)
                {
                    if (model.Data.Parameters != null)
                    {
                        // 计算I1和I2对应的参数
                        if (model.Data.CurrentPowerData.Any() && model.Data.CurrentVoltageData.Any())
                        {
                            // I1参数
                            var i1Power = InterpolateValue(model.Data.CurrentPowerData, I1Current);
                            var i1Voltage = InterpolateValue(model.Data.CurrentVoltageData, I1Current);
                            if (i1Power.HasValue && i1Voltage.HasValue && i1Voltage.Value > 0)
                            {
                                model.Data.Parameters.I1Current = I1Current;
                                model.Data.Parameters.I1Power = i1Power.Value;
                                model.Data.Parameters.I1Voltage = i1Voltage.Value;
                                model.Data.Parameters.I1Efficiency = (i1Power.Value / (I1Current * i1Voltage.Value)) * 100;
                            }
                            
                            // I2参数
                            var i2Power = InterpolateValue(model.Data.CurrentPowerData, I2Current);
                            var i2Voltage = InterpolateValue(model.Data.CurrentVoltageData, I2Current);
                            if (i2Power.HasValue && i2Voltage.HasValue && i2Voltage.Value > 0)
                            {
                                model.Data.Parameters.I2Current = I2Current;
                                model.Data.Parameters.I2Power = i2Power.Value;
                                model.Data.Parameters.I2Voltage = i2Voltage.Value;
                                model.Data.Parameters.I2Efficiency = (i2Power.Value / (I2Current * i2Voltage.Value)) * 100;
                            }
                        }
                    }
                    
                    // 重新计算发散角参数，确保使用当前的平滑设置
                    if ((model.Data.HorizontalDivergenceData?.Any() == true) || 
                        (model.Data.VerticalDivergenceData?.Any() == true))
                    {
                        var divergenceProcessor = new LIVAnalyzer.Core.Processors.DivergenceProcessor();
                        
                        // 确保有发散角结果对象
                        if (model.Data.DivergenceResults == null)
                        {
                            model.Data.DivergenceResults = new LIVAnalyzer.Models.DivergenceResults();
                        }
                        
                        // 处理水平发散角数据
                        if (model.Data.HorizontalDivergenceData?.Any() == true)
                        {
                            // 应用当前的平滑设置
                            var hData = IsSmoothingEnabled ?
                                _dataProcessor.SmoothData(model.Data.HorizontalDivergenceData.ToList(), _currentSmoothingConfig) :
                                model.Data.HorizontalDivergenceData.ToList();
                            
                            // 准备数据格式（角度在X，强度在Y）
                            var angleData = hData.Select(p => new LIVAnalyzer.Models.DataPoint(p.X, p.X)).ToList();
                            var intensityData = hData.Select(p => new LIVAnalyzer.Models.DataPoint(p.X, p.Y)).ToList();
                            var hffAngles = divergenceProcessor.CalculateDivergence(angleData, intensityData, IsSmoothingEnabled, SmoothingWindowSize);
                            
                            if (hffAngles.IsValid)
                            {
                                model.Data.DivergenceResults.HorizontalFWHM = hffAngles.FWHM;
                                model.Data.DivergenceResults.HorizontalFW1e2 = hffAngles.FW1e2;
                                model.Data.DivergenceResults.HorizontalFW1e2PowerContainment = hffAngles.FW1e2PowerContainment;
                                model.Data.DivergenceResults.HorizontalFW95 = hffAngles.FW95;
                            }
                        }
                        
                        // 处理垂直发散角数据
                        if (model.Data.VerticalDivergenceData?.Any() == true)
                        {
                            // 应用当前的平滑设置
                            var vData = IsSmoothingEnabled ?
                                _dataProcessor.SmoothData(model.Data.VerticalDivergenceData.ToList(), _currentSmoothingConfig) :
                                model.Data.VerticalDivergenceData.ToList();
                            
                            // 准备数据格式（角度在X，强度在Y）
                            var angleData = vData.Select(p => new LIVAnalyzer.Models.DataPoint(p.X, p.X)).ToList();
                            var intensityData = vData.Select(p => new LIVAnalyzer.Models.DataPoint(p.X, p.Y)).ToList();
                            var vffAngles = divergenceProcessor.CalculateDivergence(angleData, intensityData, IsSmoothingEnabled, SmoothingWindowSize);
                            
                            if (vffAngles.IsValid)
                            {
                                model.Data.DivergenceResults.VerticalFWHM = vffAngles.FWHM;
                                model.Data.DivergenceResults.VerticalFW1e2 = vffAngles.FW1e2;
                                model.Data.DivergenceResults.VerticalFW1e2PowerContainment = vffAngles.FW1e2PowerContainment;
                                model.Data.DivergenceResults.VerticalFW95 = vffAngles.FW95;
                            }
                        }
                    }
                }
                
                // 使用ExcelDataExporter导出数据
                var exporter = new ExcelDataExporter();
                await exporter.ExportDataAsync(fileDataModels, filePath);
                
                LoggingService.LogUserAction("数据导出完成", filePath);
                MessageBox.Show($"数据导出完成！\n成功导出 {selectedFiles.Count} 个文件的数据\n结果已保存至：\n{filePath}", 
                    "成功", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            finally
            {
                IsLoading = false;
                LoadingText = "";
            }
        }
        
        private double? InterpolateValue(List<LIVAnalyzer.Models.DataPoint> data, double targetX)
        {
            if (!data.Any()) return null;
            
            // 排序数据
            var sortedData = data.OrderBy(p => p.X).ToList();
            
            // 边界情况
            if (targetX <= sortedData.First().X) return sortedData.First().Y;
            if (targetX >= sortedData.Last().X) return sortedData.Last().Y;
            
            // 找到插值点
            for (int i = 0; i < sortedData.Count - 1; i++)
            {
                if (targetX >= sortedData[i].X && targetX <= sortedData[i + 1].X)
                {
                    // 线性插值
                    var x1 = sortedData[i].X;
                    var x2 = sortedData[i + 1].X;
                    var y1 = sortedData[i].Y;
                    var y2 = sortedData[i + 1].Y;
                    
                    return y1 + (y2 - y1) * (targetX - x1) / (x2 - x1);
                }
            }
            
            return null;
        }
        
        private async Task BatchProcessAsync(string folderPath)
        {
            IsLoading = true;
            LoadingText = "正在批量处理...";
            
            try
            {
                LoggingService.LogUserAction($"开始批量处理 {folderPath}");
                
                // 使用BatchProcessor进行批量处理
                var batchProcessor = new OptimizedBatchProcessor();
                var progressCallback = new Progress<BatchProgressInfo>(info =>
                {
                    LoadingText = $"正在处理: {info.CurrentFileName} ({info.CurrentFile}/{info.TotalFiles})";
                });
                
                var result = await batchProcessor.ProcessBatchAsync(
                    folderPath, 
                    I1Current, 
                    I2Current,
                    progressCallback);
                
                if (result.IsSuccess)
                {
                    LoggingService.LogUserAction("批量处理完成", result.OutputFilePath ?? "");
                    MessageBox.Show($"批量处理完成！\n成功处理 {result.ProcessedFiles} 个文件\n失败 {result.FailedFiles} 个文件\n结果已保存至：\n{result.OutputFilePath}", 
                        "成功", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    MessageBox.Show($"批量处理失败：{result.ErrorMessage}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "批量处理失败");
                MessageBox.Show($"批量处理失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
                LoadingText = "";
            }
        }
        
        /// <summary>
        /// 强制更新图表（忽略_plotUpdateEnabled状态）
        /// </summary>
        private async void ForceUpdatePlots()
        {
            try
            {
                // 取消之前的更新操作
                _plotUpdateCancellation?.Cancel();
                _plotUpdateCancellation = new CancellationTokenSource();
                var cancellationToken = _plotUpdateCancellation.Token;

                await _plotUpdateSemaphore.WaitAsync(cancellationToken);
                try
                {
                    if (cancellationToken.IsCancellationRequested) return;

                    var selectedFiles = LoadedFiles.Where(f => f.IsSelected).ToList();

                    // 使用增量更新而不是完全重建
                    await UpdatePlotsIncremental(selectedFiles, cancellationToken);
                }
                finally
                {
                    _plotUpdateSemaphore.Release();
                }
            }
            catch (OperationCanceledException)
            {
                // 操作被取消是正常情况
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "强制更新图表时发生错误");
            }
        }

        private async void UpdatePlots()
        {
            // 如果图表更新被禁用，直接返回
            if (!_plotUpdateEnabled) return;

            try
            {
                // 取消之前的更新操作
                _plotUpdateCancellation?.Cancel();
                _plotUpdateCancellation = new CancellationTokenSource();
                var cancellationToken = _plotUpdateCancellation.Token;

                await _plotUpdateSemaphore.WaitAsync(cancellationToken);
                try
                {
                    if (cancellationToken.IsCancellationRequested) return;

                    var selectedFiles = LoadedFiles.Where(f => f.IsSelected).ToList();

                    // 使用增量更新而不是完全重建
                    await UpdatePlotsIncremental(selectedFiles, cancellationToken);
                }
                finally
                {
                    _plotUpdateSemaphore.Release();
                }
            }
            catch (OperationCanceledException)
            {
                // 操作被取消是正常情况，不需要显示错误（包括TaskCanceledException）
                LoggingService.LogInformation("图表更新操作被取消");
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "更新图表时发生错误");
                // 只有真正的错误才显示给用户
                MessageBox.Show($"更新图表时发生错误：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        /// <summary>
        /// 增量更新图表，避免闪烁
        /// </summary>
        private async Task UpdatePlotsIncremental(List<FileViewModel> selectedFiles, CancellationToken cancellationToken)
        {
            // 初始化图表模型（如果尚未初始化）
            InitializePlotModelsIfNeeded();

            // 只在必要时更新图表设置
            if (_needsSettingsUpdate)
            {
                UpdateAllPlotSettings();
                _needsSettingsUpdate = false;
            }

            if (!selectedFiles.Any())
            {
                // 如果没有选中文件，只清空数据系列，保留图表结构
                ClearPlotSeries();
                return;
            }

            // 并行更新图表数据
            var tasks = new List<Task>
            {
                UpdateLIVPlotIncremental(selectedFiles, cancellationToken),
                UpdateSpectrumPlotIncremental(selectedFiles, cancellationToken),
                UpdateEfficiencyPlotIncremental(selectedFiles, cancellationToken),
                UpdateDivergencePlotIncremental(selectedFiles, cancellationToken)
            };

            await Task.WhenAll(tasks);
        }

        /// <summary>
        /// 初始化图表模型（如果尚未初始化）
        /// </summary>
        private void InitializePlotModelsIfNeeded()
        {
            if (LIVPlotModel == null)
            {
                LIVPlotModel = CreatePlotModel("LIV曲线", "电流 (A)", "功率 (W) / 电压 (V)");
            }

            if (SpectrumPlotModel == null)
            {
                SpectrumPlotModel = CreatePlotModel("光谱", "波长 (nm)", "强度");
            }

            if (EfficiencyPlotModel == null)
            {
                EfficiencyPlotModel = CreatePlotModel("效率", "电流 (A)", "效率 (%)");
            }

            if (DivergencePlotModel == null)
            {
                DivergencePlotModel = CreatePlotModel("发散角", "角度 (°)", "强度");
            }

            if (HffDivergencePlotModel == null)
            {
                HffDivergencePlotModel = CreatePlotModel("水平发散角", "角度 (°)", "强度");
            }

            if (VffDivergencePlotModel == null)
            {
                VffDivergencePlotModel = CreatePlotModel("垂直发散角", "角度 (°)", "强度");
            }
        }

        /// <summary>
        /// 只清空数据系列，保留图表结构
        /// </summary>
        private void ClearPlotSeries()
        {
            LIVPlotModel?.Series.Clear();
            SpectrumPlotModel?.Series.Clear();
            EfficiencyPlotModel?.Series.Clear();
            DivergencePlotModel?.Series.Clear();
            HffDivergencePlotModel?.Series.Clear();
            VffDivergencePlotModel?.Series.Clear();

            // 刷新图表显示
            LIVPlotModel?.InvalidatePlot(false);
            SpectrumPlotModel?.InvalidatePlot(false);
            EfficiencyPlotModel?.InvalidatePlot(false);
            DivergencePlotModel?.InvalidatePlot(false);
            HffDivergencePlotModel?.InvalidatePlot(false);
            VffDivergencePlotModel?.InvalidatePlot(false);
        }

        /// <summary>
        /// 增量更新LIV图表
        /// </summary>
        private async Task UpdateLIVPlotIncremental(List<FileViewModel> selectedFiles, CancellationToken cancellationToken)
        {
            try
            {
                if (LIVPlotModel == null) return;

                // 当平滑设置改变时，需要强制重建所有系列
                // 清空现有系列，重新创建以应用新的平滑设置
                LIVPlotModel.Series.Clear();

                // 重新创建所有系列
                var seriesDataList = new List<SeriesData>();
                var colorIndex = 0;

                foreach (var file in selectedFiles)
                {
                    if (cancellationToken.IsCancellationRequested) return;

                    var currentPowerData = file.Data.CurrentPowerData;
                    var currentVoltageData = file.Data.CurrentVoltageData;
                    var fileColor = GetSeriesColor(colorIndex);

                    // 添加功率曲线
                    if (currentPowerData.Any())
                    {
                        // 应用平滑处理
                        var powerData = IsSmoothingEnabled ?
                            _dataProcessor.SmoothData(currentPowerData.ToList(), _currentSmoothingConfig) :
                            currentPowerData.ToList();

                        var powerSeries = new SeriesData
                        {
                            Title = $"{file.FileName} - 功率",
                            YAxisKey = "PowerAxis",
                            Color = fileColor,
                            Points = powerData,
                            LineStyle = IsLineStyle ? LineStyle.Solid : (IsPointLineStyle ? LineStyle.Solid : LineStyle.None),
                            MarkerType = ConvertToMarkerType(_currentChartSettings.MarkerType),
                            MarkerSize = _currentChartSettings.MarkerSize,
                            StrokeThickness = _currentChartSettings.LineThickness
                        };
                        seriesDataList.Add(powerSeries);
                    }

                    // 添加电压曲线
                    if (currentVoltageData.Any())
                    {
                        // 应用平滑处理
                        var voltageData = IsSmoothingEnabled ?
                            _dataProcessor.SmoothData(currentVoltageData.ToList(), _currentSmoothingConfig) :
                            currentVoltageData.ToList();

                        var voltageSeries = new SeriesData
                        {
                            Title = $"{file.FileName} - 电压",
                            YAxisKey = "VoltageAxis",
                            Color = fileColor,
                            Points = voltageData,
                            LineStyle = LineStyle.Dash,
                            MarkerType = ConvertToMarkerType(_currentChartSettings.MarkerType),
                            MarkerSize = _currentChartSettings.MarkerSize,
                            StrokeThickness = _currentChartSettings.LineThickness
                        };
                        seriesDataList.Add(voltageSeries);
                    }

                    // 添加效率曲线（如果启用）
                    if (_currentChartSettings.ShowEfficiencyOverlay && currentPowerData.Any() && currentVoltageData.Any())
                    {
                        var efficiencyData = CalculateEfficiencyData(currentPowerData, currentVoltageData);

                        if (efficiencyData.Any())
                        {
                            // 应用平滑处理
                            var smoothedEfficiencyData = IsSmoothingEnabled ?
                                _dataProcessor.SmoothData(efficiencyData, _currentSmoothingConfig) :
                                efficiencyData;

                            var efficiencySeries = new SeriesData
                            {
                                Title = $"{file.FileName} - 效率",
                                YAxisKey = "EfficiencyAxis",
                                Color = GetEfficiencySeriesColor(fileColor), // 使用不同的颜色变体
                                Points = smoothedEfficiencyData,
                                LineStyle = IsLineStyle ? LineStyle.Solid : (IsPointLineStyle ? LineStyle.Solid : LineStyle.None),
                                MarkerType = ConvertToMarkerType(_currentChartSettings.MarkerType),
                                MarkerSize = _currentChartSettings.MarkerSize,
                                StrokeThickness = _currentChartSettings.LineThickness
                            };
                            seriesDataList.Add(efficiencySeries);
                        }
                    }

                    colorIndex++;
                }

                // 批量添加新系列
                if (seriesDataList.Any())
                {
                    await _plotManager.BatchUpdateSeriesAsync(LIVPlotModel, seriesDataList, cancellationToken, false);

                    // 如果添加了新系列，自动调整轴范围
                    LIVPlotModel.ResetAllAxes();
                    LIVPlotModel.InvalidatePlot(true);
                }
                else
                {
                    // 如果没有新系列要添加，只刷新现有图表
                    LIVPlotModel.InvalidatePlot(false);
                }
            }
            catch (OperationCanceledException)
            {
                // 操作被取消，正常情况
            }
        }

        /// <summary>
        /// 增量更新光谱图表
        /// </summary>
        private async Task UpdateSpectrumPlotIncremental(List<FileViewModel> selectedFiles, CancellationToken cancellationToken)
        {
            try
            {
                if (SpectrumPlotModel == null) return;

                // 当平滑设置改变时，需要强制重建所有系列
                // 清空现有系列，重新创建以应用新的平滑设置
                SpectrumPlotModel.Series.Clear();

                // 重新创建所有系列
                var seriesDataList = new List<SeriesData>();
                var colorIndex = 0;

                foreach (var file in selectedFiles)
                {
                    if (cancellationToken.IsCancellationRequested) return;

                    if (file.Data.WavelengthIntensityData?.Any() == true)
                    {
                        // 应用平滑处理
                        var spectrumData = IsSmoothingEnabled ?
                            _dataProcessor.SmoothData(file.Data.WavelengthIntensityData.ToList(), _currentSmoothingConfig) :
                            file.Data.WavelengthIntensityData.ToList();

                        var seriesData = new SeriesData
                        {
                            Title = file.FileName,
                            Points = spectrumData,
                            Color = GetSeriesColor(colorIndex),
                            LineStyle = GetCurrentLineStyle(),
                            MarkerType = GetCurrentMarkerType(),
                            MarkerSize = _currentChartSettings.MarkerSize,
                            StrokeThickness = _currentChartSettings.LineThickness
                        };
                        seriesDataList.Add(seriesData);
                    }
                    colorIndex++;
                }

                if (seriesDataList.Any())
                {
                    await _plotManager.BatchUpdateSeriesAsync(SpectrumPlotModel, seriesDataList, cancellationToken, true);

                    // 自动调整轴范围
                    SpectrumPlotModel.ResetAllAxes();
                    SpectrumPlotModel.InvalidatePlot(true);
                }
                else
                {
                    SpectrumPlotModel.InvalidatePlot(false);
                }
            }
            catch (OperationCanceledException)
            {
                // 操作被取消，正常情况
            }
        }

        /// <summary>
        /// 增量更新效率图表
        /// </summary>
        private async Task UpdateEfficiencyPlotIncremental(List<FileViewModel> selectedFiles, CancellationToken cancellationToken)
        {
            try
            {
                if (EfficiencyPlotModel == null) return;

                // 当平滑设置改变时，需要强制重建所有系列
                // 清空现有系列，重新创建以应用新的平滑设置
                EfficiencyPlotModel.Series.Clear();

                // 重新创建所有系列
                var seriesDataList = new List<SeriesData>();
                var colorIndex = 0;

                foreach (var file in selectedFiles)
                {
                    if (cancellationToken.IsCancellationRequested) return;

                    if (file.Data.CurrentPowerData?.Any() == true)
                    {
                        // 应用平滑处理到功率数据
                        var powerData = IsSmoothingEnabled ?
                            _dataProcessor.SmoothData(file.Data.CurrentPowerData.ToList(), _currentSmoothingConfig) :
                            file.Data.CurrentPowerData.ToList();

                        var efficiencyData = powerData
                            .Where(d => d.X > 0 && d.Y > 0)
                            .Select(d => new LIVAnalyzer.Models.DataPoint(d.X, d.Y / d.X))
                            .ToList();

                        if (efficiencyData.Any())
                        {
                            var seriesData = new SeriesData
                            {
                                Title = file.FileName,
                                Points = efficiencyData,
                                Color = GetSeriesColor(colorIndex),
                                LineStyle = GetCurrentLineStyle(),
                                MarkerType = GetCurrentMarkerType(),
                                MarkerSize = _currentChartSettings.MarkerSize,
                                StrokeThickness = _currentChartSettings.LineThickness
                            };
                            seriesDataList.Add(seriesData);
                        }
                    }
                    colorIndex++;
                }

                if (seriesDataList.Any())
                {
                    await _plotManager.BatchUpdateSeriesAsync(EfficiencyPlotModel, seriesDataList, cancellationToken, true);

                    // 自动调整轴范围
                    EfficiencyPlotModel.ResetAllAxes();
                    EfficiencyPlotModel.InvalidatePlot(true);
                }
                else
                {
                    EfficiencyPlotModel.InvalidatePlot(false);
                }
            }
            catch (OperationCanceledException)
            {
                // 操作被取消，正常情况
            }
        }

        /// <summary>
        /// 增量更新发散角图表
        /// </summary>
        private async Task UpdateDivergencePlotIncremental(List<FileViewModel> selectedFiles, CancellationToken cancellationToken)
        {
            try
            {
                if (HffDivergencePlotModel == null || VffDivergencePlotModel == null) return;

                // 对于发散角图表，由于需要重新计算虚线，我们使用完全重建的方式
                // 但保持图表结构，只清空系列
                HffDivergencePlotModel.Series.Clear();
                VffDivergencePlotModel.Series.Clear();

                var divergenceResults = new System.Text.StringBuilder();
                var colorIndex = 0;

                foreach (var file in selectedFiles)
                {
                    if (cancellationToken.IsCancellationRequested) return;

                    bool hasHffData = file.Data.HorizontalDivergenceData?.Any() == true;
                    bool hasVffData = file.Data.VerticalDivergenceData?.Any() == true;

                    if (!hasHffData && !hasVffData)
                    {
                        continue;
                    }

                    divergenceResults.AppendLine($"文件: {file.FileName}");
                    divergenceResults.AppendLine(new string('-', 30));

                    // 处理水平发散角数据
                    if (hasHffData)
                    {
                        // 应用平滑处理
                        var hData = IsSmoothingEnabled ?
                            _dataProcessor.SmoothData(file.Data.HorizontalDivergenceData, _currentSmoothingConfig) :
                            file.Data.HorizontalDivergenceData;

                        var series = new LineSeries
                        {
                            Title = file.FileName,
                            Color = GetSeriesColor(colorIndex),
                            TrackerFormatString = "{0}\n角度 (°): {2:F2}\n强度: {4:F2}"
                        };
                        SetLineStyle(series);

                        foreach (var point in hData)
                        {
                            series.Points.Add(new OxyPlot.DataPoint(point.X, point.Y));
                        }
                        HffDivergencePlotModel.Series.Add(series);

                        // 计算发散角
                        var divergenceProcessor = new LIVAnalyzer.Core.Processors.DivergenceProcessor();
                        var angleData = hData.Select(p => new LIVAnalyzer.Models.DataPoint(p.X, p.X)).ToList();
                        var intensityData = hData.Select(p => new LIVAnalyzer.Models.DataPoint(p.X, p.Y)).ToList();
                        var hffAngles = divergenceProcessor.CalculateDivergence(angleData, intensityData, IsSmoothingEnabled, SmoothingWindowSize);

                        if (hffAngles.IsValid)
                        {
                            divergenceResults.AppendLine("水平发散角 (HFF):");
                            divergenceResults.AppendLine($"  FWHM: {hffAngles.FWHM:F2}°");
                            divergenceResults.AppendLine($"  FW(1/e²): {hffAngles.FW1e2:F2}° (能量占比: {hffAngles.FW1e2PowerContainment:P3})");
                            divergenceResults.AppendLine($"  FW95%: {hffAngles.FW95:F2}°");

                            // 保存计算结果
                            if (file.Data.DivergenceResults == null)
                            {
                                file.Data.DivergenceResults = new LIVAnalyzer.Models.DivergenceResults();
                            }
                            file.Data.DivergenceResults.HorizontalFWHM = hffAngles.FWHM;
                            file.Data.DivergenceResults.HorizontalFW1e2 = hffAngles.FW1e2;
                            file.Data.DivergenceResults.HorizontalFW1e2PowerContainment = hffAngles.FW1e2PowerContainment;
                            file.Data.DivergenceResults.HorizontalFW95 = hffAngles.FW95;

                            // 添加计算线到图表
                            AddDivergenceLines(HffDivergencePlotModel, hData, hffAngles, colorIndex);
                        }
                        else
                        {
                            divergenceResults.AppendLine($"水平发散角 (HFF): 计算失败 - {hffAngles.ErrorMessage}");
                        }
                    }

                    // 处理垂直发散角数据
                    if (hasVffData)
                    {
                        // 应用平滑处理
                        var vData = IsSmoothingEnabled ?
                            _dataProcessor.SmoothData(file.Data.VerticalDivergenceData, _currentSmoothingConfig) :
                            file.Data.VerticalDivergenceData;

                        var series = new LineSeries
                        {
                            Title = file.FileName,
                            Color = GetSeriesColor(colorIndex),
                            TrackerFormatString = "{0}\n角度 (°): {2:F2}\n强度: {4:F2}"
                        };
                        SetLineStyle(series);

                        foreach (var point in vData)
                        {
                            series.Points.Add(new OxyPlot.DataPoint(point.X, point.Y));
                        }
                        VffDivergencePlotModel.Series.Add(series);

                        // 计算发散角
                        var divergenceProcessor = new LIVAnalyzer.Core.Processors.DivergenceProcessor();
                        var angleData = vData.Select(p => new LIVAnalyzer.Models.DataPoint(p.X, p.X)).ToList();
                        var intensityData = vData.Select(p => new LIVAnalyzer.Models.DataPoint(p.X, p.Y)).ToList();
                        var vffAngles = divergenceProcessor.CalculateDivergence(angleData, intensityData, IsSmoothingEnabled, SmoothingWindowSize);

                        if (vffAngles.IsValid)
                        {
                            divergenceResults.AppendLine("\n垂直发散角 (VFF):");
                            divergenceResults.AppendLine($"  FWHM: {vffAngles.FWHM:F2}°");
                            divergenceResults.AppendLine($"  FW(1/e²): {vffAngles.FW1e2:F2}° (能量占比: {vffAngles.FW1e2PowerContainment:P3})");
                            divergenceResults.AppendLine($"  FW95%: {vffAngles.FW95:F2}°");

                            // 保存计算结果
                            if (file.Data.DivergenceResults == null)
                            {
                                file.Data.DivergenceResults = new LIVAnalyzer.Models.DivergenceResults();
                            }
                            file.Data.DivergenceResults.VerticalFWHM = vffAngles.FWHM;
                            file.Data.DivergenceResults.VerticalFW1e2 = vffAngles.FW1e2;
                            file.Data.DivergenceResults.VerticalFW1e2PowerContainment = vffAngles.FW1e2PowerContainment;
                            file.Data.DivergenceResults.VerticalFW95 = vffAngles.FW95;

                            // 添加计算线到图表
                            AddDivergenceLines(VffDivergencePlotModel, vData, vffAngles, colorIndex);
                        }
                        else
                        {
                            divergenceResults.AppendLine($"\n垂直发散角 (VFF): 计算失败 - {vffAngles.ErrorMessage}");
                        }
                    }

                    divergenceResults.AppendLine();
                    colorIndex++;
                }

                // 更新发散角结果文本
                DivergenceResultText = divergenceResults.Length > 0
                    ? divergenceResults.ToString()
                    : "请选择包含发散角数据的文件...";

                // 自动调整轴范围并刷新图表
                if (HffDivergencePlotModel.Series.Any())
                {
                    HffDivergencePlotModel.ResetAllAxes();
                    HffDivergencePlotModel.InvalidatePlot(true);
                }
                else
                {
                    HffDivergencePlotModel.InvalidatePlot(false);
                }

                if (VffDivergencePlotModel.Series.Any())
                {
                    VffDivergencePlotModel.ResetAllAxes();
                    VffDivergencePlotModel.InvalidatePlot(true);
                }
                else
                {
                    VffDivergencePlotModel.InvalidatePlot(false);
                }
            }
            catch (OperationCanceledException)
            {
                // 操作被取消，正常情况
            }
        }

        /// <summary>
        /// 获取当前线条样式
        /// </summary>
        private LineStyle GetCurrentLineStyle()
        {
            if (IsLineStyle || IsPointLineStyle)
                return LineStyle.Solid;
            else
                return LineStyle.None;
        }

        /// <summary>
        /// 获取当前标记类型
        /// </summary>
        private MarkerType GetCurrentMarkerType()
        {
            if (IsPointStyle || IsPointLineStyle)
                return ConvertToMarkerType(_currentChartSettings.MarkerType);
            else if (IsLineStyle && _currentChartSettings.MarkerType != "无")
                return ConvertToMarkerType(_currentChartSettings.MarkerType);
            else
                return MarkerType.None;
        }
        
        private void UpdateAllPlotSettings()
        {
            // 更新所有图表的显示设置（图例、网格、十字光标）
            UpdateSinglePlotSettings(LIVPlotModel, "LIV");
            UpdateSinglePlotSettings(SpectrumPlotModel, "光谱");
            UpdateSinglePlotSettings(EfficiencyPlotModel, "效率");
            UpdateSinglePlotSettings(DivergencePlotModel, "发散角");
            UpdateSinglePlotSettings(HffDivergencePlotModel, "HFF");
            UpdateSinglePlotSettings(VffDivergencePlotModel, "VFF");
        }
        
        private void UpdateSinglePlotSettings(PlotModel? plotModel, string plotName)
        {
            if (plotModel == null) return;

            // 重新配置图例
            ConfigureLegend(plotModel);

            // 更新网格显示/隐藏设置（只更新样式，不更新颜色）
            UpdateGridSettings(plotModel);

            // 应用主题（设置默认的主题颜色）
            ApplyThemeToPlot(plotModel);

            // 应用用户设置（覆盖主题颜色）
            ApplySettingsToPlot(plotModel);

            // 强制刷新图表
            plotModel.InvalidatePlot(true);
        }

        private void UpdateGridSettings(PlotModel model)
        {
            // 配置网格显示
            foreach (var axis in model.Axes)
            {
                if (ShowGrid)
                {
                    axis.MajorGridlineStyle = LineStyle.Solid;
                    axis.MinorGridlineStyle = LineStyle.Dot;
                    // 不在这里设置颜色，让 ApplyThemeToPlot 和 ApplySettingsToPlot 处理颜色
                }
                else
                {
                    axis.MajorGridlineStyle = LineStyle.None;
                    axis.MinorGridlineStyle = LineStyle.None;
                }
            }
        }

        private void UpdateGridSettings()
        {
            // 更新所有图表的网格设置
            var plots = new[] { LIVPlotModel, SpectrumPlotModel, EfficiencyPlotModel, 
                               DivergencePlotModel, HffDivergencePlotModel, VffDivergencePlotModel };
            
            foreach (var plot in plots)
            {
                if (plot != null)
                {
                    UpdateGridSettings(plot);
                    plot.InvalidatePlot(false);
                }
            }
        }

        private void UpdateLegendSettings()
        {
            // 更新所有图表的图例设置
            var plots = new[] { LIVPlotModel, SpectrumPlotModel, EfficiencyPlotModel, 
                               DivergencePlotModel, HffDivergencePlotModel, VffDivergencePlotModel };
            
            foreach (var plot in plots)
            {
                if (plot != null && plot.Legends.Any())
                {
                    var legend = plot.Legends.First();
                    legend.IsLegendVisible = ShowLegend;
                    plot.InvalidatePlot(false);
                }
            }
        }


        
        /// <summary>
        /// 计算效率曲线数据点
        /// </summary>
        private List<LIVAnalyzer.Models.DataPoint> CalculateEfficiencyData(List<LIVAnalyzer.Models.DataPoint> currentPowerData, List<LIVAnalyzer.Models.DataPoint> currentVoltageData)
        {
            var efficiencyData = new List<LIVAnalyzer.Models.DataPoint>();

            if (!currentPowerData.Any() || !currentVoltageData.Any())
                return efficiencyData;

            // 创建电流到电压的映射
            var voltageMap = currentVoltageData.ToDictionary(p => p.X, p => p.Y);

            foreach (var powerPoint in currentPowerData)
            {
                // 检查是否有对应的电压数据
                if (voltageMap.TryGetValue(powerPoint.X, out var voltage))
                {
                    // 计算效率：η = P / (I × V) × 100%
                    if (powerPoint.X > 0 && voltage > 0 && powerPoint.Y >= 0)
                    {
                        var efficiency = (powerPoint.Y / (powerPoint.X * voltage)) * 100;

                        // 过滤异常值（效率不应超过100%或为负值）
                        if (efficiency >= 0 && efficiency <= 100)
                        {
                            efficiencyData.Add(new LIVAnalyzer.Models.DataPoint(powerPoint.X, efficiency));
                        }
                    }
                }
            }

            return efficiencyData;
        }

        /// <summary>
        /// 为效率曲线生成颜色变体
        /// </summary>
        private OxyColor GetEfficiencySeriesColor(OxyColor baseColor)
        {
            // 将基础颜色转换为HSV，调整饱和度和亮度来生成变体
            var hsv = baseColor.ToHsv();

            // 增加饱和度，降低亮度，使效率曲线颜色更深一些
            var newSaturation = Math.Min(1.0, hsv[1] * 1.2);
            var newValue = Math.Max(0.3, hsv[2] * 0.8);

            return OxyColor.FromHsv(hsv[0], newSaturation, newValue);
        }

        private async Task UpdateLIVPlot(List<FileViewModel> selectedFiles)
        {
            try
            {
                if (LIVPlotModel == null) return;

                var seriesDataList = new List<SeriesData>();
                var colorIndex = 0;

                foreach (var file in selectedFiles)
                {
                    // 检查取消令牌
                    if (_plotUpdateCancellation?.Token.IsCancellationRequested == true) return;

                    var currentPowerData = file.Data.CurrentPowerData;
                    var currentVoltageData = file.Data.CurrentVoltageData;
                    var fileColor = GetSeriesColor(colorIndex);

                    if (currentPowerData.Any())
                    {
                        // 应用平滑处理
                        var powerData = IsSmoothingEnabled ?
                            _dataProcessor.SmoothData(currentPowerData.ToList(), _currentSmoothingConfig) :
                            currentPowerData.ToList();

                        var powerSeries = new SeriesData
                        {
                            Title = $"{file.FileName} - 功率",
                            YAxisKey = "PowerAxis",
                            Color = fileColor,
                            Points = powerData,
                            LineStyle = IsLineStyle ? LineStyle.Solid : (IsPointLineStyle ? LineStyle.Solid : LineStyle.None),
                            MarkerType = ConvertToMarkerType(_currentChartSettings.MarkerType),
                            MarkerSize = _currentChartSettings.MarkerSize,
                            StrokeThickness = _currentChartSettings.LineThickness
                        };
                        seriesDataList.Add(powerSeries);
                    }

                    if (currentVoltageData.Any())
                    {
                        // 应用平滑处理
                        var voltageData = IsSmoothingEnabled ?
                            _dataProcessor.SmoothData(currentVoltageData.ToList(), _currentSmoothingConfig) :
                            currentVoltageData.ToList();

                        var voltageSeries = new SeriesData
                        {
                            Title = $"{file.FileName} - 电压",
                            YAxisKey = "VoltageAxis",
                            Color = fileColor,
                            Points = voltageData,
                            LineStyle = LineStyle.Dash,
                            MarkerType = ConvertToMarkerType(_currentChartSettings.MarkerType),
                            MarkerSize = _currentChartSettings.MarkerSize,
                            StrokeThickness = _currentChartSettings.LineThickness
                        };
                        seriesDataList.Add(voltageSeries);
                    }

                    // 添加效率曲线（如果启用）
                    if (_currentChartSettings.ShowEfficiencyOverlay && currentPowerData.Any() && currentVoltageData.Any())
                    {
                        var efficiencyData = CalculateEfficiencyData(currentPowerData, currentVoltageData);

                        if (efficiencyData.Any())
                        {
                            // 应用平滑处理
                            var smoothedEfficiencyData = IsSmoothingEnabled ?
                                _dataProcessor.SmoothData(efficiencyData, _currentSmoothingConfig) :
                                efficiencyData;

                            var efficiencySeries = new SeriesData
                            {
                                Title = $"{file.FileName} - 效率",
                                YAxisKey = "EfficiencyAxis",
                                Color = GetEfficiencySeriesColor(fileColor), // 使用不同的颜色变体
                                Points = smoothedEfficiencyData,
                                LineStyle = IsLineStyle ? LineStyle.Solid : (IsPointLineStyle ? LineStyle.Solid : LineStyle.None),
                                MarkerType = ConvertToMarkerType(_currentChartSettings.MarkerType),
                                MarkerSize = _currentChartSettings.MarkerSize,
                                StrokeThickness = _currentChartSettings.LineThickness
                            };
                            seriesDataList.Add(efficiencySeries);
                        }
                    }

                    colorIndex++;
                }

                await _plotManager.BatchUpdateSeriesAsync(LIVPlotModel, seriesDataList, _plotUpdateCancellation?.Token ?? CancellationToken.None);
            }
            catch (OperationCanceledException)
            {
                // 操作被取消，正常情况（包括TaskCanceledException）
            }
        }
        
        private async Task UpdateSpectrumPlot(List<FileViewModel> selectedFiles)
        {
            try
            {
                if (SpectrumPlotModel == null) return;

                var seriesDataList = new List<SeriesData>();
                var colorIndex = 0;

                foreach (var file in selectedFiles)
                {
                    // 检查取消令牌
                    if (_plotUpdateCancellation?.Token.IsCancellationRequested == true) return;

                    if (file.Data.WavelengthIntensityData.Any())
                    {
                        // 应用平滑处理
                        var spectrumData = IsSmoothingEnabled ?
                            _dataProcessor.SmoothData(file.Data.WavelengthIntensityData.ToList(), _currentSmoothingConfig) :
                            file.Data.WavelengthIntensityData.ToList();

                        var series = new SeriesData
                        {
                            Title = file.FileName,
                            Color = GetSeriesColor(colorIndex),
                            Points = spectrumData,
                            LineStyle = IsLineStyle ? LineStyle.Solid : (IsPointLineStyle ? LineStyle.Solid : LineStyle.None),
                            MarkerType = ConvertToMarkerType(_currentChartSettings.MarkerType),
                            MarkerSize = _currentChartSettings.MarkerSize,
                            StrokeThickness = _currentChartSettings.LineThickness
                        };
                        seriesDataList.Add(series);
                    }
                    colorIndex++;
                }

                await _plotManager.BatchUpdateSeriesAsync(SpectrumPlotModel, seriesDataList, _plotUpdateCancellation?.Token ?? CancellationToken.None);
            }
            catch (OperationCanceledException)
            {
                // 操作被取消，正常情况（包括TaskCanceledException）
            }
        }
        
        private async Task UpdateEfficiencyPlot(List<FileViewModel> selectedFiles)
        {
            try
            {
                if (EfficiencyPlotModel == null) return;

                var seriesDataList = new List<SeriesData>();
                var colorIndex = 0;

                foreach (var file in selectedFiles)
                {
                    // 检查取消令牌
                    if (_plotUpdateCancellation?.Token.IsCancellationRequested == true) return;

                    if (file.Data.CurrentPowerData.Any() && file.Data.CurrentVoltageData.Any())
                    {
                        var efficiencyData = new List<LIVAnalyzer.Models.DataPoint>();

                        // 计算效率曲线
                        foreach (var powerPoint in file.Data.CurrentPowerData)
                        {
                            var voltagePoint = file.Data.CurrentVoltageData
                                .OrderBy(v => Math.Abs(v.X - powerPoint.X))
                                .FirstOrDefault();

                            if (voltagePoint != null && voltagePoint.Y > 0 && powerPoint.X > 0)
                            {
                                var efficiency = (powerPoint.Y / (powerPoint.X * voltagePoint.Y)) * 100;
                                efficiencyData.Add(new LIVAnalyzer.Models.DataPoint(powerPoint.X, efficiency));
                            }
                        }

                        // 应用平滑处理
                        var smoothedEfficiency = IsSmoothingEnabled ?
                            _dataProcessor.SmoothData(efficiencyData, _currentSmoothingConfig) :
                            efficiencyData;

                        var series = new SeriesData
                        {
                            Title = file.FileName,
                            Color = GetSeriesColor(colorIndex),
                            Points = smoothedEfficiency,
                            LineStyle = IsLineStyle ? LineStyle.Solid : (IsPointLineStyle ? LineStyle.Solid : LineStyle.None),
                            MarkerType = ConvertToMarkerType(_currentChartSettings.MarkerType),
                            MarkerSize = _currentChartSettings.MarkerSize,
                            StrokeThickness = _currentChartSettings.LineThickness
                        };
                        seriesDataList.Add(series);
                    }
                    colorIndex++;
                }

                await _plotManager.BatchUpdateSeriesAsync(EfficiencyPlotModel, seriesDataList, _plotUpdateCancellation?.Token ?? CancellationToken.None);
            }
            catch (OperationCanceledException)
            {
                // 操作被取消，正常情况（包括TaskCanceledException）
            }
        }
        
        private async Task UpdateDivergencePlot(List<FileViewModel> selectedFiles)
        {
            try
            {
                // Clear existing plots
                HffDivergencePlotModel?.Series.Clear();
                VffDivergencePlotModel?.Series.Clear();

                var divergenceResults = new System.Text.StringBuilder();
                var colorIndex = 0;

                foreach (var file in selectedFiles)
                {
                    // 检查取消令牌
                    if (_plotUpdateCancellation?.Token.IsCancellationRequested == true) return;

                    bool hasHffData = file.Data.HorizontalDivergenceData?.Any() == true;
                    bool hasVffData = file.Data.VerticalDivergenceData?.Any() == true;
                
                if (!hasHffData && !hasVffData)
                {
                    continue;
                }
                
                divergenceResults.AppendLine($"文件: {file.FileName}");
                divergenceResults.AppendLine(new string('-', 30));
                
                // Process HFF data
                if (hasHffData && HffDivergencePlotModel != null)
                {
                    // Apply smoothing if enabled
                    var hData = IsSmoothingEnabled ?
                        _dataProcessor.SmoothData(file.Data.HorizontalDivergenceData, _currentSmoothingConfig) :
                        file.Data.HorizontalDivergenceData;

                    var series = new LineSeries
                    {
                        Title = file.FileName,
                        Color = GetSeriesColor(colorIndex),
                        TrackerFormatString = "{0}\n角度 (°): {2:F2}\n强度: {4:F2}"
                    };
                    SetLineStyle(series);

                    foreach (var point in hData)
                    {
                        series.Points.Add(new OxyPlot.DataPoint(point.X, point.Y));
                    }
                    HffDivergencePlotModel.Series.Add(series);
                    
                    // Calculate divergence angles
                    var divergenceProcessor = new LIVAnalyzer.Core.Processors.DivergenceProcessor();
                    // The divergence data has angle in X and intensity in Y
                    var angleData = hData.Select(p => new LIVAnalyzer.Models.DataPoint(p.X, p.X)).ToList();
                    var intensityData = hData.Select(p => new LIVAnalyzer.Models.DataPoint(p.X, p.Y)).ToList();
                    var hffAngles = divergenceProcessor.CalculateDivergence(angleData, intensityData, IsSmoothingEnabled, SmoothingWindowSize);
                    
                    if (hffAngles.IsValid)
                    {
                        divergenceResults.AppendLine("水平发散角 (HFF):");
                        divergenceResults.AppendLine($"  FWHM: {hffAngles.FWHM:F2}°");
                        divergenceResults.AppendLine($"  FW(1/e²): {hffAngles.FW1e2:F2}° (能量占比: {hffAngles.FW1e2PowerContainment:P3})");
                        divergenceResults.AppendLine($"  FW95%: {hffAngles.FW95:F2}°");
                        
                        // 保存UI计算的结果到数据模型
                        if (file.Data.DivergenceResults == null)
                        {
                            file.Data.DivergenceResults = new LIVAnalyzer.Models.DivergenceResults();
                        }
                        file.Data.DivergenceResults.HorizontalFWHM = hffAngles.FWHM;
                        file.Data.DivergenceResults.HorizontalFW1e2 = hffAngles.FW1e2;
                        file.Data.DivergenceResults.HorizontalFW1e2PowerContainment = hffAngles.FW1e2PowerContainment;
                        file.Data.DivergenceResults.HorizontalFW95 = hffAngles.FW95;
                        
                        // Add calculation lines to plot
                        AddDivergenceLines(HffDivergencePlotModel, hData, hffAngles, colorIndex);
                    }
                    else
                    {
                        divergenceResults.AppendLine($"水平发散角 (HFF): 计算失败 - {hffAngles.ErrorMessage}");
                    }
                }
                
                // Process VFF data
                if (hasVffData && VffDivergencePlotModel != null)
                {
                    // Apply smoothing if enabled
                    var vData = IsSmoothingEnabled ?
                        _dataProcessor.SmoothData(file.Data.VerticalDivergenceData, _currentSmoothingConfig) :
                        file.Data.VerticalDivergenceData;

                    var series = new LineSeries
                    {
                        Title = file.FileName,
                        Color = GetSeriesColor(colorIndex),
                        TrackerFormatString = "{0}\n角度 (°): {2:F2}\n强度: {4:F2}"
                    };
                    SetLineStyle(series);

                    foreach (var point in vData)
                    {
                        series.Points.Add(new OxyPlot.DataPoint(point.X, point.Y));
                    }
                    VffDivergencePlotModel.Series.Add(series);
                    
                    // Calculate divergence angles
                    var divergenceProcessor = new LIVAnalyzer.Core.Processors.DivergenceProcessor();
                    // The divergence data has angle in X and intensity in Y
                    var angleData = vData.Select(p => new LIVAnalyzer.Models.DataPoint(p.X, p.X)).ToList();
                    var intensityData = vData.Select(p => new LIVAnalyzer.Models.DataPoint(p.X, p.Y)).ToList();
                    var vffAngles = divergenceProcessor.CalculateDivergence(angleData, intensityData, IsSmoothingEnabled, SmoothingWindowSize);
                    
                    if (vffAngles.IsValid)
                    {
                        divergenceResults.AppendLine("\n垂直发散角 (VFF):");
                        divergenceResults.AppendLine($"  FWHM: {vffAngles.FWHM:F2}°");
                        divergenceResults.AppendLine($"  FW(1/e²): {vffAngles.FW1e2:F2}° (能量占比: {vffAngles.FW1e2PowerContainment:P3})");
                        divergenceResults.AppendLine($"  FW95%: {vffAngles.FW95:F2}°");
                        
                        // 保存UI计算的结果到数据模型
                        if (file.Data.DivergenceResults == null)
                        {
                            file.Data.DivergenceResults = new LIVAnalyzer.Models.DivergenceResults();
                        }
                        file.Data.DivergenceResults.VerticalFWHM = vffAngles.FWHM;
                        file.Data.DivergenceResults.VerticalFW1e2 = vffAngles.FW1e2;
                        file.Data.DivergenceResults.VerticalFW1e2PowerContainment = vffAngles.FW1e2PowerContainment;
                        file.Data.DivergenceResults.VerticalFW95 = vffAngles.FW95;
                        
                        // Add calculation lines to plot
                        AddDivergenceLines(VffDivergencePlotModel, vData, vffAngles, colorIndex);
                    }
                    else
                    {
                        divergenceResults.AppendLine($"\n垂直发散角 (VFF): 计算失败 - {vffAngles.ErrorMessage}");
                    }
                }
                
                divergenceResults.AppendLine();
                colorIndex++;
            }
            
                // Update divergence results text
                DivergenceResultText = divergenceResults.Length > 0 ?
                    divergenceResults.ToString() :
                    "请选择包含发散角数据的文件...";

                // Refresh plots
                HffDivergencePlotModel?.InvalidatePlot(true);
                VffDivergencePlotModel?.InvalidatePlot(true);
            }
            catch (OperationCanceledException)
            {
                // 操作被取消，正常情况（包括TaskCanceledException）
            }
        }
        
        private void AddDivergenceLines(PlotModel plotModel, List<LIVAnalyzer.Models.DataPoint> data, 
            LIVAnalyzer.Core.Processors.DivergenceResult angles, int colorIndex)
        {
            if (plotModel == null || data == null || data.Count == 0) return;
            
            // Find max intensity for scaling
            var maxIntensity = data.Max(p => p.Y);
            var lineColor = GetSeriesColor(colorIndex);
            
            // Add FWHM lines (50% intensity)
            if (!double.IsNaN(angles.FWHM) && angles.FWHM > 0 && angles.FWHMCrossings.Count >= 2)
            {
                var halfMax = maxIntensity * 0.5;
                // Use actual crossing points from the calculation
                var leftAngle = angles.FWHMCrossings.First();
                var rightAngle = angles.FWHMCrossings.Last();
                
                // Horizontal line at 50%
                var hLine = new LineSeries
                {
                    Title = "FWHM",
                    Color = OxyColor.FromAColor(128, lineColor),
                    LineStyle = LineStyle.Dash,
                    StrokeThickness = 2
                };
                hLine.Points.Add(new OxyPlot.DataPoint(leftAngle, halfMax));
                hLine.Points.Add(new OxyPlot.DataPoint(rightAngle, halfMax));
                plotModel.Series.Add(hLine);
                
                // Vertical lines at FWHM boundaries
                var vLine1 = new LineSeries
                {
                    Color = OxyColor.FromAColor(128, lineColor),
                    LineStyle = LineStyle.Dash,
                    StrokeThickness = 1
                };
                vLine1.Points.Add(new OxyPlot.DataPoint(leftAngle, 0));
                vLine1.Points.Add(new OxyPlot.DataPoint(leftAngle, halfMax));
                plotModel.Series.Add(vLine1);
                
                var vLine2 = new LineSeries
                {
                    Color = OxyColor.FromAColor(128, lineColor),
                    LineStyle = LineStyle.Dash,
                    StrokeThickness = 1
                };
                vLine2.Points.Add(new OxyPlot.DataPoint(rightAngle, 0));
                vLine2.Points.Add(new OxyPlot.DataPoint(rightAngle, halfMax));
                plotModel.Series.Add(vLine2);
            }
            
            // Add FW(1/e²) lines
            if (!double.IsNaN(angles.FW1e2) && angles.FW1e2 > 0 && angles.FW1e2Crossings.Count >= 2)
            {
                var threshold1e2 = maxIntensity * angles.FW1e2Threshold;
                var leftAngle1e2 = angles.FW1e2Crossings.First();
                var rightAngle1e2 = angles.FW1e2Crossings.Last();
                
                // Horizontal line at 13.5% intensity threshold (1/e²)
                var hLine1e2 = new LineSeries
                {
                    Title = "FW(1/e²)",
                    Color = OxyColor.FromAColor(96, lineColor),
                    LineStyle = LineStyle.Dot,
                    StrokeThickness = 2
                };
                hLine1e2.Points.Add(new OxyPlot.DataPoint(leftAngle1e2, threshold1e2));
                hLine1e2.Points.Add(new OxyPlot.DataPoint(rightAngle1e2, threshold1e2));
                plotModel.Series.Add(hLine1e2);
                
                // Vertical lines at FW(1/e²) boundaries
                var vLine1e2_1 = new LineSeries
                {
                    Color = OxyColor.FromAColor(96, lineColor),
                    LineStyle = LineStyle.Dot,
                    StrokeThickness = 1
                };
                vLine1e2_1.Points.Add(new OxyPlot.DataPoint(leftAngle1e2, 0));
                vLine1e2_1.Points.Add(new OxyPlot.DataPoint(leftAngle1e2, threshold1e2));
                plotModel.Series.Add(vLine1e2_1);
                
                var vLine1e2_2 = new LineSeries
                {
                    Color = OxyColor.FromAColor(96, lineColor),
                    LineStyle = LineStyle.Dot,
                    StrokeThickness = 1
                };
                vLine1e2_2.Points.Add(new OxyPlot.DataPoint(rightAngle1e2, 0));
                vLine1e2_2.Points.Add(new OxyPlot.DataPoint(rightAngle1e2, threshold1e2));
                plotModel.Series.Add(vLine1e2_2);
            }
            
            // Add FW95% lines
            if (!double.IsNaN(angles.FW95) && angles.FW95 > 0 && angles.FW95Crossings.Count >= 2)
            {
                var threshold95 = maxIntensity * angles.FW95Threshold;
                var leftAngle95 = angles.FW95Crossings.First();
                var rightAngle95 = angles.FW95Crossings.Last();
                
                // Horizontal line at 95% threshold
                var hLine95 = new LineSeries
                {
                    Title = "FW95%",
                    Color = OxyColor.FromAColor(64, lineColor),
                    LineStyle = LineStyle.DashDot,
                    StrokeThickness = 2
                };
                hLine95.Points.Add(new OxyPlot.DataPoint(leftAngle95, threshold95));
                hLine95.Points.Add(new OxyPlot.DataPoint(rightAngle95, threshold95));
                plotModel.Series.Add(hLine95);
                
                // Vertical lines at FW95% boundaries
                var vLine95_1 = new LineSeries
                {
                    Color = OxyColor.FromAColor(64, lineColor),
                    LineStyle = LineStyle.DashDot,
                    StrokeThickness = 1
                };
                vLine95_1.Points.Add(new OxyPlot.DataPoint(leftAngle95, 0));
                vLine95_1.Points.Add(new OxyPlot.DataPoint(leftAngle95, threshold95));
                plotModel.Series.Add(vLine95_1);
                
                var vLine95_2 = new LineSeries
                {
                    Color = OxyColor.FromAColor(64, lineColor),
                    LineStyle = LineStyle.DashDot,
                    StrokeThickness = 1
                };
                vLine95_2.Points.Add(new OxyPlot.DataPoint(rightAngle95, 0));
                vLine95_2.Points.Add(new OxyPlot.DataPoint(rightAngle95, threshold95));
                plotModel.Series.Add(vLine95_2);
            }
        }
        
        private void ClearPlots()
        {
            LIVPlotModel?.Series.Clear();
            SpectrumPlotModel?.Series.Clear();
            EfficiencyPlotModel?.Series.Clear();
            DivergencePlotModel?.Series.Clear();
            HffDivergencePlotModel?.Series.Clear();
            VffDivergencePlotModel?.Series.Clear();
            
            LIVPlotModel?.InvalidatePlot(true);
            SpectrumPlotModel?.InvalidatePlot(true);
            EfficiencyPlotModel?.InvalidatePlot(true);
            DivergencePlotModel?.InvalidatePlot(true);
            HffDivergencePlotModel?.InvalidatePlot(true);
            VffDivergencePlotModel?.InvalidatePlot(true);
        }
        
        private void UpdateParameters()
        {
            var selectedFiles = LoadedFiles.Where(f => f.IsSelected).ToList();
            
            // Use the last selected file if it's still selected, otherwise use the first selected file
            FileViewModel? fileToDisplay = null;
            if (_lastSelectedFile != null && _lastSelectedFile.IsSelected && selectedFiles.Contains(_lastSelectedFile))
            {
                fileToDisplay = _lastSelectedFile;
            }
            else if (selectedFiles.Any())
            {
                fileToDisplay = selectedFiles.First();
                _lastSelectedFile = fileToDisplay;
            }
            
            if (fileToDisplay != null)
            {
                // 调试信息
                System.Diagnostics.Debug.WriteLine($"UpdateParameters: 文件={fileToDisplay.FileName}, 参数是否为空={fileToDisplay.Data.Parameters == null}, 是否已处理={fileToDisplay.Data.IsProcessed}");

                if (fileToDisplay.Data.Parameters != null)
                {
                    var params_ = fileToDisplay.Data.Parameters;
                    ParametersText = $@"曲线: {fileToDisplay.FileName}
峰值波长: {params_.PeakWavelength:F2} nm
半高宽: {params_.FWHM:F2} nm
阈值电流: {params_.ThresholdCurrent:F2} A
最大功率: {params_.MaxPower:F2} W
最大效率: {params_.MaxEfficiency:F2}%
斜率效率: {params_.SlopeEfficiency:F2} W/A";

                    if (params_.SeriesResistance.HasValue)
                    {
                        ParametersText += $"\n串联电阻: {params_.SeriesResistance.Value:F3} Ω (R²={params_.SeriesResistanceR2:F3})";
                    }
                }
                else
                {
                    // 如果参数为空，尝试立即计算
                    try
                    {
                        var processor = new LIVAnalyzer.Core.Processors.LIVDataProcessor();
                        var parameters = processor.CalculateParameters(fileToDisplay.Data);
                        fileToDisplay.Data.Parameters = parameters;
                        fileToDisplay.Data.IsProcessed = true;

                        // 递归调用自己来显示刚计算的参数
                        UpdateParameters();
                        return;
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"参数计算失败: {ex.Message}");
                        ParametersText = "参数计算失败，请检查数据...";
                    }
                }
            }
            else
            {
                ParametersText = "请选择要分析的曲线...";
            }
        }
        

        
        private bool HasValidData(LIVMeasurementData data)
        {
            // 检查是否有基本的LIV数据
            var hasCurrentPower = data.CurrentPowerData.Any();
            var hasCurrentVoltage = data.CurrentVoltageData.Any();
            var hasWavelengthIntensity = data.WavelengthIntensityData.Any();
            
            return hasCurrentPower || hasCurrentVoltage || hasWavelengthIntensity;
        }
        
        /// <summary>
        /// 根据当前平滑设置计算参数
        /// </summary>
        private LIVParameters CalculateParametersWithSmoothing(LIVMeasurementData data)
        {
            // 如果启用了平滑，创建平滑后的数据副本
            var processedData = new LIVMeasurementData
            {
                FileName = data.FileName,
                CurrentPowerData = data.CurrentPowerData,
                CurrentVoltageData = data.CurrentVoltageData,
                WavelengthIntensityData = data.WavelengthIntensityData,
                HorizontalDivergenceData = data.HorizontalDivergenceData,
                VerticalDivergenceData = data.VerticalDivergenceData
            };
            
            if (IsSmoothingEnabled)
            {
                // 应用平滑到电流-功率数据
                if (processedData.CurrentPowerData.Any())
                {
                    var smoothedPowerData = _dataProcessor.SmoothData(processedData.CurrentPowerData.ToList(), _currentSmoothingConfig);
                    processedData.CurrentPowerData = smoothedPowerData;
                }
                
                // 应用平滑到电流-电压数据
                if (processedData.CurrentVoltageData.Any())
                {
                    var smoothedVoltageData = _dataProcessor.SmoothData(processedData.CurrentVoltageData.ToList(), _currentSmoothingConfig);
                    processedData.CurrentVoltageData = smoothedVoltageData;
                }
                
                // 应用平滑到波长-强度数据
                if (processedData.WavelengthIntensityData.Any())
                {
                    var smoothedSpectrumData = _dataProcessor.SmoothData(processedData.WavelengthIntensityData.ToList(), _currentSmoothingConfig);
                    processedData.WavelengthIntensityData = smoothedSpectrumData;
                }
                
                // 应用平滑到发散角数据
                if (processedData.HorizontalDivergenceData != null && processedData.HorizontalDivergenceData.Any())
                {
                    var smoothedHData = _dataProcessor.SmoothData(processedData.HorizontalDivergenceData.ToList(), _currentSmoothingConfig);
                    processedData.HorizontalDivergenceData = smoothedHData;
                }
                
                if (processedData.VerticalDivergenceData != null && processedData.VerticalDivergenceData.Any())
                {
                    var smoothedVData = _dataProcessor.SmoothData(processedData.VerticalDivergenceData.ToList(), _currentSmoothingConfig);
                    processedData.VerticalDivergenceData = smoothedVData;
                }
            }
            
            // 使用处理后的数据计算参数
            return _dataProcessor.CalculateParameters(processedData);
        }
        
        /// <summary>
        /// 重新计算所有已加载文件的参数
        /// </summary>
        private void RecalculateAllParametersWithSmoothing()
        {
            foreach (var fileViewModel in LoadedFiles)
            {
                if (fileViewModel.Data != null && fileViewModel.Data.IsProcessed)
                {
                    // 重新计算参数
                    _dataProcessor.ClearCache();
                    var newParameters = CalculateParametersWithSmoothing(fileViewModel.Data);
                    fileViewModel.Data.Parameters = newParameters;
                    
                    // 重新计算发散角参数（如果有发散角数据）
                    if ((fileViewModel.Data.HorizontalDivergenceData != null && fileViewModel.Data.HorizontalDivergenceData.Any()) ||
                        (fileViewModel.Data.VerticalDivergenceData != null && fileViewModel.Data.VerticalDivergenceData.Any()))
                    {
                        var divergenceResults = _dataProcessor.CalculateDivergenceParameters(fileViewModel.Data);
                        fileViewModel.Data.DivergenceResults = divergenceResults;
                    }
                }
            }
            
            // 更新参数显示
            UpdateParameters();
        }
        
        public void ApplyChartSettings(ChartSettings settings)
        {
            // 保存设置
            _currentChartSettings = settings;
            
            // 应用设置到所有图表
            ApplySettingsToAllPlots();
            
            // 更新所有图表
            UpdatePlots();
        }

        /// <summary>
        /// 显示单个坐标轴的设置对话框
        /// </summary>
        public void ShowAxisSettings(OxyPlot.Axes.Axis axis, string chartName)
        {
            try
            {
                // 检查轴是否为null
                if (axis == null)
                {
                    System.Windows.MessageBox.Show("错误：未能检测到有效的坐标轴", "错误", 
                        System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Warning);
                    return;
                }

                // 添加调试信息
                System.Diagnostics.Debug.WriteLine($"ShowAxisSettings called: axis={axis?.GetType().Name}, chartName={chartName}");
                
                var dialog = new LIVAnalyzer.UI.Views.AxisSettingsDialog(axis, chartName);
                dialog.Owner = System.Windows.Application.Current.MainWindow;
                
                System.Diagnostics.Debug.WriteLine("About to show dialog...");
                var result = dialog.ShowDialog();
                System.Diagnostics.Debug.WriteLine($"Dialog result: {result}");
            }
            catch (Exception ex)
            {
                LIVAnalyzer.Services.Logging.LoggingService.LogError(ex, "Failed to show axis settings dialog");
                
                // 显示错误消息给用户
                System.Windows.MessageBox.Show(
                    $"无法打开轴设置对话框：\n{ex.Message}\n\n详细信息：{ex.StackTrace}",
                    "错误",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Error);
            }
        }

        // 公共方法：获取当前图表设置
        public ChartSettings GetCurrentChartSettings()
        {
            return new ChartSettings
            {
                LineThickness = _currentChartSettings.LineThickness,
                MarkerSize = _currentChartSettings.MarkerSize,
                MarkerType = _currentChartSettings.MarkerType,
                MajorGridOpacity = _currentChartSettings.MajorGridOpacity,
                MinorGridOpacity = _currentChartSettings.MinorGridOpacity,
                MajorGridLineStyle = _currentChartSettings.MajorGridLineStyle,
                MinorGridLineStyle = _currentChartSettings.MinorGridLineStyle,
                MajorGridColor = _currentChartSettings.MajorGridColor,
                MinorGridColor = _currentChartSettings.MinorGridColor,
                ColorScheme = _currentChartSettings.ColorScheme,
                LegendPosition = _currentChartSettings.LegendPosition,
                LegendFontSize = _currentChartSettings.LegendFontSize,
                AxisTitleFontSize = _currentChartSettings.AxisTitleFontSize,
                AxisLabelFontSize = _currentChartSettings.AxisLabelFontSize,
                // 数据标签设置
                ShowDataLabels = _currentChartSettings.ShowDataLabels,
                DataLabelPosition = _currentChartSettings.DataLabelPosition,
                DataLabelFontSize = _currentChartSettings.DataLabelFontSize,
                DataLabelFormat = _currentChartSettings.DataLabelFormat,
                // 坐标轴范围设置
                EnableCustomXAxisRange = _currentChartSettings.EnableCustomXAxisRange,
                XAxisMinimum = _currentChartSettings.XAxisMinimum,
                XAxisMaximum = _currentChartSettings.XAxisMaximum,
                EnableCustomYAxisRange = _currentChartSettings.EnableCustomYAxisRange,
                YAxisMinimum = _currentChartSettings.YAxisMinimum,
                YAxisMaximum = _currentChartSettings.YAxisMaximum,
                EnableCustomSecondaryYAxisRange = _currentChartSettings.EnableCustomSecondaryYAxisRange,
                SecondaryYAxisMinimum = _currentChartSettings.SecondaryYAxisMinimum,
                SecondaryYAxisMaximum = _currentChartSettings.SecondaryYAxisMaximum
            };
        }

        // 应用设置到所有图表
        private void ApplySettingsToAllPlots()
        {
            var plots = new[] { LIVPlotModel, SpectrumPlotModel, EfficiencyPlotModel, 
                               DivergencePlotModel, HffDivergencePlotModel, VffDivergencePlotModel };
            
            foreach (var plot in plots)
            {
                ApplySettingsToPlot(plot);
            }
        }
        
        private void ApplySettingsToPlot(PlotModel? plot)
        {
            if (plot == null) return;
            
            // 应用网格设置
            foreach (var axis in plot.Axes)
            {
                // 保存现有的LabelFormatter，避免被覆盖
                var existingFormatter = axis.LabelFormatter;
                
                // 先检查是否显示网格
                if (ShowGrid)
                {
                    // 网格透明度
                    var majorAlpha = (byte)(_currentChartSettings.MajorGridOpacity * 255 / 100);
                    var minorAlpha = (byte)(_currentChartSettings.MinorGridOpacity * 255 / 100);
                    
                    // 获取网格线颜色
                    var majorColor = ConvertToOxyColor(_currentChartSettings.MajorGridColor);
                    var minorColor = ConvertToOxyColor(_currentChartSettings.MinorGridColor);
                    
                    axis.MajorGridlineColor = OxyColor.FromAColor(majorAlpha, majorColor);
                    axis.MinorGridlineColor = OxyColor.FromAColor(minorAlpha, minorColor);
                    
                    // 网格样式
                    axis.MajorGridlineStyle = ConvertToLineStyle(_currentChartSettings.MajorGridLineStyle);
                    axis.MinorGridlineStyle = ConvertToLineStyle(_currentChartSettings.MinorGridLineStyle);
                }
                else
                {
                    // 如果不显示网格，设置为None
                    axis.MajorGridlineStyle = LineStyle.None;
                    axis.MinorGridlineStyle = LineStyle.None;
                }
                
                // 应用自定义坐标轴范围
                ApplyCustomAxisRange(axis);
                
                // 字体大小
                axis.TitleFontSize = _currentChartSettings.AxisTitleFontSize;
                axis.FontSize = _currentChartSettings.AxisLabelFontSize;
                
                // 恢复LabelFormatter，确保数值格式不被重置
                if (existingFormatter != null)
                {
                    axis.LabelFormatter = existingFormatter;
                }

                // 控制效率轴的可见性
                if (axis.Key == "EfficiencyAxis")
                {
                    axis.IsAxisVisible = _currentChartSettings.ShowEfficiencyOverlay;
                }
            }
            
            // 应用图例设置
            if (plot.Legends.Any())
            {
                var legend = plot.Legends.First();
                legend.LegendFontSize = _currentChartSettings.LegendFontSize;
                legend.LegendPosition = ConvertToLegendPosition(_currentChartSettings.LegendPosition);
                legend.IsLegendVisible = ShowLegend;
            }
            
            plot.InvalidatePlot(true);
        }
        
        /// <summary>
        /// 应用自定义坐标轴范围设置
        /// </summary>
        private void ApplyCustomAxisRange(Axis axis)
        {
            if (axis == null) return;
            
            // 根据轴的位置和键确定应用哪个范围设置
            if (axis.Position == AxisPosition.Bottom || axis.Position == AxisPosition.Top)
            {
                // X轴
                if (_currentChartSettings.EnableCustomXAxisRange)
                {
                    axis.Minimum = _currentChartSettings.XAxisMinimum;
                    axis.Maximum = _currentChartSettings.XAxisMaximum;
                    axis.IsZoomEnabled = false; // 禁用缩放以保持自定义范围
                    axis.IsPanEnabled = false;  // 禁用平移以保持自定义范围
                }
                else
                {
                    // 恢复自动范围
                    axis.Minimum = double.NaN;
                    axis.Maximum = double.NaN;
                    axis.IsZoomEnabled = true;
                    axis.IsPanEnabled = true;
                }
            }
            else if (axis.Position == AxisPosition.Left ||
                    (axis.Position == AxisPosition.Right && string.IsNullOrEmpty(axis.Key)))
            {
                // 主Y轴（左侧或右侧但没有特定键）
                if (_currentChartSettings.EnableCustomYAxisRange)
                {
                    axis.Minimum = _currentChartSettings.YAxisMinimum;
                    axis.Maximum = _currentChartSettings.YAxisMaximum;
                    axis.IsZoomEnabled = false;
                    axis.IsPanEnabled = false;
                }
                else
                {
                    axis.Minimum = double.NaN;
                    axis.Maximum = double.NaN;
                    axis.IsZoomEnabled = true;
                    axis.IsPanEnabled = true;
                }
            }
            else if (axis.Position == AxisPosition.Right && !string.IsNullOrEmpty(axis.Key))
            {
                // 副Y轴（右侧且有特定键，通常用于双坐标轴）
                if (_currentChartSettings.EnableCustomSecondaryYAxisRange)
                {
                    axis.Minimum = _currentChartSettings.SecondaryYAxisMinimum;
                    axis.Maximum = _currentChartSettings.SecondaryYAxisMaximum;
                    axis.IsZoomEnabled = false;
                    axis.IsPanEnabled = false;
                }
                else
                {
                    axis.Minimum = double.NaN;
                    axis.Maximum = double.NaN;
                    axis.IsZoomEnabled = true;
                    axis.IsPanEnabled = true;
                }
            }
        }
        
        private LineStyle ConvertToLineStyle(string style)
        {
            return style switch
            {
                "实线" => LineStyle.Solid,
                "虚线" => LineStyle.Dash,
                "点线" => LineStyle.Dot,
                "点划线" => LineStyle.DashDot,
                "无" => LineStyle.None,
                _ => LineStyle.Solid
            };
        }
        
        private OxyPlot.Legends.LegendPosition ConvertToLegendPosition(string position)
        {
            return position switch
            {
                "左上" => OxyPlot.Legends.LegendPosition.TopLeft,
                "右上" => OxyPlot.Legends.LegendPosition.TopRight,
                "左下" => OxyPlot.Legends.LegendPosition.BottomLeft,
                "右下" => OxyPlot.Legends.LegendPosition.BottomRight,
                "顶部居中" => OxyPlot.Legends.LegendPosition.TopCenter,
                "底部居中" => OxyPlot.Legends.LegendPosition.BottomCenter,
                "左边" => OxyPlot.Legends.LegendPosition.LeftMiddle,
                "右边" => OxyPlot.Legends.LegendPosition.RightMiddle,
                _ => OxyPlot.Legends.LegendPosition.TopRight
            };
        }
        
        private void LoadChartSettingsFromConfig()
        {
            try
            {
                var config = _config.GetConfig();
                var chartConfig = config.Display.ChartSettings;
                
                if (chartConfig != null)
                {
                    _currentChartSettings = new ChartSettings
                    {
                        LineThickness = chartConfig.LineThickness,
                        MarkerSize = chartConfig.MarkerSize,
                        MarkerType = chartConfig.MarkerType,
                        MajorGridOpacity = chartConfig.MajorGridOpacity,
                        MinorGridOpacity = chartConfig.MinorGridOpacity,
                        MajorGridLineStyle = chartConfig.MajorGridLineStyle,
                        MinorGridLineStyle = chartConfig.MinorGridLineStyle,
                        MajorGridColor = chartConfig.MajorGridColor,
                        MinorGridColor = chartConfig.MinorGridColor,
                        ColorScheme = chartConfig.ColorScheme,
                        LegendPosition = chartConfig.LegendPosition,
                        LegendFontSize = chartConfig.LegendFontSize,
                        AxisTitleFontSize = chartConfig.AxisTitleFontSize,
                        AxisLabelFontSize = chartConfig.AxisLabelFontSize
                    };
                    
                    // Apply loaded settings to all plots
                    ApplySettingsToAllPlots();
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "Failed to load chart settings from config");
                // Continue with default settings
            }
        }
        
        private OxyColor ConvertToOxyColor(string colorName)
        {
            var isDark = NativeFluentThemeService.Instance.IsCurrentlyDarkMode();
            
            return colorName switch
            {
                "灰色" => isDark ? OxyColor.FromRgb(169, 169, 169) : OxyColor.FromRgb(128, 128, 128),
                "黑色" => isDark ? OxyColor.FromRgb(255, 255, 255) : OxyColor.FromRgb(0, 0, 0), // 深色主题下"黑色"变白色
                "蓝色" => isDark ? OxyColor.FromRgb(100, 149, 237) : OxyColor.FromRgb(0, 0, 255),
                "红色" => isDark ? OxyColor.FromRgb(220, 20, 60) : OxyColor.FromRgb(255, 0, 0),
                "绿色" => isDark ? OxyColor.FromRgb(50, 205, 50) : OxyColor.FromRgb(0, 128, 0),
                "橙色" => isDark ? OxyColor.FromRgb(255, 165, 0) : OxyColor.FromRgb(255, 140, 0),
                "紫色" => isDark ? OxyColor.FromRgb(138, 43, 226) : OxyColor.FromRgb(128, 0, 128),
                "棕色" => isDark ? OxyColor.FromRgb(210, 180, 140) : OxyColor.FromRgb(165, 42, 42),
                _ => isDark ? OxyColor.FromRgb(169, 169, 169) : OxyColor.FromRgb(128, 128, 128) // 默认灰色
            };
        }
        
        #region Theme Properties and Commands
        
        // 主题相关属性
        public bool IsSystemTheme
        {
            get => NativeFluentThemeService.Instance.GetCurrentTheme() == "System";
        }
        
        public bool IsLightTheme
        {
            get => NativeFluentThemeService.Instance.GetCurrentTheme() == "Light";
        }
        
        public bool IsDarkTheme
        {
            get => NativeFluentThemeService.Instance.GetCurrentTheme() == "Dark";
        }
        
        // 主题相关命令
        public IRelayCommand<string>? SetThemeModeCommand { get; private set; }
        public IRelayCommand<string>? SetAccentColorCommand { get; private set; }
        
        #endregion
    }
}