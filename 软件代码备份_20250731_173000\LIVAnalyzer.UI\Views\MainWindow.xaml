<Window x:Class="LIVAnalyzer.UI.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:oxy="http://oxyplot.org/wpf"
        mc:Ignorable="d"
        Title="LIV曲线分析工具 (C#版本)"
        Height="720" Width="1280"
        MinHeight="680" MinWidth="1024"
        WindowStartupLocation="CenterScreen"
        Icon="pack://application:,,,/Resources/Icons/app-icon-32.png"
>

    <Window.Resources>
        <BooleanToVisibilityConverter x:Key="BoolToVisConverter"/>
        <converters:AlgorithmTypeToVisibilityConverter x:Key="AlgorithmTypeToVisibilityConverter"
                                                      xmlns:converters="clr-namespace:LIVAnalyzer.UI.Converters"/>
        <converters:AlgorithmTypeToViewModelConverter x:Key="AlgorithmTypeToViewModelConverter"
                                                     xmlns:converters="clr-namespace:LIVAnalyzer.UI.Converters"/>
    </Window.Resources>


    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- 菜单栏和Logo -->
        <Grid Grid.Row="0" Background="{DynamicResource AppControlBackgroundBrush}">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- 菜单栏 -->
            <Menu Grid.Column="0"
                  Foreground="{DynamicResource AppForegroundBrush}"
                  Background="{DynamicResource AppControlBackgroundBrush}">
                <MenuItem Header="文件">
                    <MenuItem Header="打开" InputGestureText="Ctrl+O" Command="{Binding LoadFileAsyncCommand}"/>
                    <Separator/>
                    <MenuItem Header="COS文件转换工具" Command="{Binding ShowCOSConverterCommand}"/>
                    <Separator/>
                    <MenuItem Header="导出数据" InputGestureText="Ctrl+E" Command="{Binding ExportDataAsyncCommand}"/>
                    <MenuItem Header="保存图表" InputGestureText="Ctrl+S" Command="{Binding SavePlotCommand}"/>
                </MenuItem>
                <MenuItem Header="视图">
                    <MenuItem Header="图表设置" Command="{Binding ShowChartSettingsCommand}" InputGestureText="Ctrl+G"/>
                    <Separator/>
                    <MenuItem Header="浅色主题" Click="SetLightTheme_Click"/>
                    <MenuItem Header="深色主题" Click="SetDarkTheme_Click"/>
                    <MenuItem Header="跟随系统" Click="SetSystemTheme_Click"/>
                </MenuItem>
                <MenuItem Header="帮助">
                    <MenuItem Header="使用指南" InputGestureText="F1" Command="{Binding ShowGuideCommand}"/>
                    <MenuItem Header="技术文档" InputGestureText="F2" Command="{Binding ShowTechDocCommand}"/>
                    <MenuItem Header="发布说明" Command="{Binding ShowReleaseNotesCommand}"/>
                    <MenuItem Header="关于" Command="{Binding ShowAboutCommand}"/>
                </MenuItem>
            </Menu>

            <!-- Logo和主题切换 -->
            <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Right" VerticalAlignment="Center" Margin="0,0,10,0">
                <!-- 主题切换按钮 -->
                <Button x:Name="ThemeToggleButton"
                        Content="🌙"
                        Width="40" Height="28"
                        ToolTip="点击切换深色/浅色主题"
                        Click="ThemeToggleButton_Click"
                        Margin="0,0,10,0"
                        FontSize="16"
                        VerticalContentAlignment="Center"
                        HorizontalContentAlignment="Center"
                        Background="{DynamicResource AppControlBackgroundBrush}"
                        Foreground="{DynamicResource AppForegroundBrush}"
                        BorderBrush="{DynamicResource AppBorderBrush}"
                        BorderThickness="1"
                        Cursor="Hand">
                </Button>

                <!-- Logo - 适合菜单栏高度 -->
                <Viewbox Width="120" Height="30" Margin="0,0,0,0">
                    <Canvas Width="171" Height="50">
                        <!-- 主要的蓝色logo路径 -->
                        <Path Fill="#0A579F" Data="M24.9,0.1C11.1,0.1,0,11.3,0,25c0,13.7,11.1,24.9,24.9,24.9c13.7,0,24.9-11.1,24.9-24.9 C49.8,11.3,38.6,0.1,24.9,0.1z M19.7,37.7h-9.2L24.9,7.1l4.6,9.7L19.7,37.7z M30,37.7l-4.3-9l4.6-9.8l8.9,18.8H30z"/>
                        <Path Fill="#0A579F" Data="M111.5,25.5h-4.9l0-1c0-0.1-0.1-0.1-0.1-0.1H105c-0.1,0-0.1,0.1-0.1,0.1l0,1h-4.9c-0.1,0-0.1,0.1-0.1,0.1 l0,1.6c0,0.1,0.1,0.1,0.1,0.1h4.9l0,0.9h-4.9c-0.1,0-0.1,0.1-0.1,0.1l0,1.6c0,0.1,0.1,0.1,0.1,0.1h4.9l0,0.9h-4.9 c-0.1,0-0.1,0.1-0.1,0.1l0,1.6c0,0.1,0.1,0.1,0.1,0.1h4.9l0,1c0,0.1,0.1,0.1,0.1,0.1h1.5c0.1,0,0.1-0.1,0.1-0.1l0-1h4.9 c0.1,0,0.1-0.1,0.1-0.1l0-1.6c0-0.1-0.1-0.1-0.1-0.1h-4.9l0-0.9h4.9c0.1,0,0.1-0.1,0.1-0.1l0-1.6c0-0.1-0.1-0.1-0.1-0.1h-4.9l0-0.9 h4.9c0.1,0,0.1-0.1,0.1-0.1l0-1.6C111.7,25.6,111.6,25.5,111.5,25.5z"/>
                        <Path Fill="#0A579F" Data="M98.4,17.8h-1.9c-0.1,0-0.1,0.1-0.1,0.1l-0.1,14.2h-1.2l0.1-16.5c0-0.1-0.1-0.1-0.1-0.1h-2.2 c-0.1,0-0.1,0.1-0.1,0.1l-0.1,16.5h-1.2L91.5,18c0-0.1-0.1-0.1-0.1-0.1h-2.2c-0.1,0-0.1,0.1-0.1,0.1L89,32.2l0,1.6v0.1h0.1h2.2H93 h1.9h1.9h0.2c1.3,0,1.4-0.8,1.4-1.1l0-0.7L98.6,18C98.6,17.9,98.5,17.8,98.4,17.8z"/>
                        <Path Fill="#0A579F" Data="M111.6,25l0-2.1l0-0.1l0-0.2l-3.2-1.4l-0.1,0l3.2-1.4l0-1.4l0-0.5l0-0.4c0-0.4-0.3-0.6-0.5-0.8 c-0.3-0.2-0.5-0.2-0.5-0.2h-7.4l0.4-0.9h-2.7l-0.8,2l0,0.5l0,0.3l0,1.4l2.9,1.4l-3,1.4l0,2.4l6-2.6L111.6,25z M101.7,18.4h7.1h0.7 l-4,1.7L101.7,18.4z"/>
                        <Path Fill="#0A579F" Data="M63.1,22.4h2.2c0.1,0,0.3-0.1,0.3-0.3l-1-5.1c0-0.1-0.1-0.3-0.3-0.3H62c-0.1,0-0.3,0.1-0.3,0.3l1,5.1 C62.8,22.3,62.9,22.4,63.1,22.4z"/>
                        <Path Fill="#0A579F" Data="M76.2,22.4h2.4c0.1,0,0.3-0.1,0.3-0.3l0.8-5c0-0.1-0.1-0.3-0.3-0.3h-2.4c-0.1,0-0.3,0.1-0.3,0.3l-0.8,5 C75.9,22.2,76,22.4,76.2,22.4z"/>
                        <Path Fill="#0A579F" Data="M80.5,23.5h-5.4l0,0H72l0-7.7c0-0.1-0.1-0.3-0.3-0.3h-2.4c-0.1,0-0.3,0.1-0.3,0.3l0,7.7H68v0h-7 c-0.2,0-0.3,0.1-0.3,0.3l0,2c0,0.2,0.1,0.3,0.3,0.3h4.8L61.3,34c0,0-0.1,0.2,0.1,0.2h3.8l4.2-8.1h2.2v0l0,4.3l0,1.3l0,0.1l0,0.6 c0,0.4,0.2,1.8,2.2,1.8h6.5c0,0,0.1,0,0.1,0c0.1,0,0.2-0.1,0.2-0.2l0-2c0-0.1,0-0.1,0-0.1l0,0l0,0c0,0-0.1,0-0.1,0l0,0l0,0v0 c0,0-0.1,0-0.1,0h0h0h0h-0.2h-1.6l-2.7,0c-0.9,0-1.2-0.4-1.4-0.7c0-0.2-0.1-0.4-0.1-0.7v-4.3h5.8h0.2h0c0,0,0.1,0,0.1,0 c0.1,0,0.1-0.1,0.1-0.2l0-2.2C80.7,23.5,80.6,23.5,80.5,23.5z"/>
                        <Path Fill="#0A579F" Data="M128.8,23h-2c-0.1,0-0.1,0.1-0.1,0.1l0.5,10.9c0,0.1,0.1,0.1,0.1,0.1l2,0c0.1,0,0.1,0,0.1-0.1l-0.5-10.9 C128.9,23,128.8,23,128.8,23z"/>
                        <Path Fill="#0A579F" Data="M122.5,23h-2c-0.1,0-0.1,0.1-0.1,0.1L119.9,34c0,0.1,0.1,0.1,0.1,0.1l2,0c0.1,0,0.1-0.1,0.1-0.1l0.5-10.9 C122.6,23,122.6,23,122.5,23z"/>
                        <Path Fill="#0A579F" Data="M129.2,22.2c0.1,0,0.1-0.1,0.1-0.1v-2c0-0.1-0.1-0.1-0.1-0.1h-3.1l0-1.9l3.1-0.1c0.1,0,0.1-0.1,0.1-0.1v-2.2 c0-0.1-0.1-0.1-0.1-0.1l-9,0.3c-0.1,0-0.1,0.1-0.1,0.1l0,2.2c0,0.1,0.1,0.1,0.1,0.1l2.9-0.1l0,1.8H120c-0.1,0-0.1,0-0.1,0.1l0,2 c0,0.1,0.1,0.1,0.1,0.1h3.2l0,11.7c0,0.1,0.1,0.3,0.3,0.3h2.5c0.1,0,0.3-0.1,0.3-0.3l0-11.7H129.2z"/>
                        <Path Fill="#0A579F" Data="M143.1,27.2H141l0-11.5c0-0.1-0.1-0.3-0.3-0.3h-3.1c-0.1,0-0.3,0.1-0.3,0.3l0,11.5h-7.2c-0.1,0-0.1,0-0.1,0.1 l0,2c0,0.1,0.1,0.1,0.1,0.1h7.2l0,4.4c0,0.1,0.1,0.3,0.3,0.3h3.1c0.1,0,0.3-0.1,0.3-0.3l0-4.4h2.1c0.1,0,0.1-0.1,0.1-0.1v-2 C143.2,27.3,143.1,27.2,143.1,27.2z"/>
                        <Path Fill="#0A579F" Data="M132.9,26.1h2.3c0.2,0,0.3-0.1,0.3-0.3l-0.9-4.4h1.3c0.2,0,0.3-0.1,0.3-0.3l-1.1-5.3c0-0.1-0.1-0.3-0.3-0.3 h-2.3c-0.1,0-0.3,0.1-0.3,0.3l0.9,4.4h-1.3c-0.1,0-0.3,0.1-0.3,0.3l1.1,5.3C132.7,26,132.8,26.1,132.9,26.1z"/>
                        <Path Fill="#0A579F" Data="M154.8,29.8l0-3.4l3.4-4.3c0.1-0.1,0-0.2-0.1-0.2h-3.3l0-2.2h2.8c0.1,0,0.1,0,0.1-0.1v-2.3 c0-0.1,0-0.1-0.1-0.1h-2.8l0-1.3c0-0.1-0.1-0.3-0.3-0.3h-2.8c-0.1,0-0.3,0.1-0.3,0.3l0,1.3h-2.7c-0.1,0-0.1,0-0.1,0.1l0,2.3 c0,0.1,0,0.1,0.1,0.1h2.7l0,5.8l-3.2,4c-0.1,0.1,0,0.2,0.1,0.2h3.1l0,0.4c0,0,0,0,0,0v1c0,0.4-0.2,0.6-0.3,0.7 c-0.2,0.2-0.5,0.2-0.5,0.2h-0.4l-1,2.2h2.7c0.5,0,1.3-0.2,1.9-0.7c1-0.9,1-2,1-2V29.8z"/>
                        <Path Fill="#0A579F" Data="M171,31.8c0,0-1.1-0.1-4.2-2c-0.1-0.1-0.2-0.1-0.3-0.2c2.2-2,3.4-3.8,3.9-5.3v-0.1c0,0,0,0,0,0v-1.7 c0-0.3-0.3-0.6-0.6-0.6h-4l0-2h4.5c0.1,0,0.1,0,0.1-0.1v-2.1c0-0.1,0-0.1-0.1-0.1h-4.4l0-1.8c0-0.1-0.1-0.3-0.3-0.3h-2.7 c-0.1,0-0.3,0.1-0.3,0.3l0,1.8h-4.2c-0.1,0-0.1,0-0.1,0.1l0,2.1c0,0.1,0,0.1,0.1,0.1h4.2l0,2h-4.1c-0.1,0-0.1,0-0.1,0.1l0,2.1 c0,0.1,0,0.1,0.1,0.1h9.1v0c0,0-0.6,1.4-3.2,3.7c-1.7-1.5-2.8-3-2.8-3h-3c0,0,0.6,2,3.6,4.7c-1.4,0.9-2.9,1.8-4.5,2.2v2.4 c0,0,3.1-0.2,6.7-2.8c0,0,0,0,0,0c2.4,1.7,4.7,2.7,6.6,2.8V31.8z"/>
                    </Canvas>
                </Viewbox>
            </StackPanel>
        </Grid>
        
        <!-- 工具栏 -->
        <Border Grid.Row="1" Background="{DynamicResource AppControlBackgroundBrush}" BorderBrush="{DynamicResource AppBorderBrush}" BorderThickness="0,1">
            <Grid Margin="10">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <!-- 数据操作组 -->
                <StackPanel Grid.Column="0" Orientation="Vertical">
                    <TextBlock Text="数据操作" Style="{DynamicResource BaseTextBlockStyle}" FontWeight="SemiBold" Margin="0,0,0,4"/>
                    <StackPanel Orientation="Horizontal">
                        <Button Content="选择数据文件" Command="{Binding LoadFileAsyncCommand}" Style="{DynamicResource WhiteButton}" Margin="0,0,8,0"/>
                        <Button Content="导出数据" Command="{Binding ExportDataAsyncCommand}"
                                IsEnabled="{Binding HasData}" Margin="0,0,8,0" Style="{DynamicResource WhiteButton}"/>
                        <Button Content="保存图表" Command="{Binding SavePlotCommand}"
                                IsEnabled="{Binding HasData}" Style="{DynamicResource WhiteButton}"/>
                    </StackPanel>
                </StackPanel>
                
                <!-- 批量处理组 -->
                <StackPanel Grid.Column="1" Orientation="Vertical" Margin="20,0,0,0">
                    <TextBlock Text="批量处理" Style="{DynamicResource BaseTextBlockStyle}" FontWeight="SemiBold" Margin="0,0,0,4"/>
                    <StackPanel Orientation="Horizontal">
                        <StackPanel Orientation="Horizontal" Margin="0,0,12,0" VerticalAlignment="Center">
                            <TextBlock Text="I1 (A):" Style="{DynamicResource ParameterLabel}" Margin="0,0,4,0" VerticalAlignment="Center"/>
                            <TextBox Text="{Binding I1Current}" Width="60" Style="{DynamicResource RoundedTextBox}" VerticalAlignment="Center"/>
                        </StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,12,0" VerticalAlignment="Center">
                            <TextBlock Text="I2 (A):" Style="{DynamicResource ParameterLabel}" Margin="0,0,4,0" VerticalAlignment="Center"/>
                            <TextBox Text="{Binding I2Current}" Width="60" Style="{DynamicResource RoundedTextBox}" VerticalAlignment="Center"/>
                        </StackPanel>
                        <Button Content="批量计算" Command="{Binding BatchProcessAsyncCommand}" Style="{DynamicResource WhiteButton}" VerticalAlignment="Center"/>
                    </StackPanel>
                </StackPanel>
                

            </Grid>
        </Border>
        
        <!-- 主内容区域 -->
        <Grid Grid.Row="2">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="350" MinWidth="300" MaxWidth="600"/>
                <ColumnDefinition Width="5"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            
            <!-- 左侧参数面板 -->
            <ScrollViewer Grid.Column="0" VerticalScrollBarVisibility="Auto"
                         Background="{DynamicResource AppControlBackgroundBrush}">
                <StackPanel Margin="16,8,8,8"
                           Background="{DynamicResource AppControlBackgroundBrush}">
                    
                    <!-- 曲线选择 -->
                    <Expander Header="曲线选择" IsExpanded="True" Margin="0,0,0,8">
                        <StackPanel Margin="8,0,0,0">
                            <ScrollViewer MaxHeight="300" VerticalScrollBarVisibility="Auto" 
                                        HorizontalScrollBarVisibility="Auto">
                                <ItemsControl ItemsSource="{Binding LoadedFiles}">
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate>
                                            <CheckBox Content="{Binding FileName}"
                                                    IsChecked="{Binding IsSelected}"
                                                    Margin="2"
                                                    Style="{DynamicResource UniformCheckBox}"/>
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                </ItemsControl>
                            </ScrollViewer>
                            
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Left" Margin="0,10,0,0">
                                <Button Content="全选" Command="{Binding SelectAllCommand}" Style="{DynamicResource WhiteButton}" Margin="0,0,8,0"/>
                                <Button Content="取消全选" Command="{Binding DeselectAllCommand}" Style="{DynamicResource WhiteButton}" Margin="0,0,8,0"/>
                                <Button Content="清除" Command="{Binding ClearAllCommand}" Style="{DynamicResource WhiteButton}"/>
                            </StackPanel>
                            
                            <TextBlock Text="{Binding StatusText}" Style="{DynamicResource ParameterTextBlock}" Margin="0,8,0,0"/>
                        </StackPanel>
                    </Expander>
                    
                    <!-- 计算参数 -->
                    <Expander Header="计算参数" IsExpanded="True" Margin="0,0,0,8">
                        <TextBlock Text="{Binding ParametersText}"
                                 Style="{DynamicResource ParameterTextBlock}" Margin="8,0,0,0"/>
                    </Expander>
                    
                    <!-- 数据平滑 -->
                    <Expander Header="数据平滑" IsExpanded="True" Margin="0,0,0,8">
                        <StackPanel Margin="8,0,0,0">
                            <CheckBox Content="启用平滑" IsChecked="{Binding IsSmoothingEnabled}" Style="{DynamicResource UniformCheckBox}"/>
                            
                            <!-- 算法选择 -->
                            <StackPanel Margin="0,10,0,0" IsEnabled="{Binding IsSmoothingEnabled}">
                                <TextBlock Text="平滑算法:" Style="{DynamicResource ParameterLabel}" Margin="0,0,0,5"/>
                                <ComboBox ItemsSource="{Binding AvailableAlgorithms}"
                                         SelectedValue="{Binding SelectedSmoothingAlgorithm}"
                                         SelectedValuePath="AlgorithmType"
                                         DisplayMemberPath="Name"
                                         Width="200" HorizontalAlignment="Left"/>
                            </StackPanel>
                            
                            <!-- 移动平均参数 -->
                            <StackPanel Orientation="Horizontal" Margin="0,10,0,0"
                                       Visibility="{Binding SelectedSmoothingAlgorithm, Converter={StaticResource AlgorithmTypeToVisibilityConverter}, ConverterParameter=MovingAverage}"
                                       IsEnabled="{Binding IsSmoothingEnabled}">
                                <TextBlock Text="窗口大小:" Style="{DynamicResource ParameterLabel}"/>
                                <Slider Value="{Binding SmoothingWindowSize}" Minimum="3" Maximum="51"
                                       Width="120" TickFrequency="2" IsSnapToTickEnabled="True"
                                       Style="{DynamicResource UniformSlider}" Margin="8,0"/>
                                <TextBlock Text="{Binding SmoothingWindowSize}" Style="{DynamicResource ParameterValue}"/>
                            </StackPanel>
                            
                            <!-- Savitzky-Golay参数 -->
                            <StackPanel Margin="0,10,0,0"
                                       Visibility="{Binding SelectedSmoothingAlgorithm, Converter={StaticResource AlgorithmTypeToVisibilityConverter}, ConverterParameter=SavitzkyGolay}"
                                       IsEnabled="{Binding IsSmoothingEnabled}">
                                <!-- 窗口大小 -->
                                <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                                    <TextBlock Text="窗口大小:" Style="{DynamicResource ParameterLabel}"/>
                                    <Slider Value="{Binding SavitzkyGolayWindowSize}" Minimum="3" Maximum="21"
                                           Width="100" TickFrequency="2" IsSnapToTickEnabled="True"
                                           Style="{DynamicResource UniformSlider}" Margin="8,0"/>
                                    <TextBlock Text="{Binding SavitzkyGolayWindowSize}" Style="{DynamicResource ParameterValue}"/>
                                </StackPanel>
                                <!-- 多项式阶数 -->
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="多项式阶:" Style="{DynamicResource ParameterLabel}"/>
                                    <Slider Value="{Binding SavitzkyGolayPolynomialOrder}" Minimum="1" Maximum="5"
                                           Width="100" TickFrequency="1" IsSnapToTickEnabled="True"
                                           Style="{DynamicResource UniformSlider}" Margin="8,0"/>
                                    <TextBlock Text="{Binding SavitzkyGolayPolynomialOrder}" Style="{DynamicResource ParameterValue}"/>
                                </StackPanel>
                            </StackPanel>
                            
                            <!-- 高斯滤波参数 -->
                            <StackPanel Orientation="Horizontal" Margin="0,10,0,0"
                                       Visibility="{Binding SelectedSmoothingAlgorithm, Converter={StaticResource AlgorithmTypeToVisibilityConverter}, ConverterParameter=Gaussian}"
                                       IsEnabled="{Binding IsSmoothingEnabled}">
                                <TextBlock Text="标准差(σ):" Style="{DynamicResource ParameterLabel}"/>
                                <Slider Value="{Binding GaussianSigma}" Minimum="0.1" Maximum="5.0"
                                       Width="120" TickFrequency="0.1" IsSnapToTickEnabled="True"
                                       Style="{DynamicResource UniformSlider}" Margin="8,0"/>
                                <TextBlock Text="{Binding GaussianSigma, StringFormat='{}{0:F1}'}" Style="{DynamicResource ParameterValue}"/>
                            </StackPanel>
                        </StackPanel>
                    </Expander>
                    
                    <!-- 图表设置 -->
                    <Expander Header="图表设置" IsExpanded="True" Margin="0,0,0,8">
                        <StackPanel Margin="8,0,0,0">
                            <!-- 显示选项 - 紧凑排列，左对齐 -->
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,12" HorizontalAlignment="Left">
                                <CheckBox Content="图例" IsChecked="{Binding ShowLegend}" Margin="0,0,12,0" Style="{DynamicResource UniformCheckBox}"/>
                                <CheckBox Content="网格" IsChecked="{Binding ShowGrid}" Style="{DynamicResource UniformCheckBox}"/>
                            </StackPanel>

                            <!-- 图表工具 - 左对齐 -->
                            <StackPanel HorizontalAlignment="Left">
                                <Button Content="重置缩放" Command="{Binding ResetZoomCommand}" HorizontalAlignment="Left" Style="{DynamicResource WhiteButton}"/>
                            </StackPanel>
                        </StackPanel>
                    </Expander>
                    

                    
                </StackPanel>
            </ScrollViewer>
            
            <!-- 分隔器 -->
            <GridSplitter Grid.Column="1" HorizontalAlignment="Stretch" Background="{DynamicResource AppBorderBrush}"/>
            
            <!-- 右侧图表区域 -->
            <TabControl Grid.Column="2" Margin="5" Style="{DynamicResource WhiteTabControl}">
                <TabItem Header="LIV曲线" Style="{DynamicResource WhiteTabItem}">
                    <Grid>
                        <oxy:PlotView Model="{Binding LIVPlotModel}"
                                     Background="{DynamicResource ChartBackgroundBrush}"
                                     MouseDoubleClick="PlotView_MouseDoubleClick"
                                     PreviewMouseRightButtonDown="PlotView_PreviewMouseRightButtonDown"
                                     Tag="LIV曲线"/>
                    </Grid>
                </TabItem>
                <TabItem Header="光谱图" Style="{DynamicResource WhiteTabItem}">
                    <Grid>
                        <oxy:PlotView Model="{Binding SpectrumPlotModel}"
                                     Background="{DynamicResource ChartBackgroundBrush}"
                                     MouseDoubleClick="PlotView_MouseDoubleClick"
                                     PreviewMouseRightButtonDown="PlotView_PreviewMouseRightButtonDown"
                                     Tag="光谱图"/>
                    </Grid>
                </TabItem>
                <TabItem Header="效率曲线" Style="{DynamicResource WhiteTabItem}">
                    <Grid>
                        <oxy:PlotView Model="{Binding EfficiencyPlotModel}"
                                     Background="{DynamicResource ChartBackgroundBrush}"
                                     MouseDoubleClick="PlotView_MouseDoubleClick"
                                     PreviewMouseRightButtonDown="PlotView_PreviewMouseRightButtonDown"
                                     Tag="效率曲线"/>
                    </Grid>
                </TabItem>
                <TabItem Header="发散角" Style="{DynamicResource WhiteTabItem}">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="2*"/>
                            <ColumnDefinition Width="*" MinWidth="200"/>
                        </Grid.ColumnDefinitions>

                        <!-- HFF图表 -->
                        <Border Grid.Row="0" Grid.Column="0"
                               BorderBrush="{DynamicResource AppBorderBrush}"
                               BorderThickness="1" Margin="2" CornerRadius="4"
                               Background="{DynamicResource ChartBackgroundBrush}">
                            <oxy:PlotView Model="{Binding HffDivergencePlotModel}"
                                         Background="{DynamicResource ChartBackgroundBrush}"
                                         MouseDoubleClick="PlotView_MouseDoubleClick"
                                         PreviewMouseRightButtonDown="PlotView_PreviewMouseRightButtonDown"
                                         Tag="HFF发散角"/>
                        </Border>

                        <!-- VFF图表 -->
                        <Border Grid.Row="1" Grid.Column="0"
                               BorderBrush="{DynamicResource AppBorderBrush}"
                               BorderThickness="1" Margin="2" CornerRadius="4"
                               Background="{DynamicResource ChartBackgroundBrush}">
                            <oxy:PlotView Model="{Binding VffDivergencePlotModel}"
                                         Background="{DynamicResource ChartBackgroundBrush}"
                                         MouseDoubleClick="PlotView_MouseDoubleClick"
                                         PreviewMouseRightButtonDown="PlotView_PreviewMouseRightButtonDown"
                                         Tag="VFF发散角"/>
                        </Border>
                        
                        <!-- 发散角计算结果 -->
                        <Expander Grid.Row="0" Grid.RowSpan="2" Grid.Column="1" Header="发散角计算结果"
                                 IsExpanded="True" Margin="5"
                                 Background="{DynamicResource AppControlBackgroundBrush}">
                            <ScrollViewer VerticalScrollBarVisibility="Auto"
                                         Background="{DynamicResource AppControlBackgroundBrush}">
                                <TextBlock Text="{Binding DivergenceResultText}"
                                          Style="{DynamicResource ParameterTextBlock}"
                                          FontFamily="Cascadia Code, Consolas"
                                          FontSize="11"
                                          Padding="5"
                                          Background="{DynamicResource AppControlBackgroundBrush}"/>
                            </ScrollViewer>
                        </Expander>
                    </Grid>
                </TabItem>
            </TabControl>
        </Grid>
        
        <!-- 进度指示器 -->
        <Grid Grid.RowSpan="3" Visibility="{Binding IsLoading, Converter={StaticResource BoolToVisConverter}}">
            <Grid.Background>
                <SolidColorBrush Color="{DynamicResource SystemChromeBlackMediumColor}" Opacity="0.5"/>
            </Grid.Background>
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center" Background="{DynamicResource AppControlBackgroundBrush}"
                       Width="300" Height="100">
                <ProgressBar IsIndeterminate="True" Margin="20"/>
                <TextBlock Text="{Binding LoadingText}" HorizontalAlignment="Center" Margin="10"/>
            </StackPanel>
        </Grid>
    </Grid>
</Window>