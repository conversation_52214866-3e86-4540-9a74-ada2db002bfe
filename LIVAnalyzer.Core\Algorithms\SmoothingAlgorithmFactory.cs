using System;
using System.Collections.Generic;
using System.Linq;
using LIVAnalyzer.Models;

namespace LIVAnalyzer.Core.Algorithms
{
    /// <summary>
    /// 平滑算法工厂
    /// </summary>
    public static class SmoothingAlgorithmFactory
    {
        private static readonly Dictionary<SmoothingAlgorithmType, ISmoothingAlgorithm> _algorithms;
        
        static SmoothingAlgorithmFactory()
        {
            _algorithms = new Dictionary<SmoothingAlgorithmType, ISmoothingAlgorithm>
            {
                { SmoothingAlgorithmType.MovingAverage, new MovingAverageAlgorithm() },
                { SmoothingAlgorithmType.SavitzkyGolay, new SavitzkyGolayAlgorithm() },
                { SmoothingAlgorithmType.Gaussian, new GaussianAlgorithm() }
                // LowPass算法后续添加
            };
        }
        
        /// <summary>
        /// 获取所有可用的算法
        /// </summary>
        /// <returns>算法列表</returns>
        public static IEnumerable<ISmoothingAlgorithm> GetAllAlgorithms()
        {
            return _algorithms.Values;
        }
        
        /// <summary>
        /// 根据类型获取算法
        /// </summary>
        /// <param name="algorithmType">算法类型</param>
        /// <returns>算法实例</returns>
        public static ISmoothingAlgorithm GetAlgorithm(SmoothingAlgorithmType algorithmType)
        {
            if (_algorithms.TryGetValue(algorithmType, out var algorithm))
            {
                return algorithm;
            }
            
            // 默认返回移动平均算法
            return _algorithms[SmoothingAlgorithmType.MovingAverage];
        }
        
        /// <summary>
        /// 检查算法类型是否支持
        /// </summary>
        /// <param name="algorithmType">算法类型</param>
        /// <returns>是否支持</returns>
        public static bool IsSupported(SmoothingAlgorithmType algorithmType)
        {
            return _algorithms.ContainsKey(algorithmType);
        }
        
        /// <summary>
        /// 获取算法的显示名称
        /// </summary>
        /// <param name="algorithmType">算法类型</param>
        /// <returns>显示名称</returns>
        public static string GetAlgorithmName(SmoothingAlgorithmType algorithmType)
        {
            return GetAlgorithm(algorithmType).Name;
        }
        
        /// <summary>
        /// 获取算法的描述
        /// </summary>
        /// <param name="algorithmType">算法类型</param>
        /// <returns>算法描述</returns>
        public static string GetAlgorithmDescription(SmoothingAlgorithmType algorithmType)
        {
            return GetAlgorithm(algorithmType).Description;
        }
        
        /// <summary>
        /// 根据数据特征推荐算法
        /// </summary>
        /// <param name="dataLength">数据长度</param>
        /// <param name="noiseLevel">噪声水平估计（0-1）</param>
        /// <param name="hasSharpFeatures">是否有尖锐特征（如峰值）</param>
        /// <returns>推荐的算法类型和配置</returns>
        public static (SmoothingAlgorithmType algorithmType, SmoothingConfig config) RecommendAlgorithm(
            int dataLength, double noiseLevel = 0.1, bool hasSharpFeatures = false)
        {
            SmoothingAlgorithmType recommendedType;
            
            if (hasSharpFeatures && noiseLevel < 0.3)
            {
                // 有尖锐特征且噪声不太大：推荐Savitzky-Golay
                recommendedType = SmoothingAlgorithmType.SavitzkyGolay;
            }
            else if (noiseLevel > 0.4)
            {
                // 高噪声：推荐高斯滤波
                recommendedType = SmoothingAlgorithmType.Gaussian;
            }
            else
            {
                // 一般情况：移动平均
                recommendedType = SmoothingAlgorithmType.MovingAverage;
            }
            
            var algorithm = GetAlgorithm(recommendedType);
            var config = algorithm.GetRecommendedParameters(dataLength, noiseLevel);
            
            return (recommendedType, config);
        }
    }
}