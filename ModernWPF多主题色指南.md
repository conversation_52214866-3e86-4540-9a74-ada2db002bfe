# ModernWPF 多主题色自定义指南

## 1. 主题色自定义方法

### 方法一：直接设置主题色

```csharp
using ModernWpf;
using Windows.UI;

// 设置主题色
ThemeManager.Current.AccentColor = Colors.Orange;  // 橙色主题
ThemeManager.Current.AccentColor = Colors.Green;   // 绿色主题
ThemeManager.Current.AccentColor = Colors.Purple;  // 紫色主题
ThemeManager.Current.AccentColor = Color.FromArgb(255, 0, 120, 215); // 自定义颜色
```

### 方法二：预定义主题色集合

```csharp
public class ThemeColorManager
{
    // 预定义的主题色
    public static readonly Dictionary<string, Color> ThemeColors = new()
    {
        { "默认蓝", Color.FromArgb(255, 0, 120, 215) },
        { "活力橙", Color.FromArgb(255, 255, 140, 0) },
        { "自然绿", Color.FromArgb(255, 0, 178, 148) },
        { "优雅紫", Color.FromArgb(255, 135, 100, 184) },
        { "热情红", Color.FromArgb(255, 229, 20, 0) },
        { "科技青", Color.FromArgb(255, 0, 188, 212) },
        { "专业灰", Color.FromArgb(255, 96, 125, 139) },
        { "活力粉", Color.FromArgb(255, 233, 30, 99) },
        { "深邃蓝", Color.FromArgb(255, 33, 150, 243) },
        { "翡翠绿", Color.FromArgb(255, 76, 175, 80) }
    };
    
    public static void ApplyThemeColor(string colorName)
    {
        if (ThemeColors.TryGetValue(colorName, out var color))
        {
            ThemeManager.Current.AccentColor = color;
        }
    }
}
```

## 2. 在 ViewModel 中实现

```csharp
public class ThemeSettingsViewModel : ObservableObject
{
    private string _selectedThemeColor = "默认蓝";
    private ApplicationTheme? _applicationTheme;
    
    public ObservableCollection<string> AvailableColors { get; }
    
    public string SelectedThemeColor
    {
        get => _selectedThemeColor;
        set
        {
            if (SetProperty(ref _selectedThemeColor, value))
            {
                ThemeColorManager.ApplyThemeColor(value);
                SaveThemeSettings();
            }
        }
    }
    
    public ApplicationTheme? ApplicationTheme
    {
        get => _applicationTheme;
        set
        {
            if (SetProperty(ref _applicationTheme, value))
            {
                ThemeManager.Current.ApplicationTheme = value;
                SaveThemeSettings();
            }
        }
    }
    
    public ThemeSettingsViewModel()
    {
        AvailableColors = new ObservableCollection<string>(ThemeColorManager.ThemeColors.Keys);
        LoadThemeSettings();
    }
    
    private void SaveThemeSettings()
    {
        var config = ConfigurationManager.Instance.GetConfig();
        config.UI.ThemeColor = SelectedThemeColor;
        config.UI.Theme = ApplicationTheme?.ToString() ?? "System";
        ConfigurationManager.Instance.SaveConfig(config);
    }
    
    private void LoadThemeSettings()
    {
        var config = ConfigurationManager.Instance.GetConfig();
        SelectedThemeColor = config.UI.ThemeColor ?? "默认蓝";
        ApplicationTheme = config.UI.Theme switch
        {
            "Dark" => ModernWpf.ApplicationTheme.Dark,
            "Light" => ModernWpf.ApplicationTheme.Light,
            _ => null
        };
    }
}
```

## 3. UI 实现 - 主题设置面板

```xml
<ui:SimpleStackPanel Spacing="16" Margin="20">
    <!-- 主题模式选择 -->
    <TextBlock Text="主题模式" Style="{StaticResource SubtitleTextBlockStyle}"/>
    <ui:RadioButtons SelectedIndex="{Binding ThemeModeIndex}">
        <RadioButton Content="跟随系统"/>
        <RadioButton Content="浅色"/>
        <RadioButton Content="深色"/>
    </ui:RadioButtons>
    
    <!-- 主题色选择 -->
    <TextBlock Text="主题颜色" Style="{StaticResource SubtitleTextBlockStyle}" Margin="0,16,0,0"/>
    
    <!-- 方式1：下拉列表 -->
    <ComboBox ItemsSource="{Binding AvailableColors}"
              SelectedItem="{Binding SelectedThemeColor}"
              Width="200"
              HorizontalAlignment="Left"/>
    
    <!-- 方式2：色块网格 -->
    <ItemsControl ItemsSource="{Binding ThemeColorItems}">
        <ItemsControl.ItemsPanel>
            <ItemsPanelTemplate>
                <WrapPanel/>
            </ItemsPanelTemplate>
        </ItemsControl.ItemsPanel>
        <ItemsControl.ItemTemplate>
            <DataTemplate>
                <Button Width="60" Height="60" 
                        Margin="4"
                        Command="{Binding DataContext.SelectColorCommand, RelativeSource={RelativeSource AncestorType=ItemsControl}}"
                        CommandParameter="{Binding Name}">
                    <Button.Template>
                        <ControlTemplate TargetType="Button">
                            <Border Background="{Binding ColorBrush}"
                                    CornerRadius="8"
                                    BorderThickness="3">
                                <Border.Style>
                                    <Style TargetType="Border">
                                        <Setter Property="BorderBrush" Value="Transparent"/>
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding IsSelected}" Value="True">
                                                <Setter Property="BorderBrush" Value="{DynamicResource SystemControlForegroundBaseHighBrush}"/>
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </Border.Style>
                                <TextBlock Text="✓" 
                                          Foreground="White"
                                          FontSize="24"
                                          HorizontalAlignment="Center"
                                          VerticalAlignment="Center"
                                          Visibility="{Binding IsSelected, Converter={StaticResource BoolToVisibilityConverter}}"/>
                            </Border>
                        </ControlTemplate>
                    </Button.Template>
                </Button>
            </DataTemplate>
        </ItemsControl.ItemTemplate>
    </ItemsControl>
    
    <!-- 自定义颜色 -->
    <TextBlock Text="自定义颜色" Style="{StaticResource SubtitleTextBlockStyle}" Margin="0,16,0,0"/>
    <StackPanel Orientation="Horizontal" Spacing="8">
        <ui:ColorPicker Color="{Binding CustomColor}"
                        IsColorChannelTextInputVisible="False"
                        IsHexInputVisible="True"/>
        <Button Content="应用" 
                Command="{Binding ApplyCustomColorCommand}"
                Style="{StaticResource AccentButtonStyle}"/>
    </StackPanel>
</ui:SimpleStackPanel>
```

## 4. 高级主题定制

### 创建完整的主题包

```csharp
public class ThemePackage
{
    public string Name { get; set; }
    public Color AccentColor { get; set; }
    public Color AccentLight1 { get; set; }
    public Color AccentLight2 { get; set; }
    public Color AccentLight3 { get; set; }
    public Color AccentDark1 { get; set; }
    public Color AccentDark2 { get; set; }
    public Color AccentDark3 { get; set; }
    
    // 应用主题包
    public void Apply()
    {
        ThemeManager.Current.AccentColor = AccentColor;
        
        // 通过资源字典应用更多颜色
        var resources = Application.Current.Resources;
        resources["SystemAccentColorLight1"] = AccentLight1;
        resources["SystemAccentColorLight2"] = AccentLight2;
        resources["SystemAccentColorLight3"] = AccentLight3;
        resources["SystemAccentColorDark1"] = AccentDark1;
        resources["SystemAccentColorDark2"] = AccentDark2;
        resources["SystemAccentColorDark3"] = AccentDark3;
    }
}

// 预定义的主题包
public static class ThemePackages
{
    public static ThemePackage Ocean = new()
    {
        Name = "海洋",
        AccentColor = Color.FromArgb(255, 0, 120, 215),
        AccentLight1 = Color.FromArgb(255, 76, 160, 235),
        AccentLight2 = Color.FromArgb(255, 128, 191, 245),
        AccentLight3 = Color.FromArgb(255, 179, 223, 255),
        AccentDark1 = Color.FromArgb(255, 0, 84, 151),
        AccentDark2 = Color.FromArgb(255, 0, 48, 87),
        AccentDark3 = Color.FromArgb(255, 0, 24, 43)
    };
    
    public static ThemePackage Forest = new()
    {
        Name = "森林",
        AccentColor = Color.FromArgb(255, 76, 175, 80),
        // ... 其他颜色定义
    };
    
    public static ThemePackage Sunset = new()
    {
        Name = "日落",
        AccentColor = Color.FromArgb(255, 255, 152, 0),
        // ... 其他颜色定义
    };
}
```

## 5. 实时预览功能

```csharp
public class ThemePreviewViewModel : ObservableObject
{
    private Color _previewColor;
    private Color _originalColor;
    
    public Color PreviewColor
    {
        get => _previewColor;
        set
        {
            if (SetProperty(ref _previewColor, value))
            {
                // 实时预览，但不保存
                ThemeManager.Current.AccentColor = value;
            }
        }
    }
    
    public ICommand StartPreviewCommand { get; }
    public ICommand ApplyPreviewCommand { get; }
    public ICommand CancelPreviewCommand { get; }
    
    public ThemePreviewViewModel()
    {
        StartPreviewCommand = new RelayCommand(StartPreview);
        ApplyPreviewCommand = new RelayCommand(ApplyPreview);
        CancelPreviewCommand = new RelayCommand(CancelPreview);
    }
    
    private void StartPreview()
    {
        _originalColor = ThemeManager.Current.AccentColor ?? Colors.Blue;
        PreviewColor = _originalColor;
    }
    
    private void ApplyPreview()
    {
        // 保存新颜色
        SaveThemeColor(PreviewColor);
    }
    
    private void CancelPreview()
    {
        // 恢复原始颜色
        ThemeManager.Current.AccentColor = _originalColor;
    }
}
```

## 6. 配置文件支持

在 `ApplicationConfig.cs` 中添加：

```csharp
public class UIConfig
{
    public string Theme { get; set; } = "System";
    public string ThemeColor { get; set; } = "默认蓝";
    public string CustomAccentColor { get; set; } // 保存自定义颜色的十六进制值
    public List<string> RecentColors { get; set; } = new(); // 最近使用的颜色
}
```

## 7. 使用效果

通过以上实现，用户可以：

1. **选择预定义主题色**：提供10+种精心设计的主题色
2. **自定义颜色**：使用颜色选择器创建独特的主题
3. **实时预览**：在应用前预览效果
4. **保存偏好**：记住用户的选择
5. **快速切换**：一键切换不同主题色

## 8. 注意事项

1. **颜色对比度**：确保选择的颜色在明暗主题下都有良好的对比度
2. **颜色一致性**：主题色会影响所有使用 AccentColor 的控件
3. **性能考虑**：频繁切换主题色可能会有轻微的性能影响
4. **兼容性**：某些第三方控件可能需要额外适配

现在您的应用可以支持丰富的主题定制了！