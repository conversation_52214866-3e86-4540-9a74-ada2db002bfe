import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

def analyze_excel_divergence_data(file_path):
    """分析Excel文件中的HFF和VFF数据"""
    
    print(f"正在读取文件: {file_path}")
    
    try:
        # 读取Excel文件
        xlsx = pd.ExcelFile(file_path)
        
        # 检查工作表名称
        print(f"\n可用的工作表: {xlsx.sheet_names}")
        
        # 分析HFF数据
        if 'HFF' in xlsx.sheet_names:
            print("\n=== HFF (水平远场) 数据分析 ===")
            hff_data = pd.read_excel(xlsx, sheet_name='HFF')
            analyze_divergence_sheet(hff_data, "HFF")
        else:
            print("\n警告: 未找到HFF工作表")
            
        # 分析VFF数据
        if 'VFF' in xlsx.sheet_names:
            print("\n=== VFF (垂直远场) 数据分析 ===")
            vff_data = pd.read_excel(xlsx, sheet_name='VFF')
            analyze_divergence_sheet(vff_data, "VFF")
        else:
            print("\n警告: 未找到VFF工作表")
            
    except Exception as e:
        print(f"读取文件时出错: {e}")

def analyze_divergence_sheet(df, sheet_name):
    """分析单个发散角数据表"""
    
    print(f"\n数据形状: {df.shape}")
    print(f"列名: {list(df.columns)}")
    
    # 假设第一列是角度，第二列是强度
    if len(df.columns) >= 2:
        angle_col = df.columns[0]
        intensity_col = df.columns[1]
        
        angles = df[angle_col].values
        intensities = df[intensity_col].values
        
        # 基本统计信息
        print(f"\n角度范围: {angles.min():.2f} 到 {angles.max():.2f} 度")
        print(f"角度点数: {len(angles)}")
        print(f"角度步长: {np.mean(np.diff(angles)):.4f} 度")
        
        print(f"\n强度统计:")
        print(f"  最小值: {intensities.min():.6f}")
        print(f"  最大值: {intensities.max():.6f}")
        print(f"  平均值: {intensities.mean():.6f}")
        print(f"  标准差: {intensities.std():.6f}")
        
        # 检查边界值
        print(f"\n边界强度检查:")
        print(f"  前5个点的强度: {intensities[:5]}")
        print(f"  后5个点的强度: {intensities[-5:]}")
        
        # 检查是否归一化
        if intensities.max() > 0:
            normalized = intensities / intensities.max()
            print(f"\n归一化后的边界值:")
            print(f"  前5个点: {normalized[:5]}")
            print(f"  后5个点: {normalized[-5:]}")
        
        # 检查异常值
        print(f"\n异常值检查:")
        negative_count = np.sum(intensities < 0)
        print(f"  负值数量: {negative_count}")
        if negative_count > 0:
            print(f"  负值位置: {np.where(intensities < 0)[0]}")
        
        # 检查非常大的值
        if intensities.max() > 0:
            outliers = np.where(intensities > 10 * intensities.mean())[0]
            if len(outliers) > 0:
                print(f"  异常大值位置: {outliers}")
                print(f"  异常大值: {intensities[outliers]}")
        
        # 计算积分面积（使用梯形法则）
        # 注意：这里假设角度是以度为单位，需要转换为弧度
        angles_rad = np.deg2rad(angles)
        integral = np.trapz(intensities, angles_rad)
        print(f"\n数据积分面积: {integral:.6f}")
        
        # 计算半高全宽 (FWHM)
        if intensities.max() > 0:
            half_max = intensities.max() / 2
            indices = np.where(intensities >= half_max)[0]
            if len(indices) > 0:
                fwhm = angles[indices[-1]] - angles[indices[0]]
                print(f"半高全宽 (FWHM): {fwhm:.2f} 度")
        
        # 绘制数据图
        plt.figure(figsize=(10, 6))
        plt.plot(angles, intensities, 'b-', linewidth=1)
        plt.xlabel('角度 (度)')
        plt.ylabel('强度')
        plt.title(f'{sheet_name} 发散角分布')
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig(f'{sheet_name}_distribution.png', dpi=150)
        plt.close()
        print(f"\n已保存图表: {sheet_name}_distribution.png")
        
    else:
        print(f"错误: 数据列数不足，需要至少2列（角度和强度）")
    
    # 显示前10行数据
    print(f"\n前10行数据:")
    print(df.head(10))
    
    # 显示后10行数据
    print(f"\n后10行数据:")
    print(df.tail(10))

if __name__ == "__main__":
    file_path = r"E:\01LaserPackage\software\LIV_Analyzer\光峰芯片测试数据\640nm_QCW2P5A_30duty_T45C_LIV+Spectrum0716\芯瑞光红光芯片测试数据\R06+COS01+183_converted.xlsx"
    analyze_excel_divergence_data(file_path)