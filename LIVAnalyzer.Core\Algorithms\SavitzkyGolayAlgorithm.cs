using System;
using System.Linq;
using LIVAnalyzer.Models;
using MathNet.Numerics.LinearAlgebra;
using MathNet.Numerics.LinearAlgebra.Double;

namespace LIVAnalyzer.Core.Algorithms
{
    /// <summary>
    /// Savitzky-Golay滤波器算法
    /// 保持数据特征的同时进行平滑，特别适合光谱数据处理
    /// </summary>
    public class SavitzkyGolayAlgorithm : ISmoothingAlgorithm
    {
        public string Name => "Savitzky-Golay滤波器";
        
        public string Description => "基于多项式拟合的平滑算法，能较好保持峰值和边缘特征，适合光谱数据处理";
        
        public SmoothingAlgorithmType AlgorithmType => SmoothingAlgorithmType.SavitzkyGolay;
        
        public bool SupportsRealTimePreview => true;
        
        public double[] Smooth(double[] xData, double[] yData, SmoothingConfig config)
        {
            if (xData == null || yData == null)
                throw new ArgumentNullException("输入数据不能为空");
            
            if (xData.Length != yData.Length)
                throw new ArgumentException("X和Y数据长度不匹配");
            
            if (xData.Length == 0)
                return Array.Empty<double>();
            
            var sgConfig = config.SavitzkyGolay;
            int windowSize = sgConfig.WindowSize;
            int polynomialOrder = sgConfig.PolynomialOrder;
            
            // 确保窗口大小为奇数
            if (windowSize % 2 == 0)
                windowSize++;
            
            // 验证参数
            windowSize = Math.Max(sgConfig.MinWindowSize, Math.Min(sgConfig.MaxWindowSize, windowSize));
            polynomialOrder = Math.Max(sgConfig.MinPolynomialOrder, 
                Math.Min(sgConfig.MaxPolynomialOrder, Math.Min(polynomialOrder, windowSize - 1)));
            
            return SavitzkyGolayFilter(yData, windowSize, polynomialOrder);
        }
        
        public string? ValidateParameters(SmoothingConfig config, int dataLength)
        {
            var sgConfig = config.SavitzkyGolay;
            
            if (sgConfig.WindowSize < sgConfig.MinWindowSize)
                return $"窗口大小不能小于 {sgConfig.MinWindowSize}";
                
            if (sgConfig.WindowSize > sgConfig.MaxWindowSize)
                return $"窗口大小不能大于 {sgConfig.MaxWindowSize}";
                
            if (sgConfig.WindowSize >= dataLength)
                return "窗口大小不能大于等于数据长度";
            
            if (sgConfig.PolynomialOrder < sgConfig.MinPolynomialOrder)
                return $"多项式阶数不能小于 {sgConfig.MinPolynomialOrder}";
                
            if (sgConfig.PolynomialOrder > sgConfig.MaxPolynomialOrder)
                return $"多项式阶数不能大于 {sgConfig.MaxPolynomialOrder}";
                
            if (sgConfig.PolynomialOrder >= sgConfig.WindowSize)
                return "多项式阶数必须小于窗口大小";
            
            return null;
        }
        
        public SmoothingConfig GetRecommendedParameters(int dataLength, double noiseLevel = 0.1)
        {
            // 根据数据长度和噪声水平推荐参数
            int recommendedWindowSize = Math.Max(5, Math.Min(
                (int)(dataLength * 0.03), // 不超过数据长度的3%
                (int)(7 + noiseLevel * 8) // 噪声越大，窗口越大
            ));
            
            // 确保为奇数
            if (recommendedWindowSize % 2 == 0)
                recommendedWindowSize++;
            
            // 推荐多项式阶数
            int recommendedOrder = noiseLevel > 0.2 ? 2 : 3; // 噪声大时用低阶
            
            var config = new SmoothingConfig
            {
                AlgorithmType = SmoothingAlgorithmType.SavitzkyGolay,
                EnableByDefault = false
            };
            
            config.SavitzkyGolay.WindowSize = recommendedWindowSize;
            config.SavitzkyGolay.PolynomialOrder = recommendedOrder;
            
            return config;
        }
        
        /// <summary>
        /// Savitzky-Golay滤波器实现
        /// </summary>
        /// <param name="data">输入数据</param>
        /// <param name="windowSize">窗口大小（奇数）</param>
        /// <param name="polynomialOrder">多项式阶数</param>
        /// <returns>平滑后的数据</returns>
        private static double[] SavitzkyGolayFilter(double[] data, int windowSize, int polynomialOrder)
        {
            if (data == null || data.Length == 0)
                return Array.Empty<double>();
            
            if (windowSize >= data.Length)
                return (double[])data.Clone();
            
            // 计算Savitzky-Golay系数
            var coefficients = CalculateSavitzkyGolayCoefficients(windowSize, polynomialOrder);
            
            var result = new double[data.Length];
            int halfWindow = windowSize / 2;
            
            for (int i = 0; i < data.Length; i++)
            {
                double sum = 0;
                int validCount = 0;
                
                for (int j = -halfWindow; j <= halfWindow; j++)
                {
                    int index = i + j;
                    
                    // 边界处理：镜像填充
                    if (index < 0)
                        index = -index;
                    else if (index >= data.Length)
                        index = 2 * data.Length - index - 2;
                    
                    if (index >= 0 && index < data.Length && 
                        !double.IsNaN(data[index]) && !double.IsInfinity(data[index]))
                    {
                        sum += coefficients[j + halfWindow] * data[index];
                        validCount++;
                    }
                }
                
                result[i] = validCount > halfWindow ? sum : data[i];
            }
            
            return result;
        }
        
        /// <summary>
        /// 计算Savitzky-Golay滤波器系数
        /// </summary>
        /// <param name="windowSize">窗口大小</param>
        /// <param name="polynomialOrder">多项式阶数</param>
        /// <returns>滤波器系数</returns>
        private static double[] CalculateSavitzkyGolayCoefficients(int windowSize, int polynomialOrder)
        {
            int halfWindow = windowSize / 2;
            
            // 构建Vandermonde矩阵
            var A = DenseMatrix.Create(windowSize, polynomialOrder + 1, (i, j) => 
                Math.Pow(i - halfWindow, j));
            
            // 目标向量（只有中心点为1，其他为0的向量）
            var b = DenseVector.Create(windowSize, i => i == halfWindow ? 1.0 : 0.0);
            
            try
            {
                // 求解最小二乘问题：min ||Ax - b||²
                var svd = A.Svd();
                var coefficients = svd.Solve(b);
                
                // 转换为滤波器系数
                var result = new double[windowSize];
                for (int i = 0; i < windowSize; i++)
                {
                    double sum = 0;
                    for (int j = 0; j <= polynomialOrder; j++)
                    {
                        sum += coefficients[j] * Math.Pow(i - halfWindow, j);
                    }
                    result[i] = sum;
                }
                
                return result;
            }
            catch
            {
                // 降级到移动平均
                var coefficients = new double[windowSize];
                Array.Fill(coefficients, 1.0 / windowSize);
                return coefficients;
            }
        }
    }
}