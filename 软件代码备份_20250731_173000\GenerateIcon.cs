using System;
using System.IO;
using LIVAnalyzer.UI.Utilities;

namespace IconGenerator
{
    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("========================================");
            Console.WriteLine("    LIV分析工具图标生成器");
            Console.WriteLine("========================================");
            Console.WriteLine();
            
            try
            {
                // 确保目录存在
                var iconDir = Path.Combine("LIVAnalyzer.UI", "Resources", "Icons");
                if (!Directory.Exists(iconDir))
                {
                    Directory.CreateDirectory(iconDir);
                    Console.WriteLine($"创建目录: {iconDir}");
                }
                
                var iconPath = Path.Combine(iconDir, "app-icon.ico");
                
                Console.WriteLine("正在生成图标文件...");
                Console.WriteLine($"输出路径: {iconPath}");
                Console.WriteLine();
                
                // 生成图标
                IconGenerator.GenerateIconFile(iconPath);
                
                Console.WriteLine("✓ 图标生成成功！");
                
                // 显示文件信息
                var fileInfo = new FileInfo(iconPath);
                Console.WriteLine();
                Console.WriteLine("文件信息:");
                Console.WriteLine($"  路径: {fileInfo.FullName}");
                Console.WriteLine($"  大小: {fileInfo.Length / 1024.0:F2} KB");
                Console.WriteLine($"  包含尺寸: 16x16, 32x32, 48x48, 64x64, 128x128, 256x256 像素");
                
                Console.WriteLine();
                Console.WriteLine("========================================");
                Console.WriteLine("下一步: 集成到WPF应用程序");
                Console.WriteLine("========================================");
                Console.WriteLine("1. 更新项目文件以包含图标");
                Console.WriteLine("2. 设置窗口图标属性");
                Console.WriteLine("3. 配置应用程序清单");
                
            }
            catch (Exception ex)
            {
                Console.WriteLine($"错误: {ex.Message}");
                Console.WriteLine();
                Console.WriteLine("详细错误信息:");
                Console.WriteLine(ex.ToString());
            }
            
            Console.WriteLine();
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }
    }
}
