import { useState } from 'react';
import { useAppStore } from '../state/store';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { Checkbox } from './ui/checkbox';
import { Select } from './ui/select';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { 
  exportCSV, 
  exportExcel, 
  exportPDFReport, 
  exportBatch, 
  exportCompleteReport 
} from '../services/export';
import { Download, FileText, Table, Image, Package } from 'lucide-react';

export default function ExportPanel() {
  const { data, results } = useAppStore();
  const [filename, setFilename] = useState('LIV_Analysis');
  const [exportOptions, setExportOptions] = useState({
    includeCharts: true,
    includeData: true,
    includeAnalysis: true,
    format: 'excel' as 'csv' | 'excel' | 'pdf' | 'complete'
  });
  const [isExporting, setIsExporting] = useState(false);

  const handleExport = async () => {
    if (!data) {
      alert('没有可导出的数据');
      return;
    }

    setIsExporting(true);
    
    try {
      const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
      const exportFilename = `${filename}_${timestamp}`;

      switch (exportOptions.format) {
        case 'csv':
          await exportCSVData(exportFilename);
          break;
        case 'excel':
          await exportExcelData(exportFilename);
          break;
        case 'pdf':
          await exportPDFData(exportFilename);
          break;
        case 'complete':
          await exportCompleteData(exportFilename);
          break;
      }

      console.log('导出成功');
    } catch (error) {
      console.error('导出失败:', error);
      alert('导出失败，请检查数据格式');
    } finally {
      setIsExporting(false);
    }
  };

  const exportCSVData = async (filename: string) => {
    if (!data?.power) return;
    
    const headers = ['电流 (A)', '功率 (W)'];
    if (data.voltage) {
      headers.push('电压 (V)');
    }
    
    const rows = data.power.current.map((current, i) => {
      const row: (string | number)[] = [current, data.power!.power[i]];
      if (data.voltage) {
        row.push(data.voltage.voltage[i]);
      }
      return row;
    });
    
    exportCSV(filename, headers, rows);
  };

  const exportExcelData = async (filename: string) => {
    const exportData = {
      livData: data?.power ? {
        current: data.power.current,
        power: data.power.power,
        voltage: data.voltage?.voltage
      } : undefined,
      spectralData: data?.spectral ? {
        wavelength: data.spectral.wavelength,
        intensity: data.spectral.intensity
      } : undefined,
      divergenceData: {
        hff: data?.hff ? {
          angle: data.hff.angle,
          intensity: data.hff.intensity
        } : undefined,
        vff: data?.vff ? {
          angle: data.vff.angle,
          intensity: data.vff.intensity
        } : undefined
      },
      analysisResults: exportOptions.includeAnalysis ? {
        thresholdCurrent: results?.livParameters?.thresholdCurrent_mA,
        slopeEfficiency: results?.livParameters?.slopeEfficiency_W_per_A,
        seriesResistance: results?.livParameters?.seriesResistance_Ohm,
        maxEfficiency: results?.livParameters?.maxEfficiency
      } : undefined
    };

    await exportBatch(filename, exportData);
  };

  const exportPDFData = async (filename: string) => {
    const charts: Array<{ title: string; container: HTMLElement }> = [];
    
    if (exportOptions.includeCharts) {
      // 收集图表容器
      const livChart = document.querySelector('[data-chart="liv"]') as HTMLElement;
      if (livChart) {
        charts.push({ title: 'LIV特性曲线', container: livChart });
      }
      
      const spectrumChart = document.querySelector('[data-chart="spectrum"]') as HTMLElement;
      if (spectrumChart) {
        charts.push({ title: '光谱特性', container: spectrumChart });
      }
      
      const divergenceChart = document.querySelector('[data-chart="divergence"]') as HTMLElement;
      if (divergenceChart) {
        charts.push({ title: '发散角特性', container: divergenceChart });
      }
    }

    await exportPDFReport(filename, {
      title: 'LIV分析报告',
      subtitle: `生成时间: ${new Date().toLocaleString()}`,
      metadata: {
        '文件名': filename,
        '分析时间': new Date().toLocaleString(),
        '数据点数': data?.power?.current.length.toString() || '0'
      },
      charts: exportOptions.includeCharts ? charts : undefined,
      analysis: exportOptions.includeAnalysis ? {
        thresholdCurrent: results?.livParameters?.thresholdCurrent_mA,
        slopeEfficiency: results?.livParameters?.slopeEfficiency_W_per_A,
        seriesResistance: results?.livParameters?.seriesResistance_Ohm,
        maxEfficiency: results?.livParameters?.maxEfficiency
      } : undefined
    });
  };

  const exportCompleteData = async (filename: string) => {
    const charts: Array<{ title: string; container: HTMLElement }> = [];
    
    if (exportOptions.includeCharts) {
      const livChart = document.querySelector('[data-chart="liv"]') as HTMLElement;
      if (livChart) {
        charts.push({ title: 'LIV特性曲线', container: livChart });
      }
      
      const spectrumChart = document.querySelector('[data-chart="spectrum"]') as HTMLElement;
      if (spectrumChart) {
        charts.push({ title: '光谱特性', container: spectrumChart });
      }
      
      const divergenceChart = document.querySelector('[data-chart="divergence"]') as HTMLElement;
      if (divergenceChart) {
        charts.push({ title: '发散角特性', container: divergenceChart });
      }
    }

    const exportData = {
      livData: data?.power ? {
        current: data.power.current,
        power: data.power.power,
        voltage: data.voltage?.voltage
      } : undefined,
      spectralData: data?.spectral ? {
        wavelength: data.spectral.wavelength,
        intensity: data.spectral.intensity
      } : undefined,
      divergenceData: {
        hff: data?.hff ? {
          angle: data.hff.angle,
          intensity: data.hff.intensity
        } : undefined,
        vff: data?.vff ? {
          angle: data.vff.angle,
          intensity: data.vff.intensity
        } : undefined
      },
      analysisResults: exportOptions.includeAnalysis ? {
        thresholdCurrent: results?.livParameters?.thresholdCurrent_mA,
        slopeEfficiency: results?.livParameters?.slopeEfficiency_W_per_A,
        seriesResistance: results?.livParameters?.seriesResistance_Ohm,
        maxEfficiency: results?.livParameters?.maxEfficiency
      } : undefined
    };

    await exportCompleteReport(filename, {
      title: 'LIV分析报告',
      subtitle: `生成时间: ${new Date().toLocaleString()}`,
      metadata: {
        '文件名': filename,
        '分析时间': new Date().toLocaleString(),
        '数据点数': data?.power?.current.length.toString() || '0'
      },
      data: exportData,
      charts: exportOptions.includeCharts ? charts : undefined
    });
  };

  const getFormatIcon = (format: string) => {
    switch (format) {
      case 'csv': return <Table className="w-4 h-4" />;
      case 'excel': return <Table className="w-4 h-4" />;
      case 'pdf': return <FileText className="w-4 h-4" />;
      case 'complete': return <Package className="w-4 h-4" />;
      default: return <Download className="w-4 h-4" />;
    }
  };

  const getFormatDescription = (format: string) => {
    switch (format) {
      case 'csv': return '导出为CSV格式，适合在Excel中打开';
      case 'excel': return '导出为Excel格式，包含多个工作表';
      case 'pdf': return '生成PDF报告，包含图表和分析结果';
      case 'complete': return '完整导出：PDF报告 + Excel数据';
      default: return '';
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Download className="w-5 h-5" />
          数据导出
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="export-filename">文件名</Label>
          <Input
            id="export-filename"
            value={filename}
            onChange={(e) => setFilename(e.target.value)}
            placeholder="输入文件名..."
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="export-format">导出格式</Label>
          <Select
            id="export-format"
            value={exportOptions.format}
            onChange={(e) => setExportOptions(prev => ({ 
              ...prev, 
              format: e.target.value as any 
            }))}
          >
            <option value="csv">CSV 文件</option>
            <option value="excel">Excel 文件</option>
            <option value="pdf">PDF 报告</option>
            <option value="complete">完整导出</option>
          </Select>
          <p className="text-xs text-muted-foreground">
            {getFormatDescription(exportOptions.format)}
          </p>
        </div>

        <div className="space-y-3">
          <Label>导出选项</Label>
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="include-data"
                checked={exportOptions.includeData}
                onChange={(e) => setExportOptions(prev => ({ 
                  ...prev, 
                  includeData: e.target.checked 
                }))}
              />
              <Label htmlFor="include-data">包含原始数据</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="include-analysis"
                checked={exportOptions.includeAnalysis}
                onChange={(e) => setExportOptions(prev => ({ 
                  ...prev, 
                  includeAnalysis: e.target.checked 
                }))}
              />
              <Label htmlFor="include-analysis">包含分析结果</Label>
            </div>
            {(exportOptions.format === 'pdf' || exportOptions.format === 'complete') && (
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="include-charts"
                  checked={exportOptions.includeCharts}
                  onChange={(e) => setExportOptions(prev => ({ 
                    ...prev, 
                    includeCharts: e.target.checked 
                  }))}
                />
                <Label htmlFor="include-charts">包含图表</Label>
              </div>
            )}
          </div>
        </div>

        <Button
          onClick={handleExport}
          disabled={isExporting || !data}
          className="w-full"
        >
          {isExporting ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
              导出中...
            </>
          ) : (
            <>
              {getFormatIcon(exportOptions.format)}
              <span className="ml-2">导出数据</span>
            </>
          )}
        </Button>
      </CardContent>
    </Card>
  );
}
