import { useAppStore } from '../state/store';

export default function DataPreview() {
  const { data } = useAppStore();
  if (!data) return null;

  const rows: Array<{ label: string; count: number }> = [];
  if (data.power) rows.push({ label: 'P-I 点数', count: Math.min(data.power.current.length, data.power.power.length) });
  if (data.voltage) rows.push({ label: 'V-I 点数', count: Math.min(data.voltage.current.length, data.voltage.voltage.length) });
  if (data.wavelength) rows.push({ label: '光谱 点数', count: Math.min(data.wavelength.wavelength.length, data.wavelength.intensity.length) });
  if (data.hff) rows.push({ label: 'HFF 远场 点数', count: Math.min(data.hff.angle.length, data.hff.intensity.length) });
  if (data.vff) rows.push({ label: 'VFF 远场 点数', count: Math.min(data.vff.angle.length, data.vff.intensity.length) });

  return (
    <div className="rounded border p-3 text-sm">
      <div className="font-semibold mb-2">数据预览</div>
      <ul className="space-y-1">
        {rows.map((r) => (
          <li key={r.label} className="flex justify-between">
            <span>{r.label}</span>
            <span className="text-gray-600">{r.count}</span>
          </li>
        ))}
      </ul>
    </div>
  );
}


