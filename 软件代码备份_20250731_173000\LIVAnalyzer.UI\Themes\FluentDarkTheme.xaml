<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:sys="clr-namespace:System;assembly=mscorlib">

    <!-- Fluent Design 深色主题配色方案 - 全面覆盖 ModernWpfUI 系统资源 -->
    
    <!-- ====================== 基础背景和前景色 ====================== -->
    <SolidColorBrush x:Key="SystemControlPageBackgroundAltHighBrush" Color="#1E1E1E"/>
    <SolidColorBrush x:Key="SystemControlPageBackgroundBrush" Color="#1E1E1E"/>
    <SolidColorBrush x:Key="SystemControlBackgroundBaseLowBrush" Color="#252525"/>
    <SolidColorBrush x:Key="SystemControlBackgroundBaseMediumLowBrush" Color="#2D2D30"/>
    <SolidColorBrush x:Key="SystemControlBackgroundBaseMediumBrush" Color="#333337"/>
    <SolidColorBrush x:Key="SystemControlBackgroundBaseMediumHighBrush" Color="#3A3A3D"/>
    <SolidColorBrush x:Key="SystemControlBackgroundBaseHighBrush" Color="#404044"/>
    <SolidColorBrush x:Key="SystemControlBackgroundAltLowBrush" Color="#1A1A1A"/>
    <SolidColorBrush x:Key="SystemControlBackgroundAltMediumLowBrush" Color="#252525"/>
    <SolidColorBrush x:Key="SystemControlBackgroundAltMediumBrush" Color="#2D2D30"/>
    <SolidColorBrush x:Key="SystemControlBackgroundAltMediumHighBrush" Color="#333337"/>
    <SolidColorBrush x:Key="SystemControlBackgroundAltHighBrush" Color="#3A3A3D"/>
    
    <!-- 前景色系统 -->
    <SolidColorBrush x:Key="SystemControlForegroundBaseHighBrush" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="SystemControlForegroundBaseMediumHighBrush" Color="#E5E5E5"/>
    <SolidColorBrush x:Key="SystemControlForegroundBaseMediumBrush" Color="#C8C8C8"/>
    <SolidColorBrush x:Key="SystemControlForegroundBaseMediumLowBrush" Color="#9CA3AF"/>
    <SolidColorBrush x:Key="SystemControlForegroundBaseLowBrush" Color="#6B7280"/>
    <SolidColorBrush x:Key="SystemControlForegroundAltHighBrush" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="SystemControlForegroundAltMediumHighBrush" Color="#E5E5E5"/>
    <SolidColorBrush x:Key="SystemControlForegroundAltMediumBrush" Color="#C8C8C8"/>
    
    <!-- ====================== 按钮系统资源 ====================== -->
    <SolidColorBrush x:Key="SystemControlButtonFacePointerOverBrush" Color="#404044"/>
    <SolidColorBrush x:Key="SystemControlButtonFacePressedBrush" Color="#484848"/>
    <SolidColorBrush x:Key="SystemControlButtonFaceBrush" Color="#333337"/>
    <SolidColorBrush x:Key="SystemControlButtonBorderBrush" Color="#484848"/>
    <SolidColorBrush x:Key="SystemControlButtonBorderPointerOverBrush" Color="#60A5FA"/>
    <SolidColorBrush x:Key="SystemControlButtonBorderPressedBrush" Color="#3B82F6"/>
    <SolidColorBrush x:Key="SystemControlButtonForegroundBrush" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="SystemControlButtonForegroundPointerOverBrush" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="SystemControlButtonForegroundPressedBrush" Color="#FFFFFF"/>
    
    <!-- 高亮按钮（Accent Button）-->
    <SolidColorBrush x:Key="SystemControlHighlightAccentBrush" Color="#3B82F6"/>
    <SolidColorBrush x:Key="SystemControlHighlightAltBaseBrush" Color="#404044"/>
    <SolidColorBrush x:Key="SystemControlHighlightAltAccentBrush" Color="#60A5FA"/>
    <SolidColorBrush x:Key="SystemControlHighlightBaseLowBrush" Color="#2563EB"/>
    <SolidColorBrush x:Key="SystemControlHighlightBaseMediumLowBrush" Color="#3B82F6"/>
    <SolidColorBrush x:Key="SystemControlHighlightBaseMediumBrush" Color="#60A5FA"/>
    
    <!-- ====================== 输入控件系统资源 ====================== -->
    <SolidColorBrush x:Key="SystemControlBackgroundChromeLowBrush" Color="#1E1E1E"/>
    <SolidColorBrush x:Key="SystemControlBackgroundChromeMediumLowBrush" Color="#252525"/>
    <SolidColorBrush x:Key="SystemControlBackgroundChromeMediumBrush" Color="#2D2D30"/>
    <SolidColorBrush x:Key="SystemControlBackgroundChromeHighBrush" Color="#333337"/>
    
    <!-- Chrome 系列 - AppStyles 使用的资源 -->
    <SolidColorBrush x:Key="SystemControlPageBackgroundChromeLowBrush" Color="#252525"/>
    <SolidColorBrush x:Key="SystemControlPageBackgroundChromeMediumBrush" Color="#2D2D30"/>
    <SolidColorBrush x:Key="SystemControlPageBackgroundChromeHighBrush" Color="#333337"/>
    <SolidColorBrush x:Key="SystemControlPageBackgroundAltMediumBrush" Color="#2D2D30"/>
    
    <!-- 透明色 -->
    <SolidColorBrush x:Key="SystemControlTransparentBrush" Color="Transparent"/>
    
    <!-- 边框系统 -->
    <SolidColorBrush x:Key="SystemControlBorderBaseLowBrush" Color="#3A3A3D"/>
    <SolidColorBrush x:Key="SystemControlBorderBaseMediumLowBrush" Color="#404044"/>
    <SolidColorBrush x:Key="SystemControlBorderBaseMediumBrush" Color="#484848"/>
    <SolidColorBrush x:Key="SystemControlBorderBaseMediumHighBrush" Color="#525252"/>
    <SolidColorBrush x:Key="SystemControlBorderBaseHighBrush" Color="#5C5C5F"/>
    
    <!-- TextBox 特定资源 -->
    <SolidColorBrush x:Key="SystemControlBackgroundBaseLowRevealBackgroundBrush" Color="#2D2D30"/>
    <SolidColorBrush x:Key="SystemControlBackgroundBaseMediumLowRevealBackgroundBrush" Color="#333337"/>
    <SolidColorBrush x:Key="SystemControlForegroundBaseRevealBorderBrush" Color="#484848"/>
    
    <!-- ====================== 菜单和工具栏系统资源 ====================== -->
    <SolidColorBrush x:Key="SystemControlBackgroundListLowBrush" Color="#252525"/>
    <SolidColorBrush x:Key="SystemControlBackgroundListMediumBrush" Color="#2D2D30"/>
    <SolidColorBrush x:Key="SystemControlBackgroundListMediumRevealBackgroundBrush" Color="#333337"/>
    
    <!-- MenuItem 特定资源 -->
    <SolidColorBrush x:Key="SystemControlHighlightListLowBrush" Color="#333337"/>
    <SolidColorBrush x:Key="SystemControlHighlightListMediumBrush" Color="#404044"/>
    <SolidColorBrush x:Key="SystemControlHighlightListAccentLowBrush" Color="#2563EB"/>
    <SolidColorBrush x:Key="SystemControlHighlightListAccentMediumBrush" Color="#3B82F6"/>
    
    <!-- ====================== TabView 系统资源 ====================== -->
    <SolidColorBrush x:Key="TabViewBackground" Color="#1E1E1E"/>
    <SolidColorBrush x:Key="TabViewItemHeaderBackground" Color="#252525"/>
    <SolidColorBrush x:Key="TabViewItemHeaderBackgroundSelected" Color="#333337"/>
    <SolidColorBrush x:Key="TabViewItemHeaderBackgroundPointerOver" Color="#2D2D30"/>
    <SolidColorBrush x:Key="TabViewItemHeaderBackgroundPressed" Color="#404044"/>
    <SolidColorBrush x:Key="TabViewItemHeaderForeground" Color="#C8C8C8"/>
    <SolidColorBrush x:Key="TabViewItemHeaderForegroundSelected" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="TabViewItemHeaderForegroundPointerOver" Color="#E5E5E5"/>
    <SolidColorBrush x:Key="TabViewItemHeaderForegroundPressed" Color="#FFFFFF"/>
    
    <!-- ====================== ListView/ListBox 系统资源 ====================== -->
    <SolidColorBrush x:Key="SystemControlBackgroundListBoxItemBrush" Color="Transparent"/>
    <SolidColorBrush x:Key="SystemControlBackgroundListBoxItemPointerOverBrush" Color="#333337"/>
    <SolidColorBrush x:Key="SystemControlBackgroundListBoxItemPressedBrush" Color="#404044"/>
    <SolidColorBrush x:Key="SystemControlBackgroundListBoxItemSelectedBrush" Color="#3B82F6"/>
    <SolidColorBrush x:Key="SystemControlBackgroundListBoxItemSelectedPointerOverBrush" Color="#60A5FA"/>
    <SolidColorBrush x:Key="SystemControlBackgroundListBoxItemSelectedPressedBrush" Color="#2563EB"/>
    
    <!-- ====================== ScrollBar 系统资源 ====================== -->
    <SolidColorBrush x:Key="SystemControlBackgroundScrollBarBrush" Color="#2D2D30"/>
    <SolidColorBrush x:Key="SystemControlBackgroundScrollBarThumbBrush" Color="#484848"/>
    <SolidColorBrush x:Key="SystemControlBackgroundScrollBarThumbPointerOverBrush" Color="#525252"/>
    <SolidColorBrush x:Key="SystemControlBackgroundScrollBarThumbPressedBrush" Color="#5C5C5F"/>
    
    <!-- ====================== ProgressBar 系统资源 ====================== -->
    <SolidColorBrush x:Key="SystemControlBackgroundProgressBarBrush" Color="#333337"/>
    <SolidColorBrush x:Key="SystemControlHighlightProgressBarBrush" Color="#3B82F6"/>
    <SolidColorBrush x:Key="SystemControlHighlightProgressBarIndeterminateBrush" Color="#60A5FA"/>
    
    <!-- ====================== Slider 系统资源 ====================== -->
    <SolidColorBrush x:Key="SystemControlBackgroundSliderBrush" Color="#333337"/>
    <SolidColorBrush x:Key="SystemControlBackgroundSliderThumbBrush" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="SystemControlBackgroundSliderThumbPointerOverBrush" Color="#E5E5E5"/>
    <SolidColorBrush x:Key="SystemControlBackgroundSliderThumbPressedBrush" Color="#C8C8C8"/>
    <SolidColorBrush x:Key="SystemControlHighlightSliderBrush" Color="#3B82F6"/>
    
    <!-- ====================== ComboBox 系统资源 ====================== -->
    <SolidColorBrush x:Key="SystemControlBackgroundComboBoxBrush" Color="#2D2D30"/>
    <SolidColorBrush x:Key="SystemControlBackgroundComboBoxPointerOverBrush" Color="#333337"/>
    <SolidColorBrush x:Key="SystemControlBackgroundComboBoxPressedBrush" Color="#404044"/>
    <SolidColorBrush x:Key="SystemControlBackgroundComboBoxDropDownBrush" Color="#252525"/>
    <SolidColorBrush x:Key="SystemControlBorderComboBoxBrush" Color="#484848"/>
    <SolidColorBrush x:Key="SystemControlBorderComboBoxPointerOverBrush" Color="#60A5FA"/>
    <SolidColorBrush x:Key="SystemControlBorderComboBoxPressedBrush" Color="#3B82F6"/>
    
    <!-- ====================== CheckBox/RadioButton 系统资源 ====================== -->
    <SolidColorBrush x:Key="SystemControlBackgroundCheckBoxBrush" Color="#2D2D30"/>
    <SolidColorBrush x:Key="SystemControlBackgroundCheckBoxPointerOverBrush" Color="#333337"/>
    <SolidColorBrush x:Key="SystemControlBackgroundCheckBoxPressedBrush" Color="#404044"/>
    <SolidColorBrush x:Key="SystemControlBackgroundCheckBoxCheckedBrush" Color="#3B82F6"/>
    <SolidColorBrush x:Key="SystemControlBackgroundCheckBoxCheckedPointerOverBrush" Color="#60A5FA"/>
    <SolidColorBrush x:Key="SystemControlBackgroundCheckBoxCheckedPressedBrush" Color="#2563EB"/>
    <SolidColorBrush x:Key="SystemControlBorderCheckBoxBrush" Color="#484848"/>
    <SolidColorBrush x:Key="SystemControlBorderCheckBoxPointerOverBrush" Color="#60A5FA"/>
    <SolidColorBrush x:Key="SystemControlBorderCheckBoxPressedBrush" Color="#3B82F6"/>
    <SolidColorBrush x:Key="SystemControlForegroundCheckBoxCheckMarkBrush" Color="#FFFFFF"/>
    
    <!-- ====================== ToggleSwitch 系统资源 ====================== -->
    <SolidColorBrush x:Key="SystemControlBackgroundToggleSwitchBrush" Color="#333337"/>
    <SolidColorBrush x:Key="SystemControlBackgroundToggleSwitchPointerOverBrush" Color="#404044"/>
    <SolidColorBrush x:Key="SystemControlBackgroundToggleSwitchPressedBrush" Color="#484848"/>
    <SolidColorBrush x:Key="SystemControlBackgroundToggleSwitchCheckedBrush" Color="#3B82F6"/>
    <SolidColorBrush x:Key="SystemControlBackgroundToggleSwitchCheckedPointerOverBrush" Color="#60A5FA"/>
    <SolidColorBrush x:Key="SystemControlBackgroundToggleSwitchCheckedPressedBrush" Color="#2563EB"/>
    
    <!-- ====================== Tooltip 系统资源 ====================== -->
    <SolidColorBrush x:Key="SystemControlBackgroundTooltipBrush" Color="#252525"/>
    <SolidColorBrush x:Key="SystemControlForegroundTooltipBrush" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="SystemControlBorderTooltipBrush" Color="#484848"/>
    
    <!-- ====================== ContextMenu 系统资源 ====================== -->
    <SolidColorBrush x:Key="SystemControlBackgroundContextMenuBrush" Color="#252525"/>
    <SolidColorBrush x:Key="SystemControlBorderContextMenuBrush" Color="#484848"/>
    <SolidColorBrush x:Key="SystemControlBackgroundContextMenuItemBrush" Color="Transparent"/>
    <SolidColorBrush x:Key="SystemControlBackgroundContextMenuItemPointerOverBrush" Color="#333337"/>
    <SolidColorBrush x:Key="SystemControlBackgroundContextMenuItemPressedBrush" Color="#404044"/>
    
    <!-- ====================== ModernWpfUI Menu 系统资源 ====================== -->
    <!-- Menu 主菜单栏资源 - 使用 ModernWpfUI 的实际资源键 -->
    <SolidColorBrush x:Key="MenuBackgroundBrush" Color="#1E1E1E"/>
    <SolidColorBrush x:Key="MenuForegroundBrush" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="MenuBorderBrush" Color="#484848"/>
    
    <!-- MenuItem 基础资源 -->
    <SolidColorBrush x:Key="MenuItemBackgroundBrush" Color="Transparent"/>
    <SolidColorBrush x:Key="MenuItemForegroundBrush" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="MenuItemBorderBrush" Color="Transparent"/>
    
    <!-- MenuItem 交互状态 -->
    <SolidColorBrush x:Key="MenuItemBackgroundPointerOverBrush" Color="#333337"/>
    <SolidColorBrush x:Key="MenuItemForegroundPointerOverBrush" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="MenuItemBorderPointerOverBrush" Color="#484848"/>
    
    <SolidColorBrush x:Key="MenuItemBackgroundPressedBrush" Color="#404044"/>
    <SolidColorBrush x:Key="MenuItemForegroundPressedBrush" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="MenuItemBorderPressedBrush" Color="#484848"/>
    
    <SolidColorBrush x:Key="MenuItemBackgroundSelectedBrush" Color="#333337"/>
    <SolidColorBrush x:Key="MenuItemForegroundSelectedBrush" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="MenuItemBorderSelectedBrush" Color="#484848"/>
    
    <!-- MenuItem 禁用状态 -->
    <SolidColorBrush x:Key="MenuItemBackgroundDisabledBrush" Color="Transparent"/>
    <SolidColorBrush x:Key="MenuItemForegroundDisabledBrush" Color="#6B7280"/>
    <SolidColorBrush x:Key="MenuItemBorderDisabledBrush" Color="Transparent"/>
    
    <!-- 下拉菜单 (Popup) 资源 -->
    <SolidColorBrush x:Key="MenuFlyoutBackgroundBrush" Color="#252525"/>
    <SolidColorBrush x:Key="MenuFlyoutBorderBrush" Color="#484848"/>
    
    <!-- 分隔符资源 -->
    <SolidColorBrush x:Key="MenuFlyoutSeparatorBrush" Color="#484848"/>
    
    <!-- 传统 WPF Menu 资源键覆盖 -->
    <SolidColorBrush x:Key="{x:Static SystemColors.MenuBrushKey}" Color="#1E1E1E"/>
    <SolidColorBrush x:Key="{x:Static SystemColors.MenuTextBrushKey}" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="{x:Static SystemColors.MenuHighlightBrushKey}" Color="#333337"/>
    <SolidColorBrush x:Key="{x:Static SystemColors.MenuBarBrushKey}" Color="#1E1E1E"/>
    
    <!-- 更多可能的下拉菜单背景资源键 -->
    <SolidColorBrush x:Key="MenuPopupBrush" Color="#252525"/>
    <SolidColorBrush x:Key="MenuPopupBackgroundBrush" Color="#252525"/>
    <SolidColorBrush x:Key="ContextMenuBackgroundBrush" Color="#252525"/>
    <SolidColorBrush x:Key="PopupBackgroundBrush" Color="#252525"/>
    
    <!-- ModernWpfUI 可能使用的菜单资源 -->
    <SolidColorBrush x:Key="MenuItemPopupBackgroundBrush" Color="#252525"/>
    <SolidColorBrush x:Key="MenuItemSubmenuBackgroundBrush" Color="#252525"/>
    <SolidColorBrush x:Key="TopLevelMenuBackgroundBrush" Color="#252525"/>
    <SolidColorBrush x:Key="SubMenuBackgroundBrush" Color="#252525"/>
    
    <!-- ====================== 特殊效果和阴影 ====================== -->
    <SolidColorBrush x:Key="SystemControlTransientBorderBrush" Color="#60A5FA"/>
    <SolidColorBrush x:Key="SystemControlRevealBorderBrush" Color="#484848"/>
    <SolidColorBrush x:Key="SystemControlRevealBackgroundBrush" Color="#2D2D30"/>
    
    <!-- ====================== Fluent 深色主题专用资源 ====================== -->
    <!-- 主要背景色 -->
    <SolidColorBrush x:Key="FluentAppBackgroundBrush" Color="#1E1E1E"/>
    <SolidColorBrush x:Key="FluentPageBackgroundBrush" Color="#252525"/>
    <SolidColorBrush x:Key="FluentCardBackgroundBrush" Color="#2D2D30"/>
    <SolidColorBrush x:Key="FluentSurfaceBackgroundBrush" Color="#333337"/>
    
    <!-- 文字颜色 -->
    <SolidColorBrush x:Key="FluentPrimaryTextBrush" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="FluentSecondaryTextBrush" Color="#C8C8C8"/>
    <SolidColorBrush x:Key="FluentTertiaryTextBrush" Color="#9CA3AF"/>
    <SolidColorBrush x:Key="FluentDisabledTextBrush" Color="#6B7280"/>
    
    <!-- 边框颜色 -->
    <SolidColorBrush x:Key="FluentBorderBrush" Color="#484848"/>
    <SolidColorBrush x:Key="FluentDividerBrush" Color="#3F3F46"/>
    <SolidColorBrush x:Key="FluentFocusBorderBrush" Color="#60A5FA"/>
    
    <!-- 按钮颜色 -->
    <SolidColorBrush x:Key="FluentButtonBackgroundBrush" Color="#3F3F46"/>
    <SolidColorBrush x:Key="FluentButtonHoverBackgroundBrush" Color="#4C4F69"/>
    <SolidColorBrush x:Key="FluentButtonPressedBackgroundBrush" Color="#525252"/>
    
    <!-- 主色调按钮 -->
    <SolidColorBrush x:Key="FluentAccentButtonBackgroundBrush" Color="#3B82F6"/>
    <SolidColorBrush x:Key="FluentAccentButtonHoverBackgroundBrush" Color="#2563EB"/>
    <SolidColorBrush x:Key="FluentAccentButtonPressedBackgroundBrush" Color="#1D4ED8"/>
    <SolidColorBrush x:Key="FluentAccentButtonTextBrush" Color="#FFFFFF"/>
    
    <!-- 输入控件颜色 -->
    <SolidColorBrush x:Key="FluentTextBoxBackgroundBrush" Color="#2D2D30"/>
    <SolidColorBrush x:Key="FluentTextBoxBorderBrush" Color="#484848"/>
    <SolidColorBrush x:Key="FluentTextBoxFocusBorderBrush" Color="#60A5FA"/>
    
    <!-- 图表配色方案 - 深色主题优化（更亮的颜色以增强对比度）-->
    <x:Array x:Key="FluentDarkChartColors" Type="Color">
        <Color>#60A5FA</Color>  <!-- 亮蓝色 -->
        <Color>#34D399</Color>  <!-- 绿色 -->
        <Color>#FBBF24</Color>  <!-- 黄色 -->
        <Color>#F87171</Color>  <!-- 粉红色 -->
        <Color>#A78BFA</Color>  <!-- 紫色 -->
        <Color>#FB7185</Color>  <!-- 玫瑰色 -->
        <Color>#06B6D4</Color>  <!-- 青色 -->
        <Color>#10B981</Color>  <!-- 翠绿色 -->
        <Color>#F59E0B</Color>  <!-- 琥珀色 -->
        <Color>#EF4444</Color>  <!-- 红色 -->
    </x:Array>
    
    <!-- 图表背景和网格 -->
    <SolidColorBrush x:Key="FluentChartBackgroundBrush" Color="#1E1E1E"/>
    <SolidColorBrush x:Key="FluentChartGridBrush" Color="#374151"/>
    <SolidColorBrush x:Key="FluentChartAxisBrush" Color="#9CA3AF"/>
    <SolidColorBrush x:Key="FluentChartTextBrush" Color="#F3F4F6"/>
    
    <!-- 状态颜色 - 深色主题适配 -->
    <SolidColorBrush x:Key="FluentSuccessBrush" Color="#22C55E"/>
    <SolidColorBrush x:Key="FluentWarningBrush" Color="#F59E0B"/>
    <SolidColorBrush x:Key="FluentErrorBrush" Color="#EF4444"/>
    <SolidColorBrush x:Key="FluentInfoBrush" Color="#3B82F6"/>
    
    <!-- ====================== 布局和样式通用资源 ====================== -->
    <sys:Double x:Key="FluentCardCornerRadius">8</sys:Double>
    <Thickness x:Key="FluentCardPadding">16</Thickness>
    <Thickness x:Key="FluentCardMargin">8</Thickness>
    <sys:Double x:Key="FluentControlHeight">32</sys:Double>
    <sys:Double x:Key="FluentControlCornerRadius">4</sys:Double>
    <Thickness x:Key="FluentControlPadding">12,6</Thickness>
    <Thickness x:Key="FluentButtonPadding">16,8</Thickness>
    <Thickness x:Key="FluentBorderThickness">1</Thickness>
    
    <!-- 卡片样式定义 - 深色主题版本 -->
    <Style x:Key="FluentCardStyle" TargetType="Border">
        <Setter Property="Background" Value="{StaticResource FluentCardBackgroundBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource FluentBorderBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="CornerRadius" Value="{StaticResource FluentCardCornerRadius}"/>
        <Setter Property="Padding" Value="{StaticResource FluentCardPadding}"/>
        <Setter Property="Margin" Value="{StaticResource FluentCardMargin}"/>
        <Setter Property="Effect">
            <Setter.Value>
                <!-- 深色主题使用更柔和的阴影 -->
                <DropShadowEffect BlurRadius="20" 
                                  ShadowDepth="6" 
                                  Opacity="0.25" 
                                  Direction="270"
                                  Color="#000000"/>
            </Setter.Value>
        </Setter>
    </Style>
    
    <!-- ModernWpfUI 基础样式覆盖 -->
    <Style x:Key="BaseTextBlockStyle" TargetType="TextBlock">
        <Setter Property="Foreground" Value="{StaticResource SystemControlForegroundBaseHighBrush}"/>
        <Setter Property="FontFamily" Value="Segoe UI"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="TextWrapping" Value="NoWrap"/>
        <Setter Property="TextTrimming" Value="None"/>
        <Setter Property="TextAlignment" Value="Left"/>
        <Setter Property="LineHeight" Value="20"/>
        <Setter Property="LineStackingStrategy" Value="MaxHeight"/>
    </Style>
    
    <Style x:Key="CaptionTextBlockStyle" TargetType="TextBlock" BasedOn="{StaticResource BaseTextBlockStyle}">
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="LineHeight" Value="16"/>
    </Style>
    
    <Style x:Key="AccentButtonStyle" TargetType="Button">
        <Setter Property="Foreground" Value="{StaticResource SystemControlForegroundBaseHighBrush}"/>
        <Setter Property="Background" Value="{StaticResource SystemControlHighlightAccentBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource SystemControlHighlightAccentBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Padding" Value="8,5,8,6"/>
        <Setter Property="HorizontalAlignment" Value="Left"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
        <Setter Property="FontFamily" Value="Segoe UI"/>
        <Setter Property="FontWeight" Value="Normal"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border x:Name="Background"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="2">
                        <ContentPresenter x:Name="ContentPresenter"
                                        Content="{TemplateBinding Content}"
                                        ContentTemplate="{TemplateBinding ContentTemplate}"
                                        Margin="{TemplateBinding Padding}"
                                        HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                        VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="Background" Property="Background" Value="{StaticResource SystemControlHighlightAltAccentBrush}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="Background" Property="Background" Value="{StaticResource SystemControlHighlightBaseLowBrush}"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="Background" Property="Background" Value="{StaticResource SystemControlBackgroundBaseLowBrush}"/>
                            <Setter Property="Foreground" Value="{StaticResource SystemControlForegroundBaseLowBrush}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    
</ResourceDictionary>