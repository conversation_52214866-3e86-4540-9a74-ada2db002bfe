import { useAppStore } from '../state/store';

export default function SpectrumControls() {
  const { displayConfig, updateDisplayConfig } = useAppStore();
  return (
    <div className="flex items-center gap-4 text-sm">
      <label className="flex items-center gap-1">
        <input type="checkbox" checked={displayConfig.showSpectrumLine ?? true}
          onChange={(e) => updateDisplayConfig({ showSpectrumLine: e.target.checked })} /> 光谱
      </label>
    </div>
  );
}


