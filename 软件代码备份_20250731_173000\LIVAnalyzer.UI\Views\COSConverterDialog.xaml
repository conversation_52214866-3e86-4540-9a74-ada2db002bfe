<Window x:Class="LIVAnalyzer.UI.Views.COSConverterDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="COS文件转换工具"
        Height="600" Width="700"
        MinHeight="500" MinWidth="600"
        WindowStartupLocation="CenterOwner"
        Icon="pack://application:,,,/Resources/Icons/app-icon-32.png">

    <Window.Resources>
        <BooleanToVisibilityConverter x:Key="BoolToVisConverter"/>

        <!-- 全局样式已在App.xaml中定义，这里不需要重复 -->
    </Window.Resources>
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- 标题 -->
        <TextBlock Grid.Row="0" Text="COS文件转换工具"
                   FontSize="20" FontWeight="Bold"
                   Foreground="{DynamicResource AppForegroundBrush}"
                   HorizontalAlignment="Center" Margin="0,0,0,10"/>

        <!-- 说明文本 -->
        <TextBlock Grid.Row="1" TextWrapping="Wrap"
                   HorizontalAlignment="Center" Margin="0,0,0,20"
                   FontSize="14"
                   Foreground="{DynamicResource AppForegroundBrush}"
                   Opacity="0.8">
            此工具用于将COS_Test_LIV_Data.txt文件转换为Excel格式，<LineBreak/>
            以便在LIV分析工具中使用。转换后的文件将包含完整的LIV和光谱数据。
        </TextBlock>
        
        <!-- 源文件夹设置 -->
        <Expander Grid.Row="2" Header="源文件夹设置" IsExpanded="True" Margin="0,0,0,10">
            <StackPanel Margin="10">
                <!-- 源文件夹选择 -->
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBlock Grid.Column="0" Text="源文件夹:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    
                    <TextBox Grid.Column="1" Text="{Binding SourceFolder}" 
                             IsReadOnly="True" VerticalAlignment="Center"/>
                    
                    <Button Grid.Column="2" Content="浏览..." 
                            Command="{Binding BrowseSourceFolderCommand}"
                            Style="{DynamicResource WhiteButton}"
                            Margin="10,0,0,0"/>
                </Grid>
                
                <!-- 递归搜索选项 -->
                <CheckBox Content="递归搜索子文件夹" 
                          IsChecked="{Binding RecursiveSearch}"
                          Margin="0,10,0,0"
                          ToolTip="如果勾选，将搜索所有子文件夹中的COS文件"/>
            </StackPanel>
        </Expander>
        
        <!-- 输出设置 -->
        <Expander Grid.Row="3" Header="输出设置" IsExpanded="True" Margin="0,0,0,10">
            <StackPanel Margin="10">
                <!-- 输出文件夹选择 -->
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBlock Grid.Column="0" Text="输出文件夹:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    
                    <TextBox Grid.Column="1" Text="{Binding OutputFolder}" 
                             IsReadOnly="True" VerticalAlignment="Center"
                             IsEnabled="{Binding IsOutputFolderEnabled}"/>
                    
                    <Button Grid.Column="2" Content="浏览..." 
                            Command="{Binding BrowseOutputFolderCommand}"
                            IsEnabled="{Binding IsOutputFolderEnabled}"
                            Style="{DynamicResource WhiteButton}"
                            Margin="10,0,0,0"/>
                </Grid>
                
                <!-- 使用源文件夹作为输出文件夹的选项 -->
                <CheckBox Content="使用源文件夹作为输出文件夹" 
                          IsChecked="{Binding UseSameFolder}"
                          Margin="0,10,0,0"/>
            </StackPanel>
        </Expander>
        
        <!-- 转换日志 -->
        <Expander Grid.Row="4" Header="转换日志" IsExpanded="True">
            <ScrollViewer Margin="10" VerticalScrollBarVisibility="Auto">
                <TextBox Text="{Binding LogText}" 
                         IsReadOnly="True" 
                         TextWrapping="Wrap"
                         VerticalScrollBarVisibility="Auto"
                         FontFamily="Cascadia Code, Consolas"
                         Background="{DynamicResource AppControlBackgroundBrush}"
                         Foreground="{DynamicResource AppForegroundBrush}"
                         BorderBrush="{DynamicResource AppBorderBrush}"
                         BorderThickness="1"
                         AcceptsReturn="True"/>
            </ScrollViewer>
        </Expander>
        
        <!-- 按钮组 -->
        <Grid Grid.Row="5" Margin="0,20,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <!-- 进度指示器 -->
            <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center"
                        Visibility="{Binding IsConverting, Converter={StaticResource BoolToVisConverter}}">
                <ProgressBar IsIndeterminate="True" Width="20" Height="20" Margin="0,0,10,0"/>
                <TextBlock Text="{Binding ConversionStatus}" VerticalAlignment="Center"/>
            </StackPanel>
            
            <Button Grid.Column="1" Content="{Binding ConvertButtonText}" 
                    Command="{Binding StartConversionCommand}"
                    IsEnabled="{Binding CanStartConversion}"
                    Style="{DynamicResource AccentButtonStyle}"
                    Margin="0,0,10,0"/>
            
            <Button Grid.Column="2" Content="关闭" 
                    Style="{DynamicResource WhiteButton}"
                    Command="{Binding CloseCommand}"/>
        </Grid>
        
        <!-- 进度对话框覆盖层 -->
        <Grid Grid.RowSpan="6" Background="{DynamicResource AppBackgroundBrush}" 
              Opacity="0.8"
              Visibility="{Binding ShowProgressDialog, Converter={StaticResource BoolToVisConverter}}">
            <Border Background="{DynamicResource AppControlBackgroundBrush}" 
                    Width="300" Height="150"
                    HorizontalAlignment="Center" VerticalAlignment="Center"
                    BorderBrush="{DynamicResource AppBorderBrush}"
                    BorderThickness="1"
                    CornerRadius="8">
                <StackPanel Margin="20">
                    <TextBlock Text="正在转换文件..." 
                              FontSize="16" FontWeight="SemiBold"
                              Foreground="{DynamicResource AppForegroundBrush}"
                              HorizontalAlignment="Center" 
                              Margin="0,0,0,10"/>
                    <ProgressBar Value="{Binding ConversionProgress}" Maximum="100" Margin="0,0,0,10"/>
                    <TextBlock Text="{Binding ConversionProgressText}" 
                              HorizontalAlignment="Center" 
                              FontSize="14"
                              Foreground="{DynamicResource AppForegroundBrush}"
                              Margin="0,0,0,10"/>
                    <Button Content="取消" 
                           Command="{Binding CancelConversionCommand}" 
                           Style="{DynamicResource WhiteButton}"
                           HorizontalAlignment="Center"/>
                </StackPanel>
            </Border>
        </Grid>
    </Grid>
</Window>