import { useEffect, useRef } from 'react';
let Plot: any;
import { useAppStore } from '../../state/store';
import { movingAverage, savitzkyGolay, gaussianSmooth, butterworthLowpass } from '../../services/smoothing';
import ChartToolbar from "../ChartToolbar";

export default function DivergenceChart() {
  const ref = useRef<HTMLDivElement | null>(null);
  const { data } = useAppStore();

  useEffect(() => {
    if (!ref.current) return;
    ref.current.innerHTML = '';
    if (!Plot) {
      import('@observablehq/plot').then(mod => { Plot = mod; render(); });
      return;
    }
    render();

  }, [data]);

  function render() {
    if (!ref.current || !Plot) return;
    const marks: any[] = [];

    if ((useAppStore.getState().displayConfig.showHFF ?? true) && data?.hff && data.hff.angle.length && data.hff.intensity.length) {
      const cfg = useAppStore.getState().processingConfig;
      let y = data.hff.intensity.slice();
      if (cfg.smoothing?.enabled) {
        if (cfg.smoothing.method === 'moving-average') y = movingAverage(y, cfg.smoothing.windowSize ?? 5);
        else if (cfg.smoothing.method === 'sg') y = savitzkyGolay(y, cfg.smoothing.windowSize ?? 5, cfg.smoothing.polynomialOrder ?? 3);
        else if (cfg.smoothing.method === 'gaussian') y = gaussianSmooth(y, cfg.smoothing.windowSize ?? 5);
        else if (cfg.smoothing.method === 'butterworth') y = butterworthLowpass(y, cfg.smoothing.cutoff ?? 0.15);
      }
      const h = data.hff.angle.map((x, i) => ({ x, y: y[i], s: 'HFF' }));
      marks.push(
        Plot.line(h, { x: 'x', y: 'y', stroke: 'seagreen', title: 'HFF' }),
        Plot.dot(h, { x: 'x', y: 'y', r: 2, fill: 'seagreen', title: (d: any) => `θ=${d.x}\nI=${d.y}` })
      );
    }

    if ((useAppStore.getState().displayConfig.showVFF ?? true) && data?.vff && data.vff.angle.length && data.vff.intensity.length) {
      const cfg = useAppStore.getState().processingConfig;
      let y = data.vff.intensity.slice();
      if (cfg.smoothing?.enabled) {
        if (cfg.smoothing.method === 'moving-average') y = movingAverage(y, cfg.smoothing.windowSize ?? 5);
        else if (cfg.smoothing.method === 'sg') y = savitzkyGolay(y, cfg.smoothing.windowSize ?? 5, cfg.smoothing.polynomialOrder ?? 3);
        else if (cfg.smoothing.method === 'gaussian') y = gaussianSmooth(y, cfg.smoothing.windowSize ?? 5);
        else if (cfg.smoothing.method === 'butterworth') y = butterworthLowpass(y, cfg.smoothing.cutoff ?? 0.15);
      }
      const v = data.vff.angle.map((x, i) => ({ x, y: y[i], s: 'VFF' }));
      marks.push(
        Plot.line(v, { x: 'x', y: 'y', stroke: 'orange', title: 'VFF' }),
        Plot.dot(v, { x: 'x', y: 'y', r: 2, fill: 'orange', title: (d: any) => `θ=${d.x}\nI=${d.y}` })
      );
    }

    if (marks.length === 0) {
      ref.current.textContent = '暂无发散角数据';
      return;
    }

    const plot = Plot.plot({
      marginLeft: 50,
      marginBottom: 40,
      style: { background: 'transparent' },
      x: { label: 'Angle (deg)' },
      y: { label: 'Intensity' },
      marks,
    });

    ref.current.appendChild(plot);
    import('../../services/zoom').then(z => { (window as any).__div_zoom = z.enableZoomPan(ref.current!); });
    return () => plot.remove();
  }

  return (
    <div className="space-y-2">
      <ChartToolbar title="Divergence_chart" container={ref.current} onReset={() => (window as any).__div_zoom?.then((r: any) => r.reset())} />
      <div ref={ref} className="w-full overflow-auto" />
    </div>
  );
}


