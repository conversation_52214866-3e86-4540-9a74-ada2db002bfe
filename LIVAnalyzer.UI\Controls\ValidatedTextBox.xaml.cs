using System;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using LIVAnalyzer.UI.Validation;

namespace LIVAnalyzer.UI.Controls
{
    /// <summary>
    /// 带验证功能的TextBox控件
    /// </summary>
    public partial class ValidatedTextBox : UserControl
    {
        #region Dependency Properties

        /// <summary>
        /// 文本内容依赖属性
        /// </summary>
        public static readonly DependencyProperty TextProperty =
            DependencyProperty.Register(nameof(Text), typeof(string), typeof(ValidatedTextBox),
                new FrameworkPropertyMetadata(string.Empty, FrameworkPropertyMetadataOptions.BindsTwoWayByDefault, OnTextChanged));

        /// <summary>
        /// 标签文本依赖属性
        /// </summary>
        public static readonly DependencyProperty LabelProperty =
            DependencyProperty.Register(nameof(Label), typeof(string), typeof(ValidatedTextBox),
                new PropertyMetadata(string.Empty));

        /// <summary>
        /// 占位符文本依赖属性
        /// </summary>
        public static readonly DependencyProperty PlaceholderTextProperty =
            DependencyProperty.Register(nameof(PlaceholderText), typeof(string), typeof(ValidatedTextBox),
                new PropertyMetadata(string.Empty));

        /// <summary>
        /// 单位文本依赖属性
        /// </summary>
        public static readonly DependencyProperty UnitProperty =
            DependencyProperty.Register(nameof(Unit), typeof(string), typeof(ValidatedTextBox),
                new PropertyMetadata(string.Empty));

        /// <summary>
        /// 是否必填依赖属性
        /// </summary>
        public static readonly DependencyProperty IsRequiredProperty =
            DependencyProperty.Register(nameof(IsRequired), typeof(bool), typeof(ValidatedTextBox),
                new PropertyMetadata(false));

        /// <summary>
        /// 是否显示标签依赖属性
        /// </summary>
        public static readonly DependencyProperty ShowLabelProperty =
            DependencyProperty.Register(nameof(ShowLabel), typeof(bool), typeof(ValidatedTextBox),
                new PropertyMetadata(true));

        /// <summary>
        /// 是否显示帮助按钮依赖属性
        /// </summary>
        public static readonly DependencyProperty ShowHelpButtonProperty =
            DependencyProperty.Register(nameof(ShowHelpButton), typeof(bool), typeof(ValidatedTextBox),
                new PropertyMetadata(false));

        /// <summary>
        /// 帮助文本依赖属性
        /// </summary>
        public static readonly DependencyProperty HelpTextProperty =
            DependencyProperty.Register(nameof(HelpText), typeof(string), typeof(ValidatedTextBox),
                new PropertyMetadata(string.Empty));

        /// <summary>
        /// 最大长度依赖属性
        /// </summary>
        public static readonly DependencyProperty MaxLengthProperty =
            DependencyProperty.Register(nameof(MaxLength), typeof(int), typeof(ValidatedTextBox),
                new PropertyMetadata(0));

        /// <summary>
        /// 验证目标对象依赖属性
        /// </summary>
        public static readonly DependencyProperty ValidationTargetProperty =
            DependencyProperty.Register(nameof(ValidationTarget), typeof(object), typeof(ValidatedTextBox),
                new PropertyMetadata(null));

        /// <summary>
        /// 验证属性名依赖属性
        /// </summary>
        public static readonly DependencyProperty ValidationPropertyNameProperty =
            DependencyProperty.Register(nameof(ValidationPropertyName), typeof(string), typeof(ValidatedTextBox),
                new PropertyMetadata(string.Empty));

        /// <summary>
        /// 是否启用实时验证依赖属性
        /// </summary>
        public static readonly DependencyProperty EnableRealTimeValidationProperty =
            DependencyProperty.Register(nameof(EnableRealTimeValidation), typeof(bool), typeof(ValidatedTextBox),
                new PropertyMetadata(true));

        /// <summary>
        /// 验证延迟依赖属性（毫秒）
        /// </summary>
        public static readonly DependencyProperty ValidationDelayProperty =
            DependencyProperty.Register(nameof(ValidationDelay), typeof(int), typeof(ValidatedTextBox),
                new PropertyMetadata(300));

        /// <summary>
        /// 是否有验证错误依赖属性
        /// </summary>
        public static readonly DependencyProperty HasValidationErrorProperty =
            DependencyProperty.Register(nameof(HasValidationError), typeof(bool), typeof(ValidatedTextBox),
                new PropertyMetadata(false));

        /// <summary>
        /// 验证错误消息依赖属性
        /// </summary>
        public static readonly DependencyProperty ValidationErrorMessageProperty =
            DependencyProperty.Register(nameof(ValidationErrorMessage), typeof(string), typeof(ValidatedTextBox),
                new PropertyMetadata(string.Empty, OnValidationErrorMessageChanged));

        #endregion

        #region Properties

        /// <summary>
        /// 文本内容
        /// </summary>
        public string Text
        {
            get => (string)GetValue(TextProperty);
            set => SetValue(TextProperty, value);
        }

        /// <summary>
        /// 标签文本
        /// </summary>
        public string Label
        {
            get => (string)GetValue(LabelProperty);
            set => SetValue(LabelProperty, value);
        }

        /// <summary>
        /// 占位符文本
        /// </summary>
        public string PlaceholderText
        {
            get => (string)GetValue(PlaceholderTextProperty);
            set => SetValue(PlaceholderTextProperty, value);
        }

        /// <summary>
        /// 单位文本
        /// </summary>
        public string Unit
        {
            get => (string)GetValue(UnitProperty);
            set => SetValue(UnitProperty, value);
        }

        /// <summary>
        /// 是否必填
        /// </summary>
        public bool IsRequired
        {
            get => (bool)GetValue(IsRequiredProperty);
            set => SetValue(IsRequiredProperty, value);
        }

        /// <summary>
        /// 是否显示标签
        /// </summary>
        public bool ShowLabel
        {
            get => (bool)GetValue(ShowLabelProperty);
            set => SetValue(ShowLabelProperty, value);
        }

        /// <summary>
        /// 是否显示帮助按钮
        /// </summary>
        public bool ShowHelpButton
        {
            get => (bool)GetValue(ShowHelpButtonProperty);
            set => SetValue(ShowHelpButtonProperty, value);
        }

        /// <summary>
        /// 帮助文本
        /// </summary>
        public string HelpText
        {
            get => (string)GetValue(HelpTextProperty);
            set => SetValue(HelpTextProperty, value);
        }

        /// <summary>
        /// 最大长度
        /// </summary>
        public int MaxLength
        {
            get => (int)GetValue(MaxLengthProperty);
            set => SetValue(MaxLengthProperty, value);
        }

        /// <summary>
        /// 验证目标对象
        /// </summary>
        public object ValidationTarget
        {
            get => GetValue(ValidationTargetProperty);
            set => SetValue(ValidationTargetProperty, value);
        }

        /// <summary>
        /// 验证属性名
        /// </summary>
        public string ValidationPropertyName
        {
            get => (string)GetValue(ValidationPropertyNameProperty);
            set => SetValue(ValidationPropertyNameProperty, value);
        }

        /// <summary>
        /// 是否启用实时验证
        /// </summary>
        public bool EnableRealTimeValidation
        {
            get => (bool)GetValue(EnableRealTimeValidationProperty);
            set => SetValue(EnableRealTimeValidationProperty, value);
        }

        /// <summary>
        /// 验证延迟（毫秒）
        /// </summary>
        public int ValidationDelay
        {
            get => (int)GetValue(ValidationDelayProperty);
            set => SetValue(ValidationDelayProperty, value);
        }

        /// <summary>
        /// 是否有验证错误
        /// </summary>
        public bool HasValidationError
        {
            get => (bool)GetValue(HasValidationErrorProperty);
            set => SetValue(HasValidationErrorProperty, value);
        }

        /// <summary>
        /// 验证错误消息
        /// </summary>
        public string ValidationErrorMessage
        {
            get => (string)GetValue(ValidationErrorMessageProperty);
            set => SetValue(ValidationErrorMessageProperty, value);
        }

        #endregion

        #region Events

        /// <summary>
        /// 验证完成事件
        /// </summary>
        public event EventHandler<LIVAnalyzer.UI.Validation.ValidationResult>? ValidationCompleted;

        /// <summary>
        /// 文本变化事件
        /// </summary>
        public event EventHandler<string>? TextChanged;

        #endregion

        #region Fields

        private readonly ValidationService _validationService;
        private bool _isInternalUpdate = false;

        #endregion

        #region Constructor

        public ValidatedTextBox()
        {
            InitializeComponent();
            _validationService = ValidationServiceInstance.Instance;
            _validationService.ValidationCompleted += OnValidationServiceCompleted;
            
            Loaded += OnLoaded;
            Unloaded += OnUnloaded;
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// 立即验证当前值
        /// </summary>
        /// <returns>验证结果</returns>
        public LIVAnalyzer.UI.Validation.ValidationResult ValidateNow()
        {
            if (ValidationTarget != null && !string.IsNullOrEmpty(ValidationPropertyName))
            {
                var result = _validationService.ValidateProperty(ValidationTarget, ValidationPropertyName, Text);
                UpdateValidationState(result);
                return result;
            }

            return new LIVAnalyzer.UI.Validation.ValidationResult { IsValid = true };
        }

        /// <summary>
        /// 清除验证错误
        /// </summary>
        public void ClearValidationError()
        {
            HasValidationError = false;
            ValidationErrorMessage = string.Empty;
            ErrorDisplay.HideMessage();
        }

        /// <summary>
        /// 设置验证错误
        /// </summary>
        /// <param name="errorMessage">错误消息</param>
        public void SetValidationError(string errorMessage)
        {
            HasValidationError = !string.IsNullOrWhiteSpace(errorMessage);
            ValidationErrorMessage = errorMessage ?? string.Empty;
            
            if (HasValidationError)
            {
                ErrorDisplay.ShowMessage(ValidationErrorMessage, MessageType.Error);
            }
            else
            {
                ErrorDisplay.HideMessage();
            }
        }

        /// <summary>
        /// 聚焦到输入框
        /// </summary>
        public new void Focus()
        {
            InputTextBox?.Focus();
        }

        #endregion

        #region Event Handlers

        private void OnLoaded(object sender, RoutedEventArgs e)
        {
            // 初始验证
            if (EnableRealTimeValidation && ValidationTarget != null && !string.IsNullOrEmpty(ValidationPropertyName))
            {
                ValidateNow();
            }
        }

        private void OnUnloaded(object sender, RoutedEventArgs e)
        {
            _validationService.ValidationCompleted -= OnValidationServiceCompleted;
        }

        private void InputTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (_isInternalUpdate) return;

            var newText = InputTextBox.Text;
            
            // 更新绑定的文本属性
            _isInternalUpdate = true;
            Text = newText;
            _isInternalUpdate = false;

            // 触发文本变化事件
            TextChanged?.Invoke(this, newText);

            // 实时验证
            if (EnableRealTimeValidation && ValidationTarget != null && !string.IsNullOrEmpty(ValidationPropertyName))
            {
                _validationService.ValidationDelay = ValidationDelay;
                _validationService.ValidatePropertyAsync(ValidationTarget, ValidationPropertyName, newText);
            }
        }

        private void InputTextBox_LostFocus(object sender, RoutedEventArgs e)
        {
            // 失焦时立即验证
            if (ValidationTarget != null && !string.IsNullOrEmpty(ValidationPropertyName))
            {
                ValidateNow();
            }
        }

        private void InputTextBox_GotFocus(object sender, RoutedEventArgs e)
        {
            // 聚焦时清除之前的验证错误（可选）
            // ClearValidationError();
        }

        private void OnValidationServiceCompleted(object? sender, ValidationCompletedEventArgs e)
        {
            // 检查是否是针对当前控件的验证
            if (e.Instance == ValidationTarget && e.PropertyName == ValidationPropertyName)
            {
                Dispatcher.BeginInvoke(() =>
                {
                    UpdateValidationState(e.Result);
                    ValidationCompleted?.Invoke(this, e.Result);
                });
            }
        }

        #endregion

        #region Property Changed Handlers

        private static void OnTextChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is ValidatedTextBox control && !control._isInternalUpdate)
            {
                control._isInternalUpdate = true;
                if (control.InputTextBox != null)
                {
                    control.InputTextBox.Text = e.NewValue as string ?? string.Empty;
                }
                control._isInternalUpdate = false;
            }
        }

        private static void OnValidationErrorMessageChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is ValidatedTextBox control)
            {
                var errorMessage = e.NewValue as string ?? string.Empty;
                control.HasValidationError = !string.IsNullOrWhiteSpace(errorMessage);
                
                if (control.HasValidationError)
                {
                    control.ErrorDisplay.ShowMessage(errorMessage, MessageType.Error);
                }
                else
                {
                    control.ErrorDisplay.HideMessage();
                }
            }
        }

        #endregion

        #region Private Methods

        private void UpdateValidationState(LIVAnalyzer.UI.Validation.ValidationResult result)
        {
            HasValidationError = !result.IsValid;
            ValidationErrorMessage = result.FirstErrorMessage;
            
            if (HasValidationError)
            {
                ErrorDisplay.ShowMessage(ValidationErrorMessage, MessageType.Error);
            }
            else
            {
                ErrorDisplay.HideMessage();
            }
        }

        #endregion
    }
}