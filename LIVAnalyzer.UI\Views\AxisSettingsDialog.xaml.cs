using LIVAnalyzer.UI.ViewModels;
using OxyPlot.Axes;
using System.Windows;
using System.Windows.Input;
using System.Windows.Controls;

namespace LIVAnalyzer.UI.Views
{
    /// <summary>
    /// 坐标轴设置对话框
    /// </summary>
    public partial class AxisSettingsDialog : Window
    {
        public AxisSettingsDialog(Axis axis, string chartName)
        {
            InitializeComponent();
            
            var viewModel = new AxisSettingsViewModel(axis, chartName);
            viewModel.OnRequestClose = (result) =>
            {
                DialogResult = result;
                Close();
            };
            
            DataContext = viewModel;
        }

        private void TextBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                // 移除焦点以触发LostFocus更新绑定
                ((TextBox)sender).MoveFocus(new TraversalRequest(FocusNavigationDirection.Next));
                
                // 执行应用命令
                if (DataContext is AxisSettingsViewModel viewModel)
                {
                    viewModel.ApplyCommand.Execute(null);
                }
                
                e.Handled = true;
            }
        }
    }
}