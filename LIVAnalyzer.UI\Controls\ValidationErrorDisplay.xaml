<UserControl x:Class="LIVAnalyzer.UI.Controls.ValidationErrorDisplay"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             mc:Ignorable="d" 
             d:DesignHeight="30" d:DesignWidth="200">
    
    <UserControl.Resources>
        <!-- 错误图标样式 -->
        <Style x:Key="ErrorIcon" TargetType="Path">
            <Setter Property="Fill" Value="{DynamicResource ErrorBrush}"/>
            <Setter Property="Width" Value="16"/>
            <Setter Property="Height" Value="16"/>
            <Setter Property="Stretch" Value="Uniform"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="Data" Value="M12,2C17.53,2 22,6.47 22,12C22,17.53 17.53,22 12,22C6.47,22 2,17.53 2,12C2,6.47 6.47,2 12,2M15.59,7L12,10.59L8.41,7L7,8.41L10.59,12L7,15.59L8.41,17L12,13.41L15.59,17L17,15.59L13.41,12L17,8.41L15.59,7Z"/>
        </Style>

        <!-- 警告图标样式 -->
        <Style x:Key="WarningIcon" TargetType="Path">
            <Setter Property="Fill" Value="{DynamicResource WarningBrush}"/>
            <Setter Property="Width" Value="16"/>
            <Setter Property="Height" Value="16"/>
            <Setter Property="Stretch" Value="Uniform"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="Data" Value="M13,14H11V10H13M13,18H11V16H13M1,21H23L12,2L1,21Z"/>
        </Style>

        <!-- 信息图标样式 -->
        <Style x:Key="InfoIcon" TargetType="Path">
            <Setter Property="Fill" Value="{DynamicResource InfoBrush}"/>
            <Setter Property="Width" Value="16"/>
            <Setter Property="Height" Value="16"/>
            <Setter Property="Stretch" Value="Uniform"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="Data" Value="M13,9H11V7H13M13,17H11V11H13M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z"/>
        </Style>

        <!-- 错误消息文本样式 -->
        <Style x:Key="ErrorMessageText" TargetType="TextBlock">
            <Setter Property="Foreground" Value="{DynamicResource ErrorBrush}"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
            <Setter Property="Margin" Value="4,0,0,0"/>
        </Style>

        <!-- 警告消息文本样式 -->
        <Style x:Key="WarningMessageText" TargetType="TextBlock">
            <Setter Property="Foreground" Value="{DynamicResource WarningBrush}"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
            <Setter Property="Margin" Value="4,0,0,0"/>
        </Style>

        <!-- 信息消息文本样式 -->
        <Style x:Key="InfoMessageText" TargetType="TextBlock">
            <Setter Property="Foreground" Value="{DynamicResource InfoBrush}"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
            <Setter Property="Margin" Value="4,0,0,0"/>
        </Style>

        <!-- 淡入动画 -->
        <Storyboard x:Key="FadeInAnimation">
            <DoubleAnimation Storyboard.TargetProperty="Opacity" 
                           From="0" To="1" Duration="0:0:0.3"/>
            <DoubleAnimation Storyboard.TargetProperty="Height" 
                           From="0" To="24" Duration="0:0:0.2"/>
        </Storyboard>

        <!-- 淡出动画 -->
        <Storyboard x:Key="FadeOutAnimation">
            <DoubleAnimation Storyboard.TargetProperty="Opacity" 
                           From="1" To="0" Duration="0:0:0.2"/>
            <DoubleAnimation Storyboard.TargetProperty="Height" 
                           From="24" To="0" Duration="0:0:0.3"
                           BeginTime="0:0:0.1"/>
        </Storyboard>
    </UserControl.Resources>

    <!-- 主容器 -->
    <Border x:Name="MainBorder" 
            Background="Transparent"
            CornerRadius="4"
            Padding="8,4"
            Opacity="0"
            Height="0"
            ClipToBounds="True">
        
        <Border.Style>
            <Style TargetType="Border">
                <Style.Triggers>
                    <!-- 错误状态样式 -->
                    <DataTrigger Binding="{Binding ElementName=ErrorDisplay, Path=MessageType}" Value="Error">
                        <Setter Property="Background">
                            <Setter.Value>
                                <SolidColorBrush Color="#FFEF4444" Opacity="0.1"/>
                            </Setter.Value>
                        </Setter>
                        <Setter Property="BorderBrush" Value="{DynamicResource ErrorBrush}"/>
                        <Setter Property="BorderThickness" Value="1,0,0,0"/>
                    </DataTrigger>
                    
                    <!-- 警告状态样式 -->
                    <DataTrigger Binding="{Binding ElementName=ErrorDisplay, Path=MessageType}" Value="Warning">
                        <Setter Property="Background">
                            <Setter.Value>
                                <SolidColorBrush Color="#FFF59E0B" Opacity="0.1"/>
                            </Setter.Value>
                        </Setter>
                        <Setter Property="BorderBrush" Value="{DynamicResource WarningBrush}"/>
                        <Setter Property="BorderThickness" Value="1,0,0,0"/>
                    </DataTrigger>
                    
                    <!-- 信息状态样式 -->
                    <DataTrigger Binding="{Binding ElementName=ErrorDisplay, Path=MessageType}" Value="Info">
                        <Setter Property="Background">
                            <Setter.Value>
                                <SolidColorBrush Color="#FF3B82F6" Opacity="0.1"/>
                            </Setter.Value>
                        </Setter>
                        <Setter Property="BorderBrush" Value="{DynamicResource InfoBrush}"/>
                        <Setter Property="BorderThickness" Value="1,0,0,0"/>
                    </DataTrigger>
                </Style.Triggers>
            </Style>
        </Border.Style>

        <!-- 内容容器 -->
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- 图标 -->
            <ContentPresenter x:Name="IconPresenter" Grid.Column="0" VerticalAlignment="Center">
                <ContentPresenter.Style>
                    <Style TargetType="ContentPresenter">
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding ElementName=ErrorDisplay, Path=MessageType}" Value="Error">
                                <Setter Property="Content">
                                    <Setter.Value>
                                        <Path Style="{StaticResource ErrorIcon}"/>
                                    </Setter.Value>
                                </Setter>
                            </DataTrigger>
                            <DataTrigger Binding="{Binding ElementName=ErrorDisplay, Path=MessageType}" Value="Warning">
                                <Setter Property="Content">
                                    <Setter.Value>
                                        <Path Style="{StaticResource WarningIcon}"/>
                                    </Setter.Value>
                                </Setter>
                            </DataTrigger>
                            <DataTrigger Binding="{Binding ElementName=ErrorDisplay, Path=MessageType}" Value="Info">
                                <Setter Property="Content">
                                    <Setter.Value>
                                        <Path Style="{StaticResource InfoIcon}"/>
                                    </Setter.Value>
                                </Setter>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </ContentPresenter.Style>
            </ContentPresenter>

            <!-- 消息文本 -->
            <TextBlock x:Name="MessageText" 
                       Grid.Column="1" 
                       Text="{Binding ElementName=ErrorDisplay, Path=Message}"
                       VerticalAlignment="Center">
                <TextBlock.Style>
                    <Style TargetType="TextBlock" BasedOn="{StaticResource ErrorMessageText}">
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding ElementName=ErrorDisplay, Path=MessageType}" Value="Warning">
                                <Setter Property="Foreground" Value="{DynamicResource WarningBrush}"/>
                            </DataTrigger>
                            <DataTrigger Binding="{Binding ElementName=ErrorDisplay, Path=MessageType}" Value="Info">
                                <Setter Property="Foreground" Value="{DynamicResource InfoBrush}"/>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </TextBlock.Style>
            </TextBlock>

            <!-- 关闭按钮 -->
            <Button x:Name="CloseButton" 
                    Grid.Column="2"
                    Width="16" Height="16"
                    Margin="4,0,0,0"
                    Background="Transparent"
                    BorderThickness="0"
                    Cursor="Hand"
                    Click="CloseButton_Click"
                    ToolTip="关闭消息"
                    Visibility="{Binding ElementName=ErrorDisplay, Path=ShowCloseButton, Converter={StaticResource BooleanToVisibilityConverter}}">
                <Path Width="8" Height="8" 
                      Fill="{Binding ElementName=MessageText, Path=Foreground}"
                      Stretch="Uniform"
                      Data="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"/>
            </Button>
        </Grid>
    </Border>
</UserControl>