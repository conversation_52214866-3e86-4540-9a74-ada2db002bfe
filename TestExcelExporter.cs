using System;
using System.Collections.Generic;
using System.IO;
using LIVAnalyzer.Core.Exporters;
using LIVAnalyzer.Models;

namespace LIVAnalyzer.Test
{
    class TestExcelExporter
    {
        static void Main(string[] args)
        {
            Console.WriteLine("测试Excel导出器的工作表名称重复处理...");
            
            try
            {
                // 创建测试数据 - 模拟具有相似名称的文件
                var testFiles = new List<FileDataModel>();
                
                // 创建几个具有相同基础名称的测试文件
                for (int i = 0; i < 3; i++)
                {
                    var data = new LIVMeasurementData
                    {
                        FileName = "R03+COS01+180_converted.xlsx", // 相同的文件名
                        CurrentPowerData = new List<DataPoint>
                        {
                            new DataPoint(0.1, 0.01),
                            new DataPoint(0.2, 0.05),
                            new DataPoint(0.3, 0.12)
                        },
                        CurrentVoltageData = new List<DataPoint>
                        {
                            new DataPoint(0.1, 1.2),
                            new DataPoint(0.2, 1.4),
                            new DataPoint(0.3, 1.6)
                        },
                        Parameters = new LIVParameters
                        {
                            ThresholdCurrent = 0.15,
                            MaxPower = 0.12,
                            SlopeEfficiency = 0.4,
                            MaxEfficiency = 25.0
                        }
                    };
                    
                    testFiles.Add(new FileDataModel(data));
                }
                
                // 创建导出器并测试
                var exporter = new ExcelDataExporter();
                var outputPath = Path.Combine(Path.GetTempPath(), "test_export.xlsx");
                
                Console.WriteLine($"正在导出到: {outputPath}");
                
                // 这里应该不会抛出工作表名称重复的异常
                var result = exporter.ExportDataAsync(testFiles, outputPath).Result;
                
                if (result)
                {
                    Console.WriteLine("✅ 导出成功！工作表名称重复问题已修复。");
                    Console.WriteLine($"文件已保存到: {outputPath}");
                    
                    // 检查文件是否存在
                    if (File.Exists(outputPath))
                    {
                        var fileInfo = new FileInfo(outputPath);
                        Console.WriteLine($"文件大小: {fileInfo.Length} 字节");
                    }
                }
                else
                {
                    Console.WriteLine("❌ 导出失败");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 测试失败: {ex.Message}");
                Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
            }
            
            Console.WriteLine("\n按任意键退出...");
            Console.ReadKey();
        }
    }
}
