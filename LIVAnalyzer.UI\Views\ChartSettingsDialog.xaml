<Window x:Class="LIVAnalyzer.UI.Views.ChartSettingsDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="图表设置"
        Height="650" Width="500"
        MinHeight="550" MinWidth="450"
        WindowStartupLocation="CenterOwner">

    <Window.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        <converters:InverseBooleanToVisibilityConverter x:Key="InverseBooleanToVisibilityConverter"
                                                        xmlns:converters="clr-namespace:LIVAnalyzer.UI.Converters"/>
        <converters:InverseBooleanConverter x:Key="InverseBooleanConverter"
                                            xmlns:converters="clr-namespace:LIVAnalyzer.UI.Converters"/>

        <!-- 全局样式已在App.xaml中定义，这里不需要重复 -->
    </Window.Resources>
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- 设置内容 -->
        <ScrollViewer Grid.Row="0" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- 预设模板 -->
                <GroupBox Header="预设模板" Margin="0,0,0,16">
                    <StackPanel Margin="8">
                        <TextBlock Text="选择预设模板以快速应用常用配置:" Margin="0,0,0,8" Opacity="0.8"/>
                        <ItemsControl ItemsSource="{Binding PresetTemplates}">
                            <ItemsControl.ItemsPanel>
                                <ItemsPanelTemplate>
                                    <WrapPanel Orientation="Horizontal"/>
                                </ItemsPanelTemplate>
                            </ItemsControl.ItemsPanel>
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Button Content="{Binding}" 
                                            Command="{Binding DataContext.ApplyPresetTemplateCommand, RelativeSource={RelativeSource AncestorType=Window}}" 
                                            CommandParameter="{Binding}"
                                            Style="{DynamicResource DefaultButtonStyle}"
                                            Margin="0,0,8,0"
                                            MinWidth="80"/>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </StackPanel>
                </GroupBox>
                
                <!-- 线条样式设置 -->
                <GroupBox Header="线条样式" Margin="0,0,0,16">
                    <Grid Margin="8">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="120"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="60"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="36"/>
                            <RowDefinition Height="36"/>
                            <RowDefinition Height="36"/>
                        </Grid.RowDefinitions>
                        
                        <!-- 线条粗细 -->
                        <TextBlock Text="线条粗细:" Grid.Row="0" VerticalAlignment="Center"/>
                        <Slider Grid.Row="0" Grid.Column="1" 
                                Minimum="0.5" Maximum="5" 
                                Value="{Binding LineThickness}" 
                                TickFrequency="0.5"
                                IsSnapToTickEnabled="True"
                                VerticalAlignment="Center" Margin="8,0"/>
                        <TextBlock Grid.Row="0" Grid.Column="2" 
                                   Text="{Binding LineThickness, StringFormat='{}{0:F1}'}" 
                                   VerticalAlignment="Center" HorizontalAlignment="Center"/>
                        
                        <!-- 标记大小 -->
                        <TextBlock Text="标记大小:" Grid.Row="1" VerticalAlignment="Center"/>
                        <Slider Grid.Row="1" Grid.Column="1" 
                                Minimum="2" Maximum="10" 
                                Value="{Binding MarkerSize}" 
                                TickFrequency="1"
                                IsSnapToTickEnabled="True"
                                VerticalAlignment="Center" Margin="8,0"/>
                        <TextBlock Grid.Row="1" Grid.Column="2" 
                                   Text="{Binding MarkerSize}" 
                                   VerticalAlignment="Center" HorizontalAlignment="Center"/>
                        
                        <!-- 标记类型 -->
                        <TextBlock Text="标记类型:" Grid.Row="2" VerticalAlignment="Center"/>
                        <ComboBox Grid.Row="2" Grid.Column="1" Grid.ColumnSpan="2"
                                  ItemsSource="{Binding MarkerTypes}"
                                  SelectedItem="{Binding SelectedMarkerType}"
                                  VerticalAlignment="Center" Margin="8,0,0,0"/>
                    </Grid>
                </GroupBox>
                
                <!-- 网格设置 -->
                <GroupBox Header="网格设置" Margin="0,0,0,16">
                    <Grid Margin="8">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="120"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="60"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="36"/>
                            <RowDefinition Height="36"/>
                            <RowDefinition Height="36"/>
                            <RowDefinition Height="36"/>
                            <RowDefinition Height="36"/>
                            <RowDefinition Height="36"/>
                        </Grid.RowDefinitions>
                        
                        <!-- 主网格线透明度 -->
                        <TextBlock Text="主网格线透明度:" Grid.Row="0" VerticalAlignment="Center"/>
                        <Slider Grid.Row="0" Grid.Column="1" 
                                Minimum="0" Maximum="100" 
                                Value="{Binding MajorGridOpacity}" 
                                TickFrequency="10"
                                IsSnapToTickEnabled="True"
                                VerticalAlignment="Center" Margin="8,0"/>
                        <TextBlock Grid.Row="0" Grid.Column="2" 
                                   Text="{Binding MajorGridOpacity, StringFormat='{}{0}%'}" 
                                   VerticalAlignment="Center" HorizontalAlignment="Center"/>
                        
                        <!-- 次网格线透明度 -->
                        <TextBlock Text="次网格线透明度:" Grid.Row="1" VerticalAlignment="Center"/>
                        <Slider Grid.Row="1" Grid.Column="1" 
                                Minimum="0" Maximum="100" 
                                Value="{Binding MinorGridOpacity}" 
                                TickFrequency="10"
                                IsSnapToTickEnabled="True"
                                VerticalAlignment="Center" Margin="8,0"/>
                        <TextBlock Grid.Row="1" Grid.Column="2" 
                                   Text="{Binding MinorGridOpacity, StringFormat='{}{0}%'}" 
                                   VerticalAlignment="Center" HorizontalAlignment="Center"/>
                        
                        <!-- 主网格线样式 -->
                        <TextBlock Text="主网格线样式:" Grid.Row="2" VerticalAlignment="Center"/>
                        <ComboBox Grid.Row="2" Grid.Column="1" Grid.ColumnSpan="2"
                                  ItemsSource="{Binding LineStyles}"
                                  SelectedItem="{Binding MajorGridLineStyle}"
                                  VerticalAlignment="Center" Margin="8,0,0,0"/>
                        
                        <!-- 次网格线样式 -->
                        <TextBlock Text="次网格线样式:" Grid.Row="3" VerticalAlignment="Center"/>
                        <ComboBox Grid.Row="3" Grid.Column="1" Grid.ColumnSpan="2"
                                  ItemsSource="{Binding LineStyles}"
                                  SelectedItem="{Binding MinorGridLineStyle}"
                                  VerticalAlignment="Center" Margin="8,0,0,0"/>
                        
                        <!-- 主网格线颜色 -->
                        <TextBlock Text="主网格线颜色:" Grid.Row="4" VerticalAlignment="Center"/>
                        <ComboBox Grid.Row="4" Grid.Column="1" Grid.ColumnSpan="2"
                                  ItemsSource="{Binding GridColors}"
                                  SelectedItem="{Binding MajorGridColor}"
                                  VerticalAlignment="Center" Margin="8,0,0,0"/>
                        
                        <!-- 次网格线颜色 -->
                        <TextBlock Text="次网格线颜色:" Grid.Row="5" VerticalAlignment="Center"/>
                        <ComboBox Grid.Row="5" Grid.Column="1" Grid.ColumnSpan="2"
                                  ItemsSource="{Binding GridColors}"
                                  SelectedItem="{Binding MinorGridColor}"
                                  VerticalAlignment="Center" Margin="8,0,0,0"/>
                    </Grid>
                </GroupBox>
                
                <!-- 颜色方案 -->
                <GroupBox Header="颜色方案" Margin="0,0,0,16">
                    <Grid Margin="8">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="120"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="36"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        
                        <TextBlock Text="颜色方案:" Grid.Row="0" VerticalAlignment="Center"/>
                        <ComboBox Grid.Row="0" Grid.Column="1"
                                  ItemsSource="{Binding ColorSchemes}"
                                  SelectedItem="{Binding SelectedColorScheme}"
                                  VerticalAlignment="Center" Margin="8,0,0,0"/>
                        
                        <!-- 颜色预览 -->
                        <ItemsControl Grid.Row="1" Grid.Column="1" 
                                      ItemsSource="{Binding PreviewColors}"
                                      Margin="8,8,0,0">
                            <ItemsControl.ItemsPanel>
                                <ItemsPanelTemplate>
                                    <WrapPanel/>
                                </ItemsPanelTemplate>
                            </ItemsControl.ItemsPanel>
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Border Width="30" Height="30" 
                                            Background="{Binding}" 
                                            Margin="0,0,4,4"
                                            CornerRadius="2"
                                            BorderBrush="{DynamicResource AppBorderBrush}"
                                            BorderThickness="1"/>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </Grid>
                </GroupBox>
                
                <!-- 图例设置 -->
                <GroupBox Header="图例设置" Margin="0,0,0,16">
                    <Grid Margin="8">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="120"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="36"/>
                            <RowDefinition Height="36"/>
                        </Grid.RowDefinitions>
                        
                        <TextBlock Text="图例位置:" Grid.Row="0" VerticalAlignment="Center"/>
                        <ComboBox Grid.Row="0" Grid.Column="1"
                                  ItemsSource="{Binding LegendPositions}"
                                  SelectedItem="{Binding SelectedLegendPosition}"
                                  VerticalAlignment="Center" Margin="8,0,0,0"/>
                        
                        <TextBlock Text="图例字体大小:" Grid.Row="1" VerticalAlignment="Center"/>
                        <ComboBox Grid.Row="1" Grid.Column="1"
                                  ItemsSource="{Binding FontSizes}"
                                  SelectedItem="{Binding LegendFontSize}"
                                  VerticalAlignment="Center" Margin="8,0,0,0"/>
                    </Grid>
                </GroupBox>
                
                <!-- 数据标签设置 -->
                <GroupBox Header="数据标签" Margin="0,0,0,16">
                    <Grid Margin="8">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="120"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="36"/>
                            <RowDefinition Height="36"/>
                            <RowDefinition Height="36"/>
                            <RowDefinition Height="36"/>
                        </Grid.RowDefinitions>
                        
                        <!-- 显示数据标签 -->
                        <TextBlock Text="显示数据标签:" Grid.Row="0" VerticalAlignment="Center"/>
                        <CheckBox Grid.Row="0" Grid.Column="1"
                                  IsChecked="{Binding ShowDataLabels}"
                                  Content="在数据点显示数值"
                                  VerticalAlignment="Center" Margin="8,0,0,0"/>
                        
                        <!-- 标签位置 -->
                        <TextBlock Text="标签位置:" Grid.Row="1" VerticalAlignment="Center"
                                   IsEnabled="{Binding ShowDataLabels}"/>
                        <ComboBox Grid.Row="1" Grid.Column="1"
                                  ItemsSource="{Binding DataLabelPositions}"
                                  SelectedItem="{Binding DataLabelPosition}"
                                  IsEnabled="{Binding ShowDataLabels}"
                                  VerticalAlignment="Center" Margin="8,0,0,0"/>
                        
                        <!-- 标签字体大小 -->
                        <TextBlock Text="标签字体大小:" Grid.Row="2" VerticalAlignment="Center"
                                   IsEnabled="{Binding ShowDataLabels}"/>
                        <ComboBox Grid.Row="2" Grid.Column="1"
                                  ItemsSource="{Binding FontSizes}"
                                  SelectedItem="{Binding DataLabelFontSize}"
                                  IsEnabled="{Binding ShowDataLabels}"
                                  VerticalAlignment="Center" Margin="8,0,0,0"/>
                        
                        <!-- 数值格式 -->
                        <TextBlock Text="数值格式:" Grid.Row="3" VerticalAlignment="Center"
                                   IsEnabled="{Binding ShowDataLabels}"/>
                        <ComboBox Grid.Row="3" Grid.Column="1"
                                  ItemsSource="{Binding DataLabelFormats}"
                                  SelectedItem="{Binding DataLabelFormat}"
                                  IsEnabled="{Binding ShowDataLabels}"
                                  VerticalAlignment="Center" Margin="8,0,0,0"/>
                    </Grid>
                </GroupBox>

                <!-- 效率曲线叠加设置 -->
                <GroupBox Header="效率曲线叠加" Margin="0,0,0,16">
                    <Grid Margin="8">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="120"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="36"/>
                        </Grid.RowDefinitions>

                        <!-- 显示效率曲线叠加 -->
                        <TextBlock Text="叠加效率曲线:" Grid.Row="0" VerticalAlignment="Center"/>
                        <CheckBox Grid.Row="0" Grid.Column="1"
                                  IsChecked="{Binding ShowEfficiencyOverlay}"
                                  Content="在LIV曲线上叠加效率曲线"
                                  VerticalAlignment="Center" Margin="8,0,0,0"/>
                    </Grid>
                </GroupBox>

                <!-- 轴设置 -->
                <GroupBox Header="轴设置" Margin="0,0,0,16">
                    <Grid Margin="8">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="120"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="36"/>
                            <RowDefinition Height="36"/>
                        </Grid.RowDefinitions>
                        
                        <TextBlock Text="轴标题字体大小:" Grid.Row="0" VerticalAlignment="Center"/>
                        <ComboBox Grid.Row="0" Grid.Column="1"
                                  ItemsSource="{Binding FontSizes}"
                                  SelectedItem="{Binding AxisTitleFontSize}"
                                  VerticalAlignment="Center" Margin="8,0,0,0"/>
                        
                        <TextBlock Text="轴刻度字体大小:" Grid.Row="1" VerticalAlignment="Center"/>
                        <ComboBox Grid.Row="1" Grid.Column="1"
                                  ItemsSource="{Binding FontSizes}"
                                  SelectedItem="{Binding AxisLabelFontSize}"
                                  VerticalAlignment="Center" Margin="8,0,0,0"/>
                    </Grid>
                </GroupBox>
            </StackPanel>
        </ScrollViewer>
        
        <!-- 按钮区域 -->
        <Grid Grid.Row="1" Margin="0,16,0,0">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            
            <!-- 实时预览选项 -->
            <Border Grid.Row="0" Background="{DynamicResource AppControlBackgroundBrush}"
                    CornerRadius="4" Padding="12,8" Margin="0,0,0,12">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <CheckBox Grid.Column="0" IsChecked="{Binding EnableRealTimePreview}" 
                              VerticalAlignment="Center" Margin="0,0,8,0">
                        <TextBlock Text="实时预览" FontWeight="Medium"/>
                    </CheckBox>
                    
                    <TextBlock Grid.Column="1" Text="更改设置时自动应用到图表" 
                               VerticalAlignment="Center" Opacity="0.7" FontSize="11"/>
                    
                    <StackPanel Grid.Column="2" Orientation="Horizontal" 
                                Visibility="{Binding EnableRealTimePreview, Converter={StaticResource InverseBooleanToVisibilityConverter}}">
                        <Button Content="预览" Command="{Binding PreviewCommand}" 
                                Style="{DynamicResource WhiteButton}" Width="60" Margin="0,0,4,0"
                                IsEnabled="{Binding IsPreviewActive, Converter={StaticResource InverseBooleanConverter}}"/>
                        <Button Content="还原" Command="{Binding RevertPreviewCommand}" 
                                Style="{DynamicResource WhiteButton}" Width="60"
                                IsEnabled="{Binding IsPreviewActive}"/>
                    </StackPanel>
                </Grid>
            </Border>
            
            <!-- 操作按钮 -->
            <StackPanel Grid.Row="1" Orientation="Horizontal" HorizontalAlignment="Right">
                <Button Content="恢复默认" Command="{Binding RestoreDefaultsCommand}" 
                        Style="{DynamicResource WhiteButton}" Margin="0,0,8,0" Width="100"/>
                <Button Content="保存为默认" Command="{Binding SaveAsDefaultCommand}" 
                        Style="{DynamicResource WhiteButton}" Margin="0,0,8,0" Width="120"/>
                <Button Content="应用" Command="{Binding ApplyCommand}" IsDefault="True"
                        Style="{DynamicResource AccentButtonStyle}" Margin="0,0,8,0" Width="80"/>
                <Button Content="取消" Command="{Binding CancelCommand}" IsCancel="True"
                        Style="{DynamicResource WhiteButton}" Width="80"/>
            </StackPanel>
        </Grid>
    </Grid>
</Window>