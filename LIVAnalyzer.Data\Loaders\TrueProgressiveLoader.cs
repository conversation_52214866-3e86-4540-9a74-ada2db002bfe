using LIVAnalyzer.Models;
using LIVAnalyzer.Data.Interfaces;

namespace LIVAnalyzer.Data.Loaders
{
    /// <summary>
    /// 真正的渐进式加载器 - 多文件数据点渐进式读取
    /// </summary>
    public class TrueProgressiveLoader
    {
        private readonly IDataLoader _csvLoader;
        private readonly IDataLoader _excelLoader;
        
        public event EventHandler<MultiFileProgressEventArgs>? DataUpdated;
        
        public TrueProgressiveLoader(IDataLoader csvLoader, IDataLoader excelLoader)
        {
            _csvLoader = csvLoader;
            _excelLoader = excelLoader;
        }
        
        /// <summary>
        /// 渐进式加载多个文件
        /// </summary>
        public async Task<List<LIVMeasurementData>> LoadFilesProgressivelyAsync(string[] filePaths, CancellationToken cancellationToken = default)
        {
            var fileDataList = new List<FileDataInfo>();
            
            // 初始化所有文件的数据结构
            foreach (var filePath in filePaths)
            {
                if (File.Exists(filePath))
                {
                    var extension = Path.GetExtension(filePath).ToLower();
                    if (extension == ".csv" || extension == ".xlsx" || extension == ".xls")
                    {
                        var fileInfo = new FileDataInfo
                        {
                            FilePath = filePath,
                            Data = new LIVMeasurementData { FileName = Path.GetFileName(filePath) },
                            AllLines = await ReadAllLinesFromFile(filePath),
                            CurrentReadIndex = 0
                        };
                        fileDataList.Add(fileInfo);
                    }
                }
            }
            
            if (!fileDataList.Any()) return new List<LIVMeasurementData>();
            
            // 简化的两轮读取策略
            var intervals = new[] { 8, 1 }; // 第一轮：每隔8个点，第二轮：完整数据

            foreach (var interval in intervals)
            {
                var stageName = interval == 1 ? "完整" : "预览";

                // 对每个文件读取当前精度的数据点
                foreach (var fileInfo in fileDataList)
                {
                    if (cancellationToken.IsCancellationRequested) break;

                    await ReadFileDataAtInterval(fileInfo, interval, cancellationToken);

                    // 每读完一个文件就通知UI更新
                    DataUpdated?.Invoke(this, new MultiFileProgressEventArgs
                    {
                        AllFileData = fileDataList.Select(f => f.Data).ToList(),
                        CurrentFile = fileInfo.Data,
                        Stage = stageName,
                        FileIndex = fileDataList.IndexOf(fileInfo),
                        TotalFiles = fileDataList.Count,
                        Interval = interval
                    });

                    // 短暂延迟，让UI有时间更新
                    await Task.Delay(30, cancellationToken);
                }
            }
            
            return fileDataList.Select(f => f.Data).ToList();
        }
        
        /// <summary>
        /// 从文件读取所有行数据
        /// </summary>
        private async Task<string[]> ReadAllLinesFromFile(string filePath)
        {
            var extension = Path.GetExtension(filePath).ToLower();

            if (extension == ".csv")
            {
                return await File.ReadAllLinesAsync(filePath);
            }
            else if (extension == ".xlsx" || extension == ".xls")
            {
                // 对于Excel文件，我们需要特殊处理以支持发散角数据
                // 这里返回空数组，在ReadFileDataAtInterval中直接处理Excel
                return new string[0];
            }

            return new string[0];
        }
        
        /// <summary>
        /// 以指定间隔读取文件数据
        /// </summary>
        private async Task ReadFileDataAtInterval(FileDataInfo fileInfo, int interval, CancellationToken cancellationToken)
        {
            var extension = Path.GetExtension(fileInfo.FilePath).ToLower();

            if (extension == ".csv")
            {
                await ReadCsvDataAtInterval(fileInfo, interval, cancellationToken);
            }
            else if (extension == ".xlsx" || extension == ".xls")
            {
                await ReadExcelDataAtInterval(fileInfo, interval, cancellationToken);
            }
        }

        /// <summary>
        /// 以指定间隔读取CSV文件数据
        /// </summary>
        private async Task ReadCsvDataAtInterval(FileDataInfo fileInfo, int interval, CancellationToken cancellationToken)
        {
            if (fileInfo.AllLines.Length == 0) return;

            // 查找数据开始行
            int dataStart = -1;
            for (int i = 0; i < fileInfo.AllLines.Length; i++)
            {
                if (fileInfo.AllLines[i].StartsWith("Sample No;"))
                {
                    dataStart = i;
                    break;
                }
            }

            if (dataStart == -1) return;

            var headers = fileInfo.AllLines[dataStart].Split(';');
            var dataLines = fileInfo.AllLines.Skip(dataStart + 1)
                .Where(line => !string.IsNullOrWhiteSpace(line))
                .ToArray();

            if (dataLines.Length == 0) return;

            // 清空当前数据（重新构建）
            fileInfo.Data.CurrentPowerData.Clear();
            fileInfo.Data.CurrentVoltageData.Clear();
            fileInfo.Data.WavelengthIntensityData.Clear();

            // 读取指定间隔的数据点
            for (int i = 0; i < dataLines.Length; i += interval)
            {
                if (cancellationToken.IsCancellationRequested) break;

                try
                {
                    var values = dataLines[i].Split(';');

                    var current = ParseDouble(values, headers, "current");
                    var power = ParseDouble(values, headers, "Power");
                    var voltage = ParseDouble(values, headers, "Voltage");
                    var wavelength = ParseDouble(values, headers, "wavelength");
                    var intensity = ParseDouble(values, headers, "intensity");

                    // 添加数据点
                    if (current.HasValue && power.HasValue)
                    {
                        fileInfo.Data.CurrentPowerData.Add(new DataPoint(current.Value, power.Value));
                    }

                    if (current.HasValue && voltage.HasValue)
                    {
                        fileInfo.Data.CurrentVoltageData.Add(new DataPoint(current.Value, voltage.Value));
                    }

                    if (wavelength.HasValue && intensity.HasValue)
                    {
                        fileInfo.Data.WavelengthIntensityData.Add(new DataPoint(wavelength.Value, intensity.Value));
                    }
                }
                catch
                {
                    // 跳过解析失败的行
                    continue;
                }
            }

            // 对数据点进行排序
            SortDataPoints(fileInfo.Data);

            // 标记数据为已处理（参数计算将在UI层进行）
            if (interval == 1)
            {
                fileInfo.Data.IsProcessed = true;
            }
        }
        
        /// <summary>
        /// 以指定间隔读取Excel文件数据（包括发散角数据）
        /// </summary>
        private async Task ReadExcelDataAtInterval(FileDataInfo fileInfo, int interval, CancellationToken cancellationToken)
        {
            try
            {
                // 使用现有的Excel加载器获取完整数据
                var fullData = await _excelLoader.LoadExcelDataAsync(fileInfo.FilePath);
                if (fullData == null) return;

                // 清空当前数据
                fileInfo.Data.CurrentPowerData.Clear();
                fileInfo.Data.CurrentVoltageData.Clear();
                fileInfo.Data.WavelengthIntensityData.Clear();
                fileInfo.Data.HorizontalDivergenceData?.Clear();
                fileInfo.Data.VerticalDivergenceData?.Clear();

                // 渐进式采样LIV数据
                SampleDataPoints(fullData.CurrentPowerData, fileInfo.Data.CurrentPowerData, interval);
                SampleDataPoints(fullData.CurrentVoltageData, fileInfo.Data.CurrentVoltageData, interval);

                // 渐进式采样光谱数据
                SampleDataPoints(fullData.WavelengthIntensityData, fileInfo.Data.WavelengthIntensityData, interval);

                // 渐进式采样发散角数据
                if (fullData.HorizontalDivergenceData != null)
                {
                    fileInfo.Data.HorizontalDivergenceData = new List<DataPoint>();
                    SampleDataPoints(fullData.HorizontalDivergenceData, fileInfo.Data.HorizontalDivergenceData, interval);
                }

                if (fullData.VerticalDivergenceData != null)
                {
                    fileInfo.Data.VerticalDivergenceData = new List<DataPoint>();
                    SampleDataPoints(fullData.VerticalDivergenceData, fileInfo.Data.VerticalDivergenceData, interval);
                }

                // 标记数据为已处理
                if (interval == 1)
                {
                    fileInfo.Data.IsProcessed = true;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Excel渐进式读取失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 对数据点进行采样
        /// </summary>
        private void SampleDataPoints(List<DataPoint> source, List<DataPoint> target, int interval)
        {
            for (int i = 0; i < source.Count; i += interval)
            {
                target.Add(source[i]);
            }
        }

        /// <summary>
        /// 对数据点进行排序
        /// </summary>
        private void SortDataPoints(LIVMeasurementData data)
        {
            if (data.CurrentPowerData.Any())
            {
                var sorted = data.CurrentPowerData.OrderBy(p => p.X).ToList();
                data.CurrentPowerData.Clear();
                data.CurrentPowerData.AddRange(sorted);
            }
            
            if (data.CurrentVoltageData.Any())
            {
                var sorted = data.CurrentVoltageData.OrderBy(p => p.X).ToList();
                data.CurrentVoltageData.Clear();
                data.CurrentVoltageData.AddRange(sorted);
            }
            
            if (data.WavelengthIntensityData.Any())
            {
                var sorted = data.WavelengthIntensityData.OrderBy(p => p.X).ToList();
                data.WavelengthIntensityData.Clear();
                data.WavelengthIntensityData.AddRange(sorted);
            }
        }
        
        /// <summary>
        /// 解析双精度数值
        /// </summary>
        private double? ParseDouble(string[] values, string[] headers, string columnName)
        {
            var index = Array.IndexOf(headers, columnName);
            if (index == -1 || index >= values.Length) return null;
            
            if (double.TryParse(values[index], out var result))
            {
                if (columnName.Contains("current") || columnName.Contains("Power") || columnName.Contains("Voltage"))
                {
                    return Math.Max(0, result);
                }
                return result;
            }
            return null;
        }
    }
    
    /// <summary>
    /// 文件数据信息
    /// </summary>
    public class FileDataInfo
    {
        public string FilePath { get; set; } = string.Empty;
        public LIVMeasurementData Data { get; set; } = new();
        public string[] AllLines { get; set; } = Array.Empty<string>();
        public int CurrentReadIndex { get; set; }
    }
    
    /// <summary>
    /// 多文件进度事件参数
    /// </summary>
    public class MultiFileProgressEventArgs : EventArgs
    {
        public List<LIVMeasurementData> AllFileData { get; set; } = new();
        public LIVMeasurementData? CurrentFile { get; set; }
        public string Stage { get; set; } = string.Empty;
        public int FileIndex { get; set; }
        public int TotalFiles { get; set; }
        public int Interval { get; set; }
    }
}
