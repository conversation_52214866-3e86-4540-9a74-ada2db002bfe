using System;
using LIVAnalyzer.Services.Documents;

namespace LIVAnalyzer.Test
{
    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("测试DocumentService加载所有文档...");

            try
            {
                // 测试使用指南
                Console.WriteLine("\n=== 测试使用指南 ===");
                var userGuide = DocumentService.GetUserGuide();
                if (userGuide != null)
                {
                    Console.WriteLine("✅ 成功加载使用指南");
                    Console.WriteLine($"内容长度: {userGuide.Length} 字符");
                    if (userGuide.Contains("📖 LIV Analyzer C# 版本 - 详细使用指南"))
                    {
                        Console.WriteLine("✅ 确认加载了新的详细使用指南");
                    }
                    else
                    {
                        Console.WriteLine("⚠️ 加载的是默认使用指南");
                    }
                }
                else
                {
                    Console.WriteLine("❌ 加载使用指南失败");
                }

                // 测试技术文档
                Console.WriteLine("\n=== 测试技术文档 ===");
                var techDoc = DocumentService.GetTechnicalDocumentation();
                if (techDoc != null)
                {
                    Console.WriteLine("✅ 成功加载技术文档");
                    Console.WriteLine($"内容长度: {techDoc.Length} 字符");
                    if (techDoc.Contains("C# WPF版本"))
                    {
                        Console.WriteLine("✅ 确认是C#版本的技术文档");
                    }
                }
                else
                {
                    Console.WriteLine("❌ 加载技术文档失败");
                }

                // 测试发布说明
                Console.WriteLine("\n=== 测试发布说明 ===");
                var releaseNotes = DocumentService.GetReleaseNotes();
                if (releaseNotes != null)
                {
                    Console.WriteLine("✅ 成功加载发布说明");
                    Console.WriteLine($"内容长度: {releaseNotes.Length} 字符");
                    if (releaseNotes.Contains("2025-01-20"))
                    {
                        Console.WriteLine("✅ 确认日期已更新为2025年");
                    }
                }
                else
                {
                    Console.WriteLine("❌ 加载发布说明失败");
                }

                // 测试关于信息
                Console.WriteLine("\n=== 测试关于信息 ===");
                var aboutInfo = DocumentService.GetAboutInfo();
                if (aboutInfo != null)
                {
                    Console.WriteLine("✅ 成功加载关于信息");
                    Console.WriteLine($"内容长度: {aboutInfo.Length} 字符");
                    if (aboutInfo.Contains("2025-01-20"))
                    {
                        Console.WriteLine("✅ 确认发布日期已更新");
                    }
                }
                else
                {
                    Console.WriteLine("❌ 加载关于信息失败");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 发生错误: {ex.Message}");
            }

            Console.WriteLine("\n按任意键退出...");
            Console.ReadKey();
        }
    }
}
