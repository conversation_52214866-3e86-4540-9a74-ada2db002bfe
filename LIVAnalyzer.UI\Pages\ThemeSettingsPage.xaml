<Page x:Class="LIVAnalyzer.UI.Pages.ThemeSettingsPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      mc:Ignorable="d" 
      d:DesignHeight="600" d:DesignWidth="800"
      Title="主题设置">

    <ScrollViewer>
        <StackPanel Margin="24" MaxWidth="600" HorizontalAlignment="Left">
            
            <!-- 标题 -->
            <TextBlock Text="个性化设置" 
                       Style="{DynamicResource HeaderTextBlockStyle}"
                       Margin="0,0,0,24"/>
            
            <!-- 主题选择 -->
            <Border Style="{StaticResource DarkCard}">
                <StackPanel>
                    <TextBlock Text="应用主题" 
                               Style="{DynamicResource SubtitleTextBlockStyle}"
                               Margin="0,0,0,16"/>
                    
                    <StackPanel x:Name="ThemeRadioButtons">
                        <RadioButton Content="浅色主题" Tag="Light" Checked="ThemeRadioButton_Checked"/>
                        <RadioButton Content="深色主题" Tag="Dark" IsChecked="True" Checked="ThemeRadioButton_Checked"/>
                        <RadioButton Content="跟随系统" Tag="System" Checked="ThemeRadioButton_Checked"/>
                    </StackPanel>
                </StackPanel>
            </Border>
            
            <!-- 主题色选择 -->
            <Border Style="{StaticResource DarkCard}" Margin="0,16,0,0">
                <StackPanel>
                    <TextBlock Text="主题色" 
                               Style="{DynamicResource SubtitleTextBlockStyle}"
                               Margin="0,0,0,16"/>
                    
                    <WrapPanel>
                        <Button Width="40" Height="40" Margin="4"
                                Background="#0078D4"
                                Click="AccentColor_Click"
                                Tag="#0078D4"/>
                        <Button Width="40" Height="40" Margin="4"
                                Background="#0099BC"
                                Click="AccentColor_Click"
                                Tag="#0099BC"/>
                        <Button Width="40" Height="40" Margin="4"
                                Background="#7A7574"
                                Click="AccentColor_Click"
                                Tag="#7A7574"/>
                        <Button Width="40" Height="40" Margin="4"
                                Background="#767676"
                                Click="AccentColor_Click"
                                Tag="#767676"/>
                        <Button Width="40" Height="40" Margin="4"
                                Background="#FF8C00"
                                Click="AccentColor_Click"
                                Tag="#FF8C00"/>
                        <Button Width="40" Height="40" Margin="4"
                                Background="#E81123"
                                Click="AccentColor_Click"
                                Tag="#E81123"/>
                        <Button Width="40" Height="40" Margin="4"
                                Background="#0063B1"
                                Click="AccentColor_Click"
                                Tag="#0063B1"/>
                        <Button Width="40" Height="40" Margin="4"
                                Background="#2D7D9A"
                                Click="AccentColor_Click"
                                Tag="#2D7D9A"/>
                        <Button Width="40" Height="40" Margin="4"
                                Background="#E3008C"
                                Click="AccentColor_Click"
                                Tag="#E3008C"/>
                        <Button Width="40" Height="40" Margin="4"
                                Background="#5D2F91"
                                Click="AccentColor_Click"
                                Tag="#5D2F91"/>
                        <Button Width="40" Height="40" Margin="4"
                                Background="#B146C2"
                                Click="AccentColor_Click"
                                Tag="#B146C2"/>
                        <Button Width="40" Height="40" Margin="4"
                                Background="#00CC6A"
                                Click="AccentColor_Click"
                                Tag="#00CC6A"/>
                    </WrapPanel>
                    
                    <!-- 自定义颜色 -->
                    <StackPanel Orientation="Horizontal" Margin="0,16,0,0">
                        <TextBlock Text="自定义颜色:"
                                   VerticalAlignment="Center"
                                   Margin="0,0,8,0"/>
                        <Button x:Name="CustomColorButton"
                                Content="选择颜色"
                                Background="#0084FF"
                                Foreground="White"
                                Padding="12,6"
                                Click="CustomColorButton_Click"/>
                    </StackPanel>
                </StackPanel>
            </Border>
            
            <!-- 界面缩放 -->
            <Border Style="{StaticResource DarkCard}" Margin="0,16,0,0">
                <StackPanel>
                    <TextBlock Text="界面缩放" 
                               Style="{DynamicResource SubtitleTextBlockStyle}"
                               Margin="0,0,0,16"/>
                    
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBlock Text="75%" Grid.Column="0" 
                                   VerticalAlignment="Center"
                                   Margin="0,0,16,0"/>
                        
                        <Slider x:Name="ScaleSlider"
                                Grid.Column="1"
                                Minimum="0.75"
                                Maximum="2.0"
                                Value="1.0"
                                TickFrequency="0.25"
                                IsSnapToTickEnabled="True"
                                ValueChanged="ScaleSlider_ValueChanged"/>
                        
                        <TextBlock x:Name="ScaleText" 
                                   Text="100%"
                                   Grid.Column="2"
                                   VerticalAlignment="Center"
                                   Margin="16,0,0,0"
                                   MinWidth="40"/>
                    </Grid>
                </StackPanel>
            </Border>
            
            <!-- 图表配色方案 -->
            <Border Style="{StaticResource DarkCard}" Margin="0,16,0,0">
                <StackPanel>
                    <TextBlock Text="图表配色方案" 
                               Style="{DynamicResource SubtitleTextBlockStyle}"
                               Margin="0,0,0,16"/>
                    
                    <ComboBox x:Name="ChartThemeCombo"
                              SelectionChanged="ChartThemeCombo_SelectionChanged">
                        <ComboBoxItem Content="默认配色" IsSelected="True"/>
                        <ComboBoxItem Content="彩虹配色"/>
                        <ComboBoxItem Content="单色渐变"/>
                        <ComboBoxItem Content="高对比度"/>
                        <ComboBoxItem Content="色盲友好"/>
                    </ComboBox>
                    
                    <!-- 配色预览 -->
                    <WrapPanel Margin="0,16,0,0">
                        <Rectangle Width="40" Height="20" Fill="#4FC3F7" Margin="2"/>
                        <Rectangle Width="40" Height="20" Fill="#81C784" Margin="2"/>
                        <Rectangle Width="40" Height="20" Fill="#FFB74D" Margin="2"/>
                        <Rectangle Width="40" Height="20" Fill="#E57373" Margin="2"/>
                        <Rectangle Width="40" Height="20" Fill="#BA68C8" Margin="2"/>
                    </WrapPanel>
                </StackPanel>
            </Border>
            
            <!-- 重置按钮 -->
            <Button Content="恢复默认设置"
                    Style="{StaticResource DefaultButtonStyle}"
                    HorizontalAlignment="Left"
                    Margin="0,24,0,0"
                    Click="ResetButton_Click"/>
            
        </StackPanel>
    </ScrollViewer>
</Page>