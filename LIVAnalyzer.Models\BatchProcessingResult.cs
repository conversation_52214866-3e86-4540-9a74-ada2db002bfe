namespace LIVAnalyzer.Models
{
    /// <summary>
    /// 批量处理结果模型
    /// </summary>
    public class BatchProcessingResult
    {
        /// <summary>
        /// 文件名
        /// </summary>
        public string FileName { get; set; } = string.Empty;
        
        /// <summary>
        /// LIV参数
        /// </summary>
        public LIVParameters? Parameters { get; set; }
        
        /// <summary>
        /// I1电流下的参数
        /// </summary>
        public CurrentParameters? I1Parameters { get; set; }
        
        /// <summary>
        /// I2电流下的参数
        /// </summary>
        public CurrentParameters? I2Parameters { get; set; }
        
        /// <summary>
        /// 处理是否成功
        /// </summary>
        public bool IsSuccess { get; set; }
        
        /// <summary>
        /// 错误信息
        /// </summary>
        public string? ErrorMessage { get; set; }
    }
    
    /// <summary>
    /// 特定电流下的参数
    /// </summary>
    public class CurrentParameters
    {
        /// <summary>
        /// 电流 (A)
        /// </summary>
        public double Current { get; set; }
        
        /// <summary>
        /// 电压 (V)
        /// </summary>
        public double Voltage { get; set; }
        
        /// <summary>
        /// 功率 (W)
        /// </summary>
        public double Power { get; set; }
        
        /// <summary>
        /// 效率 (%)
        /// </summary>
        public double Efficiency { get; set; }
    }
}