<Application x:Class="LIVAnalyzer.UI.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             ThemeMode="System">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- .NET 9 原生 Fluent Design 主题 -->
                <ResourceDictionary Source="/PresentationFramework.Fluent;component/Themes/Fluent.xaml"/>

                <!-- 应用程序特定样式（基于原生Fluent） -->
                <ResourceDictionary Source="Styles/AppStyles.xaml"/>

                <!-- .NET 9 原生 Fluent Design 样式 -->
                <ResourceDictionary Source="Styles/NativeFluentStyles.xaml"/>

                <!-- 图表主题（适配原生Fluent） -->
                <ResourceDictionary Source="Themes/NativeFluentChartThemes.xaml"/>

                <!-- Fluent主题资源 -->
                <ResourceDictionary Source="Themes/FluentLightTheme.xaml"/>
                <ResourceDictionary Source="Themes/FluentDarkTheme.xaml"/>
            </ResourceDictionary.MergedDictionaries>

            <!-- 全局主题资源 - 动态更新 -->
            <SolidColorBrush x:Key="AppBackgroundBrush" Color="#FFFFFF"/>
            <SolidColorBrush x:Key="AppForegroundBrush" Color="#000000"/>
            <SolidColorBrush x:Key="AppControlBackgroundBrush" Color="#F3F3F3"/>
            <SolidColorBrush x:Key="AppBorderBrush" Color="#E1E1E1"/>
            <SolidColorBrush x:Key="ChartBackgroundBrush" Color="#FFFFFF"/>
            <SolidColorBrush x:Key="ChartPlotAreaBrush" Color="#F8F8F8"/>
            <SolidColorBrush x:Key="ChartTextBrush" Color="#000000"/>
            <SolidColorBrush x:Key="ChartAxisBrush" Color="#000000"/>
            <SolidColorBrush x:Key="ChartGridBrush" Color="#E1E1E1"/>
            <SolidColorBrush x:Key="ChartMinorGridBrush" Color="#F0F0F0"/>
            
            <!-- 强调色资源 - 主题无关的蓝色系 -->
            <SolidColorBrush x:Key="AppAccentBrush" Color="#FF0078D4"/>
            <SolidColorBrush x:Key="AppAccentHoverBrush" Color="#FF106EBE"/>
            <SolidColorBrush x:Key="AppAccentPressedBrush" Color="#FF005A9E"/>

            <!-- 全局控件样式 - 应用到所有窗口 -->
            <Style TargetType="Window">
                <Setter Property="Background" Value="{DynamicResource AppBackgroundBrush}"/>
                <Setter Property="Foreground" Value="{DynamicResource AppForegroundBrush}"/>
            </Style>

            <Style TargetType="GroupBox">
                <Setter Property="Background" Value="{DynamicResource AppControlBackgroundBrush}"/>
                <Setter Property="Foreground" Value="{DynamicResource AppForegroundBrush}"/>
                <Setter Property="BorderBrush" Value="{DynamicResource AppBorderBrush}"/>
            </Style>

            <Style TargetType="TextBlock">
                <Setter Property="Foreground" Value="{DynamicResource AppForegroundBrush}"/>
            </Style>

            <Style TargetType="ComboBox">
                <Setter Property="Background" Value="{DynamicResource AppControlBackgroundBrush}"/>
                <Setter Property="Foreground" Value="{DynamicResource AppForegroundBrush}"/>
                <Setter Property="BorderBrush" Value="{DynamicResource AppBorderBrush}"/>
                <Setter Property="BorderThickness" Value="1"/>
                <Setter Property="Padding" Value="8,4"/>
                <Setter Property="MinHeight" Value="28"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="ComboBox">
                            <Grid>
                                <ToggleButton Name="ToggleButton" 
                                            Background="{TemplateBinding Background}"
                                            BorderBrush="{TemplateBinding BorderBrush}"
                                            BorderThickness="{TemplateBinding BorderThickness}"
                                            Grid.Column="2" 
                                            Focusable="false"
                                            IsChecked="{Binding Path=IsDropDownOpen,Mode=TwoWay,RelativeSource={RelativeSource TemplatedParent}}"
                                            ClickMode="Press">
                                    <ToggleButton.Template>
                                        <ControlTemplate TargetType="ToggleButton">
                                            <Border Background="{TemplateBinding Background}"
                                                    BorderBrush="{TemplateBinding BorderBrush}"
                                                    BorderThickness="{TemplateBinding BorderThickness}"
                                                    CornerRadius="4">
                                                <Grid>
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition/>
                                                        <ColumnDefinition Width="20"/>
                                                    </Grid.ColumnDefinitions>
                                                    <Path Grid.Column="1" 
                                                          HorizontalAlignment="Center" 
                                                          VerticalAlignment="Center"
                                                          Data="M 0 0 L 4 4 L 8 0 Z"
                                                          Fill="{DynamicResource AppForegroundBrush}"/>
                                                </Grid>
                                            </Border>
                                            <ControlTemplate.Triggers>
                                                <Trigger Property="IsMouseOver" Value="True">
                                                    <Setter Property="Background" Value="{DynamicResource AppBorderBrush}"/>
                                                </Trigger>
                                            </ControlTemplate.Triggers>
                                        </ControlTemplate>
                                    </ToggleButton.Template>
                                </ToggleButton>
                                <ContentPresenter Name="ContentSite"
                                                IsHitTestVisible="False" 
                                                Content="{TemplateBinding SelectionBoxItem}"
                                                ContentTemplate="{TemplateBinding SelectionBoxItemTemplate}"
                                                ContentTemplateSelector="{TemplateBinding ItemTemplateSelector}"
                                                Margin="{TemplateBinding Padding}"
                                                VerticalAlignment="Center"
                                                HorizontalAlignment="Left"/>
                                <TextBox x:Name="PART_EditableTextBox"
                                       Style="{x:Null}" 
                                       Template="{DynamicResource ComboBoxTextBox}" 
                                       HorizontalAlignment="Left" 
                                       VerticalAlignment="Center" 
                                       Margin="3,3,23,3"
                                       Focusable="True" 
                                       Background="Transparent"
                                       Foreground="{DynamicResource AppForegroundBrush}"
                                       Visibility="Hidden"
                                       IsReadOnly="{TemplateBinding IsReadOnly}"/>
                                <Popup Name="Popup"
                                     Placement="Bottom"
                                     IsOpen="{TemplateBinding IsDropDownOpen}"
                                     AllowsTransparency="True" 
                                     Focusable="False"
                                     PopupAnimation="Slide">
                                    <Grid Name="DropDown"
                                        SnapsToDevicePixels="True"                
                                        MinWidth="{TemplateBinding ActualWidth}"
                                        MaxHeight="{TemplateBinding MaxDropDownHeight}">
                                        <Border x:Name="DropDownBorder"
                                              Background="{DynamicResource AppControlBackgroundBrush}"
                                              BorderThickness="1"
                                              BorderBrush="{DynamicResource AppBorderBrush}"
                                              CornerRadius="4">
                                            <ScrollViewer Margin="4,6,4,6" SnapsToDevicePixels="True">
                                                <StackPanel IsItemsHost="True" KeyboardNavigation.DirectionalNavigation="Contained"/>
                                            </ScrollViewer>
                                        </Border>
                                    </Grid>
                                </Popup>
                            </Grid>
                            <ControlTemplate.Triggers>
                                <Trigger Property="HasItems" Value="false">
                                    <Setter TargetName="DropDownBorder" Property="MinHeight" Value="95"/>
                                </Trigger>
                                <Trigger Property="IsEnabled" Value="false">
                                    <Setter Property="Opacity" Value="0.5"/>
                                </Trigger>
                                <Trigger Property="IsGrouping" Value="true">
                                    <Setter Property="ScrollViewer.CanContentScroll" Value="false"/>
                                </Trigger>
                                <Trigger SourceName="Popup" Property="Popup.AllowsTransparency" Value="true">
                                    <Setter TargetName="DropDownBorder" Property="CornerRadius" Value="4"/>
                                    <Setter TargetName="DropDownBorder" Property="Margin" Value="0,2,0,0"/>
                                </Trigger>
                                <Trigger Property="IsEditable" Value="true">
                                    <Setter Property="IsTabStop" Value="false"/>
                                    <Setter TargetName="PART_EditableTextBox" Property="Visibility" Value="Visible"/>
                                    <Setter TargetName="ContentSite" Property="Visibility" Value="Hidden"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>

            <!-- ComboBoxItem样式 -->
            <Style TargetType="ComboBoxItem">
                <Setter Property="Background" Value="Transparent"/>
                <Setter Property="Foreground" Value="{DynamicResource AppForegroundBrush}"/>
                <Setter Property="Padding" Value="8,4"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="ComboBoxItem">
                            <Border Background="{TemplateBinding Background}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    Padding="{TemplateBinding Padding}">
                                <ContentPresenter/>
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsHighlighted" Value="True">
                                    <Setter Property="Background" Value="{DynamicResource AppBorderBrush}"/>
                                </Trigger>
                                <Trigger Property="IsSelected" Value="True">
                                    <Setter Property="Background" Value="{DynamicResource AppAccentBrush}"/>
                                    <Setter Property="Foreground" Value="{DynamicResource CheckBoxMarkBrush}"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>

            <!-- ComboBox可编辑文本框模板 -->
            <ControlTemplate x:Key="ComboBoxTextBox" TargetType="TextBox">
                <Border x:Name="PART_ContentHost" 
                        Focusable="False" 
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"/>
            </ControlTemplate>

            <Style TargetType="TextBox">
                <Setter Property="Background" Value="{DynamicResource AppControlBackgroundBrush}"/>
                <Setter Property="Foreground" Value="{DynamicResource AppForegroundBrush}"/>
                <Setter Property="BorderBrush" Value="{DynamicResource AppBorderBrush}"/>
            </Style>

            <Style TargetType="CheckBox">
                <Setter Property="Foreground" Value="{DynamicResource AppForegroundBrush}"/>
                <Setter Property="Background" Value="{DynamicResource AppControlBackgroundBrush}"/>
                <Setter Property="BorderBrush" Value="{DynamicResource AppBorderBrush}"/>
                <Setter Property="BorderThickness" Value="1"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="CheckBox">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <!-- 复选框框体 -->
                                <Border x:Name="CheckBoxBorder" Grid.Column="0"
                                        Width="16" Height="16"
                                        Background="{TemplateBinding Background}"
                                        BorderBrush="{TemplateBinding BorderBrush}"
                                        BorderThickness="{TemplateBinding BorderThickness}"
                                        CornerRadius="2"
                                        VerticalAlignment="Center">

                                    <!-- 勾选标记 -->
                                    <Path x:Name="CheckMark"
                                          Width="14" Height="12"
                                          Stretch="Fill"
                                          Fill="{DynamicResource CheckBoxMarkBrush}"
                                          Data="M 2,6 L 5,9 L 12,2"
                                          Stroke="{DynamicResource CheckBoxMarkBrush}"
                                          StrokeThickness="1.5"
                                          StrokeLineJoin="Round"
                                          StrokeStartLineCap="Round"
                                          StrokeEndLineCap="Round"
                                          Visibility="Collapsed"
                                          HorizontalAlignment="Center"
                                          VerticalAlignment="Center"/>
                                </Border>

                                <!-- 文本内容 -->
                                <ContentPresenter Grid.Column="1"
                                                Margin="6,0,0,0"
                                                VerticalAlignment="Center"
                                                HorizontalAlignment="Left"/>
                            </Grid>

                            <ControlTemplate.Triggers>
                                <Trigger Property="IsChecked" Value="True">
                                    <Setter TargetName="CheckMark" Property="Visibility" Value="Visible"/>
                                    <Setter TargetName="CheckBoxBorder" Property="Background" Value="{DynamicResource AppAccentBrush}"/>
                                    <Setter TargetName="CheckBoxBorder" Property="BorderBrush" Value="{DynamicResource AppAccentBrush}"/>
                                    <Setter TargetName="CheckMark" Property="Fill" Value="{DynamicResource CheckBoxMarkBrush}"/>
                                    <Setter TargetName="CheckMark" Property="Stroke" Value="{DynamicResource CheckBoxMarkBrush}"/>
                                </Trigger>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter TargetName="CheckBoxBorder" Property="Background" Value="{DynamicResource AppBorderBrush}"/>
                                </Trigger>
                                <Trigger Property="IsPressed" Value="True">
                                    <Setter TargetName="CheckBoxBorder" Property="Background" Value="{DynamicResource AppBorderBrush}"/>
                                </Trigger>
                                <MultiTrigger>
                                    <MultiTrigger.Conditions>
                                        <Condition Property="IsChecked" Value="True"/>
                                        <Condition Property="IsMouseOver" Value="True"/>
                                    </MultiTrigger.Conditions>
                                    <Setter TargetName="CheckBoxBorder" Property="Background" Value="{DynamicResource AppAccentHoverBrush}"/>
                                    <Setter TargetName="CheckBoxBorder" Property="BorderBrush" Value="{DynamicResource AppAccentHoverBrush}"/>
                                    <Setter TargetName="CheckMark" Property="Fill" Value="{DynamicResource CheckBoxMarkBrush}"/>
                                    <Setter TargetName="CheckMark" Property="Stroke" Value="{DynamicResource CheckBoxMarkBrush}"/>
                                </MultiTrigger>
                                <MultiTrigger>
                                    <MultiTrigger.Conditions>
                                        <Condition Property="IsChecked" Value="True"/>
                                        <Condition Property="IsPressed" Value="True"/>
                                    </MultiTrigger.Conditions>
                                    <Setter TargetName="CheckBoxBorder" Property="Background" Value="{DynamicResource AppAccentPressedBrush}"/>
                                    <Setter TargetName="CheckBoxBorder" Property="BorderBrush" Value="{DynamicResource AppAccentPressedBrush}"/>
                                    <Setter TargetName="CheckMark" Property="Fill" Value="{DynamicResource CheckBoxMarkBrush}"/>
                                    <Setter TargetName="CheckMark" Property="Stroke" Value="{DynamicResource CheckBoxMarkBrush}"/>
                                </MultiTrigger>
                                <Trigger Property="IsEnabled" Value="False">
                                    <Setter Property="Foreground" Value="{DynamicResource AppBorderBrush}"/>
                                    <Setter TargetName="CheckBoxBorder" Property="Background" Value="{DynamicResource AppBorderBrush}"/>
                                    <Setter TargetName="CheckMark" Property="Fill" Value="{DynamicResource CheckBoxMarkBrush}"/>
                                    <Setter TargetName="CheckMark" Property="Stroke" Value="{DynamicResource CheckBoxMarkBrush}"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>

            <Style TargetType="Slider">
                <Setter Property="Foreground" Value="{DynamicResource AppForegroundBrush}"/>
            </Style>

            <Style TargetType="Menu">
                <Setter Property="Background" Value="{DynamicResource AppControlBackgroundBrush}"/>
                <Setter Property="Foreground" Value="{DynamicResource AppForegroundBrush}"/>
            </Style>

            <Style TargetType="MenuItem">
                <Setter Property="Background" Value="{DynamicResource AppControlBackgroundBrush}"/>
                <Setter Property="Foreground" Value="{DynamicResource AppForegroundBrush}"/>
            </Style>

            <Style TargetType="Button">
                <Setter Property="Background" Value="{DynamicResource AppControlBackgroundBrush}"/>
                <Setter Property="Foreground" Value="{DynamicResource AppForegroundBrush}"/>
                <Setter Property="BorderBrush" Value="{DynamicResource AppBorderBrush}"/>
            </Style>

            <Style TargetType="Label">
                <Setter Property="Foreground" Value="{DynamicResource AppForegroundBrush}"/>
            </Style>

            <Style TargetType="TabControl">
                <Setter Property="Background" Value="{DynamicResource AppControlBackgroundBrush}"/>
                <Setter Property="Foreground" Value="{DynamicResource AppForegroundBrush}"/>
                <Setter Property="BorderBrush" Value="{DynamicResource AppBorderBrush}"/>
                <Setter Property="BorderThickness" Value="1"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="TabControl">
                            <Grid ClipToBounds="True" SnapsToDevicePixels="True" KeyboardNavigation.TabNavigation="Local">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Name="ColumnDefinition0"/>
                                    <ColumnDefinition Name="ColumnDefinition1" Width="0"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Name="RowDefinition0" Height="Auto"/>
                                    <RowDefinition Name="RowDefinition1" Height="*"/>
                                </Grid.RowDefinitions>
                                
                                <!-- 标签页头部区域 -->
                                <TabPanel x:Name="HeaderPanel" 
                                        Grid.Column="0" 
                                        Grid.Row="0"
                                        IsItemsHost="True" 
                                        Margin="2,2,2,0" 
                                        KeyboardNavigation.TabIndex="1" 
                                        Panel.ZIndex="1"
                                        Background="Transparent"/>
                                
                                <!-- 内容区域 -->
                                <Border x:Name="ContentPanel" 
                                      Grid.Column="0" 
                                      Grid.Row="1"
                                      Background="{TemplateBinding Background}"
                                      BorderBrush="{TemplateBinding BorderBrush}"
                                      BorderThickness="{TemplateBinding BorderThickness}"
                                      CornerRadius="0,4,4,4"
                                      KeyboardNavigation.TabIndex="2" 
                                      KeyboardNavigation.TabNavigation="Local"
                                      KeyboardNavigation.DirectionalNavigation="Contained">
                                    <ContentPresenter x:Name="PART_SelectedContentHost" 
                                                    ContentSource="SelectedContent" 
                                                    Margin="4"
                                                    SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"/>
                                </Border>
                            </Grid>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>

            <Style TargetType="TabItem">
                <Setter Property="Background" Value="{DynamicResource AppControlBackgroundBrush}"/>
                <Setter Property="Foreground" Value="{DynamicResource AppForegroundBrush}"/>
                <Setter Property="BorderBrush" Value="{DynamicResource AppBorderBrush}"/>
                <Setter Property="BorderThickness" Value="1,1,1,0"/>
                <Setter Property="Padding" Value="12,6"/>
                <Setter Property="Margin" Value="0,0,2,0"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="TabItem">
                            <Border x:Name="Border"
                                    Background="{TemplateBinding Background}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    CornerRadius="4,4,0,0"
                                    Margin="{TemplateBinding Margin}">
                                <ContentPresenter x:Name="ContentSite"
                                                VerticalAlignment="Center"
                                                HorizontalAlignment="Center"
                                                ContentSource="Header"
                                                Margin="{TemplateBinding Padding}"/>
                            </Border>
                            <ControlTemplate.Triggers>
                                <!-- 鼠标悬停状态 -->
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter TargetName="Border" Property="Background" Value="{DynamicResource AppBorderBrush}"/>
                                </Trigger>
                                <!-- 选中状态 - 这是关键修复 -->
                                <Trigger Property="IsSelected" Value="True">
                                    <Setter TargetName="Border" Property="Background" Value="{DynamicResource AppBackgroundBrush}"/>
                                    <Setter TargetName="Border" Property="BorderBrush" Value="{DynamicResource AppBorderBrush}"/>
                                    <Setter TargetName="Border" Property="BorderThickness" Value="1,1,1,0"/>
                                    <Setter Property="Panel.ZIndex" Value="100"/>
                                </Trigger>
                                <!-- 选中且鼠标悬停状态 -->
                                <MultiTrigger>
                                    <MultiTrigger.Conditions>
                                        <Condition Property="IsSelected" Value="True"/>
                                        <Condition Property="IsMouseOver" Value="True"/>
                                    </MultiTrigger.Conditions>
                                    <Setter TargetName="Border" Property="Background" Value="{DynamicResource AppBackgroundBrush}"/>
                                    <Setter TargetName="Border" Property="Opacity" Value="0.9"/>
                                </MultiTrigger>
                                <!-- 禁用状态 -->
                                <Trigger Property="IsEnabled" Value="False">
                                    <Setter Property="Opacity" Value="0.5"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>

            <Style TargetType="Border">
                <Setter Property="Background" Value="{DynamicResource AppControlBackgroundBrush}"/>
                <Setter Property="BorderBrush" Value="{DynamicResource AppBorderBrush}"/>
            </Style>

            <Style TargetType="ListBox">
                <Setter Property="Background" Value="{DynamicResource AppControlBackgroundBrush}"/>
                <Setter Property="Foreground" Value="{DynamicResource AppForegroundBrush}"/>
                <Setter Property="BorderBrush" Value="{DynamicResource AppBorderBrush}"/>
            </Style>

            <Style TargetType="ListBoxItem">
                <Setter Property="Background" Value="Transparent"/>
                <Setter Property="Foreground" Value="{DynamicResource AppForegroundBrush}"/>
            </Style>

            <Style TargetType="ScrollViewer">
                <Setter Property="Background" Value="{DynamicResource AppControlBackgroundBrush}"/>
            </Style>

            <Style TargetType="Grid">
                <Setter Property="Background" Value="Transparent"/>
            </Style>

            <Style TargetType="StackPanel">
                <Setter Property="Background" Value="Transparent"/>
            </Style>

            <Style TargetType="ToggleButton">
                <Setter Property="Background" Value="{DynamicResource AppControlBackgroundBrush}"/>
                <Setter Property="Foreground" Value="{DynamicResource AppForegroundBrush}"/>
                <Setter Property="BorderBrush" Value="{DynamicResource AppBorderBrush}"/>
            </Style>

            <Style TargetType="RadioButton">
                <Setter Property="Background" Value="{DynamicResource AppControlBackgroundBrush}"/>
                <Setter Property="Foreground" Value="{DynamicResource AppForegroundBrush}"/>
            </Style>

            <Style TargetType="ProgressBar">
                <Setter Property="Background" Value="{DynamicResource AppControlBackgroundBrush}"/>
                <Setter Property="Foreground" Value="{DynamicResource AppForegroundBrush}"/>
                <Setter Property="BorderBrush" Value="{DynamicResource AppBorderBrush}"/>
            </Style>

            <Style TargetType="StatusBar">
                <Setter Property="Background" Value="{DynamicResource AppControlBackgroundBrush}"/>
                <Setter Property="Foreground" Value="{DynamicResource AppForegroundBrush}"/>
            </Style>

            <Style TargetType="ToolBar">
                <Setter Property="Background" Value="{DynamicResource AppControlBackgroundBrush}"/>
                <Setter Property="Foreground" Value="{DynamicResource AppForegroundBrush}"/>
            </Style>

            <Style TargetType="Expander">
                <Setter Property="Background" Value="{DynamicResource AppControlBackgroundBrush}"/>
                <Setter Property="Foreground" Value="{DynamicResource AppForegroundBrush}"/>
                <Setter Property="BorderBrush" Value="{DynamicResource AppBorderBrush}"/>
            </Style>

            <Style TargetType="TreeView">
                <Setter Property="Background" Value="{DynamicResource AppControlBackgroundBrush}"/>
                <Setter Property="Foreground" Value="{DynamicResource AppForegroundBrush}"/>
                <Setter Property="BorderBrush" Value="{DynamicResource AppBorderBrush}"/>
            </Style>

            <Style TargetType="DataGrid">
                <Setter Property="Background" Value="{DynamicResource AppControlBackgroundBrush}"/>
                <Setter Property="Foreground" Value="{DynamicResource AppForegroundBrush}"/>
                <Setter Property="BorderBrush" Value="{DynamicResource AppBorderBrush}"/>
            </Style>

            <!-- 白色按钮样式 - 修复工具栏按钮主题问题 -->
            <Style x:Key="WhiteButton" TargetType="Button">
                <Setter Property="Background" Value="{DynamicResource AppControlBackgroundBrush}"/>
                <Setter Property="Foreground" Value="{DynamicResource AppForegroundBrush}"/>
                <Setter Property="BorderBrush" Value="{DynamicResource AppBorderBrush}"/>
                <Setter Property="BorderThickness" Value="1"/>
                <Setter Property="Padding" Value="12,6"/>
                <Setter Property="MinHeight" Value="32"/>
                <Setter Property="FontWeight" Value="Normal"/>
                <Setter Property="Cursor" Value="Hand"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="Button">
                            <Border Background="{TemplateBinding Background}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    CornerRadius="4"
                                    Padding="{TemplateBinding Padding}">
                                <ContentPresenter HorizontalAlignment="Center" 
                                                VerticalAlignment="Center"/>
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="{DynamicResource AppBorderBrush}"/>
                                </Trigger>
                                <Trigger Property="IsPressed" Value="True">
                                    <Setter Property="Background" Value="{DynamicResource AppBorderBrush}"/>
                                    <Setter Property="Opacity" Value="0.8"/>
                                </Trigger>
                                <Trigger Property="IsEnabled" Value="False">
                                    <Setter Property="Opacity" Value="0.5"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>

            <!-- 默认按钮样式 -->
            <Style x:Key="DefaultButtonStyle" TargetType="Button">
                <Setter Property="Background" Value="{DynamicResource AppControlBackgroundBrush}"/>
                <Setter Property="Foreground" Value="{DynamicResource AppForegroundBrush}"/>
                <Setter Property="BorderBrush" Value="{DynamicResource AppBorderBrush}"/>
                <Setter Property="BorderThickness" Value="1"/>
                <Setter Property="Padding" Value="8,4"/>
                <Setter Property="MinHeight" Value="28"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="Button">
                            <Border Background="{TemplateBinding Background}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    CornerRadius="3"
                                    Padding="{TemplateBinding Padding}">
                                <ContentPresenter HorizontalAlignment="Center" 
                                                VerticalAlignment="Center"/>
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="{DynamicResource AppBorderBrush}"/>
                                </Trigger>
                                <Trigger Property="IsPressed" Value="True">
                                    <Setter Property="Background" Value="{DynamicResource AppBorderBrush}"/>
                                    <Setter Property="Opacity" Value="0.8"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>

            <!-- 强调按钮样式 -->
            <Style x:Key="AccentButtonStyle" TargetType="Button">
                <Setter Property="Background" Value="{DynamicResource AppAccentBrush}"/>
                <Setter Property="Foreground" Value="{DynamicResource CheckBoxMarkBrush}"/>
                <Setter Property="BorderBrush" Value="{DynamicResource AppAccentBrush}"/>
                <Setter Property="BorderThickness" Value="1"/>
                <Setter Property="Padding" Value="12,6"/>
                <Setter Property="MinHeight" Value="32"/>
                <Setter Property="FontWeight" Value="SemiBold"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="Button">
                            <Border Background="{TemplateBinding Background}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    CornerRadius="4"
                                    Padding="{TemplateBinding Padding}">
                                <ContentPresenter HorizontalAlignment="Center" 
                                                VerticalAlignment="Center"/>
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="{DynamicResource AppAccentHoverBrush}"/>
                                </Trigger>
                                <Trigger Property="IsPressed" Value="True">
                                    <Setter Property="Background" Value="{DynamicResource AppAccentPressedBrush}"/>
                                </Trigger>
                                <Trigger Property="IsEnabled" Value="False">
                                    <Setter Property="Opacity" Value="0.5"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>

            <!-- 圆角文本框样式 -->
            <Style x:Key="RoundedTextBox" TargetType="TextBox">
                <Setter Property="Background" Value="{DynamicResource AppControlBackgroundBrush}"/>
                <Setter Property="Foreground" Value="{DynamicResource AppForegroundBrush}"/>
                <Setter Property="BorderBrush" Value="{DynamicResource AppBorderBrush}"/>
                <Setter Property="BorderThickness" Value="1"/>
                <Setter Property="Padding" Value="7,4"/>
                <Setter Property="MinHeight" Value="24"/>
                <Setter Property="FontSize" Value="12"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="TextBox">
                            <Border Background="{TemplateBinding Background}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    CornerRadius="4">
                                <ScrollViewer x:Name="PART_ContentHost" 
                                            Margin="{TemplateBinding Padding}"
                                            VerticalAlignment="Center"/>
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsFocused" Value="True">
                                    <Setter Property="BorderBrush" Value="{DynamicResource AppAccentBrush}"/>
                                    <Setter Property="BorderThickness" Value="2"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>

            <!-- 参数文本样式 -->
            <Style x:Key="ParameterTextBlock" TargetType="TextBlock">
                <Setter Property="Foreground" Value="{DynamicResource AppForegroundBrush}"/>
                <Setter Property="FontSize" Value="15"/>
                <Setter Property="TextWrapping" Value="Wrap"/>
                <Setter Property="VerticalAlignment" Value="Center"/>
            </Style>

            <!-- 参数标签样式 -->
            <Style x:Key="ParameterLabel" TargetType="TextBlock">
                <Setter Property="Foreground" Value="{DynamicResource AppForegroundBrush}"/>
                <Setter Property="FontSize" Value="12"/>
                <Setter Property="VerticalAlignment" Value="Center"/>
                <Setter Property="FontWeight" Value="Normal"/>
            </Style>

            <!-- 基础文本样式 -->
            <Style x:Key="BaseTextBlockStyle" TargetType="TextBlock">
                <Setter Property="Foreground" Value="{DynamicResource AppForegroundBrush}"/>
                <Setter Property="FontSize" Value="14"/>
                <Setter Property="VerticalAlignment" Value="Center"/>
            </Style>
        </ResourceDictionary>
    </Application.Resources>
</Application>