using LIVAnalyzer.Models;

namespace LIVAnalyzer.UI.ViewModels
{
    /// <summary>
    /// 平滑算法视图模型
    /// </summary>
    public class SmoothingAlgorithmViewModel
    {
        public SmoothingAlgorithmType AlgorithmType { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public bool SupportsRealTimePreview { get; set; }
        
        public SmoothingAlgorithmViewModel(SmoothingAlgorithmType algorithmType, string name, string description, bool supportsRealTimePreview = true)
        {
            AlgorithmType = algorithmType;
            Name = name;
            Description = description;
            SupportsRealTimePreview = supportsRealTimePreview;
        }
        
        public override string ToString() => Name;
    }
}