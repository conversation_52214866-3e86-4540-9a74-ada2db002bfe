# 📖 LIV Analyzer v2.2.3 - 详细使用指南

## 🎯 软件概述

LIV Analyzer v2.2.3 是一款专业的激光器LIV特性曲线分析工具，基于最新的.NET 9和WPF技术开发，采用原生Fluent Design设计语言。本版本引入了革命性的渐进式数据加载技术，实现了所有数据类型的即时响应，显著提升了数据加载速度和用户体验。

## 🆕 v2.2.3 新特性

### 🚀 革命性的渐进式数据加载
- **数据点渐进式读取**: 实现真正的数据点级别渐进式加载技术
- **加载速度提升100倍**: 从15-20秒等待降低到0.1秒首次响应
- **所有数据类型支持**: LIV、光谱、发散角数据全部支持渐进式加载
- **Excel文件优化**: Excel文件所有工作表数据完整的渐进式加载支持

### ⚡ 用户体验革命性提升
- **立即响应**: 选择文件后0.1秒内看到图表
- **无进度条干扰**: 去掉所有进度显示，界面保持简洁
- **可立即分析**: 不需要等待完整加载就能开始数据分析
- **参数实时计算**: 数据加载过程中参数同步计算显示

### 主要功能特性
- **LIV特性分析** - 阈值电流（一阶导数法）、斜率效率、串联电阻计算
- **光谱数据分析** - 峰值波长、FWHM（插值法）计算
- **发散角分析** - 水平/垂直方向发散角测量，支持导出到Excel
- **效率曲线分析** - 实时效率计算和可视化，支持第三坐标轴显示
- **高性能处理** - 并行文件加载，批量Excel读取，显著提升处理速度
- **智能图表更新** - 增量更新机制，消除闪烁，自动调整显示范围
- **批量数据处理** - 支持文件夹批量分析，多文件并行处理
- **交互式图表** - 高质量图表显示和导出，支持高级自定义设置
- **实时参数显示** - 选择曲线时实时显示对应参数
- **增强错误处理** - 智能Excel格式处理，自动数据清理和错误恢复
- **原生Fluent Design** - 采用.NET 9原生Fluent Design系统，提供现代化用户体验

## 🚀 快速开始

### 系统要求
- **操作系统**: Windows 10/11 (x64)
- **运行环境**: .NET 9 Runtime（开发需要SDK）
- **内存**: 建议8GB以上
- **硬盘**: 至少1GB可用空间
- **显卡**: 支持DirectX 11或更高版本（用于Fluent Design效果）

### 启动应用程序

#### 方法一：使用运行向导（推荐新手）
1. 双击 `运行向导.bat`
2. 按提示检查和安装.NET 9环境
3. 选择 `R` 运行应用程序

#### 方法二：直接启动
1. 双击 `启动LIV分析工具.bat`
2. 或运行 `LIVAnalyzer.exe`（发布版本）

#### 方法三：开发环境
```bash
# 命令行方式
dotnet run --project LIVAnalyzer.UI

# 或使用Visual Studio 2022
# 双击LIVAnalyzer.sln，设置LIVAnalyzer.UI为启动项目，按F5
```

### Fluent Design 特性
本软件采用.NET 9原生Fluent Design系统，提供以下现代化体验：
- **亚克力材质效果** - 半透明背景和模糊效果
- **流畅动画** - 平滑的过渡和交互动画
- **自适应主题** - 支持浅色、深色和跟随系统主题
- **现代化控件** - 圆角按钮、卡片式布局、悬浮效果
- **响应式设计** - 适配不同屏幕尺寸和DPI设置

## 📁 数据文件格式要求

### Excel文件格式 (.xlsx, .xls)

软件支持标准的Excel文件，要求包含以下工作表：

#### 必需工作表：
1. **wavelength** - 波长数据表
   - 列A: 波长值 (nm)
   - 列B: 强度值

2. **power** - 功率数据表
   - 列A: 电流值 (A)
   - 列B: 功率值 (W)

3. **voltage** - 电压数据表
   - 列A: 电流值 (A)
   - 列B: 电压值 (V)

#### 可选工作表（发散角分析）：
4. **HFF** - 水平发散角数据
   - 列A: 角度值 (度)
   - 列B: 强度值

5. **VFF** - 垂直发散角数据
   - 列A: 角度值 (度)
   - 列B: 强度值

### CSV文件格式 (.csv)

支持标准的CSV格式，列结构与Excel相同，文件命名规则：
- `filename_wavelength.csv` - 光谱数据
- `filename_power.csv` - 功率数据
- `filename_voltage.csv` - 电压数据
- `filename_HFF.csv` - 水平发散角数据
- `filename_VFF.csv` - 垂直发散角数据

## 🖥️ 界面操作指南

### 主界面布局

#### 1. 菜单栏
- **文件菜单**
  - `打开 (Ctrl+O)` - 选择数据文件
  - `COS文件转换工具` - 转换COS格式文件
  - `导出数据 (Ctrl+E)` - 导出分析结果
  - `保存图表 (Ctrl+S)` - 保存当前图表

- **帮助菜单**
  - `使用指南 (F1)` - 查看使用说明
  - `技术文档 (F2)` - 查看技术文档
  - `发布说明` - 查看版本更新信息
  - `关于` - 软件版本信息

#### 2. 工具栏
- **数据操作组**
  - `选择数据文件` - 打开文件选择对话框
  - `导出数据` - 导出当前分析结果
  - `保存图表` - 保存当前显示的图表

- **参数设置组**
  - `I1电流` - 设置第一个参考电流点
  - `I2电流` - 设置第二个参考电流点
  - `查询电流` - 设置功率查询的电流值
  - `平滑处理` - 启用/禁用数据平滑
  - `窗口大小` - 设置平滑窗口大小

#### 3. 文件列表区域
- 显示已加载的数据文件
- 支持多选和单选
- 显示文件处理状态
- 右键菜单提供更多操作

#### 4. 图表显示区域
包含多个标签页：
- **LIV曲线** - 电流-功率和电流-电压曲线
- **光谱图** - 波长-强度曲线
- **效率曲线** - 电流-效率曲线
- **发散角** - 角度-强度曲线

#### 5. 参数显示区域
- **计算参数** - 显示LIV分析结果
- **查询结果** - 显示指定电流下的功率值
- **状态信息** - 显示当前操作状态

### 基本操作流程

#### 步骤1：加载数据文件
1. 点击 `选择数据文件` 按钮
2. 在文件对话框中选择Excel或CSV文件
3. 支持多文件同时选择（按住Ctrl键）
4. 确认选择后，软件自动加载和解析数据

#### 步骤2：查看和分析数据
1. 在文件列表中选择要分析的文件
2. 切换不同的图表标签页查看数据
3. 使用鼠标滚轮缩放图表
4. 拖拽图表进行平移
5. 查看右侧参数面板的计算结果

#### 步骤3：调整分析参数
1. **设置I1/I2电流值**
   - 用于计算斜率效率的电流区间
   - 默认值：I1=0.5A, I2=1.0A
   - 可根据实际数据调整



3. **平滑处理**
   - 启用平滑可减少数据噪声
   - 调整窗口大小控制平滑程度
   - 窗口越大平滑效果越强

#### 步骤4：导出结果
1. **导出数据**
   - 选择要导出的文件（可多选）
   - 点击 `导出数据` 按钮
   - 选择保存位置和文件名
   - 生成包含所有分析结果的Excel文件

2. **保存图表**
   - 点击 `保存图表` 按钮
   - 选择图片格式（PNG/SVG）
   - 设置保存位置和文件名

## 📊 分析参数说明

### LIV参数
- **峰值波长** - 光谱强度最大值对应的波长
- **FWHM** - 光谱半高宽，表征光谱线宽
- **阈值电流** - 激光器开始激射的电流值
- **最大功率** - 测量范围内的最大输出功率
- **最大效率** - P/(I×V)的最大值
- **斜率效率** - I1-I2区间内功率对电流的斜率
- **串联电阻** - 电流-电压曲线的线性拟合电阻
- **微分电阻** - 动态电阻值

### 发散角参数
- **FWHM** - 半高宽发散角
- **FW86.5%** - 86.5%功率包含角
- **FW95%** - 95%功率包含角
- 分别计算水平和垂直方向



## 🔧 高级功能

### 批量处理
1. 选择包含多个数据文件的文件夹
2. 设置统一的I1/I2参数
3. 软件自动处理所有文件
4. 生成汇总报告

### COS文件转换
1. 点击菜单 `文件` → `COS文件转换工具`
2. 选择COS格式文件
3. 转换为标准Excel格式
4. 可直接用于LIV分析

### 配置管理
软件配置文件位置：`%AppData%\LIVAnalyzer\config.yaml`

可配置项目：
- 默认参数值
- 界面显示选项
- 性能设置
- 文件路径记忆

## 🐛 常见问题解决

### 文件加载问题
**问题**: 提示"文件格式不正确"
**解决**: 
- 检查Excel工作表名称是否正确
- 确认数据列格式为数值型
- 检查是否有空行或非数值数据

**问题**: CSV文件无法识别
**解决**:
- 确认文件编码为UTF-8
- 检查分隔符是否为逗号
- 确认文件命名符合规范

### 计算结果异常
**问题**: 阈值电流计算不准确
**解决**:
- 检查功率数据是否单调递增
- 尝试启用数据平滑
- 调整平滑窗口大小

**问题**: 发散角无法计算
**解决**:
- 确认HFF/VFF工作表存在
- 检查角度数据范围是否合理
- 确认强度数据不全为零

### 性能问题
**问题**: 软件运行缓慢
**解决**:
- 减少同时加载的文件数量
- 关闭不必要的图表标签页
- 检查系统内存使用情况

**问题**: 图表显示卡顿
**解决**:
- 启用数据平滑减少数据点
- 调整图表显示范围
- 关闭网格显示

## 📞 技术支持

### 日志文件位置
- 一般日志：`%AppData%\LIVAnalyzer\Logs\liv_analyzer.log`
- 错误日志：`%AppData%\LIVAnalyzer\Logs\liv_analyzer_error.log`

### 联系方式
- 开发者：00106
- 技术支持：查看软件内置帮助文档
- 问题反馈：通过软件菜单提交

## 🎨 图表操作详解

### 图表交互功能
1. **缩放操作**
   - 鼠标滚轮：放大/缩小
   - 鼠标右键拖拽：框选缩放
   - 双击：重置缩放

2. **平移操作**
   - 鼠标左键拖拽：移动图表
   - 键盘方向键：精确移动

3. **数据点查看**
   - 鼠标悬停：显示数据点坐标
   - 右键菜单：复制坐标值

4. **图例控制**
   - 点击图例项：显示/隐藏对应曲线
   - 右键图例：更多显示选项

### 图表自定义
1. **显示选项**
   - 网格线：显示/隐藏坐标网格
   - 图例：控制图例位置和显示
   - 坐标轴：自定义轴标题和范围

2. **线条样式**
   - 线型：实线、虚线、点线
   - 颜色：自动分配或手动设置
   - 粗细：可调节线条宽度

## 📈 数据分析技巧

### LIV曲线分析要点
1. **阈值电流识别**
   - 观察功率曲线的拐点
   - 使用二阶导数方法自动检测
   - 手动调整平滑参数优化检测

2. **斜率效率计算**
   - 选择线性区间（通常在阈值电流之上）
   - I1应略大于阈值电流
   - I2应在线性区间内，避免热饱和区

3. **串联电阻分析**
   - 使用高电流区域的I-V数据
   - 线性拟合的R²值应大于0.95
   - 异常值可能表明接触问题

### 光谱分析技巧
1. **峰值波长测量**
   - 确保光谱数据覆盖完整峰值
   - 注意多峰结构的识别
   - 使用平滑处理减少噪声影响

2. **FWHM计算**
   - 半高宽反映光谱线宽
   - 较小的FWHM表示更好的单色性
   - 注意基线校正的影响

### 发散角分析技巧
1. **数据质量检查**
   - 确保角度扫描范围足够
   - 检查数据的对称性
   - 注意背景噪声的影响

2. **多参数对比**
   - FWHM：工程常用参数
   - FW86.5%：更严格的光束质量指标
   - FW95%：包含绝大部分光功率

## 🔄 工作流程建议

### 标准分析流程
1. **数据预处理**
   ```
   加载文件 → 检查数据质量 → 设置平滑参数
   ```

2. **参数设置**
   ```
   设置I1/I2 → 检查阈值电流 → 调整分析区间
   ```

3. **结果验证**
   ```
   查看拟合质量 → 对比理论值 → 检查异常点
   ```

4. **结果导出**
   ```
   选择导出文件 → 生成报告 → 保存图表
   ```

### 批量处理流程
1. **准备工作**
   - 整理数据文件到同一文件夹
   - 统一文件命名规范
   - 设置统一的分析参数

2. **批量执行**
   - 选择文件夹路径
   - 设置I1/I2参数
   - 启动批量处理

3. **结果检查**
   - 查看处理日志
   - 检查异常文件
   - 验证关键参数

## 🛠️ 故障排除指南

### 启动问题
**症状**: 软件无法启动
**排查步骤**:
1. 检查.NET 6运行时是否安装
2. 查看Windows事件日志
3. 以管理员权限运行
4. 检查防病毒软件拦截

**症状**: 界面显示异常
**排查步骤**:
1. 检查显示器分辨率和缩放
2. 更新显卡驱动程序
3. 重置窗口配置
4. 重新安装软件

### 数据处理问题
**症状**: 计算结果明显错误
**排查步骤**:
1. 检查原始数据质量
2. 验证单位换算
3. 对比手工计算结果
4. 查看详细日志信息

**症状**: 处理速度很慢
**排查步骤**:
1. 检查文件大小和数据点数量
2. 关闭不必要的后台程序
3. 增加系统内存
4. 使用SSD硬盘

### 文件格式问题
**症状**: Excel文件无法读取
**排查步骤**:
1. 用Excel软件验证文件完整性
2. 检查工作表名称拼写
3. 确认数据列没有合并单元格
4. 转换为较新的Excel格式

**症状**: CSV文件乱码
**排查步骤**:
1. 检查文件编码格式
2. 使用UTF-8编码保存
3. 确认分隔符为英文逗号
4. 检查数值格式（小数点vs逗号）

## 📋 最佳实践建议

### 数据准备
1. **文件组织**
   - 使用清晰的文件命名规范
   - 按项目或日期分类存储
   - 保留原始数据备份

2. **数据质量**
   - 测量前校准设备
   - 确保数据采样密度足够
   - 记录测量条件和参数

### 分析设置
1. **参数选择**
   - I1设置为阈值电流的1.2-1.5倍
   - I2设置在线性区间内
   - 平滑窗口大小通常为5-15

2. **结果验证**
   - 对比多次测量结果
   - 检查参数的物理合理性
   - 与理论计算或仿真对比

### 报告生成
1. **数据导出**
   - 包含原始数据和计算结果
   - 添加测量条件说明
   - 保存高质量图表

2. **结果展示**
   - 使用统一的图表格式
   - 标注关键参数值
   - 提供误差分析

## 📚 附录

### A. 快捷键列表
| 快捷键 | 功能 | 说明 |
|--------|------|------|
| Ctrl+O | 打开文件 | 打开文件选择对话框 |
| Ctrl+E | 导出数据 | 导出当前分析结果 |
| Ctrl+S | 保存图表 | 保存当前显示的图表 |
| F1 | 使用指南 | 打开本使用指南 |
| F2 | 技术文档 | 打开技术文档 |
| F5 | 刷新 | 重新计算当前数据 |
| Delete | 删除文件 | 从列表中移除选中文件 |
| Ctrl+A | 全选 | 选择所有已加载文件 |

### B. 配置文件说明
配置文件位置：`%AppData%\LIVAnalyzer\config.yaml`

```yaml
# 数据处理配置
DataProcessing:
  Smoothing:
    EnableByDefault: false    # 默认启用平滑
    DefaultWindowSize: 5      # 默认平滑窗口大小
    MinWindowSize: 3          # 最小窗口大小
    MaxWindowSize: 51         # 最大窗口大小

  Threshold:
    DefaultThreshold: 0.01    # 默认阈值
    MinThreshold: 0.001       # 最小阈值
    MaxThreshold: 1.0         # 最大阈值



  BatchProcessing:
    DefaultI1: 0.5            # 默认I1电流
    DefaultI2: 1.0            # 默认I2电流

# 显示配置
Display:
  ShowGrid: true              # 显示网格
  ShowLegend: false           # 显示图例
  ShowCoordinates: true       # 显示坐标
  DefaultLineStyle: "line"    # 默认线型

# 性能配置
Performance:
  MaxFiles: 1000              # 最大文件数
  MaxFileSizeMB: 500          # 最大文件大小(MB)

# 界面配置
UI:
  LastFilePath: ""            # 上次文件路径
  LastFolderPath: ""          # 上次文件夹路径
  Window:
    Width: 1280               # 窗口宽度
    Height: 720               # 窗口高度
    MinWidth: 1024            # 最小宽度
    MinHeight: 680            # 最小高度
```

### C. 文件格式模板

#### Excel文件模板结构
```
工作簿: sample_data.xlsx
├── wavelength (工作表)
│   ├── A列: 波长 (nm)
│   └── B列: 强度
├── power (工作表)
│   ├── A列: 电流 (A)
│   └── B列: 功率 (W)
├── voltage (工作表)
│   ├── A列: 电流 (A)
│   └── B列: 电压 (V)
├── HFF (工作表，可选)
│   ├── A列: 角度 (度)
│   └── B列: 强度
└── VFF (工作表，可选)
    ├── A列: 角度 (度)
    └── B列: 强度
```

#### CSV文件命名规范
```
基础文件名: sample_001
├── sample_001_wavelength.csv  # 光谱数据
├── sample_001_power.csv       # 功率数据
├── sample_001_voltage.csv     # 电压数据
├── sample_001_HFF.csv         # 水平发散角(可选)
└── sample_001_VFF.csv         # 垂直发散角(可选)
```

### D. 常用参数参考值

#### 典型激光器参数范围
| 参数 | 单位 | 典型范围 | 说明 |
|------|------|----------|------|
| 阈值电流 | A | 0.01-10 | 取决于器件类型和尺寸 |
| 斜率效率 | W/A | 0.1-2.0 | 量子效率和光学损耗相关 |
| 串联电阻 | Ω | 0.1-50 | 接触电阻和材料电阻 |
| 峰值波长 | nm | 400-2000 | 材料带隙决定 |
| 光谱FWHM | nm | 0.1-10 | 激光器类型相关 |
| 发散角FWHM | 度 | 5-60 | 波导结构决定 |

#### 数据质量指标
| 指标 | 良好 | 可接受 | 需改进 |
|------|------|--------|--------|
| 串联电阻R² | >0.98 | 0.95-0.98 | <0.95 |
| 数据点数量 | >100 | 50-100 | <50 |
| 信噪比 | >20dB | 10-20dB | <10dB |
| 测量重复性 | <2% | 2-5% | >5% |

### E. 错误代码说明

#### 文件加载错误
- **E001**: 文件不存在或无法访问
- **E002**: 文件格式不支持
- **E003**: Excel工作表缺失
- **E004**: 数据列格式错误
- **E005**: 数据为空或全零

#### 计算错误
- **E101**: 阈值电流计算失败
- **E102**: 斜率效率计算失败
- **E103**: 串联电阻拟合失败
- **E104**: 发散角计算失败
- **E105**: 数据点不足

#### 系统错误
- **E201**: 内存不足
- **E202**: 磁盘空间不足
- **E203**: 权限不足
- **E204**: 网络连接问题

### F. 版本更新历史

#### V3.0.0 (当前版本 - 2025-07-25)
1. **框架升级**
   - 升级到.NET 9.0，享受最新性能优化和语言特性
   - 采用原生Fluent Design系统，提供现代化用户体验
   - 支持最新的C# 13语言特性

2. **Fluent Design界面**
   - 全新的亚克力材质效果和模糊背景
   - 流畅的动画过渡和微交互
   - 自适应主题系统（浅色/深色/跟随系统）
   - 现代化的圆角控件和卡片式布局

3. **性能提升**
   - .NET 9的AOT编译支持，启动速度提升50%
   - 改进的垃圾回收机制，内存使用更高效
   - 原生ARM64支持，在新款Windows设备上性能更佳

4. **用户体验优化**
   - 响应式设计，适配高DPI显示器
   - 改进的触摸和手势支持
   - 无障碍功能增强，支持屏幕阅读器

5. **开发者信息**
   - 开发者：00106
   - 更新日期：2025年7月25日
   - 基于.NET 9.0和原生Fluent Design

---

## 📞 获取帮助

### 内置帮助
- 按 `F1` 查看使用指南
- 按 `F2` 查看技术文档
- 菜单 → 帮助 → 关于

### 技术支持
- 开发者：00106
- 项目地址：[GitHub Repository]
- 问题反馈：[Issues Page]

### 学习资源
- 激光器基础知识
- LIV测试原理
- 数据分析方法
- 软件开发文档

---

**感谢您使用LIV Analyzer v2.2.3！体验革命性的渐进式数据加载技术，让激光器研发工作更高效！** 🚀

*最后更新：2025年8月8日 | 版本：v2.2.3 (渐进式加载优化版本) | 开发者：00106*
