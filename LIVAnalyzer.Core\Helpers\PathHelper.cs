using System;
using System.IO;
using System.Runtime.InteropServices;

namespace LIVAnalyzer.Core.Helpers
{
    /// <summary>
    /// 路径处理帮助类
    /// </summary>
    public static class PathHelper
    {
        /// <summary>
        /// 标准化路径格式
        /// </summary>
        /// <param name="path">原始路径</param>
        /// <returns>标准化后的路径</returns>
        public static string NormalizePath(string path)
        {
            if (string.IsNullOrWhiteSpace(path))
                return path;

            // 替换正斜杠为反斜杠（Windows标准）
            path = path.Replace('/', Path.DirectorySeparatorChar);
            
            // 处理路径中的多余分隔符
            while (path.Contains($"{Path.DirectorySeparatorChar}{Path.DirectorySeparatorChar}"))
            {
                path = path.Replace($"{Path.DirectorySeparatorChar}{Path.DirectorySeparatorChar}", 
                    Path.DirectorySeparatorChar.ToString());
            }

            // 规范化驱动器号（确保大写）
            if (path.Length >= 2 && path[1] == ':' && char.IsLetter(path[0]))
            {
                path = char.ToUpper(path[0]) + path.Substring(1);
            }

            // 获取完整路径（解析相对路径）
            try
            {
                if (!Path.IsPathRooted(path))
                {
                    path = Path.GetFullPath(path);
                }
            }
            catch
            {
                // 如果路径解析失败，返回原始路径
            }

            return path;
        }

        /// <summary>
        /// 获取相对于应用程序根目录的路径
        /// </summary>
        /// <param name="relativePath">相对路径</param>
        /// <returns>完整路径</returns>
        public static string GetAppRelativePath(string relativePath)
        {
            var appDirectory = AppDomain.CurrentDomain.BaseDirectory;
            return Path.Combine(appDirectory, relativePath);
        }

        /// <summary>
        /// 确保目录存在
        /// </summary>
        /// <param name="path">目录路径</param>
        public static void EnsureDirectoryExists(string path)
        {
            var directory = Path.GetDirectoryName(path);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }
        }

        /// <summary>
        /// 验证文件是否存在
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>文件是否存在</returns>
        public static bool ValidateFileExists(string filePath)
        {
            var normalizedPath = NormalizePath(filePath);
            return File.Exists(normalizedPath);
        }

        /// <summary>
        /// 获取项目根目录下的pic文件夹路径
        /// </summary>
        /// <returns>pic文件夹的完整路径</returns>
        public static string GetPictureFolderPath()
        {
            // 从当前目录向上查找，直到找到pic文件夹
            var currentDir = AppDomain.CurrentDomain.BaseDirectory;
            
            while (!string.IsNullOrEmpty(currentDir))
            {
                var picPath = Path.Combine(currentDir, "pic");
                if (Directory.Exists(picPath))
                {
                    return picPath;
                }
                
                var parentDir = Directory.GetParent(currentDir);
                if (parentDir == null)
                    break;
                    
                currentDir = parentDir.FullName;
            }
            
            // 如果没找到，返回默认路径
            return Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "pic");
        }

        /// <summary>
        /// 获取图片文件的完整路径
        /// </summary>
        /// <param name="imageName">图片文件名（如 "12.png"）</param>
        /// <returns>图片的完整路径</returns>
        public static string GetImagePath(string imageName)
        {
            var picFolder = GetPictureFolderPath();
            return Path.Combine(picFolder, imageName);
        }
    }
}