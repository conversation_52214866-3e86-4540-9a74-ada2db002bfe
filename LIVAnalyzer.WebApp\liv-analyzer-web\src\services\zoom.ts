export async function enableZoomPan(container: HTMLElement): Promise<{ dispose: () => void; reset: () => void; }> {
  const svg = container.querySelector('svg');
  if (!svg) return { dispose: () => {}, reset: () => {} };
  const mod = await import('d3');
  const d3 = mod as any;
  const selection = d3.select(svg);
  const g = svg.querySelector('g');
  if (!g) return { dispose: () => {}, reset: () => {} };
  const zoom = d3.zoom().scaleExtent([1, 20]).on('zoom', (event: any) => {
    (g as SVGGElement).setAttribute('transform', event.transform.toString());
  });
  selection.call(zoom as any);
  const dispose = () => { selection.on('.zoom', null); };
  const reset = () => { selection.transition().duration(0).call(zoom.transform, d3.zoomIdentity); };
  return { dispose, reset };
}


