@echo off
chcp 65001 >nul
echo ========================================
echo    LIV分析工具 - 编译和发布脚本
echo ========================================
echo.

cd /d "%~dp0"

echo [1/5] 清理之前的编译输出...
rd /s /q "LIVAnalyzer.UI\bin" 2>nul
rd /s /q "LIVAnalyzer.UI\obj" 2>nul
rd /s /q "LIVAnalyzer.Core\bin" 2>nul
rd /s /q "LIVAnalyzer.Core\obj" 2>nul
rd /s /q "LIVAnalyzer.Data\bin" 2>nul
rd /s /q "LIVAnalyzer.Data\obj" 2>nul
rd /s /q "LIVAnalyzer.Models\bin" 2>nul
rd /s /q "LIVAnalyzer.Models\obj" 2>nul
rd /s /q "LIVAnalyzer.Services\bin" 2>nul
rd /s /q "LIVAnalyzer.Services\obj" 2>nul
rd /s /q "publish-release" 2>nul
echo 清理完成！

echo.
echo [2/5] 恢复NuGet包...
dotnet restore
if %errorlevel% neq 0 (
    echo 错误：NuGet包恢复失败！
    pause
    exit /b 1
)
echo NuGet包恢复完成！

echo.
echo [3/5] 编译项目（Release模式）...
dotnet build --configuration Release --no-restore
if %errorlevel% neq 0 (
    echo 错误：项目编译失败！
    pause
    exit /b 1
)
echo 编译完成！

echo.
echo [4/5] 发布自包含应用程序...
dotnet publish LIVAnalyzer.UI\LIVAnalyzer.UI.csproj ^
    --configuration Release ^
    --runtime win-x64 ^
    --self-contained true ^
    --output "publish-release" ^
    --property:PublishSingleFile=true ^
    --property:IncludeNativeLibrariesForSelfExtract=true ^
    --property:PublishTrimmed=false ^
    --property:PublishReadyToRun=true

if %errorlevel% neq 0 (
    echo 错误：发布失败！
    pause
    exit /b 1
)
echo 发布完成！

echo.
echo [5/5] 复制资源文件...
if exist "Resources" (
    xcopy "Resources" "publish-release\Resources\" /E /I /Y >nul 2>&1
)
if exist "使用指南.md" (
    copy "使用指南.md" "publish-release\" >nul 2>&1
)
if exist "快速开始指南.md" (
    copy "快速开始指南.md" "publish-release\" >nul 2>&1
)

echo.
echo ========================================
echo           发布成功完成！
echo ========================================
echo.
echo 输出位置: %CD%\publish-release\
echo 主程序: LIVAnalyzer.exe
echo.
echo 文件大小:
for %%f in ("publish-release\LIVAnalyzer.exe") do echo   LIVAnalyzer.exe: %%~zf 字节
echo.

REM 询问是否打开发布目录
set /p choice="是否打开发布目录？(Y/N): "
if /i "%choice%"=="Y" (
    explorer "publish-release"
)

echo.
echo 按任意键退出...
pause >nul
