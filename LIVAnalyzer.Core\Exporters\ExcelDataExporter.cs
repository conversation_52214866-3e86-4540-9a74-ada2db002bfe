using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using LIVAnalyzer.Models;
using LIVAnalyzer.Services.Logging;
using OfficeOpenXml;

namespace LIVAnalyzer.Core.Exporters
{
    /// <summary>
    /// Excel数据导出器，与Python版本输出格式保持一致
    /// </summary>
    public class ExcelDataExporter
    {
        public async Task<bool> ExportDataAsync(List<FileDataModel> selectedFiles, string filePath)
        {
            try
            {
                LoggingService.LogInformation($"开始导出数据到: {filePath}");

                using var package = new ExcelPackage();

                // 创建汇总工作表
                CreateSummarySheet(package, selectedFiles);

                // 为每个文件创建详细数据工作表
                foreach (var file in selectedFiles)
                {
                    await CreateDetailSheet(package, file);
                }

                // 保存文件
                var fileInfo = new FileInfo(filePath);
                await package.SaveAsAsync(fileInfo);

                LoggingService.LogInformation($"数据导出完成: {filePath}");
                return true;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "导出数据失败");
                throw;
            }
        }

        private void CreateSummarySheet(ExcelPackage package, List<FileDataModel> selectedFiles)
        {
            var worksheet = package.Workbook.Worksheets.Add("汇总结果");

            // 设置表头
            var headers = new[]
            {
                "文件名", "峰值波长 (nm)", "FWHM (nm)", "阈值电流 (A)",
                "最大功率 (W)", "最大效率 (%)", "斜率效率 (W/A)",
                "串联电阻 (Ω)", "拟合优度 (R²)", "I1电流 (A)", "I1功率 (W)", "I1电压 (V)",
                "I2电流 (A)", "I2功率 (W)", "I2电压 (V)",
                "水平FWHM (°)", "水平FW(1/e²) (°)", "水平1/e²能量占比", "水平FW95% (°)",
                "垂直FWHM (°)", "垂直FW(1/e²) (°)", "垂直1/e²能量占比", "垂直FW95% (°)"
            };

            for (int i = 0; i < headers.Length; i++)
            {
                worksheet.Cells[1, i + 1].Value = headers[i];
                worksheet.Cells[1, i + 1].Style.Font.Bold = true;
                worksheet.Cells[1, i + 1].Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                worksheet.Cells[1, i + 1].Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
            }

            // 填充数据
            int row = 2;
            foreach (var file in selectedFiles)
            {
                var parameters = file.Data.Parameters;
                if (parameters != null)
                {
                    worksheet.Cells[row, 1].Value = file.FileName;
                    worksheet.Cells[row, 2].Value = parameters.PeakWavelength;
                    worksheet.Cells[row, 3].Value = parameters.FWHM;
                    worksheet.Cells[row, 4].Value = parameters.ThresholdCurrent;
                    worksheet.Cells[row, 5].Value = parameters.MaxPower;
                    worksheet.Cells[row, 6].Value = parameters.MaxEfficiency;
                    worksheet.Cells[row, 7].Value = parameters.SlopeEfficiency;
                    worksheet.Cells[row, 8].Value = parameters.SeriesResistance ?? 0;
                    worksheet.Cells[row, 9].Value = parameters.SeriesResistanceR2 ?? 0;
                    worksheet.Cells[row, 10].Value = parameters.I1Current;
                    worksheet.Cells[row, 11].Value = parameters.I1Power;
                    worksheet.Cells[row, 12].Value = parameters.I1Voltage;
                    worksheet.Cells[row, 13].Value = parameters.I2Current;
                    worksheet.Cells[row, 14].Value = parameters.I2Power;
                    worksheet.Cells[row, 15].Value = parameters.I2Voltage;

                    // 添加发散角参数
                    if (file.Data.DivergenceResults != null)
                    {
                        var divergence = file.Data.DivergenceResults;
                        worksheet.Cells[row, 16].Value = divergence.HorizontalFWHM ?? 0;
                        worksheet.Cells[row, 17].Value = divergence.HorizontalFW1e2 ?? 0;
                        worksheet.Cells[row, 18].Value = divergence.HorizontalFW1e2PowerContainment ?? 0;
                        worksheet.Cells[row, 19].Value = divergence.HorizontalFW95 ?? 0;
                        worksheet.Cells[row, 20].Value = divergence.VerticalFWHM ?? 0;
                        worksheet.Cells[row, 21].Value = divergence.VerticalFW1e2 ?? 0;
                        worksheet.Cells[row, 22].Value = divergence.VerticalFW1e2PowerContainment ?? 0;
                        worksheet.Cells[row, 23].Value = divergence.VerticalFW95 ?? 0;
                    }
                    else
                    {
                        // 如果没有发散角数据，填入零值
                        for (int col = 16; col <= 23; col++)
                        {
                            worksheet.Cells[row, col].Value = 0;
                        }
                    }
                }
                row++;
            }

            // 自动调整列宽
            worksheet.Cells.AutoFitColumns();

            // 添加边框
            var range = worksheet.Cells[1, 1, row - 1, headers.Length];
            range.Style.Border.Top.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
            range.Style.Border.Bottom.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
            range.Style.Border.Left.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
            range.Style.Border.Right.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
        }

        private async Task CreateDetailSheet(ExcelPackage package, FileDataModel file)
        {
            // 使用安全的工作表名称（去除非法字符）
            var safeSheetName = GetSafeSheetName(file.FileName);

            // 检查工作表名称是否已存在，如果存在则添加序号
            var finalSheetName = GetUniqueSheetName(package, safeSheetName);
            var worksheet = package.Workbook.Worksheets.Add(finalSheetName);

            int currentRow = 1;

            // 添加文件信息
            worksheet.Cells[currentRow, 1].Value = "文件名:";
            worksheet.Cells[currentRow, 2].Value = file.FileName;
            worksheet.Cells[currentRow, 1].Style.Font.Bold = true;
            currentRow += 2;

            // 添加计算参数
            if (file.Data.Parameters != null)
            {
                worksheet.Cells[currentRow, 1].Value = "计算参数";
                worksheet.Cells[currentRow, 1].Style.Font.Bold = true;
                worksheet.Cells[currentRow, 1].Style.Font.Size = 14;
                currentRow++;

                var paramData = new[]
                {
                    new { Name = "峰值波长 (nm)", Value = file.Data.Parameters.PeakWavelength },
                    new { Name = "FWHM (nm)", Value = file.Data.Parameters.FWHM },
                    new { Name = "阈值电流 (A)", Value = file.Data.Parameters.ThresholdCurrent },
                    new { Name = "最大功率 (W)", Value = file.Data.Parameters.MaxPower },
                    new { Name = "最大效率 (%)", Value = file.Data.Parameters.MaxEfficiency },
                    new { Name = "斜率效率 (W/A)", Value = file.Data.Parameters.SlopeEfficiency },
                    new { Name = "串联电阻 (Ω)", Value = file.Data.Parameters.SeriesResistance ?? 0 },
                    new { Name = "拟合优度 (R²)", Value = file.Data.Parameters.SeriesResistanceR2 ?? 0 },
                    new { Name = "I1电流 (A)", Value = file.Data.Parameters.I1Current },
                    new { Name = "I1功率 (W)", Value = file.Data.Parameters.I1Power },
                    new { Name = "I2电流 (A)", Value = file.Data.Parameters.I2Current },
                    new { Name = "I2功率 (W)", Value = file.Data.Parameters.I2Power }
                };

                foreach (var param in paramData)
                {
                    worksheet.Cells[currentRow, 1].Value = param.Name;
                    worksheet.Cells[currentRow, 2].Value = param.Value;
                    worksheet.Cells[currentRow, 1].Style.Font.Bold = true;
                    currentRow++;
                }
                
                // 添加发散角参数
                if (file.Data.DivergenceResults != null)
                {
                    currentRow++; // 空一行
                    worksheet.Cells[currentRow, 1].Value = "发散角参数";
                    worksheet.Cells[currentRow, 1].Style.Font.Bold = true;
                    worksheet.Cells[currentRow, 1].Style.Font.Size = 12;
                    currentRow++;
                    
                    var divergenceData = new[]
                    {
                        new { Name = "水平FWHM (°)", Value = file.Data.DivergenceResults.HorizontalFWHM ?? 0 },
                        new { Name = "水平FW(1/e²) (°)", Value = file.Data.DivergenceResults.HorizontalFW1e2 ?? 0 },
                        new { Name = "水平1/e²能量占比", Value = file.Data.DivergenceResults.HorizontalFW1e2PowerContainment ?? 0 },
                        new { Name = "水平FW95% (°)", Value = file.Data.DivergenceResults.HorizontalFW95 ?? 0 },
                        new { Name = "垂直FWHM (°)", Value = file.Data.DivergenceResults.VerticalFWHM ?? 0 },
                        new { Name = "垂直FW(1/e²) (°)", Value = file.Data.DivergenceResults.VerticalFW1e2 ?? 0 },
                        new { Name = "垂直1/e²能量占比", Value = file.Data.DivergenceResults.VerticalFW1e2PowerContainment ?? 0 },
                        new { Name = "垂直FW95% (°)", Value = file.Data.DivergenceResults.VerticalFW95 ?? 0 }
                    };
                    
                    foreach (var divergence in divergenceData)
                    {
                        worksheet.Cells[currentRow, 1].Value = divergence.Name;
                        worksheet.Cells[currentRow, 2].Value = divergence.Value;
                        worksheet.Cells[currentRow, 1].Style.Font.Bold = true;
                        currentRow++;
                    }
                }
                currentRow++;
            }

            // 添加LIV数据
            if (file.Data.CurrentPowerData.Any() || file.Data.CurrentVoltageData.Any())
            {
                worksheet.Cells[currentRow, 1].Value = "LIV数据";
                worksheet.Cells[currentRow, 1].Style.Font.Bold = true;
                worksheet.Cells[currentRow, 1].Style.Font.Size = 14;
                currentRow++;

                // 表头
                worksheet.Cells[currentRow, 1].Value = "电流 (A)";
                worksheet.Cells[currentRow, 2].Value = "功率 (W)";
                worksheet.Cells[currentRow, 3].Value = "电压 (V)";
                worksheet.Cells[currentRow, 4].Value = "效率 (%)";

                for (int i = 1; i <= 4; i++)
                {
                    worksheet.Cells[currentRow, i].Style.Font.Bold = true;
                    worksheet.Cells[currentRow, i].Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                    worksheet.Cells[currentRow, i].Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightBlue);
                }
                currentRow++;

                // 合并功率和电压数据
                var mergedData = MergeLIVData(file.Data.CurrentPowerData, file.Data.CurrentVoltageData);
                foreach (var point in mergedData)
                {
                    worksheet.Cells[currentRow, 1].Value = point.Current;
                    worksheet.Cells[currentRow, 2].Value = point.Power;
                    worksheet.Cells[currentRow, 3].Value = point.Voltage;
                    worksheet.Cells[currentRow, 4].Value = point.Efficiency;
                    currentRow++;
                }
                currentRow++;
            }

            // 添加光谱数据
            if (file.Data.WavelengthIntensityData.Any())
            {
                worksheet.Cells[currentRow, 1].Value = "光谱数据";
                worksheet.Cells[currentRow, 1].Style.Font.Bold = true;
                worksheet.Cells[currentRow, 1].Style.Font.Size = 14;
                currentRow++;

                // 表头
                worksheet.Cells[currentRow, 1].Value = "波长 (nm)";
                worksheet.Cells[currentRow, 2].Value = "强度";

                for (int i = 1; i <= 2; i++)
                {
                    worksheet.Cells[currentRow, i].Style.Font.Bold = true;
                    worksheet.Cells[currentRow, i].Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                    worksheet.Cells[currentRow, i].Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGreen);
                }
                currentRow++;

                // 数据
                foreach (var point in file.Data.WavelengthIntensityData)
                {
                    worksheet.Cells[currentRow, 1].Value = point.X;
                    worksheet.Cells[currentRow, 2].Value = point.Y;
                    currentRow++;
                }
                currentRow++;
            }

            // 添加发散角数据
            if (file.Data.HorizontalDivergenceData?.Any() == true || file.Data.VerticalDivergenceData?.Any() == true)
            {
                worksheet.Cells[currentRow, 1].Value = "发散角数据";
                worksheet.Cells[currentRow, 1].Style.Font.Bold = true;
                worksheet.Cells[currentRow, 1].Style.Font.Size = 14;
                currentRow++;

                // 表头
                worksheet.Cells[currentRow, 1].Value = "角度 (°)";
                worksheet.Cells[currentRow, 2].Value = "水平强度";
                worksheet.Cells[currentRow, 3].Value = "垂直强度";

                for (int i = 1; i <= 3; i++)
                {
                    worksheet.Cells[currentRow, i].Style.Font.Bold = true;
                    worksheet.Cells[currentRow, i].Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                    worksheet.Cells[currentRow, i].Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightYellow);
                }
                currentRow++;

                // 合并发散角数据
                var mergedDivergenceData = MergeDivergenceData(file.Data.HorizontalDivergenceData, file.Data.VerticalDivergenceData);
                foreach (var point in mergedDivergenceData)
                {
                    worksheet.Cells[currentRow, 1].Value = point.Angle;
                    worksheet.Cells[currentRow, 2].Value = point.HorizontalIntensity;
                    worksheet.Cells[currentRow, 3].Value = point.VerticalIntensity;
                    currentRow++;
                }
            }

            // 自动调整列宽
            worksheet.Cells.AutoFitColumns();
        }

        private string GetSafeSheetName(string fileName)
        {
            // 移除文件扩展名
            var nameWithoutExtension = Path.GetFileNameWithoutExtension(fileName);

            // 替换Excel工作表名称中的非法字符
            var invalidChars = new char[] { '\\', '/', '*', '?', ':', '[', ']' };
            foreach (var c in invalidChars)
            {
                nameWithoutExtension = nameWithoutExtension.Replace(c, '_');
            }

            // 确保名称长度不超过31个字符（Excel限制）
            if (nameWithoutExtension.Length > 31)
            {
                nameWithoutExtension = nameWithoutExtension.Substring(0, 28) + "...";
            }

            return nameWithoutExtension;
        }

        private string GetUniqueSheetName(ExcelPackage package, string baseName)
        {
            var existingNames = package.Workbook.Worksheets.Select(ws => ws.Name).ToHashSet(StringComparer.OrdinalIgnoreCase);

            // 如果基础名称不存在，直接返回
            if (!existingNames.Contains(baseName))
            {
                return baseName;
            }

            // 如果存在重复，添加序号
            int counter = 1;
            string uniqueName;
            do
            {
                // 确保带序号的名称不超过31个字符
                var suffix = $"_{counter}";
                var maxBaseLength = 31 - suffix.Length;
                var truncatedBase = baseName.Length > maxBaseLength ? baseName.Substring(0, maxBaseLength) : baseName;
                uniqueName = truncatedBase + suffix;
                counter++;
            }
            while (existingNames.Contains(uniqueName));

            return uniqueName;
        }

        private List<LIVDataPoint> MergeLIVData(IEnumerable<DataPoint> powerData, IEnumerable<DataPoint> voltageData)
        {
            var result = new List<LIVDataPoint>();
            
            // 创建电流到功率的映射
            var powerDict = powerData.ToDictionary(p => p.X, p => p.Y);
            var voltageDict = voltageData.ToDictionary(p => p.X, p => p.Y);

            // 获取所有唯一的电流值
            var allCurrents = powerDict.Keys.Union(voltageDict.Keys).OrderBy(x => x);

            foreach (var current in allCurrents)
            {
                var power = powerDict.GetValueOrDefault(current, 0);
                var voltage = voltageDict.GetValueOrDefault(current, 0);
                var efficiency = (voltage > 0 && current > 0) ? (power / (current * voltage)) * 100 : 0;

                result.Add(new LIVDataPoint
                {
                    Current = current,
                    Power = power,
                    Voltage = voltage,
                    Efficiency = efficiency
                });
            }

            return result;
        }

        private List<DivergenceDataPoint> MergeDivergenceData(IEnumerable<DataPoint>? horizontalData, IEnumerable<DataPoint>? verticalData)
        {
            var result = new List<DivergenceDataPoint>();

            var hDict = horizontalData?.ToDictionary(p => p.X, p => p.Y) ?? new Dictionary<double, double>();
            var vDict = verticalData?.ToDictionary(p => p.X, p => p.Y) ?? new Dictionary<double, double>();

            var allAngles = hDict.Keys.Union(vDict.Keys).OrderBy(x => x);

            foreach (var angle in allAngles)
            {
                result.Add(new DivergenceDataPoint
                {
                    Angle = angle,
                    HorizontalIntensity = hDict.GetValueOrDefault(angle, double.NaN),
                    VerticalIntensity = vDict.GetValueOrDefault(angle, double.NaN)
                });
            }

            return result;
        }

        private class LIVDataPoint
        {
            public double Current { get; set; }
            public double Power { get; set; }
            public double Voltage { get; set; }
            public double Efficiency { get; set; }
        }

        private class DivergenceDataPoint
        {
            public double Angle { get; set; }
            public double HorizontalIntensity { get; set; }
            public double VerticalIntensity { get; set; }
        }
    }
}