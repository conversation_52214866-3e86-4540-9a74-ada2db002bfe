{"name": "liv-analyzer-web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@observablehq/plot": "^0.6.17", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "d3": "^7.9.0", "idb-keyval": "^6.2.2", "lucide-react": "^0.539.0", "ml-matrix": "^6.12.1", "papaparse": "^5.5.3", "react": "^19.1.1", "react-dom": "^19.1.1", "react-router-dom": "^7.8.0", "simple-statistics": "^7.8.8", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "xlsx": "^0.18.5", "zod": "^4.0.17", "zustand": "^5.0.7"}, "devDependencies": {"@eslint/js": "^9.32.0", "@tailwindcss/postcss": "^4.1.11", "@types/d3": "^7.4.3", "@types/papaparse": "^5.3.16", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^4.7.0", "eslint": "^9.32.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "typescript": "~5.8.3", "typescript-eslint": "^8.39.0", "vite": "^7.1.0"}}