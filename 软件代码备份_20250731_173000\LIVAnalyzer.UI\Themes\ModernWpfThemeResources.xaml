<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:ui="http://schemas.modernwpf.com/2019">

    <!-- ModernWPF 兼容的主题资源定义 -->
    
    <!-- 图表配色方案 - 使用系统主题色的变体 -->
    <Color x:Key="ChartColor1">#4FC3F7</Color>
    <Color x:Key="ChartColor2">#81C784</Color>
    <Color x:Key="ChartColor3">#FFB74D</Color>
    <Color x:Key="ChartColor4">#E57373</Color>
    <Color x:Key="ChartColor5">#BA68C8</Color>
    <Color x:Key="ChartColor6">#64B5F6</Color>
    
    <!-- 卡片样式 - 使用 ModernWPF 系统资源 -->
    <Style x:Key="ModernCard" TargetType="Border">
        <Setter Property="Background" Value="{DynamicResource SystemControlPageBackgroundAltMediumBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource SystemControlForegroundBaseLowBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="CornerRadius" Value="8"/>
        <Setter Property="Padding" Value="16"/>
        <Setter Property="Margin" Value="8"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect BlurRadius="8" 
                                  ShadowDepth="2" 
                                  Opacity="0.08" 
                                  Color="{DynamicResource SystemBaseMediumColor}"
                                  Direction="270"/>
            </Setter.Value>
        </Setter>
    </Style>
    
    <!-- 次要按钮样式 - 基于 ModernWPF -->
    <Style x:Key="SecondaryButton" TargetType="Button" BasedOn="{StaticResource DefaultButtonStyle}">
        <Setter Property="Margin" Value="4"/>
        <Setter Property="MinHeight" Value="32"/>
        <Setter Property="MinWidth" Value="80"/>
        <Setter Property="Padding" Value="12,6"/>
    </Style>
    
    <!-- 主要按钮样式 - 基于 ModernWPF -->
    <Style x:Key="PrimaryButton" TargetType="Button" BasedOn="{StaticResource AccentButtonStyle}">
        <Setter Property="Margin" Value="4"/>
        <Setter Property="MinHeight" Value="36"/>
        <Setter Property="MinWidth" Value="100"/>
        <Setter Property="Padding" Value="16,8"/>
    </Style>
    
    <!-- 标签样式 - 使用 ModernWPF 文本样式 -->
    <Style x:Key="FieldLabel" TargetType="TextBlock" BasedOn="{StaticResource CaptionTextBlockStyle}">
        <Setter Property="Foreground" Value="{DynamicResource SystemControlForegroundBaseMediumBrush}"/>
        <Setter Property="Margin" Value="0,0,0,4"/>
    </Style>
    
    <!-- 标题样式 - 使用 ModernWPF 文本样式 -->
    <Style x:Key="SectionTitle" TargetType="TextBlock" BasedOn="{StaticResource SubtitleTextBlockStyle}">
        <Setter Property="Margin" Value="0,0,0,16"/>
    </Style>
    
    <!-- 参数面板样式 - 符合 ModernWPF -->
    <Style x:Key="ParameterPanel" TargetType="Border">
        <Setter Property="Background" Value="{DynamicResource SystemControlPageBackgroundChromeLowBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource SystemControlForegroundBaseLowBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="CornerRadius" Value="4"/>
        <Setter Property="Padding" Value="12"/>
        <Setter Property="Margin" Value="0,0,0,8"/>
    </Style>
    
    <!-- ModernWPF 兼容的 DataGrid 样式 -->
    <Style x:Key="ModernDataGrid" TargetType="DataGrid" BasedOn="{StaticResource DefaultDataGridStyle}">
        <Setter Property="GridLinesVisibility" Value="Horizontal"/>
        <Setter Property="HorizontalGridLinesBrush" Value="{DynamicResource SystemControlForegroundBaseLowBrush}"/>
        <Setter Property="RowBackground" Value="{DynamicResource SystemControlPageBackgroundChromeLowBrush}"/>
        <Setter Property="AlternatingRowBackground" Value="{DynamicResource SystemControlPageBackgroundAltMediumBrush}"/>
    </Style>
    
    <!-- 进度条样式 - 使用系统主题色 -->
    <Style x:Key="ModernProgressBar" TargetType="ProgressBar" BasedOn="{StaticResource DefaultProgressBarStyle}">
        <Setter Property="Height" Value="4"/>
    </Style>

    <!-- 工具提示样式 - 符合 ModernWPF -->
    <Style x:Key="ModernToolTip" TargetType="ToolTip" BasedOn="{StaticResource DefaultToolTipStyle}">
        <Setter Property="Padding" Value="8,4"/>
    </Style>

    <!-- 分隔线样式 -->
    <Style x:Key="HorizontalSeparator" TargetType="Separator">
        <Setter Property="Background" Value="{DynamicResource SystemControlForegroundBaseLowBrush}"/>
        <Setter Property="Height" Value="1"/>
        <Setter Property="Margin" Value="0,8"/>
    </Style>

    <!-- 输入验证错误样式 -->
    <Style x:Key="ValidationErrorTemplate" TargetType="Control">
        <Setter Property="Validation.ErrorTemplate">
            <Setter.Value>
                <ControlTemplate>
                    <Grid>
                        <AdornedElementPlaceholder x:Name="placeholder"/>
                        <Border BorderBrush="{DynamicResource SystemControlErrorTextForegroundBrush}"
                                BorderThickness="2"
                                CornerRadius="4"/>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>