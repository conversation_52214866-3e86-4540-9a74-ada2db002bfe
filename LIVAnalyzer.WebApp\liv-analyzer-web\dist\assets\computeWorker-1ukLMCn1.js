(function(){"use strict";function x(e,o){const i=Math.min(e.length,o.length);if(i<2)return null;let n=0,t=0,s=0,r=0;for(let m=0;m<i;m++){const l=e[m],u=o[m];!Number.isFinite(l)||!Number.isFinite(u)||(n+=l,t+=u,s+=l*l,r+=l*u)}const c=i*s-n*n;if(Math.abs(c)<1e-12)return null;const f=(i*r-n*t)/c,a=(t-f*n)/i;return{slope:f,intercept:a}}function _(e,o){if(!e)return{};const i=o?.thresholdDetection??"linear",n=Math.min(e.current.length,e.power.length);if(n<2)return{};let t=Array.from({length:n},(l,u)=>u);if(i==="segmented"){const l=Math.max(2,Math.min(o?.fittingPoints??150,n));t=t.slice(n-l)}else{const l=Math.max(...e.power.filter(Number.isFinite),0),u=.05;t=t.filter(v=>e.power[v]>u*l),t.length<2&&(t=Array.from({length:n},(v,p)=>p))}let s=t.map(l=>e.current[l]),r=t.map(l=>e.power[l]),c=x(s,r);if(i==="robust"&&c){const l=s.map((d,F)=>r[F]-(c.slope*d+c.intercept)),u=l.reduce((d,F)=>d+F,0)/l.length,v=l.reduce((d,F)=>d+(F-u)*(F-u),0)/Math.max(1,l.length-1),p=Math.sqrt(Math.max(v,1e-30)),h=l.map(d=>Math.abs(d-u)<=2.5*p),g=[],b=[];for(let d=0;d<s.length;d++)h[d]&&(g.push(s[d]),b.push(r[d]));g.length>=2&&(c=x(g,b)??c)}if(!c)return{};const{slope:f,intercept:a}=c;return{ith:f!==0?-a/f:void 0,slope:f}}function I(e){return e?x(e.current,e.voltage)?.slope:void 0}function k(e,o){if(!e||!o)return;const i=Math.min(e.current.length,e.power.length,o.current.length,o.voltage.length);let n=-1/0;for(let t=0;t<i;t++){const s=e.current[t],r=e.power[t],c=o.voltage[t],f=s*c;if(Number.isFinite(r)&&Number.isFinite(f)&&Math.abs(f)>1e-12){const a=r/f;a>n&&(n=a)}}return Number.isFinite(n)?n:void 0}self.addEventListener("message",e=>{const o=e.data;try{if(o.kind==="compute"){const i=o.data,n=o.config??{},t=_(i.power,n),{ith:s,slope:r}=t,c=I(i.voltage),f=k(i.power,i.voltage);let a;i.wavelength&&i.wavelength.wavelength.length>1&&(a=y(i.wavelength.wavelength,i.wavelength.intensity)||void 0);let m;if(i.hff&&i.hff.angle.length>1){const u=N(i.hff.angle,i.hff.intensity),v=i.vff&&i.vff.angle.length>1?N(i.vff.angle,i.vff.intensity):void 0,p=u??void 0,h=v??void 0,g=p!==void 0&&h!==void 0&&h>0?p/h:void 0;m={horizontal_deg:p,vertical_deg:h,ellipticity:g}}const l={ok:!0,livParameters:{thresholdCurrent_mA:s!==void 0?s*1e3:void 0,slopeEfficiency_W_per_A:r,seriesResistance_Ohm:c,maxEfficiency:f},spectralParameters:a,divergenceParameters:m,livFitDiagnostics:{slope:t.slope,intercept:t.intercept,usedIndices:t.usedIndices,outlierIndices:t.outlierIndices}};self.postMessage(l);return}}catch(i){const n={ok:!1,error:i.message};self.postMessage(n)}});function y(e,o){const i=Math.min(e.length,o.length);if(i<3)return null;let n=0,t=-1/0;for(let h=0;h<i;h++){const g=o[h];Number.isFinite(g)&&g>t&&(t=g,n=h)}const s=e[n];if(!Number.isFinite(s)||!Number.isFinite(t)||t<=0)return{peakWavelength_nm:void 0,fwhm_nm:void 0,centroid_nm:void 0,smsr_dB:void 0};const r=t/2,c=M(e,o,n,-1,r),f=M(e,o,n,1,r),a=c!==null&&f!==null?Math.abs(f-c):void 0;let m=0,l=0;for(let h=0;h<i;h++){const g=e[h],b=o[h];Number.isFinite(g)&&Number.isFinite(b)&&b>0&&(m+=g*b,l+=b)}const u=l>0?m/l:void 0,v=P(o,e,n),p=v!==null&&v.val>0?10*Math.log10(t/v.val):void 0;return{peakWavelength_nm:s,fwhm_nm:a,centroid_nm:u,smsr_dB:p}}function N(e,o){const i=Math.min(e.length,o.length);if(i<3)return null;let n=0,t=-1/0;for(let f=0;f<i;f++){const a=o[f];Number.isFinite(a)&&a>t&&(t=a,n=f)}if(!Number.isFinite(t)||t<=0)return null;const s=t/2,r=M(e,o,n,-1,s),c=M(e,o,n,1,s);return r===null||c===null?null:Math.abs(c-r)}function M(e,o,i,n,t){let s=i;for(;s+n>=0&&s+n<o.length;){const r=o[s],c=o[s+n];if(!Number.isFinite(r)||!Number.isFinite(c)){s+=n;continue}if((r-t)*(c-t)<=0){const f=(t-r)/(c-r),a=e[s],m=e[s+n];return!Number.isFinite(a)||!Number.isFinite(m)?null:a+f*(m-a)}s+=n}return null}function P(e,o,i){let n=-1,t=-1/0;for(let s=1;s<e.length-1;s++){if(Math.abs(s-i)<=2)continue;const r=e[s];Number.isFinite(r)&&r>(e[s-1]??-1/0)&&r>(e[s+1]??-1/0)&&r>t&&(t=r,n=s)}return n>=0?{idx:n,val:t}:null}})();
