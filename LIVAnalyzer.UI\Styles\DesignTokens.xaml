<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- ==============================================
         Design Tokens - Unified Design System
         ============================================== -->

    <!-- Spacing Tokens -->
    <Thickness x:Key="XSMargin">2</Thickness>
    <Thickness x:Key="SmallMargin">4</Thickness>
    <Thickness x:Key="MediumMargin">8</Thickness>
    <Thickness x:Key="LargeMargin">16</Thickness>
    <Thickness x:Key="XLMargin">24</Thickness>
    <Thickness x:Key="XXLMargin">32</Thickness>

    <!-- Padding Tokens -->
    <Thickness x:Key="XSPadding">2</Thickness>
    <Thickness x:Key="SmallPadding">4</Thickness>
    <Thickness x:Key="MediumPadding">8</Thickness>
    <Thickness x:Key="LargePadding">16</Thickness>
    <Thickness x:Key="XLPadding">24</Thickness>

    <!-- Border Radius Tokens -->
    <CornerRadius x:Key="XSRadius">2</CornerRadius>
    <CornerRadius x:Key="SmallRadius">4</CornerRadius>
    <CornerRadius x:Key="MediumRadius">8</CornerRadius>
    <CornerRadius x:Key="LargeRadius">12</CornerRadius>
    <CornerRadius x:Key="XLRadius">16</CornerRadius>

    <!-- Font Size Tokens -->
    <system:Double x:Key="XSFontSize" xmlns:system="clr-namespace:System;assembly=mscorlib">10</system:Double>
    <system:Double x:Key="SmallFontSize" xmlns:system="clr-namespace:System;assembly=mscorlib">11</system:Double>
    <system:Double x:Key="MediumFontSize" xmlns:system="clr-namespace:System;assembly=mscorlib">12</system:Double>
    <system:Double x:Key="LargeFontSize" xmlns:system="clr-namespace:System;assembly=mscorlib">14</system:Double>
    <system:Double x:Key="XLFontSize" xmlns:system="clr-namespace:System;assembly=mscorlib">16</system:Double>
    <system:Double x:Key="XXLFontSize" xmlns:system="clr-namespace:System;assembly=mscorlib">20</system:Double>
    <system:Double x:Key="HeaderFontSize" xmlns:system="clr-namespace:System;assembly=mscorlib">24</system:Double>

    <!-- Font Weight Tokens -->
    <FontWeight x:Key="LightWeight">Light</FontWeight>
    <FontWeight x:Key="NormalWeight">Normal</FontWeight>
    <FontWeight x:Key="MediumWeight">Medium</FontWeight>
    <FontWeight x:Key="SemiBoldWeight">SemiBold</FontWeight>
    <FontWeight x:Key="BoldWeight">Bold</FontWeight>

    <!-- Shadow Tokens -->
    <DropShadowEffect x:Key="SmallShadow" 
                      Color="Black" 
                      Opacity="0.1" 
                      ShadowDepth="1" 
                      BlurRadius="3"/>
    
    <DropShadowEffect x:Key="MediumShadow" 
                      Color="Black" 
                      Opacity="0.15" 
                      ShadowDepth="2" 
                      BlurRadius="6"/>
    
    <DropShadowEffect x:Key="LargeShadow" 
                      Color="Black" 
                      Opacity="0.2" 
                      ShadowDepth="4" 
                      BlurRadius="12"/>

    <!-- Animation Duration Tokens -->
    <Duration x:Key="FastDuration">0:0:0.15</Duration>
    <Duration x:Key="MediumDuration">0:0:0.25</Duration>
    <Duration x:Key="SlowDuration">0:0:0.35</Duration>

    <!-- Easing Function Tokens -->
    <QuadraticEase x:Key="EaseOut" EasingMode="EaseOut"/>
    <CubicEase x:Key="EaseInOut" EasingMode="EaseInOut"/>
    <BackEase x:Key="EaseBack" EasingMode="EaseOut" Amplitude="0.3"/>

    <!-- Control Height Tokens -->
    <system:Double x:Key="SmallControlHeight" xmlns:system="clr-namespace:System;assembly=mscorlib">28</system:Double>
    <system:Double x:Key="MediumControlHeight" xmlns:system="clr-namespace:System;assembly=mscorlib">32</system:Double>
    <system:Double x:Key="LargeControlHeight" xmlns:system="clr-namespace:System;assembly=mscorlib">40</system:Double>
    <system:Double x:Key="XLControlHeight" xmlns:system="clr-namespace:System;assembly=mscorlib">48</system:Double>

    <!-- Grid Length Tokens -->
    <GridLength x:Key="SmallColumn">120</GridLength>
    <GridLength x:Key="MediumColumn">200</GridLength>
    <GridLength x:Key="LargeColumn">300</GridLength>
    <GridLength x:Key="XLColumn">400</GridLength>

    <!-- Opacity Tokens -->
    <system:Double x:Key="DisabledOpacity" xmlns:system="clr-namespace:System;assembly=mscorlib">0.4</system:Double>
    <system:Double x:Key="HoverOpacity" xmlns:system="clr-namespace:System;assembly=mscorlib">0.8</system:Double>
    <system:Double x:Key="PressedOpacity" xmlns:system="clr-namespace:System;assembly=mscorlib">0.6</system:Double>

</ResourceDictionary>