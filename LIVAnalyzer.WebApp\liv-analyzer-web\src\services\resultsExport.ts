import { useAppStore } from "../state/store";
import { exportCSV } from "./export";

export function exportResultsSummary() {
  const { results } = useAppStore.getState();
  const r = results?.livParameters;
  const sp = results?.spectralParameters;
  const dv = results?.divergenceParameters;
  exportCSV('results_summary', ['Metric','Value'], [
    ['Ith_mA', r?.thresholdCurrent_mA ?? ''],
    ['Slope_W_A', r?.slopeEfficiency_W_per_A ?? ''],
    ['Rs_Ohm', r?.seriesResistance_Ohm ?? ''],
    ['EtaMax', r?.maxEfficiency ?? ''],
    ['LambdaPeak_nm', sp?.peakWavelength_nm ?? ''],
    ['FWHM_nm', sp?.fwhm_nm ?? ''],
    ['Centroid_nm', sp?.centroid_nm ?? ''],
    ['SMSR_dB', sp?.smsr_dB ?? ''],
    ['Theta_H_deg', dv?.horizontal_deg ?? ''],
    ['Theta_V_deg', dv?.vertical_deg ?? ''],
    ['Ellipticity', dv?.ellipticity ?? ''],
  ]);
}


