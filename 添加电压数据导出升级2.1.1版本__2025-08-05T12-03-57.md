[ ] NAME:Current Task List DESCRIPTION:Root task for conversation __NEW_AGENT__
-[x] NAME:修改Excel导出器添加电压数据 DESCRIPTION:在ExcelDataExporter.cs的汇总表中添加I1电压和I2电压列，确保导出完整的电压信息
-[x] NAME:升级版本号到2.1.1 DESCRIPTION:更新AssemblyInfo.cs中的版本号从*******升级到*******
-[x] NAME:创建版本2.1.1发布脚本 DESCRIPTION:基于现有的CreateRelease_V2.1.0.bat创建新的V2.1.1发布脚本
-[x] NAME:测试导出功能 DESCRIPTION:验证修改后的导出功能是否正确包含电压数据
-[x] NAME:修复批量处理中的I1/I2电压和效率计算 DESCRIPTION:在OptimizedBatchProcessor中添加I1和I2的电压和效率计算，确保批量处理结果完整
-[x] NAME:修复批量处理中的发散角能量占比计算 DESCRIPTION:确保批量处理时正确计算水平和垂直1/e²能量占比
-[x] NAME:测试批量处理修复效果 DESCRIPTION:验证修复后的批量处理是否正确输出所有参数
-[x] NAME:更新使用指南文档 DESCRIPTION:更新GetDefaultUserGuide方法中的版本信息，将版本号更新为v2.1.1，日期更新为2025年8月5日，并添加v2.1.1的更新内容
-[x] NAME:更新技术文档 DESCRIPTION:更新GetDefaultTechnicalDoc方法中的版本信息和技术细节，反映v2.1.1的修复内容
-[x] NAME:更新发布说明 DESCRIPTION:更新GetDefaultReleaseNotes方法，添加v2.1.1的发布说明，包括电压数据导出和批量处理修复
-[x] NAME:更新关于信息 DESCRIPTION:更新GetDefaultAboutInfo方法中的版本号、日期和更新内容
-[x] NAME:添加效率曲线叠加设置项 DESCRIPTION:在ChartSettings类和ChartSettingsConfig类中添加ShowEfficiencyOverlay属性，用于控制是否在LIV曲线上叠加效率曲线
-[x] NAME:修改图表设置对话框UI DESCRIPTION:在ChartSettingsDialog.xaml中添加复选框控件，标签为「在LIV曲线上叠加效率曲线」，并在ViewModel中添加对应的属性
-[x] NAME:修改LIV图表创建逻辑 DESCRIPTION:在CreatePlotModel方法中为LIV曲线图表添加第三个Y轴（效率轴），位置在右侧，标题为「效率 (%)」
-[x] NAME:实现效率曲线数据计算 DESCRIPTION:创建方法来计算效率曲线数据点，基于电流-功率和电流-电压数据计算每个电流点的效率值
-[x] NAME:修改UpdateLIVPlot方法 DESCRIPTION:在UpdateLIVPlot方法中添加效率曲线的绘制逻辑，当设置启用时显示效率曲线，使用不同的颜色和线型
-[x] NAME:更新配置管理系统 DESCRIPTION:在配置管理系统中添加新设置项的保存和加载逻辑，确保用户选择能够持久化
-[x] NAME:测试和验证功能 DESCRIPTION:测试效率曲线叠加功能的完整性，包括UI交互、图表显示、配置保存等各个方面