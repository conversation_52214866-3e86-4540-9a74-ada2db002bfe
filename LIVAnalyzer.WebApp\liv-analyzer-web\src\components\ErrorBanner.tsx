import { useAppStore } from "../state/store";

export default function ErrorBanner() {
  const { error, setError } = useAppStore();
  if (!error) return null;
  return (
    <div className="rounded border border-destructive/40 bg-destructive/10 text-destructive px-4 py-3 text-sm flex items-start justify-between">
      <div className="pr-4">{error}</div>
      <button className="text-xs underline" onClick={() => setError(null)}>关闭</button>
    </div>
  );
}


