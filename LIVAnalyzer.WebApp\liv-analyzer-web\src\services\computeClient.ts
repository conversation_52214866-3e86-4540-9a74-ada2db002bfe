import type { LI<PERSON>ata, ProcessingResult, ProcessingConfig } from '../types/data';

export class ComputeWorkerClient {
  private worker: Worker | null = null;

  constructor() {
    try {
      this.worker = new Worker(new URL('../workers/computeWorker.ts', import.meta.url), { type: 'module' });
    } catch {
      this.worker = null;
    }
  }

  private getWorkerOrThrow(): Worker {
    if (!this.worker) throw new Error('计算 Worker 初始化失败');
    return this.worker;
  }

  async compute(data: LIVData, config?: ProcessingConfig): Promise<ProcessingResult> {
    const w = this.getWorkerOrThrow();
    return new Promise((resolve, reject) => {
      const onMessage = (ev: MessageEvent) => {
        const msg = ev.data as { ok: boolean; livParameters?: any; spectralParameters?: any; livFitDiagnostics?: any; error?: string };
        if (!msg.ok) {
          cleanup();
          reject(new Error(msg.error || '计算失败'));
          return;
        }
        cleanup();
        resolve({ livParameters: msg.livParameters, spectralParameters: msg.spectralParameters, livFitDiagnostics: msg.livFitDiagnostics, plots: [] });
      };
      const cleanup = () => w.removeEventListener('message', onMessage as EventListener);
      w.addEventListener('message', onMessage as EventListener);
      w.postMessage({ kind: 'compute', data, config });
    });
  }
}


