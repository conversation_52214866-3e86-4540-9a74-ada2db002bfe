@echo off
chcp 65001 >nul
echo ========================================
echo    LIV分析工具 v2.2.2 发布脚本
echo ========================================
echo.

echo [1/6] 清理旧的发布文件...
if exist "publish-v2.2.2" rmdir /s /q "publish-v2.2.2"
if exist "LIVAnalyzer_V2.2.2_Release" rmdir /s /q "LIVAnalyzer_V2.2.2_Release"
if exist "LIVAnalyzer_V2.2.2_Release.zip" del "LIVAnalyzer_V2.2.2_Release.zip"

echo [2/6] 构建项目...
dotnet clean --configuration Release
dotnet restore

echo [3/6] 发布应用程序...
dotnet publish LIVAnalyzer.UI -c Release -r win-x64 --self-contained true --single-file true -o publish-v2.2.2 -p:PublishSingleFile=true -p:IncludeNativeLibrariesForSelfExtract=true -p:PublishTrimmed=false

if not exist "publish-v2.2.2\LIVAnalyzer.exe" (
    echo 错误：发布失败，未找到可执行文件
    pause
    exit /b 1
)

echo [4/6] 创建发布包目录...
set RELEASE_DIR=LIVAnalyzer_V2.2.2_Release
mkdir "%RELEASE_DIR%"

echo [5/6] 复制文件到发布包...
REM 复制主程序
copy "publish-v2.2.2\LIVAnalyzer.exe" "%RELEASE_DIR%\"

REM 复制配置文件（如果存在）
if exist "publish-v2.2.2\*.config" copy "publish-v2.2.2\*.config" "%RELEASE_DIR%\"

REM 复制文档文件
copy "使用指南.md" "%RELEASE_DIR%\"
copy "技术文档.md" "%RELEASE_DIR%\"
copy "发布说明.md" "%RELEASE_DIR%\"
copy "关于.md" "%RELEASE_DIR%\"

REM 创建启动脚本
echo @echo off > "%RELEASE_DIR%\启动LIV分析工具.bat"
echo chcp 65001 ^>nul >> "%RELEASE_DIR%\启动LIV分析工具.bat"
echo echo 正在启动LIV分析工具 v2.2.2... >> "%RELEASE_DIR%\启动LIV分析工具.bat"
echo start "" "LIVAnalyzer.exe" >> "%RELEASE_DIR%\启动LIV分析工具.bat"

REM 创建README文件
echo LIV分析工具 v2.2.2 ^(.NET 9 + 性能优化版本^) > "%RELEASE_DIR%\README.txt"
echo. >> "%RELEASE_DIR%\README.txt"
echo 【🆕 v2.2.2 主要更新】 >> "%RELEASE_DIR%\README.txt"
echo ✅ 重大性能优化：图表闪烁修复，文件加载提速2-5倍 >> "%RELEASE_DIR%\README.txt"
echo ✅ 新增效率曲线显示，支持第三坐标轴 >> "%RELEASE_DIR%\README.txt"
echo ✅ 自动图表缩放，发散角图表无需手动双击 >> "%RELEASE_DIR%\README.txt"
echo ✅ 增强Excel错误处理和数据清理能力 >> "%RELEASE_DIR%\README.txt"
echo ✅ 智能增量更新机制，消除图表闪烁 >> "%RELEASE_DIR%\README.txt"
echo. >> "%RELEASE_DIR%\README.txt"
echo 【系统要求】 >> "%RELEASE_DIR%\README.txt"
echo - 操作系统：Windows 10/11 ^(x64^) >> "%RELEASE_DIR%\README.txt"
echo - 内存：建议8GB以上 >> "%RELEASE_DIR%\README.txt"
echo - 硬盘：至少300MB可用空间 >> "%RELEASE_DIR%\README.txt"
echo - 显卡：DirectX 11+ ^(用于Fluent Design效果^) >> "%RELEASE_DIR%\README.txt"
echo. >> "%RELEASE_DIR%\README.txt"
echo 【使用方法】 >> "%RELEASE_DIR%\README.txt"
echo 1. 双击"启动LIV分析工具.bat"运行程序 >> "%RELEASE_DIR%\README.txt"
echo 2. 或直接运行"LIVAnalyzer.exe" >> "%RELEASE_DIR%\README.txt"
echo 3. 查看"使用指南.md"了解详细使用方法 >> "%RELEASE_DIR%\README.txt"
echo. >> "%RELEASE_DIR%\README.txt"
echo 【特性说明】 >> "%RELEASE_DIR%\README.txt"
echo - 自包含部署，无需安装.NET运行时 >> "%RELEASE_DIR%\README.txt"
echo - 单文件发布，便于分发和使用 >> "%RELEASE_DIR%\README.txt"
echo - 支持LIV曲线分析、光谱分析、发散角分析 >> "%RELEASE_DIR%\README.txt"
echo - 批量数据处理和Excel导出功能 >> "%RELEASE_DIR%\README.txt"
echo - 高级图表设置和实时参数显示 >> "%RELEASE_DIR%\README.txt"
echo - 现代化Fluent Design界面 >> "%RELEASE_DIR%\README.txt"
echo. >> "%RELEASE_DIR%\README.txt"
echo 开发者：00106 >> "%RELEASE_DIR%\README.txt"
echo 发布日期：2025年8月6日 >> "%RELEASE_DIR%\README.txt"
echo 版本：v2.2.2 >> "%RELEASE_DIR%\README.txt"

REM 创建版本信息文件
echo LIV分析工具 版本信息 > "%RELEASE_DIR%\版本信息.txt"
echo ======================== >> "%RELEASE_DIR%\版本信息.txt"
echo. >> "%RELEASE_DIR%\版本信息.txt"
echo 软件版本: v2.2.2 >> "%RELEASE_DIR%\版本信息.txt"
echo 发布日期: 2025年8月6日 >> "%RELEASE_DIR%\版本信息.txt"
echo 开发者: 00106 >> "%RELEASE_DIR%\版本信息.txt"
echo. >> "%RELEASE_DIR%\版本信息.txt"
echo 技术信息: >> "%RELEASE_DIR%\版本信息.txt"
echo - 框架: .NET 9.0 >> "%RELEASE_DIR%\版本信息.txt"
echo - 架构: x64 >> "%RELEASE_DIR%\版本信息.txt"
echo - 部署: Self-contained, Single-file >> "%RELEASE_DIR%\版本信息.txt"
echo - UI框架: WPF + Fluent Design >> "%RELEASE_DIR%\版本信息.txt"
echo - 图表库: OxyPlot 2.1+ >> "%RELEASE_DIR%\版本信息.txt"
echo. >> "%RELEASE_DIR%\版本信息.txt"
echo 主要改进: >> "%RELEASE_DIR%\版本信息.txt"
echo - 图表闪烁修复和增量更新机制 >> "%RELEASE_DIR%\版本信息.txt"
echo - 文件加载性能提升2-5倍 >> "%RELEASE_DIR%\版本信息.txt"
echo - Excel读取效率提升3-10倍 >> "%RELEASE_DIR%\版本信息.txt"
echo - 自动图表缩放和显示优化 >> "%RELEASE_DIR%\版本信息.txt"
echo - 效率曲线显示和第三坐标轴支持 >> "%RELEASE_DIR%\版本信息.txt"
echo - 增强错误处理和数据清理 >> "%RELEASE_DIR%\版本信息.txt"

echo [6/6] 创建压缩包...
powershell -Command "Compress-Archive -Path '%RELEASE_DIR%' -DestinationPath 'LIVAnalyzer_V2.2.2_Release.zip' -Force"

echo.
echo ========================================
echo           发布完成！
echo ========================================
echo.
echo 发布包位置: %RELEASE_DIR%
echo 压缩包: LIVAnalyzer_V2.2.2_Release.zip
echo.

REM 显示文件大小信息
for %%f in ("%RELEASE_DIR%\LIVAnalyzer.exe") do echo 主程序大小: %%~zf 字节
for %%f in ("LIVAnalyzer_V2.2.2_Release.zip") do echo 压缩包大小: %%~zf 字节

echo.
echo 按任意键退出...
pause >nul
