using System;

namespace LIVAnalyzer.UI.Services
{
    /// <summary>
    /// 主题服务接口 - 解耦主题管理逻辑
    /// </summary>
    public interface IThemeService
    {
        /// <summary>
        /// 主题变化事件
        /// </summary>
        event EventHandler<ThemeChangedEventArgs> ThemeChanged;

        /// <summary>
        /// 获取当前主题模式
        /// </summary>
        string GetCurrentTheme();

        /// <summary>
        /// 判断当前是否为深色模式
        /// </summary>
        bool IsCurrentlyDarkMode();

        /// <summary>
        /// 切换主题
        /// </summary>
        /// <param name="themeMode">主题模式: System, Light, Dark</param>
        void SwitchTheme(string themeMode);

        /// <summary>
        /// 智能切换主题（深色<->浅色）
        /// </summary>
        void ToggleTheme();

        /// <summary>
        /// 获取主题显示信息
        /// </summary>
        ThemeDisplayInfo GetThemeDisplayInfo();
    }

    /// <summary>
    /// 主题显示信息
    /// </summary>
    public class ThemeDisplayInfo
    {
        public bool IsSystemTheme { get; set; }
        public bool IsLightTheme { get; set; }
        public bool IsDarkTheme { get; set; }
        public bool IsDarkMode { get; set; }
        public string CurrentTheme { get; set; } = "";
        public string ThemeDisplayName { get; set; } = "";
        public string ToggleButtonTooltip { get; set; } = "";
        public bool ShowLightIcon { get; set; }
        public bool ShowDarkIcon { get; set; }
    }
}