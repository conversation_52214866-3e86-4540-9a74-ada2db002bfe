# LIV Analyzer - C# Version

## 项目概述

LIV Analyzer C#版本是基于.NET 6和WPF技术栈重新开发的激光器LIV特性曲线分析工具。相比Python版本，C#版本提供了更好的性能、更现代化的用户界面和更简化的部署方式。

## 主要特性

### 核心功能
- **LIV特性曲线分析**：阈值电流、斜率效率、串联电阻计算
- **光谱数据分析**：峰值波长、FWHM (半高宽) 计算
- **发散角分析**：FWHM、FW86.5%、FW95% 计算
- **效率曲线分析**：实时效率计算和可视化
- **批量数据处理**：支持文件夹批量处理
- **交互式图表**：基于OxyPlot的高质量图表显示

### 技术特性
- **现代化架构**：MVVM模式，模块化设计
- **高性能**：基于.NET 6，原生编译优化
- **用户友好**：Material Design风格界面
- **扩展性强**：插件化架构，易于扩展
- **配置灵活**：YAML配置文件，支持个性化设置

## 项目结构

```
LIVAnalyzer.Solution/
├── LIVAnalyzer.Models/          # 数据模型定义
├── LIVAnalyzer.Data/            # 数据访问层 (Excel/CSV读取)
├── LIVAnalyzer.Core/            # 核心业务逻辑 (数据处理算法)
├── LIVAnalyzer.Services/        # 服务层 (配置管理、日志)
├── LIVAnalyzer.UI/              # WPF用户界面
└── LIVAnalyzer.Tests/           # 单元测试
```

## 技术栈

### 核心框架
- **.NET 6**: 现代化的.NET平台
- **WPF**: Windows桌面应用程序框架
- **MVVM**: 模型-视图-视图模型架构模式

### 主要依赖库
- **EPPlus**: Excel文件读写处理
- **CsvHelper**: CSV文件处理
- **MathNet.Numerics**: 科学计算和数值分析
- **OxyPlot**: 高性能2D图表库
- **Serilog**: 结构化日志记录
- **YamlDotNet**: YAML配置文件处理
- **MaterialDesignThemes**: Material Design UI控件
- **CommunityToolkit.Mvvm**: MVVM框架支持

## 安装和运行

### 系统要求
- Windows 10/11 (x64)
- .NET 6 Runtime (如果使用framework-dependent部署)

### 开发环境要求
- Visual Studio 2022 或 VS Code
- .NET 6 SDK

### 编译和运行
```bash
# 克隆项目
git clone <repository-url>
cd LIVAnalyzer.Solution

# 还原NuGet包
dotnet restore

# 编译项目
dotnet build

# 运行应用程序
dotnet run --project LIVAnalyzer.UI

# 运行测试
dotnet test
```

### 发布
```bash
# 发布为单文件可执行程序
dotnet publish LIVAnalyzer.UI -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true

# 发布为框架依赖
dotnet publish LIVAnalyzer.UI -c Release -r win-x64 --self-contained false
```

## 支持的文件格式

### 输入格式
- **Excel文件** (.xlsx, .xls)
  - 要求包含 `wavelength`、`power`、`voltage` 工作表
  - 可选 `HFF`、`VFF` 工作表用于发散角分析
- **CSV文件** (.csv)
  - 支持标准LIV数据格式

### 输出格式
- **Excel文件** (.xlsx) - 分析结果导出
- **图像文件** (.png, .pdf) - 图表保存

## 核心算法

### LIV参数计算
- **阈值电流**: 基于二阶导数的拐点检测
- **斜率效率**: 线性区间的线性拟合斜率
- **串联电阻**: 电流-电压曲线的线性拟合
- **最大效率**: P/(I×V) 的最大值

### 光谱分析
- **峰值波长**: 最大强度对应的波长
- **FWHM**: 半高宽计算 (50%最大强度的宽度)

### 发散角分析
- **FWHM**: 半高宽发散角
- **FW86.5%**: 86.5%功率包含角
- **FW95%**: 95%功率包含角

## 配置文件

应用程序配置文件位于：`%AppData%\LIVAnalyzer\config.yaml`

示例配置：
```yaml
DataProcessing:
  Smoothing:
    EnableByDefault: false
    DefaultWindowSize: 5
    MinWindowSize: 3
    MaxWindowSize: 51
  BatchProcessing:
    DefaultI1: 0.5
    DefaultI2: 1.0
Display:
  ShowGrid: true
  ShowLegend: false
  DefaultLineStyle: "line"
Performance:
  MaxFiles: 1000
  MaxFileSizeMB: 500
```

## 日志系统

日志文件位置：`%AppData%\LIVAnalyzer\Logs\`
- `liv_analyzer.log` - 一般日志
- `liv_analyzer_error.log` - 错误日志

## 版本历史

### V2.0 (C#版本)
- 基于C#/.NET 6重新开发
- 现代化WPF界面设计
- 更强的性能和稳定性
- 模块化架构设计
- 完整的单元测试覆盖

## 开发指南

### 添加新功能
1. 在 `LIVAnalyzer.Models` 中定义数据模型
2. 在 `LIVAnalyzer.Core` 中实现业务逻辑
3. 在 `LIVAnalyzer.UI` 中添加界面和ViewModel
4. 在 `LIVAnalyzer.Tests` 中添加单元测试

### 代码风格
- 遵循C#编码规范
- 使用async/await进行异步编程
- 实现INotifyPropertyChanged接口进行数据绑定
- 使用依赖注入模式

## 贡献指南

1. Fork项目
2. 创建特性分支
3. 提交更改
4. 创建Pull Request

## 许可证

本项目采用MIT许可证。详见LICENSE文件。

## 联系方式

- 开发者：00106
- 项目地址：[GitHub Repository]
- 问题反馈：[Issues Page]

---

*专业的激光器测试分析解决方案*