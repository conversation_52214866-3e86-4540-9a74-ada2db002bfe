using System;
using System.Collections.Generic;
using LIVAnalyzer.Core.Processors;
using LIVAnalyzer.Models;

namespace LIVAnalyzer.Tests
{
    /// <summary>
    /// 测试最大效率计算的修改
    /// </summary>
    public class TestMaxEfficiencyCalculation
    {
        public static void Main(string[] args)
        {
            Console.WriteLine("=== 测试最大效率计算修改 ===");
            Console.WriteLine();

            // 创建测试数据
            var testData = CreateTestData();
            
            // 测试标准处理器
            Console.WriteLine("1. 测试标准LIVDataProcessor:");
            TestStandardProcessor(testData);
            
            Console.WriteLine();
            
            // 测试优化处理器
            Console.WriteLine("2. 测试OptimizedLIVDataProcessor:");
            TestOptimizedProcessor(testData);
            
            Console.WriteLine();
            Console.WriteLine("测试完成！按任意键退出...");
            Console.ReadKey();
        }
        
        private static LIVMeasurementData CreateTestData()
        {
            var data = new LIVMeasurementData
            {
                FileName = "test_efficiency.xlsx"
            };
            
            // 创建电流-功率数据（阈值电流约为0.5A）
            data.CurrentPowerData = new List<DataPoint>
            {
                new(0.0, 0.0),
                new(0.1, 0.001),
                new(0.2, 0.002),
                new(0.3, 0.003),
                new(0.4, 0.004),
                new(0.5, 0.005),  // 阈值电流附近
                new(0.6, 0.050),  // 开始显著增长
                new(0.7, 0.100),
                new(0.8, 0.150),
                new(0.9, 0.200),
                new(1.0, 0.250),
                new(1.1, 0.300),
                new(1.2, 0.350),
                new(1.3, 0.400),
                new(1.4, 0.450),
                new(1.5, 0.500)
            };
            
            // 创建电流-电压数据
            data.CurrentVoltageData = new List<DataPoint>
            {
                new(0.0, 0.0),
                new(0.1, 1.5),
                new(0.2, 1.6),
                new(0.3, 1.7),
                new(0.4, 1.8),
                new(0.5, 1.9),
                new(0.6, 2.0),
                new(0.7, 2.1),
                new(0.8, 2.2),
                new(0.9, 2.3),
                new(1.0, 2.4),
                new(1.1, 2.5),
                new(1.2, 2.6),
                new(1.3, 2.7),
                new(1.4, 2.8),
                new(1.5, 2.9)
            };
            
            return data;
        }
        
        private static void TestStandardProcessor(LIVMeasurementData data)
        {
            var processor = new LIVDataProcessor();
            var parameters = processor.CalculateParameters(data);
            
            Console.WriteLine($"  阈值电流: {parameters.ThresholdCurrent:F3} A");
            Console.WriteLine($"  最大效率: {parameters.MaxEfficiency:F2} %");
            Console.WriteLine($"  最大功率: {parameters.MaxPower:F3} W");
            Console.WriteLine($"  斜率效率: {parameters.SlopeEfficiency:F3} W/A");
            
            // 手动计算验证
            Console.WriteLine();
            Console.WriteLine("  手动验证计算:");
            CalculateEfficiencyManually(data, parameters.ThresholdCurrent);
        }
        
        private static void TestOptimizedProcessor(LIVMeasurementData data)
        {
            var processor = new OptimizedLIVDataProcessor();
            var parameters = processor.CalculateParameters(data);
            
            Console.WriteLine($"  阈值电流: {parameters.ThresholdCurrent:F3} A");
            Console.WriteLine($"  最大效率: {parameters.MaxEfficiency:F2} %");
            Console.WriteLine($"  最大功率: {parameters.MaxPower:F3} W");
            Console.WriteLine($"  斜率效率: {parameters.SlopeEfficiency:F3} W/A");
        }
        
        private static void CalculateEfficiencyManually(LIVMeasurementData data, double thresholdCurrent)
        {
            var voltageMap = new Dictionary<double, double>();
            foreach (var point in data.CurrentVoltageData)
            {
                voltageMap[point.X] = point.Y;
            }
            
            double maxEfficiencyBeforeThreshold = 0;
            double maxEfficiencyAfterThreshold = 0;
            
            foreach (var point in data.CurrentPowerData)
            {
                if (point.X > 0 && point.Y > 0 && voltageMap.ContainsKey(point.X))
                {
                    var voltage = voltageMap[point.X];
                    if (voltage > 0)
                    {
                        var efficiency = (point.Y / (point.X * voltage)) * 100;
                        
                        if (point.X <= thresholdCurrent)
                        {
                            maxEfficiencyBeforeThreshold = Math.Max(maxEfficiencyBeforeThreshold, efficiency);
                        }
                        else
                        {
                            maxEfficiencyAfterThreshold = Math.Max(maxEfficiencyAfterThreshold, efficiency);
                        }
                    }
                }
            }
            
            Console.WriteLine($"    阈值前最大效率: {maxEfficiencyBeforeThreshold:F2} %");
            Console.WriteLine($"    阈值后最大效率: {maxEfficiencyAfterThreshold:F2} %");
            Console.WriteLine($"    修改后应该只计算阈值后的效率");
        }
    }
}
