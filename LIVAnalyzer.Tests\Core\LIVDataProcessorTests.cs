using LIVAnalyzer.Core.Processors;
using LIVAnalyzer.Models;
using Xunit;

namespace LIVAnalyzer.Tests.Core
{
    public class LIVDataProcessorTests
    {
        private readonly LIVDataProcessor _processor;
        
        public LIVDataProcessorTests()
        {
            _processor = new LIVDataProcessor();
        }
        
        [Fact]
        public void CalculateParameters_WithValidData_ReturnsParameters()
        {
            // Arrange
            var data = new LIVMeasurementData
            {
                FileName = "test.xlsx",
                CurrentPowerData = new List<DataPoint>
                {
                    new(0.1, 0.01),
                    new(0.2, 0.05),
                    new(0.3, 0.12),
                    new(0.4, 0.25),
                    new(0.5, 0.42)
                },
                WavelengthIntensityData = new List<DataPoint>
                {
                    new(630, 0.1),
                    new(635, 0.5),
                    new(640, 1.0),
                    new(645, 0.5),
                    new(650, 0.1)
                }
            };
            
            // Act
            var result = _processor.CalculateParameters(data);
            
            // Assert
            Assert.NotNull(result);
            Assert.True(result.PeakWavelength > 0);
            Assert.True(result.MaxPower > 0);
            Assert.True(result.ThresholdCurrent >= 0);
        }
        
        [Fact]
        public void SmoothData_WithValidData_ReturnsSmoothData()
        {
            // Arrange
            var data = new List<DataPoint>
            {
                new(1, 1),
                new(2, 10), // 噪声点
                new(3, 3),
                new(4, 4),
                new(5, 15), // 噪声点
                new(6, 6)
            };
            
            // Act
            var result = _processor.SmoothData(data, 3);
            
            // Assert
            Assert.Equal(data.Count, result.Count);
            // 平滑后的数据应该减少噪声
            Assert.True(result[1].Y < data[1].Y); // 噪声点应该被平滑
        }
        
        [Fact]
        public void CalculateThresholdCurrent_DifferentData_ReturnsDifferentValues()
        {
            // Arrange
            var processor = new OptimizedLIVDataProcessor();
            
            // 第一组数据 - 模拟激光二极管1
            var data1 = new LIVMeasurementData
            {
                FileName = "laser1.xlsx",
                CurrentPowerData = GenerateLaserData(0.5, 1.2) // 阈值电流约0.5A
            };
            
            // 第二组数据 - 模拟激光二极管2
            var data2 = new LIVMeasurementData
            {
                FileName = "laser2.xlsx",
                CurrentPowerData = GenerateLaserData(0.7, 0.8) // 阈值电流约0.7A
            };
            
            // Act
            var result1 = processor.CalculateParameters(data1);
            var result2 = processor.CalculateParameters(data2);
            
            // Assert
            Assert.NotNull(result1);
            Assert.NotNull(result2);
            Assert.NotEqual(result1.ThresholdCurrent, result2.ThresholdCurrent);
            Assert.True(Math.Abs(result1.ThresholdCurrent - 0.5) < 0.1); // 验证阈值接近预期
            Assert.True(Math.Abs(result2.ThresholdCurrent - 0.7) < 0.1);
        }
        
        [Fact]
        public void OptimizedProcessor_WithCacheClearing_ReturnsCorrectValues()
        {
            // Arrange
            var processor = new OptimizedLIVDataProcessor();
            
            // 生成多组不同的测试数据
            var testDataSets = new List<(LIVMeasurementData data, double expectedThreshold)>
            {
                (new LIVMeasurementData 
                { 
                    FileName = "test1.xlsx",
                    CurrentPowerData = GenerateLaserData(0.3, 1.5) 
                }, 0.3),
                (new LIVMeasurementData 
                { 
                    FileName = "test2.xlsx",
                    CurrentPowerData = GenerateLaserData(0.5, 1.0) 
                }, 0.5),
                (new LIVMeasurementData 
                { 
                    FileName = "test3.xlsx",
                    CurrentPowerData = GenerateLaserData(0.7, 0.8) 
                }, 0.7)
            };
            
            var results = new List<double>();
            
            // Act
            foreach (var (data, expected) in testDataSets)
            {
                processor.ClearCache(); // 清除缓存
                var parameters = processor.CalculateParameters(data);
                results.Add(parameters.ThresholdCurrent);
                
                // 调试输出
                System.Diagnostics.Debug.WriteLine($"Expected: {expected}, Got: {parameters.ThresholdCurrent}");
            }
            
            // Assert
            // 验证所有阈值电流都不相同
            var distinctCount = results.Distinct().Count();
            Assert.Equal(3, distinctCount);
            
            // 验证每个阈值接近预期值
            for (int i = 0; i < testDataSets.Count; i++)
            {
                Assert.True(Math.Abs(results[i] - testDataSets[i].expectedThreshold) < 0.15,
                    $"Threshold {i}: expected ~{testDataSets[i].expectedThreshold}, got {results[i]}");
            }
        }
        
        [Fact]
        public void CachingIssue_SimplifiedTest()
        {
            // Arrange
            var processor = new OptimizedLIVDataProcessor();
            
            // 创建两组完全不同的数据
            var data1 = new LIVMeasurementData
            {
                FileName = "test1.xlsx",
                CurrentPowerData = new List<DataPoint>
                {
                    new(0.0, 0.0),
                    new(0.1, 0.001),
                    new(0.2, 0.002),
                    new(0.3, 0.003),
                    new(0.4, 0.004),
                    new(0.5, 0.005),  // 阈值应该在这附近
                    new(0.6, 0.050),
                    new(0.7, 0.100),
                    new(0.8, 0.150),
                    new(0.9, 0.200),
                    new(1.0, 0.250)
                }
            };
            
            var data2 = new LIVMeasurementData
            {
                FileName = "test2.xlsx",
                CurrentPowerData = new List<DataPoint>
                {
                    new(0.0, 0.0),
                    new(0.1, 0.0005),
                    new(0.2, 0.001),
                    new(0.3, 0.0015),
                    new(0.4, 0.002),
                    new(0.5, 0.0025),
                    new(0.6, 0.003),
                    new(0.7, 0.0035),
                    new(0.8, 0.004),  // 阈值应该在这附近
                    new(0.9, 0.080),
                    new(1.0, 0.160),
                    new(1.1, 0.240),
                    new(1.2, 0.320)
                }
            };
            
            // Act - 不清除缓存的情况
            var result1_firstCall = processor.CalculateParameters(data1);
            var result2_firstCall = processor.CalculateParameters(data2);
            
            // Act - 清除缓存后再次计算
            processor.ClearCache();
            var result1_secondCall = processor.CalculateParameters(data1);
            var result2_secondCall = processor.CalculateParameters(data2);
            
            // Assert
            // 第一次调用应该返回不同的结果
            Assert.NotEqual(result1_firstCall.ThresholdCurrent, result2_firstCall.ThresholdCurrent);
            
            // 清除缓存后，相同数据应该返回相同结果
            Assert.Equal(result1_firstCall.ThresholdCurrent, result1_secondCall.ThresholdCurrent);
            Assert.Equal(result2_firstCall.ThresholdCurrent, result2_secondCall.ThresholdCurrent);
            
            // 输出调试信息
            System.Diagnostics.Debug.WriteLine($"Data1 threshold: {result1_firstCall.ThresholdCurrent}");
            System.Diagnostics.Debug.WriteLine($"Data2 threshold: {result2_firstCall.ThresholdCurrent}");
        }
        private List<DataPoint> GenerateLaserData(double threshold, double efficiency)
        {
            var data = new List<DataPoint>();
            
            // 生成电流-功率曲线数据
            for (double current = 0; current <= 2.0; current += 0.02)
            {
                double power;
                if (current < threshold)
                {
                    // 阈值以下，功率很小
                    power = current * 0.01;
                }
                else
                {
                    // 阈值以上，线性增长
                    power = (current - threshold) * efficiency + threshold * 0.01;
                }
                
                // 添加一些噪声使数据更真实
                power += (Random.Shared.NextDouble() - 0.5) * 0.001;
                data.Add(new DataPoint(current, Math.Max(0, power)));
            }
            
            return data;
        }
    }
}