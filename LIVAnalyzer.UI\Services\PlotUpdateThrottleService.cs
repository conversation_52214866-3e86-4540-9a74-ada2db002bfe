using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Threading;

namespace LIVAnalyzer.UI.Services
{
    /// <summary>
    /// 图表更新节流服务
    /// 防止频繁的图表更新操作阻塞UI线程
    /// </summary>
    public class PlotUpdateThrottleService : IDisposable
    {
        private readonly Timer _throttleTimer;
        private readonly object _lockObject = new();
        private readonly DispatcherTimer _uiTimer;
        private Action? _pendingAction;
        private bool _disposed = false;

        /// <summary>
        /// 节流延迟时间（毫秒）
        /// </summary>
        public int ThrottleDelayMs { get; set; } = 100;

        /// <summary>
        /// 最大等待时间（毫秒）- 防止更新被无限延迟
        /// </summary>
        public int MaxWaitTimeMs { get; set; } = 500;

        private DateTime _lastUpdateTime = DateTime.MinValue;
        private DateTime _firstPendingTime = DateTime.MinValue;

        public PlotUpdateThrottleService()
        {
            _throttleTimer = new Timer(OnTimerElapsed, null, Timeout.Infinite, Timeout.Infinite);
            
            // 使用UI线程的DispatcherTimer作为备用机制
            _uiTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(ThrottleDelayMs)
            };
            _uiTimer.Tick += OnUiTimerTick;
        }

        /// <summary>
        /// 请求执行图表更新操作（带节流）
        /// </summary>
        /// <param name="updateAction">更新操作</param>
        public void RequestUpdate(Action updateAction)
        {
            if (_disposed) return;

            lock (_lockObject)
            {
                _pendingAction = updateAction;
                var now = DateTime.UtcNow;

                // 记录第一次请求的时间
                if (_firstPendingTime == DateTime.MinValue)
                {
                    _firstPendingTime = now;
                }

                // 检查是否达到最大等待时间
                var maxWaitExceeded = (now - _firstPendingTime).TotalMilliseconds >= MaxWaitTimeMs;
                
                // 检查是否可以立即执行
                var timeSinceLastUpdate = (now - _lastUpdateTime).TotalMilliseconds;
                var canExecuteImmediately = timeSinceLastUpdate >= ThrottleDelayMs;

                if (maxWaitExceeded || canExecuteImmediately)
                {
                    // 立即执行
                    ExecutePendingAction();
                }
                else
                {
                    // 重置定时器
                    _throttleTimer.Change(ThrottleDelayMs, Timeout.Infinite);
                    
                    // 同时设置UI定时器作为备用
                    _uiTimer.Stop();
                    _uiTimer.Start();
                }
            }
        }

        /// <summary>
        /// 请求执行异步图表更新操作（带节流）
        /// </summary>
        /// <param name="updateAction">异步更新操作</param>
        public void RequestUpdateAsync(Func<Task> updateAction)
        {
            RequestUpdate(() =>
            {
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await updateAction();
                    }
                    catch (Exception ex)
                    {
                        // 记录异常但不抛出
                        System.Diagnostics.Debug.WriteLine($"PlotUpdateThrottleService异步操作异常: {ex.Message}");
                    }
                });
            });
        }

        /// <summary>
        /// 立即执行所有待定的更新（绕过节流）
        /// </summary>
        public void FlushPendingUpdates()
        {
            if (_disposed) return;

            lock (_lockObject)
            {
                _throttleTimer.Change(Timeout.Infinite, Timeout.Infinite);
                _uiTimer.Stop();
                ExecutePendingAction();
            }
        }

        private void OnTimerElapsed(object? state)
        {
            if (_disposed) return;

            lock (_lockObject)
            {
                ExecutePendingAction();
            }
        }

        private void OnUiTimerTick(object? sender, EventArgs e)
        {
            if (_disposed) return;

            _uiTimer.Stop();
            
            lock (_lockObject)
            {
                ExecutePendingAction();
            }
        }

        private void ExecutePendingAction()
        {
            if (_pendingAction == null) return;

            try
            {
                var action = _pendingAction;
                _pendingAction = null;
                _lastUpdateTime = DateTime.UtcNow;
                _firstPendingTime = DateTime.MinValue;

                // 确保在UI线程上执行
                if (System.Windows.Application.Current?.Dispatcher.CheckAccess() == true)
                {
                    action();
                }
                else
                {
                    System.Windows.Application.Current?.Dispatcher.BeginInvoke(action, DispatcherPriority.Background);
                }
            }
            catch (Exception ex)
            {
                // 记录异常但不抛出
                System.Diagnostics.Debug.WriteLine($"PlotUpdateThrottleService执行操作异常: {ex.Message}");
            }
        }

        public void Dispose()
        {
            if (_disposed) return;

            _disposed = true;

            lock (_lockObject)
            {
                _throttleTimer?.Dispose();
                _uiTimer?.Stop();
                _pendingAction = null;
            }

            GC.SuppressFinalize(this);
        }

        ~PlotUpdateThrottleService()
        {
            Dispose();
        }
    }

    /// <summary>
    /// 批量图表更新服务
    /// 用于批量处理多个图表的更新操作
    /// </summary>
    public class BatchPlotUpdateService : IDisposable
    {
        private readonly PlotUpdateThrottleService _throttleService;
        private readonly object _lockObject = new();
        private readonly Dictionary<string, Action> _pendingUpdates = new();
        private bool _disposed = false;

        public BatchPlotUpdateService()
        {
            _throttleService = new PlotUpdateThrottleService
            {
                ThrottleDelayMs = 150, // 批量更新使用稍长的延迟
                MaxWaitTimeMs = 800
            };
        }

        /// <summary>
        /// 添加图表更新操作到批次中
        /// </summary>
        /// <param name="plotId">图表标识符</param>
        /// <param name="updateAction">更新操作</param>
        public void AddPlotUpdate(string plotId, Action updateAction)
        {
            if (_disposed) return;

            lock (_lockObject)
            {
                _pendingUpdates[plotId] = updateAction;
                
                // 请求批量执行
                _throttleService.RequestUpdate(ExecuteBatchUpdates);
            }
        }

        /// <summary>
        /// 立即执行所有待定的批量更新
        /// </summary>
        public void FlushBatchUpdates()
        {
            if (_disposed) return;

            lock (_lockObject)
            {
                ExecuteBatchUpdates();
            }
        }

        private void ExecuteBatchUpdates()
        {
            if (_disposed) return;

            Dictionary<string, Action> updatesToExecute;
            
            lock (_lockObject)
            {
                if (_pendingUpdates.Count == 0) return;
                
                updatesToExecute = new Dictionary<string, Action>(_pendingUpdates);
                _pendingUpdates.Clear();
            }

            // 按顺序执行所有更新
            foreach (var kvp in updatesToExecute)
            {
                try
                {
                    kvp.Value();
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"批量图表更新异常 [{kvp.Key}]: {ex.Message}");
                }
            }
        }

        public void Dispose()
        {
            if (_disposed) return;

            _disposed = true;
            _throttleService?.Dispose();
            
            lock (_lockObject)
            {
                _pendingUpdates.Clear();
            }

            GC.SuppressFinalize(this);
        }

        ~BatchPlotUpdateService()
        {
            Dispose();
        }
    }
}