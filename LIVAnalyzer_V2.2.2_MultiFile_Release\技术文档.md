# LIV分析工具 v2.2.2 技术文档

## 📋 技术概览

LIV分析工具是基于.NET 9框架开发的专业激光器测试数据分析软件，采用现代化的WPF架构和OxyPlot图表库，提供高性能的数据处理和可视化功能。

### 核心技术栈
- **框架**: .NET 9.0 (C# 13)
- **UI框架**: WPF + Fluent Design
- **图表库**: OxyPlot 2.1+
- **数据处理**: EPPlus (Excel), CsvHelper (CSV)
- **架构模式**: MVVM + 依赖注入
- **异步处理**: Task-based Asynchronous Pattern (TAP)

## 🏗️ 系统架构

### 分层架构设计
```
┌─────────────────────────────────────┐
│           UI Layer (WPF)            │
│  ViewModels, Views, Controls        │
├─────────────────────────────────────┤
│         Services Layer              │
│  PlotManager, DocumentService       │
├─────────────────────────────────────┤
│          Core Layer                 │
│  Processors, Algorithms, Exporters  │
├─────────────────────────────────────┤
│          Data Layer                 │
│  Loaders, Interfaces, Models        │
└─────────────────────────────────────┘
```

### 核心模块

#### 1. 数据加载模块 (LIVAnalyzer.Data)
- **ExcelDataLoader**: Excel文件读取和解析
- **CsvDataLoader**: CSV文件读取和解析
- **并行加载**: 多文件并发处理
- **错误处理**: 文件格式验证和错误恢复

#### 2. 数据处理模块 (LIVAnalyzer.Core)
- **LIVDataProcessor**: LIV曲线分析算法
- **DivergenceProcessor**: 发散角计算算法
- **SpectrumProcessor**: 光谱数据处理
- **数据平滑**: 多种平滑算法支持

#### 3. 图表管理模块 (LIVAnalyzer.UI.Services)
- **OptimizedPlotManager**: 高性能图表管理
- **增量更新**: 智能图表更新机制
- **批量操作**: 批量系列添加和更新
- **自动缩放**: 智能轴范围调整

#### 4. 用户界面模块 (LIVAnalyzer.UI)
- **MainWindowViewModel**: 主界面逻辑
- **图表控件**: 自定义图表显示控件
- **主题系统**: 动态主题切换支持
- **响应式设计**: 自适应布局

## ⚡ 性能优化技术

### 1. 并行处理优化
```csharp
// 并行文件加载
var loadTasks = selectedFiles.Select(async file => 
{
    var data = await LoadFileDataAsync(file.FilePath);
    return new { File = file, Data = data };
}).ToArray();

await Task.WhenAll(loadTasks);
```

### 2. 增量图表更新
```csharp
// 智能增量更新
private async Task UpdateLIVPlotIncremental(List<FileViewModel> selectedFiles)
{
    var existingSeries = LIVPlotModel.Series.ToDictionary(s => s.Title, s => s);
    var selectedFileNames = selectedFiles.Select(f => f.FileName).ToHashSet();
    
    // 移除不再选中的系列
    var seriesToRemove = existingSeries.Keys
        .Where(title => !selectedFileNames.Contains(title)).ToList();
    
    // 只添加新选中的系列
    var newSeries = selectedFiles
        .Where(f => !existingSeries.ContainsKey(f.FileName))
        .ToList();
}
```

### 3. 批量Excel读取
```csharp
// 批量读取Excel数据
private void LoadPowerDataOptimized(ExcelPackage package, FileDataModel data)
{
    var worksheet = package.Workbook.Worksheets["power"];
    if (worksheet?.Dimension == null) return;
    
    // 一次性读取整个数据范围
    var values = worksheet.Cells[1, 1, worksheet.Dimension.End.Row, 2].Value;
    
    // 批量处理数据
    var dataPoints = ProcessDataBatch(values);
    data.CurrentPowerData = dataPoints;
}
```

### 4. 自动轴范围调整
```csharp
// 智能轴范围调整
if (seriesDataList.Any())
{
    await _plotManager.BatchUpdateSeriesAsync(PlotModel, seriesDataList, cancellationToken, false);
    
    // 自动调整轴范围
    PlotModel.ResetAllAxes();
    PlotModel.InvalidatePlot(true);
}
```

## 🔬 核心算法

### 1. LIV参数计算
- **阈值电流**: 基于二阶导数的精确计算
- **斜率效率**: 线性拟合算法
- **串联电阻**: 电压-电流特性分析
- **最大效率**: 统计异常值检测

### 2. 发散角分析
- **FWHM**: 半高全宽计算
- **1/e²宽度**: 基于强度阈值的宽度计算
- **FW95%**: 功率包含方法
- **能量占比**: 积分计算

### 3. 数据平滑算法
- **移动平均**: 简单高效的噪声处理
- **Savitzky-Golay**: 保持数据特征的高精度滤波
- **实时重计算**: 参数变化时的动态更新

### 4. 错误处理算法
```csharp
// 数据清理算法
private static string CleanNumericString(string? input)
{
    if (string.IsNullOrEmpty(input)) return "0";
    
    // 移除无效字符
    var cleaned = Regex.Replace(input, @"[^\d\.\-\+eE]", "");
    
    // 处理多个小数点
    var parts = cleaned.Split('.');
    if (parts.Length > 2)
    {
        cleaned = parts[0] + "." + string.Join("", parts.Skip(1));
    }
    
    return string.IsNullOrEmpty(cleaned) || cleaned == "-" || cleaned == "+" ? "0" : cleaned;
}
```

## 📊 数据格式支持

### Excel文件格式 (.xlsx/.xls)
```
工作表结构：
├── power    - 功率数据 (电流, 功率)
├── voltage  - 电压数据 (电流, 电压)
├── wavelength - 光谱数据 (波长, 强度)
├── HFF      - 水平发散角 (角度, 强度)
└── VFF      - 垂直发散角 (角度, 强度)
```

### CSV文件格式 (.csv)
```
文件命名规则：
├── filename_power.csv     - 功率数据
├── filename_voltage.csv   - 电压数据
├── filename_wavelength.csv - 光谱数据
├── filename_HFF.csv       - 水平发散角
└── filename_VFF.csv       - 垂直发散角
```

## 🔧 配置和扩展

### 1. 图表配置
- **颜色方案**: 支持自定义颜色配置
- **线型样式**: 多种线型和标记类型
- **轴设置**: 自定义轴范围和格式
- **图例配置**: 灵活的图例显示选项

### 2. 数据处理配置
- **平滑参数**: 可调节的平滑窗口大小
- **计算精度**: 可配置的数值精度
- **错误阈值**: 自定义错误检测阈值
- **缓存策略**: 可配置的缓存行为

### 3. 性能配置
- **并行度**: 可调节的并行处理线程数
- **内存限制**: 大文件处理的内存管理
- **超时设置**: 操作超时时间配置
- **日志级别**: 可配置的日志详细程度

## 🛠️ 开发和调试

### 构建要求
- **SDK**: .NET 9.0 SDK
- **IDE**: Visual Studio 2022 17.8+ 或 JetBrains Rider
- **操作系统**: Windows 10/11 (开发环境)

### 调试工具
- **日志系统**: 结构化日志记录
- **性能监控**: 内置性能计数器
- **错误追踪**: 详细的异常信息
- **内存分析**: 内存使用情况监控

### 测试策略
- **单元测试**: 核心算法测试
- **集成测试**: 模块间交互测试
- **性能测试**: 大数据集处理测试
- **用户测试**: 真实场景验证

## 📈 性能指标

### 基准测试结果
- **文件加载**: 100MB Excel文件 < 5秒
- **图表渲染**: 10000点数据 < 1秒
- **内存使用**: 典型场景 < 500MB
- **启动时间**: 冷启动 < 3秒

### 扩展性指标
- **最大文件数**: 支持100+文件同时处理
- **最大数据点**: 单系列支持100万+数据点
- **并发处理**: 支持CPU核心数的并行处理
- **内存效率**: 大数据集的流式处理

## 🔒 安全和稳定性

### 错误处理策略
- **优雅降级**: 部分功能失败时的备用方案
- **资源保护**: 防止内存泄漏和资源耗尽
- **数据验证**: 输入数据的完整性检查
- **异常恢复**: 自动错误恢复机制

### 数据安全
- **本地处理**: 所有数据本地处理，不上传云端
- **文件保护**: 只读方式访问原始数据文件
- **临时文件**: 自动清理临时文件
- **配置加密**: 敏感配置信息加密存储

---

**技术支持**: 如有技术问题，请联系开发者：00106  
**文档版本**: v2.2.2 (2025年8月6日)
