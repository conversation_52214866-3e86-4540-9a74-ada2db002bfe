<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:ui="http://schemas.modernwpf.com/2019">

    <!-- 暗黑主题颜色定义 -->
    
    <!-- 主题色 -->
    <Color x:Key="DarkAccentColor">#0084FF</Color>
    <Color x:Key="DarkAccentColorLight">#40A4FF</Color>
    <Color x:Key="DarkAccentColorDark">#0066CC</Color>
    
    <!-- 背景色层级 -->
    <Color x:Key="DarkBackgroundLevel0">#121212</Color>
    <Color x:Key="DarkBackgroundLevel1">#1E1E1E</Color>
    <Color x:Key="DarkBackgroundLevel2">#252526</Color>
    <Color x:Key="DarkBackgroundLevel3">#2D2D30</Color>
    <Color x:Key="DarkBackgroundLevel4">#383838</Color>
    
    <!-- 文字颜色 -->
    <Color x:Key="DarkTextPrimaryColor">#FFFFFF</Color>
    <Color x:Key="DarkTextSecondaryColor">#B3B3B3</Color>
    <Color x:Key="DarkTextDisabledColor">#6B6B6B</Color>
    
    <!-- 边框颜色 -->
    <Color x:Key="DarkBorderColor">#3F3F46</Color>
    <Color x:Key="DarkBorderHoverColor">#5A5A5A</Color>
    
    <!-- 图表配色方案 -->
    <Color x:Key="ChartColor1">#4FC3F7</Color>
    <Color x:Key="ChartColor2">#81C784</Color>
    <Color x:Key="ChartColor3">#FFB74D</Color>
    <Color x:Key="ChartColor4">#E57373</Color>
    <Color x:Key="ChartColor5">#BA68C8</Color>
    <Color x:Key="ChartColor6">#64B5F6</Color>
    <Color x:Key="ChartGridColor">#404040</Color>
    
    <!-- 画刷定义 -->
    <SolidColorBrush x:Key="DarkBackground0" Color="{StaticResource DarkBackgroundLevel0}"/>
    <SolidColorBrush x:Key="DarkBackground1" Color="{StaticResource DarkBackgroundLevel1}"/>
    <SolidColorBrush x:Key="DarkBackground2" Color="{StaticResource DarkBackgroundLevel2}"/>
    <SolidColorBrush x:Key="DarkBackground3" Color="{StaticResource DarkBackgroundLevel3}"/>
    <SolidColorBrush x:Key="DarkBackground4" Color="{StaticResource DarkBackgroundLevel4}"/>
    
    <SolidColorBrush x:Key="DarkTextPrimary" Color="{StaticResource DarkTextPrimaryColor}"/>
    <SolidColorBrush x:Key="DarkTextSecondary" Color="{StaticResource DarkTextSecondaryColor}"/>
    <SolidColorBrush x:Key="DarkTextDisabled" Color="{StaticResource DarkTextDisabledColor}"/>
    
    <SolidColorBrush x:Key="DarkBorder" Color="{StaticResource DarkBorderColor}"/>
    <SolidColorBrush x:Key="DarkBorderHover" Color="{StaticResource DarkBorderHoverColor}"/>
    
    <!-- 卡片样式 -->
    <Style x:Key="DarkCard" TargetType="Border">
        <Setter Property="Background" Value="{StaticResource DarkBackground2}"/>
        <Setter Property="BorderBrush" Value="{StaticResource DarkBorder}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="CornerRadius" Value="8"/>
        <Setter Property="Padding" Value="16"/>
        <Setter Property="Margin" Value="8"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect BlurRadius="16" 
                                  ShadowDepth="4" 
                                  Opacity="0.3" 
                                  Color="Black"
                                  Direction="270"/>
            </Setter.Value>
        </Setter>
    </Style>
    
    <!-- 按钮样式 -->
    <Style x:Key="DarkButton" TargetType="Button" BasedOn="{StaticResource DefaultButtonStyle}">
        <Setter Property="Background" Value="{StaticResource DarkBackground3}"/>
        <Setter Property="Foreground" Value="{StaticResource DarkTextPrimary}"/>
        <Setter Property="BorderBrush" Value="{StaticResource DarkBorder}"/>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="{StaticResource DarkBackground4}"/>
                <Setter Property="BorderBrush" Value="{StaticResource DarkBorderHover}"/>
            </Trigger>
        </Style.Triggers>
    </Style>
    
    <!-- 主题按钮 -->
    <Style x:Key="AccentButton" TargetType="Button" BasedOn="{StaticResource AccentButtonStyle}">
        <Setter Property="Margin" Value="4"/>
        <Setter Property="MinHeight" Value="36"/>
        <Setter Property="MinWidth" Value="100"/>
    </Style>
    
    <!-- 输入框样式 -->
    <Style x:Key="DarkTextBox" TargetType="TextBox" BasedOn="{StaticResource DefaultTextBoxStyle}">
        <Setter Property="Background" Value="{StaticResource DarkBackground1}"/>
        <Setter Property="Foreground" Value="{StaticResource DarkTextPrimary}"/>
        <Setter Property="BorderBrush" Value="{StaticResource DarkBorder}"/>
        <Setter Property="SelectionBrush" Value="{StaticResource DarkAccentColorLight}"/>
    </Style>
    
    <!-- 标签样式 -->
    <Style x:Key="DarkLabel" TargetType="TextBlock">
        <Setter Property="Foreground" Value="{StaticResource DarkTextSecondary}"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="Margin" Value="0,0,0,4"/>
    </Style>
    
    <!-- 标题样式 -->
    <Style x:Key="DarkTitle" TargetType="TextBlock">
        <Setter Property="Foreground" Value="{StaticResource DarkTextPrimary}"/>
        <Setter Property="FontSize" Value="20"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Margin" Value="0,0,0,16"/>
    </Style>
    
    <!-- 分组框样式 -->
    <Style x:Key="DarkGroupBox" TargetType="GroupBox">
        <Setter Property="BorderBrush" Value="{StaticResource DarkBorder}"/>
        <Setter Property="Foreground" Value="{StaticResource DarkTextPrimary}"/>
        <Setter Property="Margin" Value="0,0,0,12"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="GroupBox">
                    <Grid>
                        <Border Background="{StaticResource DarkBackground2}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="1"
                                CornerRadius="8"
                                Margin="0,10,0,0">
                            <ContentPresenter Margin="16,16,16,16"/>
                        </Border>
                        <Label Content="{TemplateBinding Header}"
                               Background="{StaticResource DarkBackground1}"
                               Foreground="{StaticResource DarkTextPrimary}"
                               Padding="8,2"
                               Margin="16,0,0,0"
                               HorizontalAlignment="Left"
                               VerticalAlignment="Top"/>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    
    <!-- TabControl 样式 -->
    <Style x:Key="DarkTabControl" TargetType="TabControl">
        <Setter Property="Background" Value="{StaticResource DarkBackground1}"/>
        <Setter Property="BorderBrush" Value="{StaticResource DarkBorder}"/>
    </Style>
    
    <!-- DataGrid 样式 -->
    <Style x:Key="DarkDataGrid" TargetType="DataGrid" BasedOn="{StaticResource DefaultDataGridStyle}">
        <Setter Property="Background" Value="{StaticResource DarkBackground1}"/>
        <Setter Property="Foreground" Value="{StaticResource DarkTextPrimary}"/>
        <Setter Property="BorderBrush" Value="{StaticResource DarkBorder}"/>
        <Setter Property="GridLinesVisibility" Value="Horizontal"/>
        <Setter Property="HorizontalGridLinesBrush" Value="{StaticResource DarkBorder}"/>
        <Setter Property="RowBackground" Value="{StaticResource DarkBackground2}"/>
        <Setter Property="AlternatingRowBackground" Value="{StaticResource DarkBackground3}"/>
    </Style>
    
    <!-- 进度条样式 -->
    <Style x:Key="DarkProgressBar" TargetType="ProgressBar" BasedOn="{StaticResource DefaultProgressBarStyle}">
        <Setter Property="Foreground">
            <Setter.Value>
                <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                    <GradientStop Color="{StaticResource DarkAccentColor}" Offset="0"/>
                    <GradientStop Color="{StaticResource DarkAccentColorLight}" Offset="1"/>
                </LinearGradientBrush>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- ScrollViewer 暗黑样式 -->
    <Style x:Key="DarkScrollViewer" TargetType="ScrollViewer">
        <Setter Property="Background" Value="{StaticResource DarkBackground1}"/>
        <Setter Property="Foreground" Value="{StaticResource DarkTextPrimary}"/>
    </Style>

    <!-- Expander 暗黑样式 -->
    <Style x:Key="DarkExpander" TargetType="Expander" BasedOn="{StaticResource DefaultExpanderStyle}">
        <Setter Property="Background" Value="{StaticResource DarkBackground2}"/>
        <Setter Property="Foreground" Value="{StaticResource DarkTextPrimary}"/>
        <Setter Property="BorderBrush" Value="{StaticResource DarkBorder}"/>
    </Style>

    <!-- StackPanel 暗黑样式 -->
    <Style x:Key="DarkStackPanel" TargetType="StackPanel">
        <Setter Property="Background" Value="{StaticResource DarkBackground1}"/>
    </Style>

    <!-- Grid 暗黑样式 -->
    <Style x:Key="DarkGrid" TargetType="Grid">
        <Setter Property="Background" Value="{StaticResource DarkBackground1}"/>
    </Style>

    <!-- Border 暗黑样式 -->
    <Style x:Key="DarkBorderStyle" TargetType="Border">
        <Setter Property="Background" Value="{StaticResource DarkBackground1}"/>
        <Setter Property="BorderBrush" Value="{StaticResource DarkBorder}"/>
    </Style>
    
    <!-- 工具提示样式 -->
    <Style x:Key="DarkToolTip" TargetType="ToolTip">
        <Setter Property="Background" Value="{StaticResource DarkBackground4}"/>
        <Setter Property="Foreground" Value="{StaticResource DarkTextPrimary}"/>
        <Setter Property="BorderBrush" Value="{StaticResource DarkBorder}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Padding" Value="8,4"/>
    </Style>

</ResourceDictionary>