using LIVAnalyzer.Models;
using System.Globalization;

namespace LIVAnalyzer.Data.Loaders
{
    /// <summary>
    /// 渐进式数据读取器 - 先读取稀疏数据点，再逐步完善
    /// </summary>
    public class ProgressiveDataReader
    {
        public event EventHandler<DataProgressEventArgs>? DataUpdated;
        
        /// <summary>
        /// 渐进式读取CSV数据
        /// </summary>
        public async Task<LIVMeasurementData?> ReadCsvProgressivelyAsync(string filePath, CancellationToken cancellationToken = default)
        {
            try
            {
                var fileName = Path.GetFileName(filePath);
                var data = new LIVMeasurementData { FileName = fileName };
                
                // 读取所有行
                var lines = await File.ReadAllLinesAsync(filePath, cancellationToken);
                
                // 查找数据开始行
                int dataStart = -1;
                for (int i = 0; i < lines.Length; i++)
                {
                    if (lines[i].StartsWith("Sample No;"))
                    {
                        dataStart = i;
                        break;
                    }
                }
                
                if (dataStart == -1)
                {
                    throw new InvalidOperationException("CSV文件格式错误：找不到'Sample No;'标题行");
                }
                
                var headers = lines[dataStart].Split(';');
                var dataLines = lines.Skip(dataStart + 1).Where(line => !string.IsNullOrWhiteSpace(line)).ToArray();
                
                if (dataLines.Length == 0) return data;
                
                // 渐进式读取策略
                await ReadDataProgressively(data, headers, dataLines, cancellationToken);
                
                return data;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"渐进式加载CSV文件失败: {ex.Message}", ex);
            }
        }
        
        /// <summary>
        /// 渐进式读取数据点
        /// </summary>
        private async Task ReadDataProgressively(LIVMeasurementData data, string[] headers, string[] dataLines, CancellationToken cancellationToken)
        {
            var totalLines = dataLines.Length;
            
            // 第一阶段：读取稀疏数据点（每隔8个点读1个）
            await ReadSparseData(data, headers, dataLines, 8, "粗略数据", cancellationToken);
            
            // 第二阶段：填充一半间隔（每隔4个点读1个）
            await ReadSparseData(data, headers, dataLines, 4, "中等精度", cancellationToken);
            
            // 第三阶段：填充更多间隔（每隔2个点读1个）
            await ReadSparseData(data, headers, dataLines, 2, "较高精度", cancellationToken);
            
            // 第四阶段：读取所有剩余数据点
            await ReadSparseData(data, headers, dataLines, 1, "完整数据", cancellationToken);
        }
        
        /// <summary>
        /// 读取指定间隔的数据点
        /// </summary>
        private async Task ReadSparseData(LIVMeasurementData data, string[] headers, string[] dataLines, int interval, string stage, CancellationToken cancellationToken)
        {
            var newPoints = 0;
            
            for (int i = 0; i < dataLines.Length; i += interval)
            {
                if (cancellationToken.IsCancellationRequested) break;
                
                try
                {
                    var values = dataLines[i].Split(';');
                    
                    // 检查是否已经读取过这个数据点
                    var sampleNo = ParseDouble(values, headers, "Sample No");
                    if (sampleNo.HasValue && IsDataPointAlreadyRead(data, sampleNo.Value))
                        continue;
                    
                    // 解析数据点
                    var current = ParseDouble(values, headers, "current");
                    var power = ParseDouble(values, headers, "Power");
                    var voltage = ParseDouble(values, headers, "Voltage");
                    var wavelength = ParseDouble(values, headers, "wavelength");
                    var intensity = ParseDouble(values, headers, "intensity");
                    
                    // 添加新数据点
                    if (current.HasValue && power.HasValue)
                    {
                        data.CurrentPowerData.Add(new DataPoint(current.Value, power.Value));
                        newPoints++;
                    }
                    
                    if (current.HasValue && voltage.HasValue)
                    {
                        data.CurrentVoltageData.Add(new DataPoint(current.Value, voltage.Value));
                    }
                    
                    if (wavelength.HasValue && intensity.HasValue)
                    {
                        data.WavelengthIntensityData.Add(new DataPoint(wavelength.Value, intensity.Value));
                    }
                    
                    // 每读取一定数量的点就通知UI更新
                    if (newPoints % Math.Max(1, interval) == 0)
                    {
                        await Task.Delay(1, cancellationToken); // 让出CPU时间
                        
                        // 通知UI更新
                        DataUpdated?.Invoke(this, new DataProgressEventArgs
                        {
                            Data = data,
                            Stage = stage,
                            Progress = (double)i / dataLines.Length * 100,
                            NewPointsCount = newPoints
                        });
                    }
                }
                catch
                {
                    // 跳过解析失败的行
                    continue;
                }
            }
            
            // 对数据进行排序（因为可能不是按顺序读取的）
            SortDataPoints(data);
            
            // 最终通知这个阶段完成
            DataUpdated?.Invoke(this, new DataProgressEventArgs
            {
                Data = data,
                Stage = stage + " - 完成",
                Progress = 100,
                NewPointsCount = newPoints
            });
        }
        
        /// <summary>
        /// 检查数据点是否已经读取过
        /// </summary>
        private bool IsDataPointAlreadyRead(LIVMeasurementData data, double sampleNo)
        {
            // 简化检查：看是否有相近的电流值
            return data.CurrentPowerData.Any(p => Math.Abs(p.X - sampleNo) < 0.001);
        }
        
        /// <summary>
        /// 对数据点进行排序
        /// </summary>
        private void SortDataPoints(LIVMeasurementData data)
        {
            if (data.CurrentPowerData.Any())
            {
                var sortedPower = data.CurrentPowerData.OrderBy(p => p.X).ToList();
                data.CurrentPowerData.Clear();
                data.CurrentPowerData.AddRange(sortedPower);
            }
            
            if (data.CurrentVoltageData.Any())
            {
                var sortedVoltage = data.CurrentVoltageData.OrderBy(p => p.X).ToList();
                data.CurrentVoltageData.Clear();
                data.CurrentVoltageData.AddRange(sortedVoltage);
            }
            
            if (data.WavelengthIntensityData.Any())
            {
                var sortedWavelength = data.WavelengthIntensityData.OrderBy(p => p.X).ToList();
                data.WavelengthIntensityData.Clear();
                data.WavelengthIntensityData.AddRange(sortedWavelength);
            }
        }
        
        /// <summary>
        /// 解析双精度数值
        /// </summary>
        private double? ParseDouble(string[] values, string[] headers, string columnName)
        {
            var index = Array.IndexOf(headers, columnName);
            if (index == -1 || index >= values.Length) return null;
            
            if (double.TryParse(values[index], out var result))
            {
                // 应用数据清理规则：负值清零
                if (columnName.Contains("current") || columnName.Contains("Power") || columnName.Contains("Voltage"))
                {
                    return Math.Max(0, result);
                }
                return result;
            }
            return null;
        }
    }
    
    /// <summary>
    /// 数据进度事件参数
    /// </summary>
    public class DataProgressEventArgs : EventArgs
    {
        public LIVMeasurementData? Data { get; set; }
        public string Stage { get; set; } = string.Empty;
        public double Progress { get; set; }
        public int NewPointsCount { get; set; }
    }
}
