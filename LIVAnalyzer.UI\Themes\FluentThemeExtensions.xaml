<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- Fluent Design 扩展资源 - 基于 .NET 9 原生 Fluent 主题 -->
    
    <!-- 应用程序级别的基础颜色定义 -->
    <!-- 这些颜色将根据系统主题自动调整 -->
    
    <!-- 通用间距和尺寸定义 -->
    <Thickness x:Key="FluentCardMargin">12</Thickness>
    <Thickness x:Key="FluentCardPadding">16</Thickness>
    <CornerRadius x:Key="FluentCardCornerRadius">8</CornerRadius>
    <CornerRadius x:Key="FluentButtonCornerRadius">4</CornerRadius>
    
    <!-- 阴影效果定义 -->
    <DropShadowEffect x:Key="FluentCardShadow" 
                      BlurRadius="16" 
                      ShadowDepth="4" 
                      Opacity="0.15" 
                      Direction="270"/>
    
    <DropShadowEffect x:Key="FluentButtonShadow" 
                      BlurRadius="8" 
                      ShadowDepth="2" 
                      Opacity="0.1" 
                      Direction="270"/>
    
    <!-- 动画缓动函数 -->
    <CubicEase x:Key="FluentEaseInOut" EasingMode="EaseInOut"/>
    <QuadraticEase x:Key="FluentEaseOut" EasingMode="EaseOut"/>
    
    <!-- 字体定义 -->
    <FontFamily x:Key="FluentDisplayFontFamily">Microsoft YaHei UI</FontFamily>
    <FontFamily x:Key="FluentBodyFontFamily">Microsoft YaHei UI</FontFamily>
    
    <!-- 字体大小 -->
    <system:Double x:Key="FluentCaptionFontSize" xmlns:system="clr-namespace:System;assembly=System.Runtime">12</system:Double>
    <system:Double x:Key="FluentBodyFontSize" xmlns:system="clr-namespace:System;assembly=System.Runtime">14</system:Double>
    <system:Double x:Key="FluentSubtitleFontSize" xmlns:system="clr-namespace:System;assembly=System.Runtime">16</system:Double>
    <system:Double x:Key="FluentTitleFontSize" xmlns:system="clr-namespace:System;assembly=System.Runtime">20</system:Double>
    <system:Double x:Key="FluentLargeTitleFontSize" xmlns:system="clr-namespace:System;assembly=System.Runtime">28</system:Double>
    <system:Double x:Key="FluentDisplayFontSize" xmlns:system="clr-namespace:System;assembly=System.Runtime">68</system:Double>
    
</ResourceDictionary>