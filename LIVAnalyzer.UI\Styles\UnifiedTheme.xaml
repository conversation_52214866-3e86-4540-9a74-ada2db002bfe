<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- ==============================================
         Unified Theme System - 统一主题系统
         整合所有主题资源，消除重复定义
         ============================================== -->

    <!-- 合并设计令牌和语义化颜色 -->
    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="DesignTokens.xaml"/>
        <ResourceDictionary Source="SemanticColors.xaml"/>
    </ResourceDictionary.MergedDictionaries>

    <!-- ==============================================
         Control Styles - 控件样式
         ============================================== -->

    <!-- 统一的GroupBox样式 -->
    <Style x:Key="UnifiedGroupBox" TargetType="GroupBox">
        <Setter Property="Margin" Value="{StaticResource SmallMargin}"/>
        <Setter Property="Padding" Value="{StaticResource MediumPadding}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource OutlineBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Background" Value="{DynamicResource SurfaceVariantBrush}"/>
        <Setter Property="Foreground" Value="{DynamicResource OnSurfaceBrush}"/>
        <Setter Property="FontWeight" Value="{StaticResource MediumWeight}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="GroupBox">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="{StaticResource MediumRadius}"
                            Margin="{TemplateBinding Margin}">
                        <Border.Effect>
                            <StaticResource ResourceKey="SmallShadow"/>
                        </Border.Effect>
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>
                            <ContentPresenter Grid.Row="0"
                                            ContentSource="Header"
                                            Margin="{StaticResource MediumMargin}"
                                            TextElement.FontSize="{StaticResource LargeFontSize}"
                                            TextElement.FontWeight="{StaticResource SemiBoldWeight}"/>
                            <ContentPresenter Grid.Row="1"
                                            Margin="{TemplateBinding Padding}"/>
                        </Grid>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 统一的Button样式 -->
    <Style x:Key="UnifiedButton" TargetType="Button">
        <Setter Property="Margin" Value="{StaticResource SmallMargin}"/>
        <Setter Property="Padding" Value="{StaticResource MediumPadding}"/>
        <Setter Property="MinHeight" Value="{StaticResource MediumControlHeight}"/>
        <Setter Property="FontWeight" Value="{StaticResource MediumWeight}"/>
        <Setter Property="FontSize" Value="{StaticResource MediumFontSize}"/>
        <Setter Property="Background" Value="{DynamicResource PrimaryBrush}"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="BorderBrush" Value="{DynamicResource PrimaryBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border x:Name="border"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="{StaticResource SmallRadius}">
                        <Border.Effect>
                            <StaticResource ResourceKey="SmallShadow"/>
                        </Border.Effect>
                        <ContentPresenter HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Margin="{TemplateBinding Padding}"/>
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="CommonStates">
                                <VisualState x:Name="Normal"/>
                                <VisualState x:Name="MouseOver">
                                    <Storyboard>
                                        <ColorAnimation Storyboard.TargetName="border"
                                                      Storyboard.TargetProperty="(Border.Background).(SolidColorBrush.Color)"
                                                      To="{DynamicResource PrimaryLightColor}"
                                                      Duration="{StaticResource FastDuration}"/>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Pressed">
                                    <Storyboard>
                                        <ColorAnimation Storyboard.TargetName="border"
                                                      Storyboard.TargetProperty="(Border.Background).(SolidColorBrush.Color)"
                                                      To="{DynamicResource PrimaryDarkColor}"
                                                      Duration="{StaticResource FastDuration}"/>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Disabled">
                                    <Storyboard>
                                        <DoubleAnimation Storyboard.TargetName="border"
                                                       Storyboard.TargetProperty="Opacity"
                                                       To="{StaticResource DisabledOpacity}"
                                                       Duration="{StaticResource FastDuration}"/>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 次要按钮样式 -->
    <Style x:Key="SecondaryButton" TargetType="Button" BasedOn="{StaticResource UnifiedButton}">
        <Setter Property="Background" Value="{DynamicResource SurfaceBrush}"/>
        <Setter Property="Foreground" Value="{DynamicResource OnSurfaceBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource OutlineBrush}"/>
    </Style>

    <!-- 主题切换按钮样式 -->
    <Style x:Key="ThemeToggleButton" TargetType="Button" BasedOn="{StaticResource SecondaryButton}">
        <Setter Property="MinWidth" Value="40"/>
        <Setter Property="MinHeight" Value="40"/>
        <Setter Property="Padding" Value="{StaticResource SmallPadding}"/>
        <Setter Property="ToolTip" Value="切换主题模式"/>
    </Style>

    <!-- 统一的TextBox样式 -->
    <Style x:Key="UnifiedTextBox" TargetType="TextBox">
        <Setter Property="Margin" Value="{StaticResource XSMargin}"/>
        <Setter Property="Padding" Value="{StaticResource SmallPadding}"/>
        <Setter Property="MinHeight" Value="{StaticResource SmallControlHeight}"/>
        <Setter Property="FontSize" Value="{StaticResource MediumFontSize}"/>
        <Setter Property="Background" Value="{DynamicResource SurfaceBrush}"/>
        <Setter Property="Foreground" Value="{DynamicResource OnSurfaceBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource OutlineBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="TextBox">
                    <Border x:Name="border"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="{StaticResource SmallRadius}">
                        <ScrollViewer x:Name="PART_ContentHost"
                                    Margin="{TemplateBinding Padding}"
                                    VerticalAlignment="Center"/>
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="FocusStates">
                                <VisualState x:Name="Focused">
                                    <Storyboard>
                                        <ColorAnimation Storyboard.TargetName="border"
                                                      Storyboard.TargetProperty="(Border.BorderBrush).(SolidColorBrush.Color)"
                                                      To="{DynamicResource PrimaryColor}"
                                                      Duration="{StaticResource FastDuration}"/>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Unfocused"/>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 统一的Slider样式 -->
    <Style x:Key="UnifiedSlider" TargetType="Slider">
        <Setter Property="Margin" Value="{StaticResource XSMargin}"/>
        <Setter Property="Height" Value="{StaticResource SmallControlHeight}"/>
        <Setter Property="Background" Value="{DynamicResource OutlineVariantBrush}"/>
        <Setter Property="Foreground" Value="{DynamicResource PrimaryBrush}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Slider">
                    <Grid>
                        <Border x:Name="TrackBackground"
                                Background="{TemplateBinding Background}"
                                Height="4"
                                CornerRadius="2"
                                VerticalAlignment="Center"/>
                        <Track x:Name="PART_Track">
                            <Track.DecreaseRepeatButton>
                                <RepeatButton Command="Slider.DecreaseLarge"
                                            Style="{x:Null}">
                                    <RepeatButton.Template>
                                        <ControlTemplate TargetType="RepeatButton">
                                            <Border Background="{TemplateBinding Foreground}"
                                                    Height="4"
                                                    CornerRadius="2"/>
                                        </ControlTemplate>
                                    </RepeatButton.Template>
                                </RepeatButton>
                            </Track.DecreaseRepeatButton>
                            <Track.Thumb>
                                <Thumb>
                                    <Thumb.Template>
                                        <ControlTemplate TargetType="Thumb">
                                            <Ellipse Width="16"
                                                   Height="16"
                                                   Fill="{DynamicResource PrimaryBrush}"
                                                   Stroke="White"
                                                   StrokeThickness="2"/>
                                        </ControlTemplate>
                                    </Thumb.Template>
                                </Thumb>
                            </Track.Thumb>
                            <Track.IncreaseRepeatButton>
                                <RepeatButton Command="Slider.IncreaseLarge"
                                            Style="{x:Null}">
                                    <RepeatButton.Template>
                                        <ControlTemplate TargetType="RepeatButton">
                                            <Border Background="Transparent"/>
                                        </ControlTemplate>
                                    </RepeatButton.Template>
                                </RepeatButton>
                            </Track.IncreaseRepeatButton>
                        </Track>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 统一的CheckBox样式 -->
    <Style x:Key="UnifiedCheckBox" TargetType="CheckBox">
        <Setter Property="Margin" Value="{StaticResource SmallMargin}"/>
        <Setter Property="FontSize" Value="{StaticResource MediumFontSize}"/>
        <Setter Property="Foreground" Value="{DynamicResource OnSurfaceBrush}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="CheckBox">
                    <StackPanel Orientation="Horizontal">
                        <Border x:Name="checkBorder"
                                Width="16"
                                Height="16"
                                Background="{DynamicResource SurfaceBrush}"
                                BorderBrush="{DynamicResource OutlineBrush}"
                                BorderThickness="2"
                                CornerRadius="{StaticResource XSRadius}"
                                VerticalAlignment="Center">
                            <Path x:Name="checkMark"
                                  Data="M 2,5 L 6,9 L 14,1"
                                  Stroke="{DynamicResource PrimaryBrush}"
                                  StrokeThickness="2"
                                  Visibility="Collapsed"/>
                        </Border>
                        <ContentPresenter Margin="8,0,0,0"
                                        VerticalAlignment="Center"/>
                    </StackPanel>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsChecked" Value="True">
                            <Setter TargetName="checkMark" Property="Visibility" Value="Visible"/>
                            <Setter TargetName="checkBorder" Property="Background" Value="{DynamicResource PrimaryBrush}"/>
                            <Setter TargetName="checkMark" Property="Stroke" Value="White"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 统一的ComboBox样式 -->
    <Style x:Key="UnifiedComboBox" TargetType="ComboBox">
        <Setter Property="Margin" Value="{StaticResource XSMargin}"/>
        <Setter Property="Padding" Value="{StaticResource SmallPadding}"/>
        <Setter Property="MinHeight" Value="{StaticResource SmallControlHeight}"/>
        <Setter Property="FontSize" Value="{StaticResource MediumFontSize}"/>
        <Setter Property="Background" Value="{DynamicResource SurfaceBrush}"/>
        <Setter Property="Foreground" Value="{DynamicResource OnSurfaceBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource OutlineBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ComboBox">
                    <Grid>
                        <Border x:Name="templateRoot"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="{StaticResource SmallRadius}">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition/>
                                    <ColumnDefinition Width="32"/>
                                </Grid.ColumnDefinitions>
                                <ContentPresenter x:Name="contentPresenter"
                                                Grid.Column="0"
                                                Margin="{TemplateBinding Padding}"
                                                VerticalAlignment="Center"/>
                                <Border Grid.Column="1"
                                        Background="{DynamicResource SurfaceVariantBrush}"
                                        CornerRadius="0,4,4,0">
                                    <Path Data="M 0,0 L 4,4 L 8,0 Z"
                                          Fill="{DynamicResource OnSurfaceVariantBrush}"
                                          HorizontalAlignment="Center"
                                          VerticalAlignment="Center"/>
                                </Border>
                            </Grid>
                        </Border>
                        <Popup x:Name="PART_Popup"
                               AllowsTransparency="True"
                               Placement="Bottom"
                               PlacementTarget="{Binding ElementName=templateRoot}">
                            <Border Background="{DynamicResource SurfaceBrush}"
                                    BorderBrush="{DynamicResource OutlineBrush}"
                                    BorderThickness="1"
                                    CornerRadius="{StaticResource SmallRadius}">
                                <Border.Effect>
                                    <StaticResource ResourceKey="MediumShadow"/>
                                </Border.Effect>
                                <ScrollViewer x:Name="DropDownScrollViewer">
                                    <ItemsPresenter x:Name="ItemsPresenter"/>
                                </ScrollViewer>
                            </Border>
                        </Popup>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 统一的Expander样式 -->
    <Style x:Key="UnifiedExpander" TargetType="Expander">
        <Setter Property="Margin" Value="{StaticResource SmallMargin}"/>
        <Setter Property="Background" Value="{DynamicResource SurfaceVariantBrush}"/>
        <Setter Property="Foreground" Value="{DynamicResource OnSurfaceBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource OutlineBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Expander">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="{StaticResource MediumRadius}">
                        <Border.Effect>
                            <StaticResource ResourceKey="SmallShadow"/>
                        </Border.Effect>
                        <DockPanel>
                            <ToggleButton x:Name="HeaderSite"
                                        DockPanel.Dock="Top"
                                        IsChecked="{Binding Path=IsExpanded, RelativeSource={RelativeSource TemplatedParent}}"
                                        Padding="{StaticResource MediumPadding}"
                                        Background="Transparent"
                                        BorderThickness="0">
                                <StackPanel Orientation="Horizontal">
                                    <Path x:Name="arrow"
                                          Data="M 0,0 L 4,4 L 8,0 Z"
                                          Fill="{DynamicResource OnSurfaceBrush}"
                                          RenderTransformOrigin="0.5,0.5"
                                          Margin="0,0,8,0">
                                        <Path.RenderTransform>
                                            <RotateTransform x:Name="arrowRotation" Angle="0"/>
                                        </Path.RenderTransform>
                                    </Path>
                                    <ContentPresenter ContentSource="Header"
                                                    TextElement.FontWeight="{StaticResource MediumWeight}"/>
                                </StackPanel>
                            </ToggleButton>
                            <ContentPresenter x:Name="ExpandSite"
                                            Margin="{StaticResource MediumPadding}"
                                            Visibility="Collapsed"/>
                        </DockPanel>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsExpanded" Value="True">
                            <Setter TargetName="ExpandSite" Property="Visibility" Value="Visible"/>
                            <Trigger.EnterActions>
                                <BeginStoryboard>
                                    <Storyboard>
                                        <DoubleAnimation Storyboard.TargetName="arrowRotation"
                                                       Storyboard.TargetProperty="Angle"
                                                       To="180"
                                                       Duration="{StaticResource MediumDuration}"
                                                       EasingFunction="{StaticResource EaseInOut}"/>
                                    </Storyboard>
                                </BeginStoryboard>
                            </Trigger.EnterActions>
                            <Trigger.ExitActions>
                                <BeginStoryboard>
                                    <Storyboard>
                                        <DoubleAnimation Storyboard.TargetName="arrowRotation"
                                                       Storyboard.TargetProperty="Angle"
                                                       To="0"
                                                       Duration="{StaticResource MediumDuration}"
                                                       EasingFunction="{StaticResource EaseInOut}"/>
                                    </Storyboard>
                                </BeginStoryboard>
                            </Trigger.ExitActions>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- ==============================================
         Legacy Style Aliases - 向后兼容
         ============================================== -->
    
    <!-- 保持现有样式名称的兼容性 -->
    <Style x:Key="ParameterGroupBox" TargetType="GroupBox" BasedOn="{StaticResource UnifiedGroupBox}"/>
    <Style x:Key="NativeFluentGroupBox" TargetType="GroupBox" BasedOn="{StaticResource UnifiedGroupBox}"/>
    <Style x:Key="ToolbarButton" TargetType="Button" BasedOn="{StaticResource UnifiedButton}"/>
    <Style x:Key="ThemeToggleButtonStyle" TargetType="Button" BasedOn="{StaticResource ThemeToggleButton}"/>

</ResourceDictionary>