namespace LIVAnalyzer.Core.Configuration
{
    /// <summary>
    /// 阈值电流计算的预设配置
    /// 针对不同类型的激光器和测试条件提供优化的参数组合
    /// </summary>
    public static class ThresholdCalculationPresets
    {
        /// <summary>
        /// VCSEL (垂直腔面发射激光器) 配置
        /// 特点：阈值电流通常较低，在10-30%电流范围内
        /// 优化：较小的搜索范围，更严格的约束，较高的精度要求
        /// </summary>
        public static ThresholdCalculationConfig VCSEL => new ThresholdCalculationConfig
        {
            SearchRangeRatio = 0.4,                    // 搜索前40%电流范围
            MaxThresholdRatio = 0.25,                  // 阈值不超过25%最大电流
            SecondaryMaxThresholdRatio = 0.4,          // 次级约束40%
            PrimaryFallbackPowerRatio = 0.03,          // 3%功率回退（更敏感）
            SecondaryFallbackPowerRatio = 0.008,       // 0.8%功率回退
            TertiaryFallbackPowerRatio = 0.015,        // 1.5%功率回退
            MaxSmoothingWindow = 15,                   // 较小窗口保持精度
            DataWindowRatio = 0.15,                    // 更小的自适应窗口
            MaxDerivativeSmoothingWindow = 3,          // 最小导数平滑
            DerivativeWindowRatio = 0.25,              // 较小导数窗口
            DerivativeRatio = 0.45,                    // 稍低的半最大值比例
            MinDataPoints = 8,                         // 较少的最小点数要求
            NumericalPrecision = 1e-12                 // 更高精度
        };
        
        /// <summary>
        /// 边发射激光器 (Edge-Emitting Laser) 配置
        /// 特点：阈值电流中等，通常在30-50%电流范围内
        /// 优化：标准参数，平衡精度和稳健性
        /// </summary>
        public static ThresholdCalculationConfig EdgeEmitting => new ThresholdCalculationConfig
        {
            SearchRangeRatio = 0.6,                    // 搜索前60%电流范围
            MaxThresholdRatio = 0.4,                   // 阈值不超过40%最大电流
            SecondaryMaxThresholdRatio = 0.6,          // 次级约束60%
            PrimaryFallbackPowerRatio = 0.05,          // 标准5%功率回退
            SecondaryFallbackPowerRatio = 0.01,        // 标准1%功率回退
            TertiaryFallbackPowerRatio = 0.02,         // 标准2%功率回退
            MaxSmoothingWindow = 25,                   // 中等平滑窗口
            DataWindowRatio = 0.2,                     // 标准自适应窗口
            MaxDerivativeSmoothingWindow = 5,          // 标准导数平滑
            DerivativeWindowRatio = 0.33,              // 标准导数窗口
            DerivativeRatio = 0.5,                     // 标准半最大值
            MinDataPoints = 10,                        // 标准最小点数
            NumericalPrecision = 1e-10                 // 标准精度
        };
        
        /// <summary>
        /// 高功率激光器配置
        /// 特点：阈值电流可能较高，在50-70%电流范围内
        /// 优化：更大的搜索范围，更宽松的约束，更强的平滑
        /// </summary>
        public static ThresholdCalculationConfig HighPower => new ThresholdCalculationConfig
        {
            SearchRangeRatio = 0.8,                    // 搜索前80%电流范围
            MaxThresholdRatio = 0.6,                   // 阈值可达60%最大电流
            SecondaryMaxThresholdRatio = 0.8,          // 次级约束80%
            PrimaryFallbackPowerRatio = 0.08,          // 8%功率回退（不太敏感）
            SecondaryFallbackPowerRatio = 0.02,        // 2%功率回退
            TertiaryFallbackPowerRatio = 0.04,         // 4%功率回退
            MaxSmoothingWindow = 41,                   // 更大平滑窗口抗噪声
            DataWindowRatio = 0.25,                    // 更大自适应窗口
            MaxDerivativeSmoothingWindow = 7,          // 更强导数平滑
            DerivativeWindowRatio = 0.4,               // 更大导数窗口
            DerivativeRatio = 0.55,                    // 稍高的半最大值比例
            MinDataPoints = 15,                        // 更多最小点数要求
            NumericalPrecision = 1e-9                  // 稍低精度（更稳健）
        };
        
        /// <summary>
        /// 量子级联激光器 (Quantum Cascade Laser) 配置
        /// 特点：阈值行为可能不典型，需要特殊处理
        /// 优化：最大搜索范围，最宽松约束，强平滑
        /// </summary>
        public static ThresholdCalculationConfig QuantumCascade => new ThresholdCalculationConfig
        {
            SearchRangeRatio = 0.9,                    // 搜索前90%电流范围
            MaxThresholdRatio = 0.7,                   // 阈值可达70%最大电流
            SecondaryMaxThresholdRatio = 0.9,          // 次级约束90%
            PrimaryFallbackPowerRatio = 0.1,           // 10%功率回退
            SecondaryFallbackPowerRatio = 0.03,        // 3%功率回退
            TertiaryFallbackPowerRatio = 0.05,         // 5%功率回退
            MaxSmoothingWindow = 51,                   // 最大平滑窗口
            DataWindowRatio = 0.3,                     // 最大自适应窗口
            MaxDerivativeSmoothingWindow = 9,          // 最强导数平滑
            DerivativeWindowRatio = 0.5,               // 最大导数窗口
            DerivativeRatio = 0.6,                     // 更高的半最大值比例
            MinDataPoints = 20,                        // 最多最小点数要求
            NumericalPrecision = 1e-8                  // 最低精度（最稳健）
        };
        
        /// <summary>
        /// 低噪声实验室环境配置
        /// 特点：数据质量高，噪声小，可以使用更精细的参数
        /// 优化：最小平滑，最高精度，严格约束
        /// </summary>
        public static ThresholdCalculationConfig LowNoise => new ThresholdCalculationConfig
        {
            SearchRangeRatio = 0.5,                    // 标准搜索范围
            MaxThresholdRatio = 0.3,                   // 严格约束
            SecondaryMaxThresholdRatio = 0.5,          // 标准次级约束
            PrimaryFallbackPowerRatio = 0.02,          // 更敏感的回退
            SecondaryFallbackPowerRatio = 0.005,       // 非常敏感的回退
            TertiaryFallbackPowerRatio = 0.01,         // 敏感的回退
            MaxSmoothingWindow = 9,                    // 最小平滑保持细节
            DataWindowRatio = 0.1,                     // 最小自适应窗口
            MaxDerivativeSmoothingWindow = 3,          // 最小导数平滑
            DerivativeWindowRatio = 0.2,               // 最小导数窗口
            DerivativeRatio = 0.4,                     // 更敏感的半最大值
            MinDataPoints = 6,                         // 最少点数要求
            NumericalPrecision = 1e-14                 // 最高精度
        };
        
        /// <summary>
        /// 高噪声工业环境配置
        /// 特点：数据噪声大，需要强平滑和宽松约束
        /// 优化：最大平滑，宽松约束，稳健性优先
        /// </summary>
        public static ThresholdCalculationConfig HighNoise => new ThresholdCalculationConfig
        {
            SearchRangeRatio = 0.6,                    // 稍大搜索范围
            MaxThresholdRatio = 0.5,                   // 宽松约束
            SecondaryMaxThresholdRatio = 0.7,          // 更宽松次级约束
            PrimaryFallbackPowerRatio = 0.1,           // 不敏感的回退
            SecondaryFallbackPowerRatio = 0.03,        // 不敏感的回退
            TertiaryFallbackPowerRatio = 0.05,         // 不敏感的回退
            MaxSmoothingWindow = 61,                   // 最大平滑抗噪声
            DataWindowRatio = 0.4,                     // 大自适应窗口
            MaxDerivativeSmoothingWindow = 11,         // 强导数平滑
            DerivativeWindowRatio = 0.6,               // 大导数窗口
            DerivativeRatio = 0.6,                     // 不敏感的半最大值
            MinDataPoints = 25,                        // 更多点数要求
            NumericalPrecision = 1e-8                  // 低精度（稳健）
        };
        
        /// <summary>
        /// 根据激光器类型获取推荐配置
        /// </summary>
        /// <param name="laserType">激光器类型</param>
        /// <returns>推荐的配置</returns>
        public static ThresholdCalculationConfig GetRecommendedConfig(string laserType)
        {
            return laserType?.ToLowerInvariant() switch
            {
                "vcsel" => VCSEL,
                "edge-emitting" or "edge_emitting" or "edgeemitting" => EdgeEmitting,
                "high-power" or "high_power" or "highpower" => HighPower,
                "quantum-cascade" or "quantum_cascade" or "quantumcascade" or "qcl" => QuantumCascade,
                "low-noise" or "low_noise" or "lownoise" => LowNoise,
                "high-noise" or "high_noise" or "highnoise" => HighNoise,
                _ => EdgeEmitting // 默认使用边发射激光器配置
            };
        }
    }
}
