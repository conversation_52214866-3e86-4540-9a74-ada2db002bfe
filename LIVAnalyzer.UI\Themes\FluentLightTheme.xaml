<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:sys="clr-namespace:System;assembly=mscorlib">

    <!-- Fluent Design 浅色主题配色方案 - 全面覆盖 ModernWpfUI 系统资源 -->
    
    <!-- ====================== 基础背景和前景色 ====================== -->
    <SolidColorBrush x:Key="SystemControlPageBackgroundAltHighBrush" Color="#F8F9FA"/>
    <SolidColorBrush x:Key="SystemControlPageBackgroundBrush" Color="#F8F9FA"/>
    <SolidColorBrush x:Key="SystemControlBackgroundBaseLowBrush" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="SystemControlBackgroundBaseMediumLowBrush" Color="#F3F4F6"/>
    <SolidColorBrush x:Key="SystemControlBackgroundBaseMediumBrush" Color="#E5E7EB"/>
    <SolidColorBrush x:Key="SystemControlBackgroundBaseMediumHighBrush" Color="#D1D5DB"/>
    <SolidColorBrush x:Key="SystemControlBackgroundBaseHighBrush" Color="#9CA3AF"/>
    <SolidColorBrush x:Key="SystemControlBackgroundAltLowBrush" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="SystemControlBackgroundAltMediumLowBrush" Color="#F9FAFB"/>
    <SolidColorBrush x:Key="SystemControlBackgroundAltMediumBrush" Color="#F3F4F6"/>
    <SolidColorBrush x:Key="SystemControlBackgroundAltMediumHighBrush" Color="#E5E7EB"/>
    <SolidColorBrush x:Key="SystemControlBackgroundAltHighBrush" Color="#D1D5DB"/>
    
    <!-- 前景色系统 -->
    <SolidColorBrush x:Key="SystemControlForegroundBaseHighBrush" Color="#1F2937"/>
    <SolidColorBrush x:Key="SystemControlForegroundBaseMediumHighBrush" Color="#374151"/>
    <SolidColorBrush x:Key="SystemControlForegroundBaseMediumBrush" Color="#4B5563"/>
    <SolidColorBrush x:Key="SystemControlForegroundBaseMediumLowBrush" Color="#6B7280"/>
    <SolidColorBrush x:Key="SystemControlForegroundBaseLowBrush" Color="#9CA3AF"/>
    <SolidColorBrush x:Key="SystemControlForegroundAltHighBrush" Color="#1F2937"/>
    <SolidColorBrush x:Key="SystemControlForegroundAltMediumHighBrush" Color="#374151"/>
    <SolidColorBrush x:Key="SystemControlForegroundAltMediumBrush" Color="#4B5563"/>
    
    <!-- ====================== 按钮系统资源 ====================== -->
    <SolidColorBrush x:Key="SystemControlButtonFacePointerOverBrush" Color="#E5E7EB"/>
    <SolidColorBrush x:Key="SystemControlButtonFacePressedBrush" Color="#D1D5DB"/>
    <SolidColorBrush x:Key="SystemControlButtonFaceBrush" Color="#F3F4F6"/>
    <SolidColorBrush x:Key="SystemControlButtonBorderBrush" Color="#D1D5DB"/>
    <SolidColorBrush x:Key="SystemControlButtonBorderPointerOverBrush" Color="#0078D4"/>
    <SolidColorBrush x:Key="SystemControlButtonBorderPressedBrush" Color="#106EBE"/>
    <SolidColorBrush x:Key="SystemControlButtonForegroundBrush" Color="#1F2937"/>
    <SolidColorBrush x:Key="SystemControlButtonForegroundPointerOverBrush" Color="#1F2937"/>
    <SolidColorBrush x:Key="SystemControlButtonForegroundPressedBrush" Color="#1F2937"/>
    
    <!-- 高亮按钮（Accent Button）-->
    <SolidColorBrush x:Key="SystemControlHighlightAccentBrush" Color="#0078D4"/>
    <SolidColorBrush x:Key="SystemControlHighlightAltBaseBrush" Color="#E5E7EB"/>
    <SolidColorBrush x:Key="SystemControlHighlightAltAccentBrush" Color="#4FC3F7"/>
    <SolidColorBrush x:Key="SystemControlHighlightBaseLowBrush" Color="#005A9E"/>
    <SolidColorBrush x:Key="SystemControlHighlightBaseMediumLowBrush" Color="#106EBE"/>
    <SolidColorBrush x:Key="SystemControlHighlightBaseMediumBrush" Color="#0078D4"/>
    
    <!-- ====================== 输入控件系统资源 ====================== -->
    <SolidColorBrush x:Key="SystemControlBackgroundChromeLowBrush" Color="#F8F9FA"/>
    <SolidColorBrush x:Key="SystemControlBackgroundChromeMediumLowBrush" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="SystemControlBackgroundChromeMediumBrush" Color="#F3F4F6"/>
    <SolidColorBrush x:Key="SystemControlBackgroundChromeHighBrush" Color="#E5E7EB"/>
    
    <!-- Chrome 系列 - AppStyles 使用的资源 -->
    <SolidColorBrush x:Key="SystemControlPageBackgroundChromeLowBrush" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="SystemControlPageBackgroundChromeMediumBrush" Color="#F3F4F6"/>
    <SolidColorBrush x:Key="SystemControlPageBackgroundChromeHighBrush" Color="#E5E7EB"/>
    <SolidColorBrush x:Key="SystemControlPageBackgroundAltMediumBrush" Color="#F3F4F6"/>
    
    <!-- 透明色 -->
    <SolidColorBrush x:Key="SystemControlTransparentBrush" Color="Transparent"/>
    
    <!-- 边框系统 -->
    <SolidColorBrush x:Key="SystemControlBorderBaseLowBrush" Color="#E5E7EB"/>
    <SolidColorBrush x:Key="SystemControlBorderBaseMediumLowBrush" Color="#D1D5DB"/>
    <SolidColorBrush x:Key="SystemControlBorderBaseMediumBrush" Color="#9CA3AF"/>
    <SolidColorBrush x:Key="SystemControlBorderBaseMediumHighBrush" Color="#6B7280"/>
    <SolidColorBrush x:Key="SystemControlBorderBaseHighBrush" Color="#4B5563"/>
    
    <!-- TextBox 特定资源 -->
    <SolidColorBrush x:Key="SystemControlBackgroundBaseLowRevealBackgroundBrush" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="SystemControlBackgroundBaseMediumLowRevealBackgroundBrush" Color="#F3F4F6"/>
    <SolidColorBrush x:Key="SystemControlForegroundBaseRevealBorderBrush" Color="#D1D5DB"/>
    
    <!-- ====================== 菜单和工具栏系统资源 ====================== -->
    <SolidColorBrush x:Key="SystemControlBackgroundListLowBrush" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="SystemControlBackgroundListMediumBrush" Color="#F8F9FA"/>
    <SolidColorBrush x:Key="SystemControlBackgroundListMediumRevealBackgroundBrush" Color="#F3F4F6"/>
    
    <!-- MenuItem 特定资源 -->
    <SolidColorBrush x:Key="SystemControlHighlightListLowBrush" Color="#F3F4F6"/>
    <SolidColorBrush x:Key="SystemControlHighlightListMediumBrush" Color="#E5E7EB"/>
    <SolidColorBrush x:Key="SystemControlHighlightListAccentLowBrush" Color="#0078D4"/>
    <SolidColorBrush x:Key="SystemControlHighlightListAccentMediumBrush" Color="#4FC3F7"/>
    
    <!-- ====================== TabView 系统资源 ====================== -->
    <SolidColorBrush x:Key="TabViewBackground" Color="#F8F9FA"/>
    <SolidColorBrush x:Key="TabViewItemHeaderBackground" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="TabViewItemHeaderBackgroundSelected" Color="#E5E7EB"/>
    <SolidColorBrush x:Key="TabViewItemHeaderBackgroundPointerOver" Color="#F3F4F6"/>
    <SolidColorBrush x:Key="TabViewItemHeaderBackgroundPressed" Color="#D1D5DB"/>
    <SolidColorBrush x:Key="TabViewItemHeaderForeground" Color="#4B5563"/>
    <SolidColorBrush x:Key="TabViewItemHeaderForegroundSelected" Color="#1F2937"/>
    <SolidColorBrush x:Key="TabViewItemHeaderForegroundPointerOver" Color="#374151"/>
    <SolidColorBrush x:Key="TabViewItemHeaderForegroundPressed" Color="#1F2937"/>
    
    <!-- ====================== ListView/ListBox 系统资源 ====================== -->
    <SolidColorBrush x:Key="SystemControlBackgroundListBoxItemBrush" Color="Transparent"/>
    <SolidColorBrush x:Key="SystemControlBackgroundListBoxItemPointerOverBrush" Color="#F3F4F6"/>
    <SolidColorBrush x:Key="SystemControlBackgroundListBoxItemPressedBrush" Color="#E5E7EB"/>
    <SolidColorBrush x:Key="SystemControlBackgroundListBoxItemSelectedBrush" Color="#0078D4"/>
    <SolidColorBrush x:Key="SystemControlBackgroundListBoxItemSelectedPointerOverBrush" Color="#4FC3F7"/>
    <SolidColorBrush x:Key="SystemControlBackgroundListBoxItemSelectedPressedBrush" Color="#005A9E"/>
    
    <!-- ====================== ScrollBar 系统资源 ====================== -->
    <SolidColorBrush x:Key="SystemControlBackgroundScrollBarBrush" Color="#F3F4F6"/>
    <SolidColorBrush x:Key="SystemControlBackgroundScrollBarThumbBrush" Color="#9CA3AF"/>
    <SolidColorBrush x:Key="SystemControlBackgroundScrollBarThumbPointerOverBrush" Color="#6B7280"/>
    <SolidColorBrush x:Key="SystemControlBackgroundScrollBarThumbPressedBrush" Color="#4B5563"/>
    
    <!-- ====================== ProgressBar 系统资源 ====================== -->
    <SolidColorBrush x:Key="SystemControlBackgroundProgressBarBrush" Color="#E5E7EB"/>
    <SolidColorBrush x:Key="SystemControlHighlightProgressBarBrush" Color="#0078D4"/>
    <SolidColorBrush x:Key="SystemControlHighlightProgressBarIndeterminateBrush" Color="#4FC3F7"/>
    
    <!-- ====================== Slider 系统资源 ====================== -->
    <SolidColorBrush x:Key="SystemControlBackgroundSliderBrush" Color="#E5E7EB"/>
    <SolidColorBrush x:Key="SystemControlBackgroundSliderThumbBrush" Color="#1F2937"/>
    <SolidColorBrush x:Key="SystemControlBackgroundSliderThumbPointerOverBrush" Color="#374151"/>
    <SolidColorBrush x:Key="SystemControlBackgroundSliderThumbPressedBrush" Color="#4B5563"/>
    <SolidColorBrush x:Key="SystemControlHighlightSliderBrush" Color="#0078D4"/>
    
    <!-- ====================== ComboBox 系统资源 ====================== -->
    <SolidColorBrush x:Key="SystemControlBackgroundComboBoxBrush" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="SystemControlBackgroundComboBoxPointerOverBrush" Color="#F3F4F6"/>
    <SolidColorBrush x:Key="SystemControlBackgroundComboBoxPressedBrush" Color="#E5E7EB"/>
    <SolidColorBrush x:Key="SystemControlBackgroundComboBoxDropDownBrush" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="SystemControlBorderComboBoxBrush" Color="#D1D5DB"/>
    <SolidColorBrush x:Key="SystemControlBorderComboBoxPointerOverBrush" Color="#0078D4"/>
    <SolidColorBrush x:Key="SystemControlBorderComboBoxPressedBrush" Color="#106EBE"/>
    
    <!-- ====================== CheckBox/RadioButton 系统资源 ====================== -->
    <SolidColorBrush x:Key="SystemControlBackgroundCheckBoxBrush" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="SystemControlBackgroundCheckBoxPointerOverBrush" Color="#F3F4F6"/>
    <SolidColorBrush x:Key="SystemControlBackgroundCheckBoxPressedBrush" Color="#E5E7EB"/>
    <SolidColorBrush x:Key="SystemControlBackgroundCheckBoxCheckedBrush" Color="#0078D4"/>
    <SolidColorBrush x:Key="SystemControlBackgroundCheckBoxCheckedPointerOverBrush" Color="#4FC3F7"/>
    <SolidColorBrush x:Key="SystemControlBackgroundCheckBoxCheckedPressedBrush" Color="#005A9E"/>
    <SolidColorBrush x:Key="SystemControlBorderCheckBoxBrush" Color="#D1D5DB"/>
    <SolidColorBrush x:Key="SystemControlBorderCheckBoxPointerOverBrush" Color="#0078D4"/>
    <SolidColorBrush x:Key="SystemControlBorderCheckBoxPressedBrush" Color="#106EBE"/>
    <SolidColorBrush x:Key="SystemControlForegroundCheckBoxCheckMarkBrush" Color="#FFFFFF"/>
    
    <!-- ====================== ToggleSwitch 系统资源 ====================== -->
    <SolidColorBrush x:Key="SystemControlBackgroundToggleSwitchBrush" Color="#E5E7EB"/>
    <SolidColorBrush x:Key="SystemControlBackgroundToggleSwitchPointerOverBrush" Color="#D1D5DB"/>
    <SolidColorBrush x:Key="SystemControlBackgroundToggleSwitchPressedBrush" Color="#9CA3AF"/>
    <SolidColorBrush x:Key="SystemControlBackgroundToggleSwitchCheckedBrush" Color="#0078D4"/>
    <SolidColorBrush x:Key="SystemControlBackgroundToggleSwitchCheckedPointerOverBrush" Color="#4FC3F7"/>
    <SolidColorBrush x:Key="SystemControlBackgroundToggleSwitchCheckedPressedBrush" Color="#005A9E"/>
    
    <!-- ====================== Tooltip 系统资源 ====================== -->
    <SolidColorBrush x:Key="SystemControlBackgroundTooltipBrush" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="SystemControlForegroundTooltipBrush" Color="#1F2937"/>
    <SolidColorBrush x:Key="SystemControlBorderTooltipBrush" Color="#D1D5DB"/>
    
    <!-- ====================== ContextMenu 系统资源 ====================== -->
    <SolidColorBrush x:Key="SystemControlBackgroundContextMenuBrush" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="SystemControlBorderContextMenuBrush" Color="#D1D5DB"/>
    <SolidColorBrush x:Key="SystemControlBackgroundContextMenuItemBrush" Color="Transparent"/>
    <SolidColorBrush x:Key="SystemControlBackgroundContextMenuItemPointerOverBrush" Color="#F3F4F6"/>
    <SolidColorBrush x:Key="SystemControlBackgroundContextMenuItemPressedBrush" Color="#E5E7EB"/>
    
    <!-- ====================== ModernWpfUI Menu 系统资源 ====================== -->
    <!-- Menu 主菜单栏资源 - 使用 ModernWpfUI 的实际资源键 -->
    <SolidColorBrush x:Key="MenuBackgroundBrush" Color="#F8F9FA"/>
    <SolidColorBrush x:Key="MenuForegroundBrush" Color="#1F2937"/>
    <SolidColorBrush x:Key="MenuBorderBrush" Color="#D1D5DB"/>
    
    <!-- MenuItem 基础资源 -->
    <SolidColorBrush x:Key="MenuItemBackgroundBrush" Color="Transparent"/>
    <SolidColorBrush x:Key="MenuItemForegroundBrush" Color="#1F2937"/>
    <SolidColorBrush x:Key="MenuItemBorderBrush" Color="Transparent"/>
    
    <!-- MenuItem 交互状态 -->
    <SolidColorBrush x:Key="MenuItemBackgroundPointerOverBrush" Color="#F3F4F6"/>
    <SolidColorBrush x:Key="MenuItemForegroundPointerOverBrush" Color="#1F2937"/>
    <SolidColorBrush x:Key="MenuItemBorderPointerOverBrush" Color="#D1D5DB"/>
    
    <SolidColorBrush x:Key="MenuItemBackgroundPressedBrush" Color="#E5E7EB"/>
    <SolidColorBrush x:Key="MenuItemForegroundPressedBrush" Color="#1F2937"/>
    <SolidColorBrush x:Key="MenuItemBorderPressedBrush" Color="#D1D5DB"/>
    
    <SolidColorBrush x:Key="MenuItemBackgroundSelectedBrush" Color="#F3F4F6"/>
    <SolidColorBrush x:Key="MenuItemForegroundSelectedBrush" Color="#1F2937"/>
    <SolidColorBrush x:Key="MenuItemBorderSelectedBrush" Color="#D1D5DB"/>
    
    <!-- MenuItem 禁用状态 -->
    <SolidColorBrush x:Key="MenuItemBackgroundDisabledBrush" Color="Transparent"/>
    <SolidColorBrush x:Key="MenuItemForegroundDisabledBrush" Color="#9CA3AF"/>
    <SolidColorBrush x:Key="MenuItemBorderDisabledBrush" Color="Transparent"/>
    
    <!-- 下拉菜单 (Popup) 资源 -->
    <SolidColorBrush x:Key="MenuFlyoutBackgroundBrush" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="MenuFlyoutBorderBrush" Color="#D1D5DB"/>
    
    <!-- 分隔符资源 -->
    <SolidColorBrush x:Key="MenuFlyoutSeparatorBrush" Color="#D1D5DB"/>
    
    <!-- 传统 WPF Menu 资源键覆盖 -->
    <SolidColorBrush x:Key="{x:Static SystemColors.MenuBrushKey}" Color="#F8F9FA"/>
    <SolidColorBrush x:Key="{x:Static SystemColors.MenuTextBrushKey}" Color="#1F2937"/>
    <SolidColorBrush x:Key="{x:Static SystemColors.MenuHighlightBrushKey}" Color="#F3F4F6"/>
    <SolidColorBrush x:Key="{x:Static SystemColors.MenuBarBrushKey}" Color="#F8F9FA"/>
    
    <!-- 更多可能的下拉菜单背景资源键 -->
    <SolidColorBrush x:Key="MenuPopupBrush" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="MenuPopupBackgroundBrush" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="ContextMenuBackgroundBrush" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="PopupBackgroundBrush" Color="#FFFFFF"/>
    
    <!-- ModernWpfUI 可能使用的菜单资源 -->
    <SolidColorBrush x:Key="MenuItemPopupBackgroundBrush" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="MenuItemSubmenuBackgroundBrush" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="TopLevelMenuBackgroundBrush" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="SubMenuBackgroundBrush" Color="#FFFFFF"/>
    
    <!-- 强制覆盖所有可能的 Menu 文字颜色资源 -->
    <SolidColorBrush x:Key="MenuTextBrush" Color="#1F2937"/>
    <SolidColorBrush x:Key="MenuBarTextBrush" Color="#1F2937"/>
    <SolidColorBrush x:Key="MenuItemTextBrush" Color="#1F2937"/>
    <SolidColorBrush x:Key="TopLevelHeaderNormalTextBrush" Color="#1F2937"/>
    <SolidColorBrush x:Key="TopLevelHeaderMouseOverTextBrush" Color="#1F2937"/>
    <SolidColorBrush x:Key="TopLevelHeaderPressedTextBrush" Color="#1F2937"/>
    
    <!-- ====================== 特殊效果和阴影 ====================== -->
    <SolidColorBrush x:Key="SystemControlTransientBorderBrush" Color="#0078D4"/>
    <SolidColorBrush x:Key="SystemControlRevealBorderBrush" Color="#D1D5DB"/>
    <SolidColorBrush x:Key="SystemControlRevealBackgroundBrush" Color="#F3F4F6"/>
    
    <!-- ====================== Fluent 浅色主题专用资源 ====================== -->
    <!-- 主要背景色 -->
    <SolidColorBrush x:Key="FluentAppBackgroundBrush" Color="#F8F9FA"/>
    <SolidColorBrush x:Key="FluentPageBackgroundBrush" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="FluentCardBackgroundBrush" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="FluentSurfaceBackgroundBrush" Color="#F3F4F6"/>
    
    <!-- 文字颜色 -->
    <SolidColorBrush x:Key="FluentPrimaryTextBrush" Color="#1F2937"/>
    <SolidColorBrush x:Key="FluentSecondaryTextBrush" Color="#4B5563"/>
    <SolidColorBrush x:Key="FluentTertiaryTextBrush" Color="#6B7280"/>
    <SolidColorBrush x:Key="FluentDisabledTextBrush" Color="#9CA3AF"/>
    
    <!-- 边框颜色 -->
    <SolidColorBrush x:Key="FluentBorderBrush" Color="#D1D5DB"/>
    <SolidColorBrush x:Key="FluentDividerBrush" Color="#E5E7EB"/>
    <SolidColorBrush x:Key="FluentFocusBorderBrush" Color="#0078D4"/>
    
    <!-- 按钮颜色 -->
    <SolidColorBrush x:Key="FluentButtonBackgroundBrush" Color="#F3F4F6"/>
    <SolidColorBrush x:Key="FluentButtonHoverBackgroundBrush" Color="#E5E7EB"/>
    <SolidColorBrush x:Key="FluentButtonPressedBackgroundBrush" Color="#D1D5DB"/>
    
    <!-- 主色调按钮 -->
    <SolidColorBrush x:Key="FluentAccentButtonBackgroundBrush" Color="#0078D4"/>
    <SolidColorBrush x:Key="FluentAccentButtonHoverBackgroundBrush" Color="#106EBE"/>
    <SolidColorBrush x:Key="FluentAccentButtonPressedBackgroundBrush" Color="#005A9E"/>
    <SolidColorBrush x:Key="FluentAccentButtonTextBrush" Color="#FFFFFF"/>
    
    <!-- 输入控件颜色 -->
    <SolidColorBrush x:Key="FluentTextBoxBackgroundBrush" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="FluentTextBoxBorderBrush" Color="#D1D5DB"/>
    <SolidColorBrush x:Key="FluentTextBoxFocusBorderBrush" Color="#0078D4"/>
    
    <!-- 图表配色方案 - 浅色主题优化 -->
    <x:Array x:Key="FluentLightChartColors" Type="Color">
        <Color>#0078D4</Color>  <!-- 微软蓝 -->
        <Color>#00BCF2</Color>  <!-- 亮青色 -->
        <Color>#00CC6A</Color>  <!-- 绿色 -->
        <Color>#FFB900</Color>  <!-- 金色 -->
        <Color>#FF8C00</Color>  <!-- 橙色 -->
        <Color>#E74856</Color>  <!-- 红色 -->
        <Color>#B146C2</Color>  <!-- 紫色 -->
        <Color>#0099BC</Color>  <!-- 深青色 -->
        <Color>#2D7D32</Color>  <!-- 深绿色 -->
        <Color>#FF6F00</Color>  <!-- 深橙色 -->
    </x:Array>
    
    <!-- 图表背景和网格 -->
    <SolidColorBrush x:Key="FluentChartBackgroundBrush" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="FluentChartGridBrush" Color="#E5E7EB"/>
    <SolidColorBrush x:Key="FluentChartAxisBrush" Color="#6B7280"/>
    <SolidColorBrush x:Key="FluentChartTextBrush" Color="#1F2937"/>
    
    <!-- 状态颜色 - 浅色主题适配 -->
    <SolidColorBrush x:Key="FluentSuccessBrush" Color="#059669"/>
    <SolidColorBrush x:Key="FluentWarningBrush" Color="#D97706"/>
    <SolidColorBrush x:Key="FluentErrorBrush" Color="#DC2626"/>
    <SolidColorBrush x:Key="FluentInfoBrush" Color="#0078D4"/>
    
    <!-- ====================== 布局和样式通用资源 ====================== -->
    <sys:Double x:Key="FluentCardCornerRadius">8</sys:Double>
    <Thickness x:Key="FluentCardPadding">16</Thickness>
    <Thickness x:Key="FluentCardMargin">8</Thickness>
    <sys:Double x:Key="FluentControlHeight">32</sys:Double>
    <sys:Double x:Key="FluentControlCornerRadius">4</sys:Double>
    <Thickness x:Key="FluentControlPadding">12,6</Thickness>
    <Thickness x:Key="FluentButtonPadding">16,8</Thickness>
    <Thickness x:Key="FluentBorderThickness">1</Thickness>
    
    <!-- 卡片样式定义 - 浅色主题版本 -->
    <Style x:Key="FluentCardStyle" TargetType="Border">
        <Setter Property="Background" Value="{StaticResource FluentCardBackgroundBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource FluentBorderBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="CornerRadius" Value="{StaticResource FluentCardCornerRadius}"/>
        <Setter Property="Padding" Value="{StaticResource FluentCardPadding}"/>
        <Setter Property="Margin" Value="{StaticResource FluentCardMargin}"/>
        <Setter Property="Effect">
            <Setter.Value>
                <!-- 浅色主题使用更清晰的阴影 -->
                <DropShadowEffect BlurRadius="16" 
                                  ShadowDepth="4" 
                                  Opacity="0.12" 
                                  Direction="270"
                                  Color="#000000"/>
            </Setter.Value>
        </Setter>
    </Style>
    
    <!-- ModernWpfUI 基础样式覆盖 -->
    <Style x:Key="BaseTextBlockStyle" TargetType="TextBlock">
        <Setter Property="Foreground" Value="{StaticResource SystemControlForegroundBaseHighBrush}"/>
        <Setter Property="FontFamily" Value="Segoe UI"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="TextWrapping" Value="NoWrap"/>
        <Setter Property="TextTrimming" Value="None"/>
        <Setter Property="TextAlignment" Value="Left"/>
        <Setter Property="LineHeight" Value="20"/>
        <Setter Property="LineStackingStrategy" Value="MaxHeight"/>
    </Style>
    
    <Style x:Key="CaptionTextBlockStyle" TargetType="TextBlock" BasedOn="{StaticResource BaseTextBlockStyle}">
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="LineHeight" Value="16"/>
    </Style>
    
    <Style x:Key="AccentButtonStyle" TargetType="Button">
        <Setter Property="Foreground" Value="#FFFFFF"/>
        <Setter Property="Background" Value="{StaticResource SystemControlHighlightAccentBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource SystemControlHighlightAccentBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Padding" Value="8,5,8,6"/>
        <Setter Property="HorizontalAlignment" Value="Left"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
        <Setter Property="FontFamily" Value="Segoe UI"/>
        <Setter Property="FontWeight" Value="Normal"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border x:Name="Background"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="2">
                        <ContentPresenter x:Name="ContentPresenter"
                                        Content="{TemplateBinding Content}"
                                        ContentTemplate="{TemplateBinding ContentTemplate}"
                                        Margin="{TemplateBinding Padding}"
                                        HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                        VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="Background" Property="Background" Value="{StaticResource SystemControlHighlightAltAccentBrush}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="Background" Property="Background" Value="{StaticResource SystemControlHighlightBaseLowBrush}"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="Background" Property="Background" Value="{StaticResource SystemControlBackgroundBaseLowBrush}"/>
                            <Setter Property="Foreground" Value="{StaticResource SystemControlForegroundBaseLowBrush}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    
</ResourceDictionary>