var ie={exports:{}};/* @license
Papa Parse
v5.5.3
https://github.com/mholt/PapaParse
License: MIT
*/var ye=ie.exports,pe;function ve(){return pe||(pe=1,function(_e,ke){((ue,R)=>{_e.exports=R()})(ye,function ue(){var R=typeof self<"u"?self:typeof window<"u"?window:R!==void 0?R:{},$,J=!R.document&&!!R.postMessage,ne=R.IS_PAPA_WORKER||!1,V={},ge=0,u={};function N(e){this._handle=null,this._finished=!1,this._completed=!1,this._halted=!1,this._input=null,this._baseIndex=0,this._partialLine="",this._rowCount=0,this._start=0,this._nextChunk=null,this.isFirstChunk=!0,this._completeResults={data:[],errors:[],meta:{}},(function(t){var r=ae(t);r.chunkSize=parseInt(r.chunkSize),t.step||t.chunk||(r.chunkSize=null),this._handle=new he(r),(this._handle.streamer=this)._config=r}).call(this,e),this.parseChunk=function(t,r){var n=parseInt(this._config.skipFirstNLines)||0;if(this.isFirstChunk&&0<n){let l=this._config.newline;l||(i=this._config.quoteChar||'"',l=this._handle.guessLineEndings(t,i)),t=[...t.split(l).slice(n)].join(l)}this.isFirstChunk&&y(this._config.beforeFirstChunk)&&(i=this._config.beforeFirstChunk(t))!==void 0&&(t=i),this.isFirstChunk=!1,this._halted=!1;var n=this._partialLine+t,i=(this._partialLine="",this._handle.parse(n,this._baseIndex,!this._finished));if(!this._handle.paused()&&!this._handle.aborted()){if(t=i.meta.cursor,n=(this._finished||(this._partialLine=n.substring(t-this._baseIndex),this._baseIndex=t),i&&i.data&&(this._rowCount+=i.data.length),this._finished||this._config.preview&&this._rowCount>=this._config.preview),ne)R.postMessage({results:i,workerId:u.WORKER_ID,finished:n});else if(y(this._config.chunk)&&!r){if(this._config.chunk(i,this._handle),this._handle.paused()||this._handle.aborted())return void(this._halted=!0);this._completeResults=i=void 0}return this._config.step||this._config.chunk||(this._completeResults.data=this._completeResults.data.concat(i.data),this._completeResults.errors=this._completeResults.errors.concat(i.errors),this._completeResults.meta=i.meta),this._completed||!n||!y(this._config.complete)||i&&i.meta.aborted||(this._config.complete(this._completeResults,this._input),this._completed=!0),n||i&&i.meta.paused||this._nextChunk(),i}this._halted=!0},this._sendError=function(t){y(this._config.error)?this._config.error(t):ne&&this._config.error&&R.postMessage({workerId:u.WORKER_ID,error:t,finished:!1})}}function ee(e){var t;(e=e||{}).chunkSize||(e.chunkSize=u.RemoteChunkSize),N.call(this,e),this._nextChunk=J?function(){this._readChunk(),this._chunkLoaded()}:function(){this._readChunk()},this.stream=function(r){this._input=r,this._nextChunk()},this._readChunk=function(){if(this._finished)this._chunkLoaded();else{if(t=new XMLHttpRequest,this._config.withCredentials&&(t.withCredentials=this._config.withCredentials),J||(t.onload=K(this._chunkLoaded,this),t.onerror=K(this._chunkError,this)),t.open(this._config.downloadRequestBody?"POST":"GET",this._input,!J),this._config.downloadRequestHeaders){var r,n=this._config.downloadRequestHeaders;for(r in n)t.setRequestHeader(r,n[r])}var i;this._config.chunkSize&&(i=this._start+this._config.chunkSize-1,t.setRequestHeader("Range","bytes="+this._start+"-"+i));try{t.send(this._config.downloadRequestBody)}catch(l){this._chunkError(l.message)}J&&t.status===0&&this._chunkError()}},this._chunkLoaded=function(){t.readyState===4&&(t.status<200||400<=t.status?this._chunkError():(this._start+=this._config.chunkSize||t.responseText.length,this._finished=!this._config.chunkSize||this._start>=(r=>(r=r.getResponseHeader("Content-Range"))!==null?parseInt(r.substring(r.lastIndexOf("/")+1)):-1)(t),this.parseChunk(t.responseText)))},this._chunkError=function(r){r=t.statusText||r,this._sendError(new Error(r))}}function te(e){(e=e||{}).chunkSize||(e.chunkSize=u.LocalChunkSize),N.call(this,e);var t,r,n=typeof FileReader<"u";this.stream=function(i){this._input=i,r=i.slice||i.webkitSlice||i.mozSlice,n?((t=new FileReader).onload=K(this._chunkLoaded,this),t.onerror=K(this._chunkError,this)):t=new FileReaderSync,this._nextChunk()},this._nextChunk=function(){this._finished||this._config.preview&&!(this._rowCount<this._config.preview)||this._readChunk()},this._readChunk=function(){var i=this._input,l=(this._config.chunkSize&&(l=Math.min(this._start+this._config.chunkSize,this._input.size),i=r.call(i,this._start,l)),t.readAsText(i,this._config.encoding));n||this._chunkLoaded({target:{result:l}})},this._chunkLoaded=function(i){this._start+=this._config.chunkSize,this._finished=!this._config.chunkSize||this._start>=this._input.size,this.parseChunk(i.target.result)},this._chunkError=function(){this._sendError(t.error)}}function Z(e){var t;N.call(this,e=e||{}),this.stream=function(r){return t=r,this._nextChunk()},this._nextChunk=function(){var r,n;if(!this._finished)return r=this._config.chunkSize,t=r?(n=t.substring(0,r),t.substring(r)):(n=t,""),this._finished=!t,this.parseChunk(n)}}function re(e){N.call(this,e=e||{});var t=[],r=!0,n=!1;this.pause=function(){N.prototype.pause.apply(this,arguments),this._input.pause()},this.resume=function(){N.prototype.resume.apply(this,arguments),this._input.resume()},this.stream=function(i){this._input=i,this._input.on("data",this._streamData),this._input.on("end",this._streamEnd),this._input.on("error",this._streamError)},this._checkIsFinished=function(){n&&t.length===1&&(this._finished=!0)},this._nextChunk=function(){this._checkIsFinished(),t.length?this.parseChunk(t.shift()):r=!0},this._streamData=K(function(i){try{t.push(typeof i=="string"?i:i.toString(this._config.encoding)),r&&(r=!1,this._checkIsFinished(),this.parseChunk(t.shift()))}catch(l){this._streamError(l)}},this),this._streamError=K(function(i){this._streamCleanUp(),this._sendError(i)},this),this._streamEnd=K(function(){this._streamCleanUp(),n=!0,this._streamData("")},this),this._streamCleanUp=K(function(){this._input.removeListener("data",this._streamData),this._input.removeListener("end",this._streamEnd),this._input.removeListener("error",this._streamError)},this)}function he(e){var t,r,n,i,l=Math.pow(2,53),C=-l,F=/^\s*-?(\d+\.?|\.\d+|\d+\.\d+)([eE][-+]?\d+)?\s*$/,j=/^((\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d:[0-5]\d\.\d+([+-][0-2]\d:[0-5]\d|Z))|(\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d:[0-5]\d([+-][0-2]\d:[0-5]\d|Z))|(\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d([+-][0-2]\d:[0-5]\d|Z)))$/,d=this,E=0,a=0,T=!1,h=!1,c=[],s={data:[],errors:[],meta:{}};function O(p){return e.skipEmptyLines==="greedy"?p.join("").trim()==="":p.length===1&&p[0].length===0}function S(){if(s&&n&&(z("Delimiter","UndetectableDelimiter","Unable to auto-detect delimiting character; defaulted to '"+u.DefaultDelimiter+"'"),n=!1),e.skipEmptyLines&&(s.data=s.data.filter(function(o){return!O(o)})),A()){let o=function(w,x){y(e.transformHeader)&&(w=e.transformHeader(w,x)),c.push(w)};if(s)if(Array.isArray(s.data[0])){for(var p=0;A()&&p<s.data.length;p++)s.data[p].forEach(o);s.data.splice(0,1)}else s.data.forEach(o)}function g(o,w){for(var x=e.header?{}:[],m=0;m<o.length;m++){var k=m,_=o[m],_=((M,f)=>(v=>(e.dynamicTypingFunction&&e.dynamicTyping[v]===void 0&&(e.dynamicTyping[v]=e.dynamicTypingFunction(v)),(e.dynamicTyping[v]||e.dynamicTyping)===!0))(M)?f==="true"||f==="TRUE"||f!=="false"&&f!=="FALSE"&&((v=>{if(F.test(v)&&(v=parseFloat(v),C<v&&v<l))return 1})(f)?parseFloat(f):j.test(f)?new Date(f):f===""?null:f):f)(k=e.header?m>=c.length?"__parsed_extra":c[m]:k,_=e.transform?e.transform(_,k):_);k==="__parsed_extra"?(x[k]=x[k]||[],x[k].push(_)):x[k]=_}return e.header&&(m>c.length?z("FieldMismatch","TooManyFields","Too many fields: expected "+c.length+" fields but parsed "+m,a+w):m<c.length&&z("FieldMismatch","TooFewFields","Too few fields: expected "+c.length+" fields but parsed "+m,a+w)),x}var b;s&&(e.header||e.dynamicTyping||e.transform)&&(b=1,!s.data.length||Array.isArray(s.data[0])?(s.data=s.data.map(g),b=s.data.length):s.data=g(s.data,0),e.header&&s.meta&&(s.meta.fields=c),a+=b)}function A(){return e.header&&c.length===0}function z(p,g,b,o){p={type:p,code:g,message:b},o!==void 0&&(p.row=o),s.errors.push(p)}y(e.step)&&(i=e.step,e.step=function(p){s=p,A()?S():(S(),s.data.length!==0&&(E+=p.data.length,e.preview&&E>e.preview?r.abort():(s.data=s.data[0],i(s,d))))}),this.parse=function(p,g,b){var o=e.quoteChar||'"',o=(e.newline||(e.newline=this.guessLineEndings(p,o)),n=!1,e.delimiter?y(e.delimiter)&&(e.delimiter=e.delimiter(p),s.meta.delimiter=e.delimiter):((o=((w,x,m,k,_)=>{var M,f,v,W;_=_||[",","	","|",";",u.RECORD_SEP,u.UNIT_SEP];for(var H=0;H<_.length;H++){for(var P,Y=_[H],D=0,U=0,I=0,L=(v=void 0,new se({comments:k,delimiter:Y,newline:x,preview:10}).parse(w)),B=0;B<L.data.length;B++)m&&O(L.data[B])?I++:(P=L.data[B].length,U+=P,v===void 0?v=P:0<P&&(D+=Math.abs(P-v),v=P));0<L.data.length&&(U/=L.data.length-I),(f===void 0||D<=f)&&(W===void 0||W<U)&&1.99<U&&(f=D,M=Y,W=U)}return{successful:!!(e.delimiter=M),bestDelimiter:M}})(p,e.newline,e.skipEmptyLines,e.comments,e.delimitersToGuess)).successful?e.delimiter=o.bestDelimiter:(n=!0,e.delimiter=u.DefaultDelimiter),s.meta.delimiter=e.delimiter),ae(e));return e.preview&&e.header&&o.preview++,t=p,r=new se(o),s=r.parse(t,g,b),S(),T?{meta:{paused:!0}}:s||{meta:{paused:!1}}},this.paused=function(){return T},this.pause=function(){T=!0,r.abort(),t=y(e.chunk)?"":t.substring(r.getCharIndex())},this.resume=function(){d.streamer._halted?(T=!1,d.streamer.parseChunk(t,!0)):setTimeout(d.resume,3)},this.aborted=function(){return h},this.abort=function(){h=!0,r.abort(),s.meta.aborted=!0,y(e.complete)&&e.complete(s),t=""},this.guessLineEndings=function(w,o){w=w.substring(0,1048576);var o=new RegExp(G(o)+"([^]*?)"+G(o),"gm"),b=(w=w.replace(o,"")).split("\r"),o=w.split(`
`),w=1<o.length&&o[0].length<b[0].length;if(b.length===1||w)return`
`;for(var x=0,m=0;m<b.length;m++)b[m][0]===`
`&&x++;return x>=b.length/2?`\r
`:"\r"}}function G(e){return e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function se(e){var t=(e=e||{}).delimiter,r=e.newline,n=e.comments,i=e.step,l=e.preview,C=e.fastMode,F=null,j=!1,d=e.quoteChar==null?'"':e.quoteChar,E=d;if(e.escapeChar!==void 0&&(E=e.escapeChar),(typeof t!="string"||-1<u.BAD_DELIMITERS.indexOf(t))&&(t=","),n===t)throw new Error("Comment character same as delimiter");n===!0?n="#":(typeof n!="string"||-1<u.BAD_DELIMITERS.indexOf(n))&&(n=!1),r!==`
`&&r!=="\r"&&r!==`\r
`&&(r=`
`);var a=0,T=!1;this.parse=function(h,c,s){if(typeof h!="string")throw new Error("Input must be a string");var O=h.length,S=t.length,A=r.length,z=n.length,p=y(i),g=[],b=[],o=[],w=a=0;if(!h)return D();if(C||C!==!1&&h.indexOf(d)===-1){for(var x=h.split(r),m=0;m<x.length;m++){if(o=x[m],a+=o.length,m!==x.length-1)a+=r.length;else if(s)return D();if(!n||o.substring(0,z)!==n){if(p){if(g=[],W(o.split(t)),U(),T)return D()}else W(o.split(t));if(l&&l<=m)return g=g.slice(0,l),D(!0)}}return D()}for(var k=h.indexOf(t,a),_=h.indexOf(r,a),M=new RegExp(G(E)+G(d),"g"),f=h.indexOf(d,a);;)if(h[a]===d)for(f=a,a++;;){if((f=h.indexOf(d,f+1))===-1)return s||b.push({type:"Quotes",code:"MissingQuotes",message:"Quoted field unterminated",row:g.length,index:a}),P();if(f===O-1)return P(h.substring(a,f).replace(M,d));if(d===E&&h[f+1]===E)f++;else if(d===E||f===0||h[f-1]!==E){k!==-1&&k<f+1&&(k=h.indexOf(t,f+1));var v=H((_=_!==-1&&_<f+1?h.indexOf(r,f+1):_)===-1?k:Math.min(k,_));if(h.substr(f+1+v,S)===t){o.push(h.substring(a,f).replace(M,d)),h[a=f+1+v+S]!==d&&(f=h.indexOf(d,a)),k=h.indexOf(t,a),_=h.indexOf(r,a);break}if(v=H(_),h.substring(f+1+v,f+1+v+A)===r){if(o.push(h.substring(a,f).replace(M,d)),Y(f+1+v+A),k=h.indexOf(t,a),f=h.indexOf(d,a),p&&(U(),T))return D();if(l&&g.length>=l)return D(!0);break}b.push({type:"Quotes",code:"InvalidQuotes",message:"Trailing quote on quoted field is malformed",row:g.length,index:a}),f++}}else if(n&&o.length===0&&h.substring(a,a+z)===n){if(_===-1)return D();a=_+A,_=h.indexOf(r,a),k=h.indexOf(t,a)}else if(k!==-1&&(k<_||_===-1))o.push(h.substring(a,k)),a=k+S,k=h.indexOf(t,a);else{if(_===-1)break;if(o.push(h.substring(a,_)),Y(_+A),p&&(U(),T))return D();if(l&&g.length>=l)return D(!0)}return P();function W(I){g.push(I),w=a}function H(I){var L=0;return L=I!==-1&&(I=h.substring(f+1,I))&&I.trim()===""?I.length:L}function P(I){return s||(I===void 0&&(I=h.substring(a)),o.push(I),a=O,W(o),p&&U()),D()}function Y(I){a=I,W(o),o=[],_=h.indexOf(r,a)}function D(I){if(e.header&&!c&&g.length&&!j){var L=g[0],B=Object.create(null),oe=new Set(L);let de=!1;for(let Q=0;Q<L.length;Q++){let q=L[Q];if(B[q=y(e.transformHeader)?e.transformHeader(q,Q):q]){let X,ce=B[q];for(;X=q+"_"+ce,ce++,oe.has(X););oe.add(X),L[Q]=X,B[q]++,de=!0,(F=F===null?{}:F)[X]=q}else B[q]=1,L[Q]=q;oe.add(q)}de&&console.warn("Duplicate headers found and renamed."),j=!0}return{data:g,errors:b,meta:{delimiter:t,linebreak:r,aborted:T,truncated:!!I,cursor:w+(c||0),renamedHeaders:F}}}function U(){i(D()),g=[],b=[]}},this.abort=function(){T=!0},this.getCharIndex=function(){return a}}function me(e){var t=e.data,r=V[t.workerId],n=!1;if(t.error)r.userError(t.error,t.file);else if(t.results&&t.results.data){var i={abort:function(){n=!0,fe(t.workerId,{data:[],errors:[],meta:{aborted:!0}})},pause:le,resume:le};if(y(r.userStep)){for(var l=0;l<t.results.data.length&&(r.userStep({data:t.results.data[l],errors:t.results.errors,meta:t.results.meta},i),!n);l++);delete t.results}else y(r.userChunk)&&(r.userChunk(t.results,i,t.file),delete t.results)}t.finished&&!n&&fe(t.workerId,t.results)}function fe(e,t){var r=V[e];y(r.userComplete)&&r.userComplete(t),r.terminate(),delete V[e]}function le(){throw new Error("Not implemented.")}function ae(e){if(typeof e!="object"||e===null)return e;var t,r=Array.isArray(e)?[]:{};for(t in e)r[t]=ae(e[t]);return r}function K(e,t){return function(){e.apply(t,arguments)}}function y(e){return typeof e=="function"}return u.parse=function(e,t){var r=(t=t||{}).dynamicTyping||!1;if(y(r)&&(t.dynamicTypingFunction=r,r={}),t.dynamicTyping=r,t.transform=!!y(t.transform)&&t.transform,!t.worker||!u.WORKERS_SUPPORTED)return r=null,u.NODE_STREAM_INPUT,typeof e=="string"?(e=(n=>n.charCodeAt(0)!==65279?n:n.slice(1))(e),r=new(t.download?ee:Z)(t)):e.readable===!0&&y(e.read)&&y(e.on)?r=new re(t):(R.File&&e instanceof File||e instanceof Object)&&(r=new te(t)),r.stream(e);(r=(()=>{var n;return!!u.WORKERS_SUPPORTED&&(n=(()=>{var i=R.URL||R.webkitURL||null,l=ue.toString();return u.BLOB_URL||(u.BLOB_URL=i.createObjectURL(new Blob(["var global = (function() { if (typeof self !== 'undefined') { return self; } if (typeof window !== 'undefined') { return window; } if (typeof global !== 'undefined') { return global; } return {}; })(); global.IS_PAPA_WORKER=true; ","(",l,")();"],{type:"text/javascript"})))})(),(n=new R.Worker(n)).onmessage=me,n.id=ge++,V[n.id]=n)})()).userStep=t.step,r.userChunk=t.chunk,r.userComplete=t.complete,r.userError=t.error,t.step=y(t.step),t.chunk=y(t.chunk),t.complete=y(t.complete),t.error=y(t.error),delete t.worker,r.postMessage({input:e,config:t,workerId:r.id})},u.unparse=function(e,t){var r=!1,n=!0,i=",",l=`\r
`,C='"',F=C+C,j=!1,d=null,E=!1,a=((()=>{if(typeof t=="object"){if(typeof t.delimiter!="string"||u.BAD_DELIMITERS.filter(function(c){return t.delimiter.indexOf(c)!==-1}).length||(i=t.delimiter),typeof t.quotes!="boolean"&&typeof t.quotes!="function"&&!Array.isArray(t.quotes)||(r=t.quotes),typeof t.skipEmptyLines!="boolean"&&typeof t.skipEmptyLines!="string"||(j=t.skipEmptyLines),typeof t.newline=="string"&&(l=t.newline),typeof t.quoteChar=="string"&&(C=t.quoteChar),typeof t.header=="boolean"&&(n=t.header),Array.isArray(t.columns)){if(t.columns.length===0)throw new Error("Option columns is empty");d=t.columns}t.escapeChar!==void 0&&(F=t.escapeChar+C),t.escapeFormulae instanceof RegExp?E=t.escapeFormulae:typeof t.escapeFormulae=="boolean"&&t.escapeFormulae&&(E=/^[=+\-@\t\r].*$/)}})(),new RegExp(G(C),"g"));if(typeof e=="string"&&(e=JSON.parse(e)),Array.isArray(e)){if(!e.length||Array.isArray(e[0]))return T(null,e,j);if(typeof e[0]=="object")return T(d||Object.keys(e[0]),e,j)}else if(typeof e=="object")return typeof e.data=="string"&&(e.data=JSON.parse(e.data)),Array.isArray(e.data)&&(e.fields||(e.fields=e.meta&&e.meta.fields||d),e.fields||(e.fields=Array.isArray(e.data[0])?e.fields:typeof e.data[0]=="object"?Object.keys(e.data[0]):[]),Array.isArray(e.data[0])||typeof e.data[0]=="object"||(e.data=[e.data])),T(e.fields||[],e.data||[],j);throw new Error("Unable to serialize unrecognized input");function T(c,s,O){var S="",A=(typeof c=="string"&&(c=JSON.parse(c)),typeof s=="string"&&(s=JSON.parse(s)),Array.isArray(c)&&0<c.length),z=!Array.isArray(s[0]);if(A&&n){for(var p=0;p<c.length;p++)0<p&&(S+=i),S+=h(c[p],p);0<s.length&&(S+=l)}for(var g=0;g<s.length;g++){var b=(A?c:s[g]).length,o=!1,w=A?Object.keys(s[g]).length===0:s[g].length===0;if(O&&!A&&(o=O==="greedy"?s[g].join("").trim()==="":s[g].length===1&&s[g][0].length===0),O==="greedy"&&A){for(var x=[],m=0;m<b;m++){var k=z?c[m]:m;x.push(s[g][k])}o=x.join("").trim()===""}if(!o){for(var _=0;_<b;_++){0<_&&!w&&(S+=i);var M=A&&z?c[_]:_;S+=h(s[g][M],_)}g<s.length-1&&(!O||0<b&&!w)&&(S+=l)}}return S}function h(c,s){var O,S;return c==null?"":c.constructor===Date?JSON.stringify(c).slice(1,25):(S=!1,E&&typeof c=="string"&&E.test(c)&&(c="'"+c,S=!0),O=c.toString().replace(a,F),(S=S||r===!0||typeof r=="function"&&r(c,s)||Array.isArray(r)&&r[s]||((A,z)=>{for(var p=0;p<z.length;p++)if(-1<A.indexOf(z[p]))return!0;return!1})(O,u.BAD_DELIMITERS)||-1<O.indexOf(i)||O.charAt(0)===" "||O.charAt(O.length-1)===" ")?C+O+C:O)}},u.RECORD_SEP="",u.UNIT_SEP="",u.BYTE_ORDER_MARK="\uFEFF",u.BAD_DELIMITERS=["\r",`
`,'"',u.BYTE_ORDER_MARK],u.WORKERS_SUPPORTED=!J&&!!R.Worker,u.NODE_STREAM_INPUT=1,u.LocalChunkSize=10485760,u.RemoteChunkSize=5242880,u.DefaultDelimiter=",",u.Parser=se,u.ParserHandle=he,u.NetworkStreamer=ee,u.FileStreamer=te,u.StringStreamer=Z,u.ReadableStreamStreamer=re,R.jQuery&&(($=R.jQuery).fn.parse=function(e){var t=e.config||{},r=[];return this.each(function(l){if(!($(this).prop("tagName").toUpperCase()==="INPUT"&&$(this).attr("type").toLowerCase()==="file"&&R.FileReader)||!this.files||this.files.length===0)return!0;for(var C=0;C<this.files.length;C++)r.push({file:this.files[C],inputElem:this,instanceConfig:$.extend({},t)})}),n(),this;function n(){if(r.length===0)y(e.complete)&&e.complete();else{var l,C,F,j,d=r[0];if(y(e.before)){var E=e.before(d.file,d.inputElem);if(typeof E=="object"){if(E.action==="abort")return l="AbortError",C=d.file,F=d.inputElem,j=E.reason,void(y(e.error)&&e.error({name:l},C,F,j));if(E.action==="skip")return void i();typeof E.config=="object"&&(d.instanceConfig=$.extend(d.instanceConfig,E.config))}else if(E==="skip")return void i()}var a=d.instanceConfig.complete;d.instanceConfig.complete=function(T){y(a)&&a(T,d.file,d.inputElem),i()},u.parse(d.file,d.instanceConfig)}}function i(){r.splice(0,1),n()}}),ne&&(R.onmessage=function(e){e=e.data,u.WORKER_ID===void 0&&e&&(u.WORKER_ID=e.workerId),typeof e.input=="string"?R.postMessage({workerId:u.WORKER_ID,results:u.parse(e.input,e.config),finished:!0}):(R.File&&e.input instanceof File||e.input instanceof Object)&&(e=u.parse(e.input,e.config))&&R.postMessage({workerId:u.WORKER_ID,results:e,finished:!0})}),(ee.prototype=Object.create(N.prototype)).constructor=ee,(te.prototype=Object.create(N.prototype)).constructor=te,(Z.prototype=Object.create(Z.prototype)).constructor=Z,(re.prototype=Object.create(N.prototype)).constructor=re,u})}(ie)),ie.exports}export{ve as r};
