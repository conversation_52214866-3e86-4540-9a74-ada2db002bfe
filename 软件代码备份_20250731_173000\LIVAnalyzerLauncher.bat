@echo off
chcp 65001 >nul
echo LIV Analyzer 启动器
echo ===================
echo.
echo 选择启动方式:
echo 1. 使用 dotnet run (推荐，开发模式)
echo 2. 使用 dotnet dll (部署模式)
echo 3. 直接运行 exe (可能出错)
echo.

set /p choice="请输入选项 (1-3): "

if "%choice%"=="1" (
    echo.
    echo 正在使用 dotnet run 启动...
    cd /d "E:\01LaserPackage\software\LIV_Analyzer\CSharp_Version\LIVAnalyzer.UI"
    dotnet run
) else if "%choice%"=="2" (
    echo.
    echo 正在使用 dotnet dll 启动...
    cd /d "E:\01LaserPackage\software\LIV_Analyzer\CSharp_Version\LIVAnalyzer.UI\bin\Debug\net6.0-windows"
    dotnet LIVAnalyzer.dll
) else if "%choice%"=="3" (
    echo.
    echo 正在直接运行 exe...
    cd /d "E:\01LaserPackage\software\LIV_Analyzer\CSharp_Version\LIVAnalyzer.UI\bin\Debug\net6.0-windows"
    LIVAnalyzer.exe
) else (
    echo.
    echo 无效的选项！
)

echo.
pause