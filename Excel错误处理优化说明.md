# Excel错误处理优化说明

## 问题描述
用户在加载Excel文件时遇到以下错误：
- `One or more errors occurred. ('<' is an unexpected token...)`
- `hexadecimal value 0x30, is an invalid attribute character`
- `Name cannot begin with the '=' character`
- `'B90' is an unexpected token. Expecting whitespace`

这些错误表明Excel文件中存在格式问题和无效字符。

## 错误原因分析

### 1. XML格式问题
Excel文件本质上是压缩的XML文件，错误信息显示：
- **无效的XML标记**：`'<'` 在不应该出现的位置
- **十六进制字符问题**：`0x30`, `0x3C` 等无效属性字符
- **命名规则违反**：元素名不能以 `=` 开头

### 2. 数据格式问题
- **无效数值**：单元格包含非数字字符
- **格式损坏**：文件保存时可能出现格式错误
- **编码问题**：字符编码不正确

### 3. 文件完整性问题
- **文件损坏**：部分Excel文件可能损坏
- **版本兼容性**：不同Excel版本的兼容性问题

## 解决方案

### 1. 增强文件验证
实现了更严格的文件验证机制：

```csharp
public (bool IsValid, string ErrorMessage) ValidateFile(string filePath)
{
    try
    {
        // 基本文件检查
        if (!File.Exists(filePath))
            return (false, "文件不存在");
            
        var fileInfo = new FileInfo(filePath);
        if (fileInfo.Length == 0)
            return (false, "文件为空");
            
        // 设置EPPlus许可证
        ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
        
        // 尝试打开文件验证格式
        using var package = new ExcelPackage(fileInfo);
        
        // 检查工作表
        if (package.Workbook.Worksheets.Count == 0)
            return (false, "Excel文件没有工作表");
        
        // 检查必需的工作表
        var powerSheet = package.Workbook.Worksheets["power"];
        var voltageSheet = package.Workbook.Worksheets["voltage"];
        
        if (powerSheet == null && voltageSheet == null)
            return (false, "Excel文件缺少必需的'power'或'voltage'工作表");
        
        return (true, string.Empty);
    }
    catch (InvalidDataException)
    {
        return (false, "Excel文件格式损坏或不正确，请重新保存文件");
    }
    catch (System.Xml.XmlException)
    {
        return (false, "Excel文件包含无效的XML内容，请检查文件完整性");
    }
    catch (IOException)
    {
        return (false, "文件被其他程序占用，请关闭文件后重试");
    }
    catch (UnauthorizedAccessException)
    {
        return (false, "没有权限访问该文件");
    }
}
```

### 2. 数据清理机制
实现了数值字符串清理功能：

```csharp
private static string CleanNumericString(string? input)
{
    if (string.IsNullOrEmpty(input))
        return "0";

    // 移除所有非数字、非小数点、非负号的字符
    var cleaned = Regex.Replace(input, @"[^\d\.\-\+eE]", "");
    
    // 处理多个小数点的情况，只保留第一个
    var parts = cleaned.Split('.');
    if (parts.Length > 2)
    {
        cleaned = parts[0] + "." + string.Join("", parts.Skip(1));
    }
    
    // 如果清理后为空或只有符号，返回0
    if (string.IsNullOrEmpty(cleaned) || cleaned == "-" || cleaned == "+")
        return "0";
        
    return cleaned;
}
```

### 3. 增强错误处理
在数据加载过程中增加了多层错误处理：

```csharp
// 清理字符串，移除无效字符
var currentStr = CleanNumericString(currentValues[i, 0].ToString());
var powerStr = CleanNumericString(powerValues[i, 0].ToString());

if (double.TryParse(currentStr, out var current) &&
    double.TryParse(powerStr, out var power))
{
    // 验证数值有效性
    if (!double.IsNaN(current) && !double.IsInfinity(current) &&
        !double.IsNaN(power) && !double.IsInfinity(power))
    {
        // 应用数据清理规则：负值清零
        current = Math.Max(0, current);
        power = Math.Max(0, power);
        
        dataPoints.Add(new DataPoint(current, power));
    }
}
```

### 4. 异步加载优化
改进了Excel文件的异步加载机制：

```csharp
await Task.Run(() =>
{
    try
    {
        // 设置EPPlus以处理格式问题
        ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
        
        using var package = new ExcelPackage(new FileInfo(filePath));
        
        // 串行加载以避免并发问题
        LoadPowerDataOptimized(package, data);
        LoadVoltageDataOptimized(package, data);
        LoadWavelengthDataOptimized(package, data);
        LoadDivergenceDataOptimized(package, data);
    }
    catch (InvalidDataException ex)
    {
        throw new InvalidOperationException($"Excel文件格式错误，可能文件损坏或格式不正确: {ex.Message}", ex);
    }
    catch (System.Xml.XmlException ex)
    {
        throw new InvalidOperationException($"Excel文件包含无效的XML内容，请检查文件是否正确保存: {ex.Message}", ex);
    }
    catch (IOException ex)
    {
        throw new InvalidOperationException($"无法读取Excel文件，文件可能被其他程序占用: {ex.Message}", ex);
    }
});
```

## 错误类型处理

### 1. XML格式错误
- **InvalidDataException** - 文件格式损坏
- **XmlException** - XML内容无效
- **提示用户** - 重新保存文件或检查文件完整性

### 2. 数据格式错误
- **字符清理** - 移除无效字符
- **数值验证** - 检查NaN和Infinity
- **默认值处理** - 无效数据使用默认值

### 3. 文件访问错误
- **IOException** - 文件被占用
- **UnauthorizedAccessException** - 权限不足
- **FileNotFoundException** - 文件不存在

## 用户指导

### 文件格式要求
1. **标准Excel格式** - 使用.xlsx或.xls格式
2. **必需工作表** - 包含'power'和/或'voltage'工作表
3. **数据格式** - 数值列应包含有效的数字
4. **文件完整性** - 确保文件未损坏

### 错误解决建议
1. **重新保存文件** - 使用Excel重新保存文件
2. **检查数据** - 确保数值列不包含文本或特殊字符
3. **关闭文件** - 确保Excel文件未被其他程序打开
4. **权限检查** - 确保有读取文件的权限

### 预防措施
1. **标准化数据** - 使用标准的数值格式
2. **避免特殊字符** - 工作表名和列名使用英文
3. **定期备份** - 保留文件的备份副本
4. **版本兼容** - 使用较新版本的Excel保存文件

## 技术改进

### 性能优化
- **批量读取** - 减少单元格逐个访问
- **异步处理** - 避免UI阻塞
- **错误恢复** - 自动跳过无效数据

### 兼容性增强
- **多版本支持** - 支持不同Excel版本
- **格式容错** - 处理各种数据格式问题
- **编码处理** - 正确处理字符编码

通过这些优化，系统现在能够更好地处理有问题的Excel文件，提供更清晰的错误信息，并在可能的情况下自动修复数据格式问题。
