@echo off
chcp 65001 >nul
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    LIV Analyzer C# 版本                     ║
echo ║                     环境检查工具                             ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM 检查.NET SDK
echo 🔍 检查 .NET SDK...
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ .NET SDK 未安装
    echo.
    echo 📥 需要安装 .NET 6 SDK:
    echo    网址: https://dotnet.microsoft.com/download/dotnet/6.0
    echo    选择: .NET 6.0 SDK ^(不是Runtime^)
    echo.
    echo 💡 安装完成后请重新运行此工具
    echo.
    set /p choice="是否现在打开下载页面? (Y/N): "
    if /i "%choice%"=="Y" (
        start https://dotnet.microsoft.com/download/dotnet/6.0
    )
    pause
    exit /b 1
) else (
    for /f "tokens=*" %%i in ('dotnet --version') do set dotnet_version=%%i
    echo ✅ .NET SDK 已安装 ^(版本: %dotnet_version%^)
)

echo.
echo 🔍 检查项目文件...
if not exist "LIVAnalyzer.sln" (
    echo ❌ 找不到解决方案文件
    echo    请确保在正确的目录中运行此工具
    pause
    exit /b 1
)
echo ✅ 项目文件完整

echo.
echo 🔍 检查 Visual Studio...
where devenv >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  Visual Studio 未安装或不在PATH中
    echo    建议安装 Visual Studio 2022 Community ^(免费^)
) else (
    echo ✅ Visual Studio 可用
)

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                       环境检查完成                           ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 📋 下一步操作建议:
echo.
echo    1️⃣  运行项目: 双击 "运行向导.bat"
echo    2️⃣  开发调试: 双击 "LIVAnalyzer.sln" 用VS打开
echo    3️⃣  查看指南: 打开 "快速开始指南.md"
echo.
pause