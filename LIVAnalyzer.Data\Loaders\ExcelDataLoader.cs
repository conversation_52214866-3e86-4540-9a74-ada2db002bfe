using LIVAnalyzer.Data.Interfaces;
using LIVAnalyzer.Models;
using OfficeOpenXml;
using System.Data;
using System.Text.RegularExpressions;

namespace LIVAnalyzer.Data.Loaders
{
    /// <summary>
    /// Excel文件数据加载器
    /// </summary>
    public class ExcelDataLoader : IDataLoader
    {
        public ExcelDataLoader()
        {
            // 设置EPPlus许可证模式
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
        }
        
        public async Task<LIVMeasurementData?> LoadExcelDataAsync(string filePath)
        {
            var fileName = Path.GetFileName(filePath);
            try
            {
                var data = new LIVMeasurementData { FileName = fileName };

                // 使用异步方式加载Excel文件，增强错误处理
                await Task.Run(() =>
                {
                    try
                    {
                        // 设置EPPlus以处理格式问题
                        ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

                        using var package = new ExcelPackage(new FileInfo(filePath));

                        // 串行加载以避免并发问题，但仍然异步
                        LoadPowerDataOptimized(package, data);
                        LoadVoltageDataOptimized(package, data);
                        LoadWavelengthDataOptimized(package, data);
                        LoadDivergenceDataOptimized(package, data);
                    }
                    catch (InvalidDataException ex)
                    {
                        throw new InvalidOperationException($"Excel文件格式错误，可能文件损坏或格式不正确: {ex.Message}", ex);
                    }
                    catch (System.Xml.XmlException ex)
                    {
                        throw new InvalidOperationException($"Excel文件包含无效的XML内容，请检查文件是否正确保存: {ex.Message}", ex);
                    }
                    catch (IOException ex)
                    {
                        throw new InvalidOperationException($"无法读取Excel文件，文件可能被其他程序占用: {ex.Message}", ex);
                    }
                });

                return data;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"加载Excel文件失败 ({fileName}): {ex.Message}", ex);
            }
        }
        
        public Task<LIVMeasurementData?> LoadCsvDataAsync(string filePath)
        {
            throw new NotImplementedException("Excel加载器不支持CSV文件");
        }
        
        public (bool IsValid, string ErrorMessage) ValidateFile(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                    return (false, "文件不存在");

                var extension = Path.GetExtension(filePath).ToLower();
                if (extension != ".xlsx" && extension != ".xls")
                    return (false, "不支持的文件格式，仅支持Excel文件");

                var fileInfo = new FileInfo(filePath);
                if (fileInfo.Length == 0)
                    return (false, "文件为空");

                if (fileInfo.Length > 500 * 1024 * 1024) // 500MB限制
                    return (false, "文件过大，超过500MB限制");

                // 增强的文件验证
                try
                {
                    // 设置EPPlus许可证
                    ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

                    // 尝试打开文件验证格式
                    using var package = new ExcelPackage(fileInfo);

                    if (package.Workbook.Worksheets.Count == 0)
                        return (false, "Excel文件没有工作表");

                    // 检查必需的工作表
                    var powerSheet = package.Workbook.Worksheets["power"];
                    var voltageSheet = package.Workbook.Worksheets["voltage"];

                    if (powerSheet == null && voltageSheet == null)
                        return (false, "Excel文件缺少必需的'power'或'voltage'工作表");

                    return (true, string.Empty);
                }
                catch (InvalidDataException)
                {
                    return (false, "Excel文件格式损坏或不正确，请重新保存文件");
                }
                catch (System.Xml.XmlException)
                {
                    return (false, "Excel文件包含无效的XML内容，请检查文件完整性");
                }
                catch (IOException)
                {
                    return (false, "文件被其他程序占用，请关闭文件后重试");
                }
                catch (UnauthorizedAccessException)
                {
                    return (false, "没有权限访问该文件");
                }
            }
            catch (Exception ex)
            {
                return (false, $"文件验证失败: {ex.Message}");
            }
        }
        
        private void LoadPowerData(ExcelPackage package, LIVMeasurementData data)
        {
            LoadPowerDataOptimized(package, data);
        }

        private void LoadPowerDataOptimized(ExcelPackage package, LIVMeasurementData data)
        {
            var worksheet = package.Workbook.Worksheets["power"];
            if (worksheet == null)
            {
                throw new InvalidOperationException("Excel文件缺少必需的'power'工作表");
            }

            var rowCount = worksheet.Dimension?.Rows ?? 0;
            if (rowCount < 2) return; // 至少需要标题行和一行数据

            // 查找列索引
            int currentCol = -1, powerCol = -1;
            for (int col = 1; col <= worksheet.Dimension?.Columns; col++)
            {
                var header = worksheet.Cells[1, col].Text?.Trim();
                if (string.Equals(header, "Current", StringComparison.OrdinalIgnoreCase))
                    currentCol = col;
                else if (string.Equals(header, "Power", StringComparison.OrdinalIgnoreCase))
                    powerCol = col;
            }

            if (currentCol == -1 || powerCol == -1)
            {
                throw new InvalidOperationException("power工作表缺少必需的列：Current, Power");
            }

            // 优化：批量读取数据
            try
            {
                var currentRange = worksheet.Cells[2, currentCol, rowCount, currentCol];
                var powerRange = worksheet.Cells[2, powerCol, rowCount, powerCol];

                var currentValues = currentRange.Value as object[,];
                var powerValues = powerRange.Value as object[,];

                if (currentValues != null && powerValues != null)
                {
                    var dataPoints = new List<DataPoint>(rowCount - 1);

                    for (int i = 0; i < currentValues.GetLength(0); i++)
                    {
                        try
                        {
                            if (currentValues[i, 0] != null && powerValues[i, 0] != null)
                            {
                                // 清理字符串，移除无效字符
                                var currentStr = CleanNumericString(currentValues[i, 0].ToString());
                                var powerStr = CleanNumericString(powerValues[i, 0].ToString());

                                if (double.TryParse(currentStr, out var current) &&
                                    double.TryParse(powerStr, out var power))
                                {
                                    // 验证数值有效性
                                    if (!double.IsNaN(current) && !double.IsInfinity(current) &&
                                        !double.IsNaN(power) && !double.IsInfinity(power))
                                    {
                                        // 应用数据清理规则：负值清零
                                        current = Math.Max(0, current);
                                        power = Math.Max(0, power);

                                        dataPoints.Add(new DataPoint(current, power));
                                    }
                                }
                            }
                        }
                        catch
                        {
                            // 跳过无法解析的行
                            continue;
                        }
                    }

                    data.CurrentPowerData.AddRange(dataPoints);
                    return;
                }
            }
            catch
            {
                // 批量读取失败，回退到逐行读取
            }

            // 回退到逐行读取
            for (int row = 2; row <= rowCount; row++)
            {
                try
                {
                    var current = worksheet.Cells[row, currentCol].GetValue<double>();
                    var power = worksheet.Cells[row, powerCol].GetValue<double>();

                    current = Math.Max(0, current);
                    power = Math.Max(0, power);

                    data.CurrentPowerData.Add(new DataPoint(current, power));
                }
                catch
                {
                    continue;
                }
            }
        }
        
        private void LoadVoltageData(ExcelPackage package, LIVMeasurementData data)
        {
            LoadVoltageDataOptimized(package, data);
        }

        private void LoadVoltageDataOptimized(ExcelPackage package, LIVMeasurementData data)
        {
            var worksheet = package.Workbook.Worksheets["voltage"];
            if (worksheet == null)
            {
                throw new InvalidOperationException("Excel文件缺少必需的'voltage'工作表");
            }

            var rowCount = worksheet.Dimension?.Rows ?? 0;
            if (rowCount < 2) return; // 至少需要标题行和一行数据

            // 查找列索引
            int currentCol = -1, voltageCol = -1;
            for (int col = 1; col <= worksheet.Dimension?.Columns; col++)
            {
                var header = worksheet.Cells[1, col].Text?.Trim();
                if (string.Equals(header, "Current", StringComparison.OrdinalIgnoreCase))
                    currentCol = col;
                else if (string.Equals(header, "Voltage", StringComparison.OrdinalIgnoreCase))
                    voltageCol = col;
            }

            if (currentCol == -1 || voltageCol == -1)
            {
                throw new InvalidOperationException("voltage工作表缺少必需的列：Current, Voltage");
            }

            // 优化：批量读取数据
            try
            {
                var currentRange = worksheet.Cells[2, currentCol, rowCount, currentCol];
                var voltageRange = worksheet.Cells[2, voltageCol, rowCount, voltageCol];

                var currentValues = currentRange.Value as object[,];
                var voltageValues = voltageRange.Value as object[,];

                if (currentValues != null && voltageValues != null)
                {
                    var dataPoints = new List<DataPoint>(rowCount - 1);

                    for (int i = 0; i < currentValues.GetLength(0); i++)
                    {
                        try
                        {
                            if (currentValues[i, 0] != null && voltageValues[i, 0] != null)
                            {
                                // 清理字符串，移除无效字符
                                var currentStr = CleanNumericString(currentValues[i, 0].ToString());
                                var voltageStr = CleanNumericString(voltageValues[i, 0].ToString());

                                if (double.TryParse(currentStr, out var current) &&
                                    double.TryParse(voltageStr, out var voltage))
                                {
                                    // 验证数值有效性
                                    if (!double.IsNaN(current) && !double.IsInfinity(current) &&
                                        !double.IsNaN(voltage) && !double.IsInfinity(voltage))
                                    {
                                        // 应用数据清理规则：负值清零
                                        current = Math.Max(0, current);
                                        voltage = Math.Max(0, voltage);

                                        dataPoints.Add(new DataPoint(current, voltage));
                                    }
                                }
                            }
                        }
                        catch
                        {
                            // 跳过无法解析的行
                            continue;
                        }
                    }

                    data.CurrentVoltageData.AddRange(dataPoints);
                    return;
                }
            }
            catch
            {
                // 批量读取失败，回退到逐行读取
            }

            // 回退到逐行读取
            for (int row = 2; row <= rowCount; row++)
            {
                try
                {
                    var current = worksheet.Cells[row, currentCol].GetValue<double>();
                    var voltage = worksheet.Cells[row, voltageCol].GetValue<double>();

                    current = Math.Max(0, current);
                    voltage = Math.Max(0, voltage);

                    data.CurrentVoltageData.Add(new DataPoint(current, voltage));
                }
                catch
                {
                    continue;
                }
            }
        }
        
        private void LoadWavelengthData(ExcelPackage package, LIVMeasurementData data)
        {
            LoadWavelengthDataOptimized(package, data);
        }

        private void LoadWavelengthDataOptimized(ExcelPackage package, LIVMeasurementData data)
        {
            var worksheet = package.Workbook.Worksheets["wavelength"];
            if (worksheet == null)
            {
                // 波长数据是可选的，不抛出异常
                return;
            }

            var rowCount = worksheet.Dimension?.Rows ?? 0;
            if (rowCount < 2) return; // 至少需要标题行和一行数据

            // 查找列索引
            int wavelengthCol = -1, intensityCol = -1;
            for (int col = 1; col <= worksheet.Dimension?.Columns; col++)
            {
                var header = worksheet.Cells[1, col].Text?.Trim();
                if (string.Equals(header, "Wavelength", StringComparison.OrdinalIgnoreCase))
                    wavelengthCol = col;
                else if (string.Equals(header, "Intensity", StringComparison.OrdinalIgnoreCase))
                    intensityCol = col;
            }

            if (wavelengthCol == -1 || intensityCol == -1)
            {
                // 波长数据可选，记录警告但不抛出异常
                Console.WriteLine("警告：wavelength工作表缺少必需的列：Wavelength, Intensity");
                return;
            }

            // 对于波长数据，使用简化的批量读取
            for (int row = 2; row <= rowCount; row++)
            {
                try
                {
                    var wavelength = worksheet.Cells[row, wavelengthCol].GetValue<double>();
                    var intensity = worksheet.Cells[row, intensityCol].GetValue<double>();

                    data.WavelengthIntensityData.Add(new DataPoint(wavelength, intensity));
                }
                catch
                {
                    // 跳过无法解析的行
                    continue;
                }
            }
        }
        
        private void LoadDivergenceData(ExcelPackage package, LIVMeasurementData data)
        {
            LoadDivergenceDataOptimized(package, data);
        }

        private void LoadDivergenceDataOptimized(ExcelPackage package, LIVMeasurementData data)
        {
            // 加载水平发散角数据
            var hffWorksheet = package.Workbook.Worksheets["HFF"];
            if (hffWorksheet != null)
            {
                data.HorizontalDivergenceData = new List<DataPoint>();
                var rowCount = hffWorksheet.Dimension?.Rows ?? 0;

                // 查找列索引
                int angleCol = -1, photocurrentCol = -1;
                for (int col = 1; col <= hffWorksheet.Dimension?.Columns; col++)
                {
                    var header = hffWorksheet.Cells[1, col].Text?.Trim();
                    if (string.Equals(header, "Angle", StringComparison.OrdinalIgnoreCase))
                        angleCol = col;
                    else if (string.Equals(header, "Photocurrent", StringComparison.OrdinalIgnoreCase))
                        photocurrentCol = col;
                }

                if (angleCol != -1 && photocurrentCol != -1)
                {
                    for (int row = 2; row <= rowCount; row++)
                    {
                        try
                        {
                            var angle = hffWorksheet.Cells[row, angleCol].GetValue<double>();
                            var photocurrent = hffWorksheet.Cells[row, photocurrentCol].GetValue<double>();
                            data.HorizontalDivergenceData.Add(new DataPoint(angle, photocurrent));
                        }
                        catch
                        {
                            // 跳过无法解析的行
                            continue;
                        }
                    }
                }
            }
            
            // 加载垂直发散角数据
            var vffWorksheet = package.Workbook.Worksheets["VFF"];
            if (vffWorksheet != null)
            {
                data.VerticalDivergenceData = new List<DataPoint>();
                var rowCount = vffWorksheet.Dimension?.Rows ?? 0;
                
                // 查找列索引
                int angleCol = -1, photocurrentCol = -1;
                for (int col = 1; col <= vffWorksheet.Dimension?.Columns; col++)
                {
                    var header = vffWorksheet.Cells[1, col].Text?.Trim();
                    if (string.Equals(header, "Angle", StringComparison.OrdinalIgnoreCase))
                        angleCol = col;
                    else if (string.Equals(header, "Photocurrent", StringComparison.OrdinalIgnoreCase))
                        photocurrentCol = col;
                }
                
                if (angleCol != -1 && photocurrentCol != -1)
                {
                    for (int row = 2; row <= rowCount; row++)
                    {
                        try
                        {
                            var angle = vffWorksheet.Cells[row, angleCol].GetValue<double>();
                            var photocurrent = vffWorksheet.Cells[row, photocurrentCol].GetValue<double>();
                            data.VerticalDivergenceData.Add(new DataPoint(angle, photocurrent));
                        }
                        catch
                        {
                            // 跳过无法解析的行
                            continue;
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 清理数值字符串，移除无效字符
        /// </summary>
        private static string CleanNumericString(string? input)
        {
            if (string.IsNullOrEmpty(input))
                return "0";

            // 移除所有非数字、非小数点、非负号的字符
            var cleaned = Regex.Replace(input, @"[^\d\.\-\+eE]", "");

            // 处理多个小数点的情况，只保留第一个
            var parts = cleaned.Split('.');
            if (parts.Length > 2)
            {
                cleaned = parts[0] + "." + string.Join("", parts.Skip(1));
            }

            // 如果清理后为空或只有符号，返回0
            if (string.IsNullOrEmpty(cleaned) || cleaned == "-" || cleaned == "+")
                return "0";

            return cleaned;
        }
    }
}