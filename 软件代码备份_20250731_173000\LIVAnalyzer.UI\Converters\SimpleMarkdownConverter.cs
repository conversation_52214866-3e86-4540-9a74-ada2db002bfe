using System;
using System.Collections.Generic;
using System.Globalization;
using System.Text.RegularExpressions;
using System.Windows;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Media;

namespace LIVAnalyzer.UI.Converters
{
    /// <summary>
    /// 简单的Markdown到FlowDocument转换器
    /// </summary>
    public class SimpleMarkdownConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is not string markdown || string.IsNullOrEmpty(markdown))
                return CreateEmptyDocument();

            try
            {
                return ConvertMarkdownToFlowDocument(markdown);
            }
            catch (Exception)
            {
                // 如果转换失败，返回纯文本
                return CreateFallbackDocument(markdown);
            }
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }

        private FlowDocument CreateEmptyDocument()
        {
            return new FlowDocument
            {
                FontFamily = new FontFamily("Microsoft YaHei, 微软雅黑"),
                FontSize = 14,
                PagePadding = new Thickness(20),
                LineHeight = 22
            };
        }

        private FlowDocument CreateFallbackDocument(string text)
        {
            var doc = CreateEmptyDocument();
            doc.Blocks.Add(new Paragraph(new Run(text)));
            return doc;
        }

        private FlowDocument ConvertMarkdownToFlowDocument(string markdown)
        {
            var document = CreateEmptyDocument();
            var lines = markdown.Split('\n');
            Paragraph currentParagraph = null;

            foreach (var line in lines)
            {
                var trimmedLine = line.Trim();
                
                if (string.IsNullOrEmpty(trimmedLine))
                {
                    // 空行，结束当前段落
                    if (currentParagraph != null)
                    {
                        document.Blocks.Add(currentParagraph);
                        currentParagraph = null;
                    }
                    continue;
                }

                // 处理标题
                if (trimmedLine.StartsWith("#"))
                {
                    if (currentParagraph != null)
                    {
                        document.Blocks.Add(currentParagraph);
                        currentParagraph = null;
                    }
                    
                    var heading = CreateHeading(trimmedLine);
                    document.Blocks.Add(heading);
                    continue;
                }

                // 处理列表项
                if (trimmedLine.StartsWith("- ") || trimmedLine.StartsWith("* "))
                {
                    if (currentParagraph != null)
                    {
                        document.Blocks.Add(currentParagraph);
                        currentParagraph = null;
                    }
                    
                    var listItem = CreateListItem(trimmedLine.Substring(2));
                    document.Blocks.Add(listItem);
                    continue;
                }

                // 普通段落
                if (currentParagraph == null)
                {
                    currentParagraph = new Paragraph
                    {
                        Margin = new Thickness(0, 6, 0, 6)
                    };
                }

                // 处理行内格式
                var inlines = ProcessInlineFormatting(trimmedLine);
                foreach (var inline in inlines)
                {
                    currentParagraph.Inlines.Add(inline);
                }
                
                currentParagraph.Inlines.Add(new Run(" "));
            }

            // 添加最后一个段落
            if (currentParagraph != null)
            {
                document.Blocks.Add(currentParagraph);
            }

            return document;
        }

        private Paragraph CreateHeading(string line)
        {
            var level = 0;
            while (level < line.Length && line[level] == '#')
                level++;

            var text = line.Substring(level).Trim();
            var paragraph = new Paragraph(new Run(text));

            // 使用主题感知的颜色资源
            var foregroundBrush = GetThemeAwareBrush("AppForegroundBrush");

            // 设置标题样式
            switch (level)
            {
                case 1:
                    paragraph.FontSize = 24;
                    paragraph.FontWeight = FontWeights.Bold;
                    paragraph.Foreground = foregroundBrush;
                    paragraph.Margin = new Thickness(0, 20, 0, 12);
                    break;
                case 2:
                    paragraph.FontSize = 20;
                    paragraph.FontWeight = FontWeights.Bold;
                    paragraph.Foreground = foregroundBrush;
                    paragraph.Margin = new Thickness(0, 16, 0, 10);
                    break;
                case 3:
                    paragraph.FontSize = 18;
                    paragraph.FontWeight = FontWeights.SemiBold;
                    paragraph.Foreground = foregroundBrush;
                    paragraph.Margin = new Thickness(0, 14, 0, 8);
                    break;
                default:
                    paragraph.FontSize = 16;
                    paragraph.FontWeight = FontWeights.SemiBold;
                    paragraph.Foreground = foregroundBrush;
                    paragraph.Margin = new Thickness(0, 12, 0, 6);
                    break;
            }

            return paragraph;
        }

        /// <summary>
        /// 获取主题感知的画刷资源
        /// </summary>
        private Brush GetThemeAwareBrush(string resourceKey)
        {
            try
            {
                // 尝试从应用程序资源中获取动态资源
                if (Application.Current?.Resources[resourceKey] is Brush brush)
                {
                    return brush;
                }
                
                // 如果找不到资源，返回默认的前景色
                return SystemColors.WindowTextBrush;
            }
            catch
            {
                // 异常时返回系统默认前景色
                return SystemColors.WindowTextBrush;
            }
        }

        private Paragraph CreateListItem(string text)
        {
            var paragraph = new Paragraph
            {
                Margin = new Thickness(20, 4, 0, 4)
            };
            
            // 使用主题感知的颜色
            var bulletRun = new Run("• ") { FontWeight = FontWeights.Bold };
            bulletRun.Foreground = GetThemeAwareBrush("AppForegroundBrush");
            paragraph.Inlines.Add(bulletRun);
            
            var inlines = ProcessInlineFormatting(text);
            foreach (var inline in inlines)
            {
                // 确保每个inline也使用主题颜色
                if (inline.Foreground == null)
                {
                    inline.Foreground = GetThemeAwareBrush("AppForegroundBrush");
                }
                paragraph.Inlines.Add(inline);
            }
            
            return paragraph;
        }

        private List<Inline> ProcessInlineFormatting(string text)
        {
            var inlines = new List<Inline>();
            var foregroundBrush = GetThemeAwareBrush("AppForegroundBrush");
            
            // 简单处理粗体 **text**
            var boldPattern = @"\*\*(.*?)\*\*";
            var parts = Regex.Split(text, boldPattern);
            
            for (int i = 0; i < parts.Length; i++)
            {
                if (i % 2 == 0)
                {
                    // 普通文本
                    if (!string.IsNullOrEmpty(parts[i]))
                    {
                        var run = new Run(parts[i]);
                        run.Foreground = foregroundBrush;
                        inlines.Add(run);
                    }
                }
                else
                {
                    // 粗体文本
                    var boldRun = new Run(parts[i]) { FontWeight = FontWeights.Bold };
                    boldRun.Foreground = foregroundBrush;
                    inlines.Add(boldRun);
                }
            }
            
            // 如果没有找到格式，返回原始文本
            if (inlines.Count == 0)
            {
                var run = new Run(text);
                run.Foreground = foregroundBrush;
                inlines.Add(run);
            }
            
            return inlines;
        }
    }
}
