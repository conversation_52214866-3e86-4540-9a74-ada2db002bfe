using System;
using System.Globalization;
using System.Windows.Data;
using LIVAnalyzer.Models;
using LIVAnalyzer.UI.ViewModels;

namespace LIVAnalyzer.UI.Converters
{
    /// <summary>
    /// 平滑算法类型到ViewModel转换器
    /// </summary>
    public class AlgorithmTypeToViewModelConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            // 这个转换器主要用于双向绑定，实际的转换逻辑在ViewModel中处理
            return value;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is SmoothingAlgorithmViewModel viewModel)
            {
                return viewModel.AlgorithmType;
            }
            
            return SmoothingAlgorithmType.MovingAverage;
        }
    }
}