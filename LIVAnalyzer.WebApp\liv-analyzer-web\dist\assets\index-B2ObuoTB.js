const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/plot-q4UycjGG.js","assets/react-DXNcgoS8.js","assets/zoom-Bb815BNs.js","assets/csv-rDawIJ35.js","assets/xlsx-hHwYTrUW.js"])))=>i.map(i=>d[i]);
import{r as ny,a as ly,b as k,R as Ct,g as ay,c as _h,L as Rm,d as uy,e as iy}from"./react-DXNcgoS8.js";import{r as cy}from"./csv-rDawIJ35.js";import{read as oy,utils as ry}from"./xlsx-hHwYTrUW.js";(function(){const i=document.createElement("link").relList;if(i&&i.supports&&i.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))r(s);new MutationObserver(s=>{for(const m of s)if(m.type==="childList")for(const d of m.addedNodes)d.tagName==="LINK"&&d.rel==="modulepreload"&&r(d)}).observe(document,{childList:!0,subtree:!0});function o(s){const m={};return s.integrity&&(m.integrity=s.integrity),s.referrerPolicy&&(m.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?m.credentials="include":s.crossOrigin==="anonymous"?m.credentials="omit":m.credentials="same-origin",m}function r(s){if(s.ep)return;s.ep=!0;const m=o(s);fetch(s.href,m)}})();var rr={exports:{}},lu={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Cm;function sy(){if(Cm)return lu;Cm=1;var a=Symbol.for("react.transitional.element"),i=Symbol.for("react.fragment");function o(r,s,m){var d=null;if(m!==void 0&&(d=""+m),s.key!==void 0&&(d=""+s.key),"key"in s){m={};for(var v in s)v!=="key"&&(m[v]=s[v])}else m=s;return s=m.ref,{$$typeof:a,type:r,key:d,ref:s!==void 0?s:null,props:m}}return lu.Fragment=i,lu.jsx=o,lu.jsxs=o,lu}var Dm;function fy(){return Dm||(Dm=1,rr.exports=sy()),rr.exports}var h=fy(),sr={exports:{}},au={},fr={exports:{}},dr={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var km;function dy(){return km||(km=1,function(a){function i(T,H){var U=T.length;T.push(H);e:for(;0<U;){var ge=U-1>>>1,ve=T[ge];if(0<s(ve,H))T[ge]=H,T[U]=ve,U=ge;else break e}}function o(T){return T.length===0?null:T[0]}function r(T){if(T.length===0)return null;var H=T[0],U=T.pop();if(U!==H){T[0]=U;e:for(var ge=0,ve=T.length,qe=ve>>>1;ge<qe;){var xe=2*(ge+1)-1,te=T[xe],se=xe+1,et=T[se];if(0>s(te,U))se<ve&&0>s(et,te)?(T[ge]=et,T[se]=U,ge=se):(T[ge]=te,T[xe]=U,ge=xe);else if(se<ve&&0>s(et,U))T[ge]=et,T[se]=U,ge=se;else break e}}return H}function s(T,H){var U=T.sortIndex-H.sortIndex;return U!==0?U:T.id-H.id}if(a.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var m=performance;a.unstable_now=function(){return m.now()}}else{var d=Date,v=d.now();a.unstable_now=function(){return d.now()-v}}var p=[],b=[],x=1,E=null,j=3,D=!1,B=!1,L=!1,G=!1,Q=typeof setTimeout=="function"?setTimeout:null,F=typeof clearTimeout=="function"?clearTimeout:null,R=typeof setImmediate<"u"?setImmediate:null;function Z(T){for(var H=o(b);H!==null;){if(H.callback===null)r(b);else if(H.startTime<=T)r(b),H.sortIndex=H.expirationTime,i(p,H);else break;H=o(b)}}function ee(T){if(L=!1,Z(T),!B)if(o(p)!==null)B=!0,J||(J=!0,Ue());else{var H=o(b);H!==null&&Ge(ee,H.startTime-T)}}var J=!1,oe=-1,V=5,ze=-1;function Re(){return G?!0:!(a.unstable_now()-ze<V)}function ot(){if(G=!1,J){var T=a.unstable_now();ze=T;var H=!0;try{e:{B=!1,L&&(L=!1,F(oe),oe=-1),D=!0;var U=j;try{t:{for(Z(T),E=o(p);E!==null&&!(E.expirationTime>T&&Re());){var ge=E.callback;if(typeof ge=="function"){E.callback=null,j=E.priorityLevel;var ve=ge(E.expirationTime<=T);if(T=a.unstable_now(),typeof ve=="function"){E.callback=ve,Z(T),H=!0;break t}E===o(p)&&r(p),Z(T)}else r(p);E=o(p)}if(E!==null)H=!0;else{var qe=o(b);qe!==null&&Ge(ee,qe.startTime-T),H=!1}}break e}finally{E=null,j=U,D=!1}H=void 0}}finally{H?Ue():J=!1}}}var Ue;if(typeof R=="function")Ue=function(){R(ot)};else if(typeof MessageChannel<"u"){var qt=new MessageChannel,Dt=qt.port2;qt.port1.onmessage=ot,Ue=function(){Dt.postMessage(null)}}else Ue=function(){Q(ot,0)};function Ge(T,H){oe=Q(function(){T(a.unstable_now())},H)}a.unstable_IdlePriority=5,a.unstable_ImmediatePriority=1,a.unstable_LowPriority=4,a.unstable_NormalPriority=3,a.unstable_Profiling=null,a.unstable_UserBlockingPriority=2,a.unstable_cancelCallback=function(T){T.callback=null},a.unstable_forceFrameRate=function(T){0>T||125<T?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):V=0<T?Math.floor(1e3/T):5},a.unstable_getCurrentPriorityLevel=function(){return j},a.unstable_next=function(T){switch(j){case 1:case 2:case 3:var H=3;break;default:H=j}var U=j;j=H;try{return T()}finally{j=U}},a.unstable_requestPaint=function(){G=!0},a.unstable_runWithPriority=function(T,H){switch(T){case 1:case 2:case 3:case 4:case 5:break;default:T=3}var U=j;j=T;try{return H()}finally{j=U}},a.unstable_scheduleCallback=function(T,H,U){var ge=a.unstable_now();switch(typeof U=="object"&&U!==null?(U=U.delay,U=typeof U=="number"&&0<U?ge+U:ge):U=ge,T){case 1:var ve=-1;break;case 2:ve=250;break;case 5:ve=1073741823;break;case 4:ve=1e4;break;default:ve=5e3}return ve=U+ve,T={id:x++,callback:H,priorityLevel:T,startTime:U,expirationTime:ve,sortIndex:-1},U>ge?(T.sortIndex=U,i(b,T),o(p)===null&&T===o(b)&&(L?(F(oe),oe=-1):L=!0,Ge(ee,U-ge))):(T.sortIndex=ve,i(p,T),B||D||(B=!0,J||(J=!0,Ue()))),T},a.unstable_shouldYield=Re,a.unstable_wrapCallback=function(T){var H=j;return function(){var U=j;j=H;try{return T.apply(this,arguments)}finally{j=U}}}}(dr)),dr}var Um;function my(){return Um||(Um=1,fr.exports=dy()),fr.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Vm;function hy(){if(Vm)return au;Vm=1;var a=my(),i=ny(),o=ly();function r(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function s(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function m(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function d(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function v(e){if(m(e)!==e)throw Error(r(188))}function p(e){var t=e.alternate;if(!t){if(t=m(e),t===null)throw Error(r(188));return t!==e?null:e}for(var n=e,l=t;;){var u=n.return;if(u===null)break;var c=u.alternate;if(c===null){if(l=u.return,l!==null){n=l;continue}break}if(u.child===c.child){for(c=u.child;c;){if(c===n)return v(u),e;if(c===l)return v(u),t;c=c.sibling}throw Error(r(188))}if(n.return!==l.return)n=u,l=c;else{for(var f=!1,g=u.child;g;){if(g===n){f=!0,n=u,l=c;break}if(g===l){f=!0,l=u,n=c;break}g=g.sibling}if(!f){for(g=c.child;g;){if(g===n){f=!0,n=c,l=u;break}if(g===l){f=!0,l=c,n=u;break}g=g.sibling}if(!f)throw Error(r(189))}}if(n.alternate!==l)throw Error(r(190))}if(n.tag!==3)throw Error(r(188));return n.stateNode.current===n?e:t}function b(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e;for(e=e.child;e!==null;){if(t=b(e),t!==null)return t;e=e.sibling}return null}var x=Object.assign,E=Symbol.for("react.element"),j=Symbol.for("react.transitional.element"),D=Symbol.for("react.portal"),B=Symbol.for("react.fragment"),L=Symbol.for("react.strict_mode"),G=Symbol.for("react.profiler"),Q=Symbol.for("react.provider"),F=Symbol.for("react.consumer"),R=Symbol.for("react.context"),Z=Symbol.for("react.forward_ref"),ee=Symbol.for("react.suspense"),J=Symbol.for("react.suspense_list"),oe=Symbol.for("react.memo"),V=Symbol.for("react.lazy"),ze=Symbol.for("react.activity"),Re=Symbol.for("react.memo_cache_sentinel"),ot=Symbol.iterator;function Ue(e){return e===null||typeof e!="object"?null:(e=ot&&e[ot]||e["@@iterator"],typeof e=="function"?e:null)}var qt=Symbol.for("react.client.reference");function Dt(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===qt?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case B:return"Fragment";case G:return"Profiler";case L:return"StrictMode";case ee:return"Suspense";case J:return"SuspenseList";case ze:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case D:return"Portal";case R:return(e.displayName||"Context")+".Provider";case F:return(e._context.displayName||"Context")+".Consumer";case Z:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case oe:return t=e.displayName||null,t!==null?t:Dt(e.type)||"Memo";case V:t=e._payload,e=e._init;try{return Dt(e(t))}catch{}}return null}var Ge=Array.isArray,T=i.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,H=o.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,U={pending:!1,data:null,method:null,action:null},ge=[],ve=-1;function qe(e){return{current:e}}function xe(e){0>ve||(e.current=ge[ve],ge[ve]=null,ve--)}function te(e,t){ve++,ge[ve]=e.current,e.current=t}var se=qe(null),et=qe(null),jt=qe(null),Ne=qe(null);function Zn(e,t){switch(te(jt,t),te(et,e),te(se,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?im(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)t=im(t),e=cm(t,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}xe(se),te(se,e)}function Tt(){xe(se),xe(et),xe(jt)}function on(e){e.memoizedState!==null&&te(Ne,e);var t=se.current,n=cm(t,e.type);t!==n&&(te(et,e),te(se,n))}function rn(e){et.current===e&&(xe(se),xe(et)),Ne.current===e&&(xe(Ne),Ia._currentValue=U)}var sn=Object.prototype.hasOwnProperty,$i=a.unstable_scheduleCallback,Fi=a.unstable_cancelCallback,Dg=a.unstable_shouldYield,kg=a.unstable_requestPaint,kt=a.unstable_now,Ug=a.unstable_getCurrentPriorityLevel,Hr=a.unstable_ImmediatePriority,Br=a.unstable_UserBlockingPriority,pu=a.unstable_NormalPriority,Vg=a.unstable_LowPriority,Lr=a.unstable_IdlePriority,Zg=a.log,Hg=a.unstable_setDisableYieldValue,ia=null,rt=null;function fn(e){if(typeof Zg=="function"&&Hg(e),rt&&typeof rt.setStrictMode=="function")try{rt.setStrictMode(ia,e)}catch{}}var st=Math.clz32?Math.clz32:Gg,Bg=Math.log,Lg=Math.LN2;function Gg(e){return e>>>=0,e===0?32:31-(Bg(e)/Lg|0)|0}var yu=256,bu=4194304;function Hn(e){var t=e&42;if(t!==0)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function xu(e,t,n){var l=e.pendingLanes;if(l===0)return 0;var u=0,c=e.suspendedLanes,f=e.pingedLanes;e=e.warmLanes;var g=l&134217727;return g!==0?(l=g&~c,l!==0?u=Hn(l):(f&=g,f!==0?u=Hn(f):n||(n=g&~e,n!==0&&(u=Hn(n))))):(g=l&~c,g!==0?u=Hn(g):f!==0?u=Hn(f):n||(n=l&~e,n!==0&&(u=Hn(n)))),u===0?0:t!==0&&t!==u&&(t&c)===0&&(c=u&-u,n=t&-t,c>=n||c===32&&(n&4194048)!==0)?t:u}function ca(e,t){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)===0}function qg(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Gr(){var e=yu;return yu<<=1,(yu&4194048)===0&&(yu=256),e}function qr(){var e=bu;return bu<<=1,(bu&62914560)===0&&(bu=4194304),e}function Wi(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function oa(e,t){e.pendingLanes|=t,t!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function Yg(e,t,n,l,u,c){var f=e.pendingLanes;e.pendingLanes=n,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=n,e.entangledLanes&=n,e.errorRecoveryDisabledLanes&=n,e.shellSuspendCounter=0;var g=e.entanglements,y=e.expirationTimes,z=e.hiddenUpdates;for(n=f&~n;0<n;){var M=31-st(n),C=1<<M;g[M]=0,y[M]=-1;var N=z[M];if(N!==null)for(z[M]=null,M=0;M<N.length;M++){var A=N[M];A!==null&&(A.lane&=-536870913)}n&=~C}l!==0&&Yr(e,l,0),c!==0&&u===0&&e.tag!==0&&(e.suspendedLanes|=c&~(f&~t))}function Yr(e,t,n){e.pendingLanes|=t,e.suspendedLanes&=~t;var l=31-st(t);e.entangledLanes|=t,e.entanglements[l]=e.entanglements[l]|1073741824|n&4194090}function Xr(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var l=31-st(n),u=1<<l;u&t|e[l]&t&&(e[l]|=t),n&=~u}}function Ii(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function Pi(e){return e&=-e,2<e?8<e?(e&134217727)!==0?32:268435456:8:2}function Qr(){var e=H.p;return e!==0?e:(e=window.event,e===void 0?32:Nm(e.type))}function Xg(e,t){var n=H.p;try{return H.p=e,t()}finally{H.p=n}}var dn=Math.random().toString(36).slice(2),Ie="__reactFiber$"+dn,lt="__reactProps$"+dn,dl="__reactContainer$"+dn,ec="__reactEvents$"+dn,Qg="__reactListeners$"+dn,Kg="__reactHandles$"+dn,Kr="__reactResources$"+dn,ra="__reactMarker$"+dn;function tc(e){delete e[Ie],delete e[lt],delete e[ec],delete e[Qg],delete e[Kg]}function ml(e){var t=e[Ie];if(t)return t;for(var n=e.parentNode;n;){if(t=n[dl]||n[Ie]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=fm(e);e!==null;){if(n=e[Ie])return n;e=fm(e)}return t}e=n,n=e.parentNode}return null}function hl(e){if(e=e[Ie]||e[dl]){var t=e.tag;if(t===5||t===6||t===13||t===26||t===27||t===3)return e}return null}function sa(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e.stateNode;throw Error(r(33))}function gl(e){var t=e[Kr];return t||(t=e[Kr]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function Ye(e){e[ra]=!0}var Jr=new Set,$r={};function Bn(e,t){vl(e,t),vl(e+"Capture",t)}function vl(e,t){for($r[e]=t,e=0;e<t.length;e++)Jr.add(t[e])}var Jg=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Fr={},Wr={};function $g(e){return sn.call(Wr,e)?!0:sn.call(Fr,e)?!1:Jg.test(e)?Wr[e]=!0:(Fr[e]=!0,!1)}function _u(e,t,n){if($g(t))if(n===null)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":e.removeAttribute(t);return;case"boolean":var l=t.toLowerCase().slice(0,5);if(l!=="data-"&&l!=="aria-"){e.removeAttribute(t);return}}e.setAttribute(t,""+n)}}function Su(e,t,n){if(n===null)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(t);return}e.setAttribute(t,""+n)}}function Yt(e,t,n,l){if(l===null)e.removeAttribute(n);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(n);return}e.setAttributeNS(t,n,""+l)}}var nc,Ir;function pl(e){if(nc===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);nc=t&&t[1]||"",Ir=-1<n.stack.indexOf(`
    at`)?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return`
`+nc+e+Ir}var lc=!1;function ac(e,t){if(!e||lc)return"";lc=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var l={DetermineComponentFrameRoot:function(){try{if(t){var C=function(){throw Error()};if(Object.defineProperty(C.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(C,[])}catch(A){var N=A}Reflect.construct(e,[],C)}else{try{C.call()}catch(A){N=A}e.call(C.prototype)}}else{try{throw Error()}catch(A){N=A}(C=e())&&typeof C.catch=="function"&&C.catch(function(){})}}catch(A){if(A&&N&&typeof A.stack=="string")return[A.stack,N.stack]}return[null,null]}};l.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var u=Object.getOwnPropertyDescriptor(l.DetermineComponentFrameRoot,"name");u&&u.configurable&&Object.defineProperty(l.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var c=l.DetermineComponentFrameRoot(),f=c[0],g=c[1];if(f&&g){var y=f.split(`
`),z=g.split(`
`);for(u=l=0;l<y.length&&!y[l].includes("DetermineComponentFrameRoot");)l++;for(;u<z.length&&!z[u].includes("DetermineComponentFrameRoot");)u++;if(l===y.length||u===z.length)for(l=y.length-1,u=z.length-1;1<=l&&0<=u&&y[l]!==z[u];)u--;for(;1<=l&&0<=u;l--,u--)if(y[l]!==z[u]){if(l!==1||u!==1)do if(l--,u--,0>u||y[l]!==z[u]){var M=`
`+y[l].replace(" at new "," at ");return e.displayName&&M.includes("<anonymous>")&&(M=M.replace("<anonymous>",e.displayName)),M}while(1<=l&&0<=u);break}}}finally{lc=!1,Error.prepareStackTrace=n}return(n=e?e.displayName||e.name:"")?pl(n):""}function Fg(e){switch(e.tag){case 26:case 27:case 5:return pl(e.type);case 16:return pl("Lazy");case 13:return pl("Suspense");case 19:return pl("SuspenseList");case 0:case 15:return ac(e.type,!1);case 11:return ac(e.type.render,!1);case 1:return ac(e.type,!0);case 31:return pl("Activity");default:return""}}function Pr(e){try{var t="";do t+=Fg(e),e=e.return;while(e);return t}catch(n){return`
Error generating stack: `+n.message+`
`+n.stack}}function bt(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function es(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Wg(e){var t=es(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),l=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var u=n.get,c=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return u.call(this)},set:function(f){l=""+f,c.call(this,f)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return l},setValue:function(f){l=""+f},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function wu(e){e._valueTracker||(e._valueTracker=Wg(e))}function ts(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),l="";return e&&(l=es(e)?e.checked?"true":"false":e.value),e=l,e!==n?(t.setValue(e),!0):!1}function zu(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var Ig=/[\n"\\]/g;function xt(e){return e.replace(Ig,function(t){return"\\"+t.charCodeAt(0).toString(16)+" "})}function uc(e,t,n,l,u,c,f,g){e.name="",f!=null&&typeof f!="function"&&typeof f!="symbol"&&typeof f!="boolean"?e.type=f:e.removeAttribute("type"),t!=null?f==="number"?(t===0&&e.value===""||e.value!=t)&&(e.value=""+bt(t)):e.value!==""+bt(t)&&(e.value=""+bt(t)):f!=="submit"&&f!=="reset"||e.removeAttribute("value"),t!=null?ic(e,f,bt(t)):n!=null?ic(e,f,bt(n)):l!=null&&e.removeAttribute("value"),u==null&&c!=null&&(e.defaultChecked=!!c),u!=null&&(e.checked=u&&typeof u!="function"&&typeof u!="symbol"),g!=null&&typeof g!="function"&&typeof g!="symbol"&&typeof g!="boolean"?e.name=""+bt(g):e.removeAttribute("name")}function ns(e,t,n,l,u,c,f,g){if(c!=null&&typeof c!="function"&&typeof c!="symbol"&&typeof c!="boolean"&&(e.type=c),t!=null||n!=null){if(!(c!=="submit"&&c!=="reset"||t!=null))return;n=n!=null?""+bt(n):"",t=t!=null?""+bt(t):n,g||t===e.value||(e.value=t),e.defaultValue=t}l=l??u,l=typeof l!="function"&&typeof l!="symbol"&&!!l,e.checked=g?e.checked:!!l,e.defaultChecked=!!l,f!=null&&typeof f!="function"&&typeof f!="symbol"&&typeof f!="boolean"&&(e.name=f)}function ic(e,t,n){t==="number"&&zu(e.ownerDocument)===e||e.defaultValue===""+n||(e.defaultValue=""+n)}function yl(e,t,n,l){if(e=e.options,t){t={};for(var u=0;u<n.length;u++)t["$"+n[u]]=!0;for(n=0;n<e.length;n++)u=t.hasOwnProperty("$"+e[n].value),e[n].selected!==u&&(e[n].selected=u),u&&l&&(e[n].defaultSelected=!0)}else{for(n=""+bt(n),t=null,u=0;u<e.length;u++){if(e[u].value===n){e[u].selected=!0,l&&(e[u].defaultSelected=!0);return}t!==null||e[u].disabled||(t=e[u])}t!==null&&(t.selected=!0)}}function ls(e,t,n){if(t!=null&&(t=""+bt(t),t!==e.value&&(e.value=t),n==null)){e.defaultValue!==t&&(e.defaultValue=t);return}e.defaultValue=n!=null?""+bt(n):""}function as(e,t,n,l){if(t==null){if(l!=null){if(n!=null)throw Error(r(92));if(Ge(l)){if(1<l.length)throw Error(r(93));l=l[0]}n=l}n==null&&(n=""),t=n}n=bt(t),e.defaultValue=n,l=e.textContent,l===n&&l!==""&&l!==null&&(e.value=l)}function bl(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Pg=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function us(e,t,n){var l=t.indexOf("--")===0;n==null||typeof n=="boolean"||n===""?l?e.setProperty(t,""):t==="float"?e.cssFloat="":e[t]="":l?e.setProperty(t,n):typeof n!="number"||n===0||Pg.has(t)?t==="float"?e.cssFloat=n:e[t]=(""+n).trim():e[t]=n+"px"}function is(e,t,n){if(t!=null&&typeof t!="object")throw Error(r(62));if(e=e.style,n!=null){for(var l in n)!n.hasOwnProperty(l)||t!=null&&t.hasOwnProperty(l)||(l.indexOf("--")===0?e.setProperty(l,""):l==="float"?e.cssFloat="":e[l]="");for(var u in t)l=t[u],t.hasOwnProperty(u)&&n[u]!==l&&us(e,u,l)}else for(var c in t)t.hasOwnProperty(c)&&us(e,c,t[c])}function cc(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var ev=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),tv=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Eu(e){return tv.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var oc=null;function rc(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var xl=null,_l=null;function cs(e){var t=hl(e);if(t&&(e=t.stateNode)){var n=e[lt]||null;e:switch(e=t.stateNode,t.type){case"input":if(uc(e,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+xt(""+t)+'"][type="radio"]'),t=0;t<n.length;t++){var l=n[t];if(l!==e&&l.form===e.form){var u=l[lt]||null;if(!u)throw Error(r(90));uc(l,u.value,u.defaultValue,u.defaultValue,u.checked,u.defaultChecked,u.type,u.name)}}for(t=0;t<n.length;t++)l=n[t],l.form===e.form&&ts(l)}break e;case"textarea":ls(e,n.value,n.defaultValue);break e;case"select":t=n.value,t!=null&&yl(e,!!n.multiple,t,!1)}}}var sc=!1;function os(e,t,n){if(sc)return e(t,n);sc=!0;try{var l=e(t);return l}finally{if(sc=!1,(xl!==null||_l!==null)&&(si(),xl&&(t=xl,e=_l,_l=xl=null,cs(t),e)))for(t=0;t<e.length;t++)cs(e[t])}}function fa(e,t){var n=e.stateNode;if(n===null)return null;var l=n[lt]||null;if(l===null)return null;n=l[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(l=!l.disabled)||(e=e.type,l=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!l;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(r(231,t,typeof n));return n}var Xt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),fc=!1;if(Xt)try{var da={};Object.defineProperty(da,"passive",{get:function(){fc=!0}}),window.addEventListener("test",da,da),window.removeEventListener("test",da,da)}catch{fc=!1}var mn=null,dc=null,Nu=null;function rs(){if(Nu)return Nu;var e,t=dc,n=t.length,l,u="value"in mn?mn.value:mn.textContent,c=u.length;for(e=0;e<n&&t[e]===u[e];e++);var f=n-e;for(l=1;l<=f&&t[n-l]===u[c-l];l++);return Nu=u.slice(e,1<l?1-l:void 0)}function Au(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function ju(){return!0}function ss(){return!1}function at(e){function t(n,l,u,c,f){this._reactName=n,this._targetInst=u,this.type=l,this.nativeEvent=c,this.target=f,this.currentTarget=null;for(var g in e)e.hasOwnProperty(g)&&(n=e[g],this[g]=n?n(c):c[g]);return this.isDefaultPrevented=(c.defaultPrevented!=null?c.defaultPrevented:c.returnValue===!1)?ju:ss,this.isPropagationStopped=ss,this}return x(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=ju)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=ju)},persist:function(){},isPersistent:ju}),t}var Ln={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Tu=at(Ln),ma=x({},Ln,{view:0,detail:0}),nv=at(ma),mc,hc,ha,Mu=x({},ma,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:vc,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==ha&&(ha&&e.type==="mousemove"?(mc=e.screenX-ha.screenX,hc=e.screenY-ha.screenY):hc=mc=0,ha=e),mc)},movementY:function(e){return"movementY"in e?e.movementY:hc}}),fs=at(Mu),lv=x({},Mu,{dataTransfer:0}),av=at(lv),uv=x({},ma,{relatedTarget:0}),gc=at(uv),iv=x({},Ln,{animationName:0,elapsedTime:0,pseudoElement:0}),cv=at(iv),ov=x({},Ln,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),rv=at(ov),sv=x({},Ln,{data:0}),ds=at(sv),fv={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},dv={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},mv={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function hv(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=mv[e])?!!t[e]:!1}function vc(){return hv}var gv=x({},ma,{key:function(e){if(e.key){var t=fv[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Au(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?dv[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:vc,charCode:function(e){return e.type==="keypress"?Au(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Au(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),vv=at(gv),pv=x({},Mu,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),ms=at(pv),yv=x({},ma,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:vc}),bv=at(yv),xv=x({},Ln,{propertyName:0,elapsedTime:0,pseudoElement:0}),_v=at(xv),Sv=x({},Mu,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),wv=at(Sv),zv=x({},Ln,{newState:0,oldState:0}),Ev=at(zv),Nv=[9,13,27,32],pc=Xt&&"CompositionEvent"in window,ga=null;Xt&&"documentMode"in document&&(ga=document.documentMode);var Av=Xt&&"TextEvent"in window&&!ga,hs=Xt&&(!pc||ga&&8<ga&&11>=ga),gs=" ",vs=!1;function ps(e,t){switch(e){case"keyup":return Nv.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function ys(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Sl=!1;function jv(e,t){switch(e){case"compositionend":return ys(t);case"keypress":return t.which!==32?null:(vs=!0,gs);case"textInput":return e=t.data,e===gs&&vs?null:e;default:return null}}function Tv(e,t){if(Sl)return e==="compositionend"||!pc&&ps(e,t)?(e=rs(),Nu=dc=mn=null,Sl=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return hs&&t.locale!=="ko"?null:t.data;default:return null}}var Mv={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function bs(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Mv[e.type]:t==="textarea"}function xs(e,t,n,l){xl?_l?_l.push(l):_l=[l]:xl=l,t=vi(t,"onChange"),0<t.length&&(n=new Tu("onChange","change",null,n,l),e.push({event:n,listeners:t}))}var va=null,pa=null;function Ov(e){tm(e,0)}function Ou(e){var t=sa(e);if(ts(t))return e}function _s(e,t){if(e==="change")return t}var Ss=!1;if(Xt){var yc;if(Xt){var bc="oninput"in document;if(!bc){var ws=document.createElement("div");ws.setAttribute("oninput","return;"),bc=typeof ws.oninput=="function"}yc=bc}else yc=!1;Ss=yc&&(!document.documentMode||9<document.documentMode)}function zs(){va&&(va.detachEvent("onpropertychange",Es),pa=va=null)}function Es(e){if(e.propertyName==="value"&&Ou(pa)){var t=[];xs(t,pa,e,rc(e)),os(Ov,t)}}function Rv(e,t,n){e==="focusin"?(zs(),va=t,pa=n,va.attachEvent("onpropertychange",Es)):e==="focusout"&&zs()}function Cv(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Ou(pa)}function Dv(e,t){if(e==="click")return Ou(t)}function kv(e,t){if(e==="input"||e==="change")return Ou(t)}function Uv(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var ft=typeof Object.is=="function"?Object.is:Uv;function ya(e,t){if(ft(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),l=Object.keys(t);if(n.length!==l.length)return!1;for(l=0;l<n.length;l++){var u=n[l];if(!sn.call(t,u)||!ft(e[u],t[u]))return!1}return!0}function Ns(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function As(e,t){var n=Ns(e);e=0;for(var l;n;){if(n.nodeType===3){if(l=e+n.textContent.length,e<=t&&l>=t)return{node:n,offset:t-e};e=l}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Ns(n)}}function js(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?js(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Ts(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var t=zu(e.document);t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=zu(e.document)}return t}function xc(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}var Vv=Xt&&"documentMode"in document&&11>=document.documentMode,wl=null,_c=null,ba=null,Sc=!1;function Ms(e,t,n){var l=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Sc||wl==null||wl!==zu(l)||(l=wl,"selectionStart"in l&&xc(l)?l={start:l.selectionStart,end:l.selectionEnd}:(l=(l.ownerDocument&&l.ownerDocument.defaultView||window).getSelection(),l={anchorNode:l.anchorNode,anchorOffset:l.anchorOffset,focusNode:l.focusNode,focusOffset:l.focusOffset}),ba&&ya(ba,l)||(ba=l,l=vi(_c,"onSelect"),0<l.length&&(t=new Tu("onSelect","select",null,t,n),e.push({event:t,listeners:l}),t.target=wl)))}function Gn(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var zl={animationend:Gn("Animation","AnimationEnd"),animationiteration:Gn("Animation","AnimationIteration"),animationstart:Gn("Animation","AnimationStart"),transitionrun:Gn("Transition","TransitionRun"),transitionstart:Gn("Transition","TransitionStart"),transitioncancel:Gn("Transition","TransitionCancel"),transitionend:Gn("Transition","TransitionEnd")},wc={},Os={};Xt&&(Os=document.createElement("div").style,"AnimationEvent"in window||(delete zl.animationend.animation,delete zl.animationiteration.animation,delete zl.animationstart.animation),"TransitionEvent"in window||delete zl.transitionend.transition);function qn(e){if(wc[e])return wc[e];if(!zl[e])return e;var t=zl[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Os)return wc[e]=t[n];return e}var Rs=qn("animationend"),Cs=qn("animationiteration"),Ds=qn("animationstart"),Zv=qn("transitionrun"),Hv=qn("transitionstart"),Bv=qn("transitioncancel"),ks=qn("transitionend"),Us=new Map,zc="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");zc.push("scrollEnd");function Mt(e,t){Us.set(e,t),Bn(t,[e])}var Vs=new WeakMap;function _t(e,t){if(typeof e=="object"&&e!==null){var n=Vs.get(e);return n!==void 0?n:(t={value:e,source:t,stack:Pr(t)},Vs.set(e,t),t)}return{value:e,source:t,stack:Pr(t)}}var St=[],El=0,Ec=0;function Ru(){for(var e=El,t=Ec=El=0;t<e;){var n=St[t];St[t++]=null;var l=St[t];St[t++]=null;var u=St[t];St[t++]=null;var c=St[t];if(St[t++]=null,l!==null&&u!==null){var f=l.pending;f===null?u.next=u:(u.next=f.next,f.next=u),l.pending=u}c!==0&&Zs(n,u,c)}}function Cu(e,t,n,l){St[El++]=e,St[El++]=t,St[El++]=n,St[El++]=l,Ec|=l,e.lanes|=l,e=e.alternate,e!==null&&(e.lanes|=l)}function Nc(e,t,n,l){return Cu(e,t,n,l),Du(e)}function Nl(e,t){return Cu(e,null,null,t),Du(e)}function Zs(e,t,n){e.lanes|=n;var l=e.alternate;l!==null&&(l.lanes|=n);for(var u=!1,c=e.return;c!==null;)c.childLanes|=n,l=c.alternate,l!==null&&(l.childLanes|=n),c.tag===22&&(e=c.stateNode,e===null||e._visibility&1||(u=!0)),e=c,c=c.return;return e.tag===3?(c=e.stateNode,u&&t!==null&&(u=31-st(n),e=c.hiddenUpdates,l=e[u],l===null?e[u]=[t]:l.push(t),t.lane=n|536870912),c):null}function Du(e){if(50<Ya)throw Ya=0,Co=null,Error(r(185));for(var t=e.return;t!==null;)e=t,t=e.return;return e.tag===3?e.stateNode:null}var Al={};function Lv(e,t,n,l){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=l,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function dt(e,t,n,l){return new Lv(e,t,n,l)}function Ac(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Qt(e,t){var n=e.alternate;return n===null?(n=dt(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&65011712,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n.refCleanup=e.refCleanup,n}function Hs(e,t){e.flags&=65011714;var n=e.alternate;return n===null?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=n.childLanes,e.lanes=n.lanes,e.child=n.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=n.memoizedProps,e.memoizedState=n.memoizedState,e.updateQueue=n.updateQueue,e.type=n.type,t=n.dependencies,e.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function ku(e,t,n,l,u,c){var f=0;if(l=e,typeof e=="function")Ac(e)&&(f=1);else if(typeof e=="string")f=qp(e,n,se.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case ze:return e=dt(31,n,t,u),e.elementType=ze,e.lanes=c,e;case B:return Yn(n.children,u,c,t);case L:f=8,u|=24;break;case G:return e=dt(12,n,t,u|2),e.elementType=G,e.lanes=c,e;case ee:return e=dt(13,n,t,u),e.elementType=ee,e.lanes=c,e;case J:return e=dt(19,n,t,u),e.elementType=J,e.lanes=c,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Q:case R:f=10;break e;case F:f=9;break e;case Z:f=11;break e;case oe:f=14;break e;case V:f=16,l=null;break e}f=29,n=Error(r(130,e===null?"null":typeof e,"")),l=null}return t=dt(f,n,t,u),t.elementType=e,t.type=l,t.lanes=c,t}function Yn(e,t,n,l){return e=dt(7,e,l,t),e.lanes=n,e}function jc(e,t,n){return e=dt(6,e,null,t),e.lanes=n,e}function Tc(e,t,n){return t=dt(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var jl=[],Tl=0,Uu=null,Vu=0,wt=[],zt=0,Xn=null,Kt=1,Jt="";function Qn(e,t){jl[Tl++]=Vu,jl[Tl++]=Uu,Uu=e,Vu=t}function Bs(e,t,n){wt[zt++]=Kt,wt[zt++]=Jt,wt[zt++]=Xn,Xn=e;var l=Kt;e=Jt;var u=32-st(l)-1;l&=~(1<<u),n+=1;var c=32-st(t)+u;if(30<c){var f=u-u%5;c=(l&(1<<f)-1).toString(32),l>>=f,u-=f,Kt=1<<32-st(t)+u|n<<u|l,Jt=c+e}else Kt=1<<c|n<<u|l,Jt=e}function Mc(e){e.return!==null&&(Qn(e,1),Bs(e,1,0))}function Oc(e){for(;e===Uu;)Uu=jl[--Tl],jl[Tl]=null,Vu=jl[--Tl],jl[Tl]=null;for(;e===Xn;)Xn=wt[--zt],wt[zt]=null,Jt=wt[--zt],wt[zt]=null,Kt=wt[--zt],wt[zt]=null}var tt=null,je=null,de=!1,Kn=null,Ut=!1,Rc=Error(r(519));function Jn(e){var t=Error(r(418,""));throw Sa(_t(t,e)),Rc}function Ls(e){var t=e.stateNode,n=e.type,l=e.memoizedProps;switch(t[Ie]=e,t[lt]=l,n){case"dialog":ie("cancel",t),ie("close",t);break;case"iframe":case"object":case"embed":ie("load",t);break;case"video":case"audio":for(n=0;n<Qa.length;n++)ie(Qa[n],t);break;case"source":ie("error",t);break;case"img":case"image":case"link":ie("error",t),ie("load",t);break;case"details":ie("toggle",t);break;case"input":ie("invalid",t),ns(t,l.value,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name,!0),wu(t);break;case"select":ie("invalid",t);break;case"textarea":ie("invalid",t),as(t,l.value,l.defaultValue,l.children),wu(t)}n=l.children,typeof n!="string"&&typeof n!="number"&&typeof n!="bigint"||t.textContent===""+n||l.suppressHydrationWarning===!0||um(t.textContent,n)?(l.popover!=null&&(ie("beforetoggle",t),ie("toggle",t)),l.onScroll!=null&&ie("scroll",t),l.onScrollEnd!=null&&ie("scrollend",t),l.onClick!=null&&(t.onclick=pi),t=!0):t=!1,t||Jn(e)}function Gs(e){for(tt=e.return;tt;)switch(tt.tag){case 5:case 13:Ut=!1;return;case 27:case 3:Ut=!0;return;default:tt=tt.return}}function xa(e){if(e!==tt)return!1;if(!de)return Gs(e),de=!0,!1;var t=e.tag,n;if((n=t!==3&&t!==27)&&((n=t===5)&&(n=e.type,n=!(n!=="form"&&n!=="button")||$o(e.type,e.memoizedProps)),n=!n),n&&je&&Jn(e),Gs(e),t===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(r(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8)if(n=e.data,n==="/$"){if(t===0){je=Rt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++;e=e.nextSibling}je=null}}else t===27?(t=je,Tn(e.type)?(e=Po,Po=null,je=e):je=t):je=tt?Rt(e.stateNode.nextSibling):null;return!0}function _a(){je=tt=null,de=!1}function qs(){var e=Kn;return e!==null&&(ct===null?ct=e:ct.push.apply(ct,e),Kn=null),e}function Sa(e){Kn===null?Kn=[e]:Kn.push(e)}var Cc=qe(null),$n=null,$t=null;function hn(e,t,n){te(Cc,t._currentValue),t._currentValue=n}function Ft(e){e._currentValue=Cc.current,xe(Cc)}function Dc(e,t,n){for(;e!==null;){var l=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,l!==null&&(l.childLanes|=t)):l!==null&&(l.childLanes&t)!==t&&(l.childLanes|=t),e===n)break;e=e.return}}function kc(e,t,n,l){var u=e.child;for(u!==null&&(u.return=e);u!==null;){var c=u.dependencies;if(c!==null){var f=u.child;c=c.firstContext;e:for(;c!==null;){var g=c;c=u;for(var y=0;y<t.length;y++)if(g.context===t[y]){c.lanes|=n,g=c.alternate,g!==null&&(g.lanes|=n),Dc(c.return,n,e),l||(f=null);break e}c=g.next}}else if(u.tag===18){if(f=u.return,f===null)throw Error(r(341));f.lanes|=n,c=f.alternate,c!==null&&(c.lanes|=n),Dc(f,n,e),f=null}else f=u.child;if(f!==null)f.return=u;else for(f=u;f!==null;){if(f===e){f=null;break}if(u=f.sibling,u!==null){u.return=f.return,f=u;break}f=f.return}u=f}}function wa(e,t,n,l){e=null;for(var u=t,c=!1;u!==null;){if(!c){if((u.flags&524288)!==0)c=!0;else if((u.flags&262144)!==0)break}if(u.tag===10){var f=u.alternate;if(f===null)throw Error(r(387));if(f=f.memoizedProps,f!==null){var g=u.type;ft(u.pendingProps.value,f.value)||(e!==null?e.push(g):e=[g])}}else if(u===Ne.current){if(f=u.alternate,f===null)throw Error(r(387));f.memoizedState.memoizedState!==u.memoizedState.memoizedState&&(e!==null?e.push(Ia):e=[Ia])}u=u.return}e!==null&&kc(t,e,n,l),t.flags|=262144}function Zu(e){for(e=e.firstContext;e!==null;){if(!ft(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function Fn(e){$n=e,$t=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function Pe(e){return Ys($n,e)}function Hu(e,t){return $n===null&&Fn(e),Ys(e,t)}function Ys(e,t){var n=t._currentValue;if(t={context:t,memoizedValue:n,next:null},$t===null){if(e===null)throw Error(r(308));$t=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else $t=$t.next=t;return n}var Gv=typeof AbortController<"u"?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(n,l){e.push(l)}};this.abort=function(){t.aborted=!0,e.forEach(function(n){return n()})}},qv=a.unstable_scheduleCallback,Yv=a.unstable_NormalPriority,Ve={$$typeof:R,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Uc(){return{controller:new Gv,data:new Map,refCount:0}}function za(e){e.refCount--,e.refCount===0&&qv(Yv,function(){e.controller.abort()})}var Ea=null,Vc=0,Ml=0,Ol=null;function Xv(e,t){if(Ea===null){var n=Ea=[];Vc=0,Ml=Bo(),Ol={status:"pending",value:void 0,then:function(l){n.push(l)}}}return Vc++,t.then(Xs,Xs),t}function Xs(){if(--Vc===0&&Ea!==null){Ol!==null&&(Ol.status="fulfilled");var e=Ea;Ea=null,Ml=0,Ol=null;for(var t=0;t<e.length;t++)(0,e[t])()}}function Qv(e,t){var n=[],l={status:"pending",value:null,reason:null,then:function(u){n.push(u)}};return e.then(function(){l.status="fulfilled",l.value=t;for(var u=0;u<n.length;u++)(0,n[u])(t)},function(u){for(l.status="rejected",l.reason=u,u=0;u<n.length;u++)(0,n[u])(void 0)}),l}var Qs=T.S;T.S=function(e,t){typeof t=="object"&&t!==null&&typeof t.then=="function"&&Xv(e,t),Qs!==null&&Qs(e,t)};var Wn=qe(null);function Zc(){var e=Wn.current;return e!==null?e:Se.pooledCache}function Bu(e,t){t===null?te(Wn,Wn.current):te(Wn,t.pool)}function Ks(){var e=Zc();return e===null?null:{parent:Ve._currentValue,pool:e}}var Na=Error(r(460)),Js=Error(r(474)),Lu=Error(r(542)),Hc={then:function(){}};function $s(e){return e=e.status,e==="fulfilled"||e==="rejected"}function Gu(){}function Fs(e,t,n){switch(n=e[n],n===void 0?e.push(t):n!==t&&(t.then(Gu,Gu),t=n),t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Is(e),e;default:if(typeof t.status=="string")t.then(Gu,Gu);else{if(e=Se,e!==null&&100<e.shellSuspendCounter)throw Error(r(482));e=t,e.status="pending",e.then(function(l){if(t.status==="pending"){var u=t;u.status="fulfilled",u.value=l}},function(l){if(t.status==="pending"){var u=t;u.status="rejected",u.reason=l}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Is(e),e}throw Aa=t,Na}}var Aa=null;function Ws(){if(Aa===null)throw Error(r(459));var e=Aa;return Aa=null,e}function Is(e){if(e===Na||e===Lu)throw Error(r(483))}var gn=!1;function Bc(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Lc(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function vn(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function pn(e,t,n){var l=e.updateQueue;if(l===null)return null;if(l=l.shared,(me&2)!==0){var u=l.pending;return u===null?t.next=t:(t.next=u.next,u.next=t),l.pending=t,t=Du(e),Zs(e,null,n),t}return Cu(e,l,t,n),Du(e)}function ja(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194048)!==0)){var l=t.lanes;l&=e.pendingLanes,n|=l,t.lanes=n,Xr(e,n)}}function Gc(e,t){var n=e.updateQueue,l=e.alternate;if(l!==null&&(l=l.updateQueue,n===l)){var u=null,c=null;if(n=n.firstBaseUpdate,n!==null){do{var f={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};c===null?u=c=f:c=c.next=f,n=n.next}while(n!==null);c===null?u=c=t:c=c.next=t}else u=c=t;n={baseState:l.baseState,firstBaseUpdate:u,lastBaseUpdate:c,shared:l.shared,callbacks:l.callbacks},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}var qc=!1;function Ta(){if(qc){var e=Ol;if(e!==null)throw e}}function Ma(e,t,n,l){qc=!1;var u=e.updateQueue;gn=!1;var c=u.firstBaseUpdate,f=u.lastBaseUpdate,g=u.shared.pending;if(g!==null){u.shared.pending=null;var y=g,z=y.next;y.next=null,f===null?c=z:f.next=z,f=y;var M=e.alternate;M!==null&&(M=M.updateQueue,g=M.lastBaseUpdate,g!==f&&(g===null?M.firstBaseUpdate=z:g.next=z,M.lastBaseUpdate=y))}if(c!==null){var C=u.baseState;f=0,M=z=y=null,g=c;do{var N=g.lane&-536870913,A=N!==g.lane;if(A?(ce&N)===N:(l&N)===N){N!==0&&N===Ml&&(qc=!0),M!==null&&(M=M.next={lane:0,tag:g.tag,payload:g.payload,callback:null,next:null});e:{var P=e,W=g;N=t;var be=n;switch(W.tag){case 1:if(P=W.payload,typeof P=="function"){C=P.call(be,C,N);break e}C=P;break e;case 3:P.flags=P.flags&-65537|128;case 0:if(P=W.payload,N=typeof P=="function"?P.call(be,C,N):P,N==null)break e;C=x({},C,N);break e;case 2:gn=!0}}N=g.callback,N!==null&&(e.flags|=64,A&&(e.flags|=8192),A=u.callbacks,A===null?u.callbacks=[N]:A.push(N))}else A={lane:N,tag:g.tag,payload:g.payload,callback:g.callback,next:null},M===null?(z=M=A,y=C):M=M.next=A,f|=N;if(g=g.next,g===null){if(g=u.shared.pending,g===null)break;A=g,g=A.next,A.next=null,u.lastBaseUpdate=A,u.shared.pending=null}}while(!0);M===null&&(y=C),u.baseState=y,u.firstBaseUpdate=z,u.lastBaseUpdate=M,c===null&&(u.shared.lanes=0),En|=f,e.lanes=f,e.memoizedState=C}}function Ps(e,t){if(typeof e!="function")throw Error(r(191,e));e.call(t)}function ef(e,t){var n=e.callbacks;if(n!==null)for(e.callbacks=null,e=0;e<n.length;e++)Ps(n[e],t)}var Rl=qe(null),qu=qe(0);function tf(e,t){e=ln,te(qu,e),te(Rl,t),ln=e|t.baseLanes}function Yc(){te(qu,ln),te(Rl,Rl.current)}function Xc(){ln=qu.current,xe(Rl),xe(qu)}var yn=0,ne=null,pe=null,Ce=null,Yu=!1,Cl=!1,In=!1,Xu=0,Oa=0,Dl=null,Kv=0;function Me(){throw Error(r(321))}function Qc(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!ft(e[n],t[n]))return!1;return!0}function Kc(e,t,n,l,u,c){return yn=c,ne=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,T.H=e===null||e.memoizedState===null?Hf:Bf,In=!1,c=n(l,u),In=!1,Cl&&(c=lf(t,n,l,u)),nf(e),c}function nf(e){T.H=Wu;var t=pe!==null&&pe.next!==null;if(yn=0,Ce=pe=ne=null,Yu=!1,Oa=0,Dl=null,t)throw Error(r(300));e===null||Xe||(e=e.dependencies,e!==null&&Zu(e)&&(Xe=!0))}function lf(e,t,n,l){ne=e;var u=0;do{if(Cl&&(Dl=null),Oa=0,Cl=!1,25<=u)throw Error(r(301));if(u+=1,Ce=pe=null,e.updateQueue!=null){var c=e.updateQueue;c.lastEffect=null,c.events=null,c.stores=null,c.memoCache!=null&&(c.memoCache.index=0)}T.H=ep,c=t(n,l)}while(Cl);return c}function Jv(){var e=T.H,t=e.useState()[0];return t=typeof t.then=="function"?Ra(t):t,e=e.useState()[0],(pe!==null?pe.memoizedState:null)!==e&&(ne.flags|=1024),t}function Jc(){var e=Xu!==0;return Xu=0,e}function $c(e,t,n){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~n}function Fc(e){if(Yu){for(e=e.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}Yu=!1}yn=0,Ce=pe=ne=null,Cl=!1,Oa=Xu=0,Dl=null}function ut(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Ce===null?ne.memoizedState=Ce=e:Ce=Ce.next=e,Ce}function De(){if(pe===null){var e=ne.alternate;e=e!==null?e.memoizedState:null}else e=pe.next;var t=Ce===null?ne.memoizedState:Ce.next;if(t!==null)Ce=t,pe=e;else{if(e===null)throw ne.alternate===null?Error(r(467)):Error(r(310));pe=e,e={memoizedState:pe.memoizedState,baseState:pe.baseState,baseQueue:pe.baseQueue,queue:pe.queue,next:null},Ce===null?ne.memoizedState=Ce=e:Ce=Ce.next=e}return Ce}function Wc(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function Ra(e){var t=Oa;return Oa+=1,Dl===null&&(Dl=[]),e=Fs(Dl,e,t),t=ne,(Ce===null?t.memoizedState:Ce.next)===null&&(t=t.alternate,T.H=t===null||t.memoizedState===null?Hf:Bf),e}function Qu(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return Ra(e);if(e.$$typeof===R)return Pe(e)}throw Error(r(438,String(e)))}function Ic(e){var t=null,n=ne.updateQueue;if(n!==null&&(t=n.memoCache),t==null){var l=ne.alternate;l!==null&&(l=l.updateQueue,l!==null&&(l=l.memoCache,l!=null&&(t={data:l.data.map(function(u){return u.slice()}),index:0})))}if(t==null&&(t={data:[],index:0}),n===null&&(n=Wc(),ne.updateQueue=n),n.memoCache=t,n=t.data[t.index],n===void 0)for(n=t.data[t.index]=Array(e),l=0;l<e;l++)n[l]=Re;return t.index++,n}function Wt(e,t){return typeof t=="function"?t(e):t}function Ku(e){var t=De();return Pc(t,pe,e)}function Pc(e,t,n){var l=e.queue;if(l===null)throw Error(r(311));l.lastRenderedReducer=n;var u=e.baseQueue,c=l.pending;if(c!==null){if(u!==null){var f=u.next;u.next=c.next,c.next=f}t.baseQueue=u=c,l.pending=null}if(c=e.baseState,u===null)e.memoizedState=c;else{t=u.next;var g=f=null,y=null,z=t,M=!1;do{var C=z.lane&-536870913;if(C!==z.lane?(ce&C)===C:(yn&C)===C){var N=z.revertLane;if(N===0)y!==null&&(y=y.next={lane:0,revertLane:0,action:z.action,hasEagerState:z.hasEagerState,eagerState:z.eagerState,next:null}),C===Ml&&(M=!0);else if((yn&N)===N){z=z.next,N===Ml&&(M=!0);continue}else C={lane:0,revertLane:z.revertLane,action:z.action,hasEagerState:z.hasEagerState,eagerState:z.eagerState,next:null},y===null?(g=y=C,f=c):y=y.next=C,ne.lanes|=N,En|=N;C=z.action,In&&n(c,C),c=z.hasEagerState?z.eagerState:n(c,C)}else N={lane:C,revertLane:z.revertLane,action:z.action,hasEagerState:z.hasEagerState,eagerState:z.eagerState,next:null},y===null?(g=y=N,f=c):y=y.next=N,ne.lanes|=C,En|=C;z=z.next}while(z!==null&&z!==t);if(y===null?f=c:y.next=g,!ft(c,e.memoizedState)&&(Xe=!0,M&&(n=Ol,n!==null)))throw n;e.memoizedState=c,e.baseState=f,e.baseQueue=y,l.lastRenderedState=c}return u===null&&(l.lanes=0),[e.memoizedState,l.dispatch]}function eo(e){var t=De(),n=t.queue;if(n===null)throw Error(r(311));n.lastRenderedReducer=e;var l=n.dispatch,u=n.pending,c=t.memoizedState;if(u!==null){n.pending=null;var f=u=u.next;do c=e(c,f.action),f=f.next;while(f!==u);ft(c,t.memoizedState)||(Xe=!0),t.memoizedState=c,t.baseQueue===null&&(t.baseState=c),n.lastRenderedState=c}return[c,l]}function af(e,t,n){var l=ne,u=De(),c=de;if(c){if(n===void 0)throw Error(r(407));n=n()}else n=t();var f=!ft((pe||u).memoizedState,n);f&&(u.memoizedState=n,Xe=!0),u=u.queue;var g=of.bind(null,l,u,e);if(Ca(2048,8,g,[e]),u.getSnapshot!==t||f||Ce!==null&&Ce.memoizedState.tag&1){if(l.flags|=2048,kl(9,Ju(),cf.bind(null,l,u,n,t),null),Se===null)throw Error(r(349));c||(yn&124)!==0||uf(l,t,n)}return n}function uf(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=ne.updateQueue,t===null?(t=Wc(),ne.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function cf(e,t,n,l){t.value=n,t.getSnapshot=l,rf(t)&&sf(e)}function of(e,t,n){return n(function(){rf(t)&&sf(e)})}function rf(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!ft(e,n)}catch{return!0}}function sf(e){var t=Nl(e,2);t!==null&&pt(t,e,2)}function to(e){var t=ut();if(typeof e=="function"){var n=e;if(e=n(),In){fn(!0);try{n()}finally{fn(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Wt,lastRenderedState:e},t}function ff(e,t,n,l){return e.baseState=n,Pc(e,pe,typeof l=="function"?l:Wt)}function $v(e,t,n,l,u){if(Fu(e))throw Error(r(485));if(e=t.action,e!==null){var c={payload:u,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(f){c.listeners.push(f)}};T.T!==null?n(!0):c.isTransition=!1,l(c),n=t.pending,n===null?(c.next=t.pending=c,df(t,c)):(c.next=n.next,t.pending=n.next=c)}}function df(e,t){var n=t.action,l=t.payload,u=e.state;if(t.isTransition){var c=T.T,f={};T.T=f;try{var g=n(u,l),y=T.S;y!==null&&y(f,g),mf(e,t,g)}catch(z){no(e,t,z)}finally{T.T=c}}else try{c=n(u,l),mf(e,t,c)}catch(z){no(e,t,z)}}function mf(e,t,n){n!==null&&typeof n=="object"&&typeof n.then=="function"?n.then(function(l){hf(e,t,l)},function(l){return no(e,t,l)}):hf(e,t,n)}function hf(e,t,n){t.status="fulfilled",t.value=n,gf(t),e.state=n,t=e.pending,t!==null&&(n=t.next,n===t?e.pending=null:(n=n.next,t.next=n,df(e,n)))}function no(e,t,n){var l=e.pending;if(e.pending=null,l!==null){l=l.next;do t.status="rejected",t.reason=n,gf(t),t=t.next;while(t!==l)}e.action=null}function gf(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function vf(e,t){return t}function pf(e,t){if(de){var n=Se.formState;if(n!==null){e:{var l=ne;if(de){if(je){t:{for(var u=je,c=Ut;u.nodeType!==8;){if(!c){u=null;break t}if(u=Rt(u.nextSibling),u===null){u=null;break t}}c=u.data,u=c==="F!"||c==="F"?u:null}if(u){je=Rt(u.nextSibling),l=u.data==="F!";break e}}Jn(l)}l=!1}l&&(t=n[0])}}return n=ut(),n.memoizedState=n.baseState=t,l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:vf,lastRenderedState:t},n.queue=l,n=Uf.bind(null,ne,l),l.dispatch=n,l=to(!1),c=co.bind(null,ne,!1,l.queue),l=ut(),u={state:t,dispatch:null,action:e,pending:null},l.queue=u,n=$v.bind(null,ne,u,c,n),u.dispatch=n,l.memoizedState=e,[t,n,!1]}function yf(e){var t=De();return bf(t,pe,e)}function bf(e,t,n){if(t=Pc(e,t,vf)[0],e=Ku(Wt)[0],typeof t=="object"&&t!==null&&typeof t.then=="function")try{var l=Ra(t)}catch(f){throw f===Na?Lu:f}else l=t;t=De();var u=t.queue,c=u.dispatch;return n!==t.memoizedState&&(ne.flags|=2048,kl(9,Ju(),Fv.bind(null,u,n),null)),[l,c,e]}function Fv(e,t){e.action=t}function xf(e){var t=De(),n=pe;if(n!==null)return bf(t,n,e);De(),t=t.memoizedState,n=De();var l=n.queue.dispatch;return n.memoizedState=e,[t,l,!1]}function kl(e,t,n,l){return e={tag:e,create:n,deps:l,inst:t,next:null},t=ne.updateQueue,t===null&&(t=Wc(),ne.updateQueue=t),n=t.lastEffect,n===null?t.lastEffect=e.next=e:(l=n.next,n.next=e,e.next=l,t.lastEffect=e),e}function Ju(){return{destroy:void 0,resource:void 0}}function _f(){return De().memoizedState}function $u(e,t,n,l){var u=ut();l=l===void 0?null:l,ne.flags|=e,u.memoizedState=kl(1|t,Ju(),n,l)}function Ca(e,t,n,l){var u=De();l=l===void 0?null:l;var c=u.memoizedState.inst;pe!==null&&l!==null&&Qc(l,pe.memoizedState.deps)?u.memoizedState=kl(t,c,n,l):(ne.flags|=e,u.memoizedState=kl(1|t,c,n,l))}function Sf(e,t){$u(8390656,8,e,t)}function wf(e,t){Ca(2048,8,e,t)}function zf(e,t){return Ca(4,2,e,t)}function Ef(e,t){return Ca(4,4,e,t)}function Nf(e,t){if(typeof t=="function"){e=e();var n=t(e);return function(){typeof n=="function"?n():t(null)}}if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Af(e,t,n){n=n!=null?n.concat([e]):null,Ca(4,4,Nf.bind(null,t,e),n)}function lo(){}function jf(e,t){var n=De();t=t===void 0?null:t;var l=n.memoizedState;return t!==null&&Qc(t,l[1])?l[0]:(n.memoizedState=[e,t],e)}function Tf(e,t){var n=De();t=t===void 0?null:t;var l=n.memoizedState;if(t!==null&&Qc(t,l[1]))return l[0];if(l=e(),In){fn(!0);try{e()}finally{fn(!1)}}return n.memoizedState=[l,t],l}function ao(e,t,n){return n===void 0||(yn&1073741824)!==0?e.memoizedState=t:(e.memoizedState=n,e=Rd(),ne.lanes|=e,En|=e,n)}function Mf(e,t,n,l){return ft(n,t)?n:Rl.current!==null?(e=ao(e,n,l),ft(e,t)||(Xe=!0),e):(yn&42)===0?(Xe=!0,e.memoizedState=n):(e=Rd(),ne.lanes|=e,En|=e,t)}function Of(e,t,n,l,u){var c=H.p;H.p=c!==0&&8>c?c:8;var f=T.T,g={};T.T=g,co(e,!1,t,n);try{var y=u(),z=T.S;if(z!==null&&z(g,y),y!==null&&typeof y=="object"&&typeof y.then=="function"){var M=Qv(y,l);Da(e,t,M,vt(e))}else Da(e,t,l,vt(e))}catch(C){Da(e,t,{then:function(){},status:"rejected",reason:C},vt())}finally{H.p=c,T.T=f}}function Wv(){}function uo(e,t,n,l){if(e.tag!==5)throw Error(r(476));var u=Rf(e).queue;Of(e,u,t,U,n===null?Wv:function(){return Cf(e),n(l)})}function Rf(e){var t=e.memoizedState;if(t!==null)return t;t={memoizedState:U,baseState:U,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Wt,lastRenderedState:U},next:null};var n={};return t.next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Wt,lastRenderedState:n},next:null},e.memoizedState=t,e=e.alternate,e!==null&&(e.memoizedState=t),t}function Cf(e){var t=Rf(e).next.queue;Da(e,t,{},vt())}function io(){return Pe(Ia)}function Df(){return De().memoizedState}function kf(){return De().memoizedState}function Iv(e){for(var t=e.return;t!==null;){switch(t.tag){case 24:case 3:var n=vt();e=vn(n);var l=pn(t,e,n);l!==null&&(pt(l,t,n),ja(l,t,n)),t={cache:Uc()},e.payload=t;return}t=t.return}}function Pv(e,t,n){var l=vt();n={lane:l,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},Fu(e)?Vf(t,n):(n=Nc(e,t,n,l),n!==null&&(pt(n,e,l),Zf(n,t,l)))}function Uf(e,t,n){var l=vt();Da(e,t,n,l)}function Da(e,t,n,l){var u={lane:l,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(Fu(e))Vf(t,u);else{var c=e.alternate;if(e.lanes===0&&(c===null||c.lanes===0)&&(c=t.lastRenderedReducer,c!==null))try{var f=t.lastRenderedState,g=c(f,n);if(u.hasEagerState=!0,u.eagerState=g,ft(g,f))return Cu(e,t,u,0),Se===null&&Ru(),!1}catch{}finally{}if(n=Nc(e,t,u,l),n!==null)return pt(n,e,l),Zf(n,t,l),!0}return!1}function co(e,t,n,l){if(l={lane:2,revertLane:Bo(),action:l,hasEagerState:!1,eagerState:null,next:null},Fu(e)){if(t)throw Error(r(479))}else t=Nc(e,n,l,2),t!==null&&pt(t,e,2)}function Fu(e){var t=e.alternate;return e===ne||t!==null&&t===ne}function Vf(e,t){Cl=Yu=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Zf(e,t,n){if((n&4194048)!==0){var l=t.lanes;l&=e.pendingLanes,n|=l,t.lanes=n,Xr(e,n)}}var Wu={readContext:Pe,use:Qu,useCallback:Me,useContext:Me,useEffect:Me,useImperativeHandle:Me,useLayoutEffect:Me,useInsertionEffect:Me,useMemo:Me,useReducer:Me,useRef:Me,useState:Me,useDebugValue:Me,useDeferredValue:Me,useTransition:Me,useSyncExternalStore:Me,useId:Me,useHostTransitionStatus:Me,useFormState:Me,useActionState:Me,useOptimistic:Me,useMemoCache:Me,useCacheRefresh:Me},Hf={readContext:Pe,use:Qu,useCallback:function(e,t){return ut().memoizedState=[e,t===void 0?null:t],e},useContext:Pe,useEffect:Sf,useImperativeHandle:function(e,t,n){n=n!=null?n.concat([e]):null,$u(4194308,4,Nf.bind(null,t,e),n)},useLayoutEffect:function(e,t){return $u(4194308,4,e,t)},useInsertionEffect:function(e,t){$u(4,2,e,t)},useMemo:function(e,t){var n=ut();t=t===void 0?null:t;var l=e();if(In){fn(!0);try{e()}finally{fn(!1)}}return n.memoizedState=[l,t],l},useReducer:function(e,t,n){var l=ut();if(n!==void 0){var u=n(t);if(In){fn(!0);try{n(t)}finally{fn(!1)}}}else u=t;return l.memoizedState=l.baseState=u,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:u},l.queue=e,e=e.dispatch=Pv.bind(null,ne,e),[l.memoizedState,e]},useRef:function(e){var t=ut();return e={current:e},t.memoizedState=e},useState:function(e){e=to(e);var t=e.queue,n=Uf.bind(null,ne,t);return t.dispatch=n,[e.memoizedState,n]},useDebugValue:lo,useDeferredValue:function(e,t){var n=ut();return ao(n,e,t)},useTransition:function(){var e=to(!1);return e=Of.bind(null,ne,e.queue,!0,!1),ut().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,n){var l=ne,u=ut();if(de){if(n===void 0)throw Error(r(407));n=n()}else{if(n=t(),Se===null)throw Error(r(349));(ce&124)!==0||uf(l,t,n)}u.memoizedState=n;var c={value:n,getSnapshot:t};return u.queue=c,Sf(of.bind(null,l,c,e),[e]),l.flags|=2048,kl(9,Ju(),cf.bind(null,l,c,n,t),null),n},useId:function(){var e=ut(),t=Se.identifierPrefix;if(de){var n=Jt,l=Kt;n=(l&~(1<<32-st(l)-1)).toString(32)+n,t="«"+t+"R"+n,n=Xu++,0<n&&(t+="H"+n.toString(32)),t+="»"}else n=Kv++,t="«"+t+"r"+n.toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:io,useFormState:pf,useActionState:pf,useOptimistic:function(e){var t=ut();t.memoizedState=t.baseState=e;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=n,t=co.bind(null,ne,!0,n),n.dispatch=t,[e,t]},useMemoCache:Ic,useCacheRefresh:function(){return ut().memoizedState=Iv.bind(null,ne)}},Bf={readContext:Pe,use:Qu,useCallback:jf,useContext:Pe,useEffect:wf,useImperativeHandle:Af,useInsertionEffect:zf,useLayoutEffect:Ef,useMemo:Tf,useReducer:Ku,useRef:_f,useState:function(){return Ku(Wt)},useDebugValue:lo,useDeferredValue:function(e,t){var n=De();return Mf(n,pe.memoizedState,e,t)},useTransition:function(){var e=Ku(Wt)[0],t=De().memoizedState;return[typeof e=="boolean"?e:Ra(e),t]},useSyncExternalStore:af,useId:Df,useHostTransitionStatus:io,useFormState:yf,useActionState:yf,useOptimistic:function(e,t){var n=De();return ff(n,pe,e,t)},useMemoCache:Ic,useCacheRefresh:kf},ep={readContext:Pe,use:Qu,useCallback:jf,useContext:Pe,useEffect:wf,useImperativeHandle:Af,useInsertionEffect:zf,useLayoutEffect:Ef,useMemo:Tf,useReducer:eo,useRef:_f,useState:function(){return eo(Wt)},useDebugValue:lo,useDeferredValue:function(e,t){var n=De();return pe===null?ao(n,e,t):Mf(n,pe.memoizedState,e,t)},useTransition:function(){var e=eo(Wt)[0],t=De().memoizedState;return[typeof e=="boolean"?e:Ra(e),t]},useSyncExternalStore:af,useId:Df,useHostTransitionStatus:io,useFormState:xf,useActionState:xf,useOptimistic:function(e,t){var n=De();return pe!==null?ff(n,pe,e,t):(n.baseState=e,[e,n.queue.dispatch])},useMemoCache:Ic,useCacheRefresh:kf},Ul=null,ka=0;function Iu(e){var t=ka;return ka+=1,Ul===null&&(Ul=[]),Fs(Ul,e,t)}function Ua(e,t){t=t.props.ref,e.ref=t!==void 0?t:null}function Pu(e,t){throw t.$$typeof===E?Error(r(525)):(e=Object.prototype.toString.call(t),Error(r(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)))}function Lf(e){var t=e._init;return t(e._payload)}function Gf(e){function t(S,_){if(e){var w=S.deletions;w===null?(S.deletions=[_],S.flags|=16):w.push(_)}}function n(S,_){if(!e)return null;for(;_!==null;)t(S,_),_=_.sibling;return null}function l(S){for(var _=new Map;S!==null;)S.key!==null?_.set(S.key,S):_.set(S.index,S),S=S.sibling;return _}function u(S,_){return S=Qt(S,_),S.index=0,S.sibling=null,S}function c(S,_,w){return S.index=w,e?(w=S.alternate,w!==null?(w=w.index,w<_?(S.flags|=67108866,_):w):(S.flags|=67108866,_)):(S.flags|=1048576,_)}function f(S){return e&&S.alternate===null&&(S.flags|=67108866),S}function g(S,_,w,O){return _===null||_.tag!==6?(_=jc(w,S.mode,O),_.return=S,_):(_=u(_,w),_.return=S,_)}function y(S,_,w,O){var q=w.type;return q===B?M(S,_,w.props.children,O,w.key):_!==null&&(_.elementType===q||typeof q=="object"&&q!==null&&q.$$typeof===V&&Lf(q)===_.type)?(_=u(_,w.props),Ua(_,w),_.return=S,_):(_=ku(w.type,w.key,w.props,null,S.mode,O),Ua(_,w),_.return=S,_)}function z(S,_,w,O){return _===null||_.tag!==4||_.stateNode.containerInfo!==w.containerInfo||_.stateNode.implementation!==w.implementation?(_=Tc(w,S.mode,O),_.return=S,_):(_=u(_,w.children||[]),_.return=S,_)}function M(S,_,w,O,q){return _===null||_.tag!==7?(_=Yn(w,S.mode,O,q),_.return=S,_):(_=u(_,w),_.return=S,_)}function C(S,_,w){if(typeof _=="string"&&_!==""||typeof _=="number"||typeof _=="bigint")return _=jc(""+_,S.mode,w),_.return=S,_;if(typeof _=="object"&&_!==null){switch(_.$$typeof){case j:return w=ku(_.type,_.key,_.props,null,S.mode,w),Ua(w,_),w.return=S,w;case D:return _=Tc(_,S.mode,w),_.return=S,_;case V:var O=_._init;return _=O(_._payload),C(S,_,w)}if(Ge(_)||Ue(_))return _=Yn(_,S.mode,w,null),_.return=S,_;if(typeof _.then=="function")return C(S,Iu(_),w);if(_.$$typeof===R)return C(S,Hu(S,_),w);Pu(S,_)}return null}function N(S,_,w,O){var q=_!==null?_.key:null;if(typeof w=="string"&&w!==""||typeof w=="number"||typeof w=="bigint")return q!==null?null:g(S,_,""+w,O);if(typeof w=="object"&&w!==null){switch(w.$$typeof){case j:return w.key===q?y(S,_,w,O):null;case D:return w.key===q?z(S,_,w,O):null;case V:return q=w._init,w=q(w._payload),N(S,_,w,O)}if(Ge(w)||Ue(w))return q!==null?null:M(S,_,w,O,null);if(typeof w.then=="function")return N(S,_,Iu(w),O);if(w.$$typeof===R)return N(S,_,Hu(S,w),O);Pu(S,w)}return null}function A(S,_,w,O,q){if(typeof O=="string"&&O!==""||typeof O=="number"||typeof O=="bigint")return S=S.get(w)||null,g(_,S,""+O,q);if(typeof O=="object"&&O!==null){switch(O.$$typeof){case j:return S=S.get(O.key===null?w:O.key)||null,y(_,S,O,q);case D:return S=S.get(O.key===null?w:O.key)||null,z(_,S,O,q);case V:var ae=O._init;return O=ae(O._payload),A(S,_,w,O,q)}if(Ge(O)||Ue(O))return S=S.get(w)||null,M(_,S,O,q,null);if(typeof O.then=="function")return A(S,_,w,Iu(O),q);if(O.$$typeof===R)return A(S,_,w,Hu(_,O),q);Pu(_,O)}return null}function P(S,_,w,O){for(var q=null,ae=null,K=_,I=_=0,Ke=null;K!==null&&I<w.length;I++){K.index>I?(Ke=K,K=null):Ke=K.sibling;var fe=N(S,K,w[I],O);if(fe===null){K===null&&(K=Ke);break}e&&K&&fe.alternate===null&&t(S,K),_=c(fe,_,I),ae===null?q=fe:ae.sibling=fe,ae=fe,K=Ke}if(I===w.length)return n(S,K),de&&Qn(S,I),q;if(K===null){for(;I<w.length;I++)K=C(S,w[I],O),K!==null&&(_=c(K,_,I),ae===null?q=K:ae.sibling=K,ae=K);return de&&Qn(S,I),q}for(K=l(K);I<w.length;I++)Ke=A(K,S,I,w[I],O),Ke!==null&&(e&&Ke.alternate!==null&&K.delete(Ke.key===null?I:Ke.key),_=c(Ke,_,I),ae===null?q=Ke:ae.sibling=Ke,ae=Ke);return e&&K.forEach(function(Dn){return t(S,Dn)}),de&&Qn(S,I),q}function W(S,_,w,O){if(w==null)throw Error(r(151));for(var q=null,ae=null,K=_,I=_=0,Ke=null,fe=w.next();K!==null&&!fe.done;I++,fe=w.next()){K.index>I?(Ke=K,K=null):Ke=K.sibling;var Dn=N(S,K,fe.value,O);if(Dn===null){K===null&&(K=Ke);break}e&&K&&Dn.alternate===null&&t(S,K),_=c(Dn,_,I),ae===null?q=Dn:ae.sibling=Dn,ae=Dn,K=Ke}if(fe.done)return n(S,K),de&&Qn(S,I),q;if(K===null){for(;!fe.done;I++,fe=w.next())fe=C(S,fe.value,O),fe!==null&&(_=c(fe,_,I),ae===null?q=fe:ae.sibling=fe,ae=fe);return de&&Qn(S,I),q}for(K=l(K);!fe.done;I++,fe=w.next())fe=A(K,S,I,fe.value,O),fe!==null&&(e&&fe.alternate!==null&&K.delete(fe.key===null?I:fe.key),_=c(fe,_,I),ae===null?q=fe:ae.sibling=fe,ae=fe);return e&&K.forEach(function(ty){return t(S,ty)}),de&&Qn(S,I),q}function be(S,_,w,O){if(typeof w=="object"&&w!==null&&w.type===B&&w.key===null&&(w=w.props.children),typeof w=="object"&&w!==null){switch(w.$$typeof){case j:e:{for(var q=w.key;_!==null;){if(_.key===q){if(q=w.type,q===B){if(_.tag===7){n(S,_.sibling),O=u(_,w.props.children),O.return=S,S=O;break e}}else if(_.elementType===q||typeof q=="object"&&q!==null&&q.$$typeof===V&&Lf(q)===_.type){n(S,_.sibling),O=u(_,w.props),Ua(O,w),O.return=S,S=O;break e}n(S,_);break}else t(S,_);_=_.sibling}w.type===B?(O=Yn(w.props.children,S.mode,O,w.key),O.return=S,S=O):(O=ku(w.type,w.key,w.props,null,S.mode,O),Ua(O,w),O.return=S,S=O)}return f(S);case D:e:{for(q=w.key;_!==null;){if(_.key===q)if(_.tag===4&&_.stateNode.containerInfo===w.containerInfo&&_.stateNode.implementation===w.implementation){n(S,_.sibling),O=u(_,w.children||[]),O.return=S,S=O;break e}else{n(S,_);break}else t(S,_);_=_.sibling}O=Tc(w,S.mode,O),O.return=S,S=O}return f(S);case V:return q=w._init,w=q(w._payload),be(S,_,w,O)}if(Ge(w))return P(S,_,w,O);if(Ue(w)){if(q=Ue(w),typeof q!="function")throw Error(r(150));return w=q.call(w),W(S,_,w,O)}if(typeof w.then=="function")return be(S,_,Iu(w),O);if(w.$$typeof===R)return be(S,_,Hu(S,w),O);Pu(S,w)}return typeof w=="string"&&w!==""||typeof w=="number"||typeof w=="bigint"?(w=""+w,_!==null&&_.tag===6?(n(S,_.sibling),O=u(_,w),O.return=S,S=O):(n(S,_),O=jc(w,S.mode,O),O.return=S,S=O),f(S)):n(S,_)}return function(S,_,w,O){try{ka=0;var q=be(S,_,w,O);return Ul=null,q}catch(K){if(K===Na||K===Lu)throw K;var ae=dt(29,K,null,S.mode);return ae.lanes=O,ae.return=S,ae}finally{}}}var Vl=Gf(!0),qf=Gf(!1),Et=qe(null),Vt=null;function bn(e){var t=e.alternate;te(Ze,Ze.current&1),te(Et,e),Vt===null&&(t===null||Rl.current!==null||t.memoizedState!==null)&&(Vt=e)}function Yf(e){if(e.tag===22){if(te(Ze,Ze.current),te(Et,e),Vt===null){var t=e.alternate;t!==null&&t.memoizedState!==null&&(Vt=e)}}else xn()}function xn(){te(Ze,Ze.current),te(Et,Et.current)}function It(e){xe(Et),Vt===e&&(Vt=null),xe(Ze)}var Ze=qe(0);function ei(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||Io(n)))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function oo(e,t,n,l){t=e.memoizedState,n=n(l,t),n=n==null?t:x({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var ro={enqueueSetState:function(e,t,n){e=e._reactInternals;var l=vt(),u=vn(l);u.payload=t,n!=null&&(u.callback=n),t=pn(e,u,l),t!==null&&(pt(t,e,l),ja(t,e,l))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var l=vt(),u=vn(l);u.tag=1,u.payload=t,n!=null&&(u.callback=n),t=pn(e,u,l),t!==null&&(pt(t,e,l),ja(t,e,l))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=vt(),l=vn(n);l.tag=2,t!=null&&(l.callback=t),t=pn(e,l,n),t!==null&&(pt(t,e,n),ja(t,e,n))}};function Xf(e,t,n,l,u,c,f){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(l,c,f):t.prototype&&t.prototype.isPureReactComponent?!ya(n,l)||!ya(u,c):!0}function Qf(e,t,n,l){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,l),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,l),t.state!==e&&ro.enqueueReplaceState(t,t.state,null)}function Pn(e,t){var n=t;if("ref"in t){n={};for(var l in t)l!=="ref"&&(n[l]=t[l])}if(e=e.defaultProps){n===t&&(n=x({},n));for(var u in e)n[u]===void 0&&(n[u]=e[u])}return n}var ti=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function Kf(e){ti(e)}function Jf(e){console.error(e)}function $f(e){ti(e)}function ni(e,t){try{var n=e.onUncaughtError;n(t.value,{componentStack:t.stack})}catch(l){setTimeout(function(){throw l})}}function Ff(e,t,n){try{var l=e.onCaughtError;l(n.value,{componentStack:n.stack,errorBoundary:t.tag===1?t.stateNode:null})}catch(u){setTimeout(function(){throw u})}}function so(e,t,n){return n=vn(n),n.tag=3,n.payload={element:null},n.callback=function(){ni(e,t)},n}function Wf(e){return e=vn(e),e.tag=3,e}function If(e,t,n,l){var u=n.type.getDerivedStateFromError;if(typeof u=="function"){var c=l.value;e.payload=function(){return u(c)},e.callback=function(){Ff(t,n,l)}}var f=n.stateNode;f!==null&&typeof f.componentDidCatch=="function"&&(e.callback=function(){Ff(t,n,l),typeof u!="function"&&(Nn===null?Nn=new Set([this]):Nn.add(this));var g=l.stack;this.componentDidCatch(l.value,{componentStack:g!==null?g:""})})}function tp(e,t,n,l,u){if(n.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){if(t=n.alternate,t!==null&&wa(t,n,u,!0),n=Et.current,n!==null){switch(n.tag){case 13:return Vt===null?ko():n.alternate===null&&Te===0&&(Te=3),n.flags&=-257,n.flags|=65536,n.lanes=u,l===Hc?n.flags|=16384:(t=n.updateQueue,t===null?n.updateQueue=new Set([l]):t.add(l),Vo(e,l,u)),!1;case 22:return n.flags|=65536,l===Hc?n.flags|=16384:(t=n.updateQueue,t===null?(t={transitions:null,markerInstances:null,retryQueue:new Set([l])},n.updateQueue=t):(n=t.retryQueue,n===null?t.retryQueue=new Set([l]):n.add(l)),Vo(e,l,u)),!1}throw Error(r(435,n.tag))}return Vo(e,l,u),ko(),!1}if(de)return t=Et.current,t!==null?((t.flags&65536)===0&&(t.flags|=256),t.flags|=65536,t.lanes=u,l!==Rc&&(e=Error(r(422),{cause:l}),Sa(_t(e,n)))):(l!==Rc&&(t=Error(r(423),{cause:l}),Sa(_t(t,n))),e=e.current.alternate,e.flags|=65536,u&=-u,e.lanes|=u,l=_t(l,n),u=so(e.stateNode,l,u),Gc(e,u),Te!==4&&(Te=2)),!1;var c=Error(r(520),{cause:l});if(c=_t(c,n),qa===null?qa=[c]:qa.push(c),Te!==4&&(Te=2),t===null)return!0;l=_t(l,n),n=t;do{switch(n.tag){case 3:return n.flags|=65536,e=u&-u,n.lanes|=e,e=so(n.stateNode,l,e),Gc(n,e),!1;case 1:if(t=n.type,c=n.stateNode,(n.flags&128)===0&&(typeof t.getDerivedStateFromError=="function"||c!==null&&typeof c.componentDidCatch=="function"&&(Nn===null||!Nn.has(c))))return n.flags|=65536,u&=-u,n.lanes|=u,u=Wf(u),If(u,e,n,l),Gc(n,u),!1}n=n.return}while(n!==null);return!1}var Pf=Error(r(461)),Xe=!1;function $e(e,t,n,l){t.child=e===null?qf(t,null,n,l):Vl(t,e.child,n,l)}function ed(e,t,n,l,u){n=n.render;var c=t.ref;if("ref"in l){var f={};for(var g in l)g!=="ref"&&(f[g]=l[g])}else f=l;return Fn(t),l=Kc(e,t,n,f,c,u),g=Jc(),e!==null&&!Xe?($c(e,t,u),Pt(e,t,u)):(de&&g&&Mc(t),t.flags|=1,$e(e,t,l,u),t.child)}function td(e,t,n,l,u){if(e===null){var c=n.type;return typeof c=="function"&&!Ac(c)&&c.defaultProps===void 0&&n.compare===null?(t.tag=15,t.type=c,nd(e,t,c,l,u)):(e=ku(n.type,null,l,t,t.mode,u),e.ref=t.ref,e.return=t,t.child=e)}if(c=e.child,!bo(e,u)){var f=c.memoizedProps;if(n=n.compare,n=n!==null?n:ya,n(f,l)&&e.ref===t.ref)return Pt(e,t,u)}return t.flags|=1,e=Qt(c,l),e.ref=t.ref,e.return=t,t.child=e}function nd(e,t,n,l,u){if(e!==null){var c=e.memoizedProps;if(ya(c,l)&&e.ref===t.ref)if(Xe=!1,t.pendingProps=l=c,bo(e,u))(e.flags&131072)!==0&&(Xe=!0);else return t.lanes=e.lanes,Pt(e,t,u)}return fo(e,t,n,l,u)}function ld(e,t,n){var l=t.pendingProps,u=l.children,c=e!==null?e.memoizedState:null;if(l.mode==="hidden"){if((t.flags&128)!==0){if(l=c!==null?c.baseLanes|n:n,e!==null){for(u=t.child=e.child,c=0;u!==null;)c=c|u.lanes|u.childLanes,u=u.sibling;t.childLanes=c&~l}else t.childLanes=0,t.child=null;return ad(e,t,l,n)}if((n&536870912)!==0)t.memoizedState={baseLanes:0,cachePool:null},e!==null&&Bu(t,c!==null?c.cachePool:null),c!==null?tf(t,c):Yc(),Yf(t);else return t.lanes=t.childLanes=536870912,ad(e,t,c!==null?c.baseLanes|n:n,n)}else c!==null?(Bu(t,c.cachePool),tf(t,c),xn(),t.memoizedState=null):(e!==null&&Bu(t,null),Yc(),xn());return $e(e,t,u,n),t.child}function ad(e,t,n,l){var u=Zc();return u=u===null?null:{parent:Ve._currentValue,pool:u},t.memoizedState={baseLanes:n,cachePool:u},e!==null&&Bu(t,null),Yc(),Yf(t),e!==null&&wa(e,t,l,!0),null}function li(e,t){var n=t.ref;if(n===null)e!==null&&e.ref!==null&&(t.flags|=4194816);else{if(typeof n!="function"&&typeof n!="object")throw Error(r(284));(e===null||e.ref!==n)&&(t.flags|=4194816)}}function fo(e,t,n,l,u){return Fn(t),n=Kc(e,t,n,l,void 0,u),l=Jc(),e!==null&&!Xe?($c(e,t,u),Pt(e,t,u)):(de&&l&&Mc(t),t.flags|=1,$e(e,t,n,u),t.child)}function ud(e,t,n,l,u,c){return Fn(t),t.updateQueue=null,n=lf(t,l,n,u),nf(e),l=Jc(),e!==null&&!Xe?($c(e,t,c),Pt(e,t,c)):(de&&l&&Mc(t),t.flags|=1,$e(e,t,n,c),t.child)}function id(e,t,n,l,u){if(Fn(t),t.stateNode===null){var c=Al,f=n.contextType;typeof f=="object"&&f!==null&&(c=Pe(f)),c=new n(l,c),t.memoizedState=c.state!==null&&c.state!==void 0?c.state:null,c.updater=ro,t.stateNode=c,c._reactInternals=t,c=t.stateNode,c.props=l,c.state=t.memoizedState,c.refs={},Bc(t),f=n.contextType,c.context=typeof f=="object"&&f!==null?Pe(f):Al,c.state=t.memoizedState,f=n.getDerivedStateFromProps,typeof f=="function"&&(oo(t,n,f,l),c.state=t.memoizedState),typeof n.getDerivedStateFromProps=="function"||typeof c.getSnapshotBeforeUpdate=="function"||typeof c.UNSAFE_componentWillMount!="function"&&typeof c.componentWillMount!="function"||(f=c.state,typeof c.componentWillMount=="function"&&c.componentWillMount(),typeof c.UNSAFE_componentWillMount=="function"&&c.UNSAFE_componentWillMount(),f!==c.state&&ro.enqueueReplaceState(c,c.state,null),Ma(t,l,c,u),Ta(),c.state=t.memoizedState),typeof c.componentDidMount=="function"&&(t.flags|=4194308),l=!0}else if(e===null){c=t.stateNode;var g=t.memoizedProps,y=Pn(n,g);c.props=y;var z=c.context,M=n.contextType;f=Al,typeof M=="object"&&M!==null&&(f=Pe(M));var C=n.getDerivedStateFromProps;M=typeof C=="function"||typeof c.getSnapshotBeforeUpdate=="function",g=t.pendingProps!==g,M||typeof c.UNSAFE_componentWillReceiveProps!="function"&&typeof c.componentWillReceiveProps!="function"||(g||z!==f)&&Qf(t,c,l,f),gn=!1;var N=t.memoizedState;c.state=N,Ma(t,l,c,u),Ta(),z=t.memoizedState,g||N!==z||gn?(typeof C=="function"&&(oo(t,n,C,l),z=t.memoizedState),(y=gn||Xf(t,n,y,l,N,z,f))?(M||typeof c.UNSAFE_componentWillMount!="function"&&typeof c.componentWillMount!="function"||(typeof c.componentWillMount=="function"&&c.componentWillMount(),typeof c.UNSAFE_componentWillMount=="function"&&c.UNSAFE_componentWillMount()),typeof c.componentDidMount=="function"&&(t.flags|=4194308)):(typeof c.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=l,t.memoizedState=z),c.props=l,c.state=z,c.context=f,l=y):(typeof c.componentDidMount=="function"&&(t.flags|=4194308),l=!1)}else{c=t.stateNode,Lc(e,t),f=t.memoizedProps,M=Pn(n,f),c.props=M,C=t.pendingProps,N=c.context,z=n.contextType,y=Al,typeof z=="object"&&z!==null&&(y=Pe(z)),g=n.getDerivedStateFromProps,(z=typeof g=="function"||typeof c.getSnapshotBeforeUpdate=="function")||typeof c.UNSAFE_componentWillReceiveProps!="function"&&typeof c.componentWillReceiveProps!="function"||(f!==C||N!==y)&&Qf(t,c,l,y),gn=!1,N=t.memoizedState,c.state=N,Ma(t,l,c,u),Ta();var A=t.memoizedState;f!==C||N!==A||gn||e!==null&&e.dependencies!==null&&Zu(e.dependencies)?(typeof g=="function"&&(oo(t,n,g,l),A=t.memoizedState),(M=gn||Xf(t,n,M,l,N,A,y)||e!==null&&e.dependencies!==null&&Zu(e.dependencies))?(z||typeof c.UNSAFE_componentWillUpdate!="function"&&typeof c.componentWillUpdate!="function"||(typeof c.componentWillUpdate=="function"&&c.componentWillUpdate(l,A,y),typeof c.UNSAFE_componentWillUpdate=="function"&&c.UNSAFE_componentWillUpdate(l,A,y)),typeof c.componentDidUpdate=="function"&&(t.flags|=4),typeof c.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof c.componentDidUpdate!="function"||f===e.memoizedProps&&N===e.memoizedState||(t.flags|=4),typeof c.getSnapshotBeforeUpdate!="function"||f===e.memoizedProps&&N===e.memoizedState||(t.flags|=1024),t.memoizedProps=l,t.memoizedState=A),c.props=l,c.state=A,c.context=y,l=M):(typeof c.componentDidUpdate!="function"||f===e.memoizedProps&&N===e.memoizedState||(t.flags|=4),typeof c.getSnapshotBeforeUpdate!="function"||f===e.memoizedProps&&N===e.memoizedState||(t.flags|=1024),l=!1)}return c=l,li(e,t),l=(t.flags&128)!==0,c||l?(c=t.stateNode,n=l&&typeof n.getDerivedStateFromError!="function"?null:c.render(),t.flags|=1,e!==null&&l?(t.child=Vl(t,e.child,null,u),t.child=Vl(t,null,n,u)):$e(e,t,n,u),t.memoizedState=c.state,e=t.child):e=Pt(e,t,u),e}function cd(e,t,n,l){return _a(),t.flags|=256,$e(e,t,n,l),t.child}var mo={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function ho(e){return{baseLanes:e,cachePool:Ks()}}function go(e,t,n){return e=e!==null?e.childLanes&~n:0,t&&(e|=Nt),e}function od(e,t,n){var l=t.pendingProps,u=!1,c=(t.flags&128)!==0,f;if((f=c)||(f=e!==null&&e.memoizedState===null?!1:(Ze.current&2)!==0),f&&(u=!0,t.flags&=-129),f=(t.flags&32)!==0,t.flags&=-33,e===null){if(de){if(u?bn(t):xn(),de){var g=je,y;if(y=g){e:{for(y=g,g=Ut;y.nodeType!==8;){if(!g){g=null;break e}if(y=Rt(y.nextSibling),y===null){g=null;break e}}g=y}g!==null?(t.memoizedState={dehydrated:g,treeContext:Xn!==null?{id:Kt,overflow:Jt}:null,retryLane:536870912,hydrationErrors:null},y=dt(18,null,null,0),y.stateNode=g,y.return=t,t.child=y,tt=t,je=null,y=!0):y=!1}y||Jn(t)}if(g=t.memoizedState,g!==null&&(g=g.dehydrated,g!==null))return Io(g)?t.lanes=32:t.lanes=536870912,null;It(t)}return g=l.children,l=l.fallback,u?(xn(),u=t.mode,g=ai({mode:"hidden",children:g},u),l=Yn(l,u,n,null),g.return=t,l.return=t,g.sibling=l,t.child=g,u=t.child,u.memoizedState=ho(n),u.childLanes=go(e,f,n),t.memoizedState=mo,l):(bn(t),vo(t,g))}if(y=e.memoizedState,y!==null&&(g=y.dehydrated,g!==null)){if(c)t.flags&256?(bn(t),t.flags&=-257,t=po(e,t,n)):t.memoizedState!==null?(xn(),t.child=e.child,t.flags|=128,t=null):(xn(),u=l.fallback,g=t.mode,l=ai({mode:"visible",children:l.children},g),u=Yn(u,g,n,null),u.flags|=2,l.return=t,u.return=t,l.sibling=u,t.child=l,Vl(t,e.child,null,n),l=t.child,l.memoizedState=ho(n),l.childLanes=go(e,f,n),t.memoizedState=mo,t=u);else if(bn(t),Io(g)){if(f=g.nextSibling&&g.nextSibling.dataset,f)var z=f.dgst;f=z,l=Error(r(419)),l.stack="",l.digest=f,Sa({value:l,source:null,stack:null}),t=po(e,t,n)}else if(Xe||wa(e,t,n,!1),f=(n&e.childLanes)!==0,Xe||f){if(f=Se,f!==null&&(l=n&-n,l=(l&42)!==0?1:Ii(l),l=(l&(f.suspendedLanes|n))!==0?0:l,l!==0&&l!==y.retryLane))throw y.retryLane=l,Nl(e,l),pt(f,e,l),Pf;g.data==="$?"||ko(),t=po(e,t,n)}else g.data==="$?"?(t.flags|=192,t.child=e.child,t=null):(e=y.treeContext,je=Rt(g.nextSibling),tt=t,de=!0,Kn=null,Ut=!1,e!==null&&(wt[zt++]=Kt,wt[zt++]=Jt,wt[zt++]=Xn,Kt=e.id,Jt=e.overflow,Xn=t),t=vo(t,l.children),t.flags|=4096);return t}return u?(xn(),u=l.fallback,g=t.mode,y=e.child,z=y.sibling,l=Qt(y,{mode:"hidden",children:l.children}),l.subtreeFlags=y.subtreeFlags&65011712,z!==null?u=Qt(z,u):(u=Yn(u,g,n,null),u.flags|=2),u.return=t,l.return=t,l.sibling=u,t.child=l,l=u,u=t.child,g=e.child.memoizedState,g===null?g=ho(n):(y=g.cachePool,y!==null?(z=Ve._currentValue,y=y.parent!==z?{parent:z,pool:z}:y):y=Ks(),g={baseLanes:g.baseLanes|n,cachePool:y}),u.memoizedState=g,u.childLanes=go(e,f,n),t.memoizedState=mo,l):(bn(t),n=e.child,e=n.sibling,n=Qt(n,{mode:"visible",children:l.children}),n.return=t,n.sibling=null,e!==null&&(f=t.deletions,f===null?(t.deletions=[e],t.flags|=16):f.push(e)),t.child=n,t.memoizedState=null,n)}function vo(e,t){return t=ai({mode:"visible",children:t},e.mode),t.return=e,e.child=t}function ai(e,t){return e=dt(22,e,null,t),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function po(e,t,n){return Vl(t,e.child,null,n),e=vo(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function rd(e,t,n){e.lanes|=t;var l=e.alternate;l!==null&&(l.lanes|=t),Dc(e.return,t,n)}function yo(e,t,n,l,u){var c=e.memoizedState;c===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:l,tail:n,tailMode:u}:(c.isBackwards=t,c.rendering=null,c.renderingStartTime=0,c.last=l,c.tail=n,c.tailMode=u)}function sd(e,t,n){var l=t.pendingProps,u=l.revealOrder,c=l.tail;if($e(e,t,l.children,n),l=Ze.current,(l&2)!==0)l=l&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&rd(e,n,t);else if(e.tag===19)rd(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}l&=1}switch(te(Ze,l),u){case"forwards":for(n=t.child,u=null;n!==null;)e=n.alternate,e!==null&&ei(e)===null&&(u=n),n=n.sibling;n=u,n===null?(u=t.child,t.child=null):(u=n.sibling,n.sibling=null),yo(t,!1,u,n,c);break;case"backwards":for(n=null,u=t.child,t.child=null;u!==null;){if(e=u.alternate,e!==null&&ei(e)===null){t.child=u;break}e=u.sibling,u.sibling=n,n=u,u=e}yo(t,!0,n,null,c);break;case"together":yo(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Pt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),En|=t.lanes,(n&t.childLanes)===0)if(e!==null){if(wa(e,t,n,!1),(n&t.childLanes)===0)return null}else return null;if(e!==null&&t.child!==e.child)throw Error(r(153));if(t.child!==null){for(e=t.child,n=Qt(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Qt(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function bo(e,t){return(e.lanes&t)!==0?!0:(e=e.dependencies,!!(e!==null&&Zu(e)))}function np(e,t,n){switch(t.tag){case 3:Zn(t,t.stateNode.containerInfo),hn(t,Ve,e.memoizedState.cache),_a();break;case 27:case 5:on(t);break;case 4:Zn(t,t.stateNode.containerInfo);break;case 10:hn(t,t.type,t.memoizedProps.value);break;case 13:var l=t.memoizedState;if(l!==null)return l.dehydrated!==null?(bn(t),t.flags|=128,null):(n&t.child.childLanes)!==0?od(e,t,n):(bn(t),e=Pt(e,t,n),e!==null?e.sibling:null);bn(t);break;case 19:var u=(e.flags&128)!==0;if(l=(n&t.childLanes)!==0,l||(wa(e,t,n,!1),l=(n&t.childLanes)!==0),u){if(l)return sd(e,t,n);t.flags|=128}if(u=t.memoizedState,u!==null&&(u.rendering=null,u.tail=null,u.lastEffect=null),te(Ze,Ze.current),l)break;return null;case 22:case 23:return t.lanes=0,ld(e,t,n);case 24:hn(t,Ve,e.memoizedState.cache)}return Pt(e,t,n)}function fd(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps)Xe=!0;else{if(!bo(e,n)&&(t.flags&128)===0)return Xe=!1,np(e,t,n);Xe=(e.flags&131072)!==0}else Xe=!1,de&&(t.flags&1048576)!==0&&Bs(t,Vu,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var l=t.elementType,u=l._init;if(l=u(l._payload),t.type=l,typeof l=="function")Ac(l)?(e=Pn(l,e),t.tag=1,t=id(null,t,l,e,n)):(t.tag=0,t=fo(null,t,l,e,n));else{if(l!=null){if(u=l.$$typeof,u===Z){t.tag=11,t=ed(null,t,l,e,n);break e}else if(u===oe){t.tag=14,t=td(null,t,l,e,n);break e}}throw t=Dt(l)||l,Error(r(306,t,""))}}return t;case 0:return fo(e,t,t.type,t.pendingProps,n);case 1:return l=t.type,u=Pn(l,t.pendingProps),id(e,t,l,u,n);case 3:e:{if(Zn(t,t.stateNode.containerInfo),e===null)throw Error(r(387));l=t.pendingProps;var c=t.memoizedState;u=c.element,Lc(e,t),Ma(t,l,null,n);var f=t.memoizedState;if(l=f.cache,hn(t,Ve,l),l!==c.cache&&kc(t,[Ve],n,!0),Ta(),l=f.element,c.isDehydrated)if(c={element:l,isDehydrated:!1,cache:f.cache},t.updateQueue.baseState=c,t.memoizedState=c,t.flags&256){t=cd(e,t,l,n);break e}else if(l!==u){u=_t(Error(r(424)),t),Sa(u),t=cd(e,t,l,n);break e}else{switch(e=t.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for(je=Rt(e.firstChild),tt=t,de=!0,Kn=null,Ut=!0,n=qf(t,null,l,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling}else{if(_a(),l===u){t=Pt(e,t,n);break e}$e(e,t,l,n)}t=t.child}return t;case 26:return li(e,t),e===null?(n=gm(t.type,null,t.pendingProps,null))?t.memoizedState=n:de||(n=t.type,e=t.pendingProps,l=yi(jt.current).createElement(n),l[Ie]=t,l[lt]=e,We(l,n,e),Ye(l),t.stateNode=l):t.memoizedState=gm(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return on(t),e===null&&de&&(l=t.stateNode=dm(t.type,t.pendingProps,jt.current),tt=t,Ut=!0,u=je,Tn(t.type)?(Po=u,je=Rt(l.firstChild)):je=u),$e(e,t,t.pendingProps.children,n),li(e,t),e===null&&(t.flags|=4194304),t.child;case 5:return e===null&&de&&((u=l=je)&&(l=Mp(l,t.type,t.pendingProps,Ut),l!==null?(t.stateNode=l,tt=t,je=Rt(l.firstChild),Ut=!1,u=!0):u=!1),u||Jn(t)),on(t),u=t.type,c=t.pendingProps,f=e!==null?e.memoizedProps:null,l=c.children,$o(u,c)?l=null:f!==null&&$o(u,f)&&(t.flags|=32),t.memoizedState!==null&&(u=Kc(e,t,Jv,null,null,n),Ia._currentValue=u),li(e,t),$e(e,t,l,n),t.child;case 6:return e===null&&de&&((e=n=je)&&(n=Op(n,t.pendingProps,Ut),n!==null?(t.stateNode=n,tt=t,je=null,e=!0):e=!1),e||Jn(t)),null;case 13:return od(e,t,n);case 4:return Zn(t,t.stateNode.containerInfo),l=t.pendingProps,e===null?t.child=Vl(t,null,l,n):$e(e,t,l,n),t.child;case 11:return ed(e,t,t.type,t.pendingProps,n);case 7:return $e(e,t,t.pendingProps,n),t.child;case 8:return $e(e,t,t.pendingProps.children,n),t.child;case 12:return $e(e,t,t.pendingProps.children,n),t.child;case 10:return l=t.pendingProps,hn(t,t.type,l.value),$e(e,t,l.children,n),t.child;case 9:return u=t.type._context,l=t.pendingProps.children,Fn(t),u=Pe(u),l=l(u),t.flags|=1,$e(e,t,l,n),t.child;case 14:return td(e,t,t.type,t.pendingProps,n);case 15:return nd(e,t,t.type,t.pendingProps,n);case 19:return sd(e,t,n);case 31:return l=t.pendingProps,n=t.mode,l={mode:l.mode,children:l.children},e===null?(n=ai(l,n),n.ref=t.ref,t.child=n,n.return=t,t=n):(n=Qt(e.child,l),n.ref=t.ref,t.child=n,n.return=t,t=n),t;case 22:return ld(e,t,n);case 24:return Fn(t),l=Pe(Ve),e===null?(u=Zc(),u===null&&(u=Se,c=Uc(),u.pooledCache=c,c.refCount++,c!==null&&(u.pooledCacheLanes|=n),u=c),t.memoizedState={parent:l,cache:u},Bc(t),hn(t,Ve,u)):((e.lanes&n)!==0&&(Lc(e,t),Ma(t,null,null,n),Ta()),u=e.memoizedState,c=t.memoizedState,u.parent!==l?(u={parent:l,cache:l},t.memoizedState=u,t.lanes===0&&(t.memoizedState=t.updateQueue.baseState=u),hn(t,Ve,l)):(l=c.cache,hn(t,Ve,l),l!==u.cache&&kc(t,[Ve],n,!0))),$e(e,t,t.pendingProps.children,n),t.child;case 29:throw t.pendingProps}throw Error(r(156,t.tag))}function en(e){e.flags|=4}function dd(e,t){if(t.type!=="stylesheet"||(t.state.loading&4)!==0)e.flags&=-16777217;else if(e.flags|=16777216,!xm(t)){if(t=Et.current,t!==null&&((ce&4194048)===ce?Vt!==null:(ce&62914560)!==ce&&(ce&536870912)===0||t!==Vt))throw Aa=Hc,Js;e.flags|=8192}}function ui(e,t){t!==null&&(e.flags|=4),e.flags&16384&&(t=e.tag!==22?qr():536870912,e.lanes|=t,Ll|=t)}function Va(e,t){if(!de)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var l=null;n!==null;)n.alternate!==null&&(l=n),n=n.sibling;l===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:l.sibling=null}}function Ae(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,l=0;if(t)for(var u=e.child;u!==null;)n|=u.lanes|u.childLanes,l|=u.subtreeFlags&65011712,l|=u.flags&65011712,u.return=e,u=u.sibling;else for(u=e.child;u!==null;)n|=u.lanes|u.childLanes,l|=u.subtreeFlags,l|=u.flags,u.return=e,u=u.sibling;return e.subtreeFlags|=l,e.childLanes=n,t}function lp(e,t,n){var l=t.pendingProps;switch(Oc(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Ae(t),null;case 1:return Ae(t),null;case 3:return n=t.stateNode,l=null,e!==null&&(l=e.memoizedState.cache),t.memoizedState.cache!==l&&(t.flags|=2048),Ft(Ve),Tt(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),(e===null||e.child===null)&&(xa(t)?en(t):e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,qs())),Ae(t),null;case 26:return n=t.memoizedState,e===null?(en(t),n!==null?(Ae(t),dd(t,n)):(Ae(t),t.flags&=-16777217)):n?n!==e.memoizedState?(en(t),Ae(t),dd(t,n)):(Ae(t),t.flags&=-16777217):(e.memoizedProps!==l&&en(t),Ae(t),t.flags&=-16777217),null;case 27:rn(t),n=jt.current;var u=t.type;if(e!==null&&t.stateNode!=null)e.memoizedProps!==l&&en(t);else{if(!l){if(t.stateNode===null)throw Error(r(166));return Ae(t),null}e=se.current,xa(t)?Ls(t):(e=dm(u,l,n),t.stateNode=e,en(t))}return Ae(t),null;case 5:if(rn(t),n=t.type,e!==null&&t.stateNode!=null)e.memoizedProps!==l&&en(t);else{if(!l){if(t.stateNode===null)throw Error(r(166));return Ae(t),null}if(e=se.current,xa(t))Ls(t);else{switch(u=yi(jt.current),e){case 1:e=u.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:e=u.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":e=u.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":e=u.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":e=u.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof l.is=="string"?u.createElement("select",{is:l.is}):u.createElement("select"),l.multiple?e.multiple=!0:l.size&&(e.size=l.size);break;default:e=typeof l.is=="string"?u.createElement(n,{is:l.is}):u.createElement(n)}}e[Ie]=t,e[lt]=l;e:for(u=t.child;u!==null;){if(u.tag===5||u.tag===6)e.appendChild(u.stateNode);else if(u.tag!==4&&u.tag!==27&&u.child!==null){u.child.return=u,u=u.child;continue}if(u===t)break e;for(;u.sibling===null;){if(u.return===null||u.return===t)break e;u=u.return}u.sibling.return=u.return,u=u.sibling}t.stateNode=e;e:switch(We(e,n,l),n){case"button":case"input":case"select":case"textarea":e=!!l.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&en(t)}}return Ae(t),t.flags&=-16777217,null;case 6:if(e&&t.stateNode!=null)e.memoizedProps!==l&&en(t);else{if(typeof l!="string"&&t.stateNode===null)throw Error(r(166));if(e=jt.current,xa(t)){if(e=t.stateNode,n=t.memoizedProps,l=null,u=tt,u!==null)switch(u.tag){case 27:case 5:l=u.memoizedProps}e[Ie]=t,e=!!(e.nodeValue===n||l!==null&&l.suppressHydrationWarning===!0||um(e.nodeValue,n)),e||Jn(t)}else e=yi(e).createTextNode(l),e[Ie]=t,t.stateNode=e}return Ae(t),null;case 13:if(l=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(u=xa(t),l!==null&&l.dehydrated!==null){if(e===null){if(!u)throw Error(r(318));if(u=t.memoizedState,u=u!==null?u.dehydrated:null,!u)throw Error(r(317));u[Ie]=t}else _a(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;Ae(t),u=!1}else u=qs(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=u),u=!0;if(!u)return t.flags&256?(It(t),t):(It(t),null)}if(It(t),(t.flags&128)!==0)return t.lanes=n,t;if(n=l!==null,e=e!==null&&e.memoizedState!==null,n){l=t.child,u=null,l.alternate!==null&&l.alternate.memoizedState!==null&&l.alternate.memoizedState.cachePool!==null&&(u=l.alternate.memoizedState.cachePool.pool);var c=null;l.memoizedState!==null&&l.memoizedState.cachePool!==null&&(c=l.memoizedState.cachePool.pool),c!==u&&(l.flags|=2048)}return n!==e&&n&&(t.child.flags|=8192),ui(t,t.updateQueue),Ae(t),null;case 4:return Tt(),e===null&&Yo(t.stateNode.containerInfo),Ae(t),null;case 10:return Ft(t.type),Ae(t),null;case 19:if(xe(Ze),u=t.memoizedState,u===null)return Ae(t),null;if(l=(t.flags&128)!==0,c=u.rendering,c===null)if(l)Va(u,!1);else{if(Te!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(c=ei(e),c!==null){for(t.flags|=128,Va(u,!1),e=c.updateQueue,t.updateQueue=e,ui(t,e),t.subtreeFlags=0,e=n,n=t.child;n!==null;)Hs(n,e),n=n.sibling;return te(Ze,Ze.current&1|2),t.child}e=e.sibling}u.tail!==null&&kt()>oi&&(t.flags|=128,l=!0,Va(u,!1),t.lanes=4194304)}else{if(!l)if(e=ei(c),e!==null){if(t.flags|=128,l=!0,e=e.updateQueue,t.updateQueue=e,ui(t,e),Va(u,!0),u.tail===null&&u.tailMode==="hidden"&&!c.alternate&&!de)return Ae(t),null}else 2*kt()-u.renderingStartTime>oi&&n!==536870912&&(t.flags|=128,l=!0,Va(u,!1),t.lanes=4194304);u.isBackwards?(c.sibling=t.child,t.child=c):(e=u.last,e!==null?e.sibling=c:t.child=c,u.last=c)}return u.tail!==null?(t=u.tail,u.rendering=t,u.tail=t.sibling,u.renderingStartTime=kt(),t.sibling=null,e=Ze.current,te(Ze,l?e&1|2:e&1),t):(Ae(t),null);case 22:case 23:return It(t),Xc(),l=t.memoizedState!==null,e!==null?e.memoizedState!==null!==l&&(t.flags|=8192):l&&(t.flags|=8192),l?(n&536870912)!==0&&(t.flags&128)===0&&(Ae(t),t.subtreeFlags&6&&(t.flags|=8192)):Ae(t),n=t.updateQueue,n!==null&&ui(t,n.retryQueue),n=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(n=e.memoizedState.cachePool.pool),l=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(l=t.memoizedState.cachePool.pool),l!==n&&(t.flags|=2048),e!==null&&xe(Wn),null;case 24:return n=null,e!==null&&(n=e.memoizedState.cache),t.memoizedState.cache!==n&&(t.flags|=2048),Ft(Ve),Ae(t),null;case 25:return null;case 30:return null}throw Error(r(156,t.tag))}function ap(e,t){switch(Oc(t),t.tag){case 1:return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Ft(Ve),Tt(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 26:case 27:case 5:return rn(t),null;case 13:if(It(t),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(r(340));_a()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return xe(Ze),null;case 4:return Tt(),null;case 10:return Ft(t.type),null;case 22:case 23:return It(t),Xc(),e!==null&&xe(Wn),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 24:return Ft(Ve),null;case 25:return null;default:return null}}function md(e,t){switch(Oc(t),t.tag){case 3:Ft(Ve),Tt();break;case 26:case 27:case 5:rn(t);break;case 4:Tt();break;case 13:It(t);break;case 19:xe(Ze);break;case 10:Ft(t.type);break;case 22:case 23:It(t),Xc(),e!==null&&xe(Wn);break;case 24:Ft(Ve)}}function Za(e,t){try{var n=t.updateQueue,l=n!==null?n.lastEffect:null;if(l!==null){var u=l.next;n=u;do{if((n.tag&e)===e){l=void 0;var c=n.create,f=n.inst;l=c(),f.destroy=l}n=n.next}while(n!==u)}}catch(g){_e(t,t.return,g)}}function _n(e,t,n){try{var l=t.updateQueue,u=l!==null?l.lastEffect:null;if(u!==null){var c=u.next;l=c;do{if((l.tag&e)===e){var f=l.inst,g=f.destroy;if(g!==void 0){f.destroy=void 0,u=t;var y=n,z=g;try{z()}catch(M){_e(u,y,M)}}}l=l.next}while(l!==c)}}catch(M){_e(t,t.return,M)}}function hd(e){var t=e.updateQueue;if(t!==null){var n=e.stateNode;try{ef(t,n)}catch(l){_e(e,e.return,l)}}}function gd(e,t,n){n.props=Pn(e.type,e.memoizedProps),n.state=e.memoizedState;try{n.componentWillUnmount()}catch(l){_e(e,t,l)}}function Ha(e,t){try{var n=e.ref;if(n!==null){switch(e.tag){case 26:case 27:case 5:var l=e.stateNode;break;case 30:l=e.stateNode;break;default:l=e.stateNode}typeof n=="function"?e.refCleanup=n(l):n.current=l}}catch(u){_e(e,t,u)}}function Zt(e,t){var n=e.ref,l=e.refCleanup;if(n!==null)if(typeof l=="function")try{l()}catch(u){_e(e,t,u)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof n=="function")try{n(null)}catch(u){_e(e,t,u)}else n.current=null}function vd(e){var t=e.type,n=e.memoizedProps,l=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":n.autoFocus&&l.focus();break e;case"img":n.src?l.src=n.src:n.srcSet&&(l.srcset=n.srcSet)}}catch(u){_e(e,e.return,u)}}function xo(e,t,n){try{var l=e.stateNode;Ep(l,e.type,n,t),l[lt]=t}catch(u){_e(e,e.return,u)}}function pd(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&Tn(e.type)||e.tag===4}function _o(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||pd(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&Tn(e.type)||e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function So(e,t,n){var l=e.tag;if(l===5||l===6)e=e.stateNode,t?(n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n).insertBefore(e,t):(t=n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n,t.appendChild(e),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=pi));else if(l!==4&&(l===27&&Tn(e.type)&&(n=e.stateNode,t=null),e=e.child,e!==null))for(So(e,t,n),e=e.sibling;e!==null;)So(e,t,n),e=e.sibling}function ii(e,t,n){var l=e.tag;if(l===5||l===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(l!==4&&(l===27&&Tn(e.type)&&(n=e.stateNode),e=e.child,e!==null))for(ii(e,t,n),e=e.sibling;e!==null;)ii(e,t,n),e=e.sibling}function yd(e){var t=e.stateNode,n=e.memoizedProps;try{for(var l=e.type,u=t.attributes;u.length;)t.removeAttributeNode(u[0]);We(t,l,n),t[Ie]=e,t[lt]=n}catch(c){_e(e,e.return,c)}}var tn=!1,Oe=!1,wo=!1,bd=typeof WeakSet=="function"?WeakSet:Set,Qe=null;function up(e,t){if(e=e.containerInfo,Ko=zi,e=Ts(e),xc(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var l=n.getSelection&&n.getSelection();if(l&&l.rangeCount!==0){n=l.anchorNode;var u=l.anchorOffset,c=l.focusNode;l=l.focusOffset;try{n.nodeType,c.nodeType}catch{n=null;break e}var f=0,g=-1,y=-1,z=0,M=0,C=e,N=null;t:for(;;){for(var A;C!==n||u!==0&&C.nodeType!==3||(g=f+u),C!==c||l!==0&&C.nodeType!==3||(y=f+l),C.nodeType===3&&(f+=C.nodeValue.length),(A=C.firstChild)!==null;)N=C,C=A;for(;;){if(C===e)break t;if(N===n&&++z===u&&(g=f),N===c&&++M===l&&(y=f),(A=C.nextSibling)!==null)break;C=N,N=C.parentNode}C=A}n=g===-1||y===-1?null:{start:g,end:y}}else n=null}n=n||{start:0,end:0}}else n=null;for(Jo={focusedElem:e,selectionRange:n},zi=!1,Qe=t;Qe!==null;)if(t=Qe,e=t.child,(t.subtreeFlags&1024)!==0&&e!==null)e.return=t,Qe=e;else for(;Qe!==null;){switch(t=Qe,c=t.alternate,e=t.flags,t.tag){case 0:break;case 11:case 15:break;case 1:if((e&1024)!==0&&c!==null){e=void 0,n=t,u=c.memoizedProps,c=c.memoizedState,l=n.stateNode;try{var P=Pn(n.type,u,n.elementType===n.type);e=l.getSnapshotBeforeUpdate(P,c),l.__reactInternalSnapshotBeforeUpdate=e}catch(W){_e(n,n.return,W)}}break;case 3:if((e&1024)!==0){if(e=t.stateNode.containerInfo,n=e.nodeType,n===9)Wo(e);else if(n===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":Wo(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((e&1024)!==0)throw Error(r(163))}if(e=t.sibling,e!==null){e.return=t.return,Qe=e;break}Qe=t.return}}function xd(e,t,n){var l=n.flags;switch(n.tag){case 0:case 11:case 15:Sn(e,n),l&4&&Za(5,n);break;case 1:if(Sn(e,n),l&4)if(e=n.stateNode,t===null)try{e.componentDidMount()}catch(f){_e(n,n.return,f)}else{var u=Pn(n.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(u,t,e.__reactInternalSnapshotBeforeUpdate)}catch(f){_e(n,n.return,f)}}l&64&&hd(n),l&512&&Ha(n,n.return);break;case 3:if(Sn(e,n),l&64&&(e=n.updateQueue,e!==null)){if(t=null,n.child!==null)switch(n.child.tag){case 27:case 5:t=n.child.stateNode;break;case 1:t=n.child.stateNode}try{ef(e,t)}catch(f){_e(n,n.return,f)}}break;case 27:t===null&&l&4&&yd(n);case 26:case 5:Sn(e,n),t===null&&l&4&&vd(n),l&512&&Ha(n,n.return);break;case 12:Sn(e,n);break;case 13:Sn(e,n),l&4&&wd(e,n),l&64&&(e=n.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(n=hp.bind(null,n),Rp(e,n))));break;case 22:if(l=n.memoizedState!==null||tn,!l){t=t!==null&&t.memoizedState!==null||Oe,u=tn;var c=Oe;tn=l,(Oe=t)&&!c?wn(e,n,(n.subtreeFlags&8772)!==0):Sn(e,n),tn=u,Oe=c}break;case 30:break;default:Sn(e,n)}}function _d(e){var t=e.alternate;t!==null&&(e.alternate=null,_d(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&tc(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var Ee=null,it=!1;function nn(e,t,n){for(n=n.child;n!==null;)Sd(e,t,n),n=n.sibling}function Sd(e,t,n){if(rt&&typeof rt.onCommitFiberUnmount=="function")try{rt.onCommitFiberUnmount(ia,n)}catch{}switch(n.tag){case 26:Oe||Zt(n,t),nn(e,t,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode,n.parentNode.removeChild(n));break;case 27:Oe||Zt(n,t);var l=Ee,u=it;Tn(n.type)&&(Ee=n.stateNode,it=!1),nn(e,t,n),Ja(n.stateNode),Ee=l,it=u;break;case 5:Oe||Zt(n,t);case 6:if(l=Ee,u=it,Ee=null,nn(e,t,n),Ee=l,it=u,Ee!==null)if(it)try{(Ee.nodeType===9?Ee.body:Ee.nodeName==="HTML"?Ee.ownerDocument.body:Ee).removeChild(n.stateNode)}catch(c){_e(n,t,c)}else try{Ee.removeChild(n.stateNode)}catch(c){_e(n,t,c)}break;case 18:Ee!==null&&(it?(e=Ee,sm(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,n.stateNode),nu(e)):sm(Ee,n.stateNode));break;case 4:l=Ee,u=it,Ee=n.stateNode.containerInfo,it=!0,nn(e,t,n),Ee=l,it=u;break;case 0:case 11:case 14:case 15:Oe||_n(2,n,t),Oe||_n(4,n,t),nn(e,t,n);break;case 1:Oe||(Zt(n,t),l=n.stateNode,typeof l.componentWillUnmount=="function"&&gd(n,t,l)),nn(e,t,n);break;case 21:nn(e,t,n);break;case 22:Oe=(l=Oe)||n.memoizedState!==null,nn(e,t,n),Oe=l;break;default:nn(e,t,n)}}function wd(e,t){if(t.memoizedState===null&&(e=t.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{nu(e)}catch(n){_e(t,t.return,n)}}function ip(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return t===null&&(t=e.stateNode=new bd),t;case 22:return e=e.stateNode,t=e._retryCache,t===null&&(t=e._retryCache=new bd),t;default:throw Error(r(435,e.tag))}}function zo(e,t){var n=ip(e);t.forEach(function(l){var u=gp.bind(null,e,l);n.has(l)||(n.add(l),l.then(u,u))})}function mt(e,t){var n=t.deletions;if(n!==null)for(var l=0;l<n.length;l++){var u=n[l],c=e,f=t,g=f;e:for(;g!==null;){switch(g.tag){case 27:if(Tn(g.type)){Ee=g.stateNode,it=!1;break e}break;case 5:Ee=g.stateNode,it=!1;break e;case 3:case 4:Ee=g.stateNode.containerInfo,it=!0;break e}g=g.return}if(Ee===null)throw Error(r(160));Sd(c,f,u),Ee=null,it=!1,c=u.alternate,c!==null&&(c.return=null),u.return=null}if(t.subtreeFlags&13878)for(t=t.child;t!==null;)zd(t,e),t=t.sibling}var Ot=null;function zd(e,t){var n=e.alternate,l=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:mt(t,e),ht(e),l&4&&(_n(3,e,e.return),Za(3,e),_n(5,e,e.return));break;case 1:mt(t,e),ht(e),l&512&&(Oe||n===null||Zt(n,n.return)),l&64&&tn&&(e=e.updateQueue,e!==null&&(l=e.callbacks,l!==null&&(n=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=n===null?l:n.concat(l))));break;case 26:var u=Ot;if(mt(t,e),ht(e),l&512&&(Oe||n===null||Zt(n,n.return)),l&4){var c=n!==null?n.memoizedState:null;if(l=e.memoizedState,n===null)if(l===null)if(e.stateNode===null){e:{l=e.type,n=e.memoizedProps,u=u.ownerDocument||u;t:switch(l){case"title":c=u.getElementsByTagName("title")[0],(!c||c[ra]||c[Ie]||c.namespaceURI==="http://www.w3.org/2000/svg"||c.hasAttribute("itemprop"))&&(c=u.createElement(l),u.head.insertBefore(c,u.querySelector("head > title"))),We(c,l,n),c[Ie]=e,Ye(c),l=c;break e;case"link":var f=ym("link","href",u).get(l+(n.href||""));if(f){for(var g=0;g<f.length;g++)if(c=f[g],c.getAttribute("href")===(n.href==null||n.href===""?null:n.href)&&c.getAttribute("rel")===(n.rel==null?null:n.rel)&&c.getAttribute("title")===(n.title==null?null:n.title)&&c.getAttribute("crossorigin")===(n.crossOrigin==null?null:n.crossOrigin)){f.splice(g,1);break t}}c=u.createElement(l),We(c,l,n),u.head.appendChild(c);break;case"meta":if(f=ym("meta","content",u).get(l+(n.content||""))){for(g=0;g<f.length;g++)if(c=f[g],c.getAttribute("content")===(n.content==null?null:""+n.content)&&c.getAttribute("name")===(n.name==null?null:n.name)&&c.getAttribute("property")===(n.property==null?null:n.property)&&c.getAttribute("http-equiv")===(n.httpEquiv==null?null:n.httpEquiv)&&c.getAttribute("charset")===(n.charSet==null?null:n.charSet)){f.splice(g,1);break t}}c=u.createElement(l),We(c,l,n),u.head.appendChild(c);break;default:throw Error(r(468,l))}c[Ie]=e,Ye(c),l=c}e.stateNode=l}else bm(u,e.type,e.stateNode);else e.stateNode=pm(u,l,e.memoizedProps);else c!==l?(c===null?n.stateNode!==null&&(n=n.stateNode,n.parentNode.removeChild(n)):c.count--,l===null?bm(u,e.type,e.stateNode):pm(u,l,e.memoizedProps)):l===null&&e.stateNode!==null&&xo(e,e.memoizedProps,n.memoizedProps)}break;case 27:mt(t,e),ht(e),l&512&&(Oe||n===null||Zt(n,n.return)),n!==null&&l&4&&xo(e,e.memoizedProps,n.memoizedProps);break;case 5:if(mt(t,e),ht(e),l&512&&(Oe||n===null||Zt(n,n.return)),e.flags&32){u=e.stateNode;try{bl(u,"")}catch(A){_e(e,e.return,A)}}l&4&&e.stateNode!=null&&(u=e.memoizedProps,xo(e,u,n!==null?n.memoizedProps:u)),l&1024&&(wo=!0);break;case 6:if(mt(t,e),ht(e),l&4){if(e.stateNode===null)throw Error(r(162));l=e.memoizedProps,n=e.stateNode;try{n.nodeValue=l}catch(A){_e(e,e.return,A)}}break;case 3:if(_i=null,u=Ot,Ot=bi(t.containerInfo),mt(t,e),Ot=u,ht(e),l&4&&n!==null&&n.memoizedState.isDehydrated)try{nu(t.containerInfo)}catch(A){_e(e,e.return,A)}wo&&(wo=!1,Ed(e));break;case 4:l=Ot,Ot=bi(e.stateNode.containerInfo),mt(t,e),ht(e),Ot=l;break;case 12:mt(t,e),ht(e);break;case 13:mt(t,e),ht(e),e.child.flags&8192&&e.memoizedState!==null!=(n!==null&&n.memoizedState!==null)&&(Mo=kt()),l&4&&(l=e.updateQueue,l!==null&&(e.updateQueue=null,zo(e,l)));break;case 22:u=e.memoizedState!==null;var y=n!==null&&n.memoizedState!==null,z=tn,M=Oe;if(tn=z||u,Oe=M||y,mt(t,e),Oe=M,tn=z,ht(e),l&8192)e:for(t=e.stateNode,t._visibility=u?t._visibility&-2:t._visibility|1,u&&(n===null||y||tn||Oe||el(e)),n=null,t=e;;){if(t.tag===5||t.tag===26){if(n===null){y=n=t;try{if(c=y.stateNode,u)f=c.style,typeof f.setProperty=="function"?f.setProperty("display","none","important"):f.display="none";else{g=y.stateNode;var C=y.memoizedProps.style,N=C!=null&&C.hasOwnProperty("display")?C.display:null;g.style.display=N==null||typeof N=="boolean"?"":(""+N).trim()}}catch(A){_e(y,y.return,A)}}}else if(t.tag===6){if(n===null){y=t;try{y.stateNode.nodeValue=u?"":y.memoizedProps}catch(A){_e(y,y.return,A)}}}else if((t.tag!==22&&t.tag!==23||t.memoizedState===null||t===e)&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;n===t&&(n=null),t=t.return}n===t&&(n=null),t.sibling.return=t.return,t=t.sibling}l&4&&(l=e.updateQueue,l!==null&&(n=l.retryQueue,n!==null&&(l.retryQueue=null,zo(e,n))));break;case 19:mt(t,e),ht(e),l&4&&(l=e.updateQueue,l!==null&&(e.updateQueue=null,zo(e,l)));break;case 30:break;case 21:break;default:mt(t,e),ht(e)}}function ht(e){var t=e.flags;if(t&2){try{for(var n,l=e.return;l!==null;){if(pd(l)){n=l;break}l=l.return}if(n==null)throw Error(r(160));switch(n.tag){case 27:var u=n.stateNode,c=_o(e);ii(e,c,u);break;case 5:var f=n.stateNode;n.flags&32&&(bl(f,""),n.flags&=-33);var g=_o(e);ii(e,g,f);break;case 3:case 4:var y=n.stateNode.containerInfo,z=_o(e);So(e,z,y);break;default:throw Error(r(161))}}catch(M){_e(e,e.return,M)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Ed(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var t=e;Ed(t),t.tag===5&&t.flags&1024&&t.stateNode.reset(),e=e.sibling}}function Sn(e,t){if(t.subtreeFlags&8772)for(t=t.child;t!==null;)xd(e,t.alternate,t),t=t.sibling}function el(e){for(e=e.child;e!==null;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:_n(4,t,t.return),el(t);break;case 1:Zt(t,t.return);var n=t.stateNode;typeof n.componentWillUnmount=="function"&&gd(t,t.return,n),el(t);break;case 27:Ja(t.stateNode);case 26:case 5:Zt(t,t.return),el(t);break;case 22:t.memoizedState===null&&el(t);break;case 30:el(t);break;default:el(t)}e=e.sibling}}function wn(e,t,n){for(n=n&&(t.subtreeFlags&8772)!==0,t=t.child;t!==null;){var l=t.alternate,u=e,c=t,f=c.flags;switch(c.tag){case 0:case 11:case 15:wn(u,c,n),Za(4,c);break;case 1:if(wn(u,c,n),l=c,u=l.stateNode,typeof u.componentDidMount=="function")try{u.componentDidMount()}catch(z){_e(l,l.return,z)}if(l=c,u=l.updateQueue,u!==null){var g=l.stateNode;try{var y=u.shared.hiddenCallbacks;if(y!==null)for(u.shared.hiddenCallbacks=null,u=0;u<y.length;u++)Ps(y[u],g)}catch(z){_e(l,l.return,z)}}n&&f&64&&hd(c),Ha(c,c.return);break;case 27:yd(c);case 26:case 5:wn(u,c,n),n&&l===null&&f&4&&vd(c),Ha(c,c.return);break;case 12:wn(u,c,n);break;case 13:wn(u,c,n),n&&f&4&&wd(u,c);break;case 22:c.memoizedState===null&&wn(u,c,n),Ha(c,c.return);break;case 30:break;default:wn(u,c,n)}t=t.sibling}}function Eo(e,t){var n=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(n=e.memoizedState.cachePool.pool),e=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),e!==n&&(e!=null&&e.refCount++,n!=null&&za(n))}function No(e,t){e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&za(e))}function Ht(e,t,n,l){if(t.subtreeFlags&10256)for(t=t.child;t!==null;)Nd(e,t,n,l),t=t.sibling}function Nd(e,t,n,l){var u=t.flags;switch(t.tag){case 0:case 11:case 15:Ht(e,t,n,l),u&2048&&Za(9,t);break;case 1:Ht(e,t,n,l);break;case 3:Ht(e,t,n,l),u&2048&&(e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&za(e)));break;case 12:if(u&2048){Ht(e,t,n,l),e=t.stateNode;try{var c=t.memoizedProps,f=c.id,g=c.onPostCommit;typeof g=="function"&&g(f,t.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(y){_e(t,t.return,y)}}else Ht(e,t,n,l);break;case 13:Ht(e,t,n,l);break;case 23:break;case 22:c=t.stateNode,f=t.alternate,t.memoizedState!==null?c._visibility&2?Ht(e,t,n,l):Ba(e,t):c._visibility&2?Ht(e,t,n,l):(c._visibility|=2,Zl(e,t,n,l,(t.subtreeFlags&10256)!==0)),u&2048&&Eo(f,t);break;case 24:Ht(e,t,n,l),u&2048&&No(t.alternate,t);break;default:Ht(e,t,n,l)}}function Zl(e,t,n,l,u){for(u=u&&(t.subtreeFlags&10256)!==0,t=t.child;t!==null;){var c=e,f=t,g=n,y=l,z=f.flags;switch(f.tag){case 0:case 11:case 15:Zl(c,f,g,y,u),Za(8,f);break;case 23:break;case 22:var M=f.stateNode;f.memoizedState!==null?M._visibility&2?Zl(c,f,g,y,u):Ba(c,f):(M._visibility|=2,Zl(c,f,g,y,u)),u&&z&2048&&Eo(f.alternate,f);break;case 24:Zl(c,f,g,y,u),u&&z&2048&&No(f.alternate,f);break;default:Zl(c,f,g,y,u)}t=t.sibling}}function Ba(e,t){if(t.subtreeFlags&10256)for(t=t.child;t!==null;){var n=e,l=t,u=l.flags;switch(l.tag){case 22:Ba(n,l),u&2048&&Eo(l.alternate,l);break;case 24:Ba(n,l),u&2048&&No(l.alternate,l);break;default:Ba(n,l)}t=t.sibling}}var La=8192;function Hl(e){if(e.subtreeFlags&La)for(e=e.child;e!==null;)Ad(e),e=e.sibling}function Ad(e){switch(e.tag){case 26:Hl(e),e.flags&La&&e.memoizedState!==null&&Xp(Ot,e.memoizedState,e.memoizedProps);break;case 5:Hl(e);break;case 3:case 4:var t=Ot;Ot=bi(e.stateNode.containerInfo),Hl(e),Ot=t;break;case 22:e.memoizedState===null&&(t=e.alternate,t!==null&&t.memoizedState!==null?(t=La,La=16777216,Hl(e),La=t):Hl(e));break;default:Hl(e)}}function jd(e){var t=e.alternate;if(t!==null&&(e=t.child,e!==null)){t.child=null;do t=e.sibling,e.sibling=null,e=t;while(e!==null)}}function Ga(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var n=0;n<t.length;n++){var l=t[n];Qe=l,Md(l,e)}jd(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)Td(e),e=e.sibling}function Td(e){switch(e.tag){case 0:case 11:case 15:Ga(e),e.flags&2048&&_n(9,e,e.return);break;case 3:Ga(e);break;case 12:Ga(e);break;case 22:var t=e.stateNode;e.memoizedState!==null&&t._visibility&2&&(e.return===null||e.return.tag!==13)?(t._visibility&=-3,ci(e)):Ga(e);break;default:Ga(e)}}function ci(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var n=0;n<t.length;n++){var l=t[n];Qe=l,Md(l,e)}jd(e)}for(e=e.child;e!==null;){switch(t=e,t.tag){case 0:case 11:case 15:_n(8,t,t.return),ci(t);break;case 22:n=t.stateNode,n._visibility&2&&(n._visibility&=-3,ci(t));break;default:ci(t)}e=e.sibling}}function Md(e,t){for(;Qe!==null;){var n=Qe;switch(n.tag){case 0:case 11:case 15:_n(8,n,t);break;case 23:case 22:if(n.memoizedState!==null&&n.memoizedState.cachePool!==null){var l=n.memoizedState.cachePool.pool;l!=null&&l.refCount++}break;case 24:za(n.memoizedState.cache)}if(l=n.child,l!==null)l.return=n,Qe=l;else e:for(n=e;Qe!==null;){l=Qe;var u=l.sibling,c=l.return;if(_d(l),l===n){Qe=null;break e}if(u!==null){u.return=c,Qe=u;break e}Qe=c}}}var cp={getCacheForType:function(e){var t=Pe(Ve),n=t.data.get(e);return n===void 0&&(n=e(),t.data.set(e,n)),n}},op=typeof WeakMap=="function"?WeakMap:Map,me=0,Se=null,ue=null,ce=0,he=0,gt=null,zn=!1,Bl=!1,Ao=!1,ln=0,Te=0,En=0,tl=0,jo=0,Nt=0,Ll=0,qa=null,ct=null,To=!1,Mo=0,oi=1/0,ri=null,Nn=null,Fe=0,An=null,Gl=null,ql=0,Oo=0,Ro=null,Od=null,Ya=0,Co=null;function vt(){if((me&2)!==0&&ce!==0)return ce&-ce;if(T.T!==null){var e=Ml;return e!==0?e:Bo()}return Qr()}function Rd(){Nt===0&&(Nt=(ce&536870912)===0||de?Gr():536870912);var e=Et.current;return e!==null&&(e.flags|=32),Nt}function pt(e,t,n){(e===Se&&(he===2||he===9)||e.cancelPendingCommit!==null)&&(Yl(e,0),jn(e,ce,Nt,!1)),oa(e,n),((me&2)===0||e!==Se)&&(e===Se&&((me&2)===0&&(tl|=n),Te===4&&jn(e,ce,Nt,!1)),Bt(e))}function Cd(e,t,n){if((me&6)!==0)throw Error(r(327));var l=!n&&(t&124)===0&&(t&e.expiredLanes)===0||ca(e,t),u=l?fp(e,t):Uo(e,t,!0),c=l;do{if(u===0){Bl&&!l&&jn(e,t,0,!1);break}else{if(n=e.current.alternate,c&&!rp(n)){u=Uo(e,t,!1),c=!1;continue}if(u===2){if(c=t,e.errorRecoveryDisabledLanes&c)var f=0;else f=e.pendingLanes&-536870913,f=f!==0?f:f&536870912?536870912:0;if(f!==0){t=f;e:{var g=e;u=qa;var y=g.current.memoizedState.isDehydrated;if(y&&(Yl(g,f).flags|=256),f=Uo(g,f,!1),f!==2){if(Ao&&!y){g.errorRecoveryDisabledLanes|=c,tl|=c,u=4;break e}c=ct,ct=u,c!==null&&(ct===null?ct=c:ct.push.apply(ct,c))}u=f}if(c=!1,u!==2)continue}}if(u===1){Yl(e,0),jn(e,t,0,!0);break}e:{switch(l=e,c=u,c){case 0:case 1:throw Error(r(345));case 4:if((t&4194048)!==t)break;case 6:jn(l,t,Nt,!zn);break e;case 2:ct=null;break;case 3:case 5:break;default:throw Error(r(329))}if((t&62914560)===t&&(u=Mo+300-kt(),10<u)){if(jn(l,t,Nt,!zn),xu(l,0,!0)!==0)break e;l.timeoutHandle=om(Dd.bind(null,l,n,ct,ri,To,t,Nt,tl,Ll,zn,c,2,-0,0),u);break e}Dd(l,n,ct,ri,To,t,Nt,tl,Ll,zn,c,0,-0,0)}}break}while(!0);Bt(e)}function Dd(e,t,n,l,u,c,f,g,y,z,M,C,N,A){if(e.timeoutHandle=-1,C=t.subtreeFlags,(C&8192||(C&16785408)===16785408)&&(Wa={stylesheets:null,count:0,unsuspend:Yp},Ad(t),C=Qp(),C!==null)){e.cancelPendingCommit=C(Ld.bind(null,e,t,c,n,l,u,f,g,y,M,1,N,A)),jn(e,c,f,!z);return}Ld(e,t,c,n,l,u,f,g,y)}function rp(e){for(var t=e;;){var n=t.tag;if((n===0||n===11||n===15)&&t.flags&16384&&(n=t.updateQueue,n!==null&&(n=n.stores,n!==null)))for(var l=0;l<n.length;l++){var u=n[l],c=u.getSnapshot;u=u.value;try{if(!ft(c(),u))return!1}catch{return!1}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function jn(e,t,n,l){t&=~jo,t&=~tl,e.suspendedLanes|=t,e.pingedLanes&=~t,l&&(e.warmLanes|=t),l=e.expirationTimes;for(var u=t;0<u;){var c=31-st(u),f=1<<c;l[c]=-1,u&=~f}n!==0&&Yr(e,n,t)}function si(){return(me&6)===0?(Xa(0),!1):!0}function Do(){if(ue!==null){if(he===0)var e=ue.return;else e=ue,$t=$n=null,Fc(e),Ul=null,ka=0,e=ue;for(;e!==null;)md(e.alternate,e),e=e.return;ue=null}}function Yl(e,t){var n=e.timeoutHandle;n!==-1&&(e.timeoutHandle=-1,Ap(n)),n=e.cancelPendingCommit,n!==null&&(e.cancelPendingCommit=null,n()),Do(),Se=e,ue=n=Qt(e.current,null),ce=t,he=0,gt=null,zn=!1,Bl=ca(e,t),Ao=!1,Ll=Nt=jo=tl=En=Te=0,ct=qa=null,To=!1,(t&8)!==0&&(t|=t&32);var l=e.entangledLanes;if(l!==0)for(e=e.entanglements,l&=t;0<l;){var u=31-st(l),c=1<<u;t|=e[u],l&=~c}return ln=t,Ru(),n}function kd(e,t){ne=null,T.H=Wu,t===Na||t===Lu?(t=Ws(),he=3):t===Js?(t=Ws(),he=4):he=t===Pf?8:t!==null&&typeof t=="object"&&typeof t.then=="function"?6:1,gt=t,ue===null&&(Te=1,ni(e,_t(t,e.current)))}function Ud(){var e=T.H;return T.H=Wu,e===null?Wu:e}function Vd(){var e=T.A;return T.A=cp,e}function ko(){Te=4,zn||(ce&4194048)!==ce&&Et.current!==null||(Bl=!0),(En&134217727)===0&&(tl&134217727)===0||Se===null||jn(Se,ce,Nt,!1)}function Uo(e,t,n){var l=me;me|=2;var u=Ud(),c=Vd();(Se!==e||ce!==t)&&(ri=null,Yl(e,t)),t=!1;var f=Te;e:do try{if(he!==0&&ue!==null){var g=ue,y=gt;switch(he){case 8:Do(),f=6;break e;case 3:case 2:case 9:case 6:Et.current===null&&(t=!0);var z=he;if(he=0,gt=null,Xl(e,g,y,z),n&&Bl){f=0;break e}break;default:z=he,he=0,gt=null,Xl(e,g,y,z)}}sp(),f=Te;break}catch(M){kd(e,M)}while(!0);return t&&e.shellSuspendCounter++,$t=$n=null,me=l,T.H=u,T.A=c,ue===null&&(Se=null,ce=0,Ru()),f}function sp(){for(;ue!==null;)Zd(ue)}function fp(e,t){var n=me;me|=2;var l=Ud(),u=Vd();Se!==e||ce!==t?(ri=null,oi=kt()+500,Yl(e,t)):Bl=ca(e,t);e:do try{if(he!==0&&ue!==null){t=ue;var c=gt;t:switch(he){case 1:he=0,gt=null,Xl(e,t,c,1);break;case 2:case 9:if($s(c)){he=0,gt=null,Hd(t);break}t=function(){he!==2&&he!==9||Se!==e||(he=7),Bt(e)},c.then(t,t);break e;case 3:he=7;break e;case 4:he=5;break e;case 7:$s(c)?(he=0,gt=null,Hd(t)):(he=0,gt=null,Xl(e,t,c,7));break;case 5:var f=null;switch(ue.tag){case 26:f=ue.memoizedState;case 5:case 27:var g=ue;if(!f||xm(f)){he=0,gt=null;var y=g.sibling;if(y!==null)ue=y;else{var z=g.return;z!==null?(ue=z,fi(z)):ue=null}break t}}he=0,gt=null,Xl(e,t,c,5);break;case 6:he=0,gt=null,Xl(e,t,c,6);break;case 8:Do(),Te=6;break e;default:throw Error(r(462))}}dp();break}catch(M){kd(e,M)}while(!0);return $t=$n=null,T.H=l,T.A=u,me=n,ue!==null?0:(Se=null,ce=0,Ru(),Te)}function dp(){for(;ue!==null&&!Dg();)Zd(ue)}function Zd(e){var t=fd(e.alternate,e,ln);e.memoizedProps=e.pendingProps,t===null?fi(e):ue=t}function Hd(e){var t=e,n=t.alternate;switch(t.tag){case 15:case 0:t=ud(n,t,t.pendingProps,t.type,void 0,ce);break;case 11:t=ud(n,t,t.pendingProps,t.type.render,t.ref,ce);break;case 5:Fc(t);default:md(n,t),t=ue=Hs(t,ln),t=fd(n,t,ln)}e.memoizedProps=e.pendingProps,t===null?fi(e):ue=t}function Xl(e,t,n,l){$t=$n=null,Fc(t),Ul=null,ka=0;var u=t.return;try{if(tp(e,u,t,n,ce)){Te=1,ni(e,_t(n,e.current)),ue=null;return}}catch(c){if(u!==null)throw ue=u,c;Te=1,ni(e,_t(n,e.current)),ue=null;return}t.flags&32768?(de||l===1?e=!0:Bl||(ce&536870912)!==0?e=!1:(zn=e=!0,(l===2||l===9||l===3||l===6)&&(l=Et.current,l!==null&&l.tag===13&&(l.flags|=16384))),Bd(t,e)):fi(t)}function fi(e){var t=e;do{if((t.flags&32768)!==0){Bd(t,zn);return}e=t.return;var n=lp(t.alternate,t,ln);if(n!==null){ue=n;return}if(t=t.sibling,t!==null){ue=t;return}ue=t=e}while(t!==null);Te===0&&(Te=5)}function Bd(e,t){do{var n=ap(e.alternate,e);if(n!==null){n.flags&=32767,ue=n;return}if(n=e.return,n!==null&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!t&&(e=e.sibling,e!==null)){ue=e;return}ue=e=n}while(e!==null);Te=6,ue=null}function Ld(e,t,n,l,u,c,f,g,y){e.cancelPendingCommit=null;do di();while(Fe!==0);if((me&6)!==0)throw Error(r(327));if(t!==null){if(t===e.current)throw Error(r(177));if(c=t.lanes|t.childLanes,c|=Ec,Yg(e,n,c,f,g,y),e===Se&&(ue=Se=null,ce=0),Gl=t,An=e,ql=n,Oo=c,Ro=u,Od=l,(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?(e.callbackNode=null,e.callbackPriority=0,vp(pu,function(){return Qd(),null})):(e.callbackNode=null,e.callbackPriority=0),l=(t.flags&13878)!==0,(t.subtreeFlags&13878)!==0||l){l=T.T,T.T=null,u=H.p,H.p=2,f=me,me|=4;try{up(e,t,n)}finally{me=f,H.p=u,T.T=l}}Fe=1,Gd(),qd(),Yd()}}function Gd(){if(Fe===1){Fe=0;var e=An,t=Gl,n=(t.flags&13878)!==0;if((t.subtreeFlags&13878)!==0||n){n=T.T,T.T=null;var l=H.p;H.p=2;var u=me;me|=4;try{zd(t,e);var c=Jo,f=Ts(e.containerInfo),g=c.focusedElem,y=c.selectionRange;if(f!==g&&g&&g.ownerDocument&&js(g.ownerDocument.documentElement,g)){if(y!==null&&xc(g)){var z=y.start,M=y.end;if(M===void 0&&(M=z),"selectionStart"in g)g.selectionStart=z,g.selectionEnd=Math.min(M,g.value.length);else{var C=g.ownerDocument||document,N=C&&C.defaultView||window;if(N.getSelection){var A=N.getSelection(),P=g.textContent.length,W=Math.min(y.start,P),be=y.end===void 0?W:Math.min(y.end,P);!A.extend&&W>be&&(f=be,be=W,W=f);var S=As(g,W),_=As(g,be);if(S&&_&&(A.rangeCount!==1||A.anchorNode!==S.node||A.anchorOffset!==S.offset||A.focusNode!==_.node||A.focusOffset!==_.offset)){var w=C.createRange();w.setStart(S.node,S.offset),A.removeAllRanges(),W>be?(A.addRange(w),A.extend(_.node,_.offset)):(w.setEnd(_.node,_.offset),A.addRange(w))}}}}for(C=[],A=g;A=A.parentNode;)A.nodeType===1&&C.push({element:A,left:A.scrollLeft,top:A.scrollTop});for(typeof g.focus=="function"&&g.focus(),g=0;g<C.length;g++){var O=C[g];O.element.scrollLeft=O.left,O.element.scrollTop=O.top}}zi=!!Ko,Jo=Ko=null}finally{me=u,H.p=l,T.T=n}}e.current=t,Fe=2}}function qd(){if(Fe===2){Fe=0;var e=An,t=Gl,n=(t.flags&8772)!==0;if((t.subtreeFlags&8772)!==0||n){n=T.T,T.T=null;var l=H.p;H.p=2;var u=me;me|=4;try{xd(e,t.alternate,t)}finally{me=u,H.p=l,T.T=n}}Fe=3}}function Yd(){if(Fe===4||Fe===3){Fe=0,kg();var e=An,t=Gl,n=ql,l=Od;(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?Fe=5:(Fe=0,Gl=An=null,Xd(e,e.pendingLanes));var u=e.pendingLanes;if(u===0&&(Nn=null),Pi(n),t=t.stateNode,rt&&typeof rt.onCommitFiberRoot=="function")try{rt.onCommitFiberRoot(ia,t,void 0,(t.current.flags&128)===128)}catch{}if(l!==null){t=T.T,u=H.p,H.p=2,T.T=null;try{for(var c=e.onRecoverableError,f=0;f<l.length;f++){var g=l[f];c(g.value,{componentStack:g.stack})}}finally{T.T=t,H.p=u}}(ql&3)!==0&&di(),Bt(e),u=e.pendingLanes,(n&4194090)!==0&&(u&42)!==0?e===Co?Ya++:(Ya=0,Co=e):Ya=0,Xa(0)}}function Xd(e,t){(e.pooledCacheLanes&=t)===0&&(t=e.pooledCache,t!=null&&(e.pooledCache=null,za(t)))}function di(e){return Gd(),qd(),Yd(),Qd()}function Qd(){if(Fe!==5)return!1;var e=An,t=Oo;Oo=0;var n=Pi(ql),l=T.T,u=H.p;try{H.p=32>n?32:n,T.T=null,n=Ro,Ro=null;var c=An,f=ql;if(Fe=0,Gl=An=null,ql=0,(me&6)!==0)throw Error(r(331));var g=me;if(me|=4,Td(c.current),Nd(c,c.current,f,n),me=g,Xa(0,!1),rt&&typeof rt.onPostCommitFiberRoot=="function")try{rt.onPostCommitFiberRoot(ia,c)}catch{}return!0}finally{H.p=u,T.T=l,Xd(e,t)}}function Kd(e,t,n){t=_t(n,t),t=so(e.stateNode,t,2),e=pn(e,t,2),e!==null&&(oa(e,2),Bt(e))}function _e(e,t,n){if(e.tag===3)Kd(e,e,n);else for(;t!==null;){if(t.tag===3){Kd(t,e,n);break}else if(t.tag===1){var l=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof l.componentDidCatch=="function"&&(Nn===null||!Nn.has(l))){e=_t(n,e),n=Wf(2),l=pn(t,n,2),l!==null&&(If(n,l,t,e),oa(l,2),Bt(l));break}}t=t.return}}function Vo(e,t,n){var l=e.pingCache;if(l===null){l=e.pingCache=new op;var u=new Set;l.set(t,u)}else u=l.get(t),u===void 0&&(u=new Set,l.set(t,u));u.has(n)||(Ao=!0,u.add(n),e=mp.bind(null,e,t,n),t.then(e,e))}function mp(e,t,n){var l=e.pingCache;l!==null&&l.delete(t),e.pingedLanes|=e.suspendedLanes&n,e.warmLanes&=~n,Se===e&&(ce&n)===n&&(Te===4||Te===3&&(ce&62914560)===ce&&300>kt()-Mo?(me&2)===0&&Yl(e,0):jo|=n,Ll===ce&&(Ll=0)),Bt(e)}function Jd(e,t){t===0&&(t=qr()),e=Nl(e,t),e!==null&&(oa(e,t),Bt(e))}function hp(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Jd(e,n)}function gp(e,t){var n=0;switch(e.tag){case 13:var l=e.stateNode,u=e.memoizedState;u!==null&&(n=u.retryLane);break;case 19:l=e.stateNode;break;case 22:l=e.stateNode._retryCache;break;default:throw Error(r(314))}l!==null&&l.delete(t),Jd(e,n)}function vp(e,t){return $i(e,t)}var mi=null,Ql=null,Zo=!1,hi=!1,Ho=!1,nl=0;function Bt(e){e!==Ql&&e.next===null&&(Ql===null?mi=Ql=e:Ql=Ql.next=e),hi=!0,Zo||(Zo=!0,yp())}function Xa(e,t){if(!Ho&&hi){Ho=!0;do for(var n=!1,l=mi;l!==null;){if(e!==0){var u=l.pendingLanes;if(u===0)var c=0;else{var f=l.suspendedLanes,g=l.pingedLanes;c=(1<<31-st(42|e)+1)-1,c&=u&~(f&~g),c=c&201326741?c&201326741|1:c?c|2:0}c!==0&&(n=!0,Id(l,c))}else c=ce,c=xu(l,l===Se?c:0,l.cancelPendingCommit!==null||l.timeoutHandle!==-1),(c&3)===0||ca(l,c)||(n=!0,Id(l,c));l=l.next}while(n);Ho=!1}}function pp(){$d()}function $d(){hi=Zo=!1;var e=0;nl!==0&&(Np()&&(e=nl),nl=0);for(var t=kt(),n=null,l=mi;l!==null;){var u=l.next,c=Fd(l,t);c===0?(l.next=null,n===null?mi=u:n.next=u,u===null&&(Ql=n)):(n=l,(e!==0||(c&3)!==0)&&(hi=!0)),l=u}Xa(e)}function Fd(e,t){for(var n=e.suspendedLanes,l=e.pingedLanes,u=e.expirationTimes,c=e.pendingLanes&-62914561;0<c;){var f=31-st(c),g=1<<f,y=u[f];y===-1?((g&n)===0||(g&l)!==0)&&(u[f]=qg(g,t)):y<=t&&(e.expiredLanes|=g),c&=~g}if(t=Se,n=ce,n=xu(e,e===t?n:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),l=e.callbackNode,n===0||e===t&&(he===2||he===9)||e.cancelPendingCommit!==null)return l!==null&&l!==null&&Fi(l),e.callbackNode=null,e.callbackPriority=0;if((n&3)===0||ca(e,n)){if(t=n&-n,t===e.callbackPriority)return t;switch(l!==null&&Fi(l),Pi(n)){case 2:case 8:n=Br;break;case 32:n=pu;break;case 268435456:n=Lr;break;default:n=pu}return l=Wd.bind(null,e),n=$i(n,l),e.callbackPriority=t,e.callbackNode=n,t}return l!==null&&l!==null&&Fi(l),e.callbackPriority=2,e.callbackNode=null,2}function Wd(e,t){if(Fe!==0&&Fe!==5)return e.callbackNode=null,e.callbackPriority=0,null;var n=e.callbackNode;if(di()&&e.callbackNode!==n)return null;var l=ce;return l=xu(e,e===Se?l:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),l===0?null:(Cd(e,l,t),Fd(e,kt()),e.callbackNode!=null&&e.callbackNode===n?Wd.bind(null,e):null)}function Id(e,t){if(di())return null;Cd(e,t,!0)}function yp(){jp(function(){(me&6)!==0?$i(Hr,pp):$d()})}function Bo(){return nl===0&&(nl=Gr()),nl}function Pd(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:Eu(""+e)}function em(e,t){var n=t.ownerDocument.createElement("input");return n.name=t.name,n.value=t.value,e.id&&n.setAttribute("form",e.id),t.parentNode.insertBefore(n,t),e=new FormData(e),n.parentNode.removeChild(n),e}function bp(e,t,n,l,u){if(t==="submit"&&n&&n.stateNode===u){var c=Pd((u[lt]||null).action),f=l.submitter;f&&(t=(t=f[lt]||null)?Pd(t.formAction):f.getAttribute("formAction"),t!==null&&(c=t,f=null));var g=new Tu("action","action",null,l,u);e.push({event:g,listeners:[{instance:null,listener:function(){if(l.defaultPrevented){if(nl!==0){var y=f?em(u,f):new FormData(u);uo(n,{pending:!0,data:y,method:u.method,action:c},null,y)}}else typeof c=="function"&&(g.preventDefault(),y=f?em(u,f):new FormData(u),uo(n,{pending:!0,data:y,method:u.method,action:c},c,y))},currentTarget:u}]})}}for(var Lo=0;Lo<zc.length;Lo++){var Go=zc[Lo],xp=Go.toLowerCase(),_p=Go[0].toUpperCase()+Go.slice(1);Mt(xp,"on"+_p)}Mt(Rs,"onAnimationEnd"),Mt(Cs,"onAnimationIteration"),Mt(Ds,"onAnimationStart"),Mt("dblclick","onDoubleClick"),Mt("focusin","onFocus"),Mt("focusout","onBlur"),Mt(Zv,"onTransitionRun"),Mt(Hv,"onTransitionStart"),Mt(Bv,"onTransitionCancel"),Mt(ks,"onTransitionEnd"),vl("onMouseEnter",["mouseout","mouseover"]),vl("onMouseLeave",["mouseout","mouseover"]),vl("onPointerEnter",["pointerout","pointerover"]),vl("onPointerLeave",["pointerout","pointerover"]),Bn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Bn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Bn("onBeforeInput",["compositionend","keypress","textInput","paste"]),Bn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Bn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Bn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Qa="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Sp=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Qa));function tm(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var l=e[n],u=l.event;l=l.listeners;e:{var c=void 0;if(t)for(var f=l.length-1;0<=f;f--){var g=l[f],y=g.instance,z=g.currentTarget;if(g=g.listener,y!==c&&u.isPropagationStopped())break e;c=g,u.currentTarget=z;try{c(u)}catch(M){ti(M)}u.currentTarget=null,c=y}else for(f=0;f<l.length;f++){if(g=l[f],y=g.instance,z=g.currentTarget,g=g.listener,y!==c&&u.isPropagationStopped())break e;c=g,u.currentTarget=z;try{c(u)}catch(M){ti(M)}u.currentTarget=null,c=y}}}}function ie(e,t){var n=t[ec];n===void 0&&(n=t[ec]=new Set);var l=e+"__bubble";n.has(l)||(nm(t,e,2,!1),n.add(l))}function qo(e,t,n){var l=0;t&&(l|=4),nm(n,e,l,t)}var gi="_reactListening"+Math.random().toString(36).slice(2);function Yo(e){if(!e[gi]){e[gi]=!0,Jr.forEach(function(n){n!=="selectionchange"&&(Sp.has(n)||qo(n,!1,e),qo(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[gi]||(t[gi]=!0,qo("selectionchange",!1,t))}}function nm(e,t,n,l){switch(Nm(t)){case 2:var u=$p;break;case 8:u=Fp;break;default:u=ar}n=u.bind(null,t,n,e),u=void 0,!fc||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(u=!0),l?u!==void 0?e.addEventListener(t,n,{capture:!0,passive:u}):e.addEventListener(t,n,!0):u!==void 0?e.addEventListener(t,n,{passive:u}):e.addEventListener(t,n,!1)}function Xo(e,t,n,l,u){var c=l;if((t&1)===0&&(t&2)===0&&l!==null)e:for(;;){if(l===null)return;var f=l.tag;if(f===3||f===4){var g=l.stateNode.containerInfo;if(g===u)break;if(f===4)for(f=l.return;f!==null;){var y=f.tag;if((y===3||y===4)&&f.stateNode.containerInfo===u)return;f=f.return}for(;g!==null;){if(f=ml(g),f===null)return;if(y=f.tag,y===5||y===6||y===26||y===27){l=c=f;continue e}g=g.parentNode}}l=l.return}os(function(){var z=c,M=rc(n),C=[];e:{var N=Us.get(e);if(N!==void 0){var A=Tu,P=e;switch(e){case"keypress":if(Au(n)===0)break e;case"keydown":case"keyup":A=vv;break;case"focusin":P="focus",A=gc;break;case"focusout":P="blur",A=gc;break;case"beforeblur":case"afterblur":A=gc;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":A=fs;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":A=av;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":A=bv;break;case Rs:case Cs:case Ds:A=cv;break;case ks:A=_v;break;case"scroll":case"scrollend":A=nv;break;case"wheel":A=wv;break;case"copy":case"cut":case"paste":A=rv;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":A=ms;break;case"toggle":case"beforetoggle":A=Ev}var W=(t&4)!==0,be=!W&&(e==="scroll"||e==="scrollend"),S=W?N!==null?N+"Capture":null:N;W=[];for(var _=z,w;_!==null;){var O=_;if(w=O.stateNode,O=O.tag,O!==5&&O!==26&&O!==27||w===null||S===null||(O=fa(_,S),O!=null&&W.push(Ka(_,O,w))),be)break;_=_.return}0<W.length&&(N=new A(N,P,null,n,M),C.push({event:N,listeners:W}))}}if((t&7)===0){e:{if(N=e==="mouseover"||e==="pointerover",A=e==="mouseout"||e==="pointerout",N&&n!==oc&&(P=n.relatedTarget||n.fromElement)&&(ml(P)||P[dl]))break e;if((A||N)&&(N=M.window===M?M:(N=M.ownerDocument)?N.defaultView||N.parentWindow:window,A?(P=n.relatedTarget||n.toElement,A=z,P=P?ml(P):null,P!==null&&(be=m(P),W=P.tag,P!==be||W!==5&&W!==27&&W!==6)&&(P=null)):(A=null,P=z),A!==P)){if(W=fs,O="onMouseLeave",S="onMouseEnter",_="mouse",(e==="pointerout"||e==="pointerover")&&(W=ms,O="onPointerLeave",S="onPointerEnter",_="pointer"),be=A==null?N:sa(A),w=P==null?N:sa(P),N=new W(O,_+"leave",A,n,M),N.target=be,N.relatedTarget=w,O=null,ml(M)===z&&(W=new W(S,_+"enter",P,n,M),W.target=w,W.relatedTarget=be,O=W),be=O,A&&P)t:{for(W=A,S=P,_=0,w=W;w;w=Kl(w))_++;for(w=0,O=S;O;O=Kl(O))w++;for(;0<_-w;)W=Kl(W),_--;for(;0<w-_;)S=Kl(S),w--;for(;_--;){if(W===S||S!==null&&W===S.alternate)break t;W=Kl(W),S=Kl(S)}W=null}else W=null;A!==null&&lm(C,N,A,W,!1),P!==null&&be!==null&&lm(C,be,P,W,!0)}}e:{if(N=z?sa(z):window,A=N.nodeName&&N.nodeName.toLowerCase(),A==="select"||A==="input"&&N.type==="file")var q=_s;else if(bs(N))if(Ss)q=kv;else{q=Cv;var ae=Rv}else A=N.nodeName,!A||A.toLowerCase()!=="input"||N.type!=="checkbox"&&N.type!=="radio"?z&&cc(z.elementType)&&(q=_s):q=Dv;if(q&&(q=q(e,z))){xs(C,q,n,M);break e}ae&&ae(e,N,z),e==="focusout"&&z&&N.type==="number"&&z.memoizedProps.value!=null&&ic(N,"number",N.value)}switch(ae=z?sa(z):window,e){case"focusin":(bs(ae)||ae.contentEditable==="true")&&(wl=ae,_c=z,ba=null);break;case"focusout":ba=_c=wl=null;break;case"mousedown":Sc=!0;break;case"contextmenu":case"mouseup":case"dragend":Sc=!1,Ms(C,n,M);break;case"selectionchange":if(Vv)break;case"keydown":case"keyup":Ms(C,n,M)}var K;if(pc)e:{switch(e){case"compositionstart":var I="onCompositionStart";break e;case"compositionend":I="onCompositionEnd";break e;case"compositionupdate":I="onCompositionUpdate";break e}I=void 0}else Sl?ps(e,n)&&(I="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(I="onCompositionStart");I&&(hs&&n.locale!=="ko"&&(Sl||I!=="onCompositionStart"?I==="onCompositionEnd"&&Sl&&(K=rs()):(mn=M,dc="value"in mn?mn.value:mn.textContent,Sl=!0)),ae=vi(z,I),0<ae.length&&(I=new ds(I,e,null,n,M),C.push({event:I,listeners:ae}),K?I.data=K:(K=ys(n),K!==null&&(I.data=K)))),(K=Av?jv(e,n):Tv(e,n))&&(I=vi(z,"onBeforeInput"),0<I.length&&(ae=new ds("onBeforeInput","beforeinput",null,n,M),C.push({event:ae,listeners:I}),ae.data=K)),bp(C,e,z,n,M)}tm(C,t)})}function Ka(e,t,n){return{instance:e,listener:t,currentTarget:n}}function vi(e,t){for(var n=t+"Capture",l=[];e!==null;){var u=e,c=u.stateNode;if(u=u.tag,u!==5&&u!==26&&u!==27||c===null||(u=fa(e,n),u!=null&&l.unshift(Ka(e,u,c)),u=fa(e,t),u!=null&&l.push(Ka(e,u,c))),e.tag===3)return l;e=e.return}return[]}function Kl(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function lm(e,t,n,l,u){for(var c=t._reactName,f=[];n!==null&&n!==l;){var g=n,y=g.alternate,z=g.stateNode;if(g=g.tag,y!==null&&y===l)break;g!==5&&g!==26&&g!==27||z===null||(y=z,u?(z=fa(n,c),z!=null&&f.unshift(Ka(n,z,y))):u||(z=fa(n,c),z!=null&&f.push(Ka(n,z,y)))),n=n.return}f.length!==0&&e.push({event:t,listeners:f})}var wp=/\r\n?/g,zp=/\u0000|\uFFFD/g;function am(e){return(typeof e=="string"?e:""+e).replace(wp,`
`).replace(zp,"")}function um(e,t){return t=am(t),am(e)===t}function pi(){}function ye(e,t,n,l,u,c){switch(n){case"children":typeof l=="string"?t==="body"||t==="textarea"&&l===""||bl(e,l):(typeof l=="number"||typeof l=="bigint")&&t!=="body"&&bl(e,""+l);break;case"className":Su(e,"class",l);break;case"tabIndex":Su(e,"tabindex",l);break;case"dir":case"role":case"viewBox":case"width":case"height":Su(e,n,l);break;case"style":is(e,l,c);break;case"data":if(t!=="object"){Su(e,"data",l);break}case"src":case"href":if(l===""&&(t!=="a"||n!=="href")){e.removeAttribute(n);break}if(l==null||typeof l=="function"||typeof l=="symbol"||typeof l=="boolean"){e.removeAttribute(n);break}l=Eu(""+l),e.setAttribute(n,l);break;case"action":case"formAction":if(typeof l=="function"){e.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof c=="function"&&(n==="formAction"?(t!=="input"&&ye(e,t,"name",u.name,u,null),ye(e,t,"formEncType",u.formEncType,u,null),ye(e,t,"formMethod",u.formMethod,u,null),ye(e,t,"formTarget",u.formTarget,u,null)):(ye(e,t,"encType",u.encType,u,null),ye(e,t,"method",u.method,u,null),ye(e,t,"target",u.target,u,null)));if(l==null||typeof l=="symbol"||typeof l=="boolean"){e.removeAttribute(n);break}l=Eu(""+l),e.setAttribute(n,l);break;case"onClick":l!=null&&(e.onclick=pi);break;case"onScroll":l!=null&&ie("scroll",e);break;case"onScrollEnd":l!=null&&ie("scrollend",e);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(r(61));if(n=l.__html,n!=null){if(u.children!=null)throw Error(r(60));e.innerHTML=n}}break;case"multiple":e.multiple=l&&typeof l!="function"&&typeof l!="symbol";break;case"muted":e.muted=l&&typeof l!="function"&&typeof l!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(l==null||typeof l=="function"||typeof l=="boolean"||typeof l=="symbol"){e.removeAttribute("xlink:href");break}n=Eu(""+l),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":l!=null&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(n,""+l):e.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":l&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(n,""):e.removeAttribute(n);break;case"capture":case"download":l===!0?e.setAttribute(n,""):l!==!1&&l!=null&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(n,l):e.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":l!=null&&typeof l!="function"&&typeof l!="symbol"&&!isNaN(l)&&1<=l?e.setAttribute(n,l):e.removeAttribute(n);break;case"rowSpan":case"start":l==null||typeof l=="function"||typeof l=="symbol"||isNaN(l)?e.removeAttribute(n):e.setAttribute(n,l);break;case"popover":ie("beforetoggle",e),ie("toggle",e),_u(e,"popover",l);break;case"xlinkActuate":Yt(e,"http://www.w3.org/1999/xlink","xlink:actuate",l);break;case"xlinkArcrole":Yt(e,"http://www.w3.org/1999/xlink","xlink:arcrole",l);break;case"xlinkRole":Yt(e,"http://www.w3.org/1999/xlink","xlink:role",l);break;case"xlinkShow":Yt(e,"http://www.w3.org/1999/xlink","xlink:show",l);break;case"xlinkTitle":Yt(e,"http://www.w3.org/1999/xlink","xlink:title",l);break;case"xlinkType":Yt(e,"http://www.w3.org/1999/xlink","xlink:type",l);break;case"xmlBase":Yt(e,"http://www.w3.org/XML/1998/namespace","xml:base",l);break;case"xmlLang":Yt(e,"http://www.w3.org/XML/1998/namespace","xml:lang",l);break;case"xmlSpace":Yt(e,"http://www.w3.org/XML/1998/namespace","xml:space",l);break;case"is":_u(e,"is",l);break;case"innerText":case"textContent":break;default:(!(2<n.length)||n[0]!=="o"&&n[0]!=="O"||n[1]!=="n"&&n[1]!=="N")&&(n=ev.get(n)||n,_u(e,n,l))}}function Qo(e,t,n,l,u,c){switch(n){case"style":is(e,l,c);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(r(61));if(n=l.__html,n!=null){if(u.children!=null)throw Error(r(60));e.innerHTML=n}}break;case"children":typeof l=="string"?bl(e,l):(typeof l=="number"||typeof l=="bigint")&&bl(e,""+l);break;case"onScroll":l!=null&&ie("scroll",e);break;case"onScrollEnd":l!=null&&ie("scrollend",e);break;case"onClick":l!=null&&(e.onclick=pi);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!$r.hasOwnProperty(n))e:{if(n[0]==="o"&&n[1]==="n"&&(u=n.endsWith("Capture"),t=n.slice(2,u?n.length-7:void 0),c=e[lt]||null,c=c!=null?c[n]:null,typeof c=="function"&&e.removeEventListener(t,c,u),typeof l=="function")){typeof c!="function"&&c!==null&&(n in e?e[n]=null:e.hasAttribute(n)&&e.removeAttribute(n)),e.addEventListener(t,l,u);break e}n in e?e[n]=l:l===!0?e.setAttribute(n,""):_u(e,n,l)}}}function We(e,t,n){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":ie("error",e),ie("load",e);var l=!1,u=!1,c;for(c in n)if(n.hasOwnProperty(c)){var f=n[c];if(f!=null)switch(c){case"src":l=!0;break;case"srcSet":u=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(r(137,t));default:ye(e,t,c,f,n,null)}}u&&ye(e,t,"srcSet",n.srcSet,n,null),l&&ye(e,t,"src",n.src,n,null);return;case"input":ie("invalid",e);var g=c=f=u=null,y=null,z=null;for(l in n)if(n.hasOwnProperty(l)){var M=n[l];if(M!=null)switch(l){case"name":u=M;break;case"type":f=M;break;case"checked":y=M;break;case"defaultChecked":z=M;break;case"value":c=M;break;case"defaultValue":g=M;break;case"children":case"dangerouslySetInnerHTML":if(M!=null)throw Error(r(137,t));break;default:ye(e,t,l,M,n,null)}}ns(e,c,g,y,z,f,u,!1),wu(e);return;case"select":ie("invalid",e),l=f=c=null;for(u in n)if(n.hasOwnProperty(u)&&(g=n[u],g!=null))switch(u){case"value":c=g;break;case"defaultValue":f=g;break;case"multiple":l=g;default:ye(e,t,u,g,n,null)}t=c,n=f,e.multiple=!!l,t!=null?yl(e,!!l,t,!1):n!=null&&yl(e,!!l,n,!0);return;case"textarea":ie("invalid",e),c=u=l=null;for(f in n)if(n.hasOwnProperty(f)&&(g=n[f],g!=null))switch(f){case"value":l=g;break;case"defaultValue":u=g;break;case"children":c=g;break;case"dangerouslySetInnerHTML":if(g!=null)throw Error(r(91));break;default:ye(e,t,f,g,n,null)}as(e,l,u,c),wu(e);return;case"option":for(y in n)if(n.hasOwnProperty(y)&&(l=n[y],l!=null))switch(y){case"selected":e.selected=l&&typeof l!="function"&&typeof l!="symbol";break;default:ye(e,t,y,l,n,null)}return;case"dialog":ie("beforetoggle",e),ie("toggle",e),ie("cancel",e),ie("close",e);break;case"iframe":case"object":ie("load",e);break;case"video":case"audio":for(l=0;l<Qa.length;l++)ie(Qa[l],e);break;case"image":ie("error",e),ie("load",e);break;case"details":ie("toggle",e);break;case"embed":case"source":case"link":ie("error",e),ie("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(z in n)if(n.hasOwnProperty(z)&&(l=n[z],l!=null))switch(z){case"children":case"dangerouslySetInnerHTML":throw Error(r(137,t));default:ye(e,t,z,l,n,null)}return;default:if(cc(t)){for(M in n)n.hasOwnProperty(M)&&(l=n[M],l!==void 0&&Qo(e,t,M,l,n,void 0));return}}for(g in n)n.hasOwnProperty(g)&&(l=n[g],l!=null&&ye(e,t,g,l,n,null))}function Ep(e,t,n,l){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var u=null,c=null,f=null,g=null,y=null,z=null,M=null;for(A in n){var C=n[A];if(n.hasOwnProperty(A)&&C!=null)switch(A){case"checked":break;case"value":break;case"defaultValue":y=C;default:l.hasOwnProperty(A)||ye(e,t,A,null,l,C)}}for(var N in l){var A=l[N];if(C=n[N],l.hasOwnProperty(N)&&(A!=null||C!=null))switch(N){case"type":c=A;break;case"name":u=A;break;case"checked":z=A;break;case"defaultChecked":M=A;break;case"value":f=A;break;case"defaultValue":g=A;break;case"children":case"dangerouslySetInnerHTML":if(A!=null)throw Error(r(137,t));break;default:A!==C&&ye(e,t,N,A,l,C)}}uc(e,f,g,y,z,M,c,u);return;case"select":A=f=g=N=null;for(c in n)if(y=n[c],n.hasOwnProperty(c)&&y!=null)switch(c){case"value":break;case"multiple":A=y;default:l.hasOwnProperty(c)||ye(e,t,c,null,l,y)}for(u in l)if(c=l[u],y=n[u],l.hasOwnProperty(u)&&(c!=null||y!=null))switch(u){case"value":N=c;break;case"defaultValue":g=c;break;case"multiple":f=c;default:c!==y&&ye(e,t,u,c,l,y)}t=g,n=f,l=A,N!=null?yl(e,!!n,N,!1):!!l!=!!n&&(t!=null?yl(e,!!n,t,!0):yl(e,!!n,n?[]:"",!1));return;case"textarea":A=N=null;for(g in n)if(u=n[g],n.hasOwnProperty(g)&&u!=null&&!l.hasOwnProperty(g))switch(g){case"value":break;case"children":break;default:ye(e,t,g,null,l,u)}for(f in l)if(u=l[f],c=n[f],l.hasOwnProperty(f)&&(u!=null||c!=null))switch(f){case"value":N=u;break;case"defaultValue":A=u;break;case"children":break;case"dangerouslySetInnerHTML":if(u!=null)throw Error(r(91));break;default:u!==c&&ye(e,t,f,u,l,c)}ls(e,N,A);return;case"option":for(var P in n)if(N=n[P],n.hasOwnProperty(P)&&N!=null&&!l.hasOwnProperty(P))switch(P){case"selected":e.selected=!1;break;default:ye(e,t,P,null,l,N)}for(y in l)if(N=l[y],A=n[y],l.hasOwnProperty(y)&&N!==A&&(N!=null||A!=null))switch(y){case"selected":e.selected=N&&typeof N!="function"&&typeof N!="symbol";break;default:ye(e,t,y,N,l,A)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var W in n)N=n[W],n.hasOwnProperty(W)&&N!=null&&!l.hasOwnProperty(W)&&ye(e,t,W,null,l,N);for(z in l)if(N=l[z],A=n[z],l.hasOwnProperty(z)&&N!==A&&(N!=null||A!=null))switch(z){case"children":case"dangerouslySetInnerHTML":if(N!=null)throw Error(r(137,t));break;default:ye(e,t,z,N,l,A)}return;default:if(cc(t)){for(var be in n)N=n[be],n.hasOwnProperty(be)&&N!==void 0&&!l.hasOwnProperty(be)&&Qo(e,t,be,void 0,l,N);for(M in l)N=l[M],A=n[M],!l.hasOwnProperty(M)||N===A||N===void 0&&A===void 0||Qo(e,t,M,N,l,A);return}}for(var S in n)N=n[S],n.hasOwnProperty(S)&&N!=null&&!l.hasOwnProperty(S)&&ye(e,t,S,null,l,N);for(C in l)N=l[C],A=n[C],!l.hasOwnProperty(C)||N===A||N==null&&A==null||ye(e,t,C,N,l,A)}var Ko=null,Jo=null;function yi(e){return e.nodeType===9?e:e.ownerDocument}function im(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function cm(e,t){if(e===0)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&t==="foreignObject"?0:e}function $o(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.children=="bigint"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Fo=null;function Np(){var e=window.event;return e&&e.type==="popstate"?e===Fo?!1:(Fo=e,!0):(Fo=null,!1)}var om=typeof setTimeout=="function"?setTimeout:void 0,Ap=typeof clearTimeout=="function"?clearTimeout:void 0,rm=typeof Promise=="function"?Promise:void 0,jp=typeof queueMicrotask=="function"?queueMicrotask:typeof rm<"u"?function(e){return rm.resolve(null).then(e).catch(Tp)}:om;function Tp(e){setTimeout(function(){throw e})}function Tn(e){return e==="head"}function sm(e,t){var n=t,l=0,u=0;do{var c=n.nextSibling;if(e.removeChild(n),c&&c.nodeType===8)if(n=c.data,n==="/$"){if(0<l&&8>l){n=l;var f=e.ownerDocument;if(n&1&&Ja(f.documentElement),n&2&&Ja(f.body),n&4)for(n=f.head,Ja(n),f=n.firstChild;f;){var g=f.nextSibling,y=f.nodeName;f[ra]||y==="SCRIPT"||y==="STYLE"||y==="LINK"&&f.rel.toLowerCase()==="stylesheet"||n.removeChild(f),f=g}}if(u===0){e.removeChild(c),nu(t);return}u--}else n==="$"||n==="$?"||n==="$!"?u++:l=n.charCodeAt(0)-48;else l=0;n=c}while(n);nu(t)}function Wo(e){var t=e.firstChild;for(t&&t.nodeType===10&&(t=t.nextSibling);t;){var n=t;switch(t=t.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":Wo(n),tc(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(n.rel.toLowerCase()==="stylesheet")continue}e.removeChild(n)}}function Mp(e,t,n,l){for(;e.nodeType===1;){var u=n;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!l&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(l){if(!e[ra])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(c=e.getAttribute("rel"),c==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(c!==u.rel||e.getAttribute("href")!==(u.href==null||u.href===""?null:u.href)||e.getAttribute("crossorigin")!==(u.crossOrigin==null?null:u.crossOrigin)||e.getAttribute("title")!==(u.title==null?null:u.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(c=e.getAttribute("src"),(c!==(u.src==null?null:u.src)||e.getAttribute("type")!==(u.type==null?null:u.type)||e.getAttribute("crossorigin")!==(u.crossOrigin==null?null:u.crossOrigin))&&c&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(t==="input"&&e.type==="hidden"){var c=u.name==null?null:""+u.name;if(u.type==="hidden"&&e.getAttribute("name")===c)return e}else return e;if(e=Rt(e.nextSibling),e===null)break}return null}function Op(e,t,n){if(t==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!n||(e=Rt(e.nextSibling),e===null))return null;return e}function Io(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function Rp(e,t){var n=e.ownerDocument;if(e.data!=="$?"||n.readyState==="complete")t();else{var l=function(){t(),n.removeEventListener("DOMContentLoaded",l)};n.addEventListener("DOMContentLoaded",l),e._reactRetry=l}}function Rt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?"||t==="F!"||t==="F")break;if(t==="/$")return null}}return e}var Po=null;function fm(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}function dm(e,t,n){switch(t=yi(n),e){case"html":if(e=t.documentElement,!e)throw Error(r(452));return e;case"head":if(e=t.head,!e)throw Error(r(453));return e;case"body":if(e=t.body,!e)throw Error(r(454));return e;default:throw Error(r(451))}}function Ja(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);tc(e)}var At=new Map,mm=new Set;function bi(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var an=H.d;H.d={f:Cp,r:Dp,D:kp,C:Up,L:Vp,m:Zp,X:Bp,S:Hp,M:Lp};function Cp(){var e=an.f(),t=si();return e||t}function Dp(e){var t=hl(e);t!==null&&t.tag===5&&t.type==="form"?Cf(t):an.r(e)}var Jl=typeof document>"u"?null:document;function hm(e,t,n){var l=Jl;if(l&&typeof t=="string"&&t){var u=xt(t);u='link[rel="'+e+'"][href="'+u+'"]',typeof n=="string"&&(u+='[crossorigin="'+n+'"]'),mm.has(u)||(mm.add(u),e={rel:e,crossOrigin:n,href:t},l.querySelector(u)===null&&(t=l.createElement("link"),We(t,"link",e),Ye(t),l.head.appendChild(t)))}}function kp(e){an.D(e),hm("dns-prefetch",e,null)}function Up(e,t){an.C(e,t),hm("preconnect",e,t)}function Vp(e,t,n){an.L(e,t,n);var l=Jl;if(l&&e&&t){var u='link[rel="preload"][as="'+xt(t)+'"]';t==="image"&&n&&n.imageSrcSet?(u+='[imagesrcset="'+xt(n.imageSrcSet)+'"]',typeof n.imageSizes=="string"&&(u+='[imagesizes="'+xt(n.imageSizes)+'"]')):u+='[href="'+xt(e)+'"]';var c=u;switch(t){case"style":c=$l(e);break;case"script":c=Fl(e)}At.has(c)||(e=x({rel:"preload",href:t==="image"&&n&&n.imageSrcSet?void 0:e,as:t},n),At.set(c,e),l.querySelector(u)!==null||t==="style"&&l.querySelector($a(c))||t==="script"&&l.querySelector(Fa(c))||(t=l.createElement("link"),We(t,"link",e),Ye(t),l.head.appendChild(t)))}}function Zp(e,t){an.m(e,t);var n=Jl;if(n&&e){var l=t&&typeof t.as=="string"?t.as:"script",u='link[rel="modulepreload"][as="'+xt(l)+'"][href="'+xt(e)+'"]',c=u;switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":c=Fl(e)}if(!At.has(c)&&(e=x({rel:"modulepreload",href:e},t),At.set(c,e),n.querySelector(u)===null)){switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(Fa(c)))return}l=n.createElement("link"),We(l,"link",e),Ye(l),n.head.appendChild(l)}}}function Hp(e,t,n){an.S(e,t,n);var l=Jl;if(l&&e){var u=gl(l).hoistableStyles,c=$l(e);t=t||"default";var f=u.get(c);if(!f){var g={loading:0,preload:null};if(f=l.querySelector($a(c)))g.loading=5;else{e=x({rel:"stylesheet",href:e,"data-precedence":t},n),(n=At.get(c))&&er(e,n);var y=f=l.createElement("link");Ye(y),We(y,"link",e),y._p=new Promise(function(z,M){y.onload=z,y.onerror=M}),y.addEventListener("load",function(){g.loading|=1}),y.addEventListener("error",function(){g.loading|=2}),g.loading|=4,xi(f,t,l)}f={type:"stylesheet",instance:f,count:1,state:g},u.set(c,f)}}}function Bp(e,t){an.X(e,t);var n=Jl;if(n&&e){var l=gl(n).hoistableScripts,u=Fl(e),c=l.get(u);c||(c=n.querySelector(Fa(u)),c||(e=x({src:e,async:!0},t),(t=At.get(u))&&tr(e,t),c=n.createElement("script"),Ye(c),We(c,"link",e),n.head.appendChild(c)),c={type:"script",instance:c,count:1,state:null},l.set(u,c))}}function Lp(e,t){an.M(e,t);var n=Jl;if(n&&e){var l=gl(n).hoistableScripts,u=Fl(e),c=l.get(u);c||(c=n.querySelector(Fa(u)),c||(e=x({src:e,async:!0,type:"module"},t),(t=At.get(u))&&tr(e,t),c=n.createElement("script"),Ye(c),We(c,"link",e),n.head.appendChild(c)),c={type:"script",instance:c,count:1,state:null},l.set(u,c))}}function gm(e,t,n,l){var u=(u=jt.current)?bi(u):null;if(!u)throw Error(r(446));switch(e){case"meta":case"title":return null;case"style":return typeof n.precedence=="string"&&typeof n.href=="string"?(t=$l(n.href),n=gl(u).hoistableStyles,l=n.get(t),l||(l={type:"style",instance:null,count:0,state:null},n.set(t,l)),l):{type:"void",instance:null,count:0,state:null};case"link":if(n.rel==="stylesheet"&&typeof n.href=="string"&&typeof n.precedence=="string"){e=$l(n.href);var c=gl(u).hoistableStyles,f=c.get(e);if(f||(u=u.ownerDocument||u,f={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},c.set(e,f),(c=u.querySelector($a(e)))&&!c._p&&(f.instance=c,f.state.loading=5),At.has(e)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},At.set(e,n),c||Gp(u,e,n,f.state))),t&&l===null)throw Error(r(528,""));return f}if(t&&l!==null)throw Error(r(529,""));return null;case"script":return t=n.async,n=n.src,typeof n=="string"&&t&&typeof t!="function"&&typeof t!="symbol"?(t=Fl(n),n=gl(u).hoistableScripts,l=n.get(t),l||(l={type:"script",instance:null,count:0,state:null},n.set(t,l)),l):{type:"void",instance:null,count:0,state:null};default:throw Error(r(444,e))}}function $l(e){return'href="'+xt(e)+'"'}function $a(e){return'link[rel="stylesheet"]['+e+"]"}function vm(e){return x({},e,{"data-precedence":e.precedence,precedence:null})}function Gp(e,t,n,l){e.querySelector('link[rel="preload"][as="style"]['+t+"]")?l.loading=1:(t=e.createElement("link"),l.preload=t,t.addEventListener("load",function(){return l.loading|=1}),t.addEventListener("error",function(){return l.loading|=2}),We(t,"link",n),Ye(t),e.head.appendChild(t))}function Fl(e){return'[src="'+xt(e)+'"]'}function Fa(e){return"script[async]"+e}function pm(e,t,n){if(t.count++,t.instance===null)switch(t.type){case"style":var l=e.querySelector('style[data-href~="'+xt(n.href)+'"]');if(l)return t.instance=l,Ye(l),l;var u=x({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return l=(e.ownerDocument||e).createElement("style"),Ye(l),We(l,"style",u),xi(l,n.precedence,e),t.instance=l;case"stylesheet":u=$l(n.href);var c=e.querySelector($a(u));if(c)return t.state.loading|=4,t.instance=c,Ye(c),c;l=vm(n),(u=At.get(u))&&er(l,u),c=(e.ownerDocument||e).createElement("link"),Ye(c);var f=c;return f._p=new Promise(function(g,y){f.onload=g,f.onerror=y}),We(c,"link",l),t.state.loading|=4,xi(c,n.precedence,e),t.instance=c;case"script":return c=Fl(n.src),(u=e.querySelector(Fa(c)))?(t.instance=u,Ye(u),u):(l=n,(u=At.get(c))&&(l=x({},n),tr(l,u)),e=e.ownerDocument||e,u=e.createElement("script"),Ye(u),We(u,"link",l),e.head.appendChild(u),t.instance=u);case"void":return null;default:throw Error(r(443,t.type))}else t.type==="stylesheet"&&(t.state.loading&4)===0&&(l=t.instance,t.state.loading|=4,xi(l,n.precedence,e));return t.instance}function xi(e,t,n){for(var l=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),u=l.length?l[l.length-1]:null,c=u,f=0;f<l.length;f++){var g=l[f];if(g.dataset.precedence===t)c=g;else if(c!==u)break}c?c.parentNode.insertBefore(e,c.nextSibling):(t=n.nodeType===9?n.head:n,t.insertBefore(e,t.firstChild))}function er(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.title==null&&(e.title=t.title)}function tr(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.integrity==null&&(e.integrity=t.integrity)}var _i=null;function ym(e,t,n){if(_i===null){var l=new Map,u=_i=new Map;u.set(n,l)}else u=_i,l=u.get(n),l||(l=new Map,u.set(n,l));if(l.has(e))return l;for(l.set(e,null),n=n.getElementsByTagName(e),u=0;u<n.length;u++){var c=n[u];if(!(c[ra]||c[Ie]||e==="link"&&c.getAttribute("rel")==="stylesheet")&&c.namespaceURI!=="http://www.w3.org/2000/svg"){var f=c.getAttribute(t)||"";f=e+f;var g=l.get(f);g?g.push(c):l.set(f,[c])}}return l}function bm(e,t,n){e=e.ownerDocument||e,e.head.insertBefore(n,t==="title"?e.querySelector("head > title"):null)}function qp(e,t,n){if(n===1||t.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof t.precedence!="string"||typeof t.href!="string"||t.href==="")break;return!0;case"link":if(typeof t.rel!="string"||typeof t.href!="string"||t.href===""||t.onLoad||t.onError)break;switch(t.rel){case"stylesheet":return e=t.disabled,typeof t.precedence=="string"&&e==null;default:return!0}case"script":if(t.async&&typeof t.async!="function"&&typeof t.async!="symbol"&&!t.onLoad&&!t.onError&&t.src&&typeof t.src=="string")return!0}return!1}function xm(e){return!(e.type==="stylesheet"&&(e.state.loading&3)===0)}var Wa=null;function Yp(){}function Xp(e,t,n){if(Wa===null)throw Error(r(475));var l=Wa;if(t.type==="stylesheet"&&(typeof n.media!="string"||matchMedia(n.media).matches!==!1)&&(t.state.loading&4)===0){if(t.instance===null){var u=$l(n.href),c=e.querySelector($a(u));if(c){e=c._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(l.count++,l=Si.bind(l),e.then(l,l)),t.state.loading|=4,t.instance=c,Ye(c);return}c=e.ownerDocument||e,n=vm(n),(u=At.get(u))&&er(n,u),c=c.createElement("link"),Ye(c);var f=c;f._p=new Promise(function(g,y){f.onload=g,f.onerror=y}),We(c,"link",n),t.instance=c}l.stylesheets===null&&(l.stylesheets=new Map),l.stylesheets.set(t,e),(e=t.state.preload)&&(t.state.loading&3)===0&&(l.count++,t=Si.bind(l),e.addEventListener("load",t),e.addEventListener("error",t))}}function Qp(){if(Wa===null)throw Error(r(475));var e=Wa;return e.stylesheets&&e.count===0&&nr(e,e.stylesheets),0<e.count?function(t){var n=setTimeout(function(){if(e.stylesheets&&nr(e,e.stylesheets),e.unsuspend){var l=e.unsuspend;e.unsuspend=null,l()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(n)}}:null}function Si(){if(this.count--,this.count===0){if(this.stylesheets)nr(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var wi=null;function nr(e,t){e.stylesheets=null,e.unsuspend!==null&&(e.count++,wi=new Map,t.forEach(Kp,e),wi=null,Si.call(e))}function Kp(e,t){if(!(t.state.loading&4)){var n=wi.get(e);if(n)var l=n.get(null);else{n=new Map,wi.set(e,n);for(var u=e.querySelectorAll("link[data-precedence],style[data-precedence]"),c=0;c<u.length;c++){var f=u[c];(f.nodeName==="LINK"||f.getAttribute("media")!=="not all")&&(n.set(f.dataset.precedence,f),l=f)}l&&n.set(null,l)}u=t.instance,f=u.getAttribute("data-precedence"),c=n.get(f)||l,c===l&&n.set(null,u),n.set(f,u),this.count++,l=Si.bind(this),u.addEventListener("load",l),u.addEventListener("error",l),c?c.parentNode.insertBefore(u,c.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(u,e.firstChild)),t.state.loading|=4}}var Ia={$$typeof:R,Provider:null,Consumer:null,_currentValue:U,_currentValue2:U,_threadCount:0};function Jp(e,t,n,l,u,c,f,g){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Wi(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Wi(0),this.hiddenUpdates=Wi(null),this.identifierPrefix=l,this.onUncaughtError=u,this.onCaughtError=c,this.onRecoverableError=f,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=g,this.incompleteTransitions=new Map}function _m(e,t,n,l,u,c,f,g,y,z,M,C){return e=new Jp(e,t,n,f,g,y,z,C),t=1,c===!0&&(t|=24),c=dt(3,null,null,t),e.current=c,c.stateNode=e,t=Uc(),t.refCount++,e.pooledCache=t,t.refCount++,c.memoizedState={element:l,isDehydrated:n,cache:t},Bc(c),e}function Sm(e){return e?(e=Al,e):Al}function wm(e,t,n,l,u,c){u=Sm(u),l.context===null?l.context=u:l.pendingContext=u,l=vn(t),l.payload={element:n},c=c===void 0?null:c,c!==null&&(l.callback=c),n=pn(e,l,t),n!==null&&(pt(n,e,t),ja(n,e,t))}function zm(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function lr(e,t){zm(e,t),(e=e.alternate)&&zm(e,t)}function Em(e){if(e.tag===13){var t=Nl(e,67108864);t!==null&&pt(t,e,67108864),lr(e,67108864)}}var zi=!0;function $p(e,t,n,l){var u=T.T;T.T=null;var c=H.p;try{H.p=2,ar(e,t,n,l)}finally{H.p=c,T.T=u}}function Fp(e,t,n,l){var u=T.T;T.T=null;var c=H.p;try{H.p=8,ar(e,t,n,l)}finally{H.p=c,T.T=u}}function ar(e,t,n,l){if(zi){var u=ur(l);if(u===null)Xo(e,t,l,Ei,n),Am(e,l);else if(Ip(u,e,t,n,l))l.stopPropagation();else if(Am(e,l),t&4&&-1<Wp.indexOf(e)){for(;u!==null;){var c=hl(u);if(c!==null)switch(c.tag){case 3:if(c=c.stateNode,c.current.memoizedState.isDehydrated){var f=Hn(c.pendingLanes);if(f!==0){var g=c;for(g.pendingLanes|=2,g.entangledLanes|=2;f;){var y=1<<31-st(f);g.entanglements[1]|=y,f&=~y}Bt(c),(me&6)===0&&(oi=kt()+500,Xa(0))}}break;case 13:g=Nl(c,2),g!==null&&pt(g,c,2),si(),lr(c,2)}if(c=ur(l),c===null&&Xo(e,t,l,Ei,n),c===u)break;u=c}u!==null&&l.stopPropagation()}else Xo(e,t,l,null,n)}}function ur(e){return e=rc(e),ir(e)}var Ei=null;function ir(e){if(Ei=null,e=ml(e),e!==null){var t=m(e);if(t===null)e=null;else{var n=t.tag;if(n===13){if(e=d(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return Ei=e,null}function Nm(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(Ug()){case Hr:return 2;case Br:return 8;case pu:case Vg:return 32;case Lr:return 268435456;default:return 32}default:return 32}}var cr=!1,Mn=null,On=null,Rn=null,Pa=new Map,eu=new Map,Cn=[],Wp="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function Am(e,t){switch(e){case"focusin":case"focusout":Mn=null;break;case"dragenter":case"dragleave":On=null;break;case"mouseover":case"mouseout":Rn=null;break;case"pointerover":case"pointerout":Pa.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":eu.delete(t.pointerId)}}function tu(e,t,n,l,u,c){return e===null||e.nativeEvent!==c?(e={blockedOn:t,domEventName:n,eventSystemFlags:l,nativeEvent:c,targetContainers:[u]},t!==null&&(t=hl(t),t!==null&&Em(t)),e):(e.eventSystemFlags|=l,t=e.targetContainers,u!==null&&t.indexOf(u)===-1&&t.push(u),e)}function Ip(e,t,n,l,u){switch(t){case"focusin":return Mn=tu(Mn,e,t,n,l,u),!0;case"dragenter":return On=tu(On,e,t,n,l,u),!0;case"mouseover":return Rn=tu(Rn,e,t,n,l,u),!0;case"pointerover":var c=u.pointerId;return Pa.set(c,tu(Pa.get(c)||null,e,t,n,l,u)),!0;case"gotpointercapture":return c=u.pointerId,eu.set(c,tu(eu.get(c)||null,e,t,n,l,u)),!0}return!1}function jm(e){var t=ml(e.target);if(t!==null){var n=m(t);if(n!==null){if(t=n.tag,t===13){if(t=d(n),t!==null){e.blockedOn=t,Xg(e.priority,function(){if(n.tag===13){var l=vt();l=Ii(l);var u=Nl(n,l);u!==null&&pt(u,n,l),lr(n,l)}});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Ni(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=ur(e.nativeEvent);if(n===null){n=e.nativeEvent;var l=new n.constructor(n.type,n);oc=l,n.target.dispatchEvent(l),oc=null}else return t=hl(n),t!==null&&Em(t),e.blockedOn=n,!1;t.shift()}return!0}function Tm(e,t,n){Ni(e)&&n.delete(t)}function Pp(){cr=!1,Mn!==null&&Ni(Mn)&&(Mn=null),On!==null&&Ni(On)&&(On=null),Rn!==null&&Ni(Rn)&&(Rn=null),Pa.forEach(Tm),eu.forEach(Tm)}function Ai(e,t){e.blockedOn===t&&(e.blockedOn=null,cr||(cr=!0,a.unstable_scheduleCallback(a.unstable_NormalPriority,Pp)))}var ji=null;function Mm(e){ji!==e&&(ji=e,a.unstable_scheduleCallback(a.unstable_NormalPriority,function(){ji===e&&(ji=null);for(var t=0;t<e.length;t+=3){var n=e[t],l=e[t+1],u=e[t+2];if(typeof l!="function"){if(ir(l||n)===null)continue;break}var c=hl(n);c!==null&&(e.splice(t,3),t-=3,uo(c,{pending:!0,data:u,method:n.method,action:l},l,u))}}))}function nu(e){function t(y){return Ai(y,e)}Mn!==null&&Ai(Mn,e),On!==null&&Ai(On,e),Rn!==null&&Ai(Rn,e),Pa.forEach(t),eu.forEach(t);for(var n=0;n<Cn.length;n++){var l=Cn[n];l.blockedOn===e&&(l.blockedOn=null)}for(;0<Cn.length&&(n=Cn[0],n.blockedOn===null);)jm(n),n.blockedOn===null&&Cn.shift();if(n=(e.ownerDocument||e).$$reactFormReplay,n!=null)for(l=0;l<n.length;l+=3){var u=n[l],c=n[l+1],f=u[lt]||null;if(typeof c=="function")f||Mm(n);else if(f){var g=null;if(c&&c.hasAttribute("formAction")){if(u=c,f=c[lt]||null)g=f.formAction;else if(ir(u)!==null)continue}else g=f.action;typeof g=="function"?n[l+1]=g:(n.splice(l,3),l-=3),Mm(n)}}}function or(e){this._internalRoot=e}Ti.prototype.render=or.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(r(409));var n=t.current,l=vt();wm(n,l,e,t,null,null)},Ti.prototype.unmount=or.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;wm(e.current,2,null,e,null,null),si(),t[dl]=null}};function Ti(e){this._internalRoot=e}Ti.prototype.unstable_scheduleHydration=function(e){if(e){var t=Qr();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Cn.length&&t!==0&&t<Cn[n].priority;n++);Cn.splice(n,0,e),n===0&&jm(e)}};var Om=i.version;if(Om!=="19.1.1")throw Error(r(527,Om,"19.1.1"));H.findDOMNode=function(e){var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(r(188)):(e=Object.keys(e).join(","),Error(r(268,e)));return e=p(t),e=e!==null?b(e):null,e=e===null?null:e.stateNode,e};var ey={bundleType:0,version:"19.1.1",rendererPackageName:"react-dom",currentDispatcherRef:T,reconcilerVersion:"19.1.1"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Mi=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Mi.isDisabled&&Mi.supportsFiber)try{ia=Mi.inject(ey),rt=Mi}catch{}}return au.createRoot=function(e,t){if(!s(e))throw Error(r(299));var n=!1,l="",u=Kf,c=Jf,f=$f,g=null;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(l=t.identifierPrefix),t.onUncaughtError!==void 0&&(u=t.onUncaughtError),t.onCaughtError!==void 0&&(c=t.onCaughtError),t.onRecoverableError!==void 0&&(f=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(g=t.unstable_transitionCallbacks)),t=_m(e,1,!1,null,null,n,l,u,c,f,g,null),e[dl]=t.current,Yo(e),new or(t)},au.hydrateRoot=function(e,t,n){if(!s(e))throw Error(r(299));var l=!1,u="",c=Kf,f=Jf,g=$f,y=null,z=null;return n!=null&&(n.unstable_strictMode===!0&&(l=!0),n.identifierPrefix!==void 0&&(u=n.identifierPrefix),n.onUncaughtError!==void 0&&(c=n.onUncaughtError),n.onCaughtError!==void 0&&(f=n.onCaughtError),n.onRecoverableError!==void 0&&(g=n.onRecoverableError),n.unstable_transitionCallbacks!==void 0&&(y=n.unstable_transitionCallbacks),n.formState!==void 0&&(z=n.formState)),t=_m(e,1,!0,t,n??null,l,u,c,f,g,y,z),t.context=Sm(null),n=t.current,l=vt(),l=Ii(l),u=vn(l),u.callback=null,pn(n,u,l),n=l,t.current.lanes=n,oa(t,n),Bt(t),e[dl]=t.current,Yo(e),new Ti(t)},au.version="19.1.1",au}var Zm;function gy(){if(Zm)return sr.exports;Zm=1;function a(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(a)}catch(i){console.error(i)}}return a(),sr.exports=hy(),sr.exports}var vy=gy();function Hm(a,i){if(typeof a=="function")return a(i);a!=null&&(a.current=i)}function Sh(...a){return i=>{let o=!1;const r=a.map(s=>{const m=Hm(s,i);return!o&&typeof m=="function"&&(o=!0),m});if(o)return()=>{for(let s=0;s<r.length;s++){const m=r[s];typeof m=="function"?m():Hm(a[s],null)}}}}function Zi(...a){return k.useCallback(Sh(...a),a)}function Hi(a){const i=yy(a),o=k.forwardRef((r,s)=>{const{children:m,...d}=r,v=k.Children.toArray(m),p=v.find(xy);if(p){const b=p.props.children,x=v.map(E=>E===p?k.Children.count(b)>1?k.Children.only(null):k.isValidElement(b)?b.props.children:null:E);return h.jsx(i,{...d,ref:s,children:k.isValidElement(b)?k.cloneElement(b,void 0,x):null})}return h.jsx(i,{...d,ref:s,children:m})});return o.displayName=`${a}.Slot`,o}var py=Hi("Slot");function yy(a){const i=k.forwardRef((o,r)=>{const{children:s,...m}=o;if(k.isValidElement(s)){const d=Sy(s),v=_y(m,s.props);return s.type!==k.Fragment&&(v.ref=r?Sh(r,d):d),k.cloneElement(s,v)}return k.Children.count(s)>1?k.Children.only(null):null});return i.displayName=`${a}.SlotClone`,i}var by=Symbol("radix.slottable");function xy(a){return k.isValidElement(a)&&typeof a.type=="function"&&"__radixId"in a.type&&a.type.__radixId===by}function _y(a,i){const o={...i};for(const r in i){const s=a[r],m=i[r];/^on[A-Z]/.test(r)?s&&m?o[r]=(...v)=>{const p=m(...v);return s(...v),p}:s&&(o[r]=s):r==="style"?o[r]={...s,...m}:r==="className"&&(o[r]=[s,m].filter(Boolean).join(" "))}return{...a,...o}}function Sy(a){let i=Object.getOwnPropertyDescriptor(a.props,"ref")?.get,o=i&&"isReactWarning"in i&&i.isReactWarning;return o?a.ref:(i=Object.getOwnPropertyDescriptor(a,"ref")?.get,o=i&&"isReactWarning"in i&&i.isReactWarning,o?a.props.ref:a.props.ref||a.ref)}function wh(a){var i,o,r="";if(typeof a=="string"||typeof a=="number")r+=a;else if(typeof a=="object")if(Array.isArray(a)){var s=a.length;for(i=0;i<s;i++)a[i]&&(o=wh(a[i]))&&(r&&(r+=" "),r+=o)}else for(o in a)a[o]&&(r&&(r+=" "),r+=o);return r}function zh(){for(var a,i,o=0,r="",s=arguments.length;o<s;o++)(a=arguments[o])&&(i=wh(a))&&(r&&(r+=" "),r+=i);return r}const Bm=a=>typeof a=="boolean"?`${a}`:a===0?"0":a,Lm=zh,wy=(a,i)=>o=>{var r;if(i?.variants==null)return Lm(a,o?.class,o?.className);const{variants:s,defaultVariants:m}=i,d=Object.keys(s).map(b=>{const x=o?.[b],E=m?.[b];if(x===null)return null;const j=Bm(x)||Bm(E);return s[b][j]}),v=o&&Object.entries(o).reduce((b,x)=>{let[E,j]=x;return j===void 0||(b[E]=j),b},{}),p=i==null||(r=i.compoundVariants)===null||r===void 0?void 0:r.reduce((b,x)=>{let{class:E,className:j,...D}=x;return Object.entries(D).every(B=>{let[L,G]=B;return Array.isArray(G)?G.includes({...m,...v}[L]):{...m,...v}[L]===G})?[...b,E,j]:b},[]);return Lm(a,d,p,o?.class,o?.className)},Tr="-",zy=a=>{const i=Ny(a),{conflictingClassGroups:o,conflictingClassGroupModifiers:r}=a;return{getClassGroupId:d=>{const v=d.split(Tr);return v[0]===""&&v.length!==1&&v.shift(),Eh(v,i)||Ey(d)},getConflictingClassGroupIds:(d,v)=>{const p=o[d]||[];return v&&r[d]?[...p,...r[d]]:p}}},Eh=(a,i)=>{if(a.length===0)return i.classGroupId;const o=a[0],r=i.nextPart.get(o),s=r?Eh(a.slice(1),r):void 0;if(s)return s;if(i.validators.length===0)return;const m=a.join(Tr);return i.validators.find(({validator:d})=>d(m))?.classGroupId},Gm=/^\[(.+)\]$/,Ey=a=>{if(Gm.test(a)){const i=Gm.exec(a)[1],o=i?.substring(0,i.indexOf(":"));if(o)return"arbitrary.."+o}},Ny=a=>{const{theme:i,classGroups:o}=a,r={nextPart:new Map,validators:[]};for(const s in o)br(o[s],r,s,i);return r},br=(a,i,o,r)=>{a.forEach(s=>{if(typeof s=="string"){const m=s===""?i:qm(i,s);m.classGroupId=o;return}if(typeof s=="function"){if(Ay(s)){br(s(r),i,o,r);return}i.validators.push({validator:s,classGroupId:o});return}Object.entries(s).forEach(([m,d])=>{br(d,qm(i,m),o,r)})})},qm=(a,i)=>{let o=a;return i.split(Tr).forEach(r=>{o.nextPart.has(r)||o.nextPart.set(r,{nextPart:new Map,validators:[]}),o=o.nextPart.get(r)}),o},Ay=a=>a.isThemeGetter,jy=a=>{if(a<1)return{get:()=>{},set:()=>{}};let i=0,o=new Map,r=new Map;const s=(m,d)=>{o.set(m,d),i++,i>a&&(i=0,r=o,o=new Map)};return{get(m){let d=o.get(m);if(d!==void 0)return d;if((d=r.get(m))!==void 0)return s(m,d),d},set(m,d){o.has(m)?o.set(m,d):s(m,d)}}},xr="!",_r=":",Ty=_r.length,My=a=>{const{prefix:i,experimentalParseClassName:o}=a;let r=s=>{const m=[];let d=0,v=0,p=0,b;for(let B=0;B<s.length;B++){let L=s[B];if(d===0&&v===0){if(L===_r){m.push(s.slice(p,B)),p=B+Ty;continue}if(L==="/"){b=B;continue}}L==="["?d++:L==="]"?d--:L==="("?v++:L===")"&&v--}const x=m.length===0?s:s.substring(p),E=Oy(x),j=E!==x,D=b&&b>p?b-p:void 0;return{modifiers:m,hasImportantModifier:j,baseClassName:E,maybePostfixModifierPosition:D}};if(i){const s=i+_r,m=r;r=d=>d.startsWith(s)?m(d.substring(s.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:d,maybePostfixModifierPosition:void 0}}if(o){const s=r;r=m=>o({className:m,parseClassName:s})}return r},Oy=a=>a.endsWith(xr)?a.substring(0,a.length-1):a.startsWith(xr)?a.substring(1):a,Ry=a=>{const i=Object.fromEntries(a.orderSensitiveModifiers.map(r=>[r,!0]));return r=>{if(r.length<=1)return r;const s=[];let m=[];return r.forEach(d=>{d[0]==="["||i[d]?(s.push(...m.sort(),d),m=[]):m.push(d)}),s.push(...m.sort()),s}},Cy=a=>({cache:jy(a.cacheSize),parseClassName:My(a),sortModifiers:Ry(a),...zy(a)}),Dy=/\s+/,ky=(a,i)=>{const{parseClassName:o,getClassGroupId:r,getConflictingClassGroupIds:s,sortModifiers:m}=i,d=[],v=a.trim().split(Dy);let p="";for(let b=v.length-1;b>=0;b-=1){const x=v[b],{isExternal:E,modifiers:j,hasImportantModifier:D,baseClassName:B,maybePostfixModifierPosition:L}=o(x);if(E){p=x+(p.length>0?" "+p:p);continue}let G=!!L,Q=r(G?B.substring(0,L):B);if(!Q){if(!G){p=x+(p.length>0?" "+p:p);continue}if(Q=r(B),!Q){p=x+(p.length>0?" "+p:p);continue}G=!1}const F=m(j).join(":"),R=D?F+xr:F,Z=R+Q;if(d.includes(Z))continue;d.push(Z);const ee=s(Q,G);for(let J=0;J<ee.length;++J){const oe=ee[J];d.push(R+oe)}p=x+(p.length>0?" "+p:p)}return p};function Uy(){let a=0,i,o,r="";for(;a<arguments.length;)(i=arguments[a++])&&(o=Nh(i))&&(r&&(r+=" "),r+=o);return r}const Nh=a=>{if(typeof a=="string")return a;let i,o="";for(let r=0;r<a.length;r++)a[r]&&(i=Nh(a[r]))&&(o&&(o+=" "),o+=i);return o};function Vy(a,...i){let o,r,s,m=d;function d(p){const b=i.reduce((x,E)=>E(x),a());return o=Cy(b),r=o.cache.get,s=o.cache.set,m=v,v(p)}function v(p){const b=r(p);if(b)return b;const x=ky(p,o);return s(p,x),x}return function(){return m(Uy.apply(null,arguments))}}const He=a=>{const i=o=>o[a]||[];return i.isThemeGetter=!0,i},Ah=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,jh=/^\((?:(\w[\w-]*):)?(.+)\)$/i,Zy=/^\d+\/\d+$/,Hy=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,By=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,Ly=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,Gy=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,qy=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Wl=a=>Zy.test(a),le=a=>!!a&&!Number.isNaN(Number(a)),kn=a=>!!a&&Number.isInteger(Number(a)),mr=a=>a.endsWith("%")&&le(a.slice(0,-1)),un=a=>Hy.test(a),Yy=()=>!0,Xy=a=>By.test(a)&&!Ly.test(a),Th=()=>!1,Qy=a=>Gy.test(a),Ky=a=>qy.test(a),Jy=a=>!Y(a)&&!X(a),$y=a=>na(a,Rh,Th),Y=a=>Ah.test(a),ll=a=>na(a,Ch,Xy),hr=a=>na(a,eb,le),Ym=a=>na(a,Mh,Th),Fy=a=>na(a,Oh,Ky),Oi=a=>na(a,Dh,Qy),X=a=>jh.test(a),uu=a=>la(a,Ch),Wy=a=>la(a,tb),Xm=a=>la(a,Mh),Iy=a=>la(a,Rh),Py=a=>la(a,Oh),Ri=a=>la(a,Dh,!0),na=(a,i,o)=>{const r=Ah.exec(a);return r?r[1]?i(r[1]):o(r[2]):!1},la=(a,i,o=!1)=>{const r=jh.exec(a);return r?r[1]?i(r[1]):o:!1},Mh=a=>a==="position"||a==="percentage",Oh=a=>a==="image"||a==="url",Rh=a=>a==="length"||a==="size"||a==="bg-size",Ch=a=>a==="length",eb=a=>a==="number",tb=a=>a==="family-name",Dh=a=>a==="shadow",nb=()=>{const a=He("color"),i=He("font"),o=He("text"),r=He("font-weight"),s=He("tracking"),m=He("leading"),d=He("breakpoint"),v=He("container"),p=He("spacing"),b=He("radius"),x=He("shadow"),E=He("inset-shadow"),j=He("text-shadow"),D=He("drop-shadow"),B=He("blur"),L=He("perspective"),G=He("aspect"),Q=He("ease"),F=He("animate"),R=()=>["auto","avoid","all","avoid-page","page","left","right","column"],Z=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],ee=()=>[...Z(),X,Y],J=()=>["auto","hidden","clip","visible","scroll"],oe=()=>["auto","contain","none"],V=()=>[X,Y,p],ze=()=>[Wl,"full","auto",...V()],Re=()=>[kn,"none","subgrid",X,Y],ot=()=>["auto",{span:["full",kn,X,Y]},kn,X,Y],Ue=()=>[kn,"auto",X,Y],qt=()=>["auto","min","max","fr",X,Y],Dt=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],Ge=()=>["start","end","center","stretch","center-safe","end-safe"],T=()=>["auto",...V()],H=()=>[Wl,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...V()],U=()=>[a,X,Y],ge=()=>[...Z(),Xm,Ym,{position:[X,Y]}],ve=()=>["no-repeat",{repeat:["","x","y","space","round"]}],qe=()=>["auto","cover","contain",Iy,$y,{size:[X,Y]}],xe=()=>[mr,uu,ll],te=()=>["","none","full",b,X,Y],se=()=>["",le,uu,ll],et=()=>["solid","dashed","dotted","double"],jt=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],Ne=()=>[le,mr,Xm,Ym],Zn=()=>["","none",B,X,Y],Tt=()=>["none",le,X,Y],on=()=>["none",le,X,Y],rn=()=>[le,X,Y],sn=()=>[Wl,"full",...V()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[un],breakpoint:[un],color:[Yy],container:[un],"drop-shadow":[un],ease:["in","out","in-out"],font:[Jy],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[un],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[un],shadow:[un],spacing:["px",le],text:[un],"text-shadow":[un],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",Wl,Y,X,G]}],container:["container"],columns:[{columns:[le,Y,X,v]}],"break-after":[{"break-after":R()}],"break-before":[{"break-before":R()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:ee()}],overflow:[{overflow:J()}],"overflow-x":[{"overflow-x":J()}],"overflow-y":[{"overflow-y":J()}],overscroll:[{overscroll:oe()}],"overscroll-x":[{"overscroll-x":oe()}],"overscroll-y":[{"overscroll-y":oe()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:ze()}],"inset-x":[{"inset-x":ze()}],"inset-y":[{"inset-y":ze()}],start:[{start:ze()}],end:[{end:ze()}],top:[{top:ze()}],right:[{right:ze()}],bottom:[{bottom:ze()}],left:[{left:ze()}],visibility:["visible","invisible","collapse"],z:[{z:[kn,"auto",X,Y]}],basis:[{basis:[Wl,"full","auto",v,...V()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[le,Wl,"auto","initial","none",Y]}],grow:[{grow:["",le,X,Y]}],shrink:[{shrink:["",le,X,Y]}],order:[{order:[kn,"first","last","none",X,Y]}],"grid-cols":[{"grid-cols":Re()}],"col-start-end":[{col:ot()}],"col-start":[{"col-start":Ue()}],"col-end":[{"col-end":Ue()}],"grid-rows":[{"grid-rows":Re()}],"row-start-end":[{row:ot()}],"row-start":[{"row-start":Ue()}],"row-end":[{"row-end":Ue()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":qt()}],"auto-rows":[{"auto-rows":qt()}],gap:[{gap:V()}],"gap-x":[{"gap-x":V()}],"gap-y":[{"gap-y":V()}],"justify-content":[{justify:[...Dt(),"normal"]}],"justify-items":[{"justify-items":[...Ge(),"normal"]}],"justify-self":[{"justify-self":["auto",...Ge()]}],"align-content":[{content:["normal",...Dt()]}],"align-items":[{items:[...Ge(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...Ge(),{baseline:["","last"]}]}],"place-content":[{"place-content":Dt()}],"place-items":[{"place-items":[...Ge(),"baseline"]}],"place-self":[{"place-self":["auto",...Ge()]}],p:[{p:V()}],px:[{px:V()}],py:[{py:V()}],ps:[{ps:V()}],pe:[{pe:V()}],pt:[{pt:V()}],pr:[{pr:V()}],pb:[{pb:V()}],pl:[{pl:V()}],m:[{m:T()}],mx:[{mx:T()}],my:[{my:T()}],ms:[{ms:T()}],me:[{me:T()}],mt:[{mt:T()}],mr:[{mr:T()}],mb:[{mb:T()}],ml:[{ml:T()}],"space-x":[{"space-x":V()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":V()}],"space-y-reverse":["space-y-reverse"],size:[{size:H()}],w:[{w:[v,"screen",...H()]}],"min-w":[{"min-w":[v,"screen","none",...H()]}],"max-w":[{"max-w":[v,"screen","none","prose",{screen:[d]},...H()]}],h:[{h:["screen","lh",...H()]}],"min-h":[{"min-h":["screen","lh","none",...H()]}],"max-h":[{"max-h":["screen","lh",...H()]}],"font-size":[{text:["base",o,uu,ll]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[r,X,hr]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",mr,Y]}],"font-family":[{font:[Wy,Y,i]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[s,X,Y]}],"line-clamp":[{"line-clamp":[le,"none",X,hr]}],leading:[{leading:[m,...V()]}],"list-image":[{"list-image":["none",X,Y]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",X,Y]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:U()}],"text-color":[{text:U()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...et(),"wavy"]}],"text-decoration-thickness":[{decoration:[le,"from-font","auto",X,ll]}],"text-decoration-color":[{decoration:U()}],"underline-offset":[{"underline-offset":[le,"auto",X,Y]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:V()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",X,Y]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",X,Y]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:ge()}],"bg-repeat":[{bg:ve()}],"bg-size":[{bg:qe()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},kn,X,Y],radial:["",X,Y],conic:[kn,X,Y]},Py,Fy]}],"bg-color":[{bg:U()}],"gradient-from-pos":[{from:xe()}],"gradient-via-pos":[{via:xe()}],"gradient-to-pos":[{to:xe()}],"gradient-from":[{from:U()}],"gradient-via":[{via:U()}],"gradient-to":[{to:U()}],rounded:[{rounded:te()}],"rounded-s":[{"rounded-s":te()}],"rounded-e":[{"rounded-e":te()}],"rounded-t":[{"rounded-t":te()}],"rounded-r":[{"rounded-r":te()}],"rounded-b":[{"rounded-b":te()}],"rounded-l":[{"rounded-l":te()}],"rounded-ss":[{"rounded-ss":te()}],"rounded-se":[{"rounded-se":te()}],"rounded-ee":[{"rounded-ee":te()}],"rounded-es":[{"rounded-es":te()}],"rounded-tl":[{"rounded-tl":te()}],"rounded-tr":[{"rounded-tr":te()}],"rounded-br":[{"rounded-br":te()}],"rounded-bl":[{"rounded-bl":te()}],"border-w":[{border:se()}],"border-w-x":[{"border-x":se()}],"border-w-y":[{"border-y":se()}],"border-w-s":[{"border-s":se()}],"border-w-e":[{"border-e":se()}],"border-w-t":[{"border-t":se()}],"border-w-r":[{"border-r":se()}],"border-w-b":[{"border-b":se()}],"border-w-l":[{"border-l":se()}],"divide-x":[{"divide-x":se()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":se()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...et(),"hidden","none"]}],"divide-style":[{divide:[...et(),"hidden","none"]}],"border-color":[{border:U()}],"border-color-x":[{"border-x":U()}],"border-color-y":[{"border-y":U()}],"border-color-s":[{"border-s":U()}],"border-color-e":[{"border-e":U()}],"border-color-t":[{"border-t":U()}],"border-color-r":[{"border-r":U()}],"border-color-b":[{"border-b":U()}],"border-color-l":[{"border-l":U()}],"divide-color":[{divide:U()}],"outline-style":[{outline:[...et(),"none","hidden"]}],"outline-offset":[{"outline-offset":[le,X,Y]}],"outline-w":[{outline:["",le,uu,ll]}],"outline-color":[{outline:U()}],shadow:[{shadow:["","none",x,Ri,Oi]}],"shadow-color":[{shadow:U()}],"inset-shadow":[{"inset-shadow":["none",E,Ri,Oi]}],"inset-shadow-color":[{"inset-shadow":U()}],"ring-w":[{ring:se()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:U()}],"ring-offset-w":[{"ring-offset":[le,ll]}],"ring-offset-color":[{"ring-offset":U()}],"inset-ring-w":[{"inset-ring":se()}],"inset-ring-color":[{"inset-ring":U()}],"text-shadow":[{"text-shadow":["none",j,Ri,Oi]}],"text-shadow-color":[{"text-shadow":U()}],opacity:[{opacity:[le,X,Y]}],"mix-blend":[{"mix-blend":[...jt(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":jt()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[le]}],"mask-image-linear-from-pos":[{"mask-linear-from":Ne()}],"mask-image-linear-to-pos":[{"mask-linear-to":Ne()}],"mask-image-linear-from-color":[{"mask-linear-from":U()}],"mask-image-linear-to-color":[{"mask-linear-to":U()}],"mask-image-t-from-pos":[{"mask-t-from":Ne()}],"mask-image-t-to-pos":[{"mask-t-to":Ne()}],"mask-image-t-from-color":[{"mask-t-from":U()}],"mask-image-t-to-color":[{"mask-t-to":U()}],"mask-image-r-from-pos":[{"mask-r-from":Ne()}],"mask-image-r-to-pos":[{"mask-r-to":Ne()}],"mask-image-r-from-color":[{"mask-r-from":U()}],"mask-image-r-to-color":[{"mask-r-to":U()}],"mask-image-b-from-pos":[{"mask-b-from":Ne()}],"mask-image-b-to-pos":[{"mask-b-to":Ne()}],"mask-image-b-from-color":[{"mask-b-from":U()}],"mask-image-b-to-color":[{"mask-b-to":U()}],"mask-image-l-from-pos":[{"mask-l-from":Ne()}],"mask-image-l-to-pos":[{"mask-l-to":Ne()}],"mask-image-l-from-color":[{"mask-l-from":U()}],"mask-image-l-to-color":[{"mask-l-to":U()}],"mask-image-x-from-pos":[{"mask-x-from":Ne()}],"mask-image-x-to-pos":[{"mask-x-to":Ne()}],"mask-image-x-from-color":[{"mask-x-from":U()}],"mask-image-x-to-color":[{"mask-x-to":U()}],"mask-image-y-from-pos":[{"mask-y-from":Ne()}],"mask-image-y-to-pos":[{"mask-y-to":Ne()}],"mask-image-y-from-color":[{"mask-y-from":U()}],"mask-image-y-to-color":[{"mask-y-to":U()}],"mask-image-radial":[{"mask-radial":[X,Y]}],"mask-image-radial-from-pos":[{"mask-radial-from":Ne()}],"mask-image-radial-to-pos":[{"mask-radial-to":Ne()}],"mask-image-radial-from-color":[{"mask-radial-from":U()}],"mask-image-radial-to-color":[{"mask-radial-to":U()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":Z()}],"mask-image-conic-pos":[{"mask-conic":[le]}],"mask-image-conic-from-pos":[{"mask-conic-from":Ne()}],"mask-image-conic-to-pos":[{"mask-conic-to":Ne()}],"mask-image-conic-from-color":[{"mask-conic-from":U()}],"mask-image-conic-to-color":[{"mask-conic-to":U()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:ge()}],"mask-repeat":[{mask:ve()}],"mask-size":[{mask:qe()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",X,Y]}],filter:[{filter:["","none",X,Y]}],blur:[{blur:Zn()}],brightness:[{brightness:[le,X,Y]}],contrast:[{contrast:[le,X,Y]}],"drop-shadow":[{"drop-shadow":["","none",D,Ri,Oi]}],"drop-shadow-color":[{"drop-shadow":U()}],grayscale:[{grayscale:["",le,X,Y]}],"hue-rotate":[{"hue-rotate":[le,X,Y]}],invert:[{invert:["",le,X,Y]}],saturate:[{saturate:[le,X,Y]}],sepia:[{sepia:["",le,X,Y]}],"backdrop-filter":[{"backdrop-filter":["","none",X,Y]}],"backdrop-blur":[{"backdrop-blur":Zn()}],"backdrop-brightness":[{"backdrop-brightness":[le,X,Y]}],"backdrop-contrast":[{"backdrop-contrast":[le,X,Y]}],"backdrop-grayscale":[{"backdrop-grayscale":["",le,X,Y]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[le,X,Y]}],"backdrop-invert":[{"backdrop-invert":["",le,X,Y]}],"backdrop-opacity":[{"backdrop-opacity":[le,X,Y]}],"backdrop-saturate":[{"backdrop-saturate":[le,X,Y]}],"backdrop-sepia":[{"backdrop-sepia":["",le,X,Y]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":V()}],"border-spacing-x":[{"border-spacing-x":V()}],"border-spacing-y":[{"border-spacing-y":V()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",X,Y]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[le,"initial",X,Y]}],ease:[{ease:["linear","initial",Q,X,Y]}],delay:[{delay:[le,X,Y]}],animate:[{animate:["none",F,X,Y]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[L,X,Y]}],"perspective-origin":[{"perspective-origin":ee()}],rotate:[{rotate:Tt()}],"rotate-x":[{"rotate-x":Tt()}],"rotate-y":[{"rotate-y":Tt()}],"rotate-z":[{"rotate-z":Tt()}],scale:[{scale:on()}],"scale-x":[{"scale-x":on()}],"scale-y":[{"scale-y":on()}],"scale-z":[{"scale-z":on()}],"scale-3d":["scale-3d"],skew:[{skew:rn()}],"skew-x":[{"skew-x":rn()}],"skew-y":[{"skew-y":rn()}],transform:[{transform:[X,Y,"","none","gpu","cpu"]}],"transform-origin":[{origin:ee()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:sn()}],"translate-x":[{"translate-x":sn()}],"translate-y":[{"translate-y":sn()}],"translate-z":[{"translate-z":sn()}],"translate-none":["translate-none"],accent:[{accent:U()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:U()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",X,Y]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":V()}],"scroll-mx":[{"scroll-mx":V()}],"scroll-my":[{"scroll-my":V()}],"scroll-ms":[{"scroll-ms":V()}],"scroll-me":[{"scroll-me":V()}],"scroll-mt":[{"scroll-mt":V()}],"scroll-mr":[{"scroll-mr":V()}],"scroll-mb":[{"scroll-mb":V()}],"scroll-ml":[{"scroll-ml":V()}],"scroll-p":[{"scroll-p":V()}],"scroll-px":[{"scroll-px":V()}],"scroll-py":[{"scroll-py":V()}],"scroll-ps":[{"scroll-ps":V()}],"scroll-pe":[{"scroll-pe":V()}],"scroll-pt":[{"scroll-pt":V()}],"scroll-pr":[{"scroll-pr":V()}],"scroll-pb":[{"scroll-pb":V()}],"scroll-pl":[{"scroll-pl":V()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",X,Y]}],fill:[{fill:["none",...U()]}],"stroke-w":[{stroke:[le,uu,ll,hr]}],stroke:[{stroke:["none",...U()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},lb=Vy(nb);function aa(...a){return lb(zh(a))}const ab=wy("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 ring-offset-background",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),Be=k.forwardRef(({className:a,variant:i,size:o,asChild:r=!1,...s},m)=>{const d=r?py:"button";return h.jsx(d,{className:aa(ab({variant:i,size:o,className:a})),ref:m,...s})});Be.displayName="Button";function Yi(a,i=[]){let o=[];function r(m,d){const v=k.createContext(d),p=o.length;o=[...o,d];const b=E=>{const{scope:j,children:D,...B}=E,L=j?.[a]?.[p]||v,G=k.useMemo(()=>B,Object.values(B));return h.jsx(L.Provider,{value:G,children:D})};b.displayName=m+"Provider";function x(E,j){const D=j?.[a]?.[p]||v,B=k.useContext(D);if(B)return B;if(d!==void 0)return d;throw new Error(`\`${E}\` must be used within \`${m}\``)}return[b,x]}const s=()=>{const m=o.map(d=>k.createContext(d));return function(v){const p=v?.[a]||m;return k.useMemo(()=>({[`__scope${a}`]:{...v,[a]:p}}),[v,p])}};return s.scopeName=a,[r,ub(s,...i)]}function ub(...a){const i=a[0];if(a.length===1)return i;const o=()=>{const r=a.map(s=>({useScope:s(),scopeName:s.scopeName}));return function(m){const d=r.reduce((v,{useScope:p,scopeName:b})=>{const E=p(m)[`__scope${b}`];return{...v,...E}},{});return k.useMemo(()=>({[`__scope${i.scopeName}`]:d}),[d])}};return o.scopeName=i.scopeName,o}var ib=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],Vn=ib.reduce((a,i)=>{const o=Hi(`Primitive.${i}`),r=k.forwardRef((s,m)=>{const{asChild:d,...v}=s,p=d?o:i;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),h.jsx(p,{...v,ref:m})});return r.displayName=`Primitive.${i}`,{...a,[i]:r}},{}),Mr="Progress",Or=100,[cb,a_]=Yi(Mr),[ob,rb]=cb(Mr),kh=k.forwardRef((a,i)=>{const{__scopeProgress:o,value:r=null,max:s,getValueLabel:m=sb,...d}=a;(s||s===0)&&!Qm(s)&&console.error(fb(`${s}`,"Progress"));const v=Qm(s)?s:Or;r!==null&&!Km(r,v)&&console.error(db(`${r}`,"Progress"));const p=Km(r,v)?r:null,b=Bi(p)?m(p,v):void 0;return h.jsx(ob,{scope:o,value:p,max:v,children:h.jsx(Vn.div,{"aria-valuemax":v,"aria-valuemin":0,"aria-valuenow":Bi(p)?p:void 0,"aria-valuetext":b,role:"progressbar","data-state":Zh(p,v),"data-value":p??void 0,"data-max":v,...d,ref:i})})});kh.displayName=Mr;var Uh="ProgressIndicator",Vh=k.forwardRef((a,i)=>{const{__scopeProgress:o,...r}=a,s=rb(Uh,o);return h.jsx(Vn.div,{"data-state":Zh(s.value,s.max),"data-value":s.value??void 0,"data-max":s.max,...r,ref:i})});Vh.displayName=Uh;function sb(a,i){return`${Math.round(a/i*100)}%`}function Zh(a,i){return a==null?"indeterminate":a===i?"complete":"loading"}function Bi(a){return typeof a=="number"}function Qm(a){return Bi(a)&&!isNaN(a)&&a>0}function Km(a,i){return Bi(a)&&!isNaN(a)&&a<=i&&a>=0}function fb(a,i){return`Invalid prop \`max\` of value \`${a}\` supplied to \`${i}\`. Only numbers greater than 0 are valid max values. Defaulting to \`${Or}\`.`}function db(a,i){return`Invalid prop \`value\` of value \`${a}\` supplied to \`${i}\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or ${Or} if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`}var Hh=kh,mb=Vh;const Bh=k.forwardRef(({className:a,value:i,...o},r)=>h.jsx(Hh,{ref:r,className:aa("relative h-2 w-full overflow-hidden rounded-full bg-secondary",a),...o,children:h.jsx(mb,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(i??0)}%)`}})}));Bh.displayName=Hh.displayName;const Jm=a=>{let i;const o=new Set,r=(b,x)=>{const E=typeof b=="function"?b(i):b;if(!Object.is(E,i)){const j=i;i=x??(typeof E!="object"||E===null)?E:Object.assign({},i,E),o.forEach(D=>D(i,j))}},s=()=>i,v={setState:r,getState:s,getInitialState:()=>p,subscribe:b=>(o.add(b),()=>o.delete(b))},p=i=a(r,s,v);return v},hb=a=>a?Jm(a):Jm,gb=a=>a;function vb(a,i=gb){const o=Ct.useSyncExternalStore(a.subscribe,Ct.useCallback(()=>i(a.getState()),[a,i]),Ct.useCallback(()=>i(a.getInitialState()),[a,i]));return Ct.useDebugValue(o),o}const $m=a=>{const i=hb(a),o=r=>vb(i,r);return Object.assign(o,i),o},pb=a=>a?$m(a):$m,Lh="liv_processing_config_v1",Gh="liv_display_config_v1";function yb(a){try{localStorage.setItem(Lh,JSON.stringify(a))}catch{}}function bb(){try{const a=localStorage.getItem(Lh);return a?JSON.parse(a):null}catch{return null}}function xb(a){try{localStorage.setItem(Gh,JSON.stringify(a))}catch{}}function _b(){try{const a=localStorage.getItem(Gh);return a?JSON.parse(a):null}catch{return null}}const Sb=bb()??{thresholdDetection:"linear",fittingPoints:150,smoothing:{enabled:!1,method:"moving-average",windowSize:5}},wb=_b()??{showGrid:!0,showLegend:!0,showMarkers:!0,showCoords:!0,theme:"light"},zb=(()=>{try{const a=localStorage.getItem("liv_sidebar_width_px");return a?Number(a):350}catch{return 350}})(),re=pb(a=>({data:null,results:null,currentTab:"LIV",theme:"light",sidebarWidth:zb,isProcessing:!1,progress:0,error:null,processingConfig:Sb,displayConfig:wb,setData:i=>a({data:i}),setResults:i=>a({results:i}),setCurrentTab:i=>a({currentTab:i}),setTheme:i=>a({theme:i,displayConfig:{...re.getState().displayConfig,theme:i}}),setSidebarWidth:i=>{try{localStorage.setItem("liv_sidebar_width_px",String(i))}catch{}a({sidebarWidth:i})},setProcessing:i=>a({isProcessing:i}),setProgress:i=>a({progress:i}),setError:i=>a({error:i}),updateProcessingConfig:i=>a(o=>{const r={...o.processingConfig,...i};return yb(r),{processingConfig:r}}),updateDisplayConfig:i=>a(o=>{const r={...o.displayConfig,...i};return xb(r),{displayConfig:r}}),reset:()=>a({data:null,results:null,currentTab:"LIV",isProcessing:!1,progress:0,error:null})}));var Eb=cy();const Nb=ay(Eb);async function Rr(a){return new Promise(i=>{Nb.parse(a,{dynamicTyping:!0,skipEmptyLines:!0,complete:o=>{i({data:o.data})},error:(o,r)=>i({data:null,error:o.message})})})}async function Cr(a){try{const i=await a.arrayBuffer(),o=oy(i,{type:"array"}),r={};for(const s of o.SheetNames){const m=o.Sheets[s],v=ry.sheet_to_json(m,{header:1,raw:!0}).filter(p=>p&&p.length>=2).map(p=>p.map(b=>typeof b=="number"?b:Number(b)));r[s.toLowerCase()]=v}return{data:r}}catch(i){return{data:null,error:i.message}}}function Li(a){const i=r=>({x:r.map(s=>s[0]).filter(s=>Number.isFinite(s)),y:r.map(s=>s[1]).filter(s=>Number.isFinite(s))}),o={};if(a.wavelength){const{x:r,y:s}=i(a.wavelength);o.wavelength={wavelength:r,intensity:s}}if(a.power){const{x:r,y:s}=i(a.power);o.power={current:r,power:s}}if(a.voltage){const{x:r,y:s}=i(a.voltage);o.voltage={current:r,voltage:s}}if(a.hff){const{x:r,y:s}=i(a.hff);o.hff={angle:r,intensity:s}}if(a.vff){const{x:r,y:s}=i(a.vff);o.vff={angle:r,intensity:s}}return o}class qh{worker=null;constructor(){try{this.worker=new Worker(new URL("/assets/parserWorker-CQK2Ez6U.js",import.meta.url),{type:"module"})}catch{this.worker=null}}getWorkerOrThrow(){if(!this.worker)throw new Error("Worker 初始化失败");return this.worker}async parseCSV(i){const o=this.getWorkerOrThrow();return new Promise((r,s)=>{const m=v=>{const p=v.data;if(!p.ok){d(),s(new Error(p.error||"CSV 解析失败"));return}p.kind==="csv"&&(d(),r(p.rows||[]))},d=()=>{o.removeEventListener("message",m)};o.addEventListener("message",m),o.postMessage({kind:"csv",file:i})})}async parseExcel(i){const o=this.getWorkerOrThrow();return new Promise((r,s)=>{const m=v=>{const p=v.data;if(!p.ok){d(),s(new Error(p.error||"Excel 解析失败"));return}p.kind==="excel"&&(d(),r(p.sheets||{}))},d=()=>{o.removeEventListener("message",m)};o.addEventListener("message",m),o.postMessage({kind:"excel",file:i})})}}function iu(a=[],i=[]){const o=Math.min(a.length,i.length),r=[],s=[];for(let m=0;m<o;m++){const d=a[m],v=i[m];Number.isFinite(d)&&Number.isFinite(v)&&(r.push(d),s.push(v))}return{x:r,y:s}}function Yh(a){const i={};if(a.wavelength){const{x:o,y:r}=iu(a.wavelength.wavelength,a.wavelength.intensity);i.wavelength={wavelength:o,intensity:r}}if(a.power){const{x:o,y:r}=iu(a.power.current,a.power.power);i.power={current:o,power:r}}if(a.voltage){const{x:o,y:r}=iu(a.voltage.current,a.voltage.voltage);i.voltage={current:o,voltage:r}}if(a.hff){const{x:o,y:r}=iu(a.hff.angle,a.hff.intensity);i.hff={angle:o,intensity:r}}if(a.vff){const{x:o,y:r}=iu(a.vff.angle,a.vff.intensity);i.vff={angle:o,intensity:r}}return i}function $(a,i,o){function r(v,p){var b;Object.defineProperty(v,"_zod",{value:v._zod??{},enumerable:!1}),(b=v._zod).traits??(b.traits=new Set),v._zod.traits.add(a),i(v,p);for(const x in d.prototype)x in v||Object.defineProperty(v,x,{value:d.prototype[x].bind(v)});v._zod.constr=d,v._zod.def=p}const s=o?.Parent??Object;class m extends s{}Object.defineProperty(m,"name",{value:a});function d(v){var p;const b=o?.Parent?new m:this;r(b,v),(p=b._zod).deferred??(p.deferred=[]);for(const x of b._zod.deferred)x();return b}return Object.defineProperty(d,"init",{value:r}),Object.defineProperty(d,Symbol.hasInstance,{value:v=>o?.Parent&&v instanceof o.Parent?!0:v?._zod?.traits?.has(a)}),Object.defineProperty(d,"name",{value:a}),d}class su extends Error{constructor(){super("Encountered Promise during synchronous parse. Use .parseAsync() instead.")}}const Xh={};function ol(a){return Xh}function Ab(a){const i=Object.values(a).filter(r=>typeof r=="number");return Object.entries(a).filter(([r,s])=>i.indexOf(+r)===-1).map(([r,s])=>s)}function Sr(a,i){return typeof i=="bigint"?i.toString():i}function Qh(a){return{get value(){{const i=a();return Object.defineProperty(this,"value",{value:i}),i}}}}function Dr(a){return a==null}function kr(a){const i=a.startsWith("^")?1:0,o=a.endsWith("$")?a.length-1:a.length;return a.slice(i,o)}function jb(a,i){const o=(a.toString().split(".")[1]||"").length,r=i.toString();let s=(r.split(".")[1]||"").length;if(s===0&&/\d?e-\d?/.test(r)){const p=r.match(/\d?e-(\d?)/);p?.[1]&&(s=Number.parseInt(p[1]))}const m=o>s?o:s,d=Number.parseInt(a.toFixed(m).replace(".","")),v=Number.parseInt(i.toFixed(m).replace(".",""));return d%v/10**m}const Fm=Symbol("evaluating");function we(a,i,o){let r;Object.defineProperty(a,i,{get(){if(r!==Fm)return r===void 0&&(r=Fm,r=o()),r},set(s){Object.defineProperty(a,i,{value:s})},configurable:!0})}function Tb(a){return Object.create(Object.getPrototypeOf(a),Object.getOwnPropertyDescriptors(a))}function sl(a,i,o){Object.defineProperty(a,i,{value:o,writable:!0,enumerable:!0,configurable:!0})}function ua(...a){const i={};for(const o of a){const r=Object.getOwnPropertyDescriptors(o);Object.assign(i,r)}return Object.defineProperties({},i)}function Wm(a){return JSON.stringify(a)}const Kh="captureStackTrace"in Error?Error.captureStackTrace:(...a)=>{};function wr(a){return typeof a=="object"&&a!==null&&!Array.isArray(a)}const Mb=Qh(()=>{if(typeof navigator<"u"&&navigator?.userAgent?.includes("Cloudflare"))return!1;try{const a=Function;return new a(""),!0}catch{return!1}});function Gi(a){if(wr(a)===!1)return!1;const i=a.constructor;if(i===void 0)return!0;const o=i.prototype;return!(wr(o)===!1||Object.prototype.hasOwnProperty.call(o,"isPrototypeOf")===!1)}function Jh(a){return Gi(a)?{...a}:a}const Ob=new Set(["string","number","symbol"]);function Rb(a){return a.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function fl(a,i,o){const r=new a._zod.constr(i??a._zod.def);return(!i||o?.parent)&&(r._zod.parent=a),r}function ke(a){const i=a;if(!i)return{};if(typeof i=="string")return{error:()=>i};if(i?.message!==void 0){if(i?.error!==void 0)throw new Error("Cannot specify both `message` and `error` params");i.error=i.message}return delete i.message,typeof i.error=="string"?{...i,error:()=>i.error}:i}function Cb(a){return Object.keys(a).filter(i=>a[i]._zod.optin==="optional"&&a[i]._zod.optout==="optional")}const Db={safeint:[Number.MIN_SAFE_INTEGER,Number.MAX_SAFE_INTEGER],int32:[-2147483648,2147483647],uint32:[0,4294967295],float32:[-34028234663852886e22,34028234663852886e22],float64:[-Number.MAX_VALUE,Number.MAX_VALUE]};function kb(a,i){const o=a._zod.def,r=ua(a._zod.def,{get shape(){const s={};for(const m in i){if(!(m in o.shape))throw new Error(`Unrecognized key: "${m}"`);i[m]&&(s[m]=o.shape[m])}return sl(this,"shape",s),s},checks:[]});return fl(a,r)}function Ub(a,i){const o=a._zod.def,r=ua(a._zod.def,{get shape(){const s={...a._zod.def.shape};for(const m in i){if(!(m in o.shape))throw new Error(`Unrecognized key: "${m}"`);i[m]&&delete s[m]}return sl(this,"shape",s),s},checks:[]});return fl(a,r)}function Vb(a,i){if(!Gi(i))throw new Error("Invalid input to extend: expected a plain object");const o=ua(a._zod.def,{get shape(){const r={...a._zod.def.shape,...i};return sl(this,"shape",r),r},checks:[]});return fl(a,o)}function Zb(a,i){const o=ua(a._zod.def,{get shape(){const r={...a._zod.def.shape,...i._zod.def.shape};return sl(this,"shape",r),r},get catchall(){return i._zod.def.catchall},checks:[]});return fl(a,o)}function Hb(a,i,o){const r=ua(i._zod.def,{get shape(){const s=i._zod.def.shape,m={...s};if(o)for(const d in o){if(!(d in s))throw new Error(`Unrecognized key: "${d}"`);o[d]&&(m[d]=a?new a({type:"optional",innerType:s[d]}):s[d])}else for(const d in s)m[d]=a?new a({type:"optional",innerType:s[d]}):s[d];return sl(this,"shape",m),m},checks:[]});return fl(i,r)}function Bb(a,i,o){const r=ua(i._zod.def,{get shape(){const s=i._zod.def.shape,m={...s};if(o)for(const d in o){if(!(d in m))throw new Error(`Unrecognized key: "${d}"`);o[d]&&(m[d]=new a({type:"nonoptional",innerType:s[d]}))}else for(const d in s)m[d]=new a({type:"nonoptional",innerType:s[d]});return sl(this,"shape",m),m},checks:[]});return fl(i,r)}function ru(a,i=0){for(let o=i;o<a.issues.length;o++)if(a.issues[o]?.continue!==!0)return!0;return!1}function $h(a,i){return i.map(o=>{var r;return(r=o).path??(r.path=[]),o.path.unshift(a),o})}function Ci(a){return typeof a=="string"?a:a?.message}function rl(a,i,o){const r={...a,path:a.path??[]};if(!a.message){const s=Ci(a.inst?._zod.def?.error?.(a))??Ci(i?.error?.(a))??Ci(o.customError?.(a))??Ci(o.localeError?.(a))??"Invalid input";r.message=s}return delete r.inst,delete r.continue,i?.reportInput||delete r.input,r}function Ur(a){return Array.isArray(a)?"array":typeof a=="string"?"string":"unknown"}function fu(...a){const[i,o,r]=a;return typeof i=="string"?{message:i,code:"custom",input:o,inst:r}:{...i}}const Fh=(a,i)=>{a.name="$ZodError",Object.defineProperty(a,"_zod",{value:a._zod,enumerable:!1}),Object.defineProperty(a,"issues",{value:i,enumerable:!1}),a.message=JSON.stringify(i,Sr,2),Object.defineProperty(a,"toString",{value:()=>a.message,enumerable:!1})},Wh=$("$ZodError",Fh),Ih=$("$ZodError",Fh,{Parent:Error});function Lb(a,i=o=>o.message){const o={},r=[];for(const s of a.issues)s.path.length>0?(o[s.path[0]]=o[s.path[0]]||[],o[s.path[0]].push(i(s))):r.push(i(s));return{formErrors:r,fieldErrors:o}}function Gb(a,i){const o=i||function(m){return m.message},r={_errors:[]},s=m=>{for(const d of m.issues)if(d.code==="invalid_union"&&d.errors.length)d.errors.map(v=>s({issues:v}));else if(d.code==="invalid_key")s({issues:d.issues});else if(d.code==="invalid_element")s({issues:d.issues});else if(d.path.length===0)r._errors.push(o(d));else{let v=r,p=0;for(;p<d.path.length;){const b=d.path[p];p===d.path.length-1?(v[b]=v[b]||{_errors:[]},v[b]._errors.push(o(d))):v[b]=v[b]||{_errors:[]},v=v[b],p++}}};return s(a),r}const qb=a=>(i,o,r,s)=>{const m=r?Object.assign(r,{async:!1}):{async:!1},d=i._zod.run({value:o,issues:[]},m);if(d instanceof Promise)throw new su;if(d.issues.length){const v=new(s?.Err??a)(d.issues.map(p=>rl(p,m,ol())));throw Kh(v,s?.callee),v}return d.value},Yb=a=>async(i,o,r,s)=>{const m=r?Object.assign(r,{async:!0}):{async:!0};let d=i._zod.run({value:o,issues:[]},m);if(d instanceof Promise&&(d=await d),d.issues.length){const v=new(s?.Err??a)(d.issues.map(p=>rl(p,m,ol())));throw Kh(v,s?.callee),v}return d.value},Ph=a=>(i,o,r)=>{const s=r?{...r,async:!1}:{async:!1},m=i._zod.run({value:o,issues:[]},s);if(m instanceof Promise)throw new su;return m.issues.length?{success:!1,error:new(a??Wh)(m.issues.map(d=>rl(d,s,ol())))}:{success:!0,data:m.value}},Xb=Ph(Ih),eg=a=>async(i,o,r)=>{const s=r?Object.assign(r,{async:!0}):{async:!0};let m=i._zod.run({value:o,issues:[]},s);return m instanceof Promise&&(m=await m),m.issues.length?{success:!1,error:new a(m.issues.map(d=>rl(d,s,ol())))}:{success:!0,data:m.value}},Qb=eg(Ih),Kb=/^\d+$/,Jb=/^-?\d+(?:\.\d+)?/i,Gt=$("$ZodCheck",(a,i)=>{var o;a._zod??(a._zod={}),a._zod.def=i,(o=a._zod).onattach??(o.onattach=[])}),tg={number:"number",bigint:"bigint",object:"date"},ng=$("$ZodCheckLessThan",(a,i)=>{Gt.init(a,i);const o=tg[typeof i.value];a._zod.onattach.push(r=>{const s=r._zod.bag,m=(i.inclusive?s.maximum:s.exclusiveMaximum)??Number.POSITIVE_INFINITY;i.value<m&&(i.inclusive?s.maximum=i.value:s.exclusiveMaximum=i.value)}),a._zod.check=r=>{(i.inclusive?r.value<=i.value:r.value<i.value)||r.issues.push({origin:o,code:"too_big",maximum:i.value,input:r.value,inclusive:i.inclusive,inst:a,continue:!i.abort})}}),lg=$("$ZodCheckGreaterThan",(a,i)=>{Gt.init(a,i);const o=tg[typeof i.value];a._zod.onattach.push(r=>{const s=r._zod.bag,m=(i.inclusive?s.minimum:s.exclusiveMinimum)??Number.NEGATIVE_INFINITY;i.value>m&&(i.inclusive?s.minimum=i.value:s.exclusiveMinimum=i.value)}),a._zod.check=r=>{(i.inclusive?r.value>=i.value:r.value>i.value)||r.issues.push({origin:o,code:"too_small",minimum:i.value,input:r.value,inclusive:i.inclusive,inst:a,continue:!i.abort})}}),$b=$("$ZodCheckMultipleOf",(a,i)=>{Gt.init(a,i),a._zod.onattach.push(o=>{var r;(r=o._zod.bag).multipleOf??(r.multipleOf=i.value)}),a._zod.check=o=>{if(typeof o.value!=typeof i.value)throw new Error("Cannot mix number and bigint in multiple_of check.");(typeof o.value=="bigint"?o.value%i.value===BigInt(0):jb(o.value,i.value)===0)||o.issues.push({origin:typeof o.value,code:"not_multiple_of",divisor:i.value,input:o.value,inst:a,continue:!i.abort})}}),Fb=$("$ZodCheckNumberFormat",(a,i)=>{Gt.init(a,i),i.format=i.format||"float64";const o=i.format?.includes("int"),r=o?"int":"number",[s,m]=Db[i.format];a._zod.onattach.push(d=>{const v=d._zod.bag;v.format=i.format,v.minimum=s,v.maximum=m,o&&(v.pattern=Kb)}),a._zod.check=d=>{const v=d.value;if(o){if(!Number.isInteger(v)){d.issues.push({expected:r,format:i.format,code:"invalid_type",continue:!1,input:v,inst:a});return}if(!Number.isSafeInteger(v)){v>0?d.issues.push({input:v,code:"too_big",maximum:Number.MAX_SAFE_INTEGER,note:"Integers must be within the safe integer range.",inst:a,origin:r,continue:!i.abort}):d.issues.push({input:v,code:"too_small",minimum:Number.MIN_SAFE_INTEGER,note:"Integers must be within the safe integer range.",inst:a,origin:r,continue:!i.abort});return}}v<s&&d.issues.push({origin:"number",input:v,code:"too_small",minimum:s,inclusive:!0,inst:a,continue:!i.abort}),v>m&&d.issues.push({origin:"number",input:v,code:"too_big",maximum:m,inst:a})}}),Wb=$("$ZodCheckMaxLength",(a,i)=>{var o;Gt.init(a,i),(o=a._zod.def).when??(o.when=r=>{const s=r.value;return!Dr(s)&&s.length!==void 0}),a._zod.onattach.push(r=>{const s=r._zod.bag.maximum??Number.POSITIVE_INFINITY;i.maximum<s&&(r._zod.bag.maximum=i.maximum)}),a._zod.check=r=>{const s=r.value;if(s.length<=i.maximum)return;const d=Ur(s);r.issues.push({origin:d,code:"too_big",maximum:i.maximum,inclusive:!0,input:s,inst:a,continue:!i.abort})}}),Ib=$("$ZodCheckMinLength",(a,i)=>{var o;Gt.init(a,i),(o=a._zod.def).when??(o.when=r=>{const s=r.value;return!Dr(s)&&s.length!==void 0}),a._zod.onattach.push(r=>{const s=r._zod.bag.minimum??Number.NEGATIVE_INFINITY;i.minimum>s&&(r._zod.bag.minimum=i.minimum)}),a._zod.check=r=>{const s=r.value;if(s.length>=i.minimum)return;const d=Ur(s);r.issues.push({origin:d,code:"too_small",minimum:i.minimum,inclusive:!0,input:s,inst:a,continue:!i.abort})}}),Pb=$("$ZodCheckLengthEquals",(a,i)=>{var o;Gt.init(a,i),(o=a._zod.def).when??(o.when=r=>{const s=r.value;return!Dr(s)&&s.length!==void 0}),a._zod.onattach.push(r=>{const s=r._zod.bag;s.minimum=i.length,s.maximum=i.length,s.length=i.length}),a._zod.check=r=>{const s=r.value,m=s.length;if(m===i.length)return;const d=Ur(s),v=m>i.length;r.issues.push({origin:d,...v?{code:"too_big",maximum:i.length}:{code:"too_small",minimum:i.length},inclusive:!0,exact:!0,input:r.value,inst:a,continue:!i.abort})}}),e0=$("$ZodCheckOverwrite",(a,i)=>{Gt.init(a,i),a._zod.check=o=>{o.value=i.tx(o.value)}});class t0{constructor(i=[]){this.content=[],this.indent=0,this&&(this.args=i)}indented(i){this.indent+=1,i(this),this.indent-=1}write(i){if(typeof i=="function"){i(this,{execution:"sync"}),i(this,{execution:"async"});return}const r=i.split(`
`).filter(d=>d),s=Math.min(...r.map(d=>d.length-d.trimStart().length)),m=r.map(d=>d.slice(s)).map(d=>" ".repeat(this.indent*2)+d);for(const d of m)this.content.push(d)}compile(){const i=Function,o=this?.args,s=[...(this?.content??[""]).map(m=>`  ${m}`)];return new i(...o,s.join(`
`))}}const n0={major:4,minor:0,patch:17},Le=$("$ZodType",(a,i)=>{var o;a??(a={}),a._zod.def=i,a._zod.bag=a._zod.bag||{},a._zod.version=n0;const r=[...a._zod.def.checks??[]];a._zod.traits.has("$ZodCheck")&&r.unshift(a);for(const s of r)for(const m of s._zod.onattach)m(a);if(r.length===0)(o=a._zod).deferred??(o.deferred=[]),a._zod.deferred?.push(()=>{a._zod.run=a._zod.parse});else{const s=(m,d,v)=>{let p=ru(m),b;for(const x of d){if(x._zod.def.when){if(!x._zod.def.when(m))continue}else if(p)continue;const E=m.issues.length,j=x._zod.check(m);if(j instanceof Promise&&v?.async===!1)throw new su;if(b||j instanceof Promise)b=(b??Promise.resolve()).then(async()=>{await j,m.issues.length!==E&&(p||(p=ru(m,E)))});else{if(m.issues.length===E)continue;p||(p=ru(m,E))}}return b?b.then(()=>m):m};a._zod.run=(m,d)=>{const v=a._zod.parse(m,d);if(v instanceof Promise){if(d.async===!1)throw new su;return v.then(p=>s(p,r,d))}return s(v,r,d)}}a["~standard"]={validate:s=>{try{const m=Xb(a,s);return m.success?{value:m.data}:{issues:m.error?.issues}}catch{return Qb(a,s).then(d=>d.success?{value:d.data}:{issues:d.error?.issues})}},vendor:"zod",version:1}}),ag=$("$ZodNumber",(a,i)=>{Le.init(a,i),a._zod.pattern=a._zod.bag.pattern??Jb,a._zod.parse=(o,r)=>{if(i.coerce)try{o.value=Number(o.value)}catch{}const s=o.value;if(typeof s=="number"&&!Number.isNaN(s)&&Number.isFinite(s))return o;const m=typeof s=="number"?Number.isNaN(s)?"NaN":Number.isFinite(s)?void 0:"Infinity":void 0;return o.issues.push({expected:"number",code:"invalid_type",input:s,inst:a,...m?{received:m}:{}}),o}}),l0=$("$ZodNumber",(a,i)=>{Fb.init(a,i),ag.init(a,i)}),a0=$("$ZodUnknown",(a,i)=>{Le.init(a,i),a._zod.parse=o=>o}),u0=$("$ZodNever",(a,i)=>{Le.init(a,i),a._zod.parse=(o,r)=>(o.issues.push({expected:"never",code:"invalid_type",input:o.value,inst:a}),o)});function Im(a,i,o){a.issues.length&&i.issues.push(...$h(o,a.issues)),i.value[o]=a.value}const i0=$("$ZodArray",(a,i)=>{Le.init(a,i),a._zod.parse=(o,r)=>{const s=o.value;if(!Array.isArray(s))return o.issues.push({expected:"array",code:"invalid_type",input:s,inst:a}),o;o.value=Array(s.length);const m=[];for(let d=0;d<s.length;d++){const v=s[d],p=i.element._zod.run({value:v,issues:[]},r);p instanceof Promise?m.push(p.then(b=>Im(b,o,d))):Im(p,o,d)}return m.length?Promise.all(m).then(()=>o):o}});function Di(a,i,o,r){a.issues.length&&i.issues.push(...$h(o,a.issues)),a.value===void 0?o in r&&(i.value[o]=void 0):i.value[o]=a.value}const c0=$("$ZodObject",(a,i)=>{Le.init(a,i);const o=Qh(()=>{const E=Object.keys(i.shape);for(const D of E)if(!i.shape[D]._zod.traits.has("$ZodType"))throw new Error(`Invalid element at key "${D}": expected a Zod schema`);const j=Cb(i.shape);return{shape:i.shape,keys:E,keySet:new Set(E),numKeys:E.length,optionalKeys:new Set(j)}});we(a._zod,"propValues",()=>{const E=i.shape,j={};for(const D in E){const B=E[D]._zod;if(B.values){j[D]??(j[D]=new Set);for(const L of B.values)j[D].add(L)}}return j});const r=E=>{const j=new t0(["shape","payload","ctx"]),D=o.value,B=F=>{const R=Wm(F);return`shape[${R}]._zod.run({ value: input[${R}], issues: [] }, ctx)`};j.write("const input = payload.value;");const L=Object.create(null);let G=0;for(const F of D.keys)L[F]=`key_${G++}`;j.write("const newResult = {}");for(const F of D.keys){const R=L[F],Z=Wm(F);j.write(`const ${R} = ${B(F)};`),j.write(`
        if (${R}.issues.length) {
          payload.issues = payload.issues.concat(${R}.issues.map(iss => ({
            ...iss,
            path: iss.path ? [${Z}, ...iss.path] : [${Z}]
          })));
        }
        
        if (${R}.value === undefined) {
          if (${Z} in input) {
            newResult[${Z}] = undefined;
          }
        } else {
          newResult[${Z}] = ${R}.value;
        }
      `)}j.write("payload.value = newResult;"),j.write("return payload;");const Q=j.compile();return(F,R)=>Q(E,F,R)};let s;const m=wr,d=!Xh.jitless,p=d&&Mb.value,b=i.catchall;let x;a._zod.parse=(E,j)=>{x??(x=o.value);const D=E.value;if(!m(D))return E.issues.push({expected:"object",code:"invalid_type",input:D,inst:a}),E;const B=[];if(d&&p&&j?.async===!1&&j.jitless!==!0)s||(s=r(i.shape)),E=s(E,j);else{E.value={};const R=x.shape;for(const Z of x.keys){const J=R[Z]._zod.run({value:D[Z],issues:[]},j);J instanceof Promise?B.push(J.then(oe=>Di(oe,E,Z,D))):Di(J,E,Z,D)}}if(!b)return B.length?Promise.all(B).then(()=>E):E;const L=[],G=x.keySet,Q=b._zod,F=Q.def.type;for(const R of Object.keys(D)){if(G.has(R))continue;if(F==="never"){L.push(R);continue}const Z=Q.run({value:D[R],issues:[]},j);Z instanceof Promise?B.push(Z.then(ee=>Di(ee,E,R,D))):Di(Z,E,R,D)}return L.length&&E.issues.push({code:"unrecognized_keys",keys:L,input:D,inst:a}),B.length?Promise.all(B).then(()=>E):E}});function Pm(a,i,o,r){for(const m of a)if(m.issues.length===0)return i.value=m.value,i;const s=a.filter(m=>!ru(m));return s.length===1?(i.value=s[0].value,s[0]):(i.issues.push({code:"invalid_union",input:i.value,inst:o,errors:a.map(m=>m.issues.map(d=>rl(d,r,ol())))}),i)}const o0=$("$ZodUnion",(a,i)=>{Le.init(a,i),we(a._zod,"optin",()=>i.options.some(s=>s._zod.optin==="optional")?"optional":void 0),we(a._zod,"optout",()=>i.options.some(s=>s._zod.optout==="optional")?"optional":void 0),we(a._zod,"values",()=>{if(i.options.every(s=>s._zod.values))return new Set(i.options.flatMap(s=>Array.from(s._zod.values)))}),we(a._zod,"pattern",()=>{if(i.options.every(s=>s._zod.pattern)){const s=i.options.map(m=>m._zod.pattern);return new RegExp(`^(${s.map(m=>kr(m.source)).join("|")})$`)}});const o=i.options.length===1,r=i.options[0]._zod.run;a._zod.parse=(s,m)=>{if(o)return r(s,m);let d=!1;const v=[];for(const p of i.options){const b=p._zod.run({value:s.value,issues:[]},m);if(b instanceof Promise)v.push(b),d=!0;else{if(b.issues.length===0)return b;v.push(b)}}return d?Promise.all(v).then(p=>Pm(p,s,a,m)):Pm(v,s,a,m)}}),r0=$("$ZodIntersection",(a,i)=>{Le.init(a,i),a._zod.parse=(o,r)=>{const s=o.value,m=i.left._zod.run({value:s,issues:[]},r),d=i.right._zod.run({value:s,issues:[]},r);return m instanceof Promise||d instanceof Promise?Promise.all([m,d]).then(([p,b])=>eh(o,p,b)):eh(o,m,d)}});function zr(a,i){if(a===i)return{valid:!0,data:a};if(a instanceof Date&&i instanceof Date&&+a==+i)return{valid:!0,data:a};if(Gi(a)&&Gi(i)){const o=Object.keys(i),r=Object.keys(a).filter(m=>o.indexOf(m)!==-1),s={...a,...i};for(const m of r){const d=zr(a[m],i[m]);if(!d.valid)return{valid:!1,mergeErrorPath:[m,...d.mergeErrorPath]};s[m]=d.data}return{valid:!0,data:s}}if(Array.isArray(a)&&Array.isArray(i)){if(a.length!==i.length)return{valid:!1,mergeErrorPath:[]};const o=[];for(let r=0;r<a.length;r++){const s=a[r],m=i[r],d=zr(s,m);if(!d.valid)return{valid:!1,mergeErrorPath:[r,...d.mergeErrorPath]};o.push(d.data)}return{valid:!0,data:o}}return{valid:!1,mergeErrorPath:[]}}function eh(a,i,o){if(i.issues.length&&a.issues.push(...i.issues),o.issues.length&&a.issues.push(...o.issues),ru(a))return a;const r=zr(i.value,o.value);if(!r.valid)throw new Error(`Unmergable intersection. Error path: ${JSON.stringify(r.mergeErrorPath)}`);return a.value=r.data,a}const s0=$("$ZodEnum",(a,i)=>{Le.init(a,i);const o=Ab(i.entries),r=new Set(o);a._zod.values=r,a._zod.pattern=new RegExp(`^(${o.filter(s=>Ob.has(typeof s)).map(s=>typeof s=="string"?Rb(s):s.toString()).join("|")})$`),a._zod.parse=(s,m)=>{const d=s.value;return r.has(d)||s.issues.push({code:"invalid_value",values:o,input:d,inst:a}),s}}),f0=$("$ZodTransform",(a,i)=>{Le.init(a,i),a._zod.parse=(o,r)=>{const s=i.transform(o.value,o);if(r.async)return(s instanceof Promise?s:Promise.resolve(s)).then(d=>(o.value=d,o));if(s instanceof Promise)throw new su;return o.value=s,o}});function th(a,i){return a.issues.length&&i===void 0?{issues:[],value:void 0}:a}const d0=$("$ZodOptional",(a,i)=>{Le.init(a,i),a._zod.optin="optional",a._zod.optout="optional",we(a._zod,"values",()=>i.innerType._zod.values?new Set([...i.innerType._zod.values,void 0]):void 0),we(a._zod,"pattern",()=>{const o=i.innerType._zod.pattern;return o?new RegExp(`^(${kr(o.source)})?$`):void 0}),a._zod.parse=(o,r)=>{if(i.innerType._zod.optin==="optional"){const s=i.innerType._zod.run(o,r);return s instanceof Promise?s.then(m=>th(m,o.value)):th(s,o.value)}return o.value===void 0?o:i.innerType._zod.run(o,r)}}),m0=$("$ZodNullable",(a,i)=>{Le.init(a,i),we(a._zod,"optin",()=>i.innerType._zod.optin),we(a._zod,"optout",()=>i.innerType._zod.optout),we(a._zod,"pattern",()=>{const o=i.innerType._zod.pattern;return o?new RegExp(`^(${kr(o.source)}|null)$`):void 0}),we(a._zod,"values",()=>i.innerType._zod.values?new Set([...i.innerType._zod.values,null]):void 0),a._zod.parse=(o,r)=>o.value===null?o:i.innerType._zod.run(o,r)}),h0=$("$ZodDefault",(a,i)=>{Le.init(a,i),a._zod.optin="optional",we(a._zod,"values",()=>i.innerType._zod.values),a._zod.parse=(o,r)=>{if(o.value===void 0)return o.value=i.defaultValue,o;const s=i.innerType._zod.run(o,r);return s instanceof Promise?s.then(m=>nh(m,i)):nh(s,i)}});function nh(a,i){return a.value===void 0&&(a.value=i.defaultValue),a}const g0=$("$ZodPrefault",(a,i)=>{Le.init(a,i),a._zod.optin="optional",we(a._zod,"values",()=>i.innerType._zod.values),a._zod.parse=(o,r)=>(o.value===void 0&&(o.value=i.defaultValue),i.innerType._zod.run(o,r))}),v0=$("$ZodNonOptional",(a,i)=>{Le.init(a,i),we(a._zod,"values",()=>{const o=i.innerType._zod.values;return o?new Set([...o].filter(r=>r!==void 0)):void 0}),a._zod.parse=(o,r)=>{const s=i.innerType._zod.run(o,r);return s instanceof Promise?s.then(m=>lh(m,a)):lh(s,a)}});function lh(a,i){return!a.issues.length&&a.value===void 0&&a.issues.push({code:"invalid_type",expected:"nonoptional",input:a.value,inst:i}),a}const p0=$("$ZodCatch",(a,i)=>{Le.init(a,i),we(a._zod,"optin",()=>i.innerType._zod.optin),we(a._zod,"optout",()=>i.innerType._zod.optout),we(a._zod,"values",()=>i.innerType._zod.values),a._zod.parse=(o,r)=>{const s=i.innerType._zod.run(o,r);return s instanceof Promise?s.then(m=>(o.value=m.value,m.issues.length&&(o.value=i.catchValue({...o,error:{issues:m.issues.map(d=>rl(d,r,ol()))},input:o.value}),o.issues=[]),o)):(o.value=s.value,s.issues.length&&(o.value=i.catchValue({...o,error:{issues:s.issues.map(m=>rl(m,r,ol()))},input:o.value}),o.issues=[]),o)}}),y0=$("$ZodPipe",(a,i)=>{Le.init(a,i),we(a._zod,"values",()=>i.in._zod.values),we(a._zod,"optin",()=>i.in._zod.optin),we(a._zod,"optout",()=>i.out._zod.optout),we(a._zod,"propValues",()=>i.in._zod.propValues),a._zod.parse=(o,r)=>{const s=i.in._zod.run(o,r);return s instanceof Promise?s.then(m=>ah(m,i,r)):ah(s,i,r)}});function ah(a,i,o){return a.issues.length?a:i.out._zod.run({value:a.value,issues:a.issues},o)}const b0=$("$ZodReadonly",(a,i)=>{Le.init(a,i),we(a._zod,"propValues",()=>i.innerType._zod.propValues),we(a._zod,"values",()=>i.innerType._zod.values),we(a._zod,"optin",()=>i.innerType._zod.optin),we(a._zod,"optout",()=>i.innerType._zod.optout),a._zod.parse=(o,r)=>{const s=i.innerType._zod.run(o,r);return s instanceof Promise?s.then(uh):uh(s)}});function uh(a){return a.value=Object.freeze(a.value),a}const x0=$("$ZodCustom",(a,i)=>{Gt.init(a,i),Le.init(a,i),a._zod.parse=(o,r)=>o,a._zod.check=o=>{const r=o.value,s=i.fn(r);if(s instanceof Promise)return s.then(m=>ih(m,o,r,a));ih(s,o,r,a)}});function ih(a,i,o,r){if(!a){const s={code:"custom",input:o,inst:r,path:[...r._zod.def.path??[]],continue:!r._zod.def.abort};r._zod.def.params&&(s.params=r._zod.def.params),i.issues.push(fu(s))}}class _0{constructor(){this._map=new Map,this._idmap=new Map}add(i,...o){const r=o[0];if(this._map.set(i,r),r&&typeof r=="object"&&"id"in r){if(this._idmap.has(r.id))throw new Error(`ID ${r.id} already exists in the registry`);this._idmap.set(r.id,i)}return this}clear(){return this._map=new Map,this._idmap=new Map,this}remove(i){const o=this._map.get(i);return o&&typeof o=="object"&&"id"in o&&this._idmap.delete(o.id),this._map.delete(i),this}get(i){const o=i._zod.parent;if(o){const r={...this.get(o)??{}};delete r.id;const s={...r,...this._map.get(i)};return Object.keys(s).length?s:void 0}return this._map.get(i)}has(i){return this._map.has(i)}}function S0(){return new _0}const ki=S0();function w0(a,i){return new a({type:"number",checks:[],...ke(i)})}function z0(a,i){return new a({type:"number",check:"number_format",abort:!1,format:"safeint",...ke(i)})}function E0(a){return new a({type:"unknown"})}function N0(a,i){return new a({type:"never",...ke(i)})}function ch(a,i){return new ng({check:"less_than",...ke(i),value:a,inclusive:!1})}function gr(a,i){return new ng({check:"less_than",...ke(i),value:a,inclusive:!0})}function oh(a,i){return new lg({check:"greater_than",...ke(i),value:a,inclusive:!1})}function vr(a,i){return new lg({check:"greater_than",...ke(i),value:a,inclusive:!0})}function rh(a,i){return new $b({check:"multiple_of",...ke(i),value:a})}function A0(a,i){return new Wb({check:"max_length",...ke(i),maximum:a})}function sh(a,i){return new Ib({check:"min_length",...ke(i),minimum:a})}function j0(a,i){return new Pb({check:"length_equals",...ke(i),length:a})}function T0(a){return new e0({check:"overwrite",tx:a})}function M0(a,i,o){return new a({type:"array",element:i,...ke(o)})}function O0(a,i,o){return new a({type:"custom",check:"custom",fn:i,...ke(o)})}function R0(a){const i=C0(o=>(o.addIssue=r=>{if(typeof r=="string")o.issues.push(fu(r,o.value,i._zod.def));else{const s=r;s.fatal&&(s.continue=!1),s.code??(s.code="custom"),s.input??(s.input=o.value),s.inst??(s.inst=i),s.continue??(s.continue=!i._zod.def.abort),o.issues.push(fu(s))}},a(o.value,o)));return i}function C0(a,i){const o=new Gt({check:"custom",...ke(i)});return o._zod.check=a,o}const D0=(a,i)=>{Wh.init(a,i),a.name="ZodError",Object.defineProperties(a,{format:{value:o=>Gb(a,o)},flatten:{value:o=>Lb(a,o)},addIssue:{value:o=>{a.issues.push(o),a.message=JSON.stringify(a.issues,Sr,2)}},addIssues:{value:o=>{a.issues.push(...o),a.message=JSON.stringify(a.issues,Sr,2)}},isEmpty:{get(){return a.issues.length===0}}})},Xi=$("ZodError",D0,{Parent:Error}),k0=qb(Xi),U0=Yb(Xi),V0=Ph(Xi),Z0=eg(Xi),Je=$("ZodType",(a,i)=>(Le.init(a,i),a.def=i,Object.defineProperty(a,"_def",{value:i}),a.check=(...o)=>a.clone({...i,checks:[...i.checks??[],...o.map(r=>typeof r=="function"?{_zod:{check:r,def:{check:"custom"},onattach:[]}}:r)]}),a.clone=(o,r)=>fl(a,o,r),a.brand=()=>a,a.register=(o,r)=>(o.add(a,r),a),a.parse=(o,r)=>k0(a,o,r,{callee:a.parse}),a.safeParse=(o,r)=>V0(a,o,r),a.parseAsync=async(o,r)=>U0(a,o,r,{callee:a.parseAsync}),a.safeParseAsync=async(o,r)=>Z0(a,o,r),a.spa=a.safeParseAsync,a.refine=(o,r)=>a.check(fx(o,r)),a.superRefine=o=>a.check(dx(o)),a.overwrite=o=>a.check(T0(o)),a.optional=()=>hh(a),a.nullable=()=>gh(a),a.nullish=()=>hh(gh(a)),a.nonoptional=o=>ax(a,o),a.array=()=>Er(a),a.or=o=>K0([a,o]),a.and=o=>$0(a,o),a.transform=o=>vh(a,I0(o)),a.default=o=>tx(a,o),a.prefault=o=>lx(a,o),a.catch=o=>ix(a,o),a.pipe=o=>vh(a,o),a.readonly=()=>rx(a),a.describe=o=>{const r=a.clone();return ki.add(r,{description:o}),r},Object.defineProperty(a,"description",{get(){return ki.get(a)?.description},configurable:!0}),a.meta=(...o)=>{if(o.length===0)return ki.get(a);const r=a.clone();return ki.add(r,o[0]),r},a.isOptional=()=>a.safeParse(void 0).success,a.isNullable=()=>a.safeParse(null).success,a)),ug=$("ZodNumber",(a,i)=>{ag.init(a,i),Je.init(a,i),a.gt=(r,s)=>a.check(oh(r,s)),a.gte=(r,s)=>a.check(vr(r,s)),a.min=(r,s)=>a.check(vr(r,s)),a.lt=(r,s)=>a.check(ch(r,s)),a.lte=(r,s)=>a.check(gr(r,s)),a.max=(r,s)=>a.check(gr(r,s)),a.int=r=>a.check(dh(r)),a.safe=r=>a.check(dh(r)),a.positive=r=>a.check(oh(0,r)),a.nonnegative=r=>a.check(vr(0,r)),a.negative=r=>a.check(ch(0,r)),a.nonpositive=r=>a.check(gr(0,r)),a.multipleOf=(r,s)=>a.check(rh(r,s)),a.step=(r,s)=>a.check(rh(r,s)),a.finite=()=>a;const o=a._zod.bag;a.minValue=Math.max(o.minimum??Number.NEGATIVE_INFINITY,o.exclusiveMinimum??Number.NEGATIVE_INFINITY)??null,a.maxValue=Math.min(o.maximum??Number.POSITIVE_INFINITY,o.exclusiveMaximum??Number.POSITIVE_INFINITY)??null,a.isInt=(o.format??"").includes("int")||Number.isSafeInteger(o.multipleOf??.5),a.isFinite=!0,a.format=o.format??null});function fh(a){return w0(ug,a)}const H0=$("ZodNumberFormat",(a,i)=>{l0.init(a,i),ug.init(a,i)});function dh(a){return z0(H0,a)}const B0=$("ZodUnknown",(a,i)=>{a0.init(a,i),Je.init(a,i)});function mh(){return E0(B0)}const L0=$("ZodNever",(a,i)=>{u0.init(a,i),Je.init(a,i)});function G0(a){return N0(L0,a)}const q0=$("ZodArray",(a,i)=>{i0.init(a,i),Je.init(a,i),a.element=i.element,a.min=(o,r)=>a.check(sh(o,r)),a.nonempty=o=>a.check(sh(1,o)),a.max=(o,r)=>a.check(A0(o,r)),a.length=(o,r)=>a.check(j0(o,r)),a.unwrap=()=>a.element});function Er(a,i){return M0(q0,a,i)}const Y0=$("ZodObject",(a,i)=>{c0.init(a,i),Je.init(a,i),we(a,"shape",()=>i.shape),a.keyof=()=>F0(Object.keys(a._zod.def.shape)),a.catchall=o=>a.clone({...a._zod.def,catchall:o}),a.passthrough=()=>a.clone({...a._zod.def,catchall:mh()}),a.loose=()=>a.clone({...a._zod.def,catchall:mh()}),a.strict=()=>a.clone({...a._zod.def,catchall:G0()}),a.strip=()=>a.clone({...a._zod.def,catchall:void 0}),a.extend=o=>Vb(a,o),a.merge=o=>Zb(a,o),a.pick=o=>kb(a,o),a.omit=o=>Ub(a,o),a.partial=(...o)=>Hb(ig,a,o[0]),a.required=(...o)=>Bb(cg,a,o[0])});function X0(a,i){const o={type:"object",get shape(){return sl(this,"shape",a?Tb(a):{}),this.shape},...ke(i)};return new Y0(o)}const Q0=$("ZodUnion",(a,i)=>{o0.init(a,i),Je.init(a,i),a.options=i.options});function K0(a,i){return new Q0({type:"union",options:a,...ke(i)})}const J0=$("ZodIntersection",(a,i)=>{r0.init(a,i),Je.init(a,i)});function $0(a,i){return new J0({type:"intersection",left:a,right:i})}const Nr=$("ZodEnum",(a,i)=>{s0.init(a,i),Je.init(a,i),a.enum=i.entries,a.options=Object.values(i.entries);const o=new Set(Object.keys(i.entries));a.extract=(r,s)=>{const m={};for(const d of r)if(o.has(d))m[d]=i.entries[d];else throw new Error(`Key ${d} not found in enum`);return new Nr({...i,checks:[],...ke(s),entries:m})},a.exclude=(r,s)=>{const m={...i.entries};for(const d of r)if(o.has(d))delete m[d];else throw new Error(`Key ${d} not found in enum`);return new Nr({...i,checks:[],...ke(s),entries:m})}});function F0(a,i){const o=Array.isArray(a)?Object.fromEntries(a.map(r=>[r,r])):a;return new Nr({type:"enum",entries:o,...ke(i)})}const W0=$("ZodTransform",(a,i)=>{f0.init(a,i),Je.init(a,i),a._zod.parse=(o,r)=>{o.addIssue=m=>{if(typeof m=="string")o.issues.push(fu(m,o.value,i));else{const d=m;d.fatal&&(d.continue=!1),d.code??(d.code="custom"),d.input??(d.input=o.value),d.inst??(d.inst=a),o.issues.push(fu(d))}};const s=i.transform(o.value,o);return s instanceof Promise?s.then(m=>(o.value=m,o)):(o.value=s,o)}});function I0(a){return new W0({type:"transform",transform:a})}const ig=$("ZodOptional",(a,i)=>{d0.init(a,i),Je.init(a,i),a.unwrap=()=>a._zod.def.innerType});function hh(a){return new ig({type:"optional",innerType:a})}const P0=$("ZodNullable",(a,i)=>{m0.init(a,i),Je.init(a,i),a.unwrap=()=>a._zod.def.innerType});function gh(a){return new P0({type:"nullable",innerType:a})}const ex=$("ZodDefault",(a,i)=>{h0.init(a,i),Je.init(a,i),a.unwrap=()=>a._zod.def.innerType,a.removeDefault=a.unwrap});function tx(a,i){return new ex({type:"default",innerType:a,get defaultValue(){return typeof i=="function"?i():Jh(i)}})}const nx=$("ZodPrefault",(a,i)=>{g0.init(a,i),Je.init(a,i),a.unwrap=()=>a._zod.def.innerType});function lx(a,i){return new nx({type:"prefault",innerType:a,get defaultValue(){return typeof i=="function"?i():Jh(i)}})}const cg=$("ZodNonOptional",(a,i)=>{v0.init(a,i),Je.init(a,i),a.unwrap=()=>a._zod.def.innerType});function ax(a,i){return new cg({type:"nonoptional",innerType:a,...ke(i)})}const ux=$("ZodCatch",(a,i)=>{p0.init(a,i),Je.init(a,i),a.unwrap=()=>a._zod.def.innerType,a.removeCatch=a.unwrap});function ix(a,i){return new ux({type:"catch",innerType:a,catchValue:typeof i=="function"?i:()=>i})}const cx=$("ZodPipe",(a,i)=>{y0.init(a,i),Je.init(a,i),a.in=i.in,a.out=i.out});function vh(a,i){return new cx({type:"pipe",in:a,out:i})}const ox=$("ZodReadonly",(a,i)=>{b0.init(a,i),Je.init(a,i),a.unwrap=()=>a._zod.def.innerType});function rx(a){return new ox({type:"readonly",innerType:a})}const sx=$("ZodCustom",(a,i)=>{x0.init(a,i),Je.init(a,i)});function fx(a,i={}){return O0(sx,a,i)}function dx(a){return R0(a)}X0({x:Er(fh()),y:Er(fh())});function cu(a,i,o){const r=[];return!i||!o||(i.length!==o.length&&r.push(`${a}: X/Y 长度不一致 (${i.length} vs ${o.length})`),i.length===0&&r.push(`${a}: 数据为空`)),r}function mx(a){return a.length===0?null:a.slice(0,5).join("；")}function Vr(a){return new Promise((i,o)=>{a.oncomplete=a.onsuccess=()=>i(a.result),a.onabort=a.onerror=()=>o(a.error)})}function hx(a,i){let o;const r=()=>{if(o)return o;const s=indexedDB.open(a);return s.onupgradeneeded=()=>s.result.createObjectStore(i),o=Vr(s),o.then(m=>{m.onclose=()=>o=void 0},()=>{}),o};return(s,m)=>r().then(d=>m(d.transaction(i,s).objectStore(i)))}let pr;function og(){return pr||(pr=hx("keyval-store","keyval")),pr}function gx(a,i=og()){return i("readonly",o=>Vr(o.get(a)))}function vx(a,i,o=og()){return o("readwrite",r=>(r.put(i,a),Vr(r.transaction)))}const rg="lastFileHandle";async function px(a){try{await vx(rg,a)}catch{}}async function yx(){try{return await gx(rg)}catch{return}}function sg(){return typeof window<"u"&&"showOpenFilePicker"in window}async function bx(){if(!sg())return null;try{const a=await yx(),i=await window.showOpenFilePicker({multiple:!1,types:[{description:"CSV",accept:{"text/csv":[".csv"]}},{description:"Excel",accept:{"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":[".xlsx"],"application/vnd.ms-excel":[".xls"]}}],startIn:a??"documents"});if(!i||i.length===0)return null;const o=i[0],r=await o.getFile();return await px(o),r}catch{return null}}class Qi{worker=null;constructor(){try{this.worker=new Worker(new URL("/assets/computeWorker-1ukLMCn1.js",import.meta.url),{type:"module"})}catch{this.worker=null}}getWorkerOrThrow(){if(!this.worker)throw new Error("计算 Worker 初始化失败");return this.worker}async compute(i,o){const r=this.getWorkerOrThrow();return new Promise((s,m)=>{const d=p=>{const b=p.data;if(!b.ok){v(),m(new Error(b.error||"计算失败"));return}v(),s({livParameters:b.livParameters,spectralParameters:b.spectralParameters,livFitDiagnostics:b.livFitDiagnostics,plots:[]})},v=()=>r.removeEventListener("message",d);r.addEventListener("message",d),r.postMessage({kind:"compute",data:i,config:o})})}}function xx(a){const i=a.toLowerCase();return i.includes("volt")?"voltage":i.includes("wave")||i.includes("spectrum")?"wavelength":"power"}function _x(a,i){const o=a.map(s=>s[0]).filter(s=>Number.isFinite(s)),r=a.map(s=>s[1]).filter(s=>Number.isFinite(s));return i==="power"?{power:{current:o,power:r}}:i==="voltage"?{voltage:{current:o,voltage:r}}:{wavelength:{wavelength:o,intensity:r}}}function Sx(){const{setData:a,setError:i,setProcessing:o,setProgress:r,isProcessing:s,progress:m}=re(),[d,v]=k.useState(""),p=k.useState(()=>new qh)[0],b=k.useState(()=>new Qi)[0],x=k.useRef(null);async function E(j){if(!j||j.length===0)return;const D=j[0];v(D.name),o(!0),r(5),i(null);try{const B=D.name.split(".").pop()?.toLowerCase();let L=null;const G=(D.size??0)>10*1024*1024;if(B==="csv"){const Z=G?{data:await p.parseCSV(D)}:await Rr(D);if(Z.error)throw new Error(Z.error);r(40);const ee=xx(D.name);L=_x(Z.data??[],ee)}else if(B==="xlsx"||B==="xls"){const Z=G?{data:await p.parseExcel(D)}:await Cr(D);if(Z.error)throw new Error(Z.error);r(40),L=Li(Z.data??{})}else throw new Error("不支持的文件类型，请选择 CSV 或 Excel 文件");r(70);const Q=Yh(L),F=[];Q.power&&F.push(...cu("P-I",Q.power.current,Q.power.power)),Q.voltage&&F.push(...cu("V-I",Q.voltage.current,Q.voltage.voltage)),Q.wavelength&&F.push(...cu("光谱",Q.wavelength.wavelength,Q.wavelength.intensity)),Q.hff&&F.push(...cu("HFF",Q.hff.angle,Q.hff.intensity)),Q.vff&&F.push(...cu("VFF",Q.vff.angle,Q.vff.intensity));const R=mx(F);R&&i(R),r(90),a(Q),r(95);try{const Z=await b.compute(Q,re.getState().processingConfig);re.getState().setResults(Z)}catch(Z){i(Z.message)}r(100)}catch(B){i(B.message)}finally{o(!1),setTimeout(()=>r(0),400)}}return h.jsxs("div",{className:"space-y-3",children:[h.jsx("div",{className:"text-sm text-muted-foreground",children:"选择 CSV 或 Excel 文件(.xlsx/.xls)"}),h.jsxs("label",{className:"inline-block",children:[h.jsx("input",{type:"file",accept:".csv,.xlsx,.xls",className:"hidden",ref:x,onChange:j=>E(j.target.files)}),h.jsx(Be,{disabled:s,children:s?"处理中...":"选择文件"})]}),h.jsx("button",{id:"__open_files_btn",type:"button",className:"hidden",onClick:()=>x.current?.click(),"aria-hidden":!0,tabIndex:-1}),sg()&&h.jsx(Be,{variant:"outline",size:"sm",disabled:s,onClick:async()=>{const j=await bx();j&&await E({0:j,length:1})},children:"从最近目录选择"}),s&&h.jsx(Bh,{value:m}),d&&h.jsxs("div",{className:"text-xs text-muted-foreground",children:["当前文件: ",d]})]})}function wx(){const{data:a}=re();if(!a)return null;const i=[];return a.power&&i.push({label:"P-I 点数",count:Math.min(a.power.current.length,a.power.power.length)}),a.voltage&&i.push({label:"V-I 点数",count:Math.min(a.voltage.current.length,a.voltage.voltage.length)}),a.wavelength&&i.push({label:"光谱 点数",count:Math.min(a.wavelength.wavelength.length,a.wavelength.intensity.length)}),a.hff&&i.push({label:"HFF 远场 点数",count:Math.min(a.hff.angle.length,a.hff.intensity.length)}),a.vff&&i.push({label:"VFF 远场 点数",count:Math.min(a.vff.angle.length,a.vff.intensity.length)}),h.jsxs("div",{className:"rounded border p-3 text-sm",children:[h.jsx("div",{className:"font-semibold mb-2",children:"数据预览"}),h.jsx("ul",{className:"space-y-1",children:i.map(o=>h.jsxs("li",{className:"flex justify-between",children:[h.jsx("span",{children:o.label}),h.jsx("span",{className:"text-gray-600",children:o.count})]},o.label))})]})}const il=k.forwardRef(({className:a,...i},o)=>h.jsx("div",{ref:o,className:aa("rounded-lg border bg-card text-card-foreground shadow-sm",a),...i}));il.displayName="Card";const Pl=k.forwardRef(({className:a,...i},o)=>h.jsx("div",{ref:o,className:aa("flex flex-col space-y-1.5 p-6",a),...i}));Pl.displayName="CardHeader";const ea=k.forwardRef(({className:a,...i},o)=>h.jsx("h3",{ref:o,className:aa("text-2xl font-semibold leading-none tracking-tight",a),...i}));ea.displayName="CardTitle";const cl=k.forwardRef(({className:a,...i},o)=>h.jsx("div",{ref:o,className:aa("p-6 pt-0",a),...i}));cl.displayName="CardContent";function cn(a,i,{checkForDefaultPrevented:o=!0}={}){return function(s){if(a?.(s),o===!1||!s.defaultPrevented)return i?.(s)}}function zx(a){const i=a+"CollectionProvider",[o,r]=Yi(i),[s,m]=o(i,{collectionRef:{current:null},itemMap:new Map}),d=L=>{const{scope:G,children:Q}=L,F=Ct.useRef(null),R=Ct.useRef(new Map).current;return h.jsx(s,{scope:G,itemMap:R,collectionRef:F,children:Q})};d.displayName=i;const v=a+"CollectionSlot",p=Hi(v),b=Ct.forwardRef((L,G)=>{const{scope:Q,children:F}=L,R=m(v,Q),Z=Zi(G,R.collectionRef);return h.jsx(p,{ref:Z,children:F})});b.displayName=v;const x=a+"CollectionItemSlot",E="data-radix-collection-item",j=Hi(x),D=Ct.forwardRef((L,G)=>{const{scope:Q,children:F,...R}=L,Z=Ct.useRef(null),ee=Zi(G,Z),J=m(x,Q);return Ct.useEffect(()=>(J.itemMap.set(Z,{ref:Z,...R}),()=>void J.itemMap.delete(Z))),h.jsx(j,{[E]:"",ref:ee,children:F})});D.displayName=x;function B(L){const G=m(a+"CollectionConsumer",L);return Ct.useCallback(()=>{const F=G.collectionRef.current;if(!F)return[];const R=Array.from(F.querySelectorAll(`[${E}]`));return Array.from(G.itemMap.values()).sort((J,oe)=>R.indexOf(J.ref.current)-R.indexOf(oe.ref.current))},[G.collectionRef,G.itemMap])}return[{Provider:d,Slot:b,ItemSlot:D},B,r]}var qi=globalThis?.document?k.useLayoutEffect:()=>{},Ex=_h[" useId ".trim().toString()]||(()=>{}),Nx=0;function fg(a){const[i,o]=k.useState(Ex());return qi(()=>{o(r=>r??String(Nx++))},[a]),a||(i?`radix-${i}`:"")}function Ax(a){const i=k.useRef(a);return k.useEffect(()=>{i.current=a}),k.useMemo(()=>(...o)=>i.current?.(...o),[])}var jx=_h[" useInsertionEffect ".trim().toString()]||qi;function dg({prop:a,defaultProp:i,onChange:o=()=>{},caller:r}){const[s,m,d]=Tx({defaultProp:i,onChange:o}),v=a!==void 0,p=v?a:s;{const x=k.useRef(a!==void 0);k.useEffect(()=>{const E=x.current;E!==v&&console.warn(`${r} is changing from ${E?"controlled":"uncontrolled"} to ${v?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),x.current=v},[v,r])}const b=k.useCallback(x=>{if(v){const E=Mx(x)?x(a):x;E!==a&&d.current?.(E)}else m(x)},[v,a,m,d]);return[p,b]}function Tx({defaultProp:a,onChange:i}){const[o,r]=k.useState(a),s=k.useRef(o),m=k.useRef(i);return jx(()=>{m.current=i},[i]),k.useEffect(()=>{s.current!==o&&(m.current?.(o),s.current=o)},[o,s]),[o,r,m]}function Mx(a){return typeof a=="function"}var Ox=k.createContext(void 0);function mg(a){const i=k.useContext(Ox);return a||i||"ltr"}var yr="rovingFocusGroup.onEntryFocus",Rx={bubbles:!1,cancelable:!0},gu="RovingFocusGroup",[Ar,hg,Cx]=zx(gu),[Dx,gg]=Yi(gu,[Cx]),[kx,Ux]=Dx(gu),vg=k.forwardRef((a,i)=>h.jsx(Ar.Provider,{scope:a.__scopeRovingFocusGroup,children:h.jsx(Ar.Slot,{scope:a.__scopeRovingFocusGroup,children:h.jsx(Vx,{...a,ref:i})})}));vg.displayName=gu;var Vx=k.forwardRef((a,i)=>{const{__scopeRovingFocusGroup:o,orientation:r,loop:s=!1,dir:m,currentTabStopId:d,defaultCurrentTabStopId:v,onCurrentTabStopIdChange:p,onEntryFocus:b,preventScrollOnEntryFocus:x=!1,...E}=a,j=k.useRef(null),D=Zi(i,j),B=mg(m),[L,G]=dg({prop:d,defaultProp:v??null,onChange:p,caller:gu}),[Q,F]=k.useState(!1),R=Ax(b),Z=hg(o),ee=k.useRef(!1),[J,oe]=k.useState(0);return k.useEffect(()=>{const V=j.current;if(V)return V.addEventListener(yr,R),()=>V.removeEventListener(yr,R)},[R]),h.jsx(kx,{scope:o,orientation:r,dir:B,loop:s,currentTabStopId:L,onItemFocus:k.useCallback(V=>G(V),[G]),onItemShiftTab:k.useCallback(()=>F(!0),[]),onFocusableItemAdd:k.useCallback(()=>oe(V=>V+1),[]),onFocusableItemRemove:k.useCallback(()=>oe(V=>V-1),[]),children:h.jsx(Vn.div,{tabIndex:Q||J===0?-1:0,"data-orientation":r,...E,ref:D,style:{outline:"none",...a.style},onMouseDown:cn(a.onMouseDown,()=>{ee.current=!0}),onFocus:cn(a.onFocus,V=>{const ze=!ee.current;if(V.target===V.currentTarget&&ze&&!Q){const Re=new CustomEvent(yr,Rx);if(V.currentTarget.dispatchEvent(Re),!Re.defaultPrevented){const ot=Z().filter(T=>T.focusable),Ue=ot.find(T=>T.active),qt=ot.find(T=>T.id===L),Ge=[Ue,qt,...ot].filter(Boolean).map(T=>T.ref.current);bg(Ge,x)}}ee.current=!1}),onBlur:cn(a.onBlur,()=>F(!1))})})}),pg="RovingFocusGroupItem",yg=k.forwardRef((a,i)=>{const{__scopeRovingFocusGroup:o,focusable:r=!0,active:s=!1,tabStopId:m,children:d,...v}=a,p=fg(),b=m||p,x=Ux(pg,o),E=x.currentTabStopId===b,j=hg(o),{onFocusableItemAdd:D,onFocusableItemRemove:B,currentTabStopId:L}=x;return k.useEffect(()=>{if(r)return D(),()=>B()},[r,D,B]),h.jsx(Ar.ItemSlot,{scope:o,id:b,focusable:r,active:s,children:h.jsx(Vn.span,{tabIndex:E?0:-1,"data-orientation":x.orientation,...v,ref:i,onMouseDown:cn(a.onMouseDown,G=>{r?x.onItemFocus(b):G.preventDefault()}),onFocus:cn(a.onFocus,()=>x.onItemFocus(b)),onKeyDown:cn(a.onKeyDown,G=>{if(G.key==="Tab"&&G.shiftKey){x.onItemShiftTab();return}if(G.target!==G.currentTarget)return;const Q=Bx(G,x.orientation,x.dir);if(Q!==void 0){if(G.metaKey||G.ctrlKey||G.altKey||G.shiftKey)return;G.preventDefault();let R=j().filter(Z=>Z.focusable).map(Z=>Z.ref.current);if(Q==="last")R.reverse();else if(Q==="prev"||Q==="next"){Q==="prev"&&R.reverse();const Z=R.indexOf(G.currentTarget);R=x.loop?Lx(R,Z+1):R.slice(Z+1)}setTimeout(()=>bg(R))}}),children:typeof d=="function"?d({isCurrentTabStop:E,hasTabStop:L!=null}):d})})});yg.displayName=pg;var Zx={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function Hx(a,i){return i!=="rtl"?a:a==="ArrowLeft"?"ArrowRight":a==="ArrowRight"?"ArrowLeft":a}function Bx(a,i,o){const r=Hx(a.key,o);if(!(i==="vertical"&&["ArrowLeft","ArrowRight"].includes(r))&&!(i==="horizontal"&&["ArrowUp","ArrowDown"].includes(r)))return Zx[r]}function bg(a,i=!1){const o=document.activeElement;for(const r of a)if(r===o||(r.focus({preventScroll:i}),document.activeElement!==o))return}function Lx(a,i){return a.map((o,r)=>a[(i+r)%a.length])}var Gx=vg,qx=yg;function Yx(a,i){return k.useReducer((o,r)=>i[o][r]??o,a)}var xg=a=>{const{present:i,children:o}=a,r=Xx(i),s=typeof o=="function"?o({present:r.isPresent}):k.Children.only(o),m=Zi(r.ref,Qx(s));return typeof o=="function"||r.isPresent?k.cloneElement(s,{ref:m}):null};xg.displayName="Presence";function Xx(a){const[i,o]=k.useState(),r=k.useRef(null),s=k.useRef(a),m=k.useRef("none"),d=a?"mounted":"unmounted",[v,p]=Yx(d,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return k.useEffect(()=>{const b=Ui(r.current);m.current=v==="mounted"?b:"none"},[v]),qi(()=>{const b=r.current,x=s.current;if(x!==a){const j=m.current,D=Ui(b);a?p("MOUNT"):D==="none"||b?.display==="none"?p("UNMOUNT"):p(x&&j!==D?"ANIMATION_OUT":"UNMOUNT"),s.current=a}},[a,p]),qi(()=>{if(i){let b;const x=i.ownerDocument.defaultView??window,E=D=>{const L=Ui(r.current).includes(D.animationName);if(D.target===i&&L&&(p("ANIMATION_END"),!s.current)){const G=i.style.animationFillMode;i.style.animationFillMode="forwards",b=x.setTimeout(()=>{i.style.animationFillMode==="forwards"&&(i.style.animationFillMode=G)})}},j=D=>{D.target===i&&(m.current=Ui(r.current))};return i.addEventListener("animationstart",j),i.addEventListener("animationcancel",E),i.addEventListener("animationend",E),()=>{x.clearTimeout(b),i.removeEventListener("animationstart",j),i.removeEventListener("animationcancel",E),i.removeEventListener("animationend",E)}}else p("ANIMATION_END")},[i,p]),{isPresent:["mounted","unmountSuspended"].includes(v),ref:k.useCallback(b=>{r.current=b?getComputedStyle(b):null,o(b)},[])}}function Ui(a){return a?.animationName||"none"}function Qx(a){let i=Object.getOwnPropertyDescriptor(a.props,"ref")?.get,o=i&&"isReactWarning"in i&&i.isReactWarning;return o?a.ref:(i=Object.getOwnPropertyDescriptor(a,"ref")?.get,o=i&&"isReactWarning"in i&&i.isReactWarning,o?a.props.ref:a.props.ref||a.ref)}var Ki="Tabs",[Kx,u_]=Yi(Ki,[gg]),_g=gg(),[Jx,Zr]=Kx(Ki),Sg=k.forwardRef((a,i)=>{const{__scopeTabs:o,value:r,onValueChange:s,defaultValue:m,orientation:d="horizontal",dir:v,activationMode:p="automatic",...b}=a,x=mg(v),[E,j]=dg({prop:r,onChange:s,defaultProp:m??"",caller:Ki});return h.jsx(Jx,{scope:o,baseId:fg(),value:E,onValueChange:j,orientation:d,dir:x,activationMode:p,children:h.jsx(Vn.div,{dir:x,"data-orientation":d,...b,ref:i})})});Sg.displayName=Ki;var wg="TabsList",zg=k.forwardRef((a,i)=>{const{__scopeTabs:o,loop:r=!0,...s}=a,m=Zr(wg,o),d=_g(o);return h.jsx(Gx,{asChild:!0,...d,orientation:m.orientation,dir:m.dir,loop:r,children:h.jsx(Vn.div,{role:"tablist","aria-orientation":m.orientation,...s,ref:i})})});zg.displayName=wg;var Eg="TabsTrigger",Ng=k.forwardRef((a,i)=>{const{__scopeTabs:o,value:r,disabled:s=!1,...m}=a,d=Zr(Eg,o),v=_g(o),p=Tg(d.baseId,r),b=Mg(d.baseId,r),x=r===d.value;return h.jsx(qx,{asChild:!0,...v,focusable:!s,active:x,children:h.jsx(Vn.button,{type:"button",role:"tab","aria-selected":x,"aria-controls":b,"data-state":x?"active":"inactive","data-disabled":s?"":void 0,disabled:s,id:p,...m,ref:i,onMouseDown:cn(a.onMouseDown,E=>{!s&&E.button===0&&E.ctrlKey===!1?d.onValueChange(r):E.preventDefault()}),onKeyDown:cn(a.onKeyDown,E=>{[" ","Enter"].includes(E.key)&&d.onValueChange(r)}),onFocus:cn(a.onFocus,()=>{const E=d.activationMode!=="manual";!x&&!s&&E&&d.onValueChange(r)})})})});Ng.displayName=Eg;var Ag="TabsContent",jg=k.forwardRef((a,i)=>{const{__scopeTabs:o,value:r,forceMount:s,children:m,...d}=a,v=Zr(Ag,o),p=Tg(v.baseId,r),b=Mg(v.baseId,r),x=r===v.value,E=k.useRef(x);return k.useEffect(()=>{const j=requestAnimationFrame(()=>E.current=!1);return()=>cancelAnimationFrame(j)},[]),h.jsx(xg,{present:s||x,children:({present:j})=>h.jsx(Vn.div,{"data-state":x?"active":"inactive","data-orientation":v.orientation,role:"tabpanel","aria-labelledby":p,hidden:!j,id:b,tabIndex:0,...d,ref:i,style:{...a.style,animationDuration:E.current?"0s":void 0},children:j&&m})})});jg.displayName=Ag;function Tg(a,i){return`${a}-trigger-${i}`}function Mg(a,i){return`${a}-content-${i}`}var $x=Sg,Fx=zg,Wx=Ng,Vi=jg;const Ix="modulepreload",Px=function(a){return"/"+a},ph={},Lt=function(i,o,r){let s=Promise.resolve();if(o&&o.length>0){let p=function(b){return Promise.all(b.map(x=>Promise.resolve(x).then(E=>({status:"fulfilled",value:E}),E=>({status:"rejected",reason:E}))))};document.getElementsByTagName("link");const d=document.querySelector("meta[property=csp-nonce]"),v=d?.nonce||d?.getAttribute("nonce");s=p(o.map(b=>{if(b=Px(b),b in ph)return;ph[b]=!0;const x=b.endsWith(".css"),E=x?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${b}"]${E}`))return;const j=document.createElement("link");if(j.rel=x?"stylesheet":Ix,x||(j.as="script"),j.crossOrigin="",j.href=b,v&&j.setAttribute("nonce",v),document.head.appendChild(j),x)return new Promise((D,B)=>{j.addEventListener("load",D),j.addEventListener("error",()=>B(new Error(`Unable to preload CSS for ${b}`)))})}))}function m(d){const v=new Event("vite:preloadError",{cancelable:!0});if(v.payload=d,window.dispatchEvent(v),!v.defaultPrevented)throw d}return s.then(d=>{for(const v of d||[])v.status==="rejected"&&m(v.reason);return i().catch(m)})};function ta(a,i){const o=a.length;if(i<=1||o===0)return a.slice();const r=Math.min(i,o),s=new Array(o);let m=0;for(let d=0;d<o;d++)m+=a[d],d>=r&&(m-=a[d-r]),s[d]=d>=r-1?m/r:a[d];return s}function du(a,i,o){if(i<3||o<2||i%2===0)return ta(a,Math.max(3,i|0));const r=Math.floor(i/2),s=a.slice();for(let m=r;m<a.length-r;m++){let d=0,v=0;for(let p=-r;p<=r;p++){const b=m+p,x=1;d+=a[b]*x,v+=x}s[m]=v>0?d/v:a[m]}return s}function mu(a,i,o){const r=a.length;if(i<3||r===0)return a.slice();const s=i|0,m=Math.max(1,Math.floor(s/2)),d=Math.max(1,s/3),v=[];let p=0;for(let x=-m;x<=m;x++){const E=Math.exp(-(x*x)/(2*d*d));v.push(E),p+=E}for(let x=0;x<v.length;x++)v[x]/=p;const b=a.slice();for(let x=0;x<r;x++){let E=0;for(let j=-m;j<=m;j++){const D=Math.min(r-1,Math.max(0,x+j));E+=a[D]*v[j+m]}b[x]=E}return b}function hu(a,i){const o=a.length;if(o===0)return a.slice();const s=Math.min(.49,Math.max(.001,i)),m=new Array(o);m[0]=a[0];for(let d=1;d<o;d++)m[d]=m[d-1]+s*(a[d]-m[d-1]);return m}/**
 * @license lucide-react v0.539.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const e1=a=>a.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),t1=a=>a.replace(/^([A-Z])|[\s-_]+(\w)/g,(i,o,r)=>r?r.toUpperCase():o.toLowerCase()),yh=a=>{const i=t1(a);return i.charAt(0).toUpperCase()+i.slice(1)},Og=(...a)=>a.filter((i,o,r)=>!!i&&i.trim()!==""&&r.indexOf(i)===o).join(" ").trim(),n1=a=>{for(const i in a)if(i.startsWith("aria-")||i==="role"||i==="title")return!0};/**
 * @license lucide-react v0.539.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var l1={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.539.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const a1=k.forwardRef(({color:a="currentColor",size:i=24,strokeWidth:o=2,absoluteStrokeWidth:r,className:s="",children:m,iconNode:d,...v},p)=>k.createElement("svg",{ref:p,...l1,width:i,height:i,stroke:a,strokeWidth:r?Number(o)*24/Number(i):o,className:Og("lucide",s),...!m&&!n1(v)&&{"aria-hidden":"true"},...v},[...d.map(([b,x])=>k.createElement(b,x)),...Array.isArray(m)?m:[m]]));/**
 * @license lucide-react v0.539.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yt=(a,i)=>{const o=k.forwardRef(({className:r,...s},m)=>k.createElement(a1,{ref:m,iconNode:i,className:Og(`lucide-${e1(yh(a))}`,`lucide-${a}`,r),...s}));return o.displayName=yh(a),o};/**
 * @license lucide-react v0.539.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const u1=[["line",{x1:"12",x2:"12",y1:"20",y2:"10",key:"1vz5eb"}],["line",{x1:"18",x2:"18",y1:"20",y2:"4",key:"cun8e5"}],["line",{x1:"6",x2:"6",y1:"20",y2:"16",key:"hq0ia6"}]],i1=yt("chart-no-axes-column-increasing",u1);/**
 * @license lucide-react v0.539.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const c1=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]],o1=yt("circle-question-mark",c1);/**
 * @license lucide-react v0.539.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const r1=[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]],s1=yt("copy",r1);/**
 * @license lucide-react v0.539.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const f1=[["path",{d:"M10 12.5 8 15l2 2.5",key:"1tg20x"}],["path",{d:"m14 12.5 2 2.5-2 2.5",key:"yinavb"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7z",key:"1mlx9k"}]],d1=yt("file-code",f1);/**
 * @license lucide-react v0.539.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const m1=[["path",{d:"m6 14 1.5-2.9A2 2 0 0 1 9.24 10H20a2 2 0 0 1 1.94 2.5l-1.54 6a2 2 0 0 1-1.95 1.5H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H18a2 2 0 0 1 2 2v2",key:"usdka0"}]],h1=yt("folder-open",m1);/**
 * @license lucide-react v0.539.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const g1=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]],v1=yt("image",g1);/**
 * @license lucide-react v0.539.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const p1=[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"m21 3-7 7",key:"1l2asr"}],["path",{d:"m3 21 7-7",key:"tjx5ai"}],["path",{d:"M9 21H3v-6",key:"wtvkvv"}]],y1=yt("maximize-2",p1);/**
 * @license lucide-react v0.539.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const b1=[["path",{d:"M12 22a1 1 0 0 1 0-20 10 9 0 0 1 10 9 5 5 0 0 1-5 5h-2.25a1.75 1.75 0 0 0-1.4 2.8l.3.4a1.75 1.75 0 0 1-1.4 2.8z",key:"e79jfc"}],["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}]],x1=yt("palette",b1);/**
 * @license lucide-react v0.539.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _1=[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]],S1=yt("rotate-ccw",_1);/**
 * @license lucide-react v0.539.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const w1=[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]],Rg=yt("save",w1);/**
 * @license lucide-react v0.539.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const z1=[["path",{d:"M9.671 4.136a2.34 2.34 0 0 1 4.659 0 2.34 2.34 0 0 0 3.319 1.915 2.34 2.34 0 0 1 2.33 4.033 2.34 2.34 0 0 0 0 3.831 2.34 2.34 0 0 1-2.33 4.033 2.34 2.34 0 0 0-3.319 1.915 2.34 2.34 0 0 1-4.659 0 2.34 2.34 0 0 0-3.32-1.915 2.34 2.34 0 0 1-2.33-4.033 2.34 2.34 0 0 0 0-3.831A2.34 2.34 0 0 1 6.35 6.051a2.34 2.34 0 0 0 3.319-1.915",key:"1i5ecw"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],E1=yt("settings",z1);/**
 * @license lucide-react v0.539.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const N1=[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["line",{x1:"21",x2:"16.65",y1:"21",y2:"16.65",key:"13gj7c"}],["line",{x1:"11",x2:"11",y1:"8",y2:"14",key:"1vmskp"}],["line",{x1:"8",x2:"14",y1:"11",y2:"11",key:"durymu"}]],A1=yt("zoom-in",N1);/**
 * @license lucide-react v0.539.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const j1=[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["line",{x1:"21",x2:"16.65",y1:"21",y2:"16.65",key:"13gj7c"}],["line",{x1:"8",x2:"14",y1:"11",y2:"11",key:"durymu"}]],T1=yt("zoom-out",j1);function Cg(a,i){if(!a)return;const o=a.querySelector("svg");if(!o)return;const s=new XMLSerializer().serializeToString(o),m=new Blob([s],{type:"image/svg+xml;charset=utf-8"}),d=URL.createObjectURL(m),v=document.createElement("a");v.href=d,v.download=i.endsWith(".svg")?i:`${i}.svg`,document.body.appendChild(v),v.click(),v.remove(),URL.revokeObjectURL(d)}async function jr(a,i,o=1){if(!a)return;const r=a.querySelector("svg");if(!r)return;const m=new XMLSerializer().serializeToString(r),d=new Blob([m],{type:"image/svg+xml;charset=utf-8"}),v=URL.createObjectURL(d),p=new Image,b=document.createElement("canvas"),x=r.getBoundingClientRect();b.width=Math.max(1,Math.floor(x.width*o)),b.height=Math.max(1,Math.floor(x.height*o));const E=b.getContext("2d");if(!E){URL.revokeObjectURL(v);return}await new Promise(j=>{p.onload=()=>{j()},p.src=v}),E.fillStyle="#ffffff",E.fillRect(0,0,b.width,b.height),E.drawImage(p,0,0,b.width,b.height),URL.revokeObjectURL(v),b.toBlob(j=>{if(!j)return;const D=document.createElement("a"),B=URL.createObjectURL(j);D.href=B,D.download=i.endsWith(".png")?i:`${i}.png`,document.body.appendChild(D),D.click(),D.remove(),URL.revokeObjectURL(B)},"image/png")}function vu(a,i,o){const r=[i.join(",")].concat(o.map(v=>v.map(p=>{if(p==null)return"";const b=String(p);return b.includes(",")||b.includes('"')||b.includes(`
`)?'"'+b.replace(/"/g,'""')+'"':b}).join(","))),s=new Blob([r.join(`
`)],{type:"text/csv;charset=utf-8"}),m=URL.createObjectURL(s),d=document.createElement("a");d.href=m,d.download=a.endsWith(".csv")?a:`${a}.csv`,document.body.appendChild(d),d.click(),d.remove(),URL.revokeObjectURL(m)}const M1=Object.freeze(Object.defineProperty({__proto__:null,exportCSV:vu,exportPngFromContainer:jr,exportSvgFromContainer:Cg},Symbol.toStringTag,{value:"Module"}));function Ji({title:a,container:i,onReset:o}){return h.jsxs("div",{className:"flex items-center gap-2 justify-between",children:[h.jsx("div",{className:"text-xs text-muted-foreground",children:a}),h.jsxs("div",{className:"flex items-center gap-1",children:[h.jsx(Be,{variant:"ghost",size:"icon",title:"导出SVG",onClick:()=>Cg(i,a||"chart"),children:h.jsx(d1,{className:"w-4 h-4"})}),h.jsx(Be,{variant:"ghost",size:"icon",title:"导出PNG",onClick:()=>jr(i,a||"chart"),children:h.jsx(v1,{className:"w-4 h-4"})}),h.jsx(Be,{variant:"ghost",size:"icon",title:"导出PNG(2x)",onClick:()=>jr(i,(a||"chart")+"_2x",2),children:h.jsx(Rg,{className:"w-4 h-4"})}),h.jsx(Be,{variant:"ghost",size:"icon",title:"重置视图",onClick:()=>{try{o?.()}catch{}},children:h.jsx(y1,{className:"w-4 h-4"})}),h.jsx(Be,{variant:"ghost",size:"icon",title:"放大",children:h.jsx(A1,{className:"w-4 h-4"})}),h.jsx(Be,{variant:"ghost",size:"icon",title:"缩小",children:h.jsx(T1,{className:"w-4 h-4"})})]})]})}let nt;function O1(){const a=k.useRef(null),{data:i,displayConfig:o}=re.getState();k.useEffect(()=>{if(a.current){if(a.current.innerHTML="",!nt){Lt(()=>import("./plot-q4UycjGG.js").then(s=>s.a),__vite__mapDeps([0,1])).then(s=>{nt=s,r()});return}r()}},[i]);function r(){if(!a.current||!nt)return;const s=[];if((re.getState().displayConfig.showGrid??!0)&&s.push(nt.gridX(),nt.gridY()),i?.power&&i.power.current.length&&i.power.power.length){const d=re.getState().processingConfig;let v=i.power.power.slice();d.smoothing?.enabled&&(d.smoothing.method==="moving-average"?v=ta(v,d.smoothing.windowSize??5):d.smoothing.method==="sg"?v=du(v,d.smoothing.windowSize??5,d.smoothing.polynomialOrder??3):d.smoothing.method==="gaussian"?v=mu(v,d.smoothing.windowSize??5):d.smoothing.method==="butterworth"&&(v=hu(v,d.smoothing.cutoff??.15)));const p=i.power.current.map((j,D)=>({x:j,y:v[D]}));(o.showPI??!0)&&s.push(nt.line(p,{x:"x",y:"y",stroke:"steelblue",title:"P-I"}),...o.showMarkers??!0?[nt.dot(p,{x:"x",y:"y",r:2,fill:"steelblue",title:j=>`I=${j.x}
P=${j.y}`})]:[]);const b=window.__liv_diag,E=re.getState().results?.livFitDiagnostics??b;if((o.showDiagnosticsLIV??!0)&&E?.slope!==void 0&&E?.intercept!==void 0){const j=Math.min(...i.power.current),D=Math.max(...i.power.current),B=[j,D].map(L=>({x:L,y:E.slope*L+E.intercept}));s.push(nt.line(B,{x:"x",y:"y",stroke:"red"}))}if((o.showDiagnosticsLIV??!0)&&E?.usedIndices&&Array.isArray(E.usedIndices)){const j=E.usedIndices.map(D=>({x:i.power.current[D],y:i.power.power[D]}));s.push(nt.dot(j,{x:"x",y:"y",r:3,fill:"green"}))}if((o.showDiagnosticsLIV??!0)&&E?.outlierIndices&&Array.isArray(E.outlierIndices)&&E.outlierIndices.length){const j=E.outlierIndices.map(D=>({x:i.power.current[D],y:i.power.power[D]}));s.push(nt.dot(j,{x:"x",y:"y",r:3,fill:"orange"}))}}if(i?.voltage&&i.voltage.current.length&&i.voltage.voltage.length){const d=re.getState().processingConfig;let v=i.voltage.voltage.slice();d.smoothing?.enabled&&(d.smoothing.method==="moving-average"?v=ta(v,d.smoothing.windowSize??5):d.smoothing.method==="sg"?v=du(v,d.smoothing.windowSize??5,d.smoothing.polynomialOrder??3):d.smoothing.method==="gaussian"?v=mu(v,d.smoothing.windowSize??5):d.smoothing.method==="butterworth"&&(v=hu(v,d.smoothing.cutoff??.15)));const p=i.voltage.current.map((b,x)=>({x:b,y:v[x]}));(o.showVI??!0)&&s.push(nt.line(p,{x:"x",y:"y",stroke:"tomato",title:"V-I"}),...o.showMarkers??!0?[nt.dot(p,{x:"x",y:"y",r:2,fill:"tomato",title:b=>`I=${b.x}
V=${b.y}`})]:[])}if(i?.voltage&&i.voltage.current.length&&i.voltage.voltage.length){const d=i.voltage.current.map((v,p)=>({x:v,y:i.voltage.voltage[p]}));s.push(nt.line(d,{x:"x",y:"y",stroke:"tomato",title:"V-I"}),nt.dot(d,{x:"x",y:"y",r:2,fill:"tomato"}))}if(s.length===0){a.current.textContent="暂无 LIV 数据";return}const m=nt.plot({marginLeft:50,marginBottom:40,style:{background:"transparent"},x:{label:"Current (A)"},y:{label:"Value"},marks:s});return a.current.appendChild(m),Lt(()=>import("./zoom-Bb815BNs.js"),__vite__mapDeps([2,1,3,4])).then(d=>{window.__liv_zoom=d.enableZoomPan(a.current)}),()=>{m.remove()}}return h.jsxs("div",{className:"space-y-2",children:[h.jsx(Ji,{title:"LIV_chart",container:a.current,onReset:()=>window.__liv_zoom?.then(s=>s.reset())}),h.jsx("div",{ref:a,className:"w-full overflow-auto"})]})}let al;function R1(){const a=k.useRef(null),{data:i}=re();k.useEffect(()=>{if(a.current){if(a.current.innerHTML="",!al){Lt(()=>import("./plot-q4UycjGG.js").then(r=>r.a),__vite__mapDeps([0,1])).then(r=>{al=r,o()});return}o()}},[i]);function o(){if(!a.current||!al)return;const r=i?.wavelength;if(!r||r.wavelength.length===0||r.intensity.length===0){a.current.textContent="暂无光谱数据";return}let s=r.intensity.slice();const m=re.getState().processingConfig;m.smoothing?.enabled&&(m.smoothing.method==="moving-average"?s=ta(s,m.smoothing.windowSize??5):m.smoothing.method==="sg"?s=du(s,m.smoothing.windowSize??5,m.smoothing.polynomialOrder??3):m.smoothing.method==="gaussian"?s=mu(s,m.smoothing.windowSize??5):m.smoothing.method==="butterworth"&&(s=hu(s,m.smoothing.cutoff??.15)));const d=r.wavelength.map((p,b)=>({x:p,y:s[b]})),v=al.plot({marginLeft:50,marginBottom:40,style:{background:"transparent"},x:{label:"Wavelength (nm)"},y:{label:"Intensity"},marks:[...re.getState().displayConfig.showSpectrumLine??!0?[al.line(d,{x:"x",y:"y",stroke:"rgb(99,102,241)",title:"Spectrum"}),al.dot(d,{x:"x",y:"y",r:1.5,fill:"rgb(99,102,241)",title:p=>`λ=${p.x}
I=${p.y}`})]:[],al.ruleY([0])]});return a.current.appendChild(v),Lt(()=>import("./zoom-Bb815BNs.js"),__vite__mapDeps([2,1,3,4])).then(p=>{window.__spectrum_zoom=p.enableZoomPan(a.current)}),()=>{v.remove()}}return h.jsxs("div",{className:"space-y-2",children:[h.jsx(Ji,{title:"Spectrum_chart",container:a.current,onReset:()=>window.__spectrum_zoom?.then(r=>r.reset())}),h.jsx("div",{ref:a,className:"w-full overflow-auto"})]})}let Un;function C1(){const a=k.useRef(null),{data:i}=re();k.useEffect(()=>{if(a.current){if(a.current.innerHTML="",!Un){Lt(()=>import("./plot-q4UycjGG.js").then(r=>r.a),__vite__mapDeps([0,1])).then(r=>{Un=r,o()});return}o()}},[i]);function o(){if(!a.current||!Un)return;const r=[];if((re.getState().displayConfig.showHFF??!0)&&i?.hff&&i.hff.angle.length&&i.hff.intensity.length){const m=re.getState().processingConfig;let d=i.hff.intensity.slice();m.smoothing?.enabled&&(m.smoothing.method==="moving-average"?d=ta(d,m.smoothing.windowSize??5):m.smoothing.method==="sg"?d=du(d,m.smoothing.windowSize??5,m.smoothing.polynomialOrder??3):m.smoothing.method==="gaussian"?d=mu(d,m.smoothing.windowSize??5):m.smoothing.method==="butterworth"&&(d=hu(d,m.smoothing.cutoff??.15)));const v=i.hff.angle.map((p,b)=>({x:p,y:d[b],s:"HFF"}));r.push(Un.line(v,{x:"x",y:"y",stroke:"seagreen",title:"HFF"}),Un.dot(v,{x:"x",y:"y",r:2,fill:"seagreen",title:p=>`θ=${p.x}
I=${p.y}`}))}if((re.getState().displayConfig.showVFF??!0)&&i?.vff&&i.vff.angle.length&&i.vff.intensity.length){const m=re.getState().processingConfig;let d=i.vff.intensity.slice();m.smoothing?.enabled&&(m.smoothing.method==="moving-average"?d=ta(d,m.smoothing.windowSize??5):m.smoothing.method==="sg"?d=du(d,m.smoothing.windowSize??5,m.smoothing.polynomialOrder??3):m.smoothing.method==="gaussian"?d=mu(d,m.smoothing.windowSize??5):m.smoothing.method==="butterworth"&&(d=hu(d,m.smoothing.cutoff??.15)));const v=i.vff.angle.map((p,b)=>({x:p,y:d[b],s:"VFF"}));r.push(Un.line(v,{x:"x",y:"y",stroke:"orange",title:"VFF"}),Un.dot(v,{x:"x",y:"y",r:2,fill:"orange",title:p=>`θ=${p.x}
I=${p.y}`}))}if(r.length===0){a.current.textContent="暂无发散角数据";return}const s=Un.plot({marginLeft:50,marginBottom:40,style:{background:"transparent"},x:{label:"Angle (deg)"},y:{label:"Intensity"},marks:r});return a.current.appendChild(s),Lt(()=>import("./zoom-Bb815BNs.js"),__vite__mapDeps([2,1,3,4])).then(m=>{window.__div_zoom=m.enableZoomPan(a.current)}),()=>s.remove()}return h.jsxs("div",{className:"space-y-2",children:[h.jsx(Ji,{title:"Divergence_chart",container:a.current,onReset:()=>window.__div_zoom?.then(r=>r.reset())}),h.jsx("div",{ref:a,className:"w-full overflow-auto"})]})}let ul;function D1(){const a=k.useRef(null),{data:i,displayConfig:o}=re.getState();k.useEffect(()=>{if(!a.current)return;a.current.innerHTML="";const s=i?.power,m=i?.voltage;if(!s||!m){a.current.textContent="暂无效率数据（需要 P-I 与 V-I）";return}const d=Math.min(s.current.length,s.power.length,m.current.length,m.voltage.length);if(d===0){a.current.textContent="暂无效率数据";return}const v=[];for(let p=0;p<d;p++){const b=s.current[p],x=s.power[p],E=m.voltage[p],j=b*E;Number.isFinite(b)&&Number.isFinite(x)&&Number.isFinite(E)&&Math.abs(j)>1e-12&&v.push({x:b,y:x/j})}if(v.length===0){a.current.textContent="暂无有效效率数据";return}if(!ul){Lt(()=>import("./plot-q4UycjGG.js").then(p=>p.a),__vite__mapDeps([0,1])).then(p=>{ul=p,r()});return}r()},[i]);function r(){if(!a.current||!ul)return;if(!(o.showEta??!0)){a.current.textContent="η-I 已隐藏";return}const s=i?.power,m=i?.voltage;if(!s||!m)return;const d=Math.min(s.current.length,s.power.length,m.current.length,m.voltage.length),v=[];for(let b=0;b<d;b++){const x=s.current[b],E=s.power[b],j=m.voltage[b],D=x*j;Number.isFinite(x)&&Number.isFinite(E)&&Number.isFinite(j)&&Math.abs(D)>1e-12&&v.push({x,y:E/D})}const p=ul.plot({marginLeft:50,marginBottom:40,style:{background:"transparent"},x:{label:"Current (A)"},y:{label:"Efficiency η"},marks:[ul.line(v,{x:"x",y:"y",stroke:"purple",title:"η-I"}),ul.dot(v,{x:"x",y:"y",r:2,fill:"purple",title:b=>`I=${b.x}
η=${b.y.toFixed(4)}`}),ul.ruleY([0])]});return a.current.appendChild(p),Lt(()=>import("./zoom-Bb815BNs.js"),__vite__mapDeps([2,1,3,4])).then(b=>{window.__eta_zoom=b.enableZoomPan(a.current)}),()=>p.remove()}return h.jsxs("div",{className:"space-y-2",children:[h.jsx(Ji,{title:"Efficiency_chart",container:a.current,onReset:()=>window.__eta_zoom?.then(s=>s.reset())}),h.jsx("div",{ref:a,className:"w-full overflow-auto"})]})}function k1(){const{displayConfig:a,updateDisplayConfig:i}=re(),o=a;return h.jsxs("div",{className:"flex flex-wrap items-center gap-4 text-sm",children:[h.jsxs("label",{className:"flex items-center gap-1",children:[h.jsx("input",{type:"checkbox",checked:o.showGrid??!0,onChange:r=>i({showGrid:r.target.checked})})," 网格"]}),h.jsxs("label",{className:"flex items-center gap-1",children:[h.jsx("input",{type:"checkbox",checked:o.showMarkers??!0,onChange:r=>i({showMarkers:r.target.checked})})," 标记"]}),h.jsxs("label",{className:"flex items-center gap-1",children:[h.jsx("input",{type:"checkbox",checked:o.showPI??!0,onChange:r=>i({showPI:r.target.checked})})," P-I"]}),h.jsxs("label",{className:"flex items-center gap-1",children:[h.jsx("input",{type:"checkbox",checked:o.showVI??!0,onChange:r=>i({showVI:r.target.checked})})," V-I"]}),h.jsxs("label",{className:"flex items-center gap-1",children:[h.jsx("input",{type:"checkbox",checked:o.showEta??!0,onChange:r=>i({showEta:r.target.checked})})," η-I"]}),h.jsxs("label",{className:"flex items-center gap-1",children:[h.jsx("input",{type:"checkbox",checked:o.showDiagnosticsLIV??!0,onChange:r=>i({showDiagnosticsLIV:r.target.checked})})," 诊断层"]})]})}function U1(){const{displayConfig:a,updateDisplayConfig:i}=re();return h.jsx("div",{className:"flex items-center gap-4 text-sm",children:h.jsxs("label",{className:"flex items-center gap-1",children:[h.jsx("input",{type:"checkbox",checked:a.showSpectrumLine??!0,onChange:o=>i({showSpectrumLine:o.target.checked})})," 光谱"]})})}function V1(){const{displayConfig:a,updateDisplayConfig:i}=re();return h.jsxs("div",{className:"flex items-center gap-4 text-sm",children:[h.jsxs("label",{className:"flex items-center gap-1",children:[h.jsx("input",{type:"checkbox",checked:a.showHFF??!0,onChange:o=>i({showHFF:o.target.checked})})," HFF"]}),h.jsxs("label",{className:"flex items-center gap-1",children:[h.jsx("input",{type:"checkbox",checked:a.showVFF??!0,onChange:o=>i({showVFF:o.target.checked})})," VFF"]})]})}function Z1(){return h.jsxs($x,{defaultValue:"LIV",className:"w-full",children:[h.jsx(Fx,{className:"flex gap-2 border-b",children:["LIV","光谱","发散","效率"].map(a=>h.jsx(Wx,{value:a,className:"px-3 py-2 text-sm data-[state=active]:border-b-2 data-[state=active]:border-primary",children:a},a))}),h.jsxs(Vi,{value:"LIV",className:"p-4 space-y-3",children:[h.jsx(k1,{}),h.jsx("div",{className:"text-xs text-muted-foreground",children:"图例：P-I=蓝色、V-I=红色、诊断线=红线、拟合点=绿色、异常点=橙色"}),h.jsx(O1,{})]}),h.jsxs(Vi,{value:"光谱",className:"p-4 space-y-3",children:[h.jsx(U1,{}),h.jsx(R1,{})]}),h.jsxs(Vi,{value:"发散",className:"p-4 space-y-3",children:[h.jsx(V1,{}),h.jsx(C1,{})]}),h.jsx(Vi,{value:"效率",className:"p-4",children:h.jsx(D1,{})})]})}function H1(){const{error:a,setError:i}=re();return a?h.jsxs("div",{className:"rounded border border-destructive/40 bg-destructive/10 text-destructive px-4 py-3 text-sm flex items-start justify-between",children:[h.jsx("div",{className:"pr-4",children:a}),h.jsx("button",{className:"text-xs underline",onClick:()=>i(null),children:"关闭"})]}):null}const bh=[{key:"power",label:"P-I",x:"Current (A)",y:"Power (W)"},{key:"voltage",label:"V-I",x:"Current (A)",y:"Voltage (V)"},{key:"wavelength",label:"光谱",x:"Wavelength (nm)",y:"Intensity"},{key:"hff",label:"HFF 远场",x:"Angle (deg)",y:"Intensity"},{key:"vff",label:"VFF 远场",x:"Angle (deg)",y:"Intensity"}];function B1(){const{data:a}=re(),[i,o]=k.useState("power"),r=k.useMemo(()=>{if(!a)return[];const d=(v,p)=>!v||!p?[]:v.slice(0,50).map((b,x)=>[b,p[x]]);switch(i){case"power":return d(a.power?.current,a.power?.power);case"voltage":return d(a.voltage?.current,a.voltage?.voltage);case"wavelength":return d(a.wavelength?.wavelength,a.wavelength?.intensity);case"hff":return d(a.hff?.angle,a.hff?.intensity);case"vff":return d(a.vff?.angle,a.vff?.intensity);default:return[]}},[a,i]),s=bh.find(d=>d.key===i),m=r.length>0;return h.jsxs("div",{className:"space-y-3",children:[h.jsx("div",{className:"flex gap-2 flex-wrap",children:bh.map(d=>h.jsx(Be,{variant:i===d.key?"default":"outline",size:"sm",onClick:()=>o(d.key),children:d.label},d.key))}),m?h.jsx("div",{className:"overflow-auto border rounded",children:h.jsxs("table",{className:"w-full text-sm",children:[h.jsx("thead",{children:h.jsxs("tr",{className:"bg-muted/30",children:[h.jsx("th",{className:"px-3 py-2 text-left font-medium text-muted-foreground",children:s.x}),h.jsx("th",{className:"px-3 py-2 text-left font-medium text-muted-foreground",children:s.y})]})}),h.jsx("tbody",{children:r.map(([d,v],p)=>h.jsxs("tr",{className:"even:bg-muted/10",children:[h.jsx("td",{className:"px-3 py-1",children:d}),h.jsx("td",{className:"px-3 py-1",children:v})]},p))})]})}):h.jsx("div",{className:"text-sm text-muted-foreground",children:"当前系列暂无数据"})]})}function L1(){const{results:a}=re(),i=a?.livParameters,o=a?.spectralParameters,r=a?.divergenceParameters;return!i&&!o?null:h.jsxs("div",{className:"space-y-3 text-sm",children:[h.jsxs("div",{className:"grid grid-cols-2 gap-3",children:[i?.thresholdCurrent_mA!==void 0&&h.jsxs("div",{className:"flex justify-between",children:[h.jsx("span",{children:"Ith"}),h.jsxs("span",{children:[i.thresholdCurrent_mA.toFixed(2)," mA"]})]}),i?.slopeEfficiency_W_per_A!==void 0&&h.jsxs("div",{className:"flex justify-between",children:[h.jsx("span",{children:"ηd"}),h.jsxs("span",{children:[i.slopeEfficiency_W_per_A.toFixed(4)," W/A"]})]}),i?.seriesResistance_Ohm!==void 0&&h.jsxs("div",{className:"flex justify-between",children:[h.jsx("span",{children:"Rs"}),h.jsxs("span",{children:[i.seriesResistance_Ohm.toFixed(4)," Ω"]})]}),i?.maxEfficiency!==void 0&&h.jsxs("div",{className:"flex justify-between",children:[h.jsx("span",{children:"ηmax"}),h.jsxs("span",{children:[(i.maxEfficiency*100).toFixed(2)," %"]})]}),o?.peakWavelength_nm!==void 0&&h.jsxs("div",{className:"flex justify-between",children:[h.jsx("span",{children:"λpeak"}),h.jsxs("span",{children:[o.peakWavelength_nm.toFixed(2)," nm"]})]}),o?.fwhm_nm!==void 0&&h.jsxs("div",{className:"flex justify-between",children:[h.jsx("span",{children:"FWHM"}),h.jsxs("span",{children:[o.fwhm_nm.toFixed(3)," nm"]})]}),o?.centroid_nm!==void 0&&h.jsxs("div",{className:"flex justify-between",children:[h.jsx("span",{children:"重心"}),h.jsxs("span",{children:[o.centroid_nm.toFixed(2)," nm"]})]}),o?.smsr_dB!==void 0&&h.jsxs("div",{className:"flex justify-between",children:[h.jsx("span",{children:"SMSR"}),h.jsxs("span",{children:[o.smsr_dB.toFixed(2)," dB"]})]}),r?.horizontal_deg!==void 0&&h.jsxs("div",{className:"flex justify-between",children:[h.jsx("span",{children:"θ∥(HFF)"}),h.jsxs("span",{children:[r.horizontal_deg.toFixed(2)," °"]})]}),r?.vertical_deg!==void 0&&h.jsxs("div",{className:"flex justify-between",children:[h.jsx("span",{children:"θ⊥(VFF)"}),h.jsxs("span",{children:[r.vertical_deg.toFixed(2)," °"]})]}),r?.ellipticity!==void 0&&h.jsxs("div",{className:"flex justify-between",children:[h.jsx("span",{children:"椭圆度"}),h.jsx("span",{children:r.ellipticity.toFixed(3)})]})]}),h.jsxs("div",{className:"flex items-center gap-3",children:[h.jsx("button",{className:"underline",onClick:()=>{vu("results",["Metric","Value"],[["Ith_mA",i?.thresholdCurrent_mA??""],["Slope_W_A",i?.slopeEfficiency_W_per_A??""],["Rs_Ohm",i?.seriesResistance_Ohm??""],["EtaMax",i?.maxEfficiency??""],["LambdaPeak_nm",o?.peakWavelength_nm??""],["FWHM_nm",o?.fwhm_nm??""],["Centroid_nm",o?.centroid_nm??""],["SMSR_dB",o?.smsr_dB??""],["Theta_H_deg",r?.horizontal_deg??""],["Theta_V_deg",r?.vertical_deg??""],["Ellipticity",r?.ellipticity??""]])},children:"导出结果CSV"}),h.jsx("button",{className:"underline",onClick:async()=>{const s=await Lt(()=>import("./xlsx-hHwYTrUW.js"),[]),m=s.utils.book_new(),d=[["Metric","Value"],["Ith_mA",i?.thresholdCurrent_mA??""],["Slope_W_A",i?.slopeEfficiency_W_per_A??""],["Rs_Ohm",i?.seriesResistance_Ohm??""],["EtaMax",i?.maxEfficiency??""],["LambdaPeak_nm",o?.peakWavelength_nm??""],["FWHM_nm",o?.fwhm_nm??""],["Centroid_nm",o?.centroid_nm??""],["SMSR_dB",o?.smsr_dB??""],["Theta_H_deg",r?.horizontal_deg??""],["Theta_V_deg",r?.vertical_deg??""],["Ellipticity",r?.ellipticity??""]],v=s.utils.aoa_to_sheet(d);s.utils.book_append_sheet(m,v,"Summary"),s.writeFile(m,"results.xlsx")},children:"导出Excel"})]})]})}function G1(){const{processingConfig:a,updateProcessingConfig:i}=re();return h.jsxs("div",{className:"space-y-4 text-sm",children:[h.jsxs("div",{children:[h.jsx("div",{className:"font-medium mb-2",children:"阈值检测方法"}),h.jsx("div",{className:"flex gap-2 flex-wrap",children:["linear","segmented","robust"].map(o=>h.jsx(Be,{variant:a.thresholdDetection===o?"default":"outline",size:"sm",onClick:()=>i({thresholdDetection:o}),children:o},o))})]}),h.jsxs("div",{children:[h.jsx("div",{className:"font-medium mb-2",children:"平滑设置"}),h.jsxs("label",{className:"flex items-center gap-2 mb-2",children:[h.jsx("input",{type:"checkbox",checked:a.smoothing?.enabled??!1,onChange:o=>i({smoothing:{...a.smoothing??{},enabled:o.target.checked}})}),"启用平滑"]}),h.jsxs("div",{className:"flex flex-wrap items-center gap-3",children:[h.jsxs("label",{className:"flex items-center gap-1",children:[h.jsx("span",{children:"方法"}),h.jsxs("select",{className:"border rounded px-2 py-1 bg-background",value:a.smoothing?.method??"moving-average",onChange:o=>i({smoothing:{...a.smoothing??{enabled:!0},method:o.target.value}}),children:[h.jsx("option",{value:"moving-average",children:"移动平均"}),h.jsx("option",{value:"sg",children:"Savitzky-Golay"}),h.jsx("option",{value:"gaussian",children:"高斯"}),h.jsx("option",{value:"butterworth",children:"Butterworth"})]})]}),h.jsxs("label",{className:"flex items-center gap-1",children:[h.jsx("span",{children:"窗口"}),h.jsx("input",{type:"number",className:"w-20 border rounded px-2 py-1 bg-background",value:a.smoothing?.windowSize??5,onChange:o=>i({smoothing:{...a.smoothing??{enabled:!0},windowSize:Number(o.target.value)}})})]}),h.jsxs("label",{className:"flex items-center gap-1",children:[h.jsx("span",{children:"阶数"}),h.jsx("input",{type:"number",className:"w-20 border rounded px-2 py-1 bg-background",value:a.smoothing?.polynomialOrder??3,onChange:o=>i({smoothing:{...a.smoothing??{enabled:!0},polynomialOrder:Number(o.target.value)}}),disabled:a.smoothing?.method!=="sg"})]}),h.jsxs("label",{className:"flex items-center gap-1",children:[h.jsx("span",{children:"截止(0~0.5)"}),h.jsx("input",{type:"number",step:"0.01",min:.01,max:.49,className:"w-24 border rounded px-2 py-1 bg-background",value:a.smoothing?.cutoff??.15,onChange:o=>i({smoothing:{...a.smoothing??{enabled:!0},cutoff:Number(o.target.value)}}),disabled:a.smoothing?.method!=="butterworth"})]})]})]}),h.jsxs("div",{children:[h.jsx("div",{className:"font-medium mb-2",children:"拟合点数"}),h.jsx("input",{type:"number",className:"w-32 rounded border px-2 py-1 bg-background",value:a.fittingPoints??150,onChange:o=>i({fittingPoints:Number(o.target.value)})})]}),h.jsxs("div",{children:[h.jsx("div",{className:"font-medium mb-2",children:"单位"}),h.jsxs("div",{className:"flex gap-3",children:[h.jsxs("label",{className:"flex items-center gap-1",children:[h.jsx("span",{children:"功率"}),h.jsxs("select",{className:"border rounded px-2 py-1 bg-background",value:a.units?.power??"W",onChange:o=>i({units:{...a.units??{},power:o.target.value}}),children:[h.jsx("option",{value:"W",children:"W"}),h.jsx("option",{value:"mW",children:"mW"})]})]}),h.jsxs("label",{className:"flex items-center gap-1",children:[h.jsx("span",{children:"电流"}),h.jsxs("select",{className:"border rounded px-2 py-1 bg-background",value:a.units?.current??"A",onChange:o=>i({units:{...a.units??{},current:o.target.value}}),children:[h.jsx("option",{value:"A",children:"A"}),h.jsx("option",{value:"mA",children:"mA"})]})]})]})]})]})}function q1(){return h.jsxs("div",{className:"w-full text-sm select-none border-b bg-background/60 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:[h.jsx("div",{className:"mx-auto max-w-full px-4",children:h.jsxs("div",{className:"flex h-10 items-center gap-6",children:[h.jsx(ou,{label:"File",items:[{label:"Open File(s)",action:()=>document.getElementById("__open_files_btn")?.click()},{label:"Recent Files",action:()=>window.alert("Recent Files: 待实现")},{label:"Export Results",action:()=>window.dispatchEvent(new CustomEvent("export-results"))},{label:"Export Charts",action:()=>{document.querySelectorAll("[data-chart-container]").forEach(async i=>{const o=i.getAttribute("data-chart-container")||"chart",r=i.querySelector("svg")?.parentElement??i,{exportPngFromContainer:s}=await Lt(async()=>{const{exportPngFromContainer:m}=await Promise.resolve().then(()=>M1);return{exportPngFromContainer:m}},void 0);s(r,o)})}},{label:"Exit",action:()=>window.alert("请关闭浏览器标签页")}]}),h.jsx(ou,{label:"Edit",items:[{label:"Undo",action:()=>document.execCommand("undo")},{label:"Redo",action:()=>document.execCommand("redo")},{label:"Copy",action:()=>document.execCommand("copy")},{label:"Paste",action:()=>document.execCommand("paste")},{label:"Select All",action:()=>document.execCommand("selectAll")}]}),h.jsx(ou,{label:"View",items:[{label:"Zoom In",action:()=>document.body.style.setProperty("zoom",String((Number(document.body.style.zoom||1)||1)+.1))},{label:"Zoom Out",action:()=>document.body.style.setProperty("zoom",String((Number(document.body.style.zoom||1)||1)-.1))},{label:"Fit to Window",action:()=>document.body.style.removeProperty("zoom")},{label:"Grid On/Off",action:()=>window.dispatchEvent(new CustomEvent("toggle-grid"))},{label:"Legend On/Off",action:()=>window.dispatchEvent(new CustomEvent("toggle-legend"))}]}),h.jsx(ou,{label:"Tools",items:[{label:"Settings",action:()=>window.dispatchEvent(new CustomEvent("open-settings"))},{label:"Preferences",action:()=>window.dispatchEvent(new CustomEvent("open-preferences"))},{label:"Reset Config",action:()=>window.dispatchEvent(new CustomEvent("reset-config"))}]}),h.jsx(ou,{label:"Help",items:[{label:"Documentation",action:()=>window.open("/docs","_self")},{label:"Keyboard Shortcuts",action:()=>window.alert("Shortcuts: 待完善")},{label:"About",action:()=>window.open("/about","_self")}]}),h.jsxs("div",{className:"ml-auto flex items-center gap-4 text-muted-foreground",children:[h.jsx(Rm,{to:"/regress",className:"hover:underline",children:"回归对齐"}),h.jsx(Rm,{to:"/batch",className:"hover:underline",children:"批量处理"})]})]})}),h.jsx("div",{className:"h-px border-t"})]})}function ou({label:a,items:i}){return h.jsxs("div",{className:"group relative",children:[h.jsx("button",{className:"px-2 py-1 rounded hover:bg-accent hover:text-accent-foreground",children:a}),h.jsx("div",{className:"invisible group-hover:visible opacity-0 group-hover:opacity-100 transition-opacity absolute left-0 mt-1 min-w-[180px] rounded-md border bg-popover text-popover-foreground shadow-md z-50",children:h.jsx("ul",{className:"py-1 text-sm",children:i.map(o=>h.jsx("li",{children:h.jsx("button",{className:"w-full text-left px-3 py-1.5 hover:bg-accent/60",onClick:o.action,children:o.label})},o.label))})})]})}function Y1(){const{theme:a,setTheme:i,updateDisplayConfig:o}=re();return k.useEffect(()=>{const r=()=>i(a==="light"?"dark":"light"),s=()=>o({showGrid:!(re.getState().displayConfig.showGrid??!0)}),m=()=>o({showLegend:!(re.getState().displayConfig.showLegend??!0)}),d=()=>{try{console.info("配置已保存")}catch{}};return window.addEventListener("toggle-theme",r),window.addEventListener("toggle-grid",s),window.addEventListener("toggle-legend",m),window.addEventListener("save-config",d),()=>{window.removeEventListener("toggle-theme",r),window.removeEventListener("toggle-grid",s),window.removeEventListener("toggle-legend",m),window.removeEventListener("save-config",d)}},[a,i,o]),h.jsx("div",{className:"w-full h-11 border-b bg-background/60 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:h.jsxs("div",{className:"mx-auto max-w-full px-4 h-full flex items-center gap-2",children:[h.jsx(Be,{variant:"ghost",size:"icon",title:"打开",onClick:()=>document.getElementById("__open_files_btn")?.click(),children:h.jsx(h1,{className:"w-5 h-5"})}),h.jsx(Be,{variant:"ghost",size:"icon",title:"分析",onClick:()=>window.dispatchEvent(new CustomEvent("analyze-now")),children:h.jsx(i1,{className:"w-5 h-5"})}),h.jsx(Be,{variant:"ghost",size:"icon",title:"设置",onClick:()=>window.dispatchEvent(new CustomEvent("open-settings")),children:h.jsx(E1,{className:"w-5 h-5"})}),h.jsx(Be,{variant:"ghost",size:"icon",title:"刷新",onClick:()=>window.location.reload(),children:h.jsx(S1,{className:"w-5 h-5"})}),h.jsx(Be,{variant:"ghost",size:"icon",title:"复制",onClick:()=>document.execCommand("copy"),children:h.jsx(s1,{className:"w-5 h-5"})}),h.jsx(Be,{variant:"ghost",size:"icon",title:"保存配置",onClick:()=>window.dispatchEvent(new CustomEvent("save-config")),children:h.jsx(Rg,{className:"w-5 h-5"})}),h.jsx(Be,{variant:"ghost",size:"icon",title:"主题",onClick:()=>window.dispatchEvent(new CustomEvent("toggle-theme")),children:h.jsx(x1,{className:"w-5 h-5"})}),h.jsx(Be,{variant:"ghost",size:"icon",title:"帮助",onClick:()=>window.open("/docs","_self"),children:h.jsx(o1,{className:"w-5 h-5"})}),h.jsx("div",{className:"ml-auto text-xs text-muted-foreground",children:"LIV Analyzer Web"})]})})}function X1(){const a=k.useRef(!1),i=k.useRef(0),o=k.useRef(0),{sidebarWidth:r,setSidebarWidth:s}=re();function m(p){a.current=!0,i.current=p.clientX,o.current=r,window.addEventListener("mousemove",d),window.addEventListener("mouseup",v)}function d(p){if(!a.current)return;const b=p.clientX-i.current,x=Math.min(Math.max(220,o.current+b),560);s(x)}function v(){a.current=!1,window.removeEventListener("mousemove",d),window.removeEventListener("mouseup",v)}return h.jsx("div",{onMouseDown:m,className:"w-1 cursor-col-resize bg-border hover:bg-primary/40 active:bg-primary/60 transition-colors",style:{userSelect:"none"},role:"separator","aria-orientation":"vertical","aria-label":"Resize sidebar"})}function Q1(){const{results:a}=re.getState(),i=a?.livParameters,o=a?.spectralParameters,r=a?.divergenceParameters;vu("results_summary",["Metric","Value"],[["Ith_mA",i?.thresholdCurrent_mA??""],["Slope_W_A",i?.slopeEfficiency_W_per_A??""],["Rs_Ohm",i?.seriesResistance_Ohm??""],["EtaMax",i?.maxEfficiency??""],["LambdaPeak_nm",o?.peakWavelength_nm??""],["FWHM_nm",o?.fwhm_nm??""],["Centroid_nm",o?.centroid_nm??""],["SMSR_dB",o?.smsr_dB??""],["Theta_H_deg",r?.horizontal_deg??""],["Theta_V_deg",r?.vertical_deg??""],["Ellipticity",r?.ellipticity??""]])}function K1(){const{data:a,processingConfig:i,setResults:o,setError:r,setProcessing:s}=re();return k.useEffect(()=>{const m=new Qi,d=async()=>{if(!a){r("暂无数据，请先加载文件");return}try{s(!0);const p=await m.compute(a,i);o(p)}catch(p){r(p.message)}finally{s(!1)}},v=()=>{try{Q1()}catch(p){r(p.message)}};return window.addEventListener("analyze-now",d),window.addEventListener("export-results",v),()=>{window.removeEventListener("analyze-now",d),window.removeEventListener("export-results",v)}},[a,i,o,r,s]),null}function J1(){const{sidebarWidth:a}=re();return h.jsxs("div",{className:"min-h-screen bg-background text-foreground",children:[h.jsx(K1,{}),h.jsx(q1,{}),h.jsx(Y1,{}),h.jsx("div",{className:"mx-auto max-w-[1600px] px-4 py-4",children:h.jsxs("div",{className:"flex gap-4",children:[h.jsxs("aside",{className:"shrink-0 space-y-4",style:{width:a},children:[h.jsxs(il,{children:[h.jsx(Pl,{children:h.jsx(ea,{children:"文件操作"})}),h.jsx(cl,{children:h.jsx(Sx,{})})]}),h.jsxs(il,{children:[h.jsx(Pl,{children:h.jsx(ea,{children:"分析参数"})}),h.jsx(cl,{children:h.jsx(G1,{})})]})]}),h.jsx(X1,{}),h.jsxs("main",{className:"min-w-0 grow space-y-4",children:[h.jsx(H1,{}),h.jsxs(il,{children:[h.jsx(Pl,{children:h.jsx(ea,{children:"数据预览"})}),h.jsx(cl,{children:h.jsx("div",{"data-chart-container":"preview",children:h.jsx(wx,{})})})]}),h.jsxs(il,{children:[h.jsx(Pl,{children:h.jsx(ea,{children:"结果汇总"})}),h.jsx(cl,{children:h.jsx("div",{"data-chart-container":"summary",children:h.jsx(L1,{})})})]}),h.jsxs(il,{children:[h.jsx(Pl,{children:h.jsx(ea,{children:"数据表格（前50行）"})}),h.jsx(cl,{children:h.jsx("div",{"data-chart-container":"table",children:h.jsx(B1,{})})})]}),h.jsx(il,{children:h.jsx(cl,{children:h.jsx("div",{"data-chart-container":"tabs",children:h.jsx(Z1,{})})})})]})]})})]})}const xh={power:{current:[0,.01,.02,.03,.04,.05],power:[0,.001,.004,.01,.017,.026]},voltage:{current:[0,.01,.02,.03,.04,.05],voltage:[0,.6,.7,.8,.9,1]},wavelength:{wavelength:[979.5,979.8,980.2,980],intensity:[10,12,20,15]}};function $1(){const[a,i]=k.useState(null),[o,r]=k.useState(""),[s,m]=k.useState(xh),[d,v]=k.useState({ith_mA:"",slope_WA:"",rs_ohm:"",lambda_nm:"",fwhm_nm:""}),[p,b]=k.useState({ith_pct:5,slope_pct:3,rs_pct:5,lambda_abs:.1,fwhm_abs:.05});async function x(){r("运行中...");try{const Z=await new Qi().compute(s,{thresholdDetection:"segmented",fittingPoints:3});i(Z);const ee=G(Z);r(ee?"✅ 通过（原型对齐）":"⚠️ 部分指标未计算")}catch(R){r("失败："+R.message)}}function E(){a&&vu("regression_report",["Metric","Measured","Expected","Error","Pass"],[L("Ith_mA",a.livParameters?.thresholdCurrent_mA,d.ith_mA,j(p.ith_pct)),L("Slope_W_A",a.livParameters?.slopeEfficiency_W_per_A,d.slope_WA,j(p.slope_pct)),L("Rs_Ohm",a.livParameters?.seriesResistance_Ohm,d.rs_ohm,j(p.rs_pct)),L("LambdaPeak_nm",a.spectralParameters?.peakWavelength_nm,d.lambda_nm,D(p.lambda_abs)),L("FWHM_nm",a.spectralParameters?.fwhm_nm,d.fwhm_nm,D(p.fwhm_abs))])}function j(R){return{kind:"pct",v:R}}function D(R){return{kind:"abs",v:R}}function B(R){const Z=Number(R);return Number.isFinite(Z)?Z:void 0}function L(R,Z,ee,J){const oe=B(Z),V=B(ee);if(oe===void 0||V===void 0)return[R,Z??"",ee??"","",""];const ze=J.kind==="pct"?Math.abs(oe-V)/(Math.abs(V)>1e-12?Math.abs(V):1)*100:Math.abs(oe-V),Re=ze<=J.v;return[R,oe,V,ze,Re?"PASS":"FAIL"]}function G(R){const Z=[L("Ith_mA",R.livParameters?.thresholdCurrent_mA,d.ith_mA,j(p.ith_pct)),L("Slope_W_A",R.livParameters?.slopeEfficiency_W_per_A,d.slope_WA,j(p.slope_pct)),L("Rs_Ohm",R.livParameters?.seriesResistance_Ohm,d.rs_ohm,j(p.rs_pct)),L("LambdaPeak_nm",R.spectralParameters?.peakWavelength_nm,d.lambda_nm,D(p.lambda_abs)),L("FWHM_nm",R.spectralParameters?.fwhm_nm,d.fwhm_nm,D(p.fwhm_abs))];return[d.ith_mA,d.slope_WA,d.rs_ohm,d.lambda_nm,d.fwhm_nm].some(J=>String(J||"").trim()!=="")?Z.every(J=>J[4]==="PASS"||J[4]===""):!0}async function Q(R){if(!R||R.length===0)return;const Z=R[0],ee=Z.name.split(".").pop()?.toLowerCase();if(ee==="csv"){const J=await Rr(Z);J.data&&m(F(J.data))}else if(ee==="xlsx"||ee==="xls"){const J=await Cr(Z);J.data&&m(Li(J.data))}}function F(R){const Z=R.map(J=>J[0]).filter(Number.isFinite),ee=R.map(J=>J[1]).filter(Number.isFinite);return{power:{current:Z,power:ee}}}return h.jsxs("div",{className:"max-w-3xl mx-auto p-6 space-y-4 text-sm",children:[h.jsx("div",{className:"text-lg font-semibold",children:"回归对齐（原型）"}),h.jsx("div",{className:"text-muted-foreground",children:"加载内置黄金数据，调用计算引擎并输出关键指标，作为 Phase 2 精度对齐的初步原型。"}),h.jsxs("div",{className:"space-y-2",children:[h.jsxs("div",{className:"flex items-center gap-3",children:[h.jsx("button",{className:"underline",onClick:()=>m(xh),children:"使用内置数据"}),h.jsxs("label",{className:"underline cursor-pointer",children:[h.jsx("input",{type:"file",className:"hidden",accept:".csv,.xlsx,.xls",onChange:R=>Q(R.target.files)}),"上传数据文件"]}),h.jsx("button",{className:"underline",onClick:x,children:"运行回归"}),h.jsx("button",{className:"underline",onClick:E,disabled:!a,children:"下载报告CSV"})]}),h.jsxs("div",{className:"grid grid-cols-2 gap-3",children:[h.jsxs("div",{children:[h.jsx("div",{className:"font-medium mb-1",children:"期望值（可选）"}),h.jsxs("div",{className:"grid grid-cols-2 gap-2 items-center",children:[h.jsx("label",{children:"Ith (mA)"}),h.jsx("input",{className:"border rounded px-2 py-1",value:d.ith_mA,onChange:R=>v({...d,ith_mA:R.target.value})}),h.jsx("label",{children:"Slope (W/A)"}),h.jsx("input",{className:"border rounded px-2 py-1",value:d.slope_WA,onChange:R=>v({...d,slope_WA:R.target.value})}),h.jsx("label",{children:"Rs (Ω)"}),h.jsx("input",{className:"border rounded px-2 py-1",value:d.rs_ohm,onChange:R=>v({...d,rs_ohm:R.target.value})}),h.jsx("label",{children:"λpeak (nm)"}),h.jsx("input",{className:"border rounded px-2 py-1",value:d.lambda_nm,onChange:R=>v({...d,lambda_nm:R.target.value})}),h.jsx("label",{children:"FWHM (nm)"}),h.jsx("input",{className:"border rounded px-2 py-1",value:d.fwhm_nm,onChange:R=>v({...d,fwhm_nm:R.target.value})})]})]}),h.jsxs("div",{children:[h.jsx("div",{className:"font-medium mb-1",children:"阈值（PRD 默认）"}),h.jsxs("div",{className:"grid grid-cols-2 gap-2 items-center",children:[h.jsx("label",{children:"Ith 误差(%)"}),h.jsx("input",{className:"border rounded px-2 py-1",type:"number",value:p.ith_pct,onChange:R=>b({...p,ith_pct:Number(R.target.value)})}),h.jsx("label",{children:"斜率误差(%)"}),h.jsx("input",{className:"border rounded px-2 py-1",type:"number",value:p.slope_pct,onChange:R=>b({...p,slope_pct:Number(R.target.value)})}),h.jsx("label",{children:"Rs 误差(%)"}),h.jsx("input",{className:"border rounded px-2 py-1",type:"number",value:p.rs_pct,onChange:R=>b({...p,rs_pct:Number(R.target.value)})}),h.jsx("label",{children:"λ误差(nm)"}),h.jsx("input",{className:"border rounded px-2 py-1",type:"number",value:p.lambda_abs,onChange:R=>b({...p,lambda_abs:Number(R.target.value)})}),h.jsx("label",{children:"FWHM误差(nm)"}),h.jsx("input",{className:"border rounded px-2 py-1",type:"number",value:p.fwhm_abs,onChange:R=>b({...p,fwhm_abs:Number(R.target.value)})})]})]})]})]}),h.jsx("div",{children:o}),a&&h.jsx("pre",{className:"bg-muted/30 p-3 rounded overflow-auto text-xs",children:JSON.stringify(a,null,2)})]})}function F1(){const[a,i]=k.useState([]),[o,r]=k.useState(2),[s,m]=k.useState(!1);function d(x,E){i(j=>{const D=j.slice();return D[x]={...D[x],...E},D})}async function v(x){if(!x||x.length===0)return;const E=[];for(let j=0;j<x.length;j++)E.push({name:x[j].name,status:"pending"});i(E),await p(x)}async function p(x){m(!0);const E=new qh,j=new Qi,D=[];let B=0;async function L(){for(;;){const G=B++;if(G>=x.length)break;const Q=x[G];try{d(G,{status:"processing"});const F=Q.name.split(".").pop()?.toLowerCase();let R=null;if(F==="csv"){const oe=await E.parseCSV(Q),V=oe.map(Re=>Re[0]).filter(Number.isFinite),ze=oe.map(Re=>Re[1]).filter(Number.isFinite);R={power:{current:V,power:ze}}}else if(F==="xlsx"||F==="xls"){const J=await E.parseExcel(Q);R=Li(J)}else if(F==="csv"){const oe=(await Rr(Q)).data??[],V=oe.map(Re=>Re[0]).filter(Number.isFinite),ze=oe.map(Re=>Re[1]).filter(Number.isFinite);R={power:{current:V,power:ze}}}else{const J=await Cr(Q);R=J.data?Li(J.data):null}if(!R)throw new Error("解析失败");const Z=Yh(R),ee=await j.compute(Z);d(G,{status:"done",ith:ee.livParameters?.thresholdCurrent_mA,slope:ee.livParameters?.slopeEfficiency_W_per_A,rs:ee.livParameters?.seriesResistance_Ohm,etamax:ee.livParameters?.maxEfficiency,lambda:ee.spectralParameters?.peakWavelength_nm,fwhm:ee.spectralParameters?.fwhm_nm})}catch(F){d(G,{status:"error",error:F.message})}}}for(let G=0;G<Math.max(1,Math.min(o,x.length));G++)D.push(L());await Promise.all(D),m(!1)}function b(){vu("batch_summary",["File","Status","Ith_mA","Slope_W_A","Rs_Ohm","EtaMax","LambdaPeak_nm","FWHM_nm","Error"],a.map(x=>[x.name,x.status,x.ith??"",x.slope??"",x.rs??"",x.etamax??"",x.lambda??"",x.fwhm??"",x.error??""]))}return h.jsxs("div",{className:"max-w-5xl mx-auto p-6 space-y-4 text-sm",children:[h.jsx("div",{className:"text-lg font-semibold",children:"批量处理（原型）"}),h.jsx("div",{className:"text-muted-foreground",children:"选择多个 CSV/Excel 文件，后台并行解析并计算关键指标，支持导出汇总。"}),h.jsxs("div",{className:"flex items-center gap-3",children:[h.jsxs("label",{className:"underline cursor-pointer",children:[h.jsx("input",{type:"file",className:"hidden",multiple:!0,accept:".csv,.xlsx,.xls",onChange:x=>v(x.target.files)}),"选择文件"]}),h.jsxs("label",{className:"flex items-center gap-1",children:["并发",h.jsx("input",{type:"number",className:"w-16 border rounded px-2 py-1",value:o,onChange:x=>r(Number(x.target.value)),min:1,max:8})]}),h.jsx("button",{className:"underline",onClick:b,disabled:a.length===0||s,children:"导出汇总CSV"}),s&&h.jsx("span",{children:"处理中..."})]}),h.jsx("div",{className:"overflow-auto border rounded",children:h.jsxs("table",{className:"w-full text-xs",children:[h.jsx("thead",{className:"bg-muted/30",children:h.jsxs("tr",{children:[h.jsx("th",{className:"px-2 py-2 text-left",children:"文件"}),h.jsx("th",{className:"px-2 py-2 text-left",children:"状态"}),h.jsx("th",{className:"px-2 py-2 text-left",children:"Ith(mA)"}),h.jsx("th",{className:"px-2 py-2 text-left",children:"Slope(W/A)"}),h.jsx("th",{className:"px-2 py-2 text-left",children:"Rs(Ω)"}),h.jsx("th",{className:"px-2 py-2 text-left",children:"ηmax"}),h.jsx("th",{className:"px-2 py-2 text-left",children:"λpeak(nm)"}),h.jsx("th",{className:"px-2 py-2 text-left",children:"FWHM(nm)"}),h.jsx("th",{className:"px-2 py-2 text-left",children:"错误"})]})}),h.jsx("tbody",{children:a.map((x,E)=>h.jsxs("tr",{className:"even:bg-muted/10",children:[h.jsx("td",{className:"px-2 py-1",children:x.name}),h.jsx("td",{className:"px-2 py-1",children:x.status}),h.jsx("td",{className:"px-2 py-1",children:Il(x.ith)}),h.jsx("td",{className:"px-2 py-1",children:Il(x.slope)}),h.jsx("td",{className:"px-2 py-1",children:Il(x.rs)}),h.jsx("td",{className:"px-2 py-1",children:Il(x.etamax)}),h.jsx("td",{className:"px-2 py-1",children:Il(x.lambda)}),h.jsx("td",{className:"px-2 py-1",children:Il(x.fwhm)}),h.jsx("td",{className:"px-2 py-1 text-red-600",children:x.error??""})]},E))})]})})]})}function Il(a){return a===void 0?"":String(a)}function W1(){return h.jsx("div",{className:"p-6 text-sm text-muted-foreground",children:"设置页（待完善）"})}function I1(){return h.jsx("div",{className:"p-6 text-sm text-muted-foreground",children:"文档（待完善）"})}function P1(){return h.jsx("div",{className:"p-6 text-sm text-muted-foreground",children:"关于 LIV Analyzer Web（待完善）"})}const e_=uy([{path:"/",element:h.jsx(J1,{})},{path:"/settings",element:h.jsx(W1,{})},{path:"/docs",element:h.jsx(I1,{})},{path:"/about",element:h.jsx(P1,{})},{path:"/regress",element:h.jsx($1,{})},{path:"/batch",element:h.jsx(F1,{})}]);vy.createRoot(document.getElementById("root")).render(h.jsx(k.StrictMode,{children:h.jsx(iy,{router:e_})}));export{Lt as _};
