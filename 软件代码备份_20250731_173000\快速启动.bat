@echo off
chcp 936 >nul 2>&1
cls
echo.
echo =====================================
echo       LIV Analyzer C# Version
echo        Installation Guide
echo =====================================
echo.

echo Step 1: Check .NET SDK...
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] .NET SDK not found!
    echo.
    echo Please install .NET 6 SDK:
    echo 1. Open: https://dotnet.microsoft.com/download/dotnet/6.0
    echo 2. Download ".NET 6.0 SDK" 
    echo 3. Install and restart this script
    echo.
    set /p dummy="Press Enter to open download page..."
    start https://dotnet.microsoft.com/download/dotnet/6.0
    pause
    exit /b 1
) else (
    echo [OK] .NET SDK found
    for /f "tokens=*" %%i in ('dotnet --version') do echo Version: %%i
)

echo.
echo Step 2: Restore packages...
dotnet restore
if %errorlevel% neq 0 (
    echo [ERROR] Package restore failed
    pause
    exit /b 1
)

echo.
echo Step 3: Build project...
dotnet build --configuration Release
if %errorlevel% neq 0 (
    echo [ERROR] Build failed
    pause
    exit /b 1
)

echo.
echo [SUCCESS] Project ready!
echo.
echo Choose action:
echo   1 - Run Application
echo   2 - Run Tests
echo   3 - Create Executable
echo   4 - Exit
echo.

:menu
set /p choice="Enter choice (1-4): "
if "%choice%"=="1" goto run
if "%choice%"=="2" goto test
if "%choice%"=="3" goto publish
if "%choice%"=="4" goto quit
echo Invalid choice
goto menu

:run
echo.
echo Starting application...
start dotnet run --project LIVAnalyzer.UI --configuration Release
goto quit

:test
echo.
echo Running tests...
dotnet test --configuration Release
pause
goto menu

:publish
echo.
echo Creating executable...
if not exist "Release" mkdir Release
dotnet publish LIVAnalyzer.UI -c Release -r win-x64 --self-contained true -o Release\LIVAnalyzer -p:PublishSingleFile=true
echo.
echo Executable created in: Release\LIVAnalyzer\
pause
goto menu

:quit
exit /b 0